<template>
  <div id="app">
    <router-view v-if="isRouterAlive"></router-view>
  </div>
</template>

<script>
export default {
  name: 'App',
  data: () => ({
    isRouterAlive: true
  }),
  provide() {
    return {
      reload: this.reload
    }
  },
  methods: {
    reload() {
      this.isRouterAlive = false
      this.$nextTick(function () {
        this.isRouterAlive = true
      })
    },
    autoAdjust(targetWidth = 1920) {
      const isFox =
          navigator.userAgent.indexOf('Firefox') > -1
      let adjustWindow = () => {
        const ratio = document.documentElement.clientWidth / targetWidth
        const htmlHeight =
            (document.documentElement.clientHeight * targetWidth) / document.documentElement.clientWidth + 'px'

        document.documentElement.style.height = htmlHeight
        if (isFox) {
          document.documentElement.style.transform = `scale(${ratio})`
        } else {
          document.documentElement.style.zoom = ratio
        }
        document.documentElement.setAttribute('data-ratio', ratio)
        let allTag = document.getElementsByTagName('*')
        for (let i = 0; i < allTag.length; i++) {
          const currentMarginTop = window.getComputedStyle(allTag[i]).marginTop
          if (currentMarginTop !== '0px' || currentMarginTop !== 'auto') {
            allTag[i].style.marginTop = Number(currentMarginTop.slice(0, -2)) * (document.documentElement.clientHeight / 1080 / ratio) + 'px'
          }
          const paddingTop = window.getComputedStyle(allTag[i]).paddingTop
          if (paddingTop !== '0px' || paddingTop !== 'auto') {
            allTag[i].style.paddingTop = Number(paddingTop.slice(0, -2)) * (document.documentElement.clientHeight / 1080 / ratio) + 'px'
          }
        }
      }
      adjustWindow()
      window.addEventListener('resize', adjustWindow)
      // 使鼠标坐标一致
      let x_get = Object.getOwnPropertyDescriptor(MouseEvent.prototype, 'x')
          .get
      let y_get = Object.getOwnPropertyDescriptor(MouseEvent.prototype, 'y')
          .get
      Object.defineProperties(MouseEvent.prototype, {
        R: {
          get: function () {
            return parseFloat(
                document.documentElement.getAttribute('data-ratio')
            )
          }
        },
        x: {
          get: function () {
            return x_get.call(this) / this.R
          }
        },
        y: {
          get: function () {
            return y_get.call(this) / this.R
          }
        }
      })
      if (isFox) {
        let getBoundingClientRect = Element.prototype.getBoundingClientRect
        Element.prototype.getBoundingClientRect = function () {
          let value = getBoundingClientRect.call(this)
          let ratio = parseFloat(
              document.documentElement.getAttribute('data-ratio')
          )
          value = new Proxy(value, {
            get: function (target, proper) {
              return Reflect.get(target, proper) / ratio
            }
          })
          return value
        }
      }
    }
  },
  mounted() {
  }
}
</script>

<style>
body {
  font-size: var(--textSize);
}
</style>
