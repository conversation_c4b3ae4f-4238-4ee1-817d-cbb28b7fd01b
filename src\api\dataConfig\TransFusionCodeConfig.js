import request from '@/utils/request'

/**
 * 查询输血代码信息
 * @param params
 * @returns {*}
 */
export function queryTransfusionCodeInfo (params) {
  return request({
    url: '/TransfusionCodeConfigController/queryTransfusionCodeInfo',
    method: 'post',
    params: params
  })
}
/**
 * 删除输血代码信息
 * @param params
 * @returns {*}
 */
export function deleteTransfusionCodeInfo (params) {
  return request({
    url: '/TransfusionCodeConfigController/deleteTransfusionCodeInfo',
    method: 'post',
    params: params
  })
}



/**
 * 添加输血代码信息
 * @param params
 * @returns {*}
 */
export function insertTransfusionCodeInfo (params) {
  return request({
    url: '/TransfusionCodeConfigController/insertTransfusionCodeInfo',
    method: 'post',
    params: params
  })
}
/**
 * 修改输血代码信息
 * @param params
 * @returns {*}
 */
export function updateTransfusionCodeInfo (params) {
  return request({
    url: '/TransfusionCodeConfigController/updateTransfusionCodeInfo',
    method: 'post',
    params: params
  })
}
/**
 * 文件上传
 * @param params
 * @returns {*}
 */
export function transfusionCodeUpload (params) {
  return request({
    url: '/TransfusionCodeConfigController/transfusionCodeUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}

/**
 * 护士模板下载
 * @param params
 * @returns {*}
 */
export function downTransfusionCodeTemplate (params) {
  return request({
    url: '/TransfusionCodeConfigController/downTransfusionCodeTemplate',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}
