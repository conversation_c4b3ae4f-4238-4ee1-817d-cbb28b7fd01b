import request from '@/utils/request'

/**
 * 查询病组参数配置数据
 * @param params
 * @returns {*}
 */
export function selectDiseaseGroupData (params) {
  return request({
    url: '/RegulatoryParametersConfigController/selectDiseaseGroupData',
    method: 'post',
    params: params
  })
}
/**
 * 通过ID删除数据
 * @param params
 * @returns {*}
 */
export function deleteDiseaseGroup (params) {
  return request({
    url: '/RegulatoryParametersConfigController/deleteDiseaseGroup',
    method: 'post',
    params: params
  })
}
/**
 * 添加病组数据
 * @param params
 * @returns {*}
 */
export function insertDiseaseGroup (params) {
  return request({
    url: '/RegulatoryParametersConfigController/insertDiseaseGroup',
    method: 'post',
    params: params
  })
}
/**
 * 修改数据Byid
 * @param params
 * @returns {*}
 */
export function updateDiseaseGroup (params) {
  return request({
    url: '/RegulatoryParametersConfigController/updateDiseaseGroup',
    method: 'post',
    params: params
  })
}
/**
 * 查询诊断参数配置数据
 * @param params
 * @returns {*}
 */
export function selectDiagnosisData (params) {
  return request({
    url: '/RegulatoryParametersConfigController/selectDiagnosisData',
    method: 'post',
    params: params
  })
}
/**
 * 添加诊断数据
 * @param params
 * @returns {*}
 */
export function insertDiagnosis (params) {
  return request({
    url: '/RegulatoryParametersConfigController/insertDiagnosis',
    method: 'post',
    params: params
  })
}
/**
 * 查询手术参数配置数据
 * @param params
 * @returns {*}
 */
export function selectOperationData (params) {
  return request({
    url: '/RegulatoryParametersConfigController/selectOperationData',
    method: 'post',
    params: params
  })
}
/**
 * 添加手术数据
 * @param params
 * @returns {*}
 */
export function insertOperation (params) {
  return request({
    url: '/RegulatoryParametersConfigController/insertOperation',
    method: 'post',
    params: params
  })
}
/**
 * 查询标准病组数据
 * @param params
 * @returns {*}
 */
export function selectDiseaseGroup (params) {
  return request({
    url: '/RegulatoryParametersConfigController/selectDiseaseGroup',
    method: 'post',
    params: params
  })
}
/**
 * 查询标准诊断数据
 * @param params
 * @returns {*}
 */
export function selectDiagnosis (params) {
  return request({
    url: '/RegulatoryParametersConfigController/selectDiagnosis',
    method: 'post',
    params: params
  })
}

/**
 * 查询标准手术数据
 * @param params
 * @returns {*}
 */
export function selectOperation (params) {
  return request({
    url: '/RegulatoryParametersConfigController/selectOperation',
    method: 'post',
    params: params
  })
}

/**
 * 病组参数模板下载
 * @param params
 * @returns {*}
 */
export function downPatientTemplate (params) {
  return request({
    url: '/RegulatoryParametersConfigController/downPatientTemplate',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}

/**
 * 诊断参数模板下载
 * @param params
 * @returns {*}
 */
export function downDiagnosticTemplate (params) {
  return request({
    url: '/RegulatoryParametersConfigController/downDiagnosticTemplate',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}

/**
 * 手术参数模板下载
 * @param params
 * @returns {*}
 */
export function downOperationTemplate (params) {
  return request({
    url: '/RegulatoryParametersConfigController/downOperationTemplate',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}
/**
 * 病组参数模板上传
 * @param params
 * @returns {*}
 */
export function patientTemplateUpload (params) {
  return request({
    url: '/RegulatoryParametersConfigController/patientTemplateUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}
/**
 * 诊断参数模板上传
 * @param params
 * @returns {*}
 */
export function diagnosticTemplateUpload (params) {
  return request({
    url: '/RegulatoryParametersConfigController/diagnosticTemplateUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}

/**
 * 手术参数模板上传
 * @param params
 * @returns {*}
 */
export function operationTemplateUpload (params) {
  return request({
    url: '/RegulatoryParametersConfigController/operationTemplateUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}
