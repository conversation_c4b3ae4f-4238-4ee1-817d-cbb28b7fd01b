<template>
  <div class="medical-record">
    <el-loading :visible="isSubmitting" text="正在加载" spinner="el-icon-loading"></el-loading>
    <table>
      <!-- 患者基本信息 -->
      <tr>
        <th>患者年龄(必填)</th>
        <th>不足一周岁天龄</th>
        <th>性别</th>
        <th>参保地</th>
      </tr>

      <tr>
        <td>
          <input type="number" v-model.number="patient.age" placeholder="请输入年龄 " style="width: 95%" />
        </td>
        <td>
          <input type="number" v-model.number="patient.days" placeholder="请输入天龄"style="width: 95%" />
        </td>
        <td style="text-align: center;">
          <select v-model="patient.sex" style="height: 30px; width: 95%">
            <option value="1">男性</option>
            <option value="2">女性</option>
          </select>
        </td>

          <td style="text-align: center;">
            <select v-model="patient.insuplc" style="height: 30px; width: 95%">
              <option value="0">市医保</option>
              <option value="1">省内异地</option>
              <option value="2">省外异地</option>
              <option value="3">省本级</option>
              <option value="4">未传值</option>
            </select>

        </td>

      </tr>
      <tr>
        <th>住院天数(必填)</th>
        <th>住院总费用(必填)</th>
        <th>参保类型</th>
        <th>离院方式</th>
      </tr>
      <tr>
        <td>
          <input type="number" v-model.number="patient.hosDays" placeholder="请输入天数" style="width: 95%"/>
        </td>

        <td>
          <input type="number" v-model.number="patient.zyFee" placeholder="请输入住院费用" style="width: 95%" />
        </td>
        <td style="text-align: center;">
          <select v-model="patient.canBaoType" style="height: 30px; width: 95%">
            <option value="310">城镇职工</option>
            <option value="390">城乡居民</option>
          </select>
        </td>
        <td style="text-align: center;">
          <select v-model="patient.lyfs" style="height: 30px; width: 95%">
            <option value="1">医嘱离院</option>
            <option value="2">医嘱转院</option>
            <option value="3">医嘱转社区卫生服务机构/乡镇卫生院</option>
            <option value="4">非医嘱离院</option>
            <option value="5">死亡</option>
            <option value="9">其他</option>
          </select>
        </td>
      </tr>

      <tr>
<!--        <th>基本医疗基金费用</th>-->
        <th>新生儿出生体重</th>
        <th>新生儿入院体重</th>
      </tr>
      <tr>
<!--        <td>-->
<!--          <input type="number" v-model.number="patient.jjfy" placeholder="请输入基本医疗基金费用" style="width: 95%" />-->
<!--        </td>-->
        <td>
          <input type="number" v-model.number="patient.cstz" placeholder="患者为新生儿请输入出生体重" style="width: 95%"/>
        </td>

        <td>
          <input type="number" v-model.number="patient.rytz" placeholder="患者为新生儿请输入出生体重" style="width: 95%" />
        </td>
      </tr>
    </table>

    <table>
      <!-- 诊断信息 -->
      <tr>
        <th>诊断信息</th>
        <th>手术操作</th>
      </tr>
      <tr>
        <td>
          <div v-for="(diags, index) in diagnoses" :key="'diag-' + index" >
            <el-select
              v-model="diags.value"
              filterable
              remote
              :remote-method="query => remoteDiagSearch(query, index)"
              :loading="diagLoading[index]"
              placeholder="请输入诊断编码"
              @change="diagsCode(index, diags.value)"
              style="width: 36%;"
            >
              <el-option
                v-for="item in diagOptions[index]"
                :key="item.dscgDiagCodg"
                :label="item.dscgDiagCodg"
                :value="item.dscgDiagCodg"
              >
                <span style="float: left">{{ item.diagCodgAndName }}</span>
              </el-option>
            </el-select>
            <el-select
              v-model="diags.value"
              filterable
              remote
              :remote-method="query => remoteDiagSearchName(query, index)"
              :loading="diagLoading[index]"
              placeholder="请输入诊断名称"
              @change="diagsCode(index, diags.value)"
              style="width: 36%;"
            >
              <el-option
                v-for="item in diagOptions[index]"
                :key="item.dscgDiagCodg"

                :label="item.dscgDiagName"
                :value="item.dscgDiagCodg"
              >
                <span style="float: left">{{ item.diagCodgAndName }}</span>
              </el-option>
            </el-select>
            <button v-if="index === 0" @click="clearMainDiagnosis">清空主诊断</button>
            <button v-if="index > 0" @click="removeDiagnosis(index)">删除</button>
            <span v-if="index === 0" style="color: red">这是主诊断</span>
          </div>
          <button @click="addDiagnosis">添加诊断</button>
        </td>
        <td>
          <div v-for="(oprn, index) in operations" :key="'oprn-' + index">
            <el-select
              v-model="oprn.value"
              filterable
              remote
              :remote-method="query => remoteOprnSearch(query, index)"
              :loading="oprnLoading[index]"
              placeholder="请输入手术编码"
              @change="oprnsCode(index, oprn.value)"
              style="width: 36%;"
            >
              <el-option
                v-for="item in oprnOptions[index]"
                :key="item.oprnCode"
                :label="item.oprnCode"
                :value="item.oprnCode"
              >
                <span style="float: left">{{ item.oprnCodeAndName }}</span>
              </el-option>
            </el-select>
            <el-select
              v-model="oprn.value"
              filterable
              remote
              :remote-method="query => remoteOprnSearchName(query, index)"
              :loading="oprnLoading[index]"
              placeholder="请输入手术名称"
              @change="oprnsCode(index, oprn.value)"
              style="width: 36%;"
            >
              <el-option
                v-for="item in oprnOptions[index]"
                :key="item.oprnCode"
                :label="item.oprnName"
                :value="item.oprnCode"
              >
                <span style="float: left">{{ item.oprnCodeAndName }}</span>
              </el-option>
            </el-select>
            <button v-if="index === 0" @click="clearMainOperation">清空主手术</button>
            <button v-if="index > 0" @click="removeOperation(index)">删除</button>
            <span v-if="index === 0" style="color: red">这是主手术</span>
          </div>
          <button @click="addOperation">添加手术</button>
        </td>
      </tr>
    </table>

    <!-- 特殊护理信息 -->
    <table>
      <tr>
        <th colspan="2">特殊护理信息</th>
      </tr>
      <tr>
        <td>
          重症病房时长（小时）：<input
          type="number"
          v-model.number="patient.icuHours"
          placeholder="请转为小时后输入"
          style="width: 60%"
        />
        </td>
        <td>
          呼吸机使用（小时）：<input
          type="number"
          v-model.number="patient.hxjHours"
          placeholder="请转为小时后输入"
          style="width: 60%"
        />
        </td>
      </tr>
    </table>

    <!-- drg结果 -->
    <div style="margin-top: 20px"></div>
    <template v-if="preResultArray[0].drgCodg">
      <el-table :data="preResultArray" style="width: 100%" border>
        <el-table-column prop="drgCodg" label="入组编码" width="180"> </el-table-column>
        <el-table-column prop="drgName" label="入组名称" width="300"> </el-table-column>
        <el-table-column prop="drgAvgCost" label="标杆均费"> </el-table-column>
        <el-table-column prop="fycy" label="与标杆费用差异"> </el-table-column>
        <el-table-column prop="refer_sco" label="基准分值"> </el-table-column>
        <el-table-column prop="adjm_cof" label="调节系数"> </el-table-column>
        <el-table-column prop="totl_sco" label="总分值"> </el-table-column>
        <el-table-column prop="forecast_fee" label="预测费用"> </el-table-column>
        <el-table-column prop="profitloss" label="与预测金额差异"> </el-table-column>
        <el-table-column prop="type" label="病案类型"> </el-table-column>
        <el-table-column prop="drgAvgDays" label="标杆住院天数"> </el-table-column>
        <el-table-column prop="inHospDayDiff" label="住院天数差异"> </el-table-column>
        <el-table-column prop="ccList" label="一般合并症并发症"> </el-table-column>
        <el-table-column prop="mccList" label="严重合并症并发症"> </el-table-column>
      </el-table>
    </template>
    <!-- dip结果 -->
    <div style="margin-top: 20px"></div>
    <template v-if="preResultArray[0].dipCodg">
      <el-table :data="preResultArray" style="width: 100%" border>
        <el-table-column prop="dipCodg" label="入组编码" width="180"> </el-table-column>
        <el-table-column prop="dipName" label="入组名称" width="300"> </el-table-column>
        <el-table-column prop="dipAvgCost" label="标杆费用"> </el-table-column>
        <el-table-column prop="fycy" label="与标杆费用差异"> </el-table-column>
        <el-table-column prop="refer_sco" label="基准分值"> </el-table-column>
        <el-table-column prop="adjm_cof" label="调节系数"> </el-table-column>
        <el-table-column prop="totl_sco" label="总分值"> </el-table-column>
        <el-table-column prop="forecast_fee" label="预测费用"> </el-table-column>
        <el-table-column prop="profitloss" label="与预测金额差异"> </el-table-column>
        <el-table-column prop="dipAvgDays" label="标杆住院天数"> </el-table-column>
        <el-table-column prop="inHospDayDiff" label="住院天数差异"> </el-table-column>
        <el-table-column prop="type" label="病案类型"> </el-table-column>
<!--        <el-table-column prop="presonBearCost" label="个人负担"> </el-table-column>-->
<!--        <el-table-column prop="preFundFee" label="预测报销"> </el-table-column>-->
<!--        <el-table-column prop="fundRatio" label="补偿比"> </el-table-column>-->
<!--        <el-table-column prop="fundFeeDiff" label="基金差异"> </el-table-column>-->
<!--        <el-table-column prop="fundSourceType" label="基金来源"> </el-table-column>-->
      </el-table>
    </template>

    <hr/>

    <template>
      <div style="text-align: center;">
      <el-button type="success" @click="submitPatient" :loading="isSubmitting" class="submit">将信息提交到分组器</el-button >
      </div>
    </template>
  </div>
</template>

<script>
import {
  getDiag,
  getOprns,
  getPreGroupResult,
  getHosId,
  getDiagName,
  getOprnsName
} from '@/api/medicalQuality/settleList'

export default {
  name: 'SimulatePreGroup',
  data() {
    return {
      patient: {
        age: null,
        hosDays: null,
        sex: '1',
        insuplc:'0',
        rytz: null,
        cstz: null,
        jjfy: null,
        days: 0,
        icuHours: null,
        hxjHours: null,
        yiBaoQuyu: null,
        canBaoType: '310',
        hosId: null,
        zyFee: null,
        lyfs:'1',
        diagCodes: [],
        oprnsCode: []
      },
      diagnoses: [{ value: '' }],
      diagOptions: [[]],
      diagLoading: [false],
      operations: [{ value: '' }],
      oprnOptions: [[]],
      oprnLoading: [false],
      //预分组结果
      preResult: {},
      preMessage: '',
      isSubmitting: false
    }
  },
  computed: {
    preResultArray() {
      return [this.preResult]
    }
  },
  mounted() {
    this.getHosInfo()
  },
  methods: {
    getHosInfo() {
      getHosId().then(res => {
        this.patient.hosId = res.data
      })
    },
    addDiagnosis() {
      this.diagnoses.push({ value: '' })
      this.diagOptions.push([])
      this.diagLoading.push(false)
    },
    removeDiagnosis(index) {
      if (index > 0) {
        this.diagnoses.splice(index, 1)
        this.diagOptions.splice(index, 1)
        this.diagLoading.splice(index, 1)
        this.patient.diagCodes.splice(index, 1)
      }
    },
    addOperation() {
      this.operations.push({ value: '' })
      this.oprnOptions.push([])
      this.oprnLoading.push(false)
    },
    removeOperation(index) {
      if (index > 0) {
        this.operations.splice(index, 1)
        this.oprnOptions.splice(index, 1)
        this.oprnLoading.splice(index, 1)
      }
    },
    // 清空主诊断
    clearMainDiagnosis() {
      if (this.diagnoses.length > 0) {
        this.diagnoses[0].value = ''
        this.diagOptions[0] = []
        this.patient.diagCodes[0] = ''
      }
    },
    // 清空主手术
    clearMainOperation() {
      if (this.operations.length > 0) {
        this.operations[0].value = ''
        this.oprnOptions[0] = []
        this.patient.oprnsCode[0] = ''
      }
    },
    diagsCode(index, val) {
      this.$set(this.patient.diagCodes, index, val)
    },
    oprnsCode(index, val) {
      this.$set(this.patient.oprnsCode, index, val)
    },
    remoteDiagSearch(query, index) {
      if (query.length > 3 && /^[a-zA-Z0-9.]+$/.test(query)) {
      this.diagLoading[index] = true
      getDiag({ query })
        .then(res => {
          // this.diagOptions[index] = res.data
          this.$set(this.diagOptions, index, res.data)
          this.diagLoading[index] = false
        })
        .catch(() => {
          this.diagLoading[index] = false
        })
      }
    },
    //通过名称查询
    remoteDiagSearchName(query, index) {
      if (query.length >1) {
        this.diagLoading[index] = true
        getDiagName({query})
          .then(res => {
            this.$set(this.diagOptions, index, res.data)
            this.diagLoading[index] = false
          })
          .catch(() => {
            this.diagLoading[index] = false
          })
      }
    },

    remoteOprnSearch(query, index) {
      if (query.length > 3 && /^[a-zA-Z0-9.]+$/.test(query)) {
      this.oprnLoading[index] = true
      getOprns({ query })
        .then(res => {
          this.$set(this.oprnOptions, index, res.data)
          this.oprnLoading[index] = false
        })
        .catch(() => {
          this.oprnLoading[index] = false
        })
      }
    },
    //通过名称查询
    remoteOprnSearchName(query, index) {
      if (query.length >1) {
        this.oprnLoading[index] = true
        getOprnsName({query})
          .then(res => {
            this.$set(this.oprnOptions, index, res.data)
            this.oprnLoading[index] = false
          })
          .catch(() => {
            this.oprnLoading[index] = false
          })
      }
    },

    submitPatient() {
      this.isSubmitting = true
      this.preResult = {}
      getPreGroupResult(this.patient)
        .then(res => {
          if (res.data.drgCodg != null || res.data.dipCodg != null) {
            this.preResult = res.data
            this.preMessage = res.message
            window.console.log(this.preResult)
          } else {
            this.$message.error('未入组，请调整输入信息')
          }
        })
        .finally(() => {
          this.isSubmitting = false
        })
    }
  }
}
</script>

<style scoped>
.medical-record {
  padding: 20px;
  font-family: Arial, sans-serif;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

th,
td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

th {
  background-color: #f5f5f5;
}

input {
  width: 100px;
  padding: 5px;
  margin-left: 10px;
}

button {
  padding: 5px 10px;
  background-color: #1b65b9;
  color: white;
  border: none;
  cursor: pointer;
}

button:hover {
  background-color: #45a049;
}

.patient-info {
  margin-top: 20px;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.info-row div {
  display: flex;
  align-items: center;
}

.el-select {
  margin-bottom: 15px;
}
.submit {
  height: 40px;
  width: 200px;
  margin-top: 20px;
}

/* 设置输入框 placeholder 颜色 */
input::placeholder {
  color: #c6c6c6;
  opacity: 1;
}

.el-table__header th {
  background-color: #f5f5f5;
}

/* 定义第一行灰色背景的样式 */
.first-row-gray {
  background-color: #f5f5f5;
}
</style>
