<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-date-range
             show-in-date-range
             :container="true"
             headerTitle="查询条件"
             contentTitle="统计图表"
             @query="queryData">

      <!-- 内容 -->
      <template slot="containerContent">
        <el-row :gutter="10" style="height: 50%">
          <el-col :span="12">
            <drg-echarts :options="options" class="forecast-col-content" />
          </el-col>
          <el-col :span="12">
            <drg-echarts :options="options" class="forecast-col-content" />
          </el-col>
        </el-row>
        <el-row :gutter="10" style="height: 50%">
          <el-col :span="12">
            <drg-echarts :options="options" class="forecast-col-content" />
          </el-col>
          <el-col :span="12">
            <drg-echarts :options="options" class="forecast-col-content" />
          </el-col>
        </el-row>
      </template>
    </drg-form>
  </div>
</template>

<script>
export default {
  name: 'listStatistics',
  data: () => ({
    queryForm: {},
    options: {}
  }),
  mounted () {
    this.pieCount()
  },
  methods: {
    getParams () {
      let params = Object.assign({}, queryForm)
      return params
    },
    queryData () {},

    pieCount () {
      let option = {
        title: {
          text: 'Referer of a Website',
          subtext: 'Fake Data',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: '50%',
            data: [
              { value: 1048, name: 'Search Engine' },
              { value: 735, name: 'Direct' },
              { value: 580, name: 'Email' },
              { value: 484, name: 'Union Ads' },
              { value: 300, name: 'Video Ads' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.options = option
    }
  }
}
</script>

<style lang="scss" scoped></style>
