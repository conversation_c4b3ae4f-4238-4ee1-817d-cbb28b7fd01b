import axios from 'axios'
import { Message, MessageBox } from 'element-ui'
import store from '../store'
import './requestParams'
import { getToken, removeToken, removeMenuTree } from '@/utils/auth'

// 设置请求默认为json
// axios.defaults.headers.post['Content-Type']='application/json;charse=UTF-8'
// 创建axios实例
const service = axios.create({
  baseURL: process.env.BASE_API // api的base_url
  // timeout: 30000 // 请求超时时间
})

// request拦截器
service.interceptors.request.use(config => {
  if (store.getters.token) {
    config.headers['Authorization'] = getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
  }
  return config
}, error => {
  // Do something with request error
  Promise.reject(error)
})

// respone拦截器
service.interceptors.response.use(
  response => {
    /**
     * code为非200是抛错 可结合自己业务进行修改
     */
    if (response.data instanceof Blob) {
      return response.data
    }
    // 直接输出流
    if (response.config && response.config.responseType == 'arraybuffer') {
      return response
    }
    const res = response.data
    if (res.code !== 200) {
      let msg = res.message
      if (!msg) {
        msg = '调用出现小意外'
      }
      if (msg === '10075') {
        let time = window.sessionStorage.getItem('time')
        if (time === '-1') {
          Message({
            message: '系统订阅已过期',
            type: 'error'
          })
        } else if (time === '0') {

        } else {
          window.sessionStorage.setItem('time', '0')
          MessageBox.prompt('验证码', '系统订阅', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then((value) => {
            axios.post('/sysInitController/verifyCode', { verifyCode: value.value }, { baseURL: process.env.BASE_API }).then(res => {
              let message = ''
              if (res.data.data === '1') {
                window.sessionStorage.removeItem('time')
                this.$prompt.closeAll()
                message = '认证成功'
              } else {
                window.sessionStorage.setItem('time', '-1')
                message = '认证失败'
              }
              Message({
                message: message,
                type: 'info'
              })
            })
          }).catch(() => {
            window.sessionStorage.setItem('time', '-1')
            store.dispatch('FedLogOut').then(() => {
              location.reload()// 为了重新实例化vue-router对象 避免bug
            })
          })
        }
      } else {
        Message({
          message: msg,
          type: 'error'
        })
      }

      // 401:未登录;
      if (res.code === 401 || res.code === 403) {
        MessageBox.confirm('你已被登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('FedLogOut').then(() => {
            location.reload()// 为了重新实例化vue-router对象 避免bug
          })
        })
      }
      return Promise.reject(new Error('error'))
    } else {
      return response.data
    }
  },
  error => {
    if (error.message === 'Network Error') {
      removeToken()
      removeMenuTree()
      if (!window.location.href.includes('/login')) {
        window.location = window.location.href.split('#')[0]
      }
    }
    console.log('err' + error)// for debug
    Message({
      message: error.message,
      type: 'error',
      duration: 3 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
