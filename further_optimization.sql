-- ========================================
-- 基于执行计划的进一步优化方案
-- ========================================

-- 🔥 方案1: 创建缺失的关键索引
-- 针对执行计划中仍在全表扫描的表创建索引

-- 为 hcm_rule_cfg 表创建索引（解决d表全表扫描）
CREATE INDEX idx_hcm_rule_cfg_detl_year ON hcm_rule_cfg(rule_detl_codg, rule_year);

-- 为 hcm_data_grp_cfg 表创建更精确的索引（解决b表全表扫描）
CREATE INDEX idx_hcm_data_grp_cfg_optimized ON hcm_data_grp_cfg(data_code, data_grp_code, rule_year);

-- 如果oprn_date经常用于年份提取，考虑添加函数索引（MySQL 8.0+）
-- CREATE INDEX idx_hcm_valid_result_inhosp_oprn_year ON hcm_valid_result_inhosp((YEAR(oprn_date)));

-- 🔥 方案2: 重写查询以优化JOIN顺序
-- 基于执行计划，调整JOIN顺序让小表先连接
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_valid_result_inhosp a
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
WHERE
  a.rule_scen_type = '1'
  AND EXISTS (
    SELECT 1 FROM hcm_settle_zy_b e 
    WHERE e.hisid = a.unique_id 
    AND e.discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
  )
LIMIT 200;

-- 🔥 方案3: 使用STRAIGHT_JOIN强制JOIN顺序
-- 如果MySQL选择了错误的JOIN顺序，可以强制指定
SELECT STRAIGHT_JOIN
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  (
    SELECT * FROM hcm_valid_result_inhosp 
    WHERE rule_scen_type = '1'
    AND unique_id IN (
      SELECT hisid FROM hcm_settle_zy_b 
      WHERE discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
    )
  ) a
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
LIMIT 200;

-- 🔥 方案4: 分步查询优化版本
-- 基于执行计划，先处理最有选择性的条件
WITH filtered_patients AS (
  SELECT hisid 
  FROM hcm_settle_zy_b 
  WHERE discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
),
filtered_violations AS (
  SELECT a.*, LEFT(a.oprn_date, 4) as oprn_year
  FROM hcm_valid_result_inhosp a
  INNER JOIN filtered_patients p ON a.unique_id = p.hisid
  WHERE a.rule_scen_type = '1'
)
SELECT
  fv.rule_valid_result_id AS id,
  fv.unique_id AS uniqueId,
  fv.rule_scen_type AS ruleScenType,
  fv.rule_detl_codg AS ruleDetlCodg,
  fv.rule_data_meta AS ruleDataMeta,
  fv.error_desc AS errorDesc,
  fv.error_detail_codg AS errorDetailCodg,
  fv.violation_amount AS violationAmount,
  fv.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  fv.cnt,
  fv.pric,
  fv.vola_deg AS volaDeg
FROM
  filtered_violations fv
  INNER JOIN hcm_rule_cfg d ON fv.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = fv.oprn_year
  INNER JOIN hcm_data_grp_cfg b ON fv.med_list_codg = b.data_code
    AND fv.oprn_year = b.rule_year
    AND b.data_grp_code = fv.rule_data_meta
LIMIT 200;

-- 🔥 方案5: 如果LEFT函数是性能瓶颈，预计算年份
-- 如果可能，考虑在表中添加年份字段或使用触发器维护
-- 临时解决方案：使用YEAR函数（如果支持函数索引）
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_valid_result_inhosp a
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = YEAR(a.oprn_date)
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND YEAR(a.oprn_date) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
WHERE
  a.rule_scen_type = '1'
  AND EXISTS (
    SELECT 1 FROM hcm_settle_zy_b e 
    WHERE e.hisid = a.unique_id 
    AND e.discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
  )
LIMIT 200;

-- ========================================
-- 立即执行的索引创建语句
-- ========================================

-- 执行这些索引创建语句来解决当前的全表扫描问题：
CREATE INDEX idx_hcm_rule_cfg_detl_year ON hcm_rule_cfg(rule_detl_codg, rule_year);
CREATE INDEX idx_hcm_data_grp_cfg_optimized ON hcm_data_grp_cfg(data_code, data_grp_code, rule_year);

-- 更新表统计信息
ANALYZE TABLE hcm_rule_cfg;
ANALYZE TABLE hcm_data_grp_cfg;

-- ========================================
-- 验证索引效果
-- ========================================

-- 检查索引是否创建成功
SHOW INDEX FROM hcm_rule_cfg WHERE Key_name LIKE 'idx_%';
SHOW INDEX FROM hcm_data_grp_cfg WHERE Key_name LIKE 'idx_%';

-- 重新执行EXPLAIN查看改进效果
-- EXPLAIN [您选择的优化查询];
