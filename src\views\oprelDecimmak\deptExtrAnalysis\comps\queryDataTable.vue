<template>
  <div style="height: 100%">
    <el-table
      :data="tableData"
      :summary-method="getSummaries"
      show-summary
      :id="id"
      ref="elTable"
      :header-cell-style="{'text-align':'center'}"
      height="100%"
      v-loading="tableLoading"
      border>
      <el-table-column label="序号" type="index" align="center"/>
      <el-table-column label="科室编码" prop="deptCode" align="center" v-if="false"/>
      <el-table-column label="科室名称" prop="deptName" :fixed="include('deptName')" width="150px" align="left"/>
      <el-table-column label="病案数" prop="medcasVal" :fixed="include('medicalNum')" width="100px" align="center"/>
      <el-table-column label="入组病案数" prop="inGroupMedicalNum" :fixed="include('inGroupMedicalNum')"  align="center"
                       width="90px">
        <template slot-scope="scope">
          <div v-if="Number(scope.row.inGroupMedicalNum)>0" class='skip' @click="queryInGroupMedicalNum(scope.row)">
            {{ scope.row.inGroupMedicalNum | formatIsEmpty }}
          </div>
          <div v-if="Number(scope.row.inGroupMedicalNum)==0" style="color:#000000">
            {{ scope.row.inGroupMedicalNum | formatIsEmpty }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="入组率" prop="inGroupRate" :fixed="include('inGroupRate')" align="center">
        <template slot-scope="scope">
          {{ formatRate(scope.row.inGroupRate) }}
        </template>
      </el-table-column>
      <el-table-column label="未入组病案数" prop="nonInGroupMedicalNum" :fixed="include('nonInGroupMedicalNum')"
                       align="center" width="100px"/>
      <el-table-column label="不存在标杆病案数" prop="nonBenchmarkNum" :fixed="include('nonBenchmarkNum')" width="125px"
                       align="center"/>

      <el-table-column label="正常付费人数" prop="normalNum" :fixed="include('normalNum')" align="center" width="100px">
        <template slot-scope="scope">
          <div v-if="Number(scope.row.normalNum)>0" class='skip' @click="queryNormalNum(scope.row)">
            {{ scope.row.normalNum | formatIsEmpty }}
          </div>
          <div v-if="Number(scope.row.normalNum)==0" style="color:#000000">
            {{ scope.row.normalNum | formatIsEmpty }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="超高病案数" prop="ultrahighNum" :fixed="include('ultrahighNum')" align="center"
                       width="90px">
        <template slot-scope="scope">
          <div v-if="Number(scope.row.ultrahighNum)>0" class='skip' @click="queryUltrahighNum(scope.row)">
            {{ scope.row.ultrahighNum | formatIsEmpty }}
          </div>
          <div v-if="Number(scope.row.ultrahighNum)==0" style="color:#000000">
            {{ scope.row.ultrahighNum | formatIsEmpty }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="超高率" prop="ultrahighRate" :fixed="include('ultrahighRate')" align="center">
        <template slot-scope="scope">
          {{ formatRate(scope.row.ultrahighRate) }}
        </template>
      </el-table-column>
      <el-table-column label="超低病案数" prop="ultraLowNum" align="center" :fixed="include('ultraLowNum')" width="90px">
        <template slot-scope="scope">
          <div v-if="Number(scope.row.ultraLowNum)>0" class='skip' @click="queryUltraLowNum(scope.row)">
            {{ scope.row.ultraLowNum | formatIsEmpty }}
          </div>
          <div v-if="Number(scope.row.ultraLowNum)==0" style="color:#000000">
            {{ scope.row.ultraLowNum | formatIsEmpty }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="超低率" prop="ultraLowRate" :fixed="include('ultraLowRate')" align="center">
        <template slot-scope="scope">
          {{ formatRate(scope.row.ultraLowRate) }}
        </template>
      </el-table-column>
      <el-table-column label="基层病案数" prop="ultrabaseNum" :fixed="include('ultrabaseNum')"
                       align="center" width="100px"/>
      <el-table-column label="其他病案数" prop="ultraotherNum" :fixed="include('ultraotherNum')"
                       align="center" width="100px"/>
      <el-table-column label="药品费" prop="drugfee"  :fixed="include('medicalCost')" width="120px" align="right"></el-table-column>
      <el-table-column label="药品费占比" prop="medicalCostRate" :fixed="include('medicalCostRate')" align="center"
                       width="120px"></el-table-column>
      <el-table-column label="耗材费" prop="mcsFee" :fixed="include('materialCost')" width="120px" align="right"></el-table-column>
      <el-table-column label="耗材费占比" prop="materialCostRate" :fixed="include('materialCostRate')"  align="center"
                       width="120px"></el-table-column>
      <el-table-column label="总费用" prop="sumfee" align="right" :fixed="include('totalCost')" width="120px"/>
      <el-table-column label="预测金额" prop="forecastAmount" align="right" :fixed="include('forecastAmount')"
                       width="85px"/>
      <el-table-column label="院前检查费" prop="preHosExamineCost" width="100px" align="right" :fixed="include('preHosExamineCost')" v-if="grperType == 1"/>
      <el-table-column label="预测金额差异" prop="forecastAmountDiff" width="100px"
                       :fixed="include('forecastAmountDiff')" align="right">
        <template slot-scope="scope">
          <span :class="[parseFloat(scope.row.forecastAmountDiff) < 0 ? 'som-color-error' : 'som-color-success']">
            {{ scope.row.forecastAmountDiff }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="费用状态" width="100px" align="center" fixed="right">
        <template slot-scope="scope">
          <i class="som-icon-error-waring som-icon-big" v-if="parseFloat(scope.row.forecastAmountDiff) < 0"></i>
          <i class="som-icon-success som-icon-big" v-else></i>
        </template>
      </el-table-column>
      <el-table-column label="详情" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-search" fixed="right" circle
                     @click="queryDetails(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { formatRate } from '@/utils/common'

export default {
  name: 'queryDataTable',
  props: {
    tableData: {
      type: Array
    },
    tableLoading: {
      type: Boolean,
      default: false
    },
    queryForm: {
      type: Object
    },
    queryType: {
      type: Number
    },
    id: {
      type: String
    },
    columnOptions: {
      type: Array,
      default: () => []
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    costSection: ''
  }),
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '0'
      }
    }
  },
  updated () {
    this.$nextTick(() => {
      this.$refs['elTable'].doLayout()
    })
  },
  methods: {
    formatRate,
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length,
        customAverage: (values) => {
          const sum = values.reduce((prev, curr) => prev + curr, 0)
          return sum / values.length
        }
      }
      let sum12 = 0, sum14 = 0, sum16 = 0
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计'
          return
        }
        const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value))
        if (index === 2 || index === 3 || index === 7 || index === 5 || index === 6 || index === 8 || index === 10) {
          sums[index] = calculations.sum(values)
        } else if (index === 12 || index === 14 || index === 16 || index === 17 || index === 18) {
          sums[index] = calculations.sum(values).toFixed(2)
          if (index === 12) sum12 = sums[index]
          else if (index === 14) sum14 = sums[index]
          else if (index === 16) sum16 = sums[index]
        } else if (index === 4 || index === 9 || index === 11) {
          sums[index] = calculations.average(values).toFixed(2) + '%'
        } else {
          sums[index] = ' '
        }
      })
      sums[13] = calculatePercentage(sum12, sum16)
      sums[15] = calculatePercentage(sum14, sum16)
      return sums

      function calculatePercentage (numerator, denominator) {
        if (denominator !== 0) {
          return ((numerator / denominator) * 100).toFixed(2) + '%'
        }
        return '0.00%'
      }
    },
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    include (column) {
      return this.fixedColumns.includes(column)
    },
    queryDetails (row) {
      this.$router.push({
        path: '/oprelDecimmak/diseExtrAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime,
          dateRange: this.queryForm.dateRange,
          deptCode: row.deptCode,
          group: this.queryType
        }
      })
    },
    queryInGroupMedicalNum (row) {
      this.goto('/oprelDecimmak/pattExtrAnalysis', {
        begnDate: this.queryForm.begnDate,
        expiDate: this.queryForm.expiDate,
        inStartTime: this.queryForm.inStartTime,
        inEndTime: this.queryForm.inEndTime,
        seStartTime: this.queryForm.seStartTime,
        seEndTime: this.queryForm.seEndTime,
        dateRange: this.queryForm.dateRange,
        group: this.queryType,
        deptCode: row.deptCode
      })
    },
    queryNormalNum (row) {
      this.goto('/oprelDecimmak/pattExtrAnalysis', {
        begnDate: this.queryForm.begnDate,
        expiDate: this.queryForm.expiDate,
        inStartTime: this.queryForm.inStartTime,
        inEndTime: this.queryForm.inEndTime,
        seStartTime: this.queryForm.seStartTime,
        seEndTime: this.queryForm.seEndTime,
        dateRange: this.queryForm.dateRange,
        group: this.queryType,
        deptCode: row.deptCode,
        costSection: 3
      })
    },
    queryUltrahighNum (row) {
      this.goto('/oprelDecimmak/pattExtrAnalysis', {
        begnDate: this.queryForm.begnDate,
        expiDate: this.queryForm.expiDate,
        inStartTime: this.queryForm.inStartTime,
        inEndTime: this.queryForm.inEndTime,
        seStartTime: this.queryForm.seStartTime,
        seEndTime: this.queryForm.seEndTime,
        dateRange: this.queryForm.dateRange,
        group: this.queryType,
        deptCode: row.deptCode,
        costSection: 1
      })
    },
    queryUltraLowNum (row) {
      this.goto('/oprelDecimmak/pattExtrAnalysis', {
        begnDate: this.queryForm.begnDate,
        expiDate: this.queryForm.expiDate,
        inStartTime: this.queryForm.inStartTime,
        inEndTime: this.queryForm.inEndTime,
        seStartTime: this.queryForm.seStartTime,
        seEndTime: this.queryForm.seEndTime,
        dateRange: this.queryForm.dateRange,
        group: this.queryType,
        deptCode: row.deptCode,
        costSection: 2
      })
    }
  }
}
</script>
