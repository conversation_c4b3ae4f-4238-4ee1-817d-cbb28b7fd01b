import request from '@/utils/request'
/**
 * 上传月度分组详情文件
 * @param params
 * @returns {*}
 */
export function fileUpload (params) {
  return request({
    url: '/hosMonthMedicalDetailsUploadController/medicalUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}
/**
 * 查询上传月度分组详情文件日志
 * @param params
 * @returns {*}
 */
export function queryMedicalUploadLog (params) {
  return request({
    url: '/hosMonthMedicalDetailsUploadController/queryMedicalUploadLog',
    method: 'post',
    params: params
  })
}

export function downloadMedicalTemplate (params) {
  return request({
    url: '/hosMonthMedicalDetailsUploadController/downloadMedicalTemplate',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}
export function selectDetails (params) {
  return request({
    url: '/hosMonthMedicalDetailsUploadController/selectDetails',
    method: 'post',
    params: params
  })
}
