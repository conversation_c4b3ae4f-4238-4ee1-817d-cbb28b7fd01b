import request from '@/utils/request'
export function fetchList (params) {
  return request({
    url: '/drgsSafetyAndQuality/getList',
    method: 'post',
    params: params
  })
}

export function getCountInfo (params) {
  return request({
    url: '/drgsSafetyAndQuality/getCountInfo',
    method: 'post',
    params: params
  })
}

export function fetchListDip (params) {
  return request({
    url: '/dipSafetyAndQuality/getList',
    method: 'post',
    params: params
  })
}

export function getCountInfoDip (params) {
  return request({
    url: '/dipSafetyAndQuality/getCountInfo',
    method: 'post',
    params: params
  })
}
