<template>
  <div class="som-comps-container">
    <!-- 基本信息 -->
    <el-descriptions title="基本信息" :column="4" border>
      <el-descriptions-item v-for="(item, index) in baseInfoData"
                            :key="index"
                            :label="item.label">
        {{ item.value }}
      </el-descriptions-item>
    </el-descriptions>

    <!-- 入组分析 -->
    <el-descriptions title="入组分析"
                     :column="4"
                     :colon="false"
                     border
                     class="descriptions">

      <!-- DIP 标题 -->
      <el-descriptions-item label="DIP"
                            :span="4"
                            label-class-name="medical-quality-analysis-des-label"
                            content-class-name="medical-quality-analysis-des-content"
                            v-if="inDipGroupData.length > 0"/>

      <!-- dip -->
        <template v-for="(item, index) in inDipGroupData">
      <el-descriptions-item
                            :key="index"
                            :label="item.label"
                            :content-style="{ borderRight: (index == inDipGroupData.length - 1)
                              && inDipGroupData.length % 4 != 0 ? 'none': ''}"
                            :label-style="{ width: '7rem' }"
                            label-class-name="has-colon"
                            :contentStyle="{background: getCompareColor(item)}"
                            v-if="inDipGroupData.length > 0">

        <el-tooltip :content="item.value"
                    placement="top-start"
                    :class="[item.overflow ? item.label == '编码' ? 'des-item-content-code' : 'des-item-content' : '']"
                    v-if="item.overflow">
          <div>
            {{ item.value }}
          </div>
        </el-tooltip>
        <div v-else>
          {{ item.value }}
        </div>
      </el-descriptions-item>
        </template>

      <!-- 分隔 -->
      <el-descriptions-item :span="4"
                            label-class-name="medical-quality-analysis-des-separate"
                            :content-style="{borderLeft: 'none'}"
                            :key="1"
                            v-if="inDrgGroupData.length > 0 && inDipGroupData.length % 4 != 0"/>

      <!-- DRG 标题 -->
      <el-descriptions-item label="DRG"
                            :span="4"
                            label-class-name="medical-quality-analysis-des-label"
                            content-class-name="medical-quality-analysis-des-content"
                            v-if="inDrgGroupData.length > 0" />

      <!-- DRG -->
      <el-descriptions-item v-for="(item, index) in inDrgGroupData"
                            :key="index"
                            :label="item.label"
                            :label-style="{ width: '7rem' }"
                            :contentStyle="{background: getCompareColor(item)}"
                            label-class-name="has-colon">
        <el-tooltip :content="item.value"
                    placement="top-start"
                    :class="[item.overflow ? item.label == '编码' ? 'des-item-content-code' : 'des-item-content' : '']"
                    v-if="item.overflow">
          <div>
            {{ item.value }}
          </div>
        </el-tooltip>
        <div v-else>
          {{ item.value }}
        </div>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 费用分析 -->
    <el-descriptions title="费用分析" :column="4" class="descriptions" border>
      <el-descriptions-item v-for="(item, index) in costDataPay"

                            :key="index"
                            :label="item.label">
        {{ item.value }}
      </el-descriptions-item>
    </el-descriptions>

    <!-- 支付分析 -->
    <el-descriptions title="支付分析" :column="4" class="descriptions" border>
      <!-- DIP 标题 -->
      <el-descriptions-item label="DIP"
                            :span="4"
                            label-class-name="medical-quality-analysis-des-label"
                            content-class-name="medical-quality-analysis-des-content"
                            v-if="ycCostData.length > 0"/>

      <!-- DIP 内容 -->
      <el-descriptions-item v-for="(item, index) in ycCostData"
                            :key="index"
                            :label="item.label"
                            :contentStyle="{background: getCompareColor(item)}">
        {{ item.value }}
      </el-descriptions-item>

      <!-- 分隔 -->
      <el-descriptions-item :span="4"
                            label-class-name="medical-quality-analysis-des-separate"
                            :content-style="{borderLeft: 'none'}"
                            :key="1"
                            v-if="ycCostData.length > 0 && ycCostData.length % 4 != 0"/>

      <!-- DRG 标题 -->
      <el-descriptions-item label="DRG"
                            :span="4"
                            label-class-name="medical-quality-analysis-des-label"
                            content-class-name="medical-quality-analysis-des-content"
                            v-if="drgYcCostData.length > 0" />

      <!-- DRG 内容 -->
      <el-descriptions-item v-for="(item, index) in drgYcCostData"
                            :key="index"
                            :contentStyle="{background: getCompareColor(item)}"
                            :label="item.label">
        {{ item.value }}
      </el-descriptions-item>
    </el-descriptions>

    <!-- 支撑最后一个 descriptions 和底部高度 -->
    <div style="height: 2%" />

    <div class="check-info" :style="[ rightAdjust ? {right: '0'} : {right: '-2%'} ]">
      <el-button type="primary"
                 icon="el-icon-arrow-left"
                 @click="fnBackPage">
        返回
      </el-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'pplComparAnalysisDescription',
  props: {
    data: [],
    otherPageData: [],
    otherGroupData: [],
    groupData: [],
    costData: [],
    costPayData: [],
    rightAdjust: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    baseInfoData: [],
    inDipGroupData: [],
    inDrgGroupData: [],
    ycCostData: [],
    costDataPay: [],
    drgYcCostData: []
  }),
  methods: {
    fnBackPage () {
      this.$emit('backPage', '')
    },
    handlerData (data) {
      this.baseInfoData = []
      this.baseInfoData.push({ label: '姓名', value: data.name })
      this.baseInfoData.push({ label: '性别', value: this.$somms.getDictValueByType(data.gend, 'GENDER') })
      this.baseInfoData.push({ label: '年龄', value: data.age })
      this.baseInfoData.push({ label: '病案号', value: data.patientId })
      this.baseInfoData.push({ label: '入院时间', value: data.inHosTime })
      this.baseInfoData.push({ label: '出院时间', value: data.outHosTime })
      this.baseInfoData.push({ label: '住院天数', value: data.inHosdays })
      this.baseInfoData.push({ label: '入院科室', value: data.indeptname })
      this.baseInfoData.push({ label: '出院科室', value: data.outdeptname })
      this.baseInfoData.push({ label: '参保类型', value: this.$somms.getDictValueByType(data.insuredType, 'INSURANCE_TYPE') })
    },
    handlerGroupData (data, otherData) {
      this.inDipGroupData = []
      this.inDrgGroupData = []
      if (data.group.indexOf('1') > -1) {
        this.inDipGroupData = []
        this.inDrgGroupData = []
        this.inDipGroupData.push({ label: '编码', value: data.dipcode, overflow: true })
        this.inDipGroupData.push({ label: '名称', value: data.dipname, overflow: true })
        this.inDipGroupData.push({ label: '住院天数', value: data.inHosdays })
        this.inDipGroupData.push({ label: '住院天数(区域)', value: data.dipareaStandardIndays })
        this.inDipGroupData.push({ label: '住院天数(级别)', value: data.diplevelStandardIndays })
        if (otherData) {
          let type = (Math.abs(data.dipareaStandardIndays) == Math.abs(otherData.dipareaStandardIndays)) ? 0 : (Math.abs(data.dipareaStandardIndays) > Math.abs(otherData.dipareaStandardIndays) ? 1 : 2)
          let type1 = (Math.abs(data.diplevelStandardIndays) == Math.abs(otherData.diplevelStandardIndays)) ? 0 : (Math.abs(data.diplevelStandardIndays) > Math.abs(otherData.diplevelStandardIndays) ? 1 : 2)
          if (data.dipareaStandardIndays && otherData.dipareaStandardIndays) {
            this.inDipGroupData.push({ label: '天数差值（区域与本院）', value: data.dipareaStandardIndays, type: type })
          } else {
            this.inDipGroupData.push({ label: '天数差值（区域与本院）', value: data.dipareaStandardIndays })
          }
          if (data.diplevelStandardIndays && otherData.diplevelStandardIndays) {
            this.inDipGroupData.push({ label: '天数差值（级别与本院）', value: data.diplevelStandardIndays, type: type1 })
          } else {
            this.inDipGroupData.push({ label: '天数差值（级别与本院）', value: data.diplevelStandardIndays })
          }
        } else {
          this.inDipGroupData.push({ label: '天数差值（区域与本院）', value: data.dipareaStandardIndays })
          this.inDipGroupData.push({ label: '天数差值（级别与本院）', value: data.diplevelStandardIndays })
        }
        this.inDipGroupData.push({ label: '住院总费用', value: data.zong })
        this.inDipGroupData.push({ label: '标杆费用(区域)', value: data.dipareaStandardCost })
        this.inDipGroupData.push({ label: '标杆费用(级别)', value: data.diplevelStandardCost })
        if (otherData) {
          let type = (Math.abs(data.dipqyfycy) == Math.abs(otherData.dipqyfycy)) ? 0 : (Math.abs(data.dipqyfycy) > Math.abs(otherData.dipqyfycy) ? 1 : 2)
          let type1 = (Math.abs(data.dipjbfycy) == Math.abs(otherData.dipjbfycy)) ? 0 : (Math.abs(data.dipjbfycy) > Math.abs(otherData.dipjbfycy) ? 1 : 2)
          if (data.dipqyfycy && otherData.dipqyfycy) {
            this.inDipGroupData.push({ label: '费用差异（区域与本院）', value: data.dipqyfycy, type: type })
          } else {
            this.inDipGroupData.push({ label: '费用差异（区域与本院）', value: data.dipqyfycy })
          }
          if (data.dipjbfycy && otherData.dipjbfycy) {
            this.inDipGroupData.push({ label: '费用差异（级别与本院）', value: data.dipjbfycy, type: type1 })
          } else {
            this.inDipGroupData.push({ label: '费用差异（级别与本院）', value: data.dipjbfycy })
          }
        } else {
          this.inDipGroupData.push({ label: '费用差异（区域与本院）', value: data.dipqyfycy })
          this.inDipGroupData.push({ label: '费用差异（级别与本院）', value: data.dipjbfycy })
        }
      }
      if (data.group.indexOf('3') > -1) {
        this.inDipGroupData = []
        this.inDrgGroupData = []
        this.inDrgGroupData.push({ label: '编码', value: data.drgcode, overflow: true })
        this.inDrgGroupData.push({ label: '名称', value: data.drgname, overflow: true })
        this.inDrgGroupData.push({ label: '住院天数', value: data.inHosdays })
        this.inDrgGroupData.push({ label: '标杆住院日', value: data.drgindayavg })
        if (otherData) {
          let type2 = (Math.abs(data.drgindaycy) == Math.abs(otherData.drgindaycy)) ? 0 : (Math.abs(data.drgindaycy) > Math.abs(otherData.drgindaycy) ? 1 : 2)
          if (data.drgindaycy && otherData.drgindaycy) {
            this.inDrgGroupData.push({ label: '住院天数差异', value: data.drgindaycy, type: type2 })
          } else {
            this.inDrgGroupData.push({ label: '住院天数差异', value: data.drgindaycy })
          }
        } else {
          this.inDrgGroupData.push({ label: '住院天数差异', value: data.drgindaycy })
        }
        this.inDrgGroupData.push({ label: '住院总费用', value: data.zong })
        this.inDrgGroupData.push({ label: '标杆住院费用', value: data.drgavgcost })
        if (otherData) {
          let type2 = (Math.abs(data.drgfycy) == Math.abs(otherData.drgfycy)) ? 0 : (Math.abs(data.drgfycy) > Math.abs(otherData.drgfycy) ? 1 : 2)
          if (data.drgfycy && otherData.drgfycy) {
            this.inDrgGroupData.push({ label: '住院费用差异', value: data.drgfycy, type: type2 })
          } else {
            this.inDrgGroupData.push({ label: '住院费用差异', value: data.drgfycy })
          }
        } else {
          this.inDrgGroupData.push({ label: '住院费用差异', value: data.drgfycy })
        }
      }
      if (data.group.length == 0 || data.group.length == 2) {
        this.inDipGroupData = []
        this.inDrgGroupData = []
        this.inDipGroupData.push({ label: '编码', value: data.dipcode, overflow: true })
        this.inDipGroupData.push({ label: '名称', value: data.dipname, overflow: true })
        this.inDipGroupData.push({ label: '住院天数', value: data.inHosdays })
        this.inDipGroupData.push({ label: '住院天数(区域)', value: data.dipareaStandardIndays })
        this.inDipGroupData.push({ label: '住院天数(级别)', value: data.diplevelStandardIndays })
        if (otherData) {
          let type = (Math.abs(data.dipareaStandardIndays) == Math.abs(otherData.dipareaStandardIndays)) ? 0 : (Math.abs(data.dipareaStandardIndays) > Math.abs(otherData.dipareaStandardIndays) ? 1 : 2)
          let type1 = (Math.abs(data.diplevelStandardIndays) == Math.abs(otherData.diplevelStandardIndays)) ? 0 : (Math.abs(data.diplevelStandardIndays) > Math.abs(otherData.diplevelStandardIndays) ? 1 : 2)
          if (data.dipareaStandardIndays && otherData.dipareaStandardIndays) {
            this.inDipGroupData.push({ label: '天数差值（区域与本院）', value: data.dipareaStandardIndays, type: type })
          } else {
            this.inDipGroupData.push({ label: '天数差值（区域与本院）', value: data.dipareaStandardIndays })
          }
          if (data.diplevelStandardIndays && otherData.diplevelStandardIndays) {
            this.inDipGroupData.push({ label: '天数差值（级别与本院）', value: data.diplevelStandardIndays, type: type1 })
          } else {
            this.inDipGroupData.push({ label: '天数差值（级别与本院）', value: data.diplevelStandardIndays })
          }
        } else {
          this.inDrgGroupData.push({ label: '天数差值（区域与本院）', value: data.dipareaStandardIndays })
          this.inDrgGroupData.push({ label: '天数差值（级别与本院）', value: data.diplevelStandardIndays })
        }
        this.inDipGroupData.push({ label: '住院总费用', value: data.zong })
        this.inDipGroupData.push({ label: '标杆费用(区域)', value: data.dipareaStandardCost })
        this.inDipGroupData.push({ label: '标杆费用(级别)', value: data.diplevelStandardCost })
        if (otherData) {
          let type = (Math.abs(data.dipqyfycy) == Math.abs(otherData.dipqyfycy)) ? 0 : (Math.abs(data.dipqyfycy) > Math.abs(otherData.dipqyfycy) ? 1 : 2)
          let type1 = (Math.abs(data.dipjbfycy) == Math.abs(otherData.dipjbfycy)) ? 0 : (Math.abs(data.dipjbfycy) > Math.abs(otherData.dipjbfycy) ? 1 : 2)
          if (data.dipqyfycy && otherData.dipqyfycy) {
            this.inDipGroupData.push({ label: '费用差异（区域与本院）', value: data.dipqyfycy, type: type })
          } else {
            this.inDipGroupData.push({ label: '费用差异（区域与本院）', value: data.dipqyfycy })
          }
          if (data.dipjbfycy && otherData.dipjbfycy) {
            this.inDipGroupData.push({ label: '费用差异（级别与本院）', value: data.dipjbfycy, type: type1 })
          } else {
            this.inDipGroupData.push({ label: '费用差异（级别与本院）', value: data.dipjbfycy })
          }
        } else {
          this.inDrgGroupData.push({ label: '费用差异（区域与本院）', value: data.dipqyfycy })
          this.inDrgGroupData.push({ label: '费用差异（级别与本院）', value: data.dipjbfycy })
        }
        this.inDrgGroupData.push({ label: '编码', value: data.drgcode, overflow: true })
        this.inDrgGroupData.push({ label: '名称', value: data.drgname, overflow: true })
        this.inDrgGroupData.push({ label: '住院天数', value: data.inHosdays })
        this.inDrgGroupData.push({ label: '标杆住院日', value: data.drgindayavg })
        if (otherData) {
          let type2 = (Math.abs(data.drgindaycy) == Math.abs(otherData.drgindaycy)) ? 0 : (Math.abs(data.drgindaycy) > Math.abs(otherData.drgindaycy) ? 1 : 2)
          if (data.drgindaycy && otherData.drgindaycy) {
            this.inDrgGroupData.push({ label: '住院天数差异', value: data.drgindaycy, type: type2 })
          } else {
            this.inDrgGroupData.push({ label: '住院天数差异', value: data.drgindaycy })
          }
        } else {
          this.inDrgGroupData.push({ label: '住院天数差异', value: data.drgindaycy })
        }
        // this.inDrgGroupData.push({label: '住院天数差异', value: data.drgindaycy})
        this.inDrgGroupData.push({ label: '住院总费用', value: data.zong })
        this.inDrgGroupData.push({ label: '标杆住院费用', value: data.drgavgcost })
        if (otherData) {
          let type2 = (Math.abs(data.drgfycy) == Math.abs(otherData.drgfycy)) ? 0 : (Math.abs(data.drgfycy) > Math.abs(otherData.drgfycy) ? 1 : 2)
          if (data.drgfycy && otherData.drgfycy) {
            this.inDrgGroupData.push({ label: '住院费用差异', value: data.drgfycy, type: type2 })
          } else {
            this.inDrgGroupData.push({ label: '住院费用差异', value: data.drgfycy })
          }
        } else {
          this.inDrgGroupData.push({ label: '住院费用差异', value: data.drgfycy })
        }
      }
    },
    handlerCostData (data, otherData) {
      this.ycCostData = []
      this.drgYcCostData = []
      if (data.group.indexOf('1') > -1) {
        this.ycCostData = []
        this.drgYcCostData = []
        this.ycCostData.push({ label: '预测费用', value: data.dippredictCost })
        this.ycCostData.push({ label: '总费用', value: data.zong })
        this.ycCostData.push({ label: '支付差异', value: data.dipcy })
        if (otherData) {
          let type = (Math.abs(data.dipcy) == Math.abs(otherData.dipcy)) ? 0 : (Math.abs(data.dipcy) > Math.abs(otherData.dipcy) ? 1 : 2)
          if (data.dipcy && otherData.dipcy) {
            this.ycCostData.push({ label: '支付差异', value: data.dipcy, type: type })
          }
        }
        this.ycCostData.push({ label: '费用差异比例', value: data.dipcybl })
      }
      if (data.group.indexOf('3') > -1) {
        this.ycCostData = []
        this.drgYcCostData = []
        this.drgYcCostData.push({ label: '预测费用', value: data.predictCost })
        this.drgYcCostData.push({ label: '总费用', value: data.zong })
        if (otherData) {
          let type = (Math.abs(data.drgcy) == Math.abs(otherData.drgcy)) ? 0 : (Math.abs(data.drgcy) > Math.abs(otherData.drgcy) ? 1 : 2)
          if (data.drgcy && otherData.drgcy) {
            this.drgYcCostData.push({ label: '支付差异', value: data.drgcy, type: type })
          }
        }
        this.drgYcCostData.push({ label: '费用差异比例', value: data.drgcybl })
      }
      if (data.group.length == 0 || data.group.length == 2) {
        this.ycCostData = []
        this.drgYcCostData = []
        this.ycCostData.push({ label: '预测费用', value: data.dippredictCost })
        this.ycCostData.push({ label: '总费用', value: data.zong })
        // this.ycCostData.push({ label: '标杆费用(区域)', value: data.dipareaStandardCost })
        // this.ycCostData.push({ label: '标杆费用(级别)', value: data.diplevelStandardCost })
        this.ycCostData.push({ label: '费用差异比例', value: data.dipcybl })
        this.drgYcCostData.push({ label: '预测费用', value: data.predictCost })
        this.drgYcCostData.push({ label: '总费用', value: data.zong })
        if (otherData) {
          let type = (Math.abs(data.drgcy) == Math.abs(otherData.drgcy)) ? 0 : (Math.abs(data.drgcy) > Math.abs(otherData.drgcy) ? 1 : 2)
          let type1 = (Math.abs(data.dipcy) == Math.abs(otherData.dipcy)) ? 0 : (Math.abs(data.dipcy) > Math.abs(otherData.dipcy) ? 1 : 2)
          if (data.drgcy && otherData.drgcy) {
            this.drgYcCostData.push({ label: '支付差异', value: data.drgcy, type: type })
          } else {
            this.drgYcCostData.push({ label: '支付差异', value: data.drgcy })
          }
          if (data.dipcy && otherData.dipcy) {
            this.ycCostData.push({ label: '支付差异', value: data.dipcy, type: type1 })
          } else {
            this.ycCostData.push({ label: '支付差异', value: data.dipcy })
          }
        } else {
          this.drgYcCostData.push({ label: '支付差异', value: data.drgcy })
          this.ycCostData.push({ label: '支付差异', value: data.dipcy })
        }
        this.drgYcCostData.push({ label: '费用差异比例', value: data.drgcybl })
      }
    },
    handlerCostPayData (data) {
      this.costDataPay = []
      this.costDataPay.push({ label: '床位费', value: data.cwf })
      this.costDataPay.push({ label: '诊查费', value: data.zcf })
      this.costDataPay.push({ label: '检查费', value: data.jcf })
      this.costDataPay.push({ label: '化验费', value: data.hyf })
      this.costDataPay.push({ label: '治疗费', value: data.treat_fee })
      this.costDataPay.push({ label: '手术费', value: data.ssf })
      this.costDataPay.push({ label: '护理费', value: data.nursfee })
      this.costDataPay.push({ label: '卫生材料费', value: data.wsclf })
      this.costDataPay.push({ label: '西药费', value: data.west_fee })
      this.costDataPay.push({ label: '中药饮片费', value: data.zyypf })
      this.costDataPay.push({ label: '中成药', value: data.zcy })
      this.costDataPay.push({ label: '一般治疗费', value: data.ybzlf })
      this.costDataPay.push({ label: '挂号费', value: data.ghf })
      this.costDataPay.push({ label: '其他费', value: data.qt })
    },
    getCompareColor (item) {
      if (item && item.type) {
        if (item.type == 1) {
          return '#FDE2E2'
        } else if (item.type == 2) {
          return '#E1F3D8'
        }
      }
      return ''
    }
  },
  watch: {
    data: function (data) {
      this.handlerData(data)
    },
    groupData: function (data) {
      this.handlerGroupData(data)
    },
    costData: function (data) {
      this.handlerCostData(data)
    },
    costPayData: function (data) {
      this.handlerCostPayData(data)
    },
    otherPageData: function (data) {
      if (this.costData) {
        this.handlerCostData(this.costData, data)
      }
    },
    otherGroupData: function (data) {
      if (this.groupData) {
        this.handlerGroupData(this.groupData, data)
      }
    }
  }
}
</script>
<style scoped>
.check-info{
  position: absolute;
  top: -1%;
  font-size: var(--textSize);
}
.descriptions{
  margin-top: 1rem;
}
.des-item-content{
  width: 8rem;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.des-item-content-code {
  width: 5rem;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.item-up{
  background: #E1F3D8;
}
.item-down{
  background: #FDE2E2;
}

</style>
