<template>
  <div style="display: flex;height: 100%;width: 100%">
    <div style="width: 100%;border: 1px black solid;display: inline"
         v-loading="loading"
         element-loading-background="white"
         :element-loading-text="loadingText"
         element-loading-spinner="el-icon-loading">
      <div style="width: 100%;height: 5%;display: flex">
        <span style="float: left;font-size: 17px;font-weight: bold;margin: 8px 5px 5px 5px;">医疗保障基金结算清单</span>
        <el-radio-group  v-model="selectDic" style="margin-top:5px;margin-left:20px;" size="mini"  @change="changeSelectDic">
          <el-radio-button :label="1" >结算清单</el-radio-button>
<!--          <el-radio-button :label="2">病案首页</el-radio-button>-->
<!--          <el-radio-button :label="3">测试</el-radio-button>-->
<!--          <el-radio-button :label="4">提交</el-radio-button>-->
        </el-radio-group>
        <div style="margin-top:5px;margin-left:20px;" size="mini">
<!--          <el-tooltip class="item" effect="dark" content="数据修改后、通过此按钮可以查看数据修改后的分组及校验情况、不会提交数据到数据库" placement="bottom">-->
<!--            <el-button @click="dialogVisible = true,changePre()">测试</el-button>-->
<!--          </el-tooltip>-->
          <el-tooltip class="item" effect="dark" content="数据修改后、通过此按钮可以提交到数据库、也可查看修改后的预分组及校验情况" placement="bottom">
            <el-button @click="changeData()">提交</el-button>
          </el-tooltip>
<!--          <el-tooltip class="item" effect="dark" content="确认无误后请点击此按钮" placement="bottom">-->
<!--            <el-button @click="clickLookOver()">完成</el-button>-->
<!--          </el-tooltip>-->
<!--          <el-tooltip class="item" effect="dark" content="历史数据修改详情" placement="bottom">-->
<!--            <el-button @click="historyDialogVisible = true" >历史数据修改情况</el-button>-->
<!--          </el-tooltip>-->
          <el-button @click="drawer=true">预分组</el-button>
          <el-button @click="drawerJy=true;selectPreBusSettleLIstError()">预校验</el-button>
          <div style="position:relative;top: 8%;width: 10%;right: 1%;cursor: pointer;z-index: 1000;position: fixed;">
            <el-switch
              :value="switchValue"
              active-color="#13ce66"
              inactive-color="#ff4949"
              @change="clickLookOver()"
              active-text="完成"
              :disabled="switchValue"
              inactive-text="未完成">
            </el-switch>
          </div>
        </div>

<!--   抽屉 /预分组    -->
        <el-drawer
          title="1"
          size="36.5%"
          :visible.sync="drawer"
          :direction="direction"
          :with-header="false"
          >
          <iframe :src="url" height="920px" width="700px"></iframe>
        </el-drawer>

        <!--   抽屉 /预校验    -->
        <el-drawer
          title="校验情况"
          size="30%"
          :visible.sync="drawerJy"
          :direction="direction"
          >
          <div style="width: 100%;height: 100%">
            <div style="max-height: 25rem;overflow-y: auto">
              <el-button @click="changeRed" style="float: right;margin: 0px 20px 5px;">展示所有错误</el-button>
              <el-tabs v-model="activeName" >
                  <template v-for="(item,index) in errorShowList">
                <el-tab-pane :key="'a'+index" :label="item.label"  :name="item.value" v-if="item.value<5" >
                  <el-table
                    :data="item.error"
                    @row-click="tableRowClick"
                    style="max-height: 50%;overflow-y: auto">
                    <el-table-column
                      label="序号"
                      type="index">
                    </el-table-column>
<!--                    <el-table-column-->
<!--                      prop="fld"-->
<!--                      label="错误字段"-->
<!--                    >-->
<!--                    </el-table-column>-->
                    <el-table-column
                      prop="error"
                      label="错误原因"
                    >
                    </el-table-column>
                  </el-table>
                </el-tab-pane>
                  </template>
              </el-tabs>
            </div>
            <div style="max-height: 28rem;overflow-y: auto">
                <template v-for="(item,index) in errorShowList">
              <el-card class="box-card"  :key="'b'+index" :name="item.value" v-if="item.value>4 && item.error" shadow="hover" style="margin: 15px ">
                <div slot="header" style="background: #fdf6ec;color: #e6a23c;">
                  <span>{{item.label}}</span>
                </div>
                <div v-for="(itemDeep,index) in item.error" :key="'c'+index" style="font-size: 10px;margin-bottom: 18px;line-height: 20px;margin-left: 10px">
                  {{itemDeep.error}}
                </div>
              </el-card>
                </template>
            </div>
          </div>

        </el-drawer>

<!--  测试弹框-->
<!--        <el-dialog-->
<!--          title="数据修改详情"-->
<!--          :visible.sync="dialogVisible"-->
<!--          width="30%"-->
<!--          :before-close="handleClose">-->
<!--            <el-table-->
<!--              :data="this.changeMessage"-->
<!--              style="width: 100%;max-height: 600px;overflow-y: auto">-->
<!--              <el-table-column-->
<!--                prop="name"-->
<!--                label="修改字段"-->
<!--                >-->
<!--              </el-table-column>-->
<!--              <el-table-column-->
<!--                prop="old"-->
<!--                label="修改前">-->
<!--              </el-table-column>-->
<!--              <el-table-column-->
<!--                prop="after"-->
<!--                label="修改后"-->
<!--                >-->
<!--              </el-table-column>-->
<!--            </el-table>-->
<!--          <span slot="footer" class="dialog-footer">-->
<!--          <el-button @click="dialogVisible = false">取 消</el-button>-->
<!--          <el-button type="primary" @click="dialogVisible = false,clickSure()">确 定</el-button>-->
<!--          </span>-->
<!--        </el-dialog>-->
        <!--  历史修改数据弹框-->
<!--        <el-dialog-->
<!--          title="数据修改详情"-->
<!--          :visible.sync="historyDialogVisible"-->
<!--          width="30%"-->
<!--          :before-close="handleClose">-->
<!--          <el-collapse >-->
<!--            <div v-for="(item,key,index) in this.busHistoryModifyList" :key="index" style="height: 100%;width: 100%">-->
<!--              <el-collapse-item :title="'修改时间：'+key" :name="index" >-->
<!--               <el-table-->
<!--                 :data="item">-->
<!--                 <el-table-column-->
<!--                   prop="name"-->
<!--                   label="字段"-->
<!--                  >-->
<!--                 </el-table-column>-->
<!--                 <el-table-column-->
<!--                   prop="hisVal"-->
<!--                   label="修改前"-->
<!--                   >-->
<!--                 </el-table-column>-->
<!--                 <el-table-column-->
<!--                   prop="newVal"-->
<!--                   label="修改后"-->
<!--                   >-->
<!--                 </el-table-column>-->
<!--               </el-table>-->
<!--                <el-button style="float: right;margin: 10px" @click="restoreHistory(item,key)">还原到该时间节点</el-button>-->
<!--              </el-collapse-item>-->
<!--            </div>-->
<!--          </el-collapse>-->
<!--          <span slot="footer" class="dialog-footer">-->
<!--          <el-button @click="historyDialogVisible = false">取 消</el-button>-->
<!--          <el-button type="primary" @click="historyDialogVisible = false,closeDialogHistory()">确 定</el-button>-->
<!--          </span>-->
<!--        </el-dialog>-->

      </div>
      <div style="max-height: 92%;overflow-y: auto;margin: 20px 50px 0px 50px;font-size: 15px;" >
        <first-page-info v-if="show && showBa"
          :value="settleListParam"/>
        <settle-list-info v-if="showJs"
                          ref="settleListInfo"
                          :value="settleListParam"
                          :data="this.jycwzd"
                          :busMedicalCostList="this.busMedicalCostList2"
                          :busSettleLErrorList=this.busSettleLErrorList
                          :storeOperationList=this.storeOperationList
                          :storeXyDiseaseList=this.storeXyDiseaseList
                          :storeZyDiseaseList=this.storeZyDiseaseList
                          :storeOutpatientList=this.storeOutpatientList
                          :storeBusIcuList=this.storeBusIcuList
                          :deleteWatchData=this.deleteWatchData
                          @showChange-Message="showChangeMessage"
                          @update-showChange-Message="obtainExchangeData"
                          @selectPre-Result="selectPreResult"
                         />
      </div>
    </div>
  </div>
</template>
<script>
import firstPageInfo from './comps/firstPageInfo'
import settleListInfo from './comps/settleListInfo'
import { getSettleListAllInfo, getErrorInfo, selectBusSettleLIstError, updateSettleList, insertHistory, updateSettleListHisState, updateSettleListLookOver, restoreHistoryBusSettle, selectProcessResult } from '@/api/medicalQuality/settleListDetail'

const defaultSettleListParam = {
  somHiInvyBasInfo: {},
  busOutpatientClinicDiagnosisList: [],
  busDiseaseDiagnosisTrimList: [],
  busOperateDiagnosisList: []
}
export default {
  name: 'setlListDetailInfo',
  components: {
    'first-page-info': firstPageInfo,
    'settle-list-info': settleListInfo
  },
  data () {
    return {
      settleListParam: Object.assign({}, defaultSettleListParam),
      obtainData: Object.assign({}, defaultSettleListParam),
      showBa: false,
      show: false,
      showJs: true,
      switchValue: false,
      selectDic: 1,
      loading: false,
      DiseaseDiagnosisTrimListBa: [],
      busOutpatientListBa: [],
      busIcuListBa: [],
      busOperateDiagnosisListBa: [],
      storeOperationList: [],
      storeXyDiseaseList: [],
      storeZyDiseaseList: [],
      storeOutpatientList: [],
      storeBusIcuList: [],
      active: '',
      completeErrorNum: 0,
      allCompleteError: '无完整性校验错误！',
      logicErrorNum: 0,
      allLogicError: '无逻辑性校验错误！',
      refer_sco: 100.0,
      scoreMsg: '病案质量完整，无相应扣分！',
      errorNum: '',
      error: '病案质量完整，无相应扣分',
      completeErrors: {},
      logicErrors: {},
      scoreErrors: {},
      dipPreGroupTop1: [], // 预入组top1
      dipPreGroup: [], // 预入组所有数据
      curCheckedTag: 'preGroup',
      jyCheckedTag: 'nullError',
      busMedicalCostList2: [],
      xyDiseaseList: [],
      zyDiseaseList: [],
      errorShowList: [],
      busSettleLErrorList: [],
      nullErrorList: [],
      dictErrorList: [],
      regErrorList: [],
      lengthErrorList: [],
      // allErrorList:[],
      changeMessage: [],
      busHistoryModifyList: [],
      dialogVisible: false,
      historyDialogVisible: false,
      activeNames: ['0'],
      sureType: '1',
      deleteWatchData: '1',
      url: '',
      closeTips: '',
      value: '0',
      newSettleListId: '',
      timer: '',
      drawer: false,
      drawerJy: false,
      direction: 'rtl',
      errorDataByType: [],
      errorAllData: {},
      errorDeepAllData: [],
      loadingText: '拼命加载中',
      activeName: this.$store.getters.getSettleListDictByKey('QDJYCWLX')[0].value,
      jycwzd: {
        zd: ''
      },
      jyzd: [{
        zd: 'a01',
        yy: '填写不规范'
      }, {
        zd: 'a02',
        yy: '填写不规范'
      }]
    }
  },
  async mounted () {
    let _this = this
    window.onbeforeunload = function (e) {
      e = e || window.event
      if (_this.changeMessage.length < 1) {
        return
      }
      // 兼容IE8和Firefox 4之前的版本
      if (e) {
        e.returnValue = '关闭提示'
      }
    }
    let params = new URLSearchParams()
    params.append('id', this.$route.query.id)
    params.append('k00', this.$route.query.k00)
    await this.getData(params)
    await this.selectBusSettleLIstError(params)
    this.$nextTick(async () => {
      let data = await this.$refs.settleListInfo.pre('2')
      this.url = data.url
    })
  },
  methods: {
    // 查询预校验情况
    selectPreBusSettleLIstError () {
      let params = new URLSearchParams()
      params.append('id', this.$route.query.id)
      params.append('k00', this.$route.query.k00)
      this.selectBusSettleLIstError(params)
    },
    changeSureType () {
      this.queryChangData()
      this.sureType = '2'
      this.deleteWatchData = '2'
    },
    // 定时查询
    scheduledTask () {
      this.value++
      let params = { id: '' }
      params.id = this.newSettleListId
      selectProcessResult(params).then(res => {
        if (res.data.resNumber > 0) {
          let params = new URLSearchParams()
          params.append('id', this.$route.query.id)
          params.append('k00', this.$route.query.k00)
          this.getData(params)
          this.$message({ message: '提交成功', type: 'success' })
          this.loading = false
          this.loadingText = '拼命加载中'
          clearInterval(this.timer)
          this.value = 0
        } else if (this.value == '20') {
          clearInterval(this.timer)
          this.value = 0
          this.loading = false
          this.loadingText = '拼命加载中'
        }
      })
    },
    async getData (params) {
      this.loading = true
      await getSettleListAllInfo(params).then(response => {
        this.settleListParam = response.data
        // 判断map集合是否为空
        // if(this.settleListParam.somHiInvyBasInfo.hisTipState==0 && JSON.stringify(response.data.busHistoryModifyMap ) != JSON.stringify({}) ){
        //   this.historyDialogVisible=true
        // }else {
        //   this.historyDialogVisible=false
        // }
        // if(this.settleListParam.somHiInvyBasInfo.lookOver==1){
        //   this.switchValue=true
        // }
        // this.busHistoryModifyList=response.data.busHistoryModifyMap;
        this.DiseaseDiagnosisTrimListBa = [...response.data.busDiseaseDiagnosisTrimList]
        let busDiseaseLength = this.DiseaseDiagnosisTrimListBa.length
        const busDiseaseLengthMap = { busDiseaseLength: busDiseaseLength }
        Object.assign(this.settleListParam, busDiseaseLengthMap)
        this.busOperateDiagnosisListBa = [...response.data.busOperateDiagnosisList]
        let busOperateLength = this.busOperateDiagnosisListBa.length
        const busOperateLengthMap = { busOperateLength: busOperateLength }
        Object.assign(this.settleListParam, busOperateLengthMap)
        let busDiseaseListLength = 16 - this.DiseaseDiagnosisTrimListBa.length
        let busOperateDiagnosisListLength = 10 - this.busOperateDiagnosisListBa.length
        if (this.DiseaseDiagnosisTrimListBa.length < 17) {
          for (let i = 1; i <= busDiseaseListLength; i++) {
            this.DiseaseDiagnosisTrimListBa.push({
              c07n1: '-', c06c1: '-', c08c1: '-'
            })
          }
        }
        this.settleListParam.DiseaseDiagnosisTrimListBa = this.DiseaseDiagnosisTrimListBa
        if (this.DiseaseDiagnosisTrimListBa.length > 0) {
          this.DiseaseDiagnosisTrimListBa.forEach(item => {
            // 西医诊断list
            this.xyDiseaseList.push(
              { c06c1: item.c06c1, c07n1: item.c07n1, c08c1: item.c08c1 }
            )
          })
          this.DiseaseDiagnosisTrimListBa.forEach(item => {
            // 中医诊断list
            this.zyDiseaseList.push(
              { c06c2: item.c06c2 ? item.c06c2 : '-', c07n2: item.c07n2 ? item.c07n2 : '-', c08c2: item.c08c2 ? item.c08c2 : '-' }
            )
          })
          // this.xyDiseaseList=this.xyDiseaseList.slice(0,9)
          // this.zyDiseaseList=this.zyDiseaseList.slice(0,9)
          this.storeXyDiseaseList = JSON.parse(JSON.stringify(this.xyDiseaseList))
          this.storeZyDiseaseList = JSON.parse(JSON.stringify(this.zyDiseaseList))
          const xyDiseaseListMap = { xyDiseaseList: this.xyDiseaseList }
          const zyDiseaseListMap = { zyDiseaseList: this.zyDiseaseList }
          Object.assign(this.settleListParam, xyDiseaseListMap)
          Object.assign(this.settleListParam, zyDiseaseListMap)
        }
        // 手术编码
        if (this.busOperateDiagnosisListBa.length < 10) {
          for (let i = 1; i <= busOperateDiagnosisListLength; i++) {
            this.busOperateDiagnosisListBa.push({
              c36n: '-', c35c: '-', oprn_oprt_date: '-', c43: '-', oprn_oprt_oper_name: '-', c39c: '-', oprn_oprt_anst_dr_name: '-', oprn_oprt_anst_dr_code: '-', oprnOprtBegntime: '-', oprnOprtEndtime: '-', anstBgentime: '-', anstEndtime: '-'
            })
          }
        }
        this.settleListParam.busOperateDiagnosisListBa = this.busOperateDiagnosisListBa
        this.storeOperationList = JSON.parse(JSON.stringify(this.busOperateDiagnosisListBa))
        // 个人支付合计
        const busSettleBaseInfoList = response.data.somHiInvyBasInfo
        const zftableData = [
          { col1: '个人支付', col2: '个人自付', col3: busSettleBaseInfoList.d54 > 0 ? busSettleBaseInfoList.d54 : 0 },
          { col1: null, col2: '个人自费', col3: busSettleBaseInfoList.d55 > 0 ? busSettleBaseInfoList.d55 : 0 },
          { col1: null, col2: '个人账户支付', col3: busSettleBaseInfoList.d56 > 0 ? busSettleBaseInfoList.d56 : 0 },
          { col1: null, col2: '个人现金支付', col3: busSettleBaseInfoList.d57 > 0 ? busSettleBaseInfoList.d57 : 0 }
        ]
        // if(response.data.busFundPayList.length<2){
        //   const  busFundPayList= [
        //     {col1: '基金支付',fundPayType: '医保统筹基金支付',fundPayamt: ''},
        //     {col1: null,fundPayType: '其他支付：',fundPayamt: ''},
        //     {col1: null,fundPayType: '大病保险',fundPayamt: ''},
        //     {col1: null, fundPayType: '医疗救助', fundPayamt: ''},
        //     {col1: null, fundPayType: '公务员医疗补助', fundPayamt: ''},
        //     {col1: null, fundPayType: '大额补充', fundPayamt: ''},
        //     {col1: null, fundPayType: '企业补充', fundPayamt: ''},
        //     {col1: null, fundPayType: '......', fundPayamt: ''},
        //     {col1: null, fundPayType: '......', fundPayamt: ''}
        //   ];
        //   const busFundPayListMap = {busFundPayList:busFundPayList};
        //   Object.assign(this.settleListParam,busFundPayListMap);
        // }
        if (response.data.busFundPayList.length < 7) {
          for (let i = 0; i < 7 - response.data.busFundPayList.length; i++) {
            this.settleListParam.busFundPayList.push(
              { col1: null, fundPayType: '......', fundPayamt: '0' }
            )
          }
          // const busFundPayListMap = {busFundPayList:busFundPayList};
          // Object.assign(this.settleListParam,busFundPayListMap);
        }
        // 获取门诊慢特病科室和就诊日期信息
        const busOutpatientClinicDiagnosisList = response.data.busOutpatientClinicDiagnosisList
        if (busOutpatientClinicDiagnosisList.length > 0) {
          const deptCode = { deptCode: busOutpatientClinicDiagnosisList[0].deptCode }
          Object.assign(this.settleListParam, deptCode)
          const mdtrtDate = { mdtrtDate: busOutpatientClinicDiagnosisList[0].mdtrtDate }
          Object.assign(this.settleListParam, mdtrtDate)
        }
        // 补齐页面门慢门特
        this.busOutpatientListBa = [...response.data.busOutpatientClinicDiagnosisList]
        let busOutpatientListLength = 6 - this.busOutpatientListBa.length
        if (this.busOutpatientListBa.length < 6) {
          for (let i = 1; i <= busOutpatientListLength; i++) {
            this.busOutpatientListBa.push({
              diagName: '-', diagCode: '-', oprnOprtName: '-', oprnOprtCode: '-'
            })
          }
        }
        this.settleListParam.busOutpatientListBa = this.busOutpatientListBa
        // 赋值给新数组、不改变原有值
        this.storeOutpatientList = JSON.parse(JSON.stringify(this.busOutpatientListBa))
        // 补齐页面ICU
        this.busIcuListBa = [...response.data.busIcuList]
        let busIcuListLength = 3 - this.busIcuListBa.length
        if (this.busIcuListBa.length < 3) {
          for (let i = 1; i <= busIcuListLength; i++) {
            this.busIcuListBa.push({
              b40: '-', b41: '-', b42: '-', b43: '-'
            })
          }
        }
        this.settleListParam.busIcuListBa = this.busIcuListBa
        this.storeBusIcuList = JSON.parse(JSON.stringify(this.busIcuListBa))
        const zftableDataMap = { zftableData: zftableData }
        Object.assign(this.settleListParam, zftableDataMap)
        // this.getScoreDetail();
        // this.groupDiseaseInfo();
        if (this.settleListParam.somHiInvyBasInfo.a19c) {
          // this.settleListParam.somHiInvyBasInfo.a19c=this.$somms.getDictValueByType(this.settleListParam.somHiInvyBasInfo.a19c, 'MZ',2)
        }
        if (this.settleListParam.somHiInvyBasInfo.a53) {
          this.settleListParam.somHiInvyBasInfo.a53 = this.$somms.getDictValueByType(this.settleListParam.somHiInvyBasInfo.a53, 'HZZJLB', 2)
        }
        if (this.settleListParam.somHiInvyBasInfo.a38c) {
          this.settleListParam.somHiInvyBasInfo.a38c = this.$somms.getDictValueByType(this.settleListParam.somHiInvyBasInfo.a38c, 'ZY', 2)
        }
        // if(this.settleListParam.somHiInvyBasInfo.a54){
        //   this.settleListParam.somHiInvyBasInfo.a54=this.$somms.getDictValueByType(this.settleListParam.somHiInvyBasInfo.a54, 'ZY',2)
        // }
        if (this.settleListParam.somHiInvyBasInfo.a55) {
          this.settleListParam.somHiInvyBasInfo.a55 = this.$somms.getDictValueByType(this.settleListParam.somHiInvyBasInfo.a55, 'TSRYLX', 2)
        }
        // if(this.settleListParam.somHiInvyBasInfo.a57){
        //   this.settleListParam.somHiInvyBasInfo.a57=this.$somms.getDictValueByType(this.settleListParam.somHiInvyBasInfo.a57, 'XSELX',2)
        // }
        if (this.settleListParam.somHiInvyBasInfo.a33c) {
          let gxList = this.$somms.getDictValue('JTGX', 2)
          gxList.forEach(item => {
            if (item.value == this.settleListParam.somHiInvyBasInfo.a33c ||
              (this.settleListParam.somHiInvyBasInfo.a33c.endsWith('0') &&
                this.settleListParam.somHiInvyBasInfo.a33c.replace('0', '').includes(item.value))) {
              this.settleListParam.somHiInvyBasInfo.a33c = item.label
            }
          })
        }
        if (response.data.busMedicalCostList.length < 3) {
          response.data.busMedicalCostList.forEach(item => {
            if (item.claa == 0 && item.amt == 0 && item.clab == 0 &&
              item.oth == 0 && item.ownpay == 0 && item.medChrgItemname == '') {
              response.data.busMedicalCostList = []
            }
          })
        }
        if (response.data.busMedicalCostList.length < 1) {
          this.busMedicalCostList2.push(
            { medChrgItemname: '床位费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '诊察费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '检查费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '化验费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '治疗费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '手术费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '护理费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '卫生材料费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '西药费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '中药饮片费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '中成药费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '一般诊疗费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '挂号费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '其他费', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' },
            { medChrgItemname: '金额合计', amt: '-', claa: '-', clab: '-', ownpay: '-', oth: '-' }
          )
        } else {
          this.busMedicalCostList2 = response.data.busMedicalCostList
        }
        // if(this.settleListParam.somHiInvyBasInfo.a55){
        //   this.settleListParam.somHiInvyBasInfo.a55=this.$somms.getDictValueByType(this.settleListParam.somHiInvyBasInfo.a55, '')
        // }
        this.loading = false
        this.show = true
      }).catch(err => {
        console.log(err)
      })
    },
    async selectBusSettleLIstError (params) {
      await selectBusSettleLIstError(params).then(res => {
        if (res.code == 200) {
          this.dataTyping(res.data)
          this.addError(res.data[1])
          this.addError(res.data[2])
          this.addError(res.data[3])
          this.addError(res.data[4])
        }
      })
    },
    // 将数据按样式放置
    dataTyping (data) {
      this.errorShowList = JSON.parse(JSON.stringify(this.$store.getters.getSettleListDictByKey('QDJYCWLX')))
      this.errorAllData = data
      Object.keys(data).forEach(key => {
        this.errorShowList.forEach(item => {
          if (key == item.value) {
            item.error = data[key]
          }
        })
      })
    },
    addError (errorArr) {
      if (errorArr) {
        this.busSettleLErrorList.push(errorArr)
      }
    },
    changeRed () {
      this.$refs.settleListInfo.changeRed()
    },
    // 校验点击行
    tableRowClick (row) {
      this.jycwzd.zd = row.fld
    },
    changeSelectDic (value) {
      if (value == 1) {
        this.showBa = false
        this.showJs = true
      } else if (value == 2) {
        this.showBa = true
        this.showJs = false
      }
    },
    showChangeMessage (data) {
      this.changeMessage = data
      const map = new Map()
      this.changeMessage = data.filter(key => !map.has(key.zd) && map.set(key.zd, 1))
      this.changeMessage.forEach((item, index) => {
        if (item.old == item.after) {
          this.changeMessage.splice(index, 1)
        }
      })
      if (this.changeMessage.length > 0) {
        let url = window.location.href
        this.closeTips = url.slice(23)
      } else {
        this.closeTips = ''
      }
      // 菜单关闭是否启动提示
      // this.$store.commit("tagsView/SET_CLOSE_TIPS", this.closeTips)
    },
    changePre () {
      this.sureType = '1'
      this.queryChangData()
    },
    queryChangData () {
      this.$refs.settleListInfo.operationListChange()
      this.$refs.settleListInfo.xyDiseaseListChange()
      this.$refs.settleListInfo.zyDiseaseListChange()
      this.$refs.settleListInfo.OutpatientListChange()
      this.$refs.settleListInfo.BusIcuListChange()
    },
    clickSure () {
      if (this.sureType == '1') {
        this.loading = true
        this.selectPreResult()
        this.loading = false
      } else if (this.sureType == '2') {
        this.changeData()
      }
    },
    async selectPreResult () {
      if (this.changeMessage.length > 0) {
        let data = await this.$refs.settleListInfo.pre('1')
        this.$nextTick(function () {
          this.dataTyping(data.error)
          this.busSettleLErrorList = []
          try {
            if (data.error[1]) {
              this.busSettleLErrorList.concat(data.error[1])
            } if (data.error[2]) {
              this.busSettleLErrorList.concat(data.error[2])
              // this.busSettleLErrorList = [...data.error[2]]
            } if (data.error[3]) {
              this.busSettleLErrorList.concat(data.error[3])
              // this.busSettleLErrorList = [...data.error[3]]
            } if (data.error[4]) {
              this.busSettleLErrorList.concat(data.error[4])
              // this.busSettleLErrorList = [...data.error[4]]
            }
          } catch (err) {
            console.log(err)
          }
          this.url = data.url
          this.$forceUpdate()
        })
      } else {
        this.$message({ message: '未修改数据，无需测试', type: 'warning' })
      }
    },
    // 新增修改数据
    insertHistoryData () {
      if (this.changeMessage.length > 0) {
        let params = { id: '', k00: '', exchange: [] }
        params.id = this.$route.query.id
        params.k00 = this.$route.query.k00
        params.exchange = this.changeMessage
        insertHistory(params).then(res => {
          if (res.code == 200) {
            // this.$message({message: '修改数据成功', type: 'success'})
          }
        })
      }
    },
    // 提交，修改数据
    changeData () {
      this.xyDiseaseList = []
      this.zyDiseaseList = []
      this.busMedicalCostList2 = []
      this.queryChangData()
      if (this.changeMessage.length > 0) {
        this.$confirm('确认提交吗？', '提示', {
          type: 'warning'
        }).then(() => {
          this.loading = true
          this.loadingText = '正在同步数据、请稍等'
          this.insertHistoryData()
          setTimeout(() => {
            let params = { id: '', k00: '', exchange: [] }
            params.id = this.$route.query.id
            params.k00 = this.$route.query.k00
            params.exchange = this.changeMessage
            if (this.obtainData.xyDiseaseList[0].c06c1) {
              this.changeMessage.push(
                { zd: 'C03C', old: '-', after: this.obtainData.xyDiseaseList[0].c06c1 },
                { zd: 'C04N', old: '-', after: this.obtainData.xyDiseaseList[0].c07n1 },
                { zd: 'C05C', old: '-', after: this.obtainData.xyDiseaseList[0].c08c1 }
              )
            }
            if (this.obtainData.zyDiseaseList) {
              this.changeMessage.push(
                { zd: 'C37C', old: '-', after: this.obtainData.zyDiseaseList[0].c06c2 },
                { zd: 'C38N', old: '-', after: this.obtainData.zyDiseaseList[0].c07n2 },
                { zd: 'C39C', old: '-', after: this.obtainData.zyDiseaseList[0].c08c2 }
              )
            }
            if (this.obtainData.busDiseaseDiagnosisTrimsList) {
              const busDiseaseDiagnosisTrimsList = { busDiseaseDiagnosisTrimsList: this.obtainData.busDiseaseDiagnosisTrimList }
              Object.assign(params, busDiseaseDiagnosisTrimsList)
            }
            if (this.obtainData.busOperateDiagnosisListBa) {
              const busOperateDiagnosisList = { busOperateDiagnosisList: this.obtainData.busOperateDiagnosisListBa }
              Object.assign(params, busOperateDiagnosisList)
            }
            updateSettleList(params).then(res => {
              if (res.code == 200) {
                this.selectPreResult()
                this.newSettleListId = res.data.id
                this.changeMessage = []
                this.showChangeMessage(this.changeMessage)
              }
            })
          }, 500)
          this.timer = setInterval(this.scheduledTask, 1000)
        })
      } else {
        this.$message({ message: '未修改数据，无需提交', type: 'warning' })
      }
    },
    obtainExchangeData (data) {
      if (data) {
        this.obtainData = data
      }
    },
    handleClose (done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {})
    },
    closeDialogHistory () {
      let params = { id: '', k00: '' }
      params.id = this.$route.query.id
      params.k00 = this.$route.query.k00
      updateSettleListHisState(params).then(res => {})
    },
    // 还原历史数据
    restoreHistory (data, modi_time) {
      let params = { id: '', k00: '', exchange: [] }
      params.id = this.$route.query.id
      params.k00 = this.$route.query.k00
      params.modi_time = modi_time
      params.exchange = data
      let param = new URLSearchParams()
      param.append('id', this.$route.query.id)
      param.append('k00', this.$route.query.k00)
      restoreHistoryBusSettle(params).then(res => {
        if (res.code == 200) {
          this.getData(param)
          this.$message({ message: '数据还原成功', type: 'success' })
        }
      })
    },
    // 点击查看
    clickLookOver () {
      this.$confirm('确认修改吗？', '提示', {
        type: 'warning'
      }).then(() => {
        let params = { id: '', k00: '' }
        params.id = this.$route.query.id
        params.k00 = this.$route.query.k00
        updateSettleListLookOver(params).then(res => {
          if (res.code == 200) {
            this.switchValue = !this.switchValue
            this.$message({ message: '完成', type: 'success' })
          }
        })
      })
    }
  },
  directives: {
    drag (el) {
      let oDiv = el // 当前元素
      // let self = this // 上下文
      // 禁止选择网页上的文字
      document.onselectstart = function () {
        return false
      }
      oDiv.onmousedown = function (e) {
        // 鼠标按下，计算当前元素距离可视区的距离
        let disX = e.clientX - oDiv.offsetLeft
        let disY = e.clientY - oDiv.offsetTop
        document.onmousemove = function (e) {
          // 通过事件委托，计算移动的距离
          let l = e.clientX - disX
          let t = e.clientY - disY
          // 移动当前元素
          oDiv.style.left = l + 'px'
          oDiv.style.top = t + 'px'
        }
        document.onmouseup = function (e) {
          document.onmousemove = null
          document.onmouseup = null
        }
        // return false不加的话可能导致黏连，就是拖到一个地方时div粘在鼠标上不下来，相当于onmouseup失效
        return false
      }
    }
  }
}
</script>
<style scoped>

.count-panel>.count-item{
  height: 20px;
  width: 1px;
  flex: 1 1 1px;
  border-radius: 4px;
  border: solid 1px #0ba2b3;
  margin: 5px;
  background-size: 100% auto;
}
.count-item .title{
  width: 100%;
  text-align: left;
  font-family: MicrosoftYaHei;
  font-size: 12px;
}
.count-item .value,.count-item .value *{
  width: 100%;
  text-align: center;
  font-family: "290-CAI978";
  font-size: 16px;
  font-style: normal;
  color: #0ba2b3;
}
.count-item .value,.count-item .drgValue{
  width:100%;
  margin-top:8px;
  text-align: center;
  font-size: 12px;
  font-style: normal;
  color: #0ba2b3;
}
.count-item .value .others{
  font-family: MicrosoftYaHei;
  font-size: 12px;
  font-style:normal;
  margin: 0px 3px;
}

/deep/ .el-header{
  padding:0 5px;
}

/deep/ .el-collapse-item__header{
  height:40px;
  line-height:40px;
  font-size:15px;
  color:#FFFFFF;
  font-weight:600;
  background-color: #409EFF;
  padding-left: 15px;
  border-radius: 5px;
}
/deep/ .el-collapse-item__content{
  padding-bottom:0px;
}

/*编码资源消耗悬浮框*/
.diagnose-float-area {
  position:relative;
  top: 70px;
  width: 800px;
  /*max-height: 600px;*/
  border:1.5px solid #988e19;
  position: fixed;
  right: 50px;
  cursor: pointer;
  z-index: 1000;
  border-radius: 5px;
}

.diagnose-float-area-fixed{
  width: 95%;
  height: 41px;
  /*background: red;*/
  position: absolute;
  top: 0px;
  z-index: 99;
  opacity: 0;
  cursor: default;
}
/deep/ .el-card__body{
  padding:0px;
}
.currentGroup-title{
  font-weight:600;
}
.currentGroup-item{
  font-size: 12px;
  padding:1px 26px;
}
.currentGroup-costdays{
  font-size: 12px;
  padding:1px 26px;
}
.suggestGroup-title{
  font-weight:600;
}
.suggestGroup-item{
  font-size: 12px;
  padding:1px 26px;
  overflow: hidden;
  white-space:nowrap;
  text-overflow: ellipsis;
}
.suggestGroup-costdays{
  font-size: 12px;
  padding:1px 26px;
}
.diagnoseResourceRank{
  max-height: 170px;
  overflow-y:auto;
}
.diagnoseResourceRank-title{
  font-weight:600;
}
.diagnoseResourceRank-firstItem{
  font-size: 12px;
  color:#1e6abc ;
  font-weight:600;
  padding:1px 26px;
}
.diagnoseResourceRank-otherItem{
  font-size: 12px;
  padding:1px 26px;
}

.operateResourceRank{
  max-height: 170px;
  overflow-y:auto;
}
.operateResourceRank-title{
  font-weight:600;
}
.operateResourceRank-firstItem{
  font-size: 12px;
  color:#1e6abc ;
  font-weight:600;
  padding:1px 26px;
}
.operateResourceRank-otherItem{
  font-size: 12px;
  padding:1px 26px;
}
iframe{
  border: 0;
  background-color: transparent;
}
/deep/ .el-tabs__header {
  margin: 0 15px 15px 15px;
  width: 80%;
}
/deep/ .el-drawer__header{
 font-size: 22px!important;
}

/deep/ .el-card__header{
  background: #fdf6ec;
  color: white;
}

el-scrollbar__thumb {
  display: none;
}
.el-scrollbar__wrap {
  overflow-x: hidden;
  overflow-y: auto;
}

</style>
