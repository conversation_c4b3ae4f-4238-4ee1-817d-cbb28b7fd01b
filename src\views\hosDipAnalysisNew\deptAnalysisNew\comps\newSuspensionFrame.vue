<template>
  <div style="display: inline;" v-if="close">
    <div class="diagnose-float-area" style="border: 1px #EBEEF5 solid;border-radius: 10px" v-drag draggable="false" v-show="(zbData.length > 0 && zbData[0].show) || (ycData.length && ycData[0].show) && minimize ">
      <div class="mvDiv"  v-show="minimize">
        <div style="width: 87%;font-size: 17px;color: #409EFF;padding-left: 10px;cursor:default">悬浮框</div>
<!--        <i @click="showMini" v-if="!minimize" class="el-icon-circle-plus-outline" style="font-size: 25px; color: #409EFF"></i>-->
        <i @click="showMini"  v-if="minimize" class="el-icon-minus" style="font-size: 25px; color: #409EFF;"></i>
        <i @click="closeDiv"  v-if="minimize" class="el-icon-close" style="font-size: 25px; color: #409EFF;padding-left: 2%"></i>
      </div>
      <el-collapse v-model="activeNames" v-show="minimize">
        <el-collapse-item  name="1" title="指标" v-if="zbData.length>0">
          <el-descriptions :column="3"  border size="mini" >
<!--          <el-descriptions :column="3"  border size="mini" :label-style="LS"  :content-style="CS">-->
            <el-descriptions-item v-for="(item,index) in zbData"  :key="index" :label="item.label">{{item.value}}</el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
        <el-collapse-item  name="2" v-if="ycData.length>0" title="预测" >
          <el-descriptions :column="3"  border size="mini">
            <el-descriptions-item v-for="(item,index) in ycData"  :key="index" :label="item.label">{{item.value}}</el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="diagnose-float-area">
      <div style="display: flex" >
        <div style="width: 95%;cursor:default"></div>
        <i @click="showMini"  v-if="!minimize" class="el-icon-copy-document" style="font-size: 25px; color: #409EFF"></i>
<!--        <i v-if="minimize" class="el-icon-remove-outline" style="font-size: 25px; color: #409EFF"></i>-->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name:'newSuspensionFrame',
  props: {
    // 数据
    zbData: {
      type: Array,
      default: () => []
    },
    ycData: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    activeNames: ['1'],
    minimize: true,
    close: true,
    LS: {
      'word-break': 'keep-all'
    },
    CS: {
      'max-width': '50px',
      'word-break': 'break-all'
    }
  }),
  mounted () {

  },
  methods: {
    showMini () {
      this.minimize = !this.minimize
    },
    closeDiv () {
      this.close = false
    }
  },
  directives: {
    drag (el) {
      let oDiv = el // 当前元素
      // let self = this // 上下文
      // 禁止选择网页上的文字
      document.onselectstart = function () {
        return false
      }
      oDiv.onmousedown = function (e) {
        // 鼠标按下，计算当前元素距离可视区的距离
        let disX = e.clientX - oDiv.offsetLeft
        let disY = e.clientY - oDiv.offsetTop
        document.onmousemove = function (e) {
          // 通过事件委托，计算移动的距离
          let l = e.clientX - disX
          let t = e.clientY - disY
          // 移动当前元素
          oDiv.style.left = l + 'px'
          oDiv.style.top = t + 'px'
        }
        document.onmouseup = function (e) {
          document.onmousemove = null
          document.onmouseup = null
        }
        // return false不加的话可能导致黏连，就是拖到一个地方时div粘在鼠标上不下来，相当于onmouseup失效
        return false
      }
    }
  },
  watch: {
    zbData: {
      immediate: true,
      deep: true,
      handler (options) {
        if (options) {
          this.$nextTick(() => {
            this.close = true
          })
        }
      }
    },
    ycData: {
      immediate: true,
      deep: true,
      handler (options) {
        if (options) {
          this.$nextTick(() => {
            this.close = true
          })
        }
      }
    }
  }
}
</script>

<style scoped>
.drg-float-area {
  position:relative;
  width: 280px;
  top: 115px;
  border:1.5px solid #988e19;
  border-radius: 3px;
  position: fixed;
  right: 50px;
  cursor: pointer;
  z-index: 1000;
}
/deep/ .el-collapse-item__header{
  height:40px;
  line-height:40px;
  font-size:15px;
  /*color:#409EFF;*/
  color:#FFFFFF;
  font-weight:600;
  padding-left: 10px;
  background-color: #409EFF;
  /*background-color: whitesmoke;*/
}
/deep/ .el-collapse-item__content{
  padding-bottom:0px;
}
/*悬浮框*/
.diagnose-float-area {
  position:relative;
  width: 500px;
  top: 70px;
  position: fixed;
  right: 10px;
  cursor: pointer;
  z-index: 1000;
}
.mvDiv{
  display: flex;
  background-color: whitesmoke;
  height: 40px;
  align-items: center;
  border: 1px #EBEEF5 solid;
  border-radius: 10px 10px 0px 0px;
  border-bottom:none;
}
/deep/ .el-descriptions-item__content {
  word-break: break-all
}
</style>
