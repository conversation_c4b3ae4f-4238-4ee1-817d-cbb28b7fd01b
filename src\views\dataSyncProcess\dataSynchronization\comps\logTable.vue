<template>
  <div class="app-container">
    <el-table :data="data"
              border
              height="100%"
              ref="tableRef"
              stripe>
      <el-table-column label="序号" type="index" align="center"  />
      <el-table-column label="同步数据" prop="syncData" align="left" :key="9" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="skip" @click="queryData(scope.row.syncData)">
            {{ scope.row.syncData }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="错误信息" prop="errMsg"  :key="2" show-overflow-tooltip/>
      <el-table-column label="同步人" prop="crter" :key="3"/>
      <el-table-column label="同步时间" prop="crteTime" align="left"  :key="7"/>
      <el-table-column label="同步结果" prop="syncRes" align="center"  :key="1">
        <template slot-scope="scope">
          <el-tag type="danger" v-if="scope.row.syncRes === '0'">失败</el-tag>
          <el-tag type="success" v-else>成功</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="同步记录详情"
      :visible.sync="showDetail"
      width="80%">
      <div style="height: 400px">
        <list-table :data="detailData" :show-operate="false"/>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showDetail = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import ListTable from './listTable'
import { QueryData } from '@/api/dataHandle/dataSynchronization'

export default {
  name: 'logTable',
  components: {
    ListTable
  },
  props: {
    data: {
      type: Array,
      required: true
    }
  },
  data: () => ({
    showDetail: false,
    detailData: []
  }),
  methods: {
    queryData (syncData) {
      if (syncData) {
        let arr = syncData.split(',')
        QueryData({ pageSize: arr.length, pageNum: 1, uniqueIdArr: arr }).then(res => {
          this.detailData = res.data.list
          this.showDetail = true
        })
      }
    }
  }
}
</script>
