<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             show-pagination
             :total-num="total"
             headerTitle="查询条件"
             contentTitle="德阳限定与排除"
             :label-width="labelWidth"
             @query="queryData">
      <!-- 查询 -->
      <template slot="extendFormItems">
        <el-form-item label="1.0版手术操作代码" v-if="type == 1" prop="oneOperationTechniqueCode">
          <el-input v-model="queryForm.oneOperationTechniqueCode" placeholder="请输入1.0版手术操作代码" clearable  @input="queryData"/>
        </el-form-item>
        <el-form-item label="2.0版手术操作代码" v-if="type == 1" prop="twoOperationTechniqueCode">
          <el-input v-model="queryForm.twoOperationTechniqueCode" placeholder="请输入2.0版手术操作代码"  clearable @input="queryData"/>
        </el-form-item>
        <el-form-item label="1.0版诊断代码" v-if="type == 2" prop="oneDiagnoseCode">
          <el-input v-model="queryForm.oneDiagnoseCode" placeholder="请输入1.0诊断代码" clearable  @input="queryData" />
        </el-form-item>
        <el-form-item label="2.0版诊断代码" v-if="type == 2" prop="twoDiagnoseCode">
          <el-input v-model="queryForm.twoDiagnoseCode" placeholder="请输入1.0诊断代码" clearable  @input="queryData" />
        </el-form-item>
        <el-form-item label="操作编码" v-if="type == 3" prop="oprnOprtCode">
          <el-input v-model="queryForm.oprnOprtCode" placeholder="请输入操作编码" clearable @input="queryData" />
        </el-form-item>
        <el-form-item label="亚目编码" v-if="type == 3" prop="mainDiagnoseSuborderCode">
          <el-input v-model="queryForm.mainDiagnoseSuborderCode" placeholder="请输入亚目编码" clearable @input="queryData" />
        </el-form-item>
        <el-form-item label="病种编码" v-if="type == 3" prop="limitCoreDiseaseCode">
          <el-input v-model="queryForm.limitCoreDiseaseCode" placeholder="请输入核心病种编码" clearable @input="queryData" />
        </el-form-item>
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <el-tabs v-model="tabActiveName" @tab-click="tabClick" class="som-table-height">
          <el-tab-pane label="不参与分组的操作汇总表" name="1" class="som-tab-pane">
            <dy-limit-and-execlude-table :table-loading="baseTableLoading"
                                           :table-data="baseTableData"
                                           :type="1"
                                           @queryData="queryData"/>
          </el-tab-pane>
          <el-tab-pane label="不参与分组的诊断汇总表" name="2" class="som-tab-pane">
            <dy-limit-and-execlude-table :table-loading="baseTableLoading"
                                           :table-data="baseTableData"
                                           :type="2"
                                           @queryData="queryData"/>
          </el-tab-pane>
          <el-tab-pane label="限定核心病种的操作汇总表" name="3" class="som-tab-pane">
            <dy-limit-and-execlude-table :table-loading="baseTableLoading"
                                           :table-data="baseTableData"
                                           :type="3"
                                           @queryData="queryData"/>
          </el-tab-pane>

        </el-tabs>

      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDyLimtAndExclude } from '../../../api/custom/dyLimitAndExclude'
import dyLimitAndExcludeTable from './comps/dyLimitAndExcludeTable'

export default {
  name: 'dyLimitAndExclude',
  components: {
    'dy-limit-and-execlude-table': dyLimitAndExcludeTable
  },
  data: () => ({
    queryForm: {
      type: 1,
      oneOperationTechniqueCode: '',
      twoOperationTechniqueCode: '',
      oneDiagnoseCode: '',
      twoDiagnoseCode: '',
      oprnOprtCode: '',
      mainDiagnoseSuborderCode: '',
      limitCoreDiseaseCode: ''
    },
    tabActiveName: '1',
    baseTableLoading: false,
    baseTableData: [],
    showOperation: true,
    showDiagnose: false,
    showCoreDisease: false,
    type: 1,
    total: 0,
    labelWidth: '130px'
  }),
  methods: {
    queryData () {
      this.baseTableLoading = true
      this.queryForm.type = this.type
      queryDyLimtAndExclude(this.queryForm).then(res => {
        this.baseTableData = res.data.list
        this.total = res.data.total
        this.baseTableLoading = false
      })
    },
    tabClick () {
      this.showOperation = false
      this.showDiagnose = false
      this.showCoreDisease = false
      if (this.tabActiveName == 1) {
        this.showOperation = true
        this.type = 1
        this.labelWidth = '130px'
      } else if (this.tabActiveName == 2) {
        this.showDiagnose = true
        this.type = 2
        this.labelWidth = '100px'
      } else if (this.tabActiveName == 3) {
        this.showCoreDisease = true
        this.type = 3
        this.labelWidth = '70px'
      }
      this.queryData()
    }
  }
}
</script>
<style scoped>
/deep/ .el-tabs__content{
  height: 93%;
}
</style>
