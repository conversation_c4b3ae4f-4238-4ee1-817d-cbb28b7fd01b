<template>
  <div class="app-container">
    <drg-container :headerPercent="10">
      <template slot="header">
      </template>
      <template slot="content">
        <el-upload
          style="text-align: center"
          drag
          ref="upload"
          :limit="1"
          action="customize"
          accept=".xlsx,.xls"
          :http-request="upload">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
        </el-upload>
      </template>
    </drg-container>
  </div>
</template>
<script>
import { uploadRecordData } from '@/api/upload/upload'
export default {
  name: 'medicalRecordUpload',
  data: () => ({

  }),
  methods: {
    upload (data) {
      let params = new FormData()
      params.append('file', data.file)
      uploadRecordData(params).then(res => {
        if (res.code == 200) {
          this.dialogVisible = false
          this.$refs.upload.clearFiles()
          this.$message.success('上传成功')
          this.queryData()
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
      })
    }
  }
}
</script>
