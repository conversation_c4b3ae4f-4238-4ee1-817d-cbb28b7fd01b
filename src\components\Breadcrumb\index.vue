<template>
  <el-breadcrumb class="app-breadcrumb" separator-class="el-icon-caret-right">
    <transition-group name="breadcrumb">
      <template v-for="(item,index)  in levelList">
        <el-breadcrumb-item :key="item.path" v-if="item.meta.profttl">
          <span v-if="item.redirect==='noredirect'||index==levelList.length-1"
                class="no-redirect">{{ item.meta.profttl }}</span>
          <router-link v-else :to="item.redirect||item.path" class="no-redirect">{{ item.meta.profttl }}</router-link>
        </el-breadcrumb-item>
      </template>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
export default {
  created () {
    this.getBreadcrumb()
  },
  data () {
    return {
      levelList: null
    }
  },
  watch: {
    $route () {
      this.getBreadcrumb()
    }
  },
  methods: {
    getBreadcrumb () {
      let matched = this.$route.matched.filter(item => item.name)
      const first = matched[0]
      if (first && first.name !== 'home') {
        matched = [{ path: '/home', meta: { profttl: '工作台' } }].concat(matched)
      }
      this.levelList = matched
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 13px;
  line-height: 40px;
  margin-left: 10px;

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>
