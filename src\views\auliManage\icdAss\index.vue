<template>
  <div class="app-container">

    <drg-container :headerPercent="14.5" :isButtonEnd="true">
      <template slot="header">
        <drg-title-line title="查询条件" />
        <el-form :model="listQuery" size="mini" label-width="80px">
          <el-row type="flex" :gutter="10" justify="space-between">
            <el-col>
              <el-form-item label="出院时间">
                <el-date-picker
                  v-model="listQuery.cysj"
                  type="daterange"
                  size="mini"
                  unlink-panels
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  @change="dateChangeCysj"
                  :picker-options="pickerOptions">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="入院时间">
                <el-date-picker
                  v-model="listQuery.rysj"
                  type="daterange"
                  size="mini"
                  unlink-panels
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  @change="dateChangeRysj"
                  :picker-options="pickerOptions">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="病案号">
                <el-input v-model="listQuery.a48" placeholder="请输入病案号"></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="患者姓名">
                <el-input  v-model="listQuery.a11" placeholder="请输入患者姓名"></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="出院科室">
                <select-tree v-model="listQuery.b16c" :options="depts"  placeholder="请选择出院科室"/>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="som-align-center">
              <el-button
                @click="handleSearchList()"
                type="primary"
                size="mini">
                查询结果
              </el-button>
            <el-popconfirm
              confirm-button-text='确定'
              cancel-button-text='导出全部'
              icon="el-icon-info"
              icon-color="red"
              title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
              <el-button slot="reference" type="success">导出Excel</el-button>
            </el-popconfirm>
              <el-button
                @click="handleResetSearch()"
                size="mini">
                重置
              </el-button>
          </div>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="医生诊断辅助" />
        <div class="table-container" style="height: 90%;width: 100%">
          <el-table ref="settleListTable"
                    :header-cell-style="{'text-align':'center'}"
                    id="slTable"
                    size="mini"
                    height="100%"
                    width="100%"
                    stripe
                    :data="list"
                    v-loading="listLoading"
                    border>
            <el-table-column label="序号" type="index" width="50" align="right">
            </el-table-column>
            <el-table-column label="查看详情" align="center" width="75">
              <template slot-scope="scope">
                <el-button type="info" size="mini" icon="el-icon-search" style="height:24px;width: 24px;"  @click="handleShowMedicalDetail(scope.$index, scope.row)" circle>
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="病案号" prop="a48" align="center" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.a48 | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="姓名" pro="a11" align="center" width="60" >
              <template slot-scope="scope">{{scope.row.a11 | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="疾病主诊段" prop="c04n" align="left" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.c04n | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="手术主诊段" prop="c15x01n" align="left" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.c15x01n | formatIsEmpty}}</template>
            </el-table-column>
            <!--<el-table-column label="性别"  align="center" width="60" prop="a12c"-->
            <!--:filters="[{ text: '男', value: '1' }, { text: '女', value: '2' }]"-->
            <!--:filter-method="filterSex">-->
            <!--<template slot-scope="scope">{{scope.row.a12c | formatA12c}}</template>-->
            <!--</el-table-column>-->
            <!--<el-table-column prop="a14" label="年龄"  align="center" width="80" sortable>-->
            <!--<template slot-scope="scope">{{scope.row.a14 | formatIsEmpty}}</template>-->
            <!--</el-table-column>-->
            <el-table-column label="主要诊断资源消耗是否合理"  prop="mainDiagIsChoErr" align="center" :show-overflow-tooltip='true'>
              <template slot-scope="scope">
            <span v-if="scope.row.mainDiagnoseCodeError=='0'">
               <el-button size="mini" type="success" icon="el-icon-check"  circle></el-button>
            </span>
                <span v-else>
               <el-button size="mini" type="danger" icon="el-icon-close" circle></el-button>
            </span>
              </template>
            </el-table-column>
            <el-table-column label="主要操作资源消耗是否合理"  prop="mainOprnIsChoErr" align="center" :show-overflow-tooltip='true'>
              <template slot-scope="scope">
            <span v-if="scope.row.mainOperateCodeError=='0'">
               <el-button size="mini" type="success" icon="el-icon-check"  circle></el-button>
            </span>
                <span v-else>
               <el-button size="mini" type="danger" icon="el-icon-close" circle></el-button>
            </span>
              </template>
            </el-table-column>
            <el-table-column label="当前DIP入组" prop="dipCodeAndName" align="left" width="120" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.dipCodeAndName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="建议DIP入组" prop="newDipCodeAndName" align="left"  width="120" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.newDipCodeAndName | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="当前DRG入组" prop="drgsCodeAndName" align="left" width="120" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.drgsCodeAndName | formatDrg}}</template>
            </el-table-column>
            <el-table-column label="建议DRG入组" prop="newDrgCodeAndName" align="left"  width="120" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.newDrgCodeAndName | formatDrg}}</template>
            </el-table-column>
            <el-table-column label="入院科室" prop="b13n" align="center" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.b13n | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="出院科室" prop="b16n" align="center" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.b16n | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column prop="b12" label="入院时间"  align="center" width="100" sortable>
              <template slot-scope="scope">{{scope.row.b12 | formatTime }}</template>
            </el-table-column>
            <el-table-column prop="b15" label="出院时间"  align="center" width="100" sortable>
              <template slot-scope="scope">{{scope.row.b15 | formatTime }}</template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            layout="total, sizes,prev, pager, next,jumper"
            :page-size="listQuery.pageSize"
            :page-sizes="[200,1000,5000,10000]"
            :current-page.sync="listQuery.pageNum"
            :total="total">
          </el-pagination>
        </div>
      </template>
    </drg-container>
  </div>
</template>
<script>
import { querySelectTreeAndSelectList, queryDataIsuue } from '@/api/common/drgCommon'
import { fetchListForDoctor as queryPageData } from '@/api/medicalQuality/settleList'
import { formatDate } from '@/utils/date'
import { elExportExcel } from '@/utils/exportExcel'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  a48: null,
  a11: null,
  b16c: null,
  ry_start_date: null,
  ry_end_date: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date,
  tableHeight: 0
}
export default {
  name: 'icdAss',
  components: { },
  data () {
    return {
      listLoading: true,
      list: null,
      total: null,
      ry_start_date: null,
      ry_end_date: null,
      cy_start_date: null,
      cy_end_date: null,
      dictVoList: {},
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      depts: [],
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }

    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatDrg (value) {
      if (value) {
        switch (value) {
          case '0001': return '主要诊断为空'
          case '0004': return '总费用不得小于5元'
          case '0007': return '分组方案无此编码'
          case '0008': return '主要诊断编码不规范'
          default: return value
        }
      } else {
        return '-'
      }
    },
    formatTime (time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },
    formatA12c (value) {
      if (value == '1') {
        return '男'
      } else if (value == '2') {
        return '女'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.a48 = this.listQuery.a48
      this.submitListQuery.a11 = this.listQuery.a11
      this.submitListQuery.b16c = this.listQuery.b16c
      this.submitListQuery.ry_start_date = this.ry_start_date
      this.submitListQuery.ry_end_date = this.ry_end_date
      this.submitListQuery.cy_start_date = this.cy_start_date
      this.submitListQuery.cy_end_date = this.cy_end_date
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.settleListTable.$children, document.getElementById('slTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病案数据(医生诊断辅助）')
    },
    dateChangeRysj (val) {
      if (val) {
        this.ry_start_date = val[0]
        this.ry_end_date = val[1]
      } else {
        this.ry_start_date = null
        this.ry_end_date = null
      }
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.getList()
    },
    handleResetSearch () {
      this.getDataIsuue()
    },
    handleShowMedicalDetail (index, row) {
      this.$router.push({ path: '/setlListManage/setlListDetail', query: { id: row.id, activeMenu: '3' } })
    },
    filterSex (value, row) {
      return row.a12c === value
    },
    exportExcel () {
      let tableId = 'doctorSlTable'
      let fileName = '病案数据(医生诊断辅助）'
      elExportExcel(tableId, fileName)
    }
  }
}
</script>
<style scoped>
  /*时间样式设置*/
  /deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner{
    width: 200px;
  }
</style>
