<template>
  <div class="app-container" style="overflow-x: auto">
    <drg-echarts :options="options" @chartClick="chartClick" ref="chart"/>
  </div>
</template>
<script>
export default {
  name: 'pathwayChart',
  props: {
    data: Array,
    // 类型，1：一级路径 2：二级路径
    chartType: {
      type: Number,
      default: 1
    }
  },
  data: () => ({
    options: {},
    tempParams: {}
  }),
  computed: {
    isType1 () {
      return this.chartType === 1
    },
    isType2 () {
      return this.chartType === 2
    }
  },
  methods: {
    initChart () {
      this.handlerData(this.data)
      this.options = {
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove'
        },
        series: [
          {
            type: 'tree',
            data: this.data,
            top: '1%',
            left: this.isType1 ? '10%' : '35%',
            bottom: '1%',
            right: '20%',
            symbolSize: 7,
            label: {
              position: 'left',
              verticalAlign: 'middle',
              align: 'right',
              fontSize: 12,
              formatter: params => {
                if (params.name.length > 20) {
                  return params.name.substring(0, 20) + '...'
                }
                return params.name
              }
            },
            leaves: {
              label: {
                position: 'right',
                verticalAlign: 'middle',
                align: 'left'
              }
            },
            emphasis: {
              focus: 'descendant'
            },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750
          }
        ]
      }
    },
    handlerData (data) {
      if (data) {
        data.forEach(item => {
          if (Object.keys(this.tempParams).length > 0) {
            if (this.tempParams.path.includes(item.path)) {
              item.collapsed = false
            } else {
              item.collapsed = true
            }
          } else {
            item.collapsed = false
          }
          if (item.children && item.children.length > 0) {
            this.handlerData(item.children)
          }
        })
        this.$nextTick(() => {
          this.tempParams = {}
        })
      }
    },
    chartClick (params) {
      if (this.isType1) {
        // 点击组后显示多个组信息
        if (params.data.canLaunchedNode) {
          this.tempParams = { path: params.data.path.substring(0, params.data.path.lastIndexOf('/')), cnt: 20 }
          this.$emit('chartClick', this.tempParams)
        }

        // 二级路径
        if (!params.data.canLaunchedNode && !params.data.children) {
          let p = {}
          if (this.$somms.isDIP()) {
            let { dipCodg, dipName, asstListAgeGrp, asstListTmorSevDeg, asstListDiseSevDeg } = params.data.data[0]
            p = { dipCodg, dipName, asstListAgeGrp, asstListTmorSevDeg, asstListDiseSevDeg }
          }
          this.$emit('showLeve2Path', p)
        }
      }

      if (this.isType2) {
        if (!params.data.children) {
          this.$emit('chartClick', { name: params.name })
        }
      }
    },
    reInit () {
      this.$refs.chart.initChart()
    }
  },
  watch: {
    data: {
      immediate: true,
      handler: function () {
        this.initChart()
      }
    }
  }
}
</script>
