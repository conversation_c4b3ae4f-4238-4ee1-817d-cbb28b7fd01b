import request from '@/utils/request'
export function fetchList (params) {
  return request({
    url: '/dataHandleLog/logList',
    method: 'post',
    params: params
  })
}

export function queryCurrentBusSettleList (logId) {
  return request({
    url: '/dataHandleLog/queryCurrentBusSettleList',
    method: 'post',
    params: logId
  })
}

export function restartProcessByLogId (logId) {
  return request({
    url: '/dataHandleLog/restartProcessByLogId',
    method: 'post',
    params: logId
  })
}

export function keepProcessByLogId (logId) {
  return request({
    url: '/dataHandleLog/keepProcessByLogId',
    method: 'post',
    params: logId
  })
}
