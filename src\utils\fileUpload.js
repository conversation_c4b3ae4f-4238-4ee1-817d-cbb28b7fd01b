import axios from 'axios'
import { Message, MessageBox } from 'element-ui'
import store from '../store'
import { getToken } from '@/utils/auth'

// 创建axios实例
const uploadService = axios.create({
  baseURL: process.env.BASE_API, // api的base_url
  method: 'post',
  headers: {
    'Content-Type': 'multipart/form-data;charset=UTF-8'
  }
})
// request拦截器
uploadService.interceptors.request.use(config => {
  if (store.getters.token) {
    config.headers['Authorization'] = getToken() // 让每个请求携带自定义token 请根据实际情况自行修
  }
  return config
}, error => {
  Promise.reject(error)
})
export default uploadService
