<template>
  <el-dialog
    title="修改清单"
    :visible.sync="modifyVisible"
    width="60%">
    <div class="sl-submit-item-wrapper" v-loading="loading" v-if="showModify">
      <div v-for="(item, index) in uploadModifyData" :key="index" class="sl-submit-item">
        <div class="sl-submit-item-title">
          {{ item.name }}
        </div>
        <div class="sl-submit-item-content">
          <div class="sl-submit-item-content-log" v-if="item.log && item.log.length > 0">
            <ul>
              <li v-for="(logItem, index) in item.log" :key="index">
                {{ index + 1 }}、{{ logItem }}
              </li>
            </ul>
          </div>
          <el-table border
                    :data="item.data"
                    :header-cell-style="{ textAlign: 'center' }"
                    :cell-style="{ textAlign: 'center' }"
                    height="100%"
                    style="width: 100%">
            <el-table-column label="修改字段" prop="name" />

            <!-- 修改前值 -->
            <el-table-column label="修改前" prop="hisVal">
              <template slot-scope="scope">
                {{ getValue(scope.row, 'hisVal') }}
              </template>
            </el-table-column>

            <!-- 修改后值 -->
            <el-table-column label="修改后" prop="value">
              <template slot-scope="scope">
                {{ getValue(scope.row, 'value') }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
        <el-button @click="modifyVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">提 交</el-button>
      </span>
  </el-dialog>
</template>
<script>
import { modifySettleListInfo } from '@/api/medicalQuality/settleListDetail'

export default {
  props: {
    // 改变的值
    uploadModifyData: [Array],
    visible: [Boolean],
    // 上传参数
    uploadParams: [Object]
  },
  data: () => ({
    modifyVisible: false,
    loading: false,
    showModify: true
  }),
  methods: {
    // 提交
    submit () {
      this.$confirm('是否确认提交(5秒后更新数据)？', '提交提示', {
        type: 'warning'
      }).then(() => {
        this.loading = true
        this.resetSno()
        modifySettleListInfo(this.uploadParams).then(res => {
          this.modifyVisible = false
          if (res.data) {
            this.loading = false
            this.modifyVisible = false
            if (res.data.k00) {
              this.$router.push({ path: '/setlListManage/setlListInfo2', query: { k00: res.data.k00, id: res.data.id } })
            }
            this.success()
          }
        }).catch(() => {
          this.loading = false
        })
      }).catch(() => {
        this.loading = false
        this.fail()
      })
    },
    // 重置sno
    resetSno () {
      Object.keys(this.uploadParams).map(key => {
        let data = this.uploadParams[key]
        if (!data.modify) {
          let seq = 0
          data.insertData.map(d => {
            d.seq = seq
            seq++
          })
        }
      })
    },
    // 获取值，如果是下拉选则转换
    getValue (item, key) {
      if (item.type === 'select') {
        return this.getDictLabel(item.dicType, item[key])
      }
      // 处理空值显示
      if (item[key] === null || item[key] === undefined || item[key] === '') {
        return '-'
      }
      return item[key]
    },
    getDictLabel (dicType, val) {
      let dict = this.$somms.getDictValue(dicType, 2)
      if (dict) {
        let dictObj = dict.find(item => item.value == val)
        if (dictObj) {
          return dictObj.label
        }
        return val
      }
      return val
    },
    success () {
      this.$message.success('修改成功')
      this.$emit('success')
      this.complete()
    },
    fail () {
      this.$emit('fail')
      this.complete()
    },
    complete () {
      this.$emit('complete')
      this.loading = false
    }
  },
  watch: {
    visible (val) {
      this.modifyVisible = val
    },
    modifyVisible (val) {
      this.$emit('change', val)
    }
  }
}
</script>
<style scoped lang="scss">
.sl-submit-item-wrapper{
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
}
.sl-submit-item{
  height: 50%;
  width: 50%;

  &-title{
    font-size: 20px;
    height: 15%;
    display: flex;
    align-items: center;
  }

  &-content{
    height: 85%;
    width: 100%;

    &-log{
      height: 30%;
      overflow-y: auto;
      width: 100%
    }
  }
}

ul,li{list-style:none;}

/deep/ .el-dialog {
  height: 80vh;
}
/deep/ .el-dialog__header{
  height: 6vh;
}
/deep/ .el-dialog__body{
  height: 66vh;
}
/deep/ .el-dialog__footer{
  height: 8vh;
}
</style>
