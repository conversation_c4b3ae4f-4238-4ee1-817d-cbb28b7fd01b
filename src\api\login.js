import request from '@/utils/request'

export function login (username, password, hospitalId, token = '') {
  return request({
    url: '/user/login',
    method: 'post',
    data: {
      username,
      password,
      hospitalId,
      token
    }
  })
}

export function getInfo () {
  return request({
    url: '/user/info',
    method: 'get'
  })
}

export function logout () {
  return request({
    url: '/user/logout',
    method: 'post'
  })
}

export function querySysOutOfDate () {
  return request({
    url: '/user/querySysOutOfDate',
    method: 'get'
  })
}

export function verifyToken (params) {
  return request({
    url: '/user/verifyToken',
    method: 'post',
    data: params
  })
}

export function modifySysUserInfo (params) {
  return request({
    url: '/user/modifySysUserInfo',
    method: 'post',
    data: params
  })
}

export function queryHospitalInfo (params) {
  return request({
    url: '/user/queryHospitalInfo',
    method: 'post',
    params: params
  })
}
