<template>
  <div class="app-container">
    <drg-form v-model="queryFrom"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-hos-dept
             headerTitle="查询条件"
             :container="true"
             @query="query(true)" @reset="refresh">

      <template slot="extendFormItems">
        <el-form-item label="盈亏比" class="som-button-margin-right">
          <el-button round
                     class="dcew-query-button"
                     :class="[yk_check.yz.checked ? 'decw-query-check-yz' : 'decw-query-uncheck-yz',
                                  yk_check.yz.mouseover ? 'decw-query-check-yz-mouseover': '']"
                     @mouseover.native="yk_check.yz.mouseover = true"
                     @mouseleave.native="yk_check.yz.mouseover = false"
                     @click="fnProfitAndLossQuery(1)">严重超支</el-button>
          <el-button round
                     class="dcew-query-button"
                     :class="[yk_check.cz.checked ? 'decw-query-check-cz' : 'decw-query-uncheck-cz',
                                  yk_check.cz.mouseover ? 'decw-query-check-cz-mouseover': '']"
                     @mouseover.native="yk_check.cz.mouseover = true"
                     @mouseleave.native="yk_check.cz.mouseover = false"
                     @click="fnProfitAndLossQuery(2)">超支</el-button>
          <el-button round
                     class="dcew-query-button"
                     :class="[yk_check.cp.checked ? 'decw-query-check-cp' : 'decw-query-uncheck-cp',
                                  yk_check.cp.mouseover ? 'decw-query-check-cp-mouseover': '']"
                     @mouseover.native="yk_check.cp.mouseover = true"
                     @mouseleave.native="yk_check.cp.mouseover = false"
                     @click="fnProfitAndLossQuery(3)">持平</el-button>
          <el-button round
                     class="dcew-query-button"
                     :class="[yk_check.jy.checked ? 'decw-query-check-jy' : 'decw-query-uncheck-jy',
                                  yk_check.jy.mouseover ? 'decw-query-check-jy-mouseover': '']"
                     @mouseover.native="yk_check.jy.mouseover = true"
                     @mouseleave.native="yk_check.jy.mouseover = false"
                     @click="fnProfitAndLossQuery(4)">结余</el-button>
        </el-form-item>
      </template>

      <template slot="contentTitle">
        <drg-title-line title="费用预警">
          <template slot="rightSide">
            <i class="som-icon-pie som-iconTool"
               title="饼图"
               v-if="!showPie"
               @click="changePieOrCard(1)"
               style="height: 1.2rem;width: 1.2rem"></i>
            <i class="som-icon-card som-iconTool"
               title="卡片"
               v-else
               @click="changePieOrCard(2)"
               style="height: 1.2rem;width: 1.2rem"></i>
          </template>
        </drg-title-line>
      </template>

      <template slot="containerContent">
        <drg-loading :loading="loading" style="position: absolute;top: 50%;left: 45%;"/>
        <el-empty v-show="empty" style="position: absolute;top: 25%;left: 45%" description="暂无数据"></el-empty>
        <div style="height: 100%;width: 100%;background-color: white;position: absolute" v-show="loading" />
        <div v-if="!showPie" style="height: 100%;width: 100%;overflow-y: auto">
          <el-card v-for="(item, index) in data" :key="index"
                   :body-style="{'padding': '0px!important'}"
                   shadow="hover"
                   class="dcew-content-box"
                   :class="['dcew-content-box-left', range(item)]">
            <div class="dcew-content-box-title">
              {{ item.deptName }}
            </div>
            <div class="dcew-content-box-item">
              <div class="dcew-content-box-item-title">
                盈亏比
              </div>
              <span class="dcew-content-box-item-val">
                {{ item.balanceRate }}%
              </span>
            </div>
            <div class="dcew-content-box-item">
              <div class="dcew-content-box-item-title">
                盈亏金额
              </div>
              <span class="dcew-content-box-item-val" v-html="formatCost(item.balanceCost,true)"/>
            </div>
            <div style="width: 100%;height: 40%;">
              <div class="dcew-content-box-item-end">
                <div class="dcew-content-box-item-end-title">
                  入组病案数
                </div>
                <div v-if="Number(item.allCount)>0" class="dcew-content-box-item-val dcew-content-box-item-end-val" @click="queryAllCount(item)">
                  {{ item.allCount }}
                </div>
                <div v-if="Number(item.allCount)==0">
                  {{item.allCount}}
                </div>
              </div>
              <div class="dcew-content-box-item-end">
                <div class="dcew-content-box-item-end-title">
                  病组
                </div>
                <div v-if="Number(item.groupCount)>0" class="dcew-content-box-item-val dcew-content-box-item-end-val"  @click="queryGroupCount(item)">
                  {{ item.groupCount }}
                </div>
                <div v-if="Number(item.groupCount)==0" >
                  {{item.groupCount}}
                </div>
              </div>
              <div class="dcew-content-box-item-end">
                <div class="dcew-content-box-item-end-title">
                  超高病案数
                </div>
                <div v-if="Number(item.upNum)>0" class="dcew-content-box-item-val dcew-content-box-item-end-val" @click="queryUpCount(item)">
                  {{ item.upNum }}
                </div>
                <div v-if="Number(item.upNum)==0" class="dcew-content-box-item-val dcew-content-box-item-end-val">
                  {{item.upNum}}
                </div>
              </div>
              <div class="dcew-content-box-item-end">
                <div class="dcew-content-box-item-end-title">
                  超低病案数
                </div>
                <div v-if="Number(item.lowNum)>0" class="dcew-content-box-item-val dcew-content-box-item-end-val" @click="queryLowCount(item)">
                  {{ item.lowNum }}
                </div>
                <div v-if="Number(item.lowNum)==0" class="dcew-content-box-item-val dcew-content-box-item-end-val">
                  {{item.lowNum}}
                </div>
              </div>
            </div>
          </el-card>
        </div>
        <div id="pie" v-else style="height: 96%;width: 100%">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="12" style="height: 100%">
              <drg-echarts :options="pieOptions1" ref="pieChart1"/>
            </el-col>
            <el-col :span="12" style="height: 100%">
              <drg-echarts :options="pieOptions2" ref="pieChart2"/>
            </el-col>
          </el-row>
        </div>
      </template>
    </drg-form>
  </div>
</template>

<script>
import { querySelectTreeAndSelectList, queryDataIsuue } from '@/api/common/drgCommon'
import { getList } from '@/api/dipBusiness/dipDeptCostEarlyWarning'
import { formatCost } from '@/utils/common'
import moment from 'moment'

export default {
  name: 'deptFeeWarn',
  components: { },
  inject: ['reload'],
  data () {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick (picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick (picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      rules: {
        // time_range: [
        //   { type: 'date', required: true, message: '请选择时间范围', trigger: 'change' }
        // ]
      },
      depts: [],
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      queryFrom: {
        updt_date: [], // 日期
        deptCode: '', // 部门
        rate: 0, // 盈亏比
        ym: '',
        cysj: '',
        cy_start_date: this.cy_start_date,
        cy_end_date: this.cy_end_date,
        feeStas: '0'
      },
      data: [],
      yk_check: {
        yz: {
          index: 1,
          checked: false,
          mouseover: false
        },
        cz: {
          index: 2,
          checked: false,
          mouseover: false
        },
        cp: {
          index: 3,
          checked: false,
          mouseover: false
        },
        jy: {
          index: 4,
          checked: false,
          mouseover: false
        }
      },
      empty: false,
      loading: false,
      showPie: false,
      pieOptions1: {},
      pieOptions2: {}
    }
  },
  created () {
    this.queryFrom.feeStas = String(this.$store.getters.feeStas)
    // 获取数据查询时间
    // this.getDataIsuue();
    this.getIssue()
  },
  methods: {
    formatCost,
    getIssue () {
      queryDataIsuue().then(res => {
        this.queryFrom.cy_start_date = res.data.cy_start_date
        this.queryFrom.cy_end_date = res.data.cy_end_date
        this.queryFrom.cysj = [this.queryFrom.cy_start_date, this.queryFrom.cy_end_date]
        this.queryFrom.ym = res.data.cy_start_date
        // 查询数据
        this.init()
      })
    },
    dateChangeCysj (val) {
      if (val) {
        this.queryFrom.cy_start_date = val[0]
        this.queryFrom.cy_end_date = val[1]
      } else {
        this.queryFrom.cy_start_date = null
        this.queryFrom.cy_end_date = null
      }
      this.query()
    },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'ERROR_TYPE')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
      })
    },
    getYearMonthStartTimeByData () {
      let now = this.queryFrom.ym
      let nowMonth = now.substr(5, 2)
      let nowYear = now.substr(0, 4)
      return new Date(nowYear, nowMonth - 1, 1)
    },
    getYearMonthEndTimeByData () {
      let now = this.queryFrom.ym
      let nowMonth = now.substr(5, 2)
      let nowYear = now.substr(0, 4)
      return new Date(nowYear, nowMonth, 0)
    },
    queryAllCount (item) {
      this.goto('/oprelDecimmak/pattExtrAnalysis', { queryType: 'groupNum',
        priOutHosDeptCode: item.deptCode,
        deptCode: item.deptCode,
        priOutHosDeptName: item.deptName,
        // ym: this.queryFrom.ym.toString().substring(1,7),
        begnDate: this.queryFrom.begnDate,
        expiDate: this.queryFrom.expiDate,
        dateRange: [this.queryFrom.begnDate, this.queryFrom.expiDate],
        inStartTime: this.queryFrom.inStartTime,
        inEndTime: this.queryFrom.inEndTime,
        inHosFlag: this.queryFrom.inHosFlag,
        group: '1',
        type: '1',
        isGroup: true,
        seStartTime: this.queryFrom.seStartTime,
        seEndTime: this.queryFrom.seEndTime
      })
      // this.$router.push({
      // path: '/oprelDecimmak/pattExtrAnalysis', query: {
      //   path: '/oprelDecimmak/pattExtrAnalysis', query: {
      //     queryType: 'groupNum',
      //     priOutHosDeptCode: item.deptCode,
      //     deptCode: item.deptCode,
      //     priOutHosDeptName: item.deptName,
      //     ym: this.queryFrom.ym,
      //     begnDate: this.queryFrom.begnDate,
      //     expiDate: this.queryFrom.expiDate,
      //     dateRange: [this.queryFrom.begnDate,this.queryFrom.expiDate],
      //     group: '1',
      //     type: '1',
      //   }
      // });
    },
    queryGroupCount (item) {
      this.$router.push({
        path: '/oprelDecimmak/diseExtrAnalysis',
        query: {
          queryType: 'groupNum',
          priOutHosDeptCode: item.deptCode,
          deptCode: item.deptCode,
          priOutHosDeptName: item.deptName,
          ym: this.queryFrom.ym,
          begnDate: this.queryFrom.begnDate,
          expiDate: this.queryFrom.expiDate,
          dateRange: [this.queryFrom.begnDate, this.queryFrom.expiDate],
          inStartTime: this.queryFrom.inStartTime,
          inEndTime: this.queryFrom.inEndTime,
          inHosFlag: this.queryFrom.inHosFlag,
          group: '1',
          type: '1',
          seStartTime: this.queryFrom.seStartTime,
          seEndTime: this.queryFrom.seEndTime
        }
      })
    },
    refresh () {
      this.reload()
    },
    query (isQuery = false) {
      let params = this.queryFrom
      params.cy_start_date = this.queryFrom.begnDate
      params.cy_end_date = this.queryFrom.expiDate
      params.seStartTime = this.queryFrom.seStartTime
      params.seEndTime = this.queryFrom.seEndTime
      if (isQuery) {
        params.rate = 0
      }
      if (params.updt_date) {
        params.begnDate = params.cy_start_date
        params.expiDate = params.cy_end_date
      }
      params.dataAuth = true
      this.loading = true
      getList(params).then((res) => {
        if (res.code == 200) {
          if (res.data) {
            if (res.data.length == 0 && this.showPie == false) {
              this.empty = true
            } else {
              this.empty = false
            }
            this.data = res.data
          }
          this.loading = false
          this.createPie1()
          this.createPie2()
        }
      }).catch(reason => {
        this.loading = false
      })
    },
    // 盈亏比查询
    fnProfitAndLossQuery (data) {
      if (data) {
        let flag = true
        Object.keys(this.yk_check).forEach(key => {
          if (this.yk_check[key].index == data) {
            if (this.yk_check[key].checked) {
              this.yk_check[key].checked = false
              this.queryFrom.rate = 0
              flag = false
            } else {
              this.yk_check[key].checked = true
            }
          } else {
            this.yk_check[key].checked = false
          }
        })
        if (flag) {
          this.queryFrom.rate = data
        }
        this.query()
      }
    },
    init () {
      this.query()
    },
    range (item) {
      if (item && item.balanceRate) {
        let ykb = parseFloat(item.balanceRate)
        if (ykb <= -10) {
          return 'seriousness'
        }
        if (ykb > -10 && ykb <= -1) {
          return 'warning'
        }
        if (ykb > -1 && ykb <= 1) {
          return 'steady'
        }
        if (ykb > 1) {
          return 'surplus'
        }
      }
      // -0.0 情况
      return 'steady'
    },
    fnClearTree () {
      this.queryFrom.rate = 0
      this.queryFrom.deptCode = ''
      // this.query()
    },
    fnChangeDept () {
      this.query()
    },
    queryMedicalTotalNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          begnDate: this.queryFrom.begnDate,
          expiDate: this.queryFrom.expiDate,
          dateRange: [this.queryFrom.begnDate, this.queryFrom.expiDate],
          inStartTime: this.queryFrom.inStartTime,
          inEndTime: this.queryFrom.inEndTime,
          inHosFlag: this.queryFrom.inHosFlag,
          type: '2',
          seStartTime: this.queryFrom.seStartTime,
          seEndTime: this.queryFrom.seEndTime
        }
      })
    },
    queryDrgsNum (row) {
      this.$router.push({
        path: '/hosDipAnalysis/groupAnalysis',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          begnDate: this.queryFrom.begnDate,
          expiDate: this.queryFrom.expiDate,
          dateRange: [this.queryFrom.begnDate, this.queryFrom.expiDate],
          inStartTime: this.queryFrom.inStartTime,
          inEndTime: this.queryFrom.inEndTime,
          inHosFlag: this.queryFrom.inHosFlag,
          type: '2',
          seStartTime: this.queryFrom.seStartTime,
          seEndTime: this.queryFrom.seEndTime
        }
      })
    },
    queryUpCount (row) {
      this.goto('/oprelDecimmak/pattExtrAnalysis', {
        ym: this.queryFrom.ym,
        deptCode: row.deptCode,
        begnDate: this.queryFrom.begnDate,
        expiDate: this.queryFrom.expiDate,
        dateRange: [this.queryFrom.begnDate, this.queryFrom.expiDate],
        inStartTime: this.queryFrom.inStartTime,
        inEndTime: this.queryFrom.inEndTime,
        inHosFlag: this.queryFrom.inHosFlag,
        costSection: 1,
        group: 1,
        seStartTime: this.queryFrom.seStartTime,
        seEndTime: this.queryFrom.seEndTime
      })
    },
    queryLowCount (row) {
      this.goto('/oprelDecimmak/pattExtrAnalysis', {
        deptCode: row.deptCode,
        begnDate: this.queryFrom.begnDate,
        expiDate: this.queryFrom.expiDate,
        dateRange: [this.queryFrom.begnDate, this.queryFrom.expiDate],
        inStartTime: this.queryFrom.inStartTime,
        inEndTime: this.queryFrom.inEndTime,
        inHosFlag: this.queryFrom.inHosFlag,
        costSection: 2,
        group: 1,
        seStartTime: this.queryFrom.seStartTime,
        seEndTime: this.queryFrom.seEndTime
      })
    },
    changePieOrCard (index) {
      if (index == 1) {
        this.showPie = true
        this.empty = false
        this.query()
      } else {
        this.showPie = false
        this.query()
      }
    },
    createPie1 () {
      let _this = this
      let deptData = []
      let deptName = []
      let total = 0
      if (_this.data.length > 0) {
        for (let i = 0; i < _this.data.length; i++) {
          if (_this.data[i].balanceCost < 0) {
            total = total + _this.data[i].balanceCost
            deptName.push(_this.data[i].deptName ? _this.data[i].deptName : '')
          }
        }
        for (let i = 0; i < _this.data.length; i++) {
          if (_this.data[i].balanceCost < 0) {
            deptData.push({ value: _this.data[i].balanceCost < 0 ? _this.data[i].balanceCost : '', name: _this.data[i].balanceCost < 0 ? (_this.data[i].deptName ? _this.data[i].deptName : '') : '', rate: ((_this.data[i].balanceCost / total) * 100).toFixed(2) })
          }
        }
      } else {
        deptData.push({ value: 0 })
        deptName.push('')
      }
      this.pieOptions1 = {
        color: this.$somms.generateColor(),
        title: {
          text: '亏损',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return params.name + '</br>' +
              '金额:' + params.data.value + '</br>' +
              '占比:' + params.data.rate + '%'
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          right: 10,
          top: 20,
          bottom: 20,
          data: deptName
        },
        series: [
          {
            type: 'pie',
            height: '90%',
            width: '90%',
            label: {
              show: true,
              formatter: '{b}'
            },
            data: deptData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      if (this.showPie) {
        this.$nextTick(() => {
          this.$refs.pieChart1.initChart()
        })
      }
    },
    createPie2 () {
      let _this = this
      let deptData = []
      let deptName = []
      let total = 0
      if (_this.data.length > 0) {
        for (let i = 0; i < _this.data.length; i++) {
          if (_this.data[i].balanceCost > 0) {
            total = total + _this.data[i].balanceCost
            deptName.push(_this.data[i].deptName ? _this.data[i].deptName : '')
          }
        }
        for (let i = 0; i < _this.data.length; i++) {
          if (_this.data[i].balanceCost > 0) {
            deptData.push({ value: _this.data[i].balanceCost > 0 ? _this.data[i].balanceCost : '', name: _this.data[i].balanceCost > 0 ? (_this.data[i].deptName ? _this.data[i].deptName : '') : '', rate: ((_this.data[i].balanceCost / total) * 100).toFixed(2) })
          }
        }
      } else {
        deptData.push({ value: 0 })
        deptName.push('')
      }
      this.pieOptions2 = {
        color: this.$somms.generateColor(),
        title: {
          text: '盈利',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return params.name + '</br>' +
                   '金额:' + params.data.value + '</br>' +
                   '占比:' + params.data.rate + '%'
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          right: 10,
          top: 20,
          bottom: 20,
          data: deptName
        },
        series: [
          {
            type: 'pie',
            height: '90%',
            width: '90%',
            label: {
              show: true,
              formatter: '{b}'
            },
            data: deptData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      if (this.showPie) {
        this.$nextTick(() => {
          this.$refs.pieChart2.initChart()
        })
      }
    }
  }
}
</script>

<style scoped>
.dcew-query-button {
  width: 4.2rem;
}

.dcew-content-box {
  width: 24.5%;
  display: inline-block;
}
.dcew-content-box-title {
  width: 100%;
  height: 10%;
  padding: 1rem;
  font-size: var(--biggerSize);
  font-weight: bold;
}
.dcew-content-box-item {
  margin: 1rem 1rem 1rem 1rem;
  width: 25%;
  display: inline-block;
  text-align: center;
}
.dcew-content-box-item-title {
  color: gray;
  font-size: var(--biggerSmallSize);
  margin-bottom: 0.5rem
}
.dcew-content-box-item-val {
  font-size: var(--biggerSize);
  font-weight: bold;
}
.dcew-content-box-item-end{
  width: 20%;
  height: 100%;
  margin: 0.5rem 0 1rem 0.5rem;
  text-align: center;
  display: inline-block
}
.dcew-content-box-item-end-title{
  margin-bottom: 0.75rem;
  color: gray;
  font-size: var(--biggerSmallSize)
}
.dcew-content-box-item-end-val {
  cursor: pointer;
  text-decoration: underline;
  font-size: var(--biggerSize);
}
.dcew-content-box-item-end-val:hover{
  color: rgb(77,162,255);
  text-decoration: underline;
}
.seriousness {
  /*<!-- rgba(246,114,114,0.5) #f67272 -->*/
  background: linear-gradient(to bottom, rgba(246,114,114,0.5),white);
}
.warning {
  /*<!-- e7a646 rgba(231,166,70,0.5) -->*/
  background: linear-gradient(to bottom, rgba(231,166,70,0.5),white);
}
.steady {
  /** rgba(77,162,255,0.5) 4da2ff*/
  background: linear-gradient(to bottom, rgba(77,162,255,0.5),white);
}
.surplus {
  /** rgba(111,196,68,0.5) 6fc444*/
  background: linear-gradient(to bottom, rgba(111,196,68,0.5),white);
}
.decw-query-check-yz {
  background-color: rgb(246,114,114);
  color: white;
}
.decw-query-uncheck-yz{
  background-color: rgba(246,114,114,0.5);
  color: white
}
.decw-query-check-cz {
  background-color: rgb(231,166,70);
  color: white;
}
.decw-query-uncheck-cz{
  background-color: rgba(231,166,70,0.5);
  color: white
}
.decw-query-check-cp {
  background-color: rgb(77,162,255);
  color: white;
}
.decw-query-uncheck-cp{
  background-color: rgba(77,162,255,0.5);
  color: white
}
.decw-query-check-jy {
  background-color: rgb(111,196,68);
  color: white;
}
.decw-query-uncheck-jy{
  background-color: rgba(111,196,68,0.5);
  color: white
}
.decw-query-check-yz-mouseover {
  border: 1px solid rgb(246,114,114);
}
.decw-query-check-cz-mouseover {
  border: 1px solid rgb(231,166,70);
}
.decw-query-check-cp-mouseover{
  border: 1px solid rgb(77,162,255);
}
.decw-query-check-jy-mouseover{
  border: 1px solid rgb(111,196,68);
}
.dcew-content-box-left {
  margin-left: 0.5%;
}
.el-button--mini, .el-button--mini.is-round {
  padding: 7px 10px;
}

/deep/ .container-content{

  height: 100%;
}
</style>
