<template>
    <el-autocomplete
      popper-class="my-autocomplete"
      size="mini"
      v-model="group"
      :fetch-suggestions="fetchSuggestions"
      :placeholder="placeholder"
      @select="handleSelect"
      @clear="clearGroup"
      :popper-append-to-body="true"
      :clearable="true"
      :trigger-on-focus="false"
      ref='elautocomplete'>
      <template slot-scope="{ item }">
        <div class="code">{{ getShowObj(item).code }}</div>
        <span class="name">{{ getShowObj(item).name }}</span>
      </template>
    </el-autocomplete>
</template>
<script>
import { queryLikeDipGroupByPram, queryLikeDrgsByPram, queryLikePpsGroupByPram } from '@/api/common/drgCommon'
export default {
  name: 'jpGroup',
  props: {
    type: String,
    modelVal: String
  },
  data: () => ({
    group: '',
    placeholder: ''
  }),
  mounted () {
    this.getPlaceholder()
  },
  model: {
    prop: 'modelVal',
    event: 'groupSelected'
  },
  methods: {
    handleSelect (item) {
      if (this.type == 'DIP') {
        this.group = item.dipCodg
      }
      if (this.type == 'DRG') {
        this.group = item.drgsCode
      }
      if (this.type == 'CD') {
        this.group = item.cdCodg
      }
      this.$emit('groupSelected', this.group)
      this.changeGroup()
    },
    clearGroup () {
      this.$emit('groupSelected', this.group)
      this.changeGroup()
    },
    changeGroup () {
      this.$emit('changeGroup', this.group)
    },
    fetchSuggestions (queryString, cb) {
      if (this.type == 'DIP') {
        this.queryDipData(queryString, cb)
      }
      if (this.type == 'DRG') {
        this.queryDrgData(queryString, cb)
      }
      if (this.type == 'CD') {
        this.queryCdData(queryString, cb)
      }
    },
    queryDipData (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    queryDrgData (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    queryCdData (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikePpsGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    getShowObj (item) {
      let obj = {}
      if (this.type == 'DIP') {
        obj.code = item.dipCodg
        obj.name = item.dipName
      }
      if (this.type == 'DRG') {
        obj.code = item.drgsCode
        obj.name = item.drgsName
      }
      if (this.type == 'CD') {
        obj.code = item.cdCodg
        obj.name = item.cdName
      }
      return obj
    },
    getPlaceholder () {
      let head = '请输入'
      let end = '编码或名称'
      let content = '组'
      if (this.type == 'DIP') {
        content = 'DIP组'
      } else if (this.type == 'DRG') {
        content = 'DRG组'
      } else if (this.type == 'CD') {
        content = '成都组'
      }
      this.placeholder = head + content + end
    }
  },
  watch: {
    modelVal: {
      immediate: true,
      deep: true,
      handler: function (modelVal) {
        this.group = modelVal
      }
    },
    group: {
      immediate: true,
      handler () {
        this.$emit('groupSelected', this.group)
      }
    },
    type: {
      immediate: true,
      handler () {
        this.getPlaceholder()
      }
    }
  }
}
</script>
