<template>
  <el-table ref="elTable"
            :id="id"
            height="100%"
            stripe
            :header-cell-style="{'text-align':'center'}"
            :data="data"
            v-loading="loading"
            border>
    <el-table-column label="序号" type="index" width="50" fixed align="center"></el-table-column>
    <el-table-column label="病种编码" prop="icdCodg" :show-overflow-tooltip="true" fixed></el-table-column>
    <el-table-column label="病种名称" prop="icdName" :show-overflow-tooltip="true" fixed></el-table-column>
    <el-table-column label="入组病案数" prop="drgInGroupMedcasVal" width="150" :fixed="include('inGroupNum')">
      <template slot-scope="scope">
        <div :class="scope.row.drgInGroupMedcasVal == 0 ? '' : 'skip'" @click="scope.row.drgInGroupMedcasVal == 0 ? '' : queryGroupPatient(scope.row)">
          {{ scope.row.drgInGroupMedcasVal }}
        </div>
      </template>
    </el-table-column>
    <!--    <el-table-column label="正常付费数量" prop="normalNum" width="150"></el-table-column>-->
    <!--    <el-table-column label="超高病案数" prop="ultrahighNum" width="150"></el-table-column>-->
    <!--    <el-table-column label="超高率" prop="ultrahighRate" width="150"></el-table-column>-->
    <!--    <el-table-column label="超低病案数" prop="ultraLowNum" width="150"></el-table-column>-->
    <!--    <el-table-column label="超低率" prop="ultraLowRate" width="75"></el-table-column>-->
    <el-table-column label="总费用" prop="sumfee" width="150" :fixed="include('sumfee')"></el-table-column>
    <el-table-column :label="this.$somms.getFCOrFBName(this.$store.getters.feeStas,0)"  prop="forecastAmount" width="150"  :fixed="include('forecastAmount')"></el-table-column>
    <el-table-column :label="this.$somms.getFCOrFBName(this.$store.getters.feeStas,1)" prop="forecastAmountDiff" width="150"  :fixed="include('forecastAmountDiff')"></el-table-column>
    <el-table-column label="O/E值" prop="oeVal" width="75" :fixed="include('oeVal')"></el-table-column>
    <el-table-column label="悬浮"  width="75" align="center" >
      <template slot-scope="scope">
        <el-button size="mini" type="primary" @click="showSuspension(scope.row)">悬浮</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'newDipDiseaseTypeAnalysisComp',
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    },
    // 表格id
    id: {
      type: String
    },
    queryForm: {
      type: Object
    },
    columnOptions: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    include (column) {
      return this.fixedColumns.includes(column)
    },
    queryGroupPatient (item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          dipCodg: item.dipCodg,
          icdCodg: item.icdCodg,
          deptCode: this.queryForm.deptCode,
          isInGroup: 1,
          feeStas: this.queryForm.feeStas,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    // 显示悬浮
    showSuspension (scope) {
      this.scopeData = scope
      let scopeList = []
      for (let i in this.scopeData) {
        let o = {}
        o.key = i
        o.value = this.scopeData[i]
        scopeList.push(o)
      }
      let res = []
      if (scopeList.length) {
        for (let i = 0; i < scopeList.length; i++) {
          for (let j = 0; j < this.columnOptions.length; j++) {
            if (scopeList[i].key == this.columnOptions[j].value) {
              res.push({
                key: scopeList[i].key,
                label: this.columnOptions[j].label,
                value: scopeList[i].value,
                type: 2,
                show: true
              })
            }
          }
        }
      }
      if (scope) {
        this.$emit('showSuspension', res)
      }
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    }
  }
}
</script>

<style scoped>

</style>
