import request from '@/utils/request'

export function queryDeptFileUploadLog (params) {
  return request({
    url: '/deptFileUploadController/queryDeptFileUploadLog',
    method: 'post',
    params: params
  })
}

export function workersFileUploadLog (params) {
  return request({
    url: '/workersUploadController/workersFileUploadLog',
    method: 'post',
    params: params
  })
}

/**
 * 科室收入日志
 * @param params
 * @returns {*}
 */
export function queryDeptIncomeLog (params) {
  return request({
    url: '/costDataUploadController/queryDeptIncomeLog',
    method: 'post',
    params: params
  })
}

/**
 * 科室单元直接成本日志
 * @param params
 * @returns {*}
 */
export function deptCostExtractionLog (params) {
  return request({
    url: '/costDataUploadController/deptCostExtractionLog',
    method: 'post',
    params: params
  })
}

export function queryDeptIncomeDetailsLog (params) {
  return request({
    url: '/costDataUploadController/queryDeptIncomeDetailsLog',
    method: 'post',
    params: params
  })
}

/**
 * 科室单元间接成本抽取日志
 * @param params
 * @returns {*}
 */
export function inDirectCostExtractionLog (params) {
  return request({
    url: '/costDataUploadController/inDirectCostExtractionLog',
    method: 'post',
    params: params
  })
}

/**
 * 科室其他费用日志
 * @param params
 * @returns {*}
 */
export function queryDeptOtherCostLog (params) {
  return request({
    url: '/costDataUploadController/queryDeptOtherCostLog',
    method: 'post',
    params: params
  })
}
