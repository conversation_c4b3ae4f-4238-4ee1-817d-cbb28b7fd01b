<template>
  <div class="navbar">
    <div class="main-menu">
      <main-menu :menuList="routes" @select-menue="fnSelectMenu"></main-menu>
    </div>
    <div class="navbar-info">
      <div class="message">
        <i class="el-icon-message"
           @click="open2"
           style="font-size: 20px;color: #FFFFFF">
        </i>
        <div class="mark" v-if="value != 0">{{ value }}</div>
      </div>
      <el-card class="box-card" v-if="ToolShow">
        <div slot="header" class="clearfix">
          <span>系统更新日志</span>
          <el-button style="float: right" type="text" @click="closeMessage()">关闭</el-button>
        </div>
        <div v-for="(item,index) in data" class="text item" style="overflow: auto" :key="index">
          <el-card style="overflow-y: auto;max-height: 88%;">
            <div>版本号:{{ item.ver }}</div>
            <div style="line-height: 20px;">{{ item.content }}</div>
            <div style="float: right">{{ item.ym }}</div>
          </el-card>
        </div>
      </el-card>
      <!--      <div class="navbar-divider">-->
      <!--        <div class="navbar-divider-item"></div>-->
      <!--      </div>-->
      <div style="height: 100%;margin-right: 5px">
        <el-divider direction="vertical"></el-divider>
      </div>
      <el-dropdown class="avatar-container" trigger="click" ref="avatarContainer">
        <div class="avatar-wrapper">
          <div style="height: 25px;width: 25px;border-radius: 50%;font-size: 12px;cursor: pointer;
        background-color: #224abe;color: white;text-align: center;line-height: 25px;margin-right: 5px">
            {{ nickname ? nickname.substring(0, 1) : '' }}
          </div>
          <div class="userName">{{ nickname }}</div>
        </div>
        <el-dropdown-menu class="user-dropdown" slot="dropdown">
          <!--          <router-link class="inlineBlock" to="/">-->
          <!--            <el-dropdown-item class="dropdown-item">-->
          <!--              <i class="som-icon-home"></i>返回首页-->
          <!--            </el-dropdown-item>-->
          <!--          </router-link>-->
          <el-dropdown-item class="dropdown-item">
          <span @click="showInfo" class="dropdown-item-child">
            <i class="som-icon-modify"></i>修改密码
          </span>
          </el-dropdown-item>
          <!--          <el-dropdown-item class="dropdown-item">-->
          <!--          <span @click="refreshCache" class="dropdown-item-child">-->
          <!--            <i class="som-icon-refresh"></i>刷新缓存-->
          <!--          </span>-->
          <!--          </el-dropdown-item>-->
          <el-dropdown-item class="dropdown-item">
          <span @click="system" class="dropdown-item-child">
            <i class="som-icon-about"></i>关于系统
          </span>
          </el-dropdown-item>
          <el-dropdown-item class="dropdown-item" style="border: none">
          <span @click="logout" class="dropdown-item-child">
            <i class="som-icon-logout"></i>退出登录
          </span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-dialog
      title="关于系统"
      :visible.sync="dialogVisible"
      width="30%"
      @close="handleClose">
      <span>
        系统愿景：提供可靠的、准确的和及时的数据分析结果,帮助用户在海量数据中发现有意义的模式和趋势。
        <br>
        系统组成：数据收集工具、数据清洗和转换工具、数据存储和管理工具、数据分析和可视化工具。
      </span>
      <br/>
      <span style="cursor: pointer" @click="updateCode">订阅有效期：{{ time }}</span>
    </el-dialog>
  </div>

</template>

<script>
import {mapGetters} from 'vuex'
// import Breadcrumb from '@/components/Breadcrumb'
import mainMenu from './mainMenu'
import {getInfo} from '@/api/login'
import {queryData, querySystemTime, updateMessage} from '@/api/dataConfig/versionMessagePrompt'
import {Message, MessageBox} from 'element-ui'
import axios from 'axios'

export default {
  name: 'Navbar',
  components: {
    mainMenu
  },
  data: () => ({
    nickname: null,
    dialogFormVisible: false,
    img: '',
    dialogVisible: false,
    ver: [],
    message: [],
    time: '',
    ToolShow: false,
    data: {},
    value: '',
    username: ''
  }),
  created() {
    getInfo().then(response => {
      const data = response.data
      if (data.message != undefined) {
        if (data.message[0] == 0) {
          this.value = 0
        } else {
          this.value = data.message.length
        }
      }
      this.ver = response.data.message
      this.username = response.data.username
      if (data.nknm.length > 15) {
        this.nickname = data.nknm.substring(0, 15) + '.'
      } else {
        this.nickname = data.nknm
      }
    })
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar'
    ]),
    routes() {
      let routers = this.$store.state.user.routers
      this.initRouters(routers)
      return routers
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('ToggleSideBar')
    },
    logout() {
      this.$store.dispatch('LogOut').then(() => {
        this.out()
      }).catch(() => {
        this.out()
      })
    },
    out() {
      this.$router.push({path: '/login'}).then(res => {
        // location.reload() // 为了重新实例化vue-router对象 避免bug
        this.$store.dispatch('tagsView/delAllViews')
      })
    },
    showInfo() {
      this.$emit('showInfo', '')
    },
    refreshCache() {
      this.$store.dispatch('evictAll')
    },
    open2() {
      this.ToolShow = true
      let params = {}
      params.username = this.username
      params.versions = this.ver
      queryData(params).then(result => {
        this.data = result.data
      })
      updateMessage(params).then(res => {
        if (res.code == 200) {
          this.value = 0
        }
      })
    },
    system() {
      querySystemTime({}).then(res => {
        this.time = res.data
        this.dialogVisible = true
      })
    },
    updateCode() {
      MessageBox.prompt('验证码', '系统订阅', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then((value) => {
        axios.post('/sysInitController/verifyCode', {crtfCode: value.value}, {baseURL: process.env.BASE_API}).then(res => {
          let message = ''
          let type = ''
          if (res.data.data === '1') {
            window.sessionStorage.removeItem('time')
            message = '认证成功'
            type = 'success'
            this.system()
          } else {
            message = '认证失败'
            type = 'error'
          }
          Message({
            message: message,
            type: type
          })
        })
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    closeMessage() {
      this.ToolShow = false
    },
    initRouters(routers) {
      if (routers.length > 0) {
        routers.map(router => {
          router.active = false
          if (router.children && router.children.length > 0) {
            this.initRouters(router.children)
          }
        })
      }
    },
    fnSelectMenu(obj) {
      this.$emit('selectMainMenu', obj)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.navbar {
  height: 60px;
  width: 100%;
  line-height: 60px;
  padding: 0 0 0 1px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  border-radius: 0 !important;

  .main-menu {
    flex: 1 1 1px;
    max-width: calc(100% - 140px);
    min-width: 400px;
    width: calc(100% - 140px);
    height: 100%;
    display: flex;
    justify-content: flex-start;

  }

  .navbar-info {
    padding-left: 15px;
  }

  &-info {
    height: 100%;
    display: flex;
    justify-content: flex-end;
    background-color: #1b65b9;
  }

  .hamburger-container {
    line-height: 60px;
    height: 60px;
    float: left;
    padding: 0 10px;
  }

  .screenfull {
    position: absolute;
    right: 90px;
    top: 16px;
  }

  .avatar-container {
    display: flex;
    align-items: center;
    height: 60px;
    //position: absolute;
    //right: 25px;
    .avatar-wrapper {
      display: flex;
      align-items: center;
      cursor: pointer;

      .user-avatar {
        width: 25px;
        height: 25px;
        border-radius: 25px;
      }

      .el-icon-caret-bottom {
        position: absolute;
        right: -20px;
        top: 25px;
        font-size: 12px;
      }
    }
  }

  .userName {
    margin-right: 10px;
    font-size: 10px;
    color: #FFFFFF;
  }
}

.fade-leave-active {
  transition: 3s;
}

.message {
  cursor: pointer;
  float: right;
  align-items: center;
  display: flex;
  margin-right: 20px;
}

.diagnose-float-area {
  position: relative;
  width: 280px;
  top: 115px;
  border: 1.5px solid #988e19;
  border-radius: 3px;
  position: fixed;
  right: 50px;
  cursor: pointer;
  z-index: 1000;
}

.suggestGroup-title {
  font-weight: 600;
}

.suggestGroup-item {
  font-size: 12px;
  padding: 1px 26px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

.box-card {
  position: relative;
  width: 480px;
  top: 43px;
  border-radius: 3px;
  position: fixed;
  right: 6px;
  cursor: pointer;
  z-index: 1000;
  max-height: 90%;
  overflow-y: auto
}

.suggestGroup-item {
  font-size: 12px;
  padding: 1px 26px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.mark {
  background-color: rgb(245, 108, 108);
  color: white;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  position: absolute;
  top: 11px;
  right: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-item {
  padding: .3rem;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(220, 223, 230, 1);

  &-child {
    display: flex;
    align-items: center;
  }
}

.navbar-divider {
  width: 4rem;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  &-item {
    width: 1px;
    height: 70%;
    background: #e3e6f0;
  }
}
</style>
