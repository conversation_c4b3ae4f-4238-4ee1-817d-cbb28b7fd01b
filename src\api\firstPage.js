import request from '@/utils/request'
export function getCountInfo (params) {
  return request({
    url: '/firstPage/getCountInfo',
    method: 'post',
    params: params
  })
}

export function getDrgIndexInfo (params) {
  return request({
    url: '/firstPage/getDrgIndexInfo',
    method: 'post',
    params: params
  })
}

export function getMedicalTreatmentCostInfo (params) {
  return request({
    url: '/firstPage/getMedicalTreatmentCostInfo',
    method: 'post',
    params: params
  })
}

export function getDoctorDrgIndexInfo (params) {
  return request({
    url: '/firstPage/getDoctorDrgIndexInfo',
    method: 'post',
    params: params
  })
}

export function getOperativeInfo (params) {
  return request({
    url: '/firstPage/getOperativeInfo',
    method: 'post',
    params: params
  })
}

export function getDiseaseInfo (params) {
  return request({
    url: '/firstPage/getDiseaseInfo',
    method: 'post',
    params: params
  })
}

export function getDiseaseCostInfo (params) {
  return request({
    url: '/firstPage/getDiseaseCostInfo',
    method: 'post',
    params: params
  })
}

export function getDipCostInfo (params) {
  return request({
    url: '/firstPage/getDipCostInfo',
    method: 'post',
    params: params
  })
}

export function getIndexTop10DrgsInfo (params) {
  return request({
    url: '/firstPage/getIndexTop10DrgsInfo',
    method: 'post',
    params: params
  })
}

export function getIndexTop10DipInfo (params) {
  return request({
    url: '/firstPage/getIndexTop10DipInfo',
    method: 'post',
    params: params
  })
}
