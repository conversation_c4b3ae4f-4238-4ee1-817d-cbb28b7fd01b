<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-patient-num
             :show-dip="{show : this.$somms.getGroupType() === '1'}"
             :show-drg="{show : this.$somms.getGroupType() === '3'}"
             :show-hos-dept="{ show: this.$somms.hasHosRole()}"
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="结算清单详情"
             :container="true"
             :exportExcel="{ 'tableId': tableId, exportName: '结算清单'}"
             :exportExcelFun="queryPageData"
             :exportExcelHasChild="false"
             :initTimeValueNotQuery="false"
             ref="somForm"
             @query="handleSearchList">

      <template slot="extendFormItems">

        <el-form-item label="患者姓名">
          <el-input  v-model="listQuery.a11" placeholder="请输入患者姓名" />
        </el-form-item>

        <el-form-item label="是否标识" prop="lookOver">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择" v-model="listQuery.lookOver" @change="getDataIsuue"/>
        </el-form-item>

      </template>
      <template #buttonsMiddle>
        <el-button type="primary" class="som-button-margin-right" @click="fnToExtract">数据同步</el-button>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  :id="tableId"
                  size="mini"
                  stripe
                  height="100%"
                  :data="list"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>
          <el-table-column label="序号" type="index" align="right">
          </el-table-column>
          <el-table-column label="病案号" prop="a48" align="left" >
            <template slot-scope="scope">{{scope.row.a48 | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="姓名" prop="a11" align="left"  width="90" >
            <template slot-scope="scope">{{scope.row.a11 | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="性别" prop="a12c" align="left"  width="60"
                           :filters="[{ text: '男', value: '1' }, { text: '女', value: '2' }]"
                           :filter-method="filterSex">
            <template slot-scope="scope">{{scope.row.a12c | formatA12c}}</template>
          </el-table-column>
          <el-table-column prop="a14" label="年龄"  align="right" width="80" sortable>
            <template slot-scope="scope">{{scope.row.a14 | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="DRGs编码和名称" prop="drgsCodeAndName" align="left"  width="180" :show-overflow-tooltip="true" v-if="this.$somms.getGroupType() === '3'">
            <template slot-scope="scope">{{scope.row.drgsCodeAndName | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="DIP编码和名称" prop="dipCodeAndName" align="left"  width="180" :show-overflow-tooltip="true" v-if="this.$somms.getGroupType() === '1'">
            <template slot-scope="scope">{{scope.row.dipCodeAndName | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="入院科室"  align="left" prop="b13n"  :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.b13n | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="出院科室"  align="left" prop="b16n"  :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.b16n | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="主要诊断"  align="left" prop="c04n" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{ scope.row.c04n | formatIsEmpty }}</template>
          </el-table-column>
          <el-table-column label="主要手术"  align="left" prop="c15x01n" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.c15x01n | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column prop="b12" label="入院时间"  align="right"  width="140px" sortable>
          </el-table-column>
          <el-table-column prop="b15" label="出院时间"  align="right" width="140px"  sortable>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="90px" >
            <template slot-scope="scope">
              <el-button type="primary" size="mini"  @click="fnExtract(scope.row)" circle>
                同步数据
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </drg-form>
    <el-dialog
      title="抽取数据选择"
      :visible.sync="dialogVisible"
      width="30%"
      >
      <el-form :label-position="'right'" label-width="80px" :model="formData">
        <el-form-item label="查询类型">
          <el-radio v-model="formData.type" label="3">结算时间</el-radio>
          <el-radio v-model="formData.type" label="1">入院时间</el-radio>
          <el-radio v-model="formData.type" label="2">出院时间</el-radio>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="formData.time"
            style="width: 100%;"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="fnToExtractData">同 步</el-button>
  </span>
    </el-dialog>
  </div>
</template>
<script>
import { querySelectTreeAndSelectList, queryDataIsuue } from '@/api/common/drgCommon'
import { fetchList as queryPageData, extract, dataSynchronization } from '@/api/medicalQuality/settleListReExtract'
import { formatDate } from '@/utils/date'
import { elExportExcel } from '@/utils/exportExcel'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  a48: null,
  a11: null,
  b16c: null,
  b34c: null,
  settle_start_date: null,
  settle_end_date: null,
  ry_start_date: null,
  ry_end_date: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date,
  queryDipGroup: '',
  queryDrg: '',
  tableHeight: 0,
  lookOver: '',
  isAdjustable: null,
  isNullPreHosCost: null
}
export default {
  name: 'settleListReExtract',
  inject: ['reload'],
  data () {
    return {
      showFlag: false,
      dialogVisible: false,
      formData: {
        time: [],
        type: '3'
      },
      tableId: 'dataTable',
      listLoading: true,
      list: null,
      total: null,
      settle_start_date: null,
      settle_end_date: null,
      ry_start_date: null,
      ry_end_date: null,
      cy_start_date: null,
      cy_end_date: null,
      dictVoList: {},
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      depts: [],
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created () {

  },
  watch: {
    list: function () {
      this.$nextTick(() => {
        if (this.$refs.settleListTable) {
          this.$refs.settleListTable.doLayout()
        }
      })
    }
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime (time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },
    formatA12c (value) {
      if (value == '1') {
        return '男'
      } else if (value == '2') {
        return '女'
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.findSelectTreeAndSelectList()
      if (Object.keys(this.$route.query).length > 0) {
        if (this.$route.query.lookOver) {
          this.listQuery.lookOver = this.$route.query.lookOver
        }
        // 时间需要在最下面设置
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.listQuery)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.listQuery)
        }
      }
      this.getList()
    })
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'B34C')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        this.getList()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.a48 = this.listQuery.medcasCodg
      this.submitListQuery.a11 = this.listQuery.a11
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.b34c = this.listQuery.b34c
      this.submitListQuery.queryDipGroup = this.listQuery.dipCodg
      this.submitListQuery.queryDrg = this.listQuery.drgCodg
      this.submitListQuery.settle_start_date = this.settle_start_date
      this.submitListQuery.settle_end_date = this.settle_end_date
      this.submitListQuery.ry_start_date = this.listQuery.inStartTime
      this.submitListQuery.ry_end_date = this.listQuery.inEndTime
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.lookOver = this.listQuery.lookOver
      this.submitListQuery.isAdjustable = this.listQuery.isAdjustable
      this.submitListQuery.isNullPreHosCost = this.listQuery.isNullPreHosCost
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    handleSearchList () {
      this.getList()
    },
    preview (row) {
      this.$router.push({ path: '/setlListManage/mdcsListPreview', query: { k00: row.k00, id: row.id } })
    },
    handleShowMedicalDetail (index, row) {
      this.$router.push({ path: '/setlListManage/setlListDetail', query: { k00: row.k00, id: row.id } })
    },
    newHandleShowMedicalDetail (index, row, type) {
      let obj = { path: '/setlListManage/setlListInfo2', query: { k00: row.k00, id: row.id } }
      if (type === '2') {
        obj.query.see = true
      } else {
        obj.query.see = false
      }
      this.$router.push(obj)
    },
    filterSex (value, row) {
      return row.a12c === value
    },
    exportExcel () {
      let tableId = 'slTable'
      let fileName = '病案数据'
      elExportExcel(tableId, fileName)
    },
    fnToExtract () {
      this.dialogVisible = true
    },
    fnToExtractData () {
      let params = {}
      params.type = this.formData.type
      let updt_date = this.formData.time
      if (updt_date && updt_date.length > 0) {
        this.$confirm('同步数据后将会覆盖掉医生在结算清单页面上修改的数据', '确认同步?', {
          type: 'warning'
        }).then(async () => {
          params.begnDate = updt_date[0]
          params.expiDate = updt_date[1]
          extract(params).then(res => {
            if (res.code === 200) {
              this.$message.success('数据同步成功')
            }
          })
        })
      } else {
        this.$message.error('请选择时间范围')
      }
    },
    fnExtract (row) {
      // 数据同步
      this.$confirm('同步数据后将会覆盖掉医生在结算清单页面上修改的数据', '确认同步?', {
        type: 'warning'
      }).then(async () => {
        dataSynchronization({ k00: row.k00 }).then(res => {
          if (res.code === 200) {
            this.$message.success('数据同步成功')
          }
        })
      }).catch(() => {})
    },
    getParams () {
      return this.submitListQuery
    }
  }
}
</script>
<style scoped>
/*时间样式设置*/
/deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner{
  width: 200px;
}
.autoSelectInputWidth{
  width: 178px;
}

</style>
