<template>
  <el-dialog
    :z-index="1000"
    title="医院环境生成"
    :visible.sync="showDialog"
    width="80%"
    @closed="closed">
    <div>
      <el-steps :active="masterStep" align-center finish-status="success">
        <el-step title="选择操作医院" :description="step1Desc"></el-step>
        <el-step title="生成组织架构" :description="step2Desc"></el-step>
        <el-step title="修改系统配置" :description="step3Desc"></el-step>
        <el-step title="生成标杆配置" :description="step4Desc"></el-step>
      </el-steps>
    </div>

    <el-divider></el-divider>

    <div style="height: 40vh;">
      <!-- step1 -->
      <hospital-env-generate-step1
        :tree-data="step1TreeData"
        :tree-hospital-info="step1HospitalInfo"
        v-show="masterStep == 0"
        @success="step1Success"
        @reset="step1Init"/>

      <!-- step2 -->
      <hospital-env-generate-step2
        :step="masterStep"
        :hospital-info="hospitalInfo"
        v-show="masterStep == 1"
        @success="step2Success"
        @prevStep="step2PrevStep"/>

      <!-- step3 -->
      <hospital-env-generate-step3
        :step="masterStep"
        :hospital-info="hospitalInfo"
        v-show="masterStep == 2"
        @success="step3Success"
        @prevStep="step3PrevStep"/>

      <!-- step4 -->
      <hospital-env-generate-step4
        :step="masterStep"
        :hospital-info="hospitalInfo"
        v-show="masterStep == 3"
        @success="step4Success"
        @prevStep="step4PrevStep"/>

      <div v-if="masterStep == 4" style="width: 100%;height: 100%;flex-direction: column" class="som-flex-center">
        <h2>{{ 'Congratulation！' }}</h2>
        <img :src="Congratulation" width="490" height="280">
      </div>
    </div>

    <div style="position: absolute; bottom: 5%;right: 2%" v-if="masterStep == 4">
      <el-button type="primary" @click="prevStep">上一步</el-button>
      <el-button type="primary" @click="showDialog = false">完 成</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { queryTreeNode } from '@/api/orgManamement'
import { queryHospitalInfo } from '@/api/dataConfig/hospitalView'
import hospitalEnvGenerateStep1 from './hospitalEnvGenerateStep1'
import hospitalEnvGenerateStep2 from './hospitalEnvGenerateStep2'
import hospitalEnvGenerateStep3 from './hospitalEnvGenerateStep3'
import hospitalEnvGenerateStep4 from './hospitalEnvGenerateStep4'
import Congratulation from '@/assets/images/congratulation3.gif'

export default {
  name: 'hospitalEnvGenerateDialog',
  components: {
    'hospital-env-generate-step1': hospitalEnvGenerateStep1,
    'hospital-env-generate-step2': hospitalEnvGenerateStep2,
    'hospital-env-generate-step3': hospitalEnvGenerateStep3,
    'hospital-env-generate-step4': hospitalEnvGenerateStep4
  },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    Congratulation,
    showDialog: false,
    masterStep: 0,
    hospitalInfo: {},

    // 步骤1
    step1Desc: '生成当前医院标杆数据',
    step1TreeData: [],
    step1HospitalInfo: [],

    // 步骤2
    step2Desc: '生成医院，科室，医生账号，需要导入科室数据和医生数据',

    // 步骤3
    step3Desc: '修改系统配置，如当前系统是DIP还是DRG',

    // 步骤4
    step4Desc: '生成当前医院标杆数据'
  }),
  mounted () {
    let stepInitName = 'step' + (this.masterStep + 1) + 'Init'
    this[stepInitName]()
  },
  methods: {
    // 关闭
    closed () {
      this.$emit('closed', true)
    },
    // 步骤1初始化
    async step1Init () {
      let hospitalIds = []
      await queryHospitalInfo().then(res => {
        res.data.map(d => hospitalIds.push(d.hospitalId))
      })
      await queryTreeNode().then(response => {
        this.step1HospitalInfo = []
        this.step1TreeData = []
        if (response.data.length > 0) {
          this.generateHospitalData(response.data, this.step1HospitalInfo, hospitalIds)
          this.step1TreeData = response.data
        }
      })
      this.step1DescInit()
    },
    step1DescInit () {
      this.step1Desc = '选择或者新增需要生成医院，如无医院可新增'
    },
    // 生成医院数据
    generateHospitalData (hospitalTreeData, hospitalInfo, hospitalIds) {
      hospitalTreeData.map(res => {
        if (hospitalIds.includes(res.orgId)) {
          hospitalInfo.push(res)
          res.children = []
        }
        if (res.children && res.children.length > 0) {
          this.generateHospitalData(res.children, hospitalInfo, hospitalIds)
        }
      })
    },
    // 步骤1完成
    step1Success (data) {
      this.step1Desc = '已选择【' + data.medinsName + '】'
      this.hospitalInfo = data
      this.masterStep++
    },
    // 步骤2初始化
    step2Init () {
      this.step2Desc = '生成医院，科室，医生账号，需要导入科室数据和医生数据'
    },
    // 步骤2完成
    step2Success (data) {
      this.step2Desc = data.msg
      this.masterStep++
    },
    // 步骤2上一步
    step2PrevStep () {
      this.step1Init()
      this.masterStep--
    },
    // 步骤3初始化
    step3Init () {
      this.step3Desc = '修改系统配置，如当前系统是DIP还是DRG'
    },
    // 步骤3完成
    step3Success (data) {
      this.step3Desc = data.msg
      this.masterStep++
    },
    // 步骤3上一步
    step3PrevStep () {
      this.step2Init()
      this.masterStep--
    },

    // 步骤4初始化
    step4Init () {
      this.step4Desc = '生成当前医院标杆数据'
    },
    // 步骤4完成
    step4Success (data) {
      this.step4Desc = data.msg
      this.masterStep++
    },
    // 步骤4上一步
    step4PrevStep (step) {
      this.step3Init()
      this.masterStep -= step
    },

    // 上一步
    prevStep () {
      this.masterStep--
    }
  },
  watch: {
    show (val) {
      this.showDialog = val
    }
  }
}
</script>
<style scoped>
/deep/ .el-dialog {
  height: 80vh;
}
</style>
