<template>
  <div style="height: 100%">
    <el-table :ref="nameRef"
              :id="id"
              height="100%"
              :header-cell-style="{'text-align':'center'}"
              :data="tableData"
              v-loading="tableLoading"
              border>
      <el-table-column label="序号" type="index" align="left"></el-table-column>
      <el-table-column label="标杆年份" prop="standardYear" align="left" width="80px"></el-table-column>
      <el-table-column label="DIP编码" prop="dipCodg" align="left" show-overflow-tooltip v-if="this.queryType==1" :key="1"></el-table-column>
      <el-table-column label="DIP名称" prop="dipName" align="left" show-overflow-tooltip v-if="this.queryType==1" :key="2"></el-table-column>
      <drg-table-column label="是否使用辅助目录" prop="usedAsstList" width="80px" dicType="AD" v-if="this.queryType==1" :key="3"/>
      <el-table-column label="年龄辅助目录" prop="asstListAgeGrp" align="left" show-overflow-tooltip v-if="this.queryType==1" :key="4"></el-table-column>
      <el-table-column label="严重程度辅助目录" prop="asstListDiseSevDeg" align="left" show-overflow-tooltip v-if="this.queryType==1" :key="5"></el-table-column>
      <el-table-column label="肿瘤辅助目录" prop="asstListTmorSevDeg" align="left" show-overflow-tooltip v-if="this.queryType==1" :key="6"></el-table-column>
      <el-table-column label="DIP标杆住院费用(同区域)" prop="dipStandardCost" width="100px" align="right" v-if="this.queryType==1" fixed="right" :key="7"></el-table-column>
      <el-table-column label="DIP标杆住院费用(同级别)" prop="dipStandardCostLevel" width="100px" align="right" v-if="this.queryType==1" fixed="right" :key="8"></el-table-column>
      <el-table-column label="DIP平均住院天数" prop="dipStandardDaysLevel" width="100px" align="right" v-if="this.queryType==1" :key="9"></el-table-column>
      <el-table-column label="DIP药占比" prop="dipDrugRate" width="100px" align="right" v-if="this.queryType==1" :key="10">
        <template slot-scope="scope">
          {{ scope.row.dipDrugRate + '%' }}
        </template>
      </el-table-column>
      <el-table-column label="DIP耗占比" prop="dipComsumableRate" width="100px" align="right" v-if="this.queryType==1" :key="11">
        <template slot-scope="scope">
          {{ scope.row.dipComsumableRate + '%' }}
        </template>
      </el-table-column>
      <el-table-column label="DRG编码" prop="drgCodg" align="left" show-overflow-tooltip v-if="this.queryType==3" :key="9"></el-table-column>
      <el-table-column label="DRG名称" prop="drgName" align="left" show-overflow-tooltip v-if="this.queryType==3" :key="10"></el-table-column>
      <el-table-column label="基准分值" prop="basSco" width="100px" align="right" :key="12"></el-table-column>
      <el-table-column label="倍率下限" prop="lowlmtMag" width="100px" align="right" :key="13"></el-table-column>
      <el-table-column label="倍率上限" prop="uplmtMag" width="100px" align="right" :key="14"></el-table-column>
      <el-table-column label="DRG标杆例均费用" prop="drgStandardCost" align="right" v-if="this.queryType==3" fixed="right" :key="11"></el-table-column>
      <el-table-column label="费用推荐区间" prop="recommendRange" width="160px" align="right"/>
      <el-table-column label="全市均费" prop="allAvgFee" width="160px" align="right"/>
<!--      <el-table-column label="精准控费区间" prop="accurateRange" width="160px" align="right"/>-->
      <el-table-column label="操作" width="100px" align="center" fixed="right">
        <template #default="scope">
          <el-button
            size="mini"
            icon="el-icon-edit"
            type="primary"
            circle
            @click="handleEdit(scope.$index, scope.row)"></el-button>
        </template>
      </el-table-column>
      <el-table-column label="删除" width="100" align="center" fixed="right">
        <template slot-scope="scope">
          <el-popconfirm style="cursor: pointer;display: block" @confirm="deleteData(scope.row)"
                         title="是否删除？">
            <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="编辑"
      ref="editForm"
      width="30%"
      :visible.sync="editVisible">
      <el-form :model="editForm" size="mini" ref="editForm"  :inline="false">
            <el-form-item
              prop="code"
              :label="getDiseaseShowName(1)">
              <el-input disabled v-model="editForm.code" />
            </el-form-item>
            <el-form-item
              prop="name"
              :label="getDiseaseShowName(2)">
              <el-input disabled v-model="editForm.name"></el-input>
             </el-form-item>
            <el-form-item
              prop="drgStandardCost"
              v-if="this.queryType==3 ? true : false"
              label="DRG标杆费用">
              <el-input v-model="editForm.drgStandardCost" ></el-input>
            </el-form-item>
            <el-form-item
              prop="dipStandardCost"
              v-if="this.queryType==1 ? true : false"
              label="DIP标杆住院费用(同区域)">
              <el-input v-model="editForm.dipStandardCost" ></el-input>
            </el-form-item>
            <el-form-item
              prop="dipStandardCostLevel"
              v-if="this.queryType==1 ? true : false"
              label="DIP标杆住院费用(同级别)">
              <el-input v-model="editForm.dipStandardCostLevel" ></el-input>
            </el-form-item>
            <el-form-item
              prop="basSco"
              v-if="this.queryType==1 ? true : false"
              label="基准分值">
              <el-input v-model="editForm.basSco" ></el-input>
            </el-form-item>
      </el-form>
      <template #footer>
          <span class="dialog-footer">
            <el-button @click="editCancel" size="mini" >取 消</el-button>
            <el-button type="primary" @click="updateData"  size="mini" >保 存</el-button>
          </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>

import { deleteData, updateData } from '../../../../api/dataConfig/benchmarkConfig'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200
}
export default {
  name: 'benchmarkDataResult',
  props: {
    standardYear: {

    },
    tableData: {
      type: Array
    },
    queryType: {
      type: Number
    },
    tableLoading: {
      type: Boolean,
      default: false
    },
    // 表格id
    id: {
      type: String
    },
    nameRef: {
      type: String
    }
  },
  data: () => ({
    editVisible: false,
    editForm: {
      queryType: '',
      code: '',
      name: '',
      drgStandardCost: '',
      dipStandardCost: '',
      dipStandardCostLevel: '',
      basSco: ''
    }
  }),
  methods: {
    handleEdit (index, row) {
      if (this.queryType == 1) {
        this.editForm.code = row.dipCodg
        this.editForm.name = row.dipName
        this.editForm.dipStandardCost = row.dipStandardCost
        this.editForm.dipStandardCostLevel = row.dipStandardCostLevel
        this.editForm.basSco = row.basSco
      } else if (this.queryType == 3) {
        this.editForm.code = row.drgCodg
        this.editForm.name = row.drgName
        this.editForm.drgStandardCost = row.drgStandardCost
      }
      this.editVisible = true
    },
    editCancel () {
      this.$confirm('关闭后所做修改将不会保存,是否确认关闭？')
        .then(_ => {
          this.editVisible = false
        })
        .catch(_ => {
          this.editVisible = false
        })
    },
    updateData () {
      let params = this.editForm
      if (this.queryType == 1) {
        params.queryType = this.queryType
        params.dipCodg = this.editForm.code
        params.dipName = this.editForm.name
        params.dipStandardCost = this.editForm.dipStandardCost
        params.dipStandardCostLevel = this.editForm.dipStandardCostLevel
        params.standardYear = this.standardYear
        params.basSco = this.editForm.basSco
      } else if (this.queryType == 3) {
        params.queryType = this.queryType
        params.drgCodg = this.editForm.code
        params.drgName = this.editForm.name
        params.drgStandardCost = this.editForm.drgStandardCost
        params.standardYear = this.standardYear
      }
      updateData(params).then(res => {
        if (res.code == 200) {
          this.$message.success('更新成功')
          this.editVisible = false
          this.$emit('selectData', '')
        }
      })
    },
    deleteData (row) {
      row.queryType = this.queryType
      deleteData(row).then(res => {
        if (res.code == 200) {
          this.$message.success('删除成功')
        }
      })
    },
    getDiseaseShowName (type) {
      let suffix = ''
      if (type == 1) {
        suffix = '编码'
      } else {
        suffix = '名称'
      }
      if (this.queryType == 1) {
        return 'DIP' + suffix
      }
      if (this.queryType == 3) {
        return 'DRG' + suffix
      }
    }
  }

}

</script>
