import request from '@/utils/request'

/**
 * 新增服务单元
 * @param data
 * @returns {*}
 */
export function addServiceUnit (data) {
  return request({
    url: '/costStandardServiceUnitController/addServiceUnit',
    method: 'post',
    data: data
  })
}

/**
 * 删除服务单元
 * @param params
 * @returns {*}
 */
export function deleteServiceUnit (params) {
  return request({
    url: '/costStandardServiceUnitController/deleteServiceUnit',
    method: 'post',
    params: params
  })
}

/**
 * 修改服务单元
 * @param data
 * @returns {*}
 */
export function modifyServiceUnit (data) {
  return request({
    url: '/costStandardServiceUnitController/modifyServiceUnit',
    method: 'post',
    data: data
  })
}

/**
 * 查询启用标准科室单元数据
 * @param params
 * @returns {*}
 */
export function queryEnabledDeptData (params) {
  return request({
    url: '/costStandardServiceUnitController/queryEnabledDeptData',
    method: 'post',
    params: params
  })
}

/**
 * 查询服务单元名称是否重复
 * @param params
 * @returns {*}
 */
export function queryServiceNameExists (params) {
  return request({
    url: '/costStandardServiceUnitController/queryServiceNameExists',
    method: 'post',
    params: params
  })
}

/**
 * 查询服务单元数据
 * @param params
 * @returns {*}
 */
export function queryServiceUnitData (params) {
  return request({
    url: '/costStandardServiceUnitController/queryServiceUnitData',
    method: 'post',
    params: params
  })
}

/**
 * 查询服务单元费用项数据
 * @param params
 * @returns {*}
 */
export function queryServiceCostItem (params) {
  return request({
    url: '/costStandardServiceUnitController/queryServiceCostItem',
    method: 'post',
    params: params
  })
}
