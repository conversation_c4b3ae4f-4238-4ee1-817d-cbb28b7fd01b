<template>
  <!--  <div class="analysis-wrapper">-->
  <!--    <div class="analysis-wrapper-left">-->
  <el-table ref="elTable"
            :id="id"
            height="100%"
            stripe
            :header-cell-style="{'text-align' : 'center'}"
            :data="data"
            :summary-method="getSummaries"
            show-summary
            v-loading="loading"
            @row-click="tableRowClick"
            border>
    <el-table-column label="序号" type="index" width="50"  align="center"/>
    <!--        <el-table-column label="DRG编码" prop="drgCodg" :show-overflow-tooltip="true"  width="110" fixed />-->
    <!--        <el-table-column label="DRG名称" prop="drgName" :show-overflow-tooltip="true"  width="110" fixed />-->
    <el-table-column label="病种编码" prop="icdCodg" :show-overflow-tooltip="true" width="110"
                     :fixed="include('icdName')"/>
    <el-table-column label="病种名称" prop="icdName" :show-overflow-tooltip="true" width="200"
                     :fixed="include('diagnosisType')"/>
    <el-table-column label="入组病案数" prop="drgInGroupMedcasVal" width="110" sortable align="center"
                     :fixed="include('inGroupNum')">
      <template slot-scope="scope">
        <div :class="scope.row.drgInGroupMedcasVal == 0 ? '' : 'skip'"
             @click="scope.row.drgInGroupMedcasVal == 0 ? '' : queryGroupPatient(scope.row)">
          {{ scope.row.drgInGroupMedcasVal }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="平均住院费用" prop="avgInHosCost" width="125" sortable align="right"
                     :fixed="include('avgInHosCost')"/>
    <el-table-column label="平均住院费用(反馈)" prop="fbAvgInHosCost" width="160" sortable align="right"
                     :fixed="include('fbAvgInHosCost')" v-if="queryForm.feeStas == 1"/>
    <el-table-column label="标杆住院费用" prop="standardInHosCost" width="125" align="right"
                     :fixed="include('standardInHosCost')"/>
    <el-table-column label="平均住院天数" prop="avgInHosDays" width="125" sortable align="center"
                     :fixed="include('avgInHosDays')"/>
    <el-table-column label="标杆住院天数" prop="standardInHosDays" width="125" align="center"
                     :fixed="include('standardInHosDays')"/>
    <el-table-column label="实际总费用" prop="sumfee" width="125" :fixed="include('sumfee')" align="right" sortable></el-table-column>

    <el-table-column label="药品费" prop="drugFee" width="125" :fixed="include('drugFee')" align="right" sortable></el-table-column>

    <el-table-column label="药占比" prop="drugRatio" width="152" sortable align="center" :fixed="include('drugRatio')">
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.drugRatio) }}
      </template>
    </el-table-column>
    <el-table-column label="标杆药占比" prop="standardDrugRatio" width="125" align="center"
                     :fixed="include('standardDrugRatio')">
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.standardDrugRatio) }}
      </template>
    </el-table-column>
    <el-table-column label="耗材费" prop="consumeFee" width="125" :fixed="include('consumeFee')" align="right" sortable></el-table-column>

    <el-table-column label="耗占比" prop="consumeRatio" width="125" sortable align="center"
                     :fixed="include('consumeRatio')">
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.consumeRatio) }}
      </template>
    </el-table-column>
    <el-table-column label="标杆耗占比" prop="standardConsumeRatio" width="90" align="right"
                     :fixed="include('standardConsumeRatio')">
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.standardConsumeRatio) }}
      </template>
    </el-table-column>
    <el-table-column label="时间消耗指数" prop="timeIndex" width="125" sortable align="center"
                     :fixed="include('timeIndex')"/>
    <el-table-column label="费用消耗指数" prop="costIndex" width="125" sortable align="center"
                     :fixed="include('costIndex')"/>
    <el-table-column label="悬浮" align="center">
      <template slot-scope="scope">
        <el-button size="mini" type="primary" @click="showSuspension(scope.row)">悬浮</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>

export default {
  name: 'newDrgIlnessKpiTable',
  components: {},
  computed: {
    formattedStandardInHosCost() {
      return this.standardInHosCost.toFixed(2);
    }
  },
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    },
    // 表格id
    id: {
      type: String
    },
    queryForm: {
      type: Object
    },
    columnOptions: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    scopeData: {},
    rightAnalysisData: {}
  }),
  // 在vue生命周期updated中添加方法（使用该方法要给table里加ref="table"）
  updated: function () {
    this.$nextTick(() => {
      this.$refs['elTable'].doLayout()
    })
  },
  methods: {
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length,
        customAverage: (values) => {
          const sum = values.reduce((prev, curr) => prev + curr, 0)
          return sum / values.length
        }
      }
      let sum8 = 0, sum9 = 0, sum12 = 0
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计'
          return
        }
        const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value))
        if (index === 3) {
          sums[index] = calculations.sum(values)
        } else if (index === 8 || index === 9 || index === 12) {
          sums[index] = calculations.sum(values).toFixed(2)
          if (index === 8) sum8 = sums[index]
          else if (index === 9) sum9 = sums[index]
          else if (index === 12) sum12 = sums[index]
        } else if (index === 4 || index === 5 || index === 6 || index === 7 || index === 15 || index === 16) {
          sums[index] = calculations.average(values).toFixed(2)
        } else {
          sums[index] = ' '
        }
      })
      sums[10] = calculatePercentage(sum9, sum8)
      sums[13] = calculatePercentage(sum12, sum8)
      return sums
      function calculatePercentage (numerator, denominator) {
        if (denominator !== 0) {
          return ((numerator / denominator) * 100).toFixed(2) + '%'
        }
        return '0.00%'
      }
    },
    include (column) {
      return this.fixedColumns.includes(column)
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    // 显示悬浮
    showSuspension (scope) {
      this.scopeData = scope
      let scopeList = []
      for (let i in this.scopeData) {
        let o = {}
        o.key = i
        o.value = this.scopeData[i]
        scopeList.push(o)
      }
      let res = []
      for (let i = 0; i < scopeList.length; i++) {
        for (let j = 0; j < this.columnOptions.length; j++) {
          if (scopeList[i].key == this.columnOptions[j].value) {
            res.push({
              key: scopeList[i].key,
              label: this.columnOptions[j].label,
              value: scopeList[i].value,
              type: 1,
              show: true
            })
          }
        }
      }
      if (scope) {
        this.$emit('showSuspension', res)
      }
    },
    queryGroupPatient (item) {
      this.$router.push({
        path: '/hosDrgAnalysisNew/pattAnalysis',
        query: {
          drgCodg: this.queryForm.drgCodg,
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          icdCodg: item.icdCodg,
          deptCode: this.queryForm.deptCode,
          isInGroup: 1,
          feeStas: this.queryForm.feeStas,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    tableRowClick (row) {
      this.rightAnalysisData = {}
      this.rightAnalysisData.profttl = row.drgCodg
      this.rightAnalysisData.subhead = row.drgName
      this.rightAnalysisData.items = this.getCostItemInfo(row)
      this.$emit('rowClick', row)
    },
    getCostItemInfo (row) {
      this.suggestData = []
      let costItem = [
        {
          name: '综合医疗服务费',
          value: row.com_med_servfee,
          bgValue: row.bgzhylfwf,
          children: [
            { name: '一般医疗服务费', value: row.ordn_med_servfee, bgValue: row.bgybylfwf },
            { name: '一般治疗操作费', value: row.ordn_trt_oprt_fee, bgValue: row.bgybzlczf },
            { name: '护理费', value: row.nursfee, bgValue: row.bghlf },
            { name: '其他费', value: row.zhylfwqtf, bgValue: row.bgzhylfwqtf }
          ]
        },
        {
          name: '诊断费',
          value: row.diag_fee,
          bgValue: row.bgzdf,
          children: [
            { name: '病理诊断费', value: row.cas_diag_fee, bgValue: row.bgblzdf },
            { name: '实验室诊断费', value: row.lab_diag_fee, bgValue: row.bgsyszdf },
            { name: '影像学诊断费', value: row.rdhy_diag_fee, bgValue: row.bgyxxzdf },
            { name: '临床诊断项目费', value: row.clnc_diag_item_fee, bgValue: row.bglczdxmf }
          ]
        },
        {
          name: '治疗费',
          value: row.treat_fee,
          bgValue: row.bgzlf,
          children: [
            { name: '非手术治疗项目费', value: row.nsrgtrt_item_fee, bgValue: row.bgfsszlxmf },
            { name: '手术治疗费', value: row.oprn_treat_fee, bgValue: row.bgsszlf }
          ]
        },
        { name: '康复费', value: row.rhab_fee, bgValue: row.bgkff, children: [] },
        { name: '中医费', value: row.tcmdrug_fee, bgValue: row.bgzyf, children: [] },
        { name: '西药费', value: row.west_fee, bgValue: row.bgxyf, children: [] },
        {
          name: '中药费',
          value: row.zyf1,
          bgValue: row.bgzyf1,
          children: [
            { name: '中成药费', value: row.tcmpat_fee, bgValue: row.bgzcyf },
            { name: '中草药费', value: row.tcmherb, bgValue: row.bgzcyf1 }
          ]
        },
        {
          name: '血液和血液制品费',
          value: row.blood_blo_pro_fee,
          bgValue: row.bgxyhxyzpf,
          children: [
            { name: '血费', value: row.blo_fee, bgValue: row.bgxf },
            { name: '白蛋白类制品费', value: row.bdblzpf, bgValue: row.bgbdblzpf },
            { name: '球蛋白类制品费', value: row.qdblzpf, bgValue: row.bgqdblzpf },
            { name: '凝血因子类制品费', value: row.nxyzlzpf, bgValue: row.bgnxyzlzpf },
            { name: '细胞因子类制品费', value: row.xbyzlzpf, bgValue: row.bgxbyzlzpf }
          ]
        },
        {
          name: '耗材费',
          value: row.mcs_fee,
          bgValue: row.bghcf,
          children: [
            { name: '检查用一次性医用材料费', value: row.jcyycxyyclf, bgValue: row.bgjcyycxyyclf },
            { name: '治疗用一次性医用材料费', value: row.trt_use_dspo_med_matlfee, bgValue: row.bgzlyycxyyclf },
            { name: '手术用一次性医用材料费', value: row.oprn_use_dspo_med_matlfee, bgValue: row.bgssyycxyyclf }
          ]
        },
        { name: '其他费', value: row.oth_fee, bgValue: row.bgqtf, children: [] }
      ]

      // 建议
      costItem.forEach(item => {
        if (item.value / item.bgValue > 2) {
          this.suggestData.push('建议降低' + item.name)
        }

        if (item.value / item.bgValue < 0.5) {
          this.suggestData.push('建议提高' + item.name)
        }
      })
      return costItem
    }
  }
}
</script>

<style lang="scss" scoped>
.analysis-wrapper {
  height: 100%;
  width: 100%;
  position: relative;

  &-left {
    width: 80%;
    height: 100%;
  }

  &-analysis {
    width: 100%;
    height: 80%;
    position: relative;
    display: flex;
  }

  &-right {
    width: 20%;
    height: 100%;
    background-color: rgba(131, 175, 155, .3);
    padding: 1% 1% 0 1%;
    border-radius: 1%;
  }

  &-no-table {
    width: 100%;
    height: 80%;
  }
}

.analysis-head {
  width: 80%;
  height: 20%;

  &-title {
    width: 100%;
    height: 20%;
    font-size: var(--biggerSize);
    font-weight: 600;
    position: relative;

    &-dropdown {
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  &-content {
    width: 100%;
    height: 54%;
    display: flex;

    &-item {
      width: 14%;
      height: 100%;
      cursor: pointer;
      background-color: rgb(64, 158, 255);
      font-weight: 600;
      margin-right: 1%;
      padding: 1%;
      display: flex;
      align-items: center;
      justify-items: center;
      flex-direction: column;
      border-radius: 16% 0 16% 0;

      .title {
        font-size: 14px;
        color: white;
      }

      .value {
        margin-top: 8%;
        font-size: 24px;
        color: white;
      }
    }
  }
}

.analysis-content {
  width: 100%;
  height: 94%;
  position: relative;

  &-summary {
    width: 30%;
    height: 40%;
    border: 1px solid red;
  }
}

.analysis-wrapper-right {
  width: 20%;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0%;
  display: flex;
  justify-content: center;

  &-content {
    width: 100%;
    height: 100%;
  }
}

$titleGray: gray;
$titleSize: 13px;
.analysis-right {

  &-title {
    width: 100%;
    height: 4%;
    font-size: var(--biggerSize);
    font-weight: 600;
  }

  &-subhead {
    width: 100%;
    height: 4%;
    font-size: var(--biggerSmallSize);
    color: gray;
    border-bottom: 1px solid $titleGray;
  }

  &-cost {
    width: 100%;
    height: 62%;
    line-height: 20px;
    overflow-y: auto;
    border-bottom: 1px solid $titleGray;
  }

  &-suggest {
    padding-top: 4%;
    width: 100%;
    height: 30%;
    overflow-y: auto;

    &-title {
      width: 100%;
      font-weight: 600;
      height: 10%;
      font-size: $titleSize;
    }

    &-content {
      width: 100%;
      height: 70%;
      overflow-y: auto;
    }

    &-declaration {
      width: 100%;
      height: 15%;
      color: #fa5d5d;
      display: flex;
      align-items: center
    }

    &-item {
      width: 100%;
      height: 16%;
    }
  }
}
</style>
