import request from '@/utils/request'

export function queryErrorDesc () {
  return request({
    url: '/checkAnalysisController/queryErrorDesc',
    method: 'post'
  })
}

export function queryErrorNum (params) {
  return request({
    url: '/checkAnalysisController/queryErrorNum',
    method: 'post',
    params: params
  })
}

export function queryErrorData (params) {
  return request({
    url: '/checkAnalysisController/queryErrorData',
    method: 'post',
    params: params
  })
}

export function queryDeptErrorNum (params) {
  return request({
    url: '/checkAnalysisController/queryDeptErrorNum',
    method: 'post',
    params: params
  })
}

export function queryErrorType (params) {
  return request({
    url: '/checkAnalysisController/queryErrorType',
    method: 'post',
    params: params
  })
}
