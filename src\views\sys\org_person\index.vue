<template>
  <div class="app-container fs-13 flex app-container-row" style="height: 100%;width: 100%;position: relative">
    <el-container class="el-container">
      <el-aside class="el-aside aside-content">
        <el-header height="50px">
          <i class="el-icon-s-platform"></i>
          <span class="orgTitle">组织机构</span>
          <div style="margin-left: 100px;z-index: 99" v-show="hidedept">
            <el-button @click="extractDialog = true">抽取科室组织</el-button>
          </div>
        </el-header>
        <el-input size="mini"
                  placeholder="输入关键字进行过滤"
                  v-model="filterText">
        </el-input>
        <el-tree
          :data="treeData"
          node-key="orgId"
          default-expand-all
          :expand-on-click-node="false"
          class="el-tree-customer"
          :filter-node-method="filterNode"
          empty-text="加载中.."
          @node-click="nodeClick"
          ref="tree">
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span class="text-ellip node-name">{{ data.orgName }}</span>
              <div>
                <el-dropdown trigger="click">
                  <i class="el-icon-circle-plus-outline el-icon--right"></i>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-if="data.teamleadType!='3'">
                      <el-button icon="el-icon-plus" type="text" @click="()=>appendNode(data)">新增</el-button>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button icon="el-icon-edit" type="text" @click="()=>editNode(data)">编辑</el-button>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button class="color-danger" icon="el-icon-delete" type="text"
                                 @click="()=>removeNode(node,data)">删除</el-button>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </span>


        </el-tree>
      </el-aside>
    </el-container>
    <el-container style="position: relative">
      <drg-loading :loading="fullscreenLoading" style="position: absolute;top: 0;left: 0;height: 100%;width: 100%"/>
      <el-header height="50px">
        <el-row>
          <el-col :span="12">
            <div class="grid-content bg-purple-dark flex flex-align-center">
              <el-row class="flex flex-1">
                <el-col :span="18" style="width: 65%;">
                  <div class="flex flex-align-center ">
                    <span class="show-org-title">组织机构</span>
                    <span class="selectOrg flex flex-1 show-selectOrg-name">{{ selectOrg }}</span>
                  </div>
                </el-col>
                <el-col :span="5" :offset="1">
                  <el-checkbox v-model="checkChildOrg">子组织</el-checkbox>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col :span="10">
            <div class="grid-content bg-purple-dark flex flex-align-center">
              <el-input placeholder="请输入内容，支持模糊" size="mini" v-model="searchText" class="input-with-select" style="width: 80%;">
                <el-select v-model="searchType" size="mini" slot="prepend" placeholder="请选择" class="costom-select">
                  <el-option label="账户" value="1"></el-option>
                  <el-option label="昵称" value="2"></el-option>
                </el-select>
                <el-button slot="append" size="mini" icon="el-icon-search" type="primary"
                           @click="queryUserList"></el-button>
              </el-input>
            </div>
          </el-col>

          <el-col :span="2">
            <div class="grid-content bg-purple-dark flex flex-align-center" style="margin-left: -1.2cm;">
              <el-button size="mini" type="primary"  @click="addUser" >新增</el-button>
              <el-button size="mini" type="primary"  @click="exportExcel" style="background-color: green; border-color: green; color: white;">导出</el-button>
              <el-button size="mini" type="primary"  @click="extractDeptAndDoctoer" style="background-color: green; border-color: green; color: white;">抽取</el-button>
            </div>
          </el-col>
        </el-row>
      </el-header>
      <el-main class="flex flex-1 flex-col main-style">

        <el-table :data="tableData" class="grid-list" ref="userListTable" id = 'orgTable'>
          <el-table-column
            type="selection"
            width="55" prop="id">
          </el-table-column>
          <el-table-column prop="nknm" label="姓名" width="140">
          </el-table-column>
          <el-table-column prop="username" label="登录账户" align="center" width="120">
          </el-table-column>
          <el-table-column prop="blngOrgOrgName" label="所属组织" align="center" :show-overflow-tooltip="true">
          </el-table-column>
          <el-table-column prop="roleNames" label="角色" width="150" align="center" :show-overflow-tooltip="true">
          </el-table-column>
          <el-table-column prop="isLckUser" label="锁定" width="100" type="" collection="LOCK_TYPE"
                           :formatter="(col,row)=>dictFormatter(row,col,'LOCK_TYPE')">
          </el-table-column>
          <el-table-column prop="do" label="操作" width="100" align="center">
            <!--            <template slot="header" >-->
            <!--              <el-button size="mini" type="primary" icon="el-icon-plus" @click="addUser">新增用户</el-button>-->
            <!--            </template>-->
            <template slot-scope="scope">
              <el-dropdown trigger="click">
                <el-button size="mini" type="primary" icon="el-icon-arrow-down" circle></el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>
                    <el-button icon="el-icon-edit" type="text" @click="editUser(scope.$index, scope.row)">编辑
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button icon="el-icon-open" type="text" @click="()=>unLockUser(scope.$index, scope.row)">解锁
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button icon="el-icon-refresh" type="text" @click="()=>resetUserPw(scope.$index, scope.row)">
                      密码重置
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button class="color-danger" icon="el-icon-delete" type="text"
                               @click="()=>deleteUser(scope.$index, scope.row)">删除
                    </el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <el-row>
          <el-col :span="12">
            <div class="batch-operate-container">
              <!--<el-select-->
              <!--size="small"-->
              <!--v-model="operateType" placeholder="批量操作">-->
              <!--<el-option-->
              <!--v-for="item in operates"-->
              <!--:key="item.value"-->
              <!--:label="item.label"-->
              <!--:value="item.value">-->
              <!--</el-option>-->
              <!--</el-select>-->
              <!-- <el-button
                style="margin-left: 20px"
                class="search-button"
                @click="handleBatchOperate()"
                type="primary"
                size="small"> -->
              <!--确定-->
              <!-- </el-button> -->
            </div>
          </el-col>
          <el-col class="pageHeder-text-align">
            <div class="pagination-container">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :page-sizes="[50,100,200,1000,2000,5000,10000]"
                :current-page.sync="listQuery.pageNum"
                :page-size="listQuery.pageSize"
                layout="total,sizes, prev, pager, next, jumper"
                :total="listQuery.total" class="pagination-style">
              </el-pagination>
            </div>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
    <!--新增组织机构-->
    <el-dialog title="新增组织机构" :visible.sync="dialogFormVisible">
      <el-form :model="form" label-position="left" :label-width="formLabelWidth">
        <el-form-item label="父级机构id" v-show="false">
          <el-input v-model="form.prntDeptId" autocomplete="off" disabled size="small"></el-input>
        </el-form-item>
        <el-form-item label="父级机构">
          <el-input v-model="form.deptNamePath" autocomplete="off" disabled size="small"></el-input>
        </el-form-item>
        <el-form-item label="机构Id层级" v-show="false">
          <el-input v-model="form.deptIdPath" autocomplete="off" disabled size="small"></el-input>
        </el-form-item>
        <el-form-item label="组织名称" v-show="orgShow">
          <el-input v-model="form.orgName" autocomplete="off" size="small"></el-input>
        </el-form-item>
        <el-form-item label="科室选择" v-show="deptShow">
          <el-col :span=10>
            <select-tree v-model="b16c" :options="depts" width="200" :props="defaultProps"
                         placeholder="请选择院内科室"/>
          </el-col>
        </el-form-item>
        <el-form-item label="科室编号（唯一）" v-show="deptShow">
          <el-input v-model="form.orgId" autocomplete="off" size="small" :disabled="operatorType=='edit'"></el-input>
        </el-form-item>
        <el-form-item label="医护人员" v-show="doctorShow">
          <el-col :span=10>
            <el-select v-model="form.orgId" placeholder="请选择医护人员" @change="changeSelectDoctor" clearable>
              <el-option
                v-for="item in doctorNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 12px">{{ item.value }}</span>
              </el-option>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="医生姓名" v-show="doctorShow">
          <el-input v-model="form.orgName" autocomplete="off" size="small"></el-input>
        </el-form-item>
        <el-form-item label="组织层级" v-show="false">
          <el-input v-model="form.deptHery" autocomplete="off" disabled size="small"></el-input>
        </el-form-item>
        <el-form-item label="邮政编码" v-show="postCode">
          <el-input v-model="form.yzbm" size="small"></el-input>
        </el-form-item>
        <el-form-item label="机构类型">
          <el-col :span=10>
            <el-select class="org-type-style" v-model="form.teamleadType" placeholder="请选择机构类型" size="small">
              <el-option
                v-for="item in dictVoList.ORG_TYPE"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span=10>
            <el-form-item label="排序">
              <el-input-number v-model="form.argtSeq" :min="1" size="small"></el-input-number>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="机构负责人">
          <el-input v-model="form.deptResper" autocomplete="off" placeholder="可以为空" size="small"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="()=>submitAppend()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 抽取组织机构 -->
    <el-dialog width="40%" title="抽取组织架构" :visible.sync="extractDialog">
      <el-radio-group v-model="extractRadio">
        <el-radio :label="1">用户名生成医生账号</el-radio>
        <el-radio :label="2">员工编号生成医生账号</el-radio>
      </el-radio-group>

      <div slot="footer" class="dialog-footer">
        <el-button @click="extractDialog = false">取 消</el-button>
        <el-button type="primary" @click="deptExtract">确 定</el-button>
      </div>
    </el-dialog>

    <!--用户信息-->
    <el-drawer
      :title="drawerTitle"
      :visible.sync="dialogUserFormVisible"
      :direction="direction"
      custom-class="custom-el-drawer"
      ref="drawer">
      <div class="user-drawer__content">
        <el-form :model="userForm" label-position="left" :label-width="formLabelWidth">
          <el-form-item label="所属机构id" v-show="false">
            <el-input v-model="userForm.blngOrgOrgId" autocomplete="off" disabled size="small"></el-input>
          </el-form-item>
          <el-form-item label="所属机构名称">
            <el-input v-model="userForm.blngOrgOrgName" autocomplete="off" disabled size="small"></el-input>
          </el-form-item>
          <el-form-item label="医疗机构">
            <el-select v-model="userForm.hospitalId" @change="changeHospital" placeholder="请选择医疗机构" size="small">
              <el-option
                v-for="(item, index) in hospitalList"
                :key="index"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户id" v-show="false">
            <el-input v-model="userForm.id" autocomplete="off" disabled size="small"></el-input>
          </el-form-item>
          <el-form-item label="用户名">
            <el-input v-model="userForm.nknm" autocomplete="off" size="small" :disabled="userForm.resetPw"></el-input>
          </el-form-item>
          <el-form-item label="权限">
            <el-radio-group v-model="userForm.roleAuth" @change="changeAuth">
              <el-radio :label="1">院级</el-radio>
              <el-radio :label="2">科室级</el-radio>
              <el-radio :label="3">医生/护士级</el-radio>
              <el-radio :label="4">医疗集团级</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="科室" v-if="showEditDept">
            <drg-department v-model="userForm.deptCode" placeholder="请选择科室" width="100%" @changeDept="changeDept"/>
          </el-form-item>
          <el-form-item label="登录账户">
            <el-input v-model="userForm.username" autocomplete="off" size="small"
                      :disabled="userForm.isEdit||userForm.resetPw"></el-input>
          </el-form-item>
          <el-form-item label="登录密码" v-if="userForm.isEdit != true || userForm.resetPw == true">
            <el-input v-model="userForm.password" autocomplete="off" size="small" :type="pwdType">
               <span slot="suffix" @click="showPwd">
                  <svg-icon icon-class="eye" class="color-main"></svg-icon>
                </span>
            </el-input>
          </el-form-item>
          <el-form-item label="登录密码" v-if="userForm.resetPw == true">
            <el-input v-model="userForm.checkPassword" autocomplete="off" size="small" :type="pwdType">
               <span slot="suffix" @click="showPwd">
                  <svg-icon icon-class="eye" class="color-main"></svg-icon>
                </span>
            </el-input>
          </el-form-item>
          <!--          <el-form-item label="角色" prop="userRoles" v-if="userForm.isEdit == true">-->
          <el-form-item label="角色" prop="userRoles" v-if="roleRefresh">
            <el-select v-model="userForm.userRoles" multiple placeholder="请选择" class="el-select-multiple">
              <el-option v-for="item in roles" :key="item.id"
                         :label="item.memo_info" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="排序">
            <el-input-number v-model="userForm.argtSeq" :min="1" size="small"></el-input-number>
          </el-form-item>
        </el-form>
        <div class="user-drawer__footer">
          <el-button @click="cancelForm">取 消</el-button>
          <el-button type="primary" @click="handleClose" :loading="loading">{{
              loading ? '提交中 ...' : '提 交'
            }}
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  queryTreeNode, addOrg, removeOrg, editOrg, queryUserByOrgId
  , registerRequest, editUserRequest, resetPassword, extractDept,     extractDeptAndDoctoerByView
} from '@/api/orgManamement'
import {formaterDict} from '@/utils/dict'
import {findAll} from '@/api/role'
import { elExportExcel } from '@/utils/exportExcel'
import SelectTree from '@/components/SelectTree/index'
import {
  querySelectTreeAndSelectList,
  queryLikeDipGroupByPram,
  queryMedicalDoctorSelectInput,
  queryHospitalId
} from '@/api/common/drgCommon'

let defaultUserForm = {
  blngOrgOrgId: '',
  blngOrgOrgName: '',
  id: '',
  username: '',
  nknm: '',
  password: '',
  argtSeq: 255,
  isEdit: false,
  checkPassword: '',
  userRoles: [],
  isLckUser: '',
  hospitalId: '',
  roleAuth: '',
  deptCode: ''
}
const defaultListQuery = {
  pageNum: 1,
  pageSize: 20,
  total: 0
}
export default {
  name: 'org_person',
  components: {SelectTree},
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    b16c: function () {
      if (this.b16c) {
        this.form.orgId = this.b16c
      } else {
        this.form.orgId = null
      }
    }
  },
  created() {
    this.initQuery()
  },
  data() {
    let form = {
      orgId: '',
      prntDeptId: '',
      orgName: '',
      teamleadType: '',
      argtSeq: 255,
      deptHery: 1,
      deptResper: '',
      deptIdPath: '',
      deptNamePath: '',
      b16c: '',
      yzbm: ''
    }
    return {
      name: null,
      b16c: null,
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listQuery: Object.assign({}, defaultListQuery),
      treeData: [],
      filterText: '',
      searchText: '',
      searchType: '1',
      selectOrg: '所有组织',
      checkChildOrg: true,
      tableData: [],
      dialogFormVisible: false,
      dialogUserFormVisible: false,
      form: form,
      userForm: Object.assign({}, defaultUserForm),
      formLabelWidth: '120px',
      currNode: {},
      operatorType: 'add',
      operateType: '',
      operates: [],
      currMainOrgId: '',
      currMainOrgName: '',
      direction: 'rtl',
      loading: false,
      fullscreenLoading: false,
      drawerTitle: '',
      roles: [],
      pwdType: 'password',
      orgShow: false,
      deptShow: false,
      doctorShow: false,
      postCode: false,
      hidedept: false,
      doctorNameList: null, // 医护人员姓名下拉
      tableHeight: 0,
      hospitalList: {},
      extractDialog: false,
      extractRadio: 1,
      roleRefresh: true,
      showEditDept: false
    }
  },
  // 动态调整表格高度
  mounted: function () {
    // this.$nextTick(function () {
    //   this.tableHeight = window.innerHeight - this.$refs.userListTable.$el.offsetTop ;
    //   // 监听窗口大小变化
    //   let self = this;
    //   window.onresize = function() {
    //     self.tableHeight = window.innerHeight - self.$refs.userListTable.$el.offsetTop ;
    //   }
    // });
  },
  methods: {
    initQuery() {
      this.getOrgTree()
      // 查询所有用户
      // this.queryAllUserList();
      // 加载用户角色列表
      this.findUserRoles()
      this.findSelectTreeAndSelectList()
      this.getDoctor()
      this.getHospitalId()
    },
    getHospitalId() {
      queryHospitalId().then(res => {
        this.hospitalList = res.data
      })
    },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    clickdept() {
      if (this.currMainOrgName.includes('医院')) {
        this.hidedept = true
      } else {
        this.hidedept = false
      }
    },
    // 根据科室编号获取医生
    getDoctor(b16c) {
      const params = {b16c: b16c}
      queryMedicalDoctorSelectInput(params).then((response) => {
        this.doctorNameList = response.data
      })
    },
    deptExtract() {
      let param = {orgId: this.currMainOrgId, type: this.extractRadio}
      this.fullscreenLoading = true
      extractDept(param).then((response) => {
        if (response.code == 200) {
          this.$message({
            type: 'success',
            message: '抽取成功'
          })
        }
        this.fullscreenLoading = false
        this.initQuery()
      }).catch(() => {
        this.fullscreenLoading = false
      })
    },
    // 新增组织
    appendNode(data) {
      this.currNode = data
      this.cleanForm()
      // 赋值父级相关值
      this.form.deptNamePath = data.deptNamePath
      this.form.prntDeptId = data.orgId
      this.form.deptIdPath = data.deptIdPath
      this.form.deptHery = Number(data.deptHery) + 1
      this.form.argtSeq = Number(data.argtSeq) + 1
      this.dialogFormVisible = true
      this.operatorType = 'add'
      switch (data.teamleadType) {
        case '1':
          if (data.orgName.indexOf('医院') != -1) {
            this.orgShow = false;
            this.deptShow = true;
            this.doctorShow = false;
            this.postCode = false
            this.form.teamleadType = '2'
          } else {
            this.orgShow = true;
            this.deptShow = false;
            this.doctorShow = false;
            this.postCode = true
            this.form.teamleadType = '1'
          }
          break
        case '2':
          this.orgShow = false;
          this.deptShow = false;
          this.doctorShow = true;
          this.postCode = false
          this.getDoctor(data.orgId)
          this.form.teamleadType = '3'
          break
      }
    },
    // 编辑组织
    editNode(data) {
      this.currNode = data
      this.setForm(data)
      this.operatorType = 'edit'
      this.dialogFormVisible = true
      switch (data.teamleadType) {
        case '1':
          this.orgShow = true;
          this.deptShow = false;
          this.doctorShow = false;
          break
        case '2':
          this.orgShow = false;
          this.deptShow = true;
          this.doctorShow = false;
          this.getDoctor(data.orgId);
          break
        case '3':
          this.orgShow = false;
          this.deptShow = false;
          this.doctorShow = true;
          break
      }
    },
    // 删除组织
    removeNode(node, data) {
      this.$confirm('此操作将永久删除该组织, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.setForm(data)
        let param = this.form
        removeOrg(param).then(response => {
          const parent = node.parent
          const children = parent.data.children || parent.data
          const index = children.findIndex(d => d.orgId === data.orgId)
          children.splice(index, 1)
          this.$message({
            type: 'success',
            message: '删除成功'
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    submitAppend() {
      if (!this.form.orgId && this.currMainOrgName.includes('医院')) {
        let msgObj = {
          type: 'warning',
          message: '科室不能为空'
        }
        // 提示消息
        this.$message(msgObj)
        return
      }
      switch (this.operatorType) {
        case 'add':
          if (this.currNode) {
            // 提交组织机构 后续新增前置验证
            let param = this.form
            addOrg(param).then(response => {
              let nodeChlid = response.data
              let msgObj = {
                type: 'success',
                message: '新增成功'
              }
              // 提示消息
              this.$message(msgObj)
              this.addNodeElment(nodeChlid)
            })
          }
          break
        case 'edit':
          if (this.currNode) {
            let oldNamePath = this.form.deptNamePath
            let newNamePath = oldNamePath.substring(0, oldNamePath.lastIndexOf('/') + 1) + this.form.orgName
            this.form.deptNamePath = newNamePath
            let params = this.form
            editOrg(params).then(response => {
              this.currNode.orgName = this.form.orgName
              this.currNode.teamleadType = this.form.teamleadType
              this.currNode.argtSeq = this.form.argtSeq
              this.currNode.deptNamePath = this.form.deptNamePath
              this.selectOrg = newNamePath
              let msgObj = {
                type: 'success',
                message: '编辑成功'
              }
              // 提示消息
              this.$message(msgObj)
            })
          }
          break
        default:
          break
      }
      this.dialogFormVisible = false
    },
    // 过滤节点函数
    filterNode(value, data) {
      if (!value) return true
      return data.orgName.indexOf(value) !== -1
    },
    // 获取组织架构数据，把码表一起查询出来
    getOrgTree() {
      queryTreeNode({codeKeys: 'ORG_TYPE,LOCK_TYPE'}).then(response => {
        this.treeData = response.data
        this.dictVoList = response.dictVoList
      })
    },
    cleanForm() {
      this.form = {
        orgId: '',
        prntDeptId: '',
        orgName: '',
        teamleadType: '',
        argtSeq: 1,
        deptHery: 1,
        deptResper: '',
        deptIdPath: '',
        deptNamePath: ''
      }
    },
    // 新增tree 节点
    addNodeElment(nodeData) {
      if (!this.currNode.children) {
        this.$set(this.currNode, 'children', [])
      }
      this.currNode.children.push(nodeData)
    },
    // 设置form
    setForm(data) {
      this.form.orgId = data.orgId
      this.form.prntDeptId = data.prntDeptId
      this.form.orgName = data.orgName
      this.form.teamleadType = data.teamleadType
      this.form.argtSeq = data.argtSeq
      this.form.deptHery = data.deptHery
      this.form.deptResper = data.deptResper
      this.form.deptIdPath = data.deptIdPath
      this.form.deptNamePath = data.deptNamePath
    },
    // 点击node节点
    nodeClick(data) {
      this.selectOrg = data.deptNamePath
      this.currMainOrgId = data.orgId
      this.currMainOrgName = data.orgName
      this.queryUserList()
      this.clickdept()
    },
    queryAllUserList() {
      let param = {
        blngOrgOrgId: this.currMainOrgId, // 默认查询第一个组织架构下的所有用户
        hasSubOrg: this.checkChildOrg === true ? 1 : 0
      }
      // 组装搜索参数
      if (this.searchText) {
        let key = this.searchType == '1' ? 'username' : 'nknm'
        param[key] = this.searchText
      }
      queryUserByOrgId(param).then(response => {
        let data = response.data
        this.tableData = data.list
        this.listQuery = {
          pageNum: data.pageNum,
          pageSize: data.pageSize,
          total: data.total
        }
      })
    },
    queryUserList() {
      // 同时查询对应的组织下的用户
      if (!this.currMainOrgId) {
        let msgObj = {
          type: 'warning',
          message: '请选择机构'
        }
        // 提示消息
        this.$message(msgObj)
        return
      }
      let param = {
        blngOrgOrgId: this.currMainOrgId,
        hasSubOrg: this.checkChildOrg === true ? 1 : 0,
        pageNum: this.listQuery.pageNum,
        pageSize: this.listQuery.pageSize
      }
      // 组装搜索参数
      if (this.searchText) {
        let key = this.searchType == '1' ? 'username' : 'nknm'
        param[key] = this.searchText
      }
      queryUserByOrgId(param).then(response => {
        let data = response.data
        this.tableData = data.list
        this.listQuery = {
          pageNum: data.pageNum,
          pageSize: data.pageSize,
          total: data.total
        }
      })
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = this.listQuery.pageNum
      this.listQuery.pageSize = val
      this.queryUserList()
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val
      this.queryUserList()
    },
    /**
     * 码表渲染方法
     */
    dictFormatter(col, row, dic_type) {
      return formaterDict(row[col.property], this.dictVoList[dic_type])
    },
    addUser() {
      // if (!this.currMainOrgId) {
      //   let msgObj = {
      //     type: 'warning',
      //     message: '请选中左侧组织机构'
      //   };
      //   //提示消息
      //   this.$message(msgObj);
      //   return;
      // }
      this.userForm = Object.assign({}, defaultUserForm)
      this.userForm.isEdit = false
      this.drawerTitle = '新增用户'
      this.dialogUserFormVisible = true
      this.userForm.blngOrgOrgId = this.currMainOrgId
      this.userForm.blngOrgOrgName = this.currMainOrgName
      // this.userForm.nknm = this.currMainOrgName;
    },
    editUser(index, row) {
      this.dialogUserFormVisible = true
      this.drawerTitle = '编辑用户'
      this.userForm.blngOrgOrgId = row.blngOrgOrgId
      this.userForm.blngOrgOrgName = row.blngOrgOrgName
      this.userForm.id = row.id
      this.userForm.username = row.username
      this.userForm.nknm = row.nknm
      this.userForm.password = row.password
      this.userForm.argtSeq = row.argtSeq
      this.userForm.isEdit = true
      this.userForm.isLckUser = row.isLckUser
      this.userForm.hospitalId = row.hospitalId
      this.userForm.deptCode = row.blngOrgOrgId
      let userRolesSelect = []
      let flag = false
      for (let i = 0; i < row.userRoles.length; i++) {
        if (!flag) {
          if ([3].includes(row.userRoles[i].roleId)) {
            this.userForm.roleAuth = 1
            flag = true
          } else if ([10].includes(row.userRoles[i].roleId)) {
            this.userForm.roleAuth = 2
            flag = true
          } else if ([26].includes(row.userRoles[i].roleId)) {
            this.userForm.roleAuth = 3
            flag = true
          }
        }
        userRolesSelect.push(row.userRoles[i].roleId)
      }
      this.userForm.userRoles = userRolesSelect
    },
    unLockUser(index, row) {
      if (row.isLckUser == 0) {
        return
      }// 无锁，直接返回
      let param = {
        id: row.id,
        isLckUser: 0// 解锁
      }
      editUserRequest(param.id, param).then(response => {
        let msgObj = {
          type: 'success',
          message: '解锁成功'
        }
        this.$message(msgObj)
        this.cleanUserForm()
        this.queryUserList()
      }).catch(_ => {
      })
    },
    resetUserPw(index, row) {
      this.dialogUserFormVisible = true
      this.drawerTitle = '重置密码'
      this.userForm.blngOrgOrgId = row.blngOrgOrgId
      this.userForm.blngOrgOrgName = row.blngOrgOrgName
      this.userForm.id = row.id
      this.userForm.username = row.username
      this.userForm.nknm = row.nknm
      this.userForm.password = '123456'
      this.userForm.checkPassword = '123456'
      this.userForm.hospitalId = this.hospitalList[0].value
      this.userForm.userRoles = []
      for (let i = 0; i < row.userRoles.length; i++) {
        this.userForm.userRoles.push(row.userRoles[i].roleId)
      }
      this.userForm.argtSeq = row.argtSeq
      this.userForm.resetPw = true
    },
    deleteUser(index, row) {
      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let param = {
          id: row.id,
          status: 0,
          isLckUser: row.isLckUser
        }
        editUserRequest(param.id, param).then(response => {
          this.$message({
            type: 'success',
            message: '删除成功'
          })
          this.cleanUserForm()
          this.queryUserList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleClose(done) {
      if (this.loading) {
        return
      }
      this.$confirm('确定提交吗？')
        .then(_ => {
          this.loading = true
          let msgObj = {
            type: 'success',
            message: '新增成功'
          }
          if (this.userForm.isEdit) {
            let msgObj0 = {
              type: 'error',
              message: '用户至少分配基础权限'
            }
            if (!this.userForm.userRoles) {
              this.$message(msgObj0)
              this.loading = false
              return
            }
            let flag = 0
            for (let i = 0; i < this.userForm.userRoles.length; i++) {
              if (this.userForm.userRoles[i] == 9) {
                flag = 1
              }
            }
            if (flag == 0) {
              this.$message(msgObj0)
              this.loading = false
              return
            }
            let params = Object.assign({}, {
              id: this.userForm.id,
              nknm: this.userForm.nknm,
              argtSeq: this.userForm.argtSeq,
              userRoles: this.userForm.userRoles,
              isLckUser: this.userForm.isLckUser,
              blngOrgOrgId: this.userForm.blngOrgOrgId

            })
            let userRoles = []
            for (let i = 0, len = params.userRoles.length; i < len; i++) {
              let userRole = {
                userId: params.id,
                roleId: params.userRoles[i],
                isLckUser: params.isLckUser,
                blngOrgOrgId: params.blngOrgOrgId
              }
              userRoles.push(userRole)
            }
            params['userRoles'] = userRoles
            editUserRequest(params.id, params).then(response => {
              this.loading = false
              msgObj.message = '编辑成功'
              this.dialogUserFormVisible = false
              this.$message(msgObj)
              this.cleanUserForm()
              this.queryUserList()
            }).catch(_ => {
              this.loading = false
            })
          } else if (this.userForm.resetPw) {
            // 校验两次密码输入
            if (this.userForm.checkPassword !== this.userForm.password) {
              let msgObj = {
                type: 'warning',
                message: '两次密码不一致'
              }
              this.$message(msgObj)
              return
            }
            let param = {
              id: this.userForm.id,
              username: this.userForm.checkPassword,
              password: this.userForm.password,
              checkPassword: this.userForm.checkPassword
            }
            resetPassword(param.id, param).then(response => {
              this.loading = false
              msgObj.message = '重置成功'
              this.dialogUserFormVisible = false
              this.$message(msgObj)
              this.cleanUserForm()
              this.queryUserList()
            }).catch(_ => {
              this.loading = false
            })
          } else {
            registerRequest(this.userForm).then(response => {
              this.loading = false
              msgObj.message = '新增成功'
              this.dialogUserFormVisible = false
              this.$message(msgObj)
              this.cleanUserForm()
              if (this.selectOrg) {
                this.queryUserList()
              }
            }).catch(_ => {
              this.loading = false
            })
          }
        }).catch(_ => {
      })
    },
    cancelForm() {
      this.loading = false
      this.dialogUserFormVisible = false
      this.cleanUserForm()
    },
    cleanUserForm() {
      let userForm = {
        blngOrgOrgId: '',
        id: '',
        username: '',
        nknm: '',
        password: '',
        argtSeq: 255,
        isEdit: false,
        checkPassword: '',
        userRoles: []
      }
      this.userForm = userForm
    },
    // 加载用户角色信息
    findUserRoles() {
      findAll().then((res) => {
        // 加载角色集合
        this.roles = res.data
      })
    },
    showPwd() {
      if (this.pwdType === 'password') {
        this.pwdType = ''
      } else {
        this.pwdType = 'password'
      }
    },
    changeSelectDoctor(value) {
      let obj = {}
      obj = this.doctorNameList.find(item => {
        return item.value === value
      })
      this.form.orgName = obj.label
      this.form.deptResper = obj.label
    },
    changeAuth(val) {
      this.roleRefresh = false
      let tempRoles = []
      if (this.userForm.userRoles) {
        tempRoles = this.userForm.userRoles
      }
      this.userForm.userRoles = []
      if (tempRoles.length > 0) {
        for (let i = 0; i < tempRoles.length; i++) {
          if (![3, 10, 26].includes(tempRoles[i])) {
            this.userForm.userRoles.push(tempRoles[i])
          }
        }
      }
      if (this.drawerTitle == '新增用户') {
        if (this.userForm.userRoles && this.userForm.userRoles.includes(9)) {

        } else {
          this.userForm.userRoles.push(9)
        }
      }
      this.showEditDept = false
      if (val === 1) {
        this.userForm.userRoles.push(3)
      }
      if (val === 2) {
        this.userForm.userRoles.push(10)
        this.showEditDept = true
      }
      if (val === 3) {
        this.userForm.userRoles.push(26)
        this.showEditDept = true
      }
      if (val === 4) {
        this.userForm.userRoles.push(29)
        this.showEditDept = true
      }
      this.roleRefresh = true
    },
    changeDept() {
      if (this.userForm.roleAuth !== 1) {
        let tempDept = this.$store.getters.getAllDepartments.find(dept => dept.code === this.userForm.deptCode)
        this.userForm.blngOrgOrgId = tempDept.code
        this.userForm.blngOrgOrgName = tempDept.name
      }
    },
    changeHospital() {
      if (this.userForm.roleAuth === 1) {
        let tempHospital = this.hospitalList.find(hospital => hospital.value === this.userForm.hospitalId)
        this.userForm.blngOrgOrgId = tempHospital.value
        this.userForm.blngOrgOrgName = tempHospital.label
      }
    },

    exportExcel () {
      let tableId = 'orgTable'
      let fileName = '组织架构'
      elExportExcel(tableId, fileName)
    },
    extractDeptAndDoctoer(){
      extractDeptAndDoctoerByView()
    }

  }
}
</script>

<style scoped>
.app-container {
  height: 680px;
}

.el-header {
  background-color: #fafafa;
  color: #333;
  padding: 10px;
}

.el-aside {
  color: #333;
  width: 200px;
  border-right: 1px solid #eeeeee;
}

/*.el-container{
  height: 500px; border: 1px solid #eee;
}*/
.el-dropdown.mg-l2 {
  margin-left: 5px;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-dropdown-link.el-icon--right:before {
  margin-left: 5px;
}

.el-icon-setting {
  font-size: 12px;
  padding-left: 10px;
}

.el-tree-customer {
  color: #333;
}

.el-icon-s-platform {
  padding-right: 5px;
}

.aside-content .el-header {
  color: #409EFF;
  display: flex;
  align-items: center;
}

.aside-content .el-input {
  padding: 10px;
}

.aside-content .el-input__inner {
  height: 30px;
}

.el-select .el-input {
  width: 90px;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

.selectOrg.show-selectOrg-name {
  margin-left: 3px;
  border-bottom: 1px solid #999;
  overflow-y: auto;
  height: 18px;
}

.grid-content {
  height: 40px;
}

.input-with-select .el-button:hover {
  background-color: #f2f6fc;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.grid-list {
  font-size: 13px;
}

.el-button-coustme {
  width: 60px;
}

.el-select.org-type-style .el-input {
  width: 100%;
}

.show-org-title {
  width: 64px;
}

.custom-tree-node {
  display: flex;
  align-items: center;
}

.custom-tree-node .node-name {
  max-width: 150px;
}

.app-container.app-container-row {
  flex-direction: row;
}

.user-drawer__content {
  padding: 0 20px;
}

.user-drawer__footer {
  display: flex;
}

.user-drawer__footer .el-button {
  flex: 1 auto;
}

.user-drawer__content .el-select {
  width: 100%;
}

.el-select.el-select-multiple .el-input {
  width: 100%;
}

.el-select.costom-select {
  width: 80px;
}

.pageHeder-text-align {
  text-align: right;
}

.custom-el-drawer #el-drawer__title > span {
  outline: none;
}

.orgTitle {
  font-size: 14px;
}

.el-dialog__body {
  padding: 0px 40px;
}

.el-main {
  padding: 5px 10px 0px 10px;
}

/deep/ .el-table th {
  padding: 5px 0;
}

/deep/ .el-table td {
  padding: 5px 0;
}

/* .el-table{
  overflow: hidden;
} */
.pagination-style {
  padding: 14px 5px;
}

.main-style {
  height: 100%;
}

/deep/ .el-table__body-wrapper {
  height: 86%;
  overflow-y: auto;
}
</style>
