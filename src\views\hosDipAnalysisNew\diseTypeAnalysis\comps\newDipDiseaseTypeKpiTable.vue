<template>
  <div class="analysis-wrapper">
    <div class="analysis-wrapper-left">
      <el-table ref="elTable"
                :id="id"
                height="100%"
                stripe
                :header-cell-style="{'text-align':'center'}"
                :data="data"
                v-loading="loading"
                border>
        <el-table-column label="序号" type="index" width="50" fixed="left" align="center" />
        <el-table-column label="病种名称" prop="icdName" :show-overflow-tooltip="true"  width="150" fixed/>
        <el-table-column label="病种编码" prop="icdCodg" :show-overflow-tooltip="true"  fixed />
        <el-table-column label="辅助目录-年龄" prop="asstListAgeGrp" :show-overflow-tooltip="true"  width="110" fixed />
        <el-table-column label="辅助目录-疾病" prop="asstListDiseSevDeg" :show-overflow-tooltip="true"  width="110" fixed />
        <el-table-column label="辅助目录-肿瘤" prop="asstListTmorSevDeg" :show-overflow-tooltip="true"  width="110" fixed />
        <el-table-column label="入组病案数" prop="drgInGroupMedcasVal" width="110" sortable align="right" :fixed="include('drgInGroupMedcasVal')">
          <template slot-scope="scope">
            <div :class="scope.row.drgInGroupMedcasVal == 0 ? '' : 'skip'" @click="scope.row.drgInGroupMedcasVal == 0 ? '' : queryGroupPatient(scope.row)">
              {{ scope.row.drgInGroupMedcasVal }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="平均住院费用" prop="avgInHosCost" width="125" sortable align="right" :fixed="include('avgInHosCost')" />
        <el-table-column label="平均住院费用(反馈)" prop="fbAvgInHosCost" width="160" sortable align="right" :fixed="include('fbAvgInHosCost')" v-if="queryForm.feeStas == 1" />
        <el-table-column label="标杆住院费用" prop="standardInHosCost" width="100" align="right" :fixed="include('standardInHosCost')" />
        <el-table-column label="平均住院天数" prop="avgInHosDays" width="135" sortable align="right" :fixed="include('avgInHosDays')" />
        <el-table-column label="标杆住院天数" prop="standardInHosDays" width="100" align="right" :fixed="include('standardInHosDays')" />
        <el-table-column label="药占比" prop="drugRatio" width="95" sortable align="right" :fixed="include('drugRatio')">
          <template slot-scope="scope">
            {{ $somms.addPercent(scope.row.drugRatio) }}
          </template>
        </el-table-column>
        <el-table-column label="标杆药占比" prop="standardDrugRatio" width="90" align="right" :fixed="include('standardDrugRatio')">
          <template slot-scope="scope">
            {{ $somms.addPercent(scope.row.standardDrugRatio) }}
          </template>
        </el-table-column>
        <el-table-column label="耗占比" prop="consumeRatio" width="95" sortable align="right" :fixed="include('consumeRatio')">
          <template slot-scope="scope">
            {{ $somms.addPercent(scope.row.consumeRatio) }}
          </template>
        </el-table-column>
        <el-table-column label="标杆耗占比" prop="standardConsumeRatio" width="90" align="right" :fixed="include('standardConsumeRatio')">
          <template slot-scope="scope">
            {{ $somms.addPercent(scope.row.standardConsumeRatio) }}
          </template>
        </el-table-column>
        <el-table-column label="时间消耗指数" prop="timeIndex" width="125" sortable align="right" :fixed="include('timeIndex')" />
        <el-table-column label="费用消耗指数" prop="costIndex" width="125" sortable align="right" :fixed="include('costIndex')" />
        <el-table-column label="悬浮"  align="center" >
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="showSuspension(scope.row)">悬浮</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>
export default {
  name: 'newDipDiseaseTypeKpiTable',
  components: {

  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    fixedColumns: {
      type: Array,
      default: () => []
    },
    id: {
      type: String
    },
    queryForm: {
      type: Object
    },
    columnOptions: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    rightAnalysisData: {},
    scopeData: {}
  }),
  mounted () {

  },
  methods: {
    include (column) {
      return this.fixedColumns.includes(column)
    },
    showSuspension (scope) {
      this.scopeData = scope
      let scopeList = []
      for (let i in this.scopeData) {
        let o = {}
        o.key = i
        o.value = this.scopeData[i]
        scopeList.push(o)
      }
      let res = []
      for (let i = 0; i < scopeList.length; i++) {
        for (let j = 0; j < this.columnOptions.length; j++) {
          if (scopeList[i].key == this.columnOptions[j].value) {
            res.push({
              key: scopeList[i].key,
              label: this.columnOptions[j].label,
              value: scopeList[i].value,
              type: 1,
              show: true
            })
          }
        }
      }
      if (scope) {
        this.$emit('showSuspension', res)
      }
    },
    queryGroupPatient (item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          dipCodg: item.dipCodg,
          icdCodg: item.icdCodg,
          deptCode: this.queryForm.deptCode,
          isInGroup: 1,
          feeStas: this.queryForm.feeStas,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.analysis-wrapper{
  height: 100%;
  width: 100%;
  position: relative;

  &-left{
    width: 100%;
    height: 100%;
  }

  &-analysis{
    width: 100%;
    height: 80%;
    position: relative;
    display: flex;
  }

  &-right{
    width: 20%;
    height: 100%;
    background-color: rgba(131,175,155,.3);
    padding: 1% 1% 0 1%;
    border-radius: 1%;
  }

  &-no-table{
    width: 100%;
    height: 80%;
  }
}
.analysis-head{
  width: 80%;
  height: 20%;

  &-title{
    width: 100%;
    height: 20%;
    font-size: var(--biggerSize);
    font-weight: 600;
    position: relative;

    &-dropdown{
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  &-content{
    width: 100%;
    height: 54%;
    display: flex;

    &-item{
      width: 14%;
      height: 100%;
      cursor: pointer;
      background-color: rgb(64,158,255);
      font-weight: 600;
      margin-right: 1%;
      padding: 1%;
      display: flex;
      align-items: center;
      justify-items: center;
      flex-direction: column;
      border-radius:  16% 0 16% 0 ;

      .title{
        font-size: 14px;
        color: white;
      }

      .value{
        margin-top: 8%;
        font-size: 24px;
        color: white;
      }
    }
  }
}

.analysis-content{
  width: 100%;
  height: 94%;
  position: relative;

  &-summary{
    width: 30%;
    height: 40%;
    border: 1px solid red;
  }
}

.analysis-wrapper-right{
  width: 20%;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0%;
  display: flex;
  justify-content: center;

  &-content{
    width: 100%;
    height: 100%;
  }
}

$titleGray: gray;
$titleSize: 13px;
.analysis-right{

  &-title{
    width: 100%;
    height: 4%;
    font-size: var(--biggerSize);
    font-weight: 600;
  }

  &-subhead{
    width: 100%;
    height: 4%;
    font-size: var(--biggerSmallSize);
    color: gray;
    border-bottom: 1px solid $titleGray;
  }

  &-cost{
    width: 100%;
    height: 62%;
    line-height: 20px;
    overflow-y: auto;
    border-bottom: 1px solid $titleGray;
  }

  &-suggest{
    padding-top: 4%;
    width: 100%;
    height: 30%;
    overflow-y: auto;

    &-title{
      width: 100%;
      font-weight: 600;
      height: 10%;
      font-size: $titleSize;
    }

    &-content{
      width: 100%;
      height: 70%;
      overflow-y: auto;
    }

    &-declaration{
      width: 100%;
      height: 15%;
      color: #fa5d5d;
      display: flex;
      align-items: center
    }

    &-item{
      width: 100%;
      height: 16%;
    }
  }
}
</style>
