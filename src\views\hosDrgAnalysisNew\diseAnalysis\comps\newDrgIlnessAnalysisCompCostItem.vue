<template>
  <div class="cost-wrapper">
    <div class="cost-wrapper-item"
         v-for="(item, index) in items"
         :key="index">
      <div class="title">
        {{ item.name }}
      </div>
      <div class="value">
        当前: <span> {{ item.value }} </span>
        标杆: <span> {{ item.bgValue }} </span>
      </div>
      <div class="children">
        <div class="children-item"
             v-for="(childItem, childIndex) in item.children" :key="childIndex">
          <div class="children-item-title">
            {{ childItem.name }}
          </div>
          <div class="children-item-value">
            <span>当前:  {{ childItem.value }} </span>
            <span>标杆:  {{ childItem.bgValue }} </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    // 数据
    items: {
      type: Array,
      default: () => []
    }
  }

}
</script>

<style scoped lang="scss">
$titleGray: gray;
$titleSize: 13px;
.cost-wrapper{
  height: 100%;
  width: 100%;

  &-item{
    width: 100%;
    margin-top: 10px;

    .title{
      font-size: $titleSize;
      font-weight: 600;
    }

    .value{
      font-size: 12px;
      color: $titleGray;
    }

    .children{
      width: 100%;

      &-item{
        display: inline-block;
        width: 50%;

        &-title{
          font-size: $titleSize;
        }

        &-value{
          color: $titleGray;
          font-size: 12px;
          display: flex;
          flex-direction: column;
        }
      }
    }
  }
}
</style>
