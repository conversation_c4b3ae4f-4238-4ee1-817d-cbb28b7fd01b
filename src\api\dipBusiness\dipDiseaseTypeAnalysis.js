import request from '@/utils/request'
export function queryData (params) {
  return request({
    url: '/NewDipBusinessDiseaseAnalysisTypeController/mainInfoList',
    method: 'post',
    params: params
  })
}

export function getCountInfo (params) {
  return request({
    url: '/dipDiseaseAnalysis/getCountInfo',
    method: 'post',
    params: params
  })
}

export function queryIcd (params) {
  return request({
    url: '/NewDipBusinessDiseaseAnalysisTypeController/queryDiseaseForecastData',
    method: 'post',
    params: params
  })
}
