import request from '@/utils/request'

/**
 * 上传科室文件
 * @param params
 * @returns {*}
 */
export function uploadDeptFile (params) {
  return request({
    url: '/deptFileUploadController/deptUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}

/**
 * 上传数据
 * @param params
 * @returns {*}
 */
export function uploadRecordData (params) {
  return request({
    url: '/MedicalRecordUploadController/medicalRecordUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}

/**
 * 医护人员上传数据
 * @param params
 * @returns {*}
 */
export function workersUpload (params) {
  return request({
    url: '/workersUploadController/workersUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}

/**
 * 病案首页数据上传
 * @param params
 */
export function uploadMedicalPage (params) {
  return request({
    url: '/medicalPageUploadController/medicalPageUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}

/**
 * 病案首页数据上传
 * @param params
 */
export function medicalTypeUpload (params) {
  return request({
    url: '/MedicalRecordUploadController/medicalTypeUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}
