<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             headerTitle="查询条件"
             :contentTitle="contentTitle"
             :totalNum="total"
             show-pagination
             label-width="100px"
             @query="queryData">

      <template slot="extendFormItems">
        <!--        //病组查询-->
        <el-form-item label="病组编号" prop="code" v-if="this.tab == 1">
          <el-select v-model="queryForm.code"
                     filterable
                     placeholder="请输入病组编号"
                     clearable
                     @input="queryData">
            <el-option
              v-for="item in codeOption"
              :key="item.code"
              :label="item.code"
              :value="item.code">
              <span style="float: left">{{ item.code }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="病组名称" prop="name" v-if="this.tab == 1">
          <el-select v-model="queryForm.name"
                     filterable
                     placeholder="请输入病组名称"
                     clearable
                     @input="queryData">
            <el-option
              v-for="item in codeOption"
              :key="item.name"
              :label="item.name"
              :value="item.name">
              <span style="float: left">{{item.code}}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{item.name}}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="操作人" prop="modifier" v-if="this.tab == 1">
          <el-input v-model="queryForm.modifier" placeholder="请输入操作人姓名"  @input="queryData"/>
        </el-form-item>
        <!--    诊断查询    -->
        <el-form-item label="诊断代码" prop="code" v-if="this.tab == 2">
          <el-select v-model="queryForm.code"
                     filterable
                     remote
                     placeholder="请输入诊断代码"
                     clearable
                     :remote-method="filterQuery">
            <el-option
              v-for="item in diagnosisOption"
              :key="item.code"
              :label="item.code"
              :value="item.code">
              <span style="float: left">{{ item.code }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="诊断名称" prop="name" v-if="this.tab == 2">
          <el-select v-model="queryForm.name"
                     filterable
                     remote
                     placeholder="请输入诊断名称"
                     clearable
                     :remote-method="filterQuery">
            <el-option
              v-for="item in diagnosisOption"
              :key="item.name"
              :label="item.name"
              :value="item.name">
              <span style="float: left">{{ item.code }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="操作人" prop="modifier" v-if="this.tab == 2">
          <el-input v-model="queryForm.modifier" placeholder="请输入操作人姓名"  @input="queryData"/>
        </el-form-item>
        <!--   手术查询   -->
        <el-form-item label="手术代码" prop="code" v-if="this.tab == 3">
          <el-select v-model="queryForm.code"
                     filterable
                     placeholder="请输入手术代码"
                     clearable
                     @input="queryData">
            <el-option
              v-for="item in operationOption"
              :key="item.code"
              :label="item.code"
              :value="item.code">
              <span style="float: left">{{ item.code }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.name }}</span>
            </el-option>
            <el-option
              v-if="!isAllLoaded"
              :key="'loading'"
              :label="'加载中...'"
              :value="'loading'"
              disabled
            >
              <i class="el-icon-loading"></i>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="手术名称" prop="name" v-if="this.tab == 3">
          <el-select v-model="queryForm.name"
                     filterable
                     placeholder="请输入手术名称"
                     clearable
                     @input="queryData">
            <el-option
              v-for="item in operationOption"
              :key="item.name"
              :label="item.name"
              :value="item.name">
              <span style="float: left">{{ item.code }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.name }}</span>
            </el-option>
            <el-option
              v-if="!isAllLoaded"
              :key="'loading'"
              :label="'加载中...'"
              :value="'loading'"
              disabled
            >
              <i class="el-icon-loading"></i>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="操作人" prop="modifier" v-if="this.tab === 3">
          <el-input v-model="queryForm.modifier" placeholder="请输入操作人姓名"  @input="queryData"/>
        </el-form-item>
      </template>

      <template slot="buttons">
        <el-button @click="showDialog(null,2)" type="primary" size="mini" class="som-button-margin-right">新增</el-button>
        <el-button type="primary" @click="dialogVisible = true" class="som-button-margin-right"><i class="el-icon-upload el-icon--left"></i>文件上传</el-button>
        <el-button type="primary" @click="downConfigTemplate" class="som-button-margin-right"><i class="el-icon-download el-icon--left"></i>模板下载</el-button>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <el-tabs v-model="tab"  @tab-click="DoctorNurse">
          <el-tab-pane label="病组" name="1"></el-tab-pane>
          <el-tab-pane label="诊断" name="2"></el-tab-pane>
          <el-tab-pane label="手术" name="3"></el-tab-pane>
        </el-tabs>
        <!--  病组      -->
        <el-table
          height="90%"
          :data="tableData"
          style="width: 100%" v-if="tab==1">
          <el-table-column
            type="index"
            width="50"
            label="序号">
          </el-table-column>
          <el-table-column
            prop="code"
            label="病组代码">
          </el-table-column>
          <drg-table-column
            prop="name"
            label="病组名称">
          </drg-table-column>
          <drg-table-column
            prop="modifier"
            label="操作人">
          </drg-table-column>
          <drg-table-column
            prop="modificationTime"
            label="操作时间">
          </drg-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-edit"
                type="primary"
                circle
                @click="showDialog(scope.row,1)"></el-button>
            </template>
          </el-table-column>
          <el-table-column label="删除" align="center" fixed="right">
            <template slot-scope="scope">
              <el-popconfirm style="cursor: pointer;display: block" @confirm="deleteCodeInfo(scope.row)"
                             title="是否删除？">
                <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <!--   诊断     -->
        <el-table
          height="90%"
          :data="tableData"
          style="width: 100%" v-if="tab == 2">
          <el-table-column
            type="index"
            width="50"
            label="序号">
          </el-table-column>
          <el-table-column
            prop="code"
            label="诊断代码">
          </el-table-column>
          <drg-table-column
            prop="name"
            label="诊断名称">
          </drg-table-column>
          <drg-table-column
            prop="modifier"
            label="操作人">
          </drg-table-column>
          <drg-table-column
            prop="modificationTime"
            label="操作时间">
          </drg-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-edit"
                type="primary"
                circle
                @click="showDialog(scope.row,1)"></el-button>
            </template>
          </el-table-column>
          <el-table-column label="删除" align="center" fixed="right">
            <template slot-scope="scope">
              <el-popconfirm style="cursor: pointer;display: block" @confirm="deleteCodeInfo(scope.row)"
                             title="是否删除？">
                <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <!-- 手术  -->
        <el-table
          height="90%"
          :data="tableData"
          style="width: 100%" v-if="tab == 3">
          <el-table-column
            type="index"
            width="50"
            label="序号">
          </el-table-column>
          <el-table-column
            prop="code"
            label="手术代码">
          </el-table-column>
          <el-table-column
            prop="name"
            label="手术名称">
          </el-table-column>
          <drg-table-column
            prop="modifier"
            label="操作人">
          </drg-table-column>
          <drg-table-column
            prop="modificationTime"
            label="操作时间">
          </drg-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-edit"
                type="primary"
                circle
                @click="showDialog(scope.row,1)"></el-button>
            </template>
          </el-table-column>
          <el-table-column label="删除" align="center" fixed="right">
            <template slot-scope="scope">
              <el-popconfirm style="cursor: pointer;display: block" @confirm="deleteCodeInfo(scope.row)"
                             title="是否删除？">
                <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <!--   病组 dialog     -->
        <el-dialog :title="profttl" :visible.sync="dialogFormSickGroup">
          <el-form ref="form" :model="form" label-width="90px" style="width: 50%">
            <el-form-item>
              <el-input v-model="form.id" placeholder="" v-show="false"></el-input>
            </el-form-item>
            <el-form-item label="病组代码">
              <el-input v-model="form.code" placeholder="请输入病组代码" @change="queryData">
              </el-input>
            </el-form-item>
            <el-form-item label="病组名称">
              <el-input v-model="form.name" placeholder="请输入病组名称"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormSickGroup = false">取 消</el-button>
            <el-button type="primary" @click="commitData()">确 定</el-button>
          </div>
        </el-dialog>
        <!--   诊断dialog     -->
        <el-dialog :title="profttl" :visible.sync="dialogFormDiagnosis">
          <el-form ref="form" :model="form" label-width="90px" style="width: 50%">
            <el-form-item>
              <el-input v-model="form.id" placeholder="" v-show="false"></el-input>
            </el-form-item>
            <el-form-item label="诊断代码">
              <el-input v-model="form.code" placeholder="请输入诊断代码"></el-input>
            </el-form-item>
            <el-form-item label="诊断名称">
              <el-input v-model="form.name" placeholder="请输入诊断名称"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormDiagnosis = false">取 消</el-button>
            <el-button type="primary" @click="commitData()">确 定</el-button>
          </div>
        </el-dialog>
        <!--  手术dialog      -->
        <el-dialog :title="profttl" :visible.sync="dialogFormOperation">
          <el-form ref="form" :model="form" label-width="90px" style="width: 50%">
            <el-form-item>
              <el-input v-model="form.id" placeholder="" v-show="false"></el-input>
            </el-form-item>
            <el-form-item label="手术代码">
              <el-input v-model="form.code" placeholder="请输手术代码"></el-input>
            </el-form-item>
            <el-form-item label="手术名称">
              <el-input v-model="form.name" placeholder="请输手术名称"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormOperation = false">取 消</el-button>
            <el-button type="primary" @click="commitData()">确 定</el-button>
          </div>
        </el-dialog>
        <el-dialog
          title="上传文件"
          :visible.sync="dialogVisible"
          width="50%">
          <el-upload
            style="text-align: center"
            drag
            ref="upload"
            :limit="1"
            action="customize"
            accept=".xlsx,.xls"
            :http-request="upload">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
          </el-upload>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import {
  selectDiseaseGroupData, deleteDiseaseGroup, insertDiseaseGroup, updateDiseaseGroup,
  selectDiagnosisData,
  insertDiagnosis,
  selectOperationData,
  insertOperation,
  selectDiseaseGroup,
  selectDiagnosis,
  selectOperation,
  downPatientTemplate,
  downDiagnosticTemplate,
  downOperationTemplate,
  patientTemplateUpload,
  diagnosticTemplateUpload,
  operationTemplateUpload
} from '@/api/dataConfig/RegulatoryParametersConfig'
import { nurseCodeUpload } from '../../../api/dataConfig/doctorCodeConfig'
import { transfusionCodeUpload } from '../../../api/dataConfig/TransFusionCodeConfig'
export default {
  name: 'regulatoryParameterConfig',
  data: () => ({
    queryForm: {
      code: '',
      name: '',
      modifier: '',
      modificationTime: ''
    },
    codeOption: [],
    diagnosisOption: [],
    operationOption: [],
    operationCache: {}, // 缓存对象
    isAllLoaded: false,
    form: {
      id: '',
      code: '',
      name: '',
      modifier: '',
      modificationTime: ''
    },
    tableData: [],
    dialogFormSickGroup: false,
    dialogFormDiagnosis: false,
    dialogFormOperation: false,
    profttl: '修改',
    dialogType: 1,
    dialogVisible: false,
    total: 0,
    contentTitle: '病组参数配置列表',
    tab: '1',
    timer: null
  }),
  mounted () {
    this.queryData()
    this.loadOperationData()
  },
  methods: {
    async loadOperationData () {
      if (Object.prototype.hasOwnProperty.call(this.operationCache, 'operationOption')) {
        // 如果缓存中已存在数据，则直接使用缓存数据
        this.operationOption = this.operationCache.operationOption
        this.isAllLoaded = true
      } else {
        // 如果缓存中不存在数据，则进行异步请求
        try {
          selectOperation(this.getParams()).then(res => {
            this.operationOption = res.data
            this.isAllLoaded = true
            // 将数据存入缓存对象
            this.operationCache.operationOption = res.data
          })
        } catch (error) {
          console.error(error)
        }
      }
    },
    async filterQuery (query) {
      let res = await selectDiagnosis({ code: query })
      this.diagnosisOption = res.data
      this.$forceUpdate()
    },
    queryData () {
      if (this.tab == 1) {
        this.tableData = []
        // 病组
        selectDiseaseGroupData(this.getParams()).then(res => {
          this.tableData = res.data.list
          this.total = res.data.total
        })
        // 病组下拉
        selectDiseaseGroup(this.getParams()).then(res => {
          this.codeOption = res.data
        })
      } else if (this.tab == 2) {
        this.tableData = []
        // 诊断
        selectDiagnosisData(this.getParams()).then(res => {
          this.tableData = res.data.list
          this.total = res.data.total
        })
      } else if (this.tab == 3) {
        this.tableData = []
        // 手术
        selectOperationData(this.getParams()).then(res => {
          this.tableData = res.data.list
          this.total = res.data.total
        })
      }
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    getForm () {
      let params = {}
      Object.assign(params, this.form)
      return params
    },
    deleteCodeInfo (row) {
      deleteDiseaseGroup(row).then(res => {
        this.queryData()
      })
    },
    commitData: function () {
      if (this.tab == 1) {
        if (this.dialogType == 1) {
          updateDiseaseGroup(this.getForm()).then(res => {
            this.dialogFormSickGroup = false
          })
        } else {
          insertDiseaseGroup(this.getForm()).then(res => {
            if (res.code == 200) {
              this.$message.success('新增成功')
              this.dialogFormSickGroup = false
              this.queryData()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      } else if (this.tab == 2) {
        if (this.dialogType == 1) {
          updateDiseaseGroup(this.getForm()).then(res => {
            this.dialogFormDiagnosis = false
          })
        } else {
          insertDiagnosis(this.getForm()).then(res => {
            if (res.code == 200) {
              this.$message.success('新增成功')
              this.dialogFormDiagnosis = false
              this.queryData()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      } else if (this.tab == 3) {
        if (this.dialogType == 1) {
          updateDiseaseGroup(this.getForm()).then(res => {
            this.dialogFormOperation = false
          })
        } else {
          insertOperation(this.getForm()).then(res => {
            if (res.code == 200) {
              this.$message.success('新增成功')
              this.dialogFormOperation = false
              this.queryData()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      }
    },
    showDialog (row, type) {
      if (this.tab == 1) {
        this.dialogFormSickGroup = true
      } else if (this.tab == 2) {
        this.dialogFormDiagnosis = true
      } else if (this.tab == 3) {
        this.dialogFormOperation = true
      }
      if (type == 1) {
        this.profttl = '修改'
        this.form = row
        this.dialogType = 1
      } else {
        this.form = {}
        this.profttl = '新增'
        this.dialogType = 2
      }
    },
    upload (data) {
      let params = new FormData()
      params.append('file', data.file)
      if (this.tab == 1) {
        patientTemplateUpload(params).then(res => {
          if (res.code == 200) {
            this.dialogVisible = false
            this.$refs.upload.clearFiles()
            this.$message.success('上传成功')
            this.queryData()
          }
        }).catch(() => {
          this.$refs.upload.clearFiles()
        })
      } else if (this.tab == 2) {
        diagnosticTemplateUpload(params).then(res => {
          if (res.code == 200) {
            this.dialogVisible = false
            this.$refs.upload.clearFiles()
            this.$message.success('上传成功')
            this.queryData()
          }
        }).catch(() => {
          this.$refs.upload.clearFiles()
        })
      } else if (this.tab == 3) {
        operationTemplateUpload(params).then(res => {
          if (res.code == 200) {
            this.dialogVisible = false
            this.$refs.upload.clearFiles()
            this.$message.success('上传成功')
            this.queryData()
          }
        }).catch(() => {
          this.$refs.upload.clearFiles()
        })
      }
    },
    downConfigTemplate () {
      if (this.tab == 1) {
        downPatientTemplate().then(res => {
          this.$somms.download(res, '病组参数模板', 'application/vnd.ms-excel')
        })
      } else if (this.tab == 2) {
        downDiagnosticTemplate().then(res => {
          this.$somms.download(res, '诊断参数模板', 'application/vnd.ms-excel')
        })
      } else if (this.tab == 3) {
        downOperationTemplate().then(res => {
          this.$somms.download(res, '手术参数模板', 'application/vnd.ms-excel')
        })
      }
    },
    DoctorNurse () {
      if (this.tab == 1) {
        this.contentTitle = '病组参数配置列表'
      } else if (this.tab == 2) {
        this.contentTitle = '诊断参数配置列表'
      } else if (this.tab == 3) {
        this.contentTitle = '手术参数配置列表'
      }
      this.queryData()
    }
  }
}
</script>
<style scoped>
/deep/ .el-dialog {
  height: 33vh;
  overflow: auto;
}
</style>
