<template>
  <div class="app-container">

  </div>
</template>
<script>

export default {
  mounted() {
    // 获取 query 参数
    const { k00, id ,username,password,token} = this.$route.query;
    debugger
    const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);
    console.log('Query Params1:', window.location.hash);
    console.log('Query Params:', this.$route.query);

    // 打印特定的查询参数
    console.log('ID:', this.$route.query.id);
    console.log('Username:', this.$route.query.username);

    this.newHandleShowMedicalDetail(k00, id,username,password,token);
    // 进行拼接或其他操作

    // 如果你需要跳转到另一个页面，可以使用 this.$router.push() 进行跳转

  },
  methods: {
    newHandleShowMedicalDetail(k00, id,username,password,token) {
      // 跳转到 /setlListManage/setlListInfo2，并将参数拼接到 URL 中
      let obj = { path: '/setlListManage/setlListInfo2',
        query: { k00: this.$route.query.k00, id: this.$route.query.id,username:this.$route.query.username,
          password:this.$route.query.password,token: this.$route.query.token} }
      obj.query.see = false
      this.$router.push(obj)
    }
  }
}
</script>
