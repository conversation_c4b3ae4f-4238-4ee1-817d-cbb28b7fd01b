import request from '@/utils/request'

/**
 * 查询Kpi指标数据
 * @param params
 * @returns {*}
 */
export function queryKpiData (params) {
  return request({
    url: '/newDipBusinessDeptAnalysisController/queryKpiData',
    method: 'post',
    params: params
  })
}

/**
 * 查询预测数据
 * @param params
 * @returns {*}
 */
export function queryForecastData (params) {
  return request({
    url: '/newDipBusinessDeptAnalysisController/queryForecastData',
    method: 'post',
    params: params
  })
}
/**
 * 查询病组四象限数据
 * @param params
 * @returns {*}
 */
export function selectSickGroupQuadrant (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/selectSickGroupQuadrant',
    method: 'post',
    params: params
  })
}
