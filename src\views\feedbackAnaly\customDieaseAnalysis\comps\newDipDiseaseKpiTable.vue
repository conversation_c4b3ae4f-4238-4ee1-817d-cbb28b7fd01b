<template>
  <div class="analysis-wrapper">
    <div class="analysis-wrapper-left">
      <el-table ref="elTable"
                :id="id"
                height="100%"
                stripe
                :header-cell-style = "{'text-align' : 'center'}"
                :data="data"
                v-loading="loading"
                @row-click="tableRowClick"
                border>
        <el-table-column label="序号" type="index" width="50" fixed align="center" />
        <el-table-column label="DIP编码" prop="dipCodg" :show-overflow-tooltip="true"  width="110" fixed />
        <el-table-column label="DIP名称" prop="dipName" :show-overflow-tooltip="true"  width="110" fixed />
        <el-table-column label="入组病案数" prop="drgInGroupMedcasVal" width="110" sortable align="right" :fixed="include('inGroupNum')">
          <template slot-scope="scope">
            <div :class="scope.row.drgInGroupMedcasVal == 0 ? '' : 'skip'" @click="scope.row.drgInGroupMedcasVal == 0 ? '' : queryGroupPatient(scope.row)">
              {{ scope.row.drgInGroupMedcasVal }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="平均住院费用" prop="avgInHosCost" width="125" sortable align="right" :fixed="include('avgInHosCost')" />
        <el-table-column label="平均住院费用(反馈)" prop="fbAvgInHosCost" width="160" sortable align="right" :fixed="include('fbAvgInHosCost')" v-if="queryForm.feeStas == 1" />
        <el-table-column label="标杆住院费用" prop="standardInHosCost" width="100" align="right" :fixed="include('standardInHosCost')" />
        <el-table-column label="平均住院天数" prop="avgInHosDays" width="135" sortable align="right" :fixed="include('avgInHosDays')" />
        <el-table-column label="标杆住院天数" prop="standardInHosDays" width="100" align="right" :fixed="include('standardInHosDays')" />
        <el-table-column label="药占比" prop="drugRatio" width="95" sortable align="right" :fixed="include('drugRatio')">
          <template slot-scope="scope">
            {{ $somms.addPercent(scope.row.drugRatio) }}
          </template>
        </el-table-column>
        <el-table-column label="标杆药占比" prop="standardDrugRatio" width="90" align="right" :fixed="include('standardDrugRatio')">
          <template slot-scope="scope">
            {{ $somms.addPercent(scope.row.standardDrugRatio) }}
          </template>
        </el-table-column>
        <el-table-column label="耗占比" prop="consumeRatio" width="95" sortable align="right" :fixed="include('consumeRatio')">
          <template slot-scope="scope">
            {{ $somms.addPercent(scope.row.consumeRatio) }}
          </template>
        </el-table-column>
        <el-table-column label="标杆耗占比" prop="standardConsumeRatio" width="90" align="right" :fixed="include('standardConsumeRatio')">
          <template slot-scope="scope">
            {{ $somms.addPercent(scope.row.standardConsumeRatio) }}
          </template>
        </el-table-column>
        <el-table-column label="时间消耗指数" prop="timeIndex" width="125" sortable align="right" :fixed="include('timeIndex')" />
        <el-table-column label="费用消耗指数" prop="costIndex" width="125" sortable align="right" :fixed="include('costIndex')" />
        <el-table-column label="悬浮"  align="center" >
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="showSuspension(scope.row)">悬浮</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="analysis-wrapper-right">
      <el-empty description="暂未选择数据" v-if="Object.keys(this.rightAnalysisData).length == 0"></el-empty>

      <div class="analysis-wrapper-right-content" v-else>
        <el-button type="info" style="float: right" @click="changeFourteenCost" round>切换</el-button>
        <!-- 标题 -->
        <div class="analysis-right-title text-ellip" style="width: 70%">
          {{ rightAnalysisData.profttl }}
        </div>

        <!-- 副标 -->
        <div class="analysis-right-subhead text-ellip">
          {{ rightAnalysisData.subhead }}
        </div>

        <!-- 费用项 -->
        <div class="analysis-right-cost">

          <!-- 费用项明细对比 -->
          <cost-item :items="rightAnalysisData.items" />
        </div>

        <!-- 建议 -->
        <div class="analysis-right-suggest">
          <div class="analysis-right-suggest-title">
            建议
          </div>
          <div class="analysis-right-suggest-content">
            <div class="analysis-right-suggest-item" v-for="(item, index) in suggestData" :key="index">
              {{ (index+=1) + '、' + item }}
            </div>
          </div>

          <!-- 声明 -->
          <div class="analysis-right-suggest-declaration">
            <div style="width: 80%">当前费用仅供参考</div>
            <div style="float: right">
<!--              <el-button size="mini" type="primary" @click="jumpDetails(rightAnalysisData)" >详情</el-button>-->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import costItem from '../../../hosDipAnalysisNew/deptAnalysisNew/comps/newDipDeptAnalysisCompCostItem.vue'

export default {
  name: 'newDipDiseaseKpiTable',
  components: {
    costItem
  },
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    },
    // 表格id
    id: {
      type: String
    },
    queryForm: {
      type: Object
    },
    columnOptions: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    scopeData: {},
    rightAnalysisData: {},
    fourteenCost: true,
    rowData: []
  }),
  methods: {
    include (column) {
      return this.fixedColumns.includes(column)
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    // 显示悬浮
    showSuspension (scope) {
      this.scopeData = scope
      let scopeList = []
      for (let i in this.scopeData) {
        let o = {}
        o.key = i
        o.value = this.scopeData[i]
        scopeList.push(o)
      }
      let res = []
      for (let i = 0; i < scopeList.length; i++) {
        for (let j = 0; j < this.columnOptions.length; j++) {
          if (scopeList[i].key == this.columnOptions[j].value) {
            res.push({
              key: scopeList[i].key,
              label: this.columnOptions[j].label,
              value: scopeList[i].value,
              type: 1,
              show: true
            })
          }
        }
      }
      if (scope) {
        this.$emit('showSuspension', res)
      }
    },
    queryGroupPatient (item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          dipCodg: item.dipCodg,
          deptCode: this.queryForm.deptCode,
          drCodg: this.queryForm.drCodg,
          drName: this.queryForm.drName,
          isInGroup: 1,
          feeStas: this.queryForm.feeStas,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    tableRowClick (row) {
      this.rowData = row
      this.rightAnalysisData = {}
      this.rightAnalysisData.profttl = row.dipCodg
      this.rightAnalysisData.subhead = row.dipName
      this.rightAnalysisData.items = this.getCostItemInfo(row)
      this.$emit('rowClick', row)
    },
    getCostItemInfo (row) {
      this.suggestData = []
      // 拼接结构
      let costItem = []
      if (this.fourteenCost) {
        costItem = [
          { name: '综合医疗服务费',
            value: row.com_med_servfee,
            bgValue: row.bgzhylfwf,
            children: [
              { name: '一般医疗服务费', value: row.ordn_med_servfee, bgValue: row.bgybylfwf },
              { name: '一般治疗操作费', value: row.ordn_trt_oprt_fee, bgValue: row.bgybzlczf },
              { name: '护理费', value: row.nursfee, bgValue: row.bghlf },
              { name: '其他费', value: row.zhylfwqtf, bgValue: row.bgzhylfwqtf }
            ]
          },
          { name: '诊断费',
            value: row.diag_fee,
            bgValue: row.bgzdf,
            children: [
              { name: '病理诊断费', value: row.cas_diag_fee, bgValue: row.bgblzdf },
              { name: '实验室诊断费', value: row.lab_diag_fee, bgValue: row.bgsyszdf },
              { name: '影像学诊断费', value: row.rdhy_diag_fee, bgValue: row.bgyxxzdf },
              { name: '临床诊断项目费', value: row.clnc_diag_item_fee, bgValue: row.bglczdxmf }
            ]
          },
          { name: '治疗费',
            value: row.treat_fee,
            bgValue: row.bgzlf,
            children: [
              { name: '非手术治疗项目费', value: row.nsrgtrt_item_fee, bgValue: row.bgfsszlxmf },
              { name: '手术治疗费', value: row.oprn_treat_fee, bgValue: row.bgsszlf }
            ]
          },
          { name: '康复费', value: row.rhab_fee, bgValue: row.bgkff, children: [] },
          { name: '中医费', value: row.tcmdrug_fee, bgValue: row.bgzyf, children: [] },
          { name: '西药费', value: row.west_fee, bgValue: row.bgxyf, children: [] },
          { name: '中药费',
            value: row.zyf1,
            bgValue: row.bgzyf1,
            children: [
              { name: '中成药费', value: row.tcmpat_fee, bgValue: row.bgzcyf },
              { name: '中草药费', value: row.tcmherb, bgValue: row.bgzcyf1 }
            ]
          },
          { name: '血液和血液制品费',
            value: row.blood_blo_pro_fee,
            bgValue: row.bgxyhxyzpf,
            children: [
              { name: '血费', value: row.blo_fee, bgValue: row.bgxf },
              { name: '白蛋白类制品费', value: row.bdblzpf, bgValue: row.bgbdblzpf },
              { name: '球蛋白类制品费', value: row.qdblzpf, bgValue: row.bgqdblzpf },
              { name: '凝血因子类制品费', value: row.nxyzlzpf, bgValue: row.bgnxyzlzpf },
              { name: '细胞因子类制品费', value: row.xbyzlzpf, bgValue: row.bgxbyzlzpf }
            ]
          },
          { name: '耗材费',
            value: row.mcs_fee,
            bgValue: row.bghcf,
            children: [
              { name: '检查用一次性医用材料费', value: row.jcyycxyyclf, bgValue: row.bgjcyycxyyclf },
              { name: '治疗用一次性医用材料费', value: row.trt_use_dspo_med_matlfee, bgValue: row.bgzlyycxyyclf },
              { name: '手术用一次性医用材料费', value: row.oprn_use_dspo_med_matlfee, bgValue: row.bgssyycxyyclf }
            ]
          },
          { name: '其他费', value: row.oth_fee, bgValue: row.bgqtf, children: [] }
        ]
      } else {
        costItem = [
          { name: '床位费', value: row.fourteencwf, bgValue: row.bgfourteencwf, children: [] },
          { name: '诊察费', value: row.fourteenzcf, bgValue: row.bgfourteenzcf, children: [] },
          { name: '检查费', value: row.fourteenjcf, bgValue: row.bgfourteenjcf, children: [] },
          { name: '化验费', value: row.fourteenhyf, bgValue: row.bgfourteenhyf, children: [] },
          { name: '治疗费', value: row.fourteenzlf, bgValue: row.bgfourteenzlf, children: [] },
          { name: '手术费', value: row.fourteenssf, bgValue: row.bgfourteenssf, children: [] },
          { name: '护理费', value: row.fourteenhlf, bgValue: row.bgfourteenhlf, children: [] },
          { name: '卫生材料费', value: row.fourteenwsclf, bgValue: row.bgfourteenwsclf, children: [] },
          { name: '西药费', value: row.fourteenxyf, bgValue: row.bgfourteenxyf, children: [] },
          { name: '中药饮片费', value: row.fourteenzyypf, bgValue: row.bgfourteenzyypf, children: [] },
          { name: '中成药费', value: row.fourteenzcyf, bgValue: row.bgfourteenzcyf, children: [] },
          { name: '一般诊疗费', value: row.fourteenybzlf, bgValue: row.bgfourteenybzlf, children: [] },
          { name: '挂号费', value: row.fourteenghf, bgValue: row.bgfourteenghf, children: [] },
          { name: '其他费', value: row.fourteenqtf, bgValue: row.bgfourteenqtf, children: [] }
        ]
      }

      // 建议
      costItem.forEach(item => {
        if (item.bgValue != 0) {
          if (item.value / item.bgValue > 2) {
            this.suggestData.push('建议降低' + item.name)
          }

          if (item.value / item.bgValue < 0.5) {
            this.suggestData.push('建议提高' + item.name)
          }
        }
      })
      return costItem
    },
    changeFourteenCost () {
      this.fourteenCost = !this.fourteenCost
      this.tableRowClick(this.rowData)
    }
  }
}
</script>

<style lang="scss" scoped>
.analysis-wrapper{
  height: 100%;
  width: 100%;
  position: relative;

  &-left{
    width: 80%;
    height: 100%;
  }

  &-analysis{
    width: 100%;
    height: 80%;
    position: relative;
    display: flex;
  }

  &-right{
    width: 20%;
    height: 100%;
    background-color: rgba(131,175,155,.3);
    padding: 1% 1% 0 1%;
    border-radius: 1%;
  }

  &-no-table{
    width: 100%;
    height: 80%;
  }
}
.analysis-head{
  width: 80%;
  height: 20%;

&-title{
   width: 100%;
   height: 20%;
   font-size: var(--biggerSize);
   font-weight: 600;
   position: relative;

&-dropdown{
   position: absolute;
   right: 0;
   top: 0;
 }
}

&-content{
   width: 100%;
   height: 54%;
   display: flex;

&-item{
   width: 14%;
   height: 100%;
   cursor: pointer;
   background-color: rgb(64,158,255);
   font-weight: 600;
   margin-right: 1%;
   padding: 1%;
   display: flex;
   align-items: center;
   justify-items: center;
   flex-direction: column;
   border-radius:  16% 0 16% 0 ;

.title{
  font-size: 14px;
  color: white;
}

.value{
  margin-top: 8%;
  font-size: 24px;
  color: white;
}
}
}
}

.analysis-content{
  width: 100%;
  height: 94%;
  position: relative;

&-summary{
   width: 30%;
   height: 40%;
   border: 1px solid red;
 }
}

.analysis-wrapper-right{
  width: 20%;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0%;
  display: flex;
  justify-content: center;

&-content{
   width: 100%;
   height: 100%;
 }
}

$titleGray: gray;
$titleSize: 13px;
.analysis-right{

&-title{
   width: 100%;
   height: 4%;
   font-size: var(--biggerSize);
   font-weight: 600;
 }

&-subhead{
   width: 100%;
   height: 4%;
   font-size: var(--biggerSmallSize);
   color: gray;
   border-bottom: 1px solid $titleGray;
 }

&-cost{
   width: 100%;
   height: 62%;
   line-height: 20px;
   overflow-y: auto;
   border-bottom: 1px solid $titleGray;
 }

&-suggest{
   padding-top: 4%;
   width: 100%;
   height: 30%;
   overflow-y: auto;

&-title{
   width: 100%;
   font-weight: 600;
   height: 10%;
   font-size: $titleSize;
 }

&-content{
   width: 100%;
   height: 70%;
   overflow-y: auto;
 }

&-declaration{
   width: 100%;
   height: 15%;
   color: #fa5d5d;
   display: flex;
   align-items: center
 }

&-item{
   width: 100%;
   height: 16%;
 }
}
}
</style>
