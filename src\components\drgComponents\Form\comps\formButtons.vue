<template>
  <div style="display: flex;align-items: center">
    <slot name="buttonsPrefix"></slot>
    <el-radio-group v-model="checkBox" class="som-button-margin-right" @change="changeTime" v-if="showTimeCheckBox">
      <el-radio-button v-for="(time, index) in timeCheckBox" :label="time.label" :key="index" :disabled="time.disabled"
                       v-if="!time.disabled">
        {{ time.label }}
      </el-radio-button>
    </el-radio-group>
    <el-button type="primary" size="mini" @click="queryClick" v-if="showQuery" class="som-button-margin-right">查询
    </el-button>
    <slot name="buttons"></slot>
    <el-popconfirm
      class="expBtn som-button-margin-right"
      confirm-button-text='确定'
      cancel-button-text='导出全部'
      icon="el-icon-info"
      icon-color="red"
      title="是否当前页面？" @confirm="emit('exportExcel')" @cancel="emit('exportAll')" v-if="showExportExcel">

      <el-button slot="reference" type="success">导出Excel</el-button>
    </el-popconfirm>
    <slot name="buttonsMiddle"></slot>
    <el-button @click="accurateQuery" class="som-button-margin-right" v-if="showQueryButton">
      精准查询 <i :class="[conditionIcon ? conditionIcon : 'el-icon-arrow-down']"></i>
    </el-button>
    <el-button @click="reset" v-if="showReset">重置</el-button>
    <slot name="buttonsSuffix"></slot>
  </div>
</template>
<script>
let box = '出院'
export default {
  name: 'formButtons',
  props: {
    showExportExcel: {
      type: Boolean,
      default: false
    },
    showQuery: {
      type: Boolean,
      default: true
    },
    showQueryButton: {
      type: Boolean,
      default: true
    },
    showReset: {
      type: Boolean,
      default: true
    },
    conditionIcon: {
      type: String
    },
    showTimeCheckBox: {
      type: Boolean,
      default: true
    },
    // 外部传入checkbox改变
    outCheckbox: {
      type: String
    },
    // 是否显示结算
    showSettlementButton: {
      type: Boolean,
      default: false
    },
    // 外部指定显示那些checkbox
    outTimeCheckBox: {
      type: Array
    }
  },
  mounted() {
    this.timeCheckBox = this.outTimeCheckBox
    // this.timeCheckBox[2].disabled = !this.showSettlementButton
  },
  data: () => ({
    checkBox: box,
    timeCheckBox: []
  }),
  methods: {
    emit(event) {
      this.$emit(event, '' | 0)
    },
    changeTime(val) {
      this.$emit('changeTime', val)
    },
    // 精准查询
    accurateQuery() {
      // this.changeTime(this.checkBox)
      this.emit('queryCondition')
    },
    // 查询
    queryClick() {
      if (this.checkBox.length == 0) {
        this.$message({
          type: 'warning',
          message: '请选择出院或入院时间'
        })
      } else {
        this.emit('query')
      }
    },
    // 重置
    reset() {
      this.checkBox = box
      this.changeTime(this.checkBox)
      this.emit('resetForm')
    }
  },
  watch: {
    outCheckbox(val) {
      if (typeof (val) === 'string') {
        this.checkBox = val
        this.changeTime(val)
      }
    }
  }
}
</script>

<style scoped>
/deep/ .el-button + .el-button {
  margin-left: 0;
}
</style>
