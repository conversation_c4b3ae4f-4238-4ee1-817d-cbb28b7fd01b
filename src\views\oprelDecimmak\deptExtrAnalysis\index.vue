<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-date-range
             show-in-date-range
             show-se-date-range
             :show-hos-dept="{ show: this.$somms.hasHosRole()}"
             show-pagination
             :totalNum="total"
             :container="true"
              :showCoustemContentTitle="true"
             headerTitle="查询条件"
             :exportExcel="{ 'tableId': tableId, exportName: '运营决策科室分析'+ '(' + this.queryForm.begnDate + '-' + this.queryForm.expiDate + ')' }"
             :exportExcelFun="queryPageData"
             :exportExcelHasChild="true"
             @query="queryData">
      <!-- 内容 -->
      <template slot="containerContent">
        <!-- 固定列 -->
        <div class="fixed-column">
          <el-select v-model="columnVal"
                     multiple
                     collapse-tags
                     :multiple-limit="3"
                     placeholder="请选择固定列">
            <el-option
              v-for="item in columnOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <el-tabs  v-model="curCheckedTag" v-if="!showSplashes" style="height: 95%" @tab-click="queryData">
          <el-tab-pane label="DIP" v-if="enabledModules.includes('1')" style="height: 88%" name="dipData" >
            <query-data-table :table-data="tableData"
                              :table-loading="tableLoading"
                              :query-form="queryForm"
                              :query-type="1"
                              :ref="ref1"
                              @setRefObj="(obj) => this.tableObj = obj"
                              :columnOptions="columnOptions"
                              :fixed-columns="columnVal"
                              :id="tableId"/>
           </el-tab-pane>
          <el-tab-pane label="DRG" v-if="enabledModules.includes('3')" style="height: 88%"  name="drgData" >
            <query-data-table :table-data="tableData"
                              :table-loading="tableLoading"
                              :query-form="queryForm"
                              :query-type="3"
                              :ref="ref2"
                              @setRefObj="(obj) => this.tableObj = obj"
                              :columnOptions="columnOptions"
                              :fixed-columns="columnVal"
                              :id="tableId"/>
          </el-tab-pane>
          <el-tab-pane label="CD" v-if="enabledModules.includes('2')" style="height: 88%" name="cdData" >
            <query-data-table :table-data="tableData"
                              :table-loading="tableLoading"
                              :query-form="queryForm"
                              :query-type="2"
                              :ref="ref3"
                              @setRefObj="(obj) => this.tableObj = obj"
                              :id="tableId"/>
          </el-tab-pane>
        </el-tabs>
        <!-- 散点图 -->
        <div id="splashes" v-else style="height: 100%;width: 100%">
          <drg-echarts :options="splashesOptions" ref="splashesChart"/>
        </div>
      </template>

      <!-- 内容 profttl -->
      <template slot="contentTitle">
        <drg-title-line title="科室分析列表">
          <template slot="rightSide">
            <!-- splashes -->
            <i class="som-icon-splashes som-iconTool"
               title="散点图"
               v-if="!showSplashes"
               @click="changeSplashesOrTable(1)"
               style="height: 1.2rem;width: 1.2rem"></i>

            <!-- table -->
            <i class="som-icon-table som-iconTool"
               title="表格"
               v-else
               @click="changeSplashesOrTable(2)"
               style="height: 1.2rem;width: 1.2rem"></i>
          </template>
        </drg-title-line>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryData as queryPageData, queryDrgData } from '@/api/operationalDecision/analysis/operationalDecisionDeptAnalysis'
import { formatRate } from '@/utils/common'
import queryDataTable from './/comps/queryDataTable'
import { queryEnableGroup } from '@/api/common/sysCommon'
export default {
  name: 'deptExtrAnalysis',
  components: {
    'query-data-table': queryDataTable
  },
  data: () => ({
    queryForm: {},
    tableData: [],
    ref1: '',
    ref2: '',
    ref3: '',
    tableObj: {},
    tableLoading: false,
    total: 0,
    showSplashes: false,
    showDept: true,
    splashesOptions: {},
    curCheckedTag: 'dipData',
    tableId: '',
    enabledModules: [],
    columnVal: [],
    columnOptions: []
  }),
  mounted () {
    this.$nextTick(() => {
      if (!this.$somms.hasDeptRole()) {
        this.showDept = false
      }
      this.init()
    })
  },
  methods: {
    formatRate,
    queryPageData,
    queryEnableModule () {
      // queryEnableGroup().then(res => {
      //   if (res.code == 200) {
      //     res.data.forEach(item => {
      //       this.enabledModules.push(item.value)
      //     })
      //   }
      // })
      this.enabledModules.push(this.$somms.getGroupType())
      switch (this.$somms.getGroupType()) {
        case '1':
          this.curCheckedTag = 'dipData'
          break
        case '3':
          this.curCheckedTag = 'drgData'
          break
      }
    },
    init () {
      this.queryEnableModule()
      this.queryData()
    },
    // 生成固定列
    generateFixedColumns () {
      this.columnOptions = []
      let children = this.$refs.dataTable.$children[0].$children
      if (children) {
        children.forEach(item => {
          if (item.$options.propsData.label && item.$options.propsData.label != '序号') {
            this.columnOptions.push({
              value: item.$options.propsData.prop,
              label: item.$options.propsData.label
            })
          }
        })
      }
    },
    queryData () {
      if (this.curCheckedTag == 'dipData') {
        this.tableId = 'dipData'
        this.ref1 = 'dataTable'
      } else if (this.curCheckedTag == 'drgData') {
        this.tableId = 'drgData'
        this.ref2 = 'dataTable'
      } else if (this.curCheckedTag == 'cdData') {
        this.tableId = 'cdData'
        this.ref3 = 'dataTable'
      }
      this.tableLoading = true
      queryPageData(this.getParams()).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.total
          this.tableLoading = false
          this.createSplashes()
          this.generateFixedColumns()
        }
      })
      // if(this.curCheckedTag=='dipData'){
      //   this.ref1 = "dataTable"
      //   this.tableLoading=true
      //   this.tableId="dipData"
      //   queryPageData(this.getParams()).then(res => {
      //     console.log(this.tableObj)
      //     if(res.code == 200){
      //       this.tableData.dipData = res.data.list
      //       this.total = res.data.total
      //       this.tableLoading=false
      //       this.createSplashes()
      //     }
      //   })
      // }
      // else {
      //   this.ref2 = "dataTable"
      //   this.tableLoading=true
      //   this.tableId="drgData"
      //   queryDrgData(this.getParams()).then(res => {
      //     if(res.code == 200){
      //       this.tableData.drgData = res.data.list
      //       this.total = res.data.total
      //       this.tableLoading=false
      //       this.createSplashes()
      //     }
      //   })
      // }
    },
    createSplashes () {
      let highData = []
      let lowData = []
      let deptNames = []
      let textName = ''
      if (this.tableData.length > 0 && this.curCheckedTag == 'dipData') {
        this.tableData.map(data => {
          highData.push([data.medcasVal, data.ultrahighRate])
          lowData.push([data.medcasVal, data.ultraLowRate])
          deptNames.push(data.deptName)
          textName = 'DIP科室超高超低分析'
        })
      } else if (this.tableData.length > 0 && this.curCheckedTag == 'drgData') {
        this.tableData.map(data => {
          highData.push([data.medcasVal, data.ultrahighRate])
          lowData.push([data.medcasVal, data.ultraLowRate])
          deptNames.push(data.deptName)
          textName = 'DRG科室超高超低分析'
        })
      } else if (this.tableData.length > 0 && this.curCheckedTag == 'cdData') {
        this.tableData.map(data => {
          highData.push([data.medcasVal, data.ultrahighRate])
          lowData.push([data.medcasVal, data.ultraLowRate])
          deptNames.push(data.deptName)
          textName = '成都科室超高超低分析'
        })
      }
      this.splashesOptions = {
        title: {
          text: textName,
          left: 'center',
          top: 0
        },
        tooltip: {
          // trigger: 'axis',
          showDelay: 0,
          formatter: function (params) {
            return deptNames[params.dataIndex] + '<br/>' +
                   '科室人数:' + params.data[0] +
              (params.seriesIndex == 0 ? '  超高率:' : '   超低率') + params.data[1] + '%'
          },
          axisPointer: {
            show: true,
            type: 'cross',
            lineStyle: {
              type: 'dashed',
              width: 1
            }
          }
        },
        color: ['#778dd1', '#a7d692'],
        legend: {
          data: ['高倍率', '低倍率'],
          right: 50,
          top: 50,
          width: 100
        },
        xAxis: {
          name: '科室人数',
          splitLine: { show: false },
          axisLabel: {
            formatter: '{value}'
          }
        },
        yAxis: {
          name: '超高超低率',
          scale: true,
          splitLine: { show: false },
          axisLabel: {
            formatter: '{value} %'
          }
        },
        series: [
          // 高倍率点
          {
            name: '高倍率',
            type: 'scatter',
            emphasis: {
              focus: 'series'
            },
            symbolSize: 15,
            data: highData
          },
          // 低倍率点
          {
            name: '低倍率',
            type: 'scatter',
            emphasis: {
              focus: 'series'
            },
            symbolSize: 15,
            data: lowData
          }
        ]
      }
      if (this.showSplashes) {
        this.$nextTick(() => {
          this.$refs.splashesChart.initChart()
        })
      }
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.ym = params.ym.substring(0, 4) + '-' + params.ym.substring(4, 7)
      params.year = params.ym.substring(0, 4)
      params.dataAuth = true
      if (this.curCheckedTag == 'dipData') {
        params.type = 1
      } else if (this.curCheckedTag == 'cdData') {
        params.type = 2
      } else if (this.curCheckedTag == 'drgData') {
        params.type = 3
      }
      return params
    },
    changeSplashesOrTable (index) {
      if (index == 1) {
        this.showSplashes = true
      } else {
        this.showSplashes = false
      }
      this.init()
    }
  }
}
</script>

<style scoped>
/*固定列*/
.fixed-column {
  position: absolute;
  left: 10%;
  top: 1%;
}
/deep/ .el-tab-pane{
  height: 100%;
}
/deep/ .el-tabs__content{
  height: 97%;
}
/deep/ .el-progress__text{
  font-size:10px;
}
/deep/ .el-progress-bar{
  width: 99%;
}
/deep/ .el-card__header{
  padding:5px 5px;
  font-size: 13px;
  background-color: #eef1f6;
}
.el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
  margin-bottom:20px;
  margin-right: 10px;
}
/deep/ .el-card__body{
  height:90%;
  padding-top: 0px;
  padding-right: 10px;
  padding-bottom: 0px;
  overflow-y:auto;
}
.note {
  font-size:12px;
}
.time{
  font-size:13px;
  font-weight: bold;
}
.scope{
  margin-top:10px;
}
/deep/ .is-process{
  color: #e6a23c;
  border-color: #e6a23c;
}
</style>
