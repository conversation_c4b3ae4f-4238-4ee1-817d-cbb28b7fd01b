<template>
  <div class="container" id="c-container">
    <!-- header -->
    <div class="c-header" id="c-header" ref="cHeader">
      <el-card class="card-container">
        <slot name="header"/>
      </el-card>
    </div>

    <!-- content -->
    <div class="c-content" :style="{height: dynamicContentHeight}">
      <el-card class="card-container">
        <slot name="content"/>
      </el-card>
    </div>
  </div>
</template>
<script>
export default {
  name: 'jp_container',
  props: {
    headerPercent: {
      type: Number
    },
    // 1 代表在 1920上布局 2则是在1440上布局
    type: {
      type: Number,
      default: 1
    },
    isButtonEnd: {
      type: Boolean,
      default: false
    },
    maxCondition: {
      type: Boolean,
      default: false
    },
    // 是否精确查询
    accurate: {
      type: Boolean
    }
  },
  data () {
    return {
      dynamicContentHeight: ''
    }
  },
  methods: {
    setHeight () {
      setTimeout(() => {
        let header = document.getElementById('c-header')
        let container = document.getElementById('c-container')
        let height = container.offsetHeight
        // 8 为header的bottom
        this.dynamicContentHeight = height - header.offsetHeight - 8 + 'px'
      })
    }
  },
  watch: {
    accurate: {
      immediate: true,
      handler: function (height) {
        this.$nextTick(() => {
          this.setHeight()
        })
      }
    }
  }
}
</script>
<style scoped>
.card-container {
  width: 100%;
  height: 100%;
  position: relative;
}
.c-header {
  width: 100%;
  margin-bottom: 8px;
}
.c-content {
  width: 100%;
  position: relative;
}
.container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}
</style>
