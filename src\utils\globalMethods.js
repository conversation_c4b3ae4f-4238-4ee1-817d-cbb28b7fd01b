import Vue from 'vue'
import { queryUserInfo } from '@/api/common/drgCommon'
import { elExportExcel } from '@/utils/exportExcel'
import store from '../store'
import { exportAllExcel } from '@/api/common/sysCommon'
import { clone } from 'node-notifier/lib/utils'

class Methods {
  /**
   * 如果为空则为0
   * @param data 数据
   * @returns {number|*}
   */
  ifNullZero (data) {
    if (data && typeof (data) != 'undefined' && !isNaN(data)) {
      return data
    }
    return 0
  }

  /**
   * 生成用户信息
   */
  generateUserInfo () {
    queryUserInfo().then(res => {
      if (res.code === 200) {
        store.commit('del_roles', res.data.userRoles)
        store.commit('set_roles', res.data.userRoles)
        if (res.data.b16c) {
          store.commit('setDeptCode', res.data.b16c)
          store.commit('setDeptName', store.getters.getDeptNameByCode(res.data.b16c))
        }
      }
    })
  }

  /**
   * 是否存在医院角色
   * @returns {boolean}
   */
  hasHosRole () {
    return this.hasRole('1')
  }

  /**
   * 是否存在科室角色
   * @returns {boolean}
   */
  hasDeptRole () {
    return this.hasRole('2')
  }

  /**
   * 是否存在医生角色
   * @returns {boolean}
   */
  hasDoctorRole () {
    return this.hasRole('3')
  }

  /**
   * 是否包含角色id
   * @param roleId
   * @returns {boolean}
   */
  hasRole (roleId) {
    if (store.getters.get_roleIds) {
      return store.getters.get_roleIds.includes(roleId)
    }
    return false
  }

  /**
   * 是否是开发者
   * @returns {boolean} true：是 false：否
   */
  isDev () {
    if (store.state.user.name === 'developer') {
      return true
    }
    return false
  }

  /**
   * 分组类型
   * 1：DIP
   * 3：DRG
   * @returns {string}
   */
  getGroupType () {
    return store.getters.getFzlx
  }

  /**
   * DIP
   * @returns {boolean}
   */
  isDIP () {
    return this.getGroupType() === '1'
  }

  /**
   * DRG
   * @returns {boolean}
   */
  isDRG () {
    return this.getGroupType() === '3'
  }

   getFirstDayOfPreviousMonth() {
    let date = new Date();
    // 当前月份为1月时,上个月是上一年的12月
    if(date.getMonth() === 0) {
      return new Date(date.getFullYear() - 1, 11, 1);
    }
    // 其他月份直接月份-1
    return new Date(date.getFullYear(), date.getMonth() - 1, 1);
  }

  /**
   * 获取当前月第一天
   * @returns {Date}
   */
  getYearMonthStartTime () {
    let now = new Date() // 当前日期
    let nowMonth = now.getMonth() // 当前月
    let nowYear = now.getFullYear() // 当前年
    return new Date(nowYear, nowMonth, 1)
  }

  /**
   * 获取当前月最后一天
   * @returns {Date}
   */
  getYearMonthEndTime () {
    let now = new Date() // 当前日期
    let nowMonth = now.getMonth() // 当前月
    let nowYear = now.getFullYear() // 当前年
    return new Date(nowYear, nowMonth + 1, 0)
  }

  /**
   * 获取当前时间
   * @returns {Date}
   */
  getCurDate () {
    return new Date()
  }

  /**
   * 获取编码前缀
   * @param type 类型 1：DIP 2：DRG 3：成都
   * @returns {string}
   */
  getCodePrefixByType (type) {
    if (type == '1') {
      return 'DIP'
    } else if (type == '2') {
      return 'DRG'
    } else {
      return '成都'
    }
  }

  /**
   * 获取分组类型名称
   * @returns {string}
   */
  getGroupTypeName () {
    let groupType = this.getGroupType()
    if (groupType === '1') {
      return 'DIP'
    } else if (groupType === '3') {
      return 'DRG'
    }
    return '未知'
  }

  /**
   * 通过字典类型获取字典值
   * example:
   *          value --> 1
   *          dictType --> GENDER
   *          return --> 男
   * @param value 值
   * @param dictType 字典类型
   * @type 1: 系统码表 2：清单码表
   * return
   */
  getDictValueByType (value, dictType, type = 1) {
    if (value && dictType) {
      let dictData = this.getDictValue(dictType, type)
      try {
        if (dictData) {
          return dictData.find(data => data.value == value).label
        }
      } catch (e) {
        return null
      }
    }
    return null
  }

  /**
   * 通过字典类型获取字典值
   * @param dictType 字典类型
   * @type 1: 系统码表 2：清单码表
   * @returns {字典值|*}
   */
  getDictValue (dictType, type = 1) {
    if (dictType) {
      return type == 1 ? store.getters.getDictByKey(dictType) : store.getters.getSettleListDictByKey(dictType)
    }
    return null
  }

  /**
   * 比较数组是否重复
   * arr1: [1,2] --> arr2: [1,2] --> result: true
   * arr1: [1,2] --> arr2: [1,3] --> result: false
   *
   * @param arr1 数组1
   * @param arr2 数组2
   * return true: 相同 false：不相同
   */
  compareArrayIsRepeat (arr1, arr2) {
    return arr1.length == arr2.length &&
      arr1.every(a => arr2.some(b => a == b)) &&
      arr2.every(_b => arr1.some(_a => _a == _b))
  }

  /**
   * 获取时间,如果没写 pattern 则返回当前日期
   * @param pattern 格式 支持 yy MM dd
   * @param yearDeviation 年度偏差
   *  example: 当前时间 2021-11  yearDeviation = -1 result：2020-11
   *                            yearDeviation = 1 result：2022-11
   * @param monthDeviation 月份偏差与年份偏差一致
   * @param dayDeviation  天数偏差与年份偏差一致
   */
  getDate (pattern = '',
    yearDeviation = 0,
    monthDeviation = 0,
    dayDeviation = 0,
    split = true) {
    let date = new Date()
    let year = date.getFullYear()
    let month = parseInt(date.getMonth() + 1)
    let day = date.getDate()
    let lastDay = new Date(year, month + 1, -1)
    year = calDate(year, parseInt(month) == 1 ? -1 : yearDeviation, 0, 'y')
    month = calDate(month, monthDeviation, 0, 'm')
    day = calDate(day, dayDeviation, lastDay, 'd')
    if (month < 10) month = '0' + month
    if (day < 10) day = '0' + day
    let returnDate = ''
    if (pattern) {
      if (pattern.toLowerCase().indexOf('yyyy') != -1) {
        returnDate += year
        if (split) {
          returnDate += '-'
        }
      }
      if (pattern.toLowerCase().indexOf('mm') != -1) {
        returnDate += month
        if (split) {
          returnDate += '-'
        }
      }
      if (pattern.toLowerCase().indexOf('dd') != -1) {
        returnDate += day
        if (split) {
          returnDate += '-'
        }
      }
      if (split) {
        returnDate = returnDate.substring(0, returnDate.lastIndexOf('-'))
      }
    }
    if (returnDate) {
      return returnDate
    }
    if (!split) {
      return '' + year + month + day
    }
    return '' + year + '-' + month + '-' + day
  }

  /**
   * 文件下载，res必须为blob类型
   * @param res 返回数据
   * @param fileName 文件名称
   * @param type content-type
   */
  download (res, fileName, type) {
    const blob = new Blob([res], { type: type })
    // // 创建一个超链接，将文件流赋进去，然后实现这个超链接的单击事件
    // const eLink = document.createElement('a')
    // eLink.download = decodeURIComponent(fileName)
    // eLink.style.display = 'none'
    // eLink.href = URL.createObjectURL(blob)
    // document.body.appendChild(eLink)
    // eLink.click()
    // URL.revokeObjectURL(eLink.href) // 释放URL 对象
    // document.body.removeChild(eLink)

    const a = document.createElement('a')

    if (!a.click) {
      throw new Error('DownloadManager: "a.click()" is not supported.')
    }

    a.href = URL.createObjectURL(blob)
    a.target = '_parent'
    a.download = fileName;

    (document.body || document.documentElement).appendChild(a)
    a.click()
    a.remove()
  }

  /**
   * 获取颜色
   * @returns {string[]}
   */
  generateColor () {
    return ['#25a1ff', '#51d8b9', '#91cc75', '#faca60',
      '#7ac2df', '#65ceca', '#b2db9e', '#fe8e85',
      '#68adf6', '#19CAAD', '#8CC7B5',
      '#A0EEE1', '#90b2bf', '#a3d0ad', '#D6D5B7',
      '#D1BA74', '#E6CEAC', '#ECAD9E', '#F4606C',
      '#A3C1EA', '#F8C8C8', '#71D8D2', '#F7EE94',
      '#CBB4E3', '#fa5d5d']
  }

  /**
   * 导出所有数据
   * @param getParams 参数
   * @param total 总数量
   * @param children 节点数据
   * @param childNodes 表头
   * @param exportExcelFun 查询方法
   * @param exportName 导出名称
   */
  exportExcelAll (getParams, total, children, childNodes, exportExcelFun, exportName) {
    let params = getParams
    params.pageSize = total
    let childrenList = children
    let tableNodes = childNodes
    let columnPropList = []
    let exportTableList = []
    let exportTableColumnNameList = []
    tableNodes.forEach(child => {
      if (child.innerText != '序号') {
        if (child.innerText != '') {
          exportTableColumnNameList.push(child.innerText)
        } else {
          if (child.children[0] && child.children[0].innerHTML != '序号') {
            exportTableColumnNameList.push(child.children[0].innerHTML)
          }
        }
      }
    })

    let deleteArr = []
    let tableColumnIndex = 0
    exportTableColumnNameList.forEach(columnName => {
      for (let i = 0; i < childrenList.length; i++) {
        if (childrenList[i].label == columnName) {
          if (childrenList[i].prop == undefined) {
            deleteArr.push(tableColumnIndex)
          }
          columnPropList.push(childrenList[i].prop)
          break
        }
      }
      tableColumnIndex++
    })

    for (let i = deleteArr.length - 1; i >= 0; i--) {
      exportTableColumnNameList.splice(deleteArr[i], 1)
      columnPropList.splice(deleteArr[i], 1)
    }

    exportExcelFun(params).then(res => {
      if (res.code == 200) {
        let resDataList = res.data.list
        if (resDataList && resDataList.length > 0) {
          resDataList.forEach(data => {
            let exportTableRow = []
            columnPropList.forEach(column => {
              exportTableRow.push(data[column])
            })
            exportTableList.push(exportTableRow)
          })
        }

        if (exportTableList.length > 0 && exportTableColumnNameList.length > 0) {
          let exportParams = {}
          exportParams.columns = exportTableColumnNameList
          exportParams.data = exportTableList
          exportAllExcel(exportParams).then(res => {
            this.download(res, exportName + '_all.xlsx', 'application/vnd.ms-excel')
          })
        }
      }
    })
  }

  /**
   * 获取标签宽度
   * @returns {string}
   */
  getLabelWidth () {
    return getComputedStyle(document.documentElement).getPropertyValue('--labelWidth')
  }

  /**
   * 获取进度条颜色
   * @param percentage
   * @returns {string}
   */
  getPercentageColor (percentage) {
    if (Number(percentage) < 80) {
      return '#FF0000'
    } else if (Number(percentage) < 85) {
      return '#FA8072'
    } else if (Number(percentage) < 90) {
      return '#FFA500'
    } else {
      return '#67c23a'
    }
  }

  /**
   * 添加百分号
   * @param val
   * @returns {string}
   */
  addPercent (val) {
    if (val == undefined) {
      return ''
    }
    return val + '%'
  }

  /**
   * 克隆对象
   */
  cloneObj (obj = {}) {
    if (typeof obj !== 'object') {
      return obj
    } else {
      let newObj = obj.constructor === Array ? [] : {}
      for (let i in obj) {
        newObj[i] = typeof obj[i] === 'object' ? this.cloneObj(obj[i]) : obj[i]
      }
      return newObj
    }
  }

  getFCOrFBName (state, type) {
    if (Number(state) === 0) {
      if (type === 0) {
        return '预测金额'
      } else if (type === 1) {
        return '预测金额差异'
      }
    } else if (Number(state) === 1) {
      if (type === 0) {
        return '反馈金额'
      } else if (type === 1) {
        return '反馈金额差异'
      }
    }
  }

  /**
   * 通过配置的key获取适配值
   * @param key
   */
  getVariableByKey (key) {
    if (!key) {
      return null
    }
    return getComputedStyle(document.querySelector(':root')).getPropertyValue(key)
  }

  /**
   * 获取PDFJS 地址
   * @param data
   * @param fileName
   * @returns {string}
   */
  getPdfJsUrl (data, fileName) {
    const binaryData = [data]
    // 获取blob链接
    let pdfUrl = window.URL.createObjectURL(
      new Blob(binaryData, { type: 'application/pdf' })
    )
    let baseUrl = 'static/pdfjs/web/viewer.html?file=' + pdfUrl + '&downloadName=' + fileName
    return baseUrl
  }
}

/**
 * 计算时间
 * @param date 时间
 * @param deviation 偏差
 * @param lastDay 当月最后一天
 * @param type 类型 y：年 m：月 d：日
 * @returns {*}
 */
let calDate = function (date, deviation, lastDay = 0, type) {
  if (deviation != 0) {
    if (parseInt(date) == 1 && type == 'm') {
      return 12
    }
    let tempDate = parseInt(date) + deviation
    if (lastDay != 0) {
      if (tempDate > 0 && tempDate <= lastDay) {
        return tempDate
      }
    } else {
      if (tempDate > 0) {
        return tempDate
      }
    }
  }
  return date
}

/**
 * 下转页面
 * @param path 路径，只需要传功能配置路径
 * @param params 参数
 */
let goto = function (path, params = {}) {
  this.$router.push({
    path: path,
    query: params
  })
}

// 全局导出 excel
Vue.prototype.somExportExcel = elExportExcel
Vue.prototype.goto = goto
Vue.prototype.$somms = new Methods()
