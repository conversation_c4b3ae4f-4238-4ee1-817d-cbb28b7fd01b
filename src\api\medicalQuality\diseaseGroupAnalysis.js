import request from '@/utils/request'

export function getList (params) {
  return request({
    url: '/diseaseGroupAnalysisController/getList',
    method: 'post',
    data: params
  })
}

export function getInfo (params) {
  return request({
    url: '/diseaseGroupAnalysisController/getInfo',
    method: 'post',
    data: params
  })
}

export function getInGroup (params) {
  return request({
    url: '/diseaseGroupAnalysisController/getInGroup',
    method: 'post',
    data: params
  })
}

export function getCostPay (params) {
  return request({
    url: '/diseaseGroupAnalysisController/getCostPay',
    method: 'post',
    data: params
  })
}

export function getCost (params) {
  return request({
    url: '/diseaseGroupAnalysisController/getCost',
    method: 'post',
    data: params
  })
}
