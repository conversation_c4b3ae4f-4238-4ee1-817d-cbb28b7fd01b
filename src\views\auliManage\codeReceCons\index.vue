<template>
  <div class="app-container">
    <drg-container :headerPercent="10">
        <template slot="header">
         <!--   <el-card class="filter-container"  style="height:110px;overflow-y:auto;"> -->
                <drg-title-line title="查询条件" />
                <el-form :inline="true" :model="listQuery" size="mini" style="height: 60%">
                      <el-form-item label="出院时间" >
                        <el-date-picker
                          v-model="listQuery.cysj"
                          type="daterange"
                          size="mini"
                          unlink-panels
                          range-separator="-"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          value-format="yyyy-MM-dd"
                          @change="dateChangeCysj"
                          :picker-options="pickerOptions">
                        </el-date-picker>
                      </el-form-item>
                      <el-form-item label="出院科别" class="som-el-form-item-margin-left" v-if="this.$somms.hasDeptRole()">
                        <div id="b13c" style="height: 30px;border-radius: 5px">
                          <select-tree v-model="listQuery.b16c" :options="depts" :props="defaultProps" placeholder="请选择出院科别"/>
                        </div>
                      </el-form-item>
                      <el-form-item label="病案号" class="som-el-form-item-margin-left">
                        <el-input v-model="listQuery.a48" placeholder="请输入病案号"></el-input>
                      </el-form-item >
                     <el-form-item class="som-el-form-item-margin-left">
                           <el-button
                             @click="handleSearchList()"
                             type="primary"
                             size="mini">
                             查询结果
                           </el-button>
                       <el-popconfirm
                         confirm-button-text='确定'
                         cancel-button-text='导出全部'
                         icon="el-icon-info"
                         icon-color="red"
                         title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
                         <el-button slot="reference" type="success">导出Excel</el-button>
                       </el-popconfirm>
                             <el-button @click="refresh">重置</el-button>
                         </el-form-item>
                      </el-form>
         <!--   </el-card> -->
        </template>
    <template slot="content">
      <drg-title-line title="编码资源消耗情况" />
      <div style="height:30%;">
        <el-row :gutter="0" style="height: 100%" class="el-card is-always-shadow">
          <el-col :span="6" style="height: 100%">
            <div id="errorCount1"  style="height: 100%;width: 100%"></div>
          </el-col>
          <el-col :span="6" style="height: 100%">
            <div id="errorCount2"  style="height: 100%;width: 100%"></div>
          </el-col>
          <el-col :span="6" style="height: 100%">
            <div id="errorCount3"  style="height: 100%;width: 100%"></div>
          </el-col>
          <el-col :span="6" style="height: 100%">
            <div id="errorCount4"  style="height: 100%;width: 100%"></div>
          </el-col>
        </el-row>
      </div>
      <div style="height: 60%;width: 100%;">
        <el-table ref="codeResourceConsumptionListTable"
                  id="codeResourceConsumptionTable"
                  :header-cell-style="{'text-align':'center'}"
                  size="mini"
                  height="100%"
                  stripe
                  :data="list"
                  v-loading="listLoading"
                  border>
          <el-table-column
            label="序号"
            align="right"
            type="index"
            width="50">
          </el-table-column>
          <el-table-column label="病案号" prop="patientId" align="left" width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.patientId | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="姓名" prop="name" align="left" width="60" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.name | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="出院科室" prop="priOutHosDeptName" align="left" width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="主诊断" prop="mainDiagnoseCodeAndName" align="left" width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{ scope.row.mainDiagnoseCodeAndName | formatIsEmpty }}</template>
          </el-table-column>
          <el-table-column label="主操作" prop="mainOperativeCodeAndName" align="left" width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.mainOperativeCodeAndName | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="主要诊断资源消耗是否合理"  prop="mainDiagIsChoErr" align="center" :show-overflow-tooltip='true' width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.mainDiagIsChoErr=='0'">
                 <el-button size="mini" type="success" icon="el-icon-check"  circle></el-button>
              </span>
              <span v-else>
                 <el-button size="mini" type="danger" icon="el-icon-close" circle></el-button>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="主要操作资源消耗是否合理"  prop="mainOprnIsChoErr" align="center" :show-overflow-tooltip='true' width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.mainOprnIsChoErr=='0'">
                 <el-button size="mini" type="success" icon="el-icon-check"  circle></el-button>
              </span>
              <span v-else>
                 <el-button size="mini" type="danger" icon="el-icon-close" circle></el-button>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="主要疾病诊断应为" prop="newMainDiagnoseCodeAndName" align="left" width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.newMainDiagnoseCodeAndName | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="主要手术操作应为" prop="newMainOperativeCodeAndName" align="left" width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.newMainOperativeCodeAndName | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="当前DRG入组" align="left" prop="drgCodeAndName"  :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.drgCodeAndName | formatIsEmpty  }}</template>
          </el-table-column>
          <el-table-column label="建议DRG入组" align="left" prop="newDrgCodeAndName" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.newDrgCodeAndName | formatIsEmpty }}</template>
          </el-table-column>
          <el-table-column label="当前DIP入组" align="left" prop="dipCodeAndName" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.dipCodeAndName  | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="建议DIP入组" align="left" prop="newDipCodeAndName" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.newDipCodeAndName | formatIsEmpty }}</template>
          </el-table-column>
          <el-table-column label="查看明细" align="center" width="80">
            <template slot-scope="scope">
              <div @click="handleShowMedicalDetail(scope.$index, scope.row)" style="width: 60px;height: 20px;background-color: #003399;
              color:white;font-weight:bold;border-radius: 3px;cursor: pointer">明细
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          layout="total, sizes,prev, pager, next,jumper"
          :page-size="listQuery.pageSize"
          :page-sizes="[200,1000,5000,10000]"
          :current-page.sync="listQuery.pageNum"
          :total="total">
        </el-pagination>
      </div>
    </template>
        </drg-container>
  </div>
</template>
<script>
import SelectTree from '@/components/SelectTree/index'
import { queryDataIsuue, querySelectTreeAndSelectList } from '@/api/common/drgCommon'
import { fetchList as queryPageData, getCountInfo } from '@/api/medicalQuality/codeResourceConsumption'
import { elExportExcel } from '@/utils/exportExcel'
import { handleNumber } from '@/utils/common'
import echarts from 'echarts'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  a48: null,
  b16c: null,
  cysj: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date
}
export default {
  name: 'codeReceCons',
  components: { SelectTree },
  inject: ['reload'],
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      list: null,
      total: null,
      cy_start_date: null,
      cy_end_date: null,
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      b16c: null,
      tableHeight: 0,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '--'
      }
    },
    formatValidateResult (value) {
      if (value) {
        return value
      } else {
        return '√'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.codeResourceConsumptionListTable.$el.offsetTop：表格距离浏览器的高度
      // 30表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.codeResourceConsumptionListTable.$el.offsetTop - 30
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.codeResourceConsumptionListTable.$el.offsetTop - 30
      }
    })
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
        this.getCount()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.cy_start_date
      this.submitListQuery.cy_end_date = this.cy_end_date
      this.submitListQuery.a48 = this.listQuery.a48
      this.submitListQuery.b16c = this.listQuery.b16c
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.codeResourceConsumptionListTable.$children, document.getElementById('codeResourceConsumptionTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '编码资源消耗')
    },
    getCount () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.cy_start_date
      this.submitListQuery.cy_end_date = this.cy_end_date
      this.submitListQuery.b16c = this.listQuery.b16c
      getCountInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        let legendData1 = ['不符（人） 占比(%)', '符合（人） 占比(%)']
        let legendData2 = ['不符（组） 占比(%)', '符合（组） 占比(%)']
        let graphicLeft2 = ''
        let type = getComputedStyle(document.documentElement).getPropertyValue('--type')
        if (type == 2) {
          // 1920
          graphicLeft2 = '23%'
        } else if (type == 3) {
          graphicLeft2 = '20.5%'
        }
        //
        let graphicLeftTitle1 = '不符病案数'
        let graphicLeftTitle2 = '不符病案数'
        let graphicLeftTitle3 = '不符DRGs组\n数'
        let graphicLeftTitle4 = '不符DIP组\n数'
        let totalMedicalRightNum = response.data.totalMedicalNum
        let diagnoseErrorNum = handleNumber(Number(response.data.diagnoseErrorNum))
        let diagnoseRightrNum = handleNumber(Number(response.data.totalMedicalNum) - Number(response.data.diagnoseErrorNum))
        let operativeErrorNum = handleNumber(Number(response.data.operativeErrorNum))
        let operativeRightNum = handleNumber(Number(response.data.totalMedicalNum) - Number(response.data.operativeErrorNum))
        let drgErrorNum = handleNumber(Number(response.data.drgErrorNum))
        let drgRightNum = handleNumber(Number(response.data.totalDrgNum) - Number(response.data.drgErrorNum))
        let dipErrorNum = handleNumber(Number(response.data.dipErrorNum))
        let dipRightNum = handleNumber(Number(response.data.totalDipNum) - Number(response.data.dipErrorNum))
        let seriesData1 = [
          { value: diagnoseErrorNum, name: '不符（人） 占比(%)' },
          { value: diagnoseRightrNum, name: '符合（人） 占比(%)' }
        ]
        let seriesData2 = [
          { value: operativeErrorNum, name: '不符（人） 占比(%)' },
          { value: operativeRightNum, name: '符合（人） 占比(%)' }
        ]
        let seriesData3 = [
          { value: drgErrorNum, name: '不符（组） 占比(%)' },
          { value: drgRightNum, name: '符合（组） 占比(%)' }
        ]
        let seriesData4 = [
          { value: dipErrorNum, name: '不符（组） 占比(%)' },
          { value: dipRightNum, name: '符合（组） 占比(%)' }
        ]
        this.pieCount('errorCount1', legendData1, graphicLeft2, graphicLeftTitle1, response.data.diagnoseErrorNum, response.data.totalMedicalNum, seriesData1)
        this.pieCount('errorCount2', legendData1, graphicLeft2, graphicLeftTitle2, response.data.operativeErrorNum, response.data.totalMedicalNum, seriesData2)
        this.pieCount('errorCount3', legendData2, graphicLeft2, graphicLeftTitle3, response.data.drgErrorNum, response.data.totalDrgNum, seriesData3)
        this.pieCount('errorCount4', legendData2, graphicLeft2, graphicLeftTitle4, response.data.dipErrorNum, response.data.totalDipNum, seriesData4)
      })
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.getList()
      this.getCount()
    },
    pieCount (id, legendData, graphicLeft2, graphicLeftTitle, errorNum, totalNum, seriesData) {
      let graphicLeft1 = '20%'
      let fontSize = 30
      if (errorNum.length == 1) {
        graphicLeft1 = '27%'
        fontSize = 30
      } else if (errorNum.length == 2) {
        graphicLeft1 = '25%'
        fontSize = 30
      } else if (errorNum.length == 3) {
        graphicLeft1 = '23%'
        fontSize = 30
      } else if (errorNum.length == 4) {
        graphicLeft1 = '20%'
        fontSize = 30
      } else if (errorNum.length == 5) {
        graphicLeft1 = '17%'
        fontSize = 25
      } else if (errorNum.length >= 6) {
        graphicLeft1 = '17%'
        fontSize = 20
      }
      let colors = ['#FF0000FF', '#87CEFA']
      let option = {
        title: [
          { text: handleNumber(errorNum), left: '60%', top: '45%', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 18 } },
          { text: Number(totalNum) == 0 ? 0 : (((Number(errorNum) / Number(totalNum)) * 100).toFixed(1)), left: '80%', top: '45%', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 18 } },
          { text: handleNumber(Number(totalNum) - Number(errorNum)), left: '60%', top: '70%', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 18 } },
          { text: Number(totalNum) == 0 ? 0 : ((100 - ((Number(errorNum) / Number(totalNum)) * 100)).toFixed(1)), left: '80%', top: '70%', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 18 } }
        ],
        legend: {
          orient: 'vertical',
          left: '55%',
          top: 'center',
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 40,
          data: legendData
        },
        graphic: [
          {
            type: 'text',
            left: graphicLeft1, // 根据数据的长度，动态变化
            top: '40%',
            style: {
              text: errorNum,
              textAlign: 'center',
              fill: '#000',
              fontSize: fontSize
            }
          },
          {
            type: 'text',
            top: '55%',
            left: graphicLeft2, // 根据数据的长度，动态变化
            style: {
              text: graphicLeftTitle,
              textAlign: 'center',
              fill: '#999999',
              fontSize: 12
            }
          }
        ],
        series: [
          {
            type: 'pie',
            center: ['30%', '50%'],
            radius: ['50%', '65%'],
            labelLine: {
              show: false
            },
            label: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % 2]
                }
              }
            },
            data: seriesData
          }
        ]
      }
      let errorCountEchart = echarts.getInstanceByDom(document.getElementById(id))
      if (errorCountEchart) {
        errorCountEchart.clear()
      } else {
        errorCountEchart = echarts.init(document.getElementById(id))
      }
      errorCountEchart.setOption(option)
      errorCountEchart.dispatchAction({ type: 'highlight', seriesIndex: 0, dataIndex: 0 })// 设置默认选中高亮部分
      errorCountEchart.setOption(option)
      window.addEventListener('resize', () => {
        errorCountEchart.resize()
      })
      return errorCountEchart
    },
    handleShowMedicalDetail (index, row) {
      this.$router.push({ path: '/setlListManage/setlListDetail', query: { id: row.id, activeMenu: '3', k00: row.k00 } })
    },
    exportExcel () {
      let tableId = 'codeResourceConsumptionTable'
      let fileName = '编码资源消耗'
      elExportExcel(tableId, fileName)
    },
    refresh () {
      this.reload()
    }
  }
}
</script>
<style></style>
