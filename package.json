{"name": "drg-web", "version": "1.0.0", "description": "drg后台管理前端", "author": "macro", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build": "node build/build.js", "lint": "eslint --fix --ext .js,.vue src"}, "dependencies": {"ajv": "^6.0.0", "axios": "^0.18.0", "echarts": "^4.9.0", "echarts-gl": "^1.1.1", "element-ui": "^2.15.5", "babel-eslint": "9.0.0", "eslint": "^5.8.0", "eslint-config-standard": "13.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-node": "8.0.0", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "3.0.1", "eslint-plugin-vue": "6.2.2", "file-saver": "^2.0.2", "intro.js": "^6.0.0", "js-cookie": "^2.2.0", "mathjs": "^12.4.1", "moment": "^2.29.1", "normalize.css": "^8.0.0", "nprogress": "^0.2.0", "qs": "^6.11.0", "screenfull": "^5.0.2", "sortablejs": "^1.15.0", "v-charts": "^1.19.0", "v-distpicker": "^1.0.20", "vue": "^2.5.2", "vue-fullscreen": "^2.1.6", "vue-i18n": "^8.15.5", "vue-router": "^3.0.1", "vuex": "^3.0.1", "xlsx": "^0.15.6"}, "devDependencies": {"@babel/plugin-transform-spread": "^7.24.1", "autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "code-inspector-plugin": "^0.20.12", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "import-glob-loader": "^1.1.0", "node-notifier": "^5.1.2", "node-sass": "^4.9.0", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "prettier": "^2.8.7", "rimraf": "^2.6.0", "sass-loader": "^7.0.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "svg-sprite-loader": "^3.7.3", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}