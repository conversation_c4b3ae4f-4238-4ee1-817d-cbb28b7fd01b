<template>
  <div id="tags-view-container" class="tags-view-container" v-if="hiddenTags">
    <!--    <scroll-pane ref="scrollPane" class="tags-view-wrapper">-->
    <!--      <router-link-->
    <!--        v-for="tag in visitedViews"-->
    <!--        ref="tag"-->
    <!--        :key="tag.path"-->
    <!--        :class="isActive(tag)?'active':''"-->
    <!--        :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"-->
    <!--        tag="span"-->
    <!--        class="tags-view-item"-->
    <!--        @click.middle.native="closeSelectedTag(tag)"-->
    <!--        @contextmenu.prevent.native="openMenu(tag,$event)"-->
    <!--      >-->
    <!--        {{ tag.profttl }}-->
    <!--        <span v-if="notMain(tag.path)" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag, true)" />-->
    <!--      </router-link>-->
    <el-tabs v-model="curTab" @tab-remove="closeSelectedTag" @tab-click="toPage"
             @contextmenu.prevent.native="openMenu($event)">
      <el-tab-pane :label="tag.profttl"
                   :name="tag.path"
                   :key="tag.path"
                   v-for="tag in visitedViews"
                   class="tags-view-item"
                   @mouseover.native="mouseover"
                   :closable="tag.path !== '/home'">
      </el-tab-pane>
    </el-tabs>
    <!--    </scroll-pane>-->
    <ul v-show="visible" :style="{left:left+'px',top:top+'px'}" class="contextmenu">
      <!--      <li v-if="!(selectedTag.meta&&selectedTag.meta.affix)" @click="closeSelectedTag(selectedTag)">关闭</li>-->
      <li @click="closeOthersTags">关闭其他</li>
      <li @click="closeAllTags(selectedTag)">关闭所有</li>
    </ul>
  </div>
</template>

<script>

/**
 * this.hiddenTags 标识显示当前 tab
 * 如果想保留工作台也是tab需要修改以下内容：
 *  1、data --> hiddenTags: true
 *  2、addTags() 中的 this.hiddenTags = true;
 *  3、moveToCurrentTag() 中的 this.hiddenTags = true;
 */
export default {
  name: 'TagsView',
  components: {},
  data() {
    return {
      visible: false,
      top: -25,
      left: 0,
      selectedTag: {},
      affixTags: [],
      hiddenTags: true,
      curTab: '/home'
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    }
  },
  watch: {
    $route(route) {
      this.addTags()
      // this.moveToCurrentTag()
      this.curTab = route.path
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  mounted() {
    this.addTags()
  },
  methods: {
    mouseover(e) {
      console.log(e)
    },
    toPage(dom) {
      let tag = this.visitedViews.find(view => view.path === dom.name)
      this.$router.push({path: tag.path, query: tag.query, fullPath: tag.fullPath})
    },
    notMain(path) {
      return !(path === '/home')
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    addTags() {
      const {name} = this.$route

      if (name) {
        if (this.$route.name != 'home') {
          this.hiddenTags = true
        }
        this.$store.dispatch('tagsView/addView', this.$route)
      }
    },
    moveToCurrentTag() {
      const tags = this.$refs.tag
      this.$nextTick(() => {
        for (const tag of tags) {
          if (this.$route.name == 'home') {
            this.hiddenTags = true
          }
          if (tag.to.path === this.$route.path) {
            this.$refs.scrollPane.moveToTarget(tag)

            // when query is different then update
            if (tag.to.fullPath !== this.$route.fullPath) {
              this.$store.dispatch('tagsView/updateVisitedView', this.$route)
            }
            break
          }
        }
      })
    },
    closeSelectedTag(path, flag = false) {
      let view = this.visitedViews.find(view => view.path === path)
      this.curDelView(view)
    },
    curDelView(view) {
      this.$store.dispatch('tagsView/delView', view).then(({visitedViews}) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag)
      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {
        // this.moveToCurrentTag()
      })
    },
    closeAllTags(view) {
      this.$store.dispatch('tagsView/delAllViews').then(({visitedViews}) => {
        if (this.affixTags.some(tag => tag.path === view.path)) {
          return
        }
        this.toLastView(visitedViews, view)
      })
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push({path: latestView.path, query: latestView.query})
      } else {
        this.hiddenTags = true
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        if (view.name === '工作台') {
          // to reload home page
          this.$router.replace({path: '/redirect' + view.fullPath})
        } else {
          this.$router.push('/')
        }
      }
    },
    openMenu(e) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const offsetWidth = this.$el.offsetWidth // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = e.clientX - offsetLeft + 15 // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }

      this.visible = true
      this.selectedTag = this.visitedViews.find(view => view.path === this.curTab)

      if (this.selectedTag == '工作台') {
        this.hiddenTags = false
      }
    },
    closeMenu() {
      this.visible = false
    },
    initRouters(routers, to) {
      if (routers.length > 0) {
        routers.map(router => {
          if (router.path === to.path) {
            router.active = true
          } else {
            router.active = false
          }
          if (router.children && router.children.length > 0) {
            initRouters(router.children, to)
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tags-view-container {
  padding: 0 10px;
  height: 40px;
  width: 100%;
  background: #fff;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);

  .tags-view-wrapper {
    padding: 0 0 0 10px;

    .tags-view-item {
      display: inline-block;
      //display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      cursor: pointer;
      height: 34px;
      line-height: 34px;
      border-right: 1px solid #d8dce5;
      color: #495060;
      background: #fff;
      padding: 0 8px;
      font-size: 12px;
      //margin-left: 5px;
      //margin-top: 2px;
      //border-radius: 2px;
      //&:first-of-type {
      //  margin-left: 15px;
      //}
      &:last-of-type {
        margin-right: 15px;
      }

      &.active {
        background-color: #4e73df;
        color: #fff;
        border-color: #4e73df;
        //&::before {
        //  content: '';
        //  background: #fff;
        //  display: inline-block;
        //  width: 8px;
        //  height: 8px;
        //  border-radius: 50%;
        //  position: relative;
        //  margin-right: 2px;
        //}
      }
    }
  }

  .contextmenu {
    margin-top: -5px;
    width: 100px;
    background: #fff;
    z-index: 3000;
    position: relative;
    list-style-type: none;
    padding: 0px 0px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);

    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }
}
</style>

<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .el-icon-close {
      width: 16px;
      height: 16px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      transition: all .3s cubic-bezier(.645, .045, .355, 1);
      transform-origin: 100% 50%;

      &:before {
        transform: scale(.6);
        display: inline-block;
        vertical-align: -3px;
      }

      &:hover {
        background-color: #b4bccc;
        color: #fff;
      }
    }
  }
}
</style>
