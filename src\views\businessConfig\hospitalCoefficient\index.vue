<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             headerTitle="查询条件"
             @query="queryHC">
      <template slot="extendFormItems">
        <el-form-item label="医疗机构">
          <el-input v-model="queryForm.hospital" disabled />
        </el-form-item>
      </template>
      <template slot="buttons">
        <el-button
          size="mini"
          type="primary"
          @click="addMessagePrompt()">新增</el-button>
      </template>
      <template slot="containerContent">
        <el-table :data="tableData"
                  v-loading="tableLoading"
                  style="width: 100%; height:100%">
          <el-table-column prop="hospitalId" label="医院"  ></el-table-column>
          <el-table-column prop="adjm_cof" label="系数" ></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-edit"
                type="primary"
                circle
                @click="handleEdit(scope.$index, scope.row)"></el-button>
            </template>
          </el-table-column>
          <el-table-column label="删除">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-delete"
                circle slot="reference"
                @click="deleteHC(scope.$index, scope.row)"></el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog
          :title="profttl"
          ref="editForm"
          width="30%"
          :visible.sync="editVisible">
          <el-form :model="editForm" :rules="dataRule" ref="dataForm" size="mini" >
            <el-row>
              <el-col :span="24">
                <el-form-item
                  prop="hospitalId"
                  label="医院id">
                  <el-input :disabled="!this.addMessage" v-model="editForm.hospitalId" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  prop="adjm_cof"
                  label="系数">
                  <el-input  placeholder="系数" v-model="editForm.adjm_cof" ></el-input>
                  <!--   <i class="el-icon-question" style="position: absolute; right: -25px;bottom: 6px;cursor: pointer" @click="configQuestion"></i> -->
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <template #footer>
          <span class="dialog-footer">
            <el-button @click="editCancel" size="mini" >取 消</el-button>
            <el-button type="primary" size="mini" @click="insertOrUpdateHC()" >保 存</el-button>
          </span>
          </template>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>

<script>

import { deleteHC, insertHC, queryHC, updateHC } from '../../../api/dataConfig/hospitalCoefficient'

export default {
  name: 'hospitalCoefficient',
  data: () => ({
    queryForm: {
      hospitalId: '',
      hospital: ''
    },
    tableData: [],
    tableLoading: false,
    editVisible: false,
    addMessage: false,
    profttl: '',
    editForm: {
      hospitalId: '',
      adjm_cof: ''
    },
    dataRule: {
      hospitalId: [{ required: true, message: '医院id不能为空', trigger: 'blur' }],
      adjm_cof: [{ required: true, message: '系数不能为空', trigger: 'blur' }]
    }
  }),
  mounted () {
    this.queryForm.hospital = this.$store.getters.nickname
    this.queryHC()
  },
  methods: {
    queryHC () {
      this.tableLoading = true
      queryHC(this.queryForm).then((result) => {
        this.tableData = result.data
        this.tableLoading = false
      })
    },
    deleteHC (index, row) {
      let params = {}
      params.hospitalId = row.hospitalId
      this.$confirm('是否确认删除？')
        .then(_ => {
          deleteHC(params).then(res => {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.queryHC()
          })
        })
    },
    addMessagePrompt () {
      this.editVisible = true
      this.addMessage = true
      this.profttl = '新增'
      this.editForm.hospitalId = '210021'
      this.editForm.adjm_cof = ''
    },
    handleEdit (index, row) {
      this.profttl = '编辑'
      this.editForm.hospitalId = '210021'
      this.editForm.adjm_cof = row.adjm_cof
      this.editVisible = true
      this.addMessage = false
    },
    // insertHC(){
    //   let params = {}
    //   insertHC(params).then(res => {
    //     this.$confirm({
    //          message:'新增成功',
    //          type:'success'
    //     });
    //     this.queryHC()
    //   })
    // },
    // updateHC(){
    //   let params = {}
    //   updateHC(params).then(res => {
    //     this.$confirm({
    //          message:'修改成功',
    //          type:'success'
    //     })
    //   })
    // },
    insertOrUpdateHC () {
      if (this.addMessage) {
        insertHC(this.editForm).then(result => {
          if (result.code == 200) {
            this.$message({
              message: '新增成功',
              type: 'success'
            })
            this.editVisible = false
            this.queryHC()
          } else {
            return false
          }
        })
      } else {
        updateHC(this.editForm).then(result => {
          if (result.code == 200) {
            this.$message({
              message: '修改成功',
              type: 'success'
            })
            this.editVisible = false
            this.queryHC()
          }
        })
      }
    },
    editCancel () {
      this.$refs['dataForm'].resetFields()
      this.$confirm('关闭后所做修改将不会保存,是否确认关闭？')
        .then(_ => {
          this.editVisible = false
        })
        .catch(_ => {
          this.editVisible = false
        })
    },
    resetFields () {
      if (!this.model) {
        console.warn('[Element Warn][Form]model is required for resetFields to work.')
        return
      }
      this.fields.forEach(fld => {
        fld.resetField()
      })
    }

  }
}
</script>

<style scoped>

</style>
