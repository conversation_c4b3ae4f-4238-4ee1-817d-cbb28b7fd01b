import request from '../../utils/request'

export function deacidizing () {
  return request({
    url: '/MedicalSettleCheckController/reduction',
    method: 'post'
  })
}

export function QueryMedical (params) {
  return request({
    url: '/MedicalSettleCheckController/QueryMedical',
    method: 'post',
    params: params
  })
}
export function QueryQualityList (params) {
  return request({
    url: '/SettleCheckController/QuerySettle',
    method: 'post',
    params: params
  })
}

export function updateMedical (params) {
  return request({
    url: '/MedicalSettleCheckController/updateStartFlag',
    method: 'post',
    params: params
  })
}

export function updateInspection (params) {
  return request({
    url: '/SettleCheckController/update',
    method: 'post',
    params: params
  })
}

export function updateAllMedical (params) {
  return request({
    url: '/MedicalSettleCheckController/updateAllMedical',
    method: 'post',
    params: params
  })
}

export function insertMedical (params) {
  return request({
    url: '/MedicalSettleCheckController/insertMedical',
    method: 'post',
    params: params
  })
}

export function deleteMedical (params) {
  return request({
    url: '/MedicalSettleCheckController/deleteMedical',
    method: 'post',
    params: params
  })
}

export function updateStart (params) {
  return request({
    url: '/MedicalSettleCheckController/updateStart',
    method: 'post',
    params: params
  })
}

export function updateOther (params) {
  return request({
    url: '/MedicalSettleCheckController/update',
    method: 'post',
    params: params
  })
}
