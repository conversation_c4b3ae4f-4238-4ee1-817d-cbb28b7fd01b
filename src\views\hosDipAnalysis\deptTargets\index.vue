<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-hos-dept
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="科室指标"
             :container="true"
             @query="handleSearchList" @reset="refresh">

      <template slot="buttons">
        <el-popconfirm
          confirm-button-text='确定'
          cancel-button-text='导出全部'
          icon="el-icon-info"
          icon-color="red"
          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>

      <template slot="containerContent">
        <div style="height:30%">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="24" style="position:relative;height: 100%">
              <div  class="rankSelect">
                <span style="font-size: 12px;">排名指标：</span>
                <el-select v-model="rankSelect" placeholder="请选择排名指标" size="mini" @change="changeSelectDeptRank">
                  <el-option
                    v-for="item in rankList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </div>
              <div id="deptChart" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="height: 69%">
          <el-table ref="dipDeptTable"
                    id="deptTable"
                    :header-cell-style="{'text-align':'center'}"
                    size="mini"
                    height="100%"
                    stripe
                    :data="list"
                    v-loading="listLoading"
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column label="序号"
                             type="index"
                             width="50"
                             align="right">
            </el-table-column>
            <el-table-column label="出院科室编码" prop="priOutHosDeptCode" align="right"  v-if="false">
              <template slot-scope="scope">{{scope.row.priOutHosDeptCode | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="出院科室名称" prop="priOutHosDeptName" align="left" :show-overflow-tooltip="true" >
              <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="病案总数" prop="medicalRecordNum" align="right" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.medicalRecordNum)>0" class='skip' @click="queryMedicalTotalNum(scope.row)">
                  {{scope.row.medicalRecordNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.medicalRecordNum)==0" style="color:#000000">
                  {{scope.row.medicalRecordNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="入组病案数" prop="groupNum" align="right" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.groupNum)>0" class='skip' @click="queryGroupNum(scope.row)">
                  {{scope.row.groupNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.groupNum)==0" style="color:#000000">
                  {{scope.row.groupNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="未入组病案数" prop="noGroupNum" align="right" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.noGroupNum)>0" class='skip' @click="queryNoGroupNum(scope.row)">
                  {{scope.row.noGroupNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.noGroupNum)==0" style="color:#000000">
                  {{scope.row.noGroupNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="DIP组数" prop="dipGroupNum" align="right" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.dipGroupNum)>0" class='skip' @click="queryDipGroupNum(scope.row)">
                  {{scope.row.dipGroupNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.dipGroupNum)==0" style="color:#000000">
                  {{scope.row.dipGroupNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
<!--            <el-table-column label="DIP总权重（本院）" prop="totalAreaWeight" align="right" width="135" :show-overflow-tooltip="true" >-->
<!--              <template slot-scope="scope">{{scope.row.totalAreaWeight | formatIsEmpty}}</template>-->
<!--            </el-table-column>-->
            <el-table-column label="DIP总权重（区域）" prop="totalHosWeight" align="right" width="135" :show-overflow-tooltip="true" >
              <template slot-scope="scope">{{scope.row.totalHosWeight | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院日" prop="avgDays" align="right" width="100" >
              <template slot-scope="scope">{{scope.row.avgDays | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院费用" prop="avgCost" align="right" fixed="right" width="120" >
              <template slot-scope="scope">{{scope.row.avgCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="预测金额差异" prop="profitlossTotal" align="right" fixed="right" width="120" >
              <template slot-scope="scope">{{scope.row.profitlossTotal | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="时间消耗指数"  align="right" width="100" >
              <template slot-scope="scope">{{scope.row.timeIndex | formatIsEmpty}}</template>no
            </el-table-column>
            <el-table-column label="费用消耗指数"  align="right" width="100" >
              <template slot-scope="scope">{{scope.row.costIndex | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="药品费占比" prop="medicalCostRate" align="right" width="120"  :show-overflow-tooltip='true' >
              <template slot-scope="scope">{{scope.row.medicalCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="耗材费占比" prop="materialCostRate" align="right" width="120"  :show-overflow-tooltip='true' >
              <template slot-scope="scope">{{scope.row.materialCostRate | formatIsEmpty}}</template>
            </el-table-column>
          </el-table>
        </div>
        <el-dialog v-som-dialog-drag title="指数详情" :visible.sync="showDetail" width="70%">
          <div style="height: 100%;">
            <el-table :data="detailData"
                      ref="detailRef"
                      :header-cell-style="{'text-align':'center'}"
                      height="100%"
                      border>
              <el-table-column label="dip编码" align="left">
                <template slot-scope="scope">{{scope.row.dipCodg}}</template>
              </el-table-column>
              <el-table-column label="平均住院天数（院内）" align="left" v-if="this.detailType == 1" :key="2">
                <template slot-scope="scope">{{scope.row.avgInHosDays}}</template>
              </el-table-column>
              <el-table-column label="平均住院天数（标杆）" align="left" v-if="this.detailType == 1" :key="3">
                <template slot-scope="scope">{{scope.row.dipInHosDays}}</template>
              </el-table-column>
              <el-table-column label="时间指标" align="left" v-if="this.detailType == 1" :key="4">
                <template slot-scope="scope">{{scope.row.timeIndex}}</template>
              </el-table-column>
              <el-table-column label="平均费用（院内）" align="left" v-if="this.detailType == 2" :key="2">
                <template slot-scope="scope">{{scope.row.avgInHosCost}}</template>
              </el-table-column>
              <el-table-column label="平均费用（标杆）" align="left" v-if="this.detailType == 2" :key="3">
                <template slot-scope="scope">{{scope.row.dipInHosCost}}</template>
              </el-table-column>
              <el-table-column label="费用指标" align="left" v-if="this.detailType == 2" :key="4">
                <template slot-scope="scope">{{scope.row.costIndex}}</template>
              </el-table-column>
            </el-table>
          </div>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, querySelectTreeAndSelectList, queryLikeDipGroupByPram } from '@/api/common/drgCommon'
import { fetchList as queryPageData, getCountInfo, queryConsumptionIndex } from '@/api/dipBusiness/dipDeptIndex'
import { elExportExcel } from '@/utils/exportExcel'
import echarts from 'echarts'

const defaultListQuery = {
  pageNum: 1,
  pageDetailNum: 1,
  pageSize: 200,
  pageDetailSize: 50,
  b16c: null,
  cysj: null,
  dipGroup: '',
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date
}
export default {
  name: 'deptTargets',
  components: { },
  inject: ['reload'],
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      list: null,
      tempList: [],
      total: null,
      detailTotal: null,
      cy_start_date: null,
      cy_end_date: null,
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      rankSelect: '3', // 默认权重排名
      deptName: null,
      tableHeight: 0,
      b16c: null,
      rankList: [
        { value: '0', label: '总病案数排名' },
        { value: '1', label: '入组病案数排名' },
        { value: '2', label: '未入组病案数' },
        { value: '3', label: 'DIP分组组数排名' },
        // { value: '4',label: '总权重排名'},
        { value: '5', label: '总平均住院日排名' },
        { value: '6', label: '入组平均住院日排名' },
        { value: '7', label: '总平均住院费用排名' },
        { value: '8', label: '入组平均住院费用排名' },
        { value: '9', label: '时间消耗指数排名' },
        { value: '10', label: '费用消耗指数排名' },
        { value: '11', label: '药品费排名' },
        { value: '12', label: '耗材费排名' }
      ],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      showDetail: false,
      detailData: null,
      detailType: '1'
    }
  },
  watch: {
    b16c: function () {
      if (this.listQuery.b16c) {
        this.deptName = document.getElementById('b13c').getElementsByTagName('input')[0].value
      } else {
        this.deptName = null
      }
      this.getList()
      this.getCount()
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.dipDeptTable.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.dipDeptTable.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.dipDeptTable.$el.offsetTop - 35
      }
    })
  },
  updated () {
    this.refreshTable()
  },
  methods: {
    handleSelectionChange (val) {
      if (val.length == 0) {
        this.tempList = []
      } else {
        this.tempList = this.list.filter(data => {
          for (const v of val) {
            if (data.priOutHosDeptCode == v.priOutHosDeptCode) {
              return true
            }
          }
          return false
        })
      }
    },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
        this.getCount()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.dipGroup = this.listQuery.dipGroup
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.tempList = this.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      // console.log
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.dipDeptTable.$children, document.getElementById('deptTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DIP科室指标')
    },
    getAnalysis (row, type) {
      this.showDetail = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = row.priOutHosDeptCode
      this.submitListQuery.pageNum = this.listQuery.pageDetailNum
      this.submitListQuery.pageSize = this.listQuery.pageDetailSize
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      queryConsumptionIndex(this.submitListQuery).then(res => {
        this.detailData = res.data
        this.detailType = type
      })
    },
    getCount () {
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      getCountInfo(this.submitListQuery).then(response => {
        let result = response.data
        // if (result.length > 0) {
          switch (this.rankSelect) {
            case '0':this.getMedicalNum(result); break
            case '1':this.getInGroupNum(result); break
            case '2':this.getNotGroupNum(result); break
            case '3':this.getDipGroupNum(result); break
              // case "4":this.getDipGroupWeight(result); break;
            case '5':this.getAvgDays(result); break
            case '6':this.getInGroupAvgDays(result); break
            case '7':this.getAvgCost(result); break
            case '8':this.getIngroupAvgCost(result); break
            case '9':this.getTimeIndex(result); break
            case '10':this.getCostIndex(result); break
            case '11':this.getMedicalCost(result); break
            case '12':this.getMaterialCost(result); break
            default:this.getDipGroupNum(result)
          }
        // }
      })
    },
    getMedicalNum (result) {
      result.sort(function (o1, o2) {
        return Number(o2.totalMedicalRecordNum) - Number(o1.totalMedicalRecordNum)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 科室名称
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].totalMedicalRecordNum)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].totalMedicalRecordNumRate) // 占比或者标杆值
        }
      }
      let legendData = ['病案总数', '相对全院病案总数占比']
      let profttl = '科室病案总数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = '总病案数'
      let tool2 = '总病案数占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getInGroupNum (result) {
      result.sort(function (o1, o2) {
        return Number(o2.inGroupMedicalNum) - Number(o1.inGroupMedicalNum)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 科室名称
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].inGroupMedicalNum)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].inGroupMedicalNumRate) // 占比或者标杆值
        }
      }
      let legendData = ['入组病案数', '相对全院入组病案数占比']
      let profttl = '科室入组病案数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = '入组病案数'
      let tool2 = '入组病案数占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getNotGroupNum (result) {
      result.sort(function (o1, o2) {
        return Number(o2.notGroupMedicalNum) - Number(o1.notGroupMedicalNum)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 科室名称
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].notGroupMedicalNum)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].notGroupMedicalNumRate) // 占比或者标杆值
        }
      }
      let legendData = ['未入组病案数', '相对全院未组病案数占比']
      let profttl = '科室未组病案数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = '未入组病案数'
      let tool2 = '未入组病案数占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getDipGroupNum (result) {
      result.sort(function (o1, o2) {
        return Number(o2.dipGroupNum) - Number(o1.dipGroupNum)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].dipGroupNum)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].dipGroupNumRate) // 占比或者标杆值
        }
      }
      let legendData = ['DIP分组组数', '相对全院DIP分组组数占比']
      let profttl = '科室DIP分组组数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = 'DIP分组组数'
      let tool2 = 'DIP分组组数占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getDipGroupWeight (result) {
      result.sort(function (o1, o2) {
        return Number(o2.totalDipGroupWeight) - Number(o1.totalDipGroupWeight)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].totalDipGroupWeight)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].totaldipGroupWeightRate) // 占比或者标杆值
        }
      }
      let legendData = ['科室权重', '相对全院总权重占比']
      let profttl = '科室权重排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = '权重'
      let tool2 = '权重占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getAvgDays (result) {
      result.sort(function (o1, o2) {
        return Number(o2.avgDays) - Number(o1.avgDays)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].avgDays)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].hosAvgDays) // 占比或者标杆值
        }
      }
      let legendData = ['平均住院日', '全院平均住院日']
      let profttl = '科室接纳病人平均住院日排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '平均住院日'
      let tool2 = '全院平均住院日'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getInGroupAvgDays (result) {
      result.sort(function (o1, o2) {
        return Number(o2.inGroupAvgDays) - Number(o1.inGroupAvgDays)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].inGroupAvgDays)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].hosInGroupAvgDays) // 占比或者标杆值
        }
      }
      let legendData = ['入组平均住院日', '全院入组平均住院日']
      let profttl = '科室接纳病人入组平均住院日排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '入组平均住院日'
      let tool2 = '全院入组平均住院日'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getAvgCost (result) {
      result.sort(function (o1, o2) {
        return Number(o2.avgCost) - Number(o1.avgCost)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].avgCost)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].hosAvgCost) // 占比或者标杆值
        }
      }
      let legendData = ['平均住院费用', '全院平均住院费用']
      let profttl = '科室接纳病人平均住院费用排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '平均住院费用'
      let tool2 = '全院平均住院费用'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getIngroupAvgCost (result) {
      result.sort(function (o1, o2) {
        return Number(o2.inGroupAvgCost) - Number(o1.inGroupAvgCost)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].inGroupAvgCost)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].hosInGroupAvgCost) // 占比或者标杆值
        }
      }
      let legendData = ['入组平均住院费用', '全院入组平均住院费用']
      let profttl = '科室接纳病人入组平均住院费用排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '入组平均住院费用'
      let tool2 = '全院入组平均住院费用'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getTimeIndex (result) {
      result.sort(function (o1, o2) {
        return Number(o2.timeIndex) - Number(o1.timeIndex)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].timeIndex)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].hosTimeIndex) // 占比或者标杆值
        }
      }
      let legendData = ['时间消耗指数', '全院时间消耗指数']
      let profttl = '科室接纳病人时间消耗指数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '时间消耗指数'
      let tool2 = '全院时间消耗指数'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getCostIndex (result) {
      result.sort(function (o1, o2) {
        return Number(o2.costIndex) - Number(o1.costIndex)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].costIndex)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].hosCostIndex) // 占比或者标杆值
        }
      }
      let legendData = ['费用消耗指数', '全院费用消耗指数']
      let profttl = '科室接纳病人费用消耗指数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '费用消耗指数'
      let tool2 = '全院费用消耗指数'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getMedicalCost (result) {
      result.sort(function (o1, o2) {
        return Number(o2.avgDrugFee) - Number(o1.avgDrugFee)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].avgDrugFee)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].hosAvgMedicalCost) // 占比或者标杆值
        }
      }
      let legendData = ['平均药品费', '全院平均药品费']
      let profttl = '科室接纳病人平均药品费用排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '平均药品费用'
      let tool2 = '全院平均药品费用'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getMaterialCost (result) {
      result.sort(function (o1, o2) {
        return Number(o2.avgMcsFee) - Number(o1.avgMcsFee)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].avgMcsFee)
          xAxisData.push(result[i].priOutHosDeptName)
          lineData.push(result[i].hosAvgMaterialCost) // 占比或者标杆值
        }
      }
      let legendData = ['平均耗材费', '全院平均耗材费用']
      let profttl = '科室接纳病人平均耗材费用排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '平均耗材费用'
      let tool2 = '全院平均耗材费用'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    // 下转详情点击
    queryDipGroupNum (row) {
      this.$router.push({
        path: '/common/queryDrgDetail',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          drCodg: row.drCodg,
          drName: row.drName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '1'
        }
      })
    },
    queryMedicalTotalNum (row) {
      this.$router.push({
        path: '/hosDipAnalysis/groupControlFee',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime
        }
      })
    },
    queryGroupNum (row) {
      this.$router.push({
        path: '/hosDipAnalysis/groupControlFee',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          grpStas: '1'
        }
      })
    },
    queryNoGroupNum (row) {
      this.$router.push({
        path: '/hosDipAnalysis/groupControlFee',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          grpStas: '0'
        }
      })
    },
    queryDetails (row) {
      this.$router.push({
        path: '/auliManage/deptCompar',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          dipGroup: this.listQuery.dipGroup,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '1'
        }
      })
    },
    changeSelectDeptRank (value) {
      this.rankSelect = value
      let deptChart = echarts.getInstanceByDom(document.getElementById('deptChart'))
      deptChart.clear()
      this.getCount()
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
      this.getCount()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.listQuery.pageNum = 1
      if (this.listQuery.b16c) {
        this.deptName = document.getElementById('b13c').getElementsByTagName('input')[0].value
      } else {
        this.deptName = null
      }
      this.getList()
      this.getCount()
    },
    handleResetSearch () {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.rankSelect = '4'
      this.getDataIsuue()
    },
    getChart (barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, flag) {
      let option = {
        title: [{ text: profttl, left: '20', top: '5', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }],
        tooltip: {
          trigger: 'item',
          formatter: function (param) {
            let str = '科室名称：' + xAxisData[param.dataIndex] + '</br>' +
                tool1 + '：' + barData[param.dataIndex] + yLeftUnit + '</br>'
            if (flag == 1) {
              str = str + tool2 + '：' + lineData[param.dataIndex] + '%'
            }
            if (flag == 0) {
              str = str + tool2 + '：' + lineData[param.dataIndex]
            }
            return str
          }
        },
        legend: [{
          data: legendData,
          top: '5',
          left: 'center',
          selectedMode: true
        }],
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            interval: 0,
            fontSize: 10,
            rotate: 40,
            formatter: function (value) {
              return (value.length > 4 ? (value.slice(0, 4) + '..') : value)
            }
          }
        },
        yAxis: [
          { type: 'value',
            position: 'left',
            name: yLeftUnit,
            axisLabel: {
              formatter: function (value) {
                if (value > 10000) {
                  return (Number(value) / 10000).toFixed(1) + '万'
                } else {
                  return Number(value).toFixed(1)
                }
              }
            }
          },
          { type: 'value', position: 'right', name: '单位：' + yRightUnit, max: '100', splitLine: { show: false } }
        ],
        series: [{
          name: legendData[0],
          data: barData,
          type: 'bar',
          color: 'red',
          yAxisIndex: 0,
          label: {
            show: true,
            fontSize: 9,
            position: 'top',
            formatter: function (param) {
              if (param.value > 10000) {
                return (Number(param.value) / 10000).toFixed(1) + '万'
              } else {
                return Number(param.value).toFixed(2)
              }
            }
          },
          itemStyle: {
            color: function (param) {
              if (param.dataIndex < 10) {
                return 'rgba(36,185,179,0.7)'
              } else {
                return 'rgba(40,138,242,0.7)'
              }
            }
          }
        },
        {
          name: legendData[1],
          data: lineData,
          type: 'line',
          symbol: 'circle',
          yAxisIndex: flag,
          label: {
            show: true,
            position: 'top',
            fontSize: 10,
            formatter: function (param) {
              return Number(param.value).toFixed(2)
            }
          },
          itemStyle: {
            color: function (param) {
              if (param.value < 80) {
                return 'rgb(253,94,81)'
              } else {
                return 'rgb(55,88,255)'
              }
            }
          }
        }],
        grid: {
          top: '55',
          bottom: '40',
          left: '60',
          right: '30'
        }
      }
      let deptChart = echarts.getInstanceByDom(document.getElementById('deptChart'))
      if (deptChart) {
        deptChart.clear()
      } else {
        deptChart = echarts.init(document.getElementById('deptChart'))
      }
      deptChart.setOption(option)
      window.addEventListener('resize', () => {
        deptChart.resize()
      })
      return deptChart
    },
    exportExcel () {
      let tableId = 'deptTable'
      let fileName = 'DIP科室指标'
      elExportExcel(tableId, fileName)
    },
    refresh () {
      this.reload()
    },
    refreshTable () {
      if (this.$refs.detailRef) {
        this.$nextTick(() => {
          this.$refs.detailRef.doLayout()
        })
      }
    },

    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    fnDipGroupSelect (item) {
      this.listQuery.dipGroup = item.dipCodg
    }
  }
}
</script>
<style scoped>
  /deep/ .el-table__body td {
    padding: 0;
    height: 32px;
  }
  /deep/ .el-scrollbar__wrap {
    max-height: 450px;
  }
  /deep/ .el-autocomplete-suggestion li {
    line-height: 27px;
  }
  .code {
    font-size: 12px;
    color: #000000;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .name {
    font-size: 10px;
    color: #9b9b9b;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  /deep/ .el-form-item__label{
    font-size: 12px;
  }
  /deep/.el-form--inline .el-form-item__label{
    float:left;
  }
  /deep/.el-input__prefix{
    left:0px;
  }
  /deep/.el-input--prefix .el-input__inner{
    padding-right: 0px;
  }
  /deep/.el-input__inner{
    font-size: 10px;
  }
  /deep/.el-input {
    width: 80px;
  }
  /deep/ .el-checkbox{
    margin-right:15px;
  }
  /deep/ .el-checkbox__label{
    font-size: 12px;
    padding-left:5px;
  }
  /*时间样式设置*/
  /deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner{
    width: 200px;
  }

  /*自定义样式*/
  .rankSelect{
    position:absolute;
    z-index:1000;
    right:5px;
  }

  /deep/ .el-dialog__header{
    padding:10px 20px 10px 20px;
  }
  /deep/ .el-dialog__body{
    height: 90%;
  }
  /deep/ .el-dialog{
    height: 60%;
  }
  /*/deep/.el-table{*/
  /*  height: 100%;*/
  /*}*/

  /deep/.el-table__body-wrapper is-scrolling-none{
    height: 92%;
  }
  /*/deep/.el-dialog {*/
  /*  position: absolute;*/
  /*  top: 50%;*/
  /*  left: 50%;*/
  /*  margin: 0 !important;*/
  /*  transform: translate(-50%, -50%);*/
  /*  max-height: calc(65%);*/
  /*  max-width: calc(70%);*/
  /*  display: flex;*/
  /*  flex-direction: column;*/
  /*}*/

  /*/deep/.el-dialog__body {*/
  /*  overflow: auto;*/
  /*}*/
</style>
