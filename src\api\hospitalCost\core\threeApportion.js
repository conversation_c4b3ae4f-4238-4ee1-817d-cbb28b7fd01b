import request from '@/utils/request'

/**
 * 查询三级分摊状态
 * @param params
 * @returns {*}
 */
export function queryProcessStatus (params) {
  return request({
    url: '/hospitalCostThreeApportionController/queryProcessStatus',
    method: 'post',
    params: params
  })
}

/**
 * 查询三级分摊汇总数据
 * @param params
 * @returns {*}
 */
export function queryApportionSummaryData (params) {
  return request({
    url: '/hospitalCostThreeApportionController/queryApportionSummaryData',
    method: 'post',
    params: params
  })
}

/**
 * 查询三级分摊明细数据
 * @param params
 * @returns {*}
 */
export function queryApportionDetails (params) {
  return request({
    url: '/hospitalCostThreeApportionController/queryApportionDetails',
    method: 'post',
    params: params
  })
}

/**
 * 查询三级分摊结果数据
 * @param params
 * @returns {*}
 */
export function queryApportionData (params) {
  return request({
    url: '/hospitalCostThreeApportionController/queryApportionData',
    method: 'post',
    params: params
  })
}

/**
 * 查询三级分摊科室间接成本
 * @param params
 * @returns {*}
 */
export function queryApportionIndirectData (params) {
  return request({
    url: '/hospitalCostThreeApportionController/queryApportionIndirectData',
    method: 'post',
    params: params
  })
}

/**
 * 执行三级分摊
 * @param params
 * @returns {*}
 */
export function executionApportion (params) {
  return request({
    url: '/hospitalCostThreeApportionController/executionApportion',
    method: 'post',
    params: params
  })
}
