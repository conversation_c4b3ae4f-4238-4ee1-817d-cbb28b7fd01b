import request from '@/utils/request'
// 系统配置
export function selectDip (params) {
  return request({
    url: '/benchmarkConfigController/selectDip',
    method: 'post',
    params: params
  })
}
export function updateData (params) {
  return request({
    url: '/benchmarkConfigController/update',
    method: 'post',
    params: params
  })
}

export function deleteData (params) {
  return request({
    url: '/benchmarkConfigController/delete',
    method: 'post',
    params: params
  })
}
export function insertData (params) {
  return request({
    url: '/benchmarkConfigController/insertData',
    method: 'post',
    params: params
  })
}

export function queryRatioRange (params) {
  return request({
    url: '/benchmarkConfigController/queryRatioRange',
    method: 'post',
    params: params
  })
}

export function anewScore (params) {
  return request({
    url: '/benchmarkConfigController/anewScore',
    method: 'post',
    params: params
  })
}

export function generateScore (params) {
  return request({
    url: '/benchmarkConfigController/generateScore',
    method: 'post',
    params: params
  })
}
