<template>
  <!-- container 布局 -->
  <drg-container :accurate="showQueryCondition" ref="somContainer" :maxCondition="maxCondition"
                 :isButtonEnd="isButtonEnd" v-if="container">
    <template slot="header">
      <!-- 头部 profttl -->
      <drg-title-line :title="headerTitle" v-if="headerTitle != undefined && container"/>
      <slot name="headerTitle" v-if="headerTitle == undefined && container"/>

      <el-form ref="form"
               :model="form"
               :rules="form.rules"
               :inline="inline"
               label-position="right"
               :label-width="labelWidth">

        <el-row :gutter="20">
          <!-- layout布局 -->
          <form-layout v-if="layout" :form="form" :layout-data="layoutData" @change="formItemChange"/>

          <!-- inline 布局 -->
          <template v-if="!layout">
            <el-col :span="itemSpan">
              <template v-for="(item, index) in elFormItems">
                <el-form-item
                    :key="index"
                    :prop="item.prop"
                    style="display: flex"
                    v-if="item.show">
                  <template #label>
                    <div class="form-item-label">
                      <div v-if="item.label.length > 4">
                        <el-tooltip :content="item.label" placement="top">
                          <span>{{ item.label.substring(0, 4) + '..' }}</span>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        <span>{{ item.label }}</span>
                      </div>
                    </div>
                  </template>
                  <form-items :item="item" :form="form" @change="change(item.prop)" :disabled="item.disabled"/>
                </el-form-item>
              </template>
            </el-col>
          </template>

          <!-- 扩展form，只是为了获取到插槽值,无其他实际意义，在 mounted 中会置为 false -->
          <slot name="extendFormItems" v-if="showExtendFormItems"/>

          <el-col :span="24-itemSpan">
            <!-- 按钮 -->
            <div :class="[layout ? 'som-align-center' : 'block']">
              <form-buttons @query="query"
                            @resetForm="resetForm"
                            @exportExcel="handlerExportExcel"
                            @exportAll="handlerExportAll"
                            @queryCondition="queryCondition(undefined)"
                            @changeTime="changeCheckBoxTime"
                            :show-export-excel="exportExcel && exportExcel != undefined ? true : false"
                            :conditionIcon="conditionIcon"
                            :show-time-check-box="inOutTimeCheckBoxShow"
                            :show-query-button="showQueryButton"
                            :out-checkbox="outCheckbox"
                            :out-time-check-box="outTimeCheckBox"
                            :show-query="showQuery"
                            :show-settlement-button="showSettlementButton"
                            :show-reset="showReset">
                <template slot="buttons">
                  <slot name="buttons"></slot>
                </template>

                <template slot="buttonsPrefix">
                  <slot name="buttonsPrefix"></slot>
                </template>

                <template slot="buttonsMiddle">
                  <slot name="buttonsMiddle"></slot>
                </template>

                <template slot="buttonsSuffix">
                  <slot name="buttonsSuffix"></slot>
                </template>
              </form-buttons>
            </div>
          </el-col>
        </el-row>

        <div v-if="showQueryCondition">
          <el-row :gutter="20">
            <el-col :span="6" v-for="(item, index) in queryConditionFormItems" :key="index">
              <el-form-item :label="item.label"
                            :prop="item.prop"
                            style="display: flex"
                            v-if="item.show">
                <template #label>
                  <div class="form-item-label">
                    <div v-if="item.label.length > 4">
                      <el-tooltip :content="item.label" placement="top">
                        <span>{{ item.label.substring(0, 4) + '..' }}</span>
                      </el-tooltip>
                    </div>
                    <div v-else>
                      <span>{{ item.label }}</span>
                    </div>
                  </div>
                </template>
                <form-items :item="item" :form="form" @change="change(item.prop)"/>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <!--          <el-drawer-->
      <!--            title="我是标题"-->
      <!--            :visible.sync="showQueryCondition"-->
      <!--            :with-header="false">-->
      <!--            <el-form ref="form"-->
      <!--                     :model="form"-->
      <!--                     :rules="form.rules"-->
      <!--                     :inline="false"-->
      <!--                     :label-width="labelWidth">-->
      <!--              <el-form-item v-for="(item, index) in queryConditionFormItems"-->
      <!--                            :key="index"-->
      <!--                            :label="item.label"-->
      <!--                            :prop="item.prop"-->
      <!--                            v-if="item.show">-->
      <!--                <form-items :item="item" :form="form" @change="change(item.prop)"/>-->
      <!--              </el-form-item>-->
      <!--            </el-form>-->
      <!--          </el-drawer>-->
    </template>
    <template slot="content">
      <!-- 内容 profttl -->
      <!-- drg-title-line-warp     -->
      <drg-title-line :title="contentTitle" v-if="contentTitle != undefined && container"/>
      <slot name="contentTitle" v-if="contentTitle == undefined && container"/>

      <div class="som-table-height" :style="{ height: contentTitle != undefined && container ? '95%' : '98%' }">

        <!-- 内容1 -->
        <div class="container-content" v-if="showCoustemContentTitle==undefined" :style="{ height: showPagination ? '97%' : '100%' }">
          <slot name="containerContent"/>
        </div>

        <div class="container-content" v-if="showCoustemContentTitle" :style="{ height: showPagination ? '95%' : '100%' }">
          <slot name="containerContent"/>
        </div>

        <!-- 分页条 -->
        <div class="cur-pagination-container" v-if="showPagination">
          <el-pagination
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              layout="total, sizes,prev, pager, next,jumper"
              :page-size="form.pageSize"
              :page-sizes="[50,100,200,1000,2000,5000,10000]"
              :current-page.sync="form.pageNum"
              :total="totalNum">
          </el-pagination>
        </div>
      </div>
    </template>
  </drg-container>

  <!-- form 布局 -->
  <el-form ref="form"
           v-else
           :model="form"
           :rules="form.rules"
           :inline="inline"
           :label-width="labelWidth">

    <!-- layout布局 -->
    <form-layout v-if="layout" :form="form" :layout-data="layoutData"/>

    <!-- inline 布局 -->
    <template v-if="!layout">
      <template v-for="(item, index) in elFormItems">
        <el-form-item
            :key="index"
            :label="item.label"
            :prop="item.prop"
            v-if="item.show">
          <form-items :item="item" :form="form"/>
        </el-form-item>
      </template>
    </template>

    <!-- 扩展form，只是为了获取到插槽值,无其他实际意义，在 mounted 中会置为 false -->
    <slot name="extendFormItems" v-if="showExtendFormItems"/>

    <!-- 按钮 -->
    <div :class="[layout ? 'som-align-center' : 'block']">
      <form-buttons @query="query"
                    @resetForm="resetForm"
                    @exportExcel="handlerExportExcel"
                    @exportAll="handlerExportAll"
                    @queryCondition="queryCondition(undefined)"
                    @changeTime="changeCheckBoxTime"
                    :show-export-excel="exportExcel && exportExcel != undefined ? true : false"
                    :conditionIcon="conditionIcon"
                    :show-query-button="showQueryButton"
                    :show-query="showQuery"
                    :show-reset="showReset">
        <template slot="buttons">
          <slot name="buttons"></slot>
        </template>

        <template slot="buttonsPrefix">
          <slot name="buttonsPrefix"></slot>
        </template>

        <template slot="buttonsMiddle">
          <slot name="buttonsMiddle"></slot>
        </template>

        <template slot="buttonsSuffix">
          <slot name="buttonsSuffix"></slot>
        </template>
      </form-buttons>
    </div>

  </el-form>
</template>
<script>
import formItems from './comps/formItems'
import formButtons from './comps/formButtons'
import formLayout from './comps/formLayout'
import {exportAllExcel} from '@/api/common/sysCommon'
import moment from 'moment'

const commonArr = [
  // 期号、医院科室、成本科室、DIP组、DRG组
  'showIssue', 'showHosDept', 'showCostDept', 'showDip', 'showDrg', 'showCd', 'showDateRange', 'showPatientNum', 'showInDateRange', 'showSeDateRange'
]

// boolean使用
const booleanArr = [
  'showQuery', 'showReset', 'showPagination', 'showCoustemContentTitle'
]

// 对象使用
const objectArr = [
  'exportExcel'
]

// number 使用
const numberArr = [
  'totalNum'
]
// 不监听
const notWatch = [
  'showAccurate'
]

let defaultProps = {}

let commonProps = getProps(commonArr, 1)
let booleanProps = getProps(booleanArr, 2)
let objectProps = getProps(objectArr, 3)
let numberProps = getProps(numberArr, 4)
let notWatchProps = getProps(notWatch, 2)

Object.assign(defaultProps, commonProps)
Object.assign(defaultProps, booleanProps)
Object.assign(defaultProps, objectProps)
Object.assign(defaultProps, numberProps)
Object.assign(defaultProps, notWatchProps)

function getProps(arr, type) {
  let props = {}
  for (let i = 0; i < arr.length; i++) {
    let curType
    if (type == 1) {
      curType = [Boolean, Object]
    }
    if (type == 2) {
      curType = Boolean
    }
    if (type == 3) {
      curType = Object
    }
    if (type == 4) {
      curType = Number
    }
    props[arr[i]] = {
      type: curType,
      default: false || (() => {
      }) || 0
    }
  }
  return props
}

// 监听
let watch = {}
let commonWatch = {}
let booleanWatch = {}
let numberWatch = {}
addWatch(commonWatch, commonArr)
addWatch(booleanWatch, booleanArr)
addWatch(numberWatch, numberArr)

function addWatch(watch, arr) {
  for (let i = 0; i < arr.length; i++) {
    watch[arr[i]] = {
      immediate: true,
      deep: true,
      handler: function () {
        this.watchPropChange()
      }
    }
  }
}

Object.assign(watch, commonWatch)
Object.assign(watch, booleanWatch)
Object.assign(watch, numberWatch)

let defaultFormFields = {
  ym: '',
  deptCode: '',
  dipCodg: '',
  drgCodg: '',
  cdCodg: '',
  dateRange: [],
  begnDate: '',
  expiDate: '',
  inDateRange: [],
  inStartTime: '',
  inEndTime: '',
  seDateRange: [],
  seStartTime: '',
  seEndTime: '',
  medcasCodg: '',
  pageNum: 1,
  pageSize: 50,
  rules: {
    ym: [
      {required: false, message: '请选择数据期号', trigger: 'blur'}
    ],
    deptCode: [
      {required: false, message: '请选择出院科室', trigger: 'change'}
    ],
    dipCodg: [
      {required: false, message: '请选择DIP组', trigger: 'change'}
    ],
    drgCodg: [
      {required: false, message: '请选择DRG组', trigger: 'change'}
    ],
    cdCodg: [
      {required: false, message: '请选择CD组', trigger: 'change'}
    ],
    dateRange: [
      {required: false, message: '请选择出院时间', trigger: 'blur'}
    ],
    inDateRange: [
      {required: false, message: '请选择入院时间', trigger: 'blur'}
    ],
    seDateRange: [
      {required: false, message: '请选择结算时间', trigger: 'blur'}
    ],
    medcasCodg: [
      {required: false, message: '请输入病案号', trigger: 'blur'}
    ]
  }
}

export default {
  name: 'jpForm',
  props: Object.assign({
    showItemSize: {
      type: Number,
      default: 1
    },
    itemSpan: {
      type: Number,
      default: 6
    },
    labelWidth: {
      type: String,
      default: '100px'
    },
    inline: {
      type: Boolean,
      default: true
    },
    showLayout: {
      type: Boolean,
      default: true
    },
    modelVal: {
      type: Object
    },
    extendFormIndex: {
      type: Array
    },
    container: {
      type: Boolean,
      default: false
    },
    headerTitle: {
      type: String
    },
    contentTitle: {
      type: String
    },
    notClearableProps: {
      type: Array,
      default: () => []
    },
    exportExcelFun: {
      type: Function
    },
    exportExcelHasChild: {
      type: Boolean,
      default: false
    },
    selectOptions: {
      type: Array,
      default: () => []
    },
    // 是否显示结算
    showSettlementButton: {
      type: Boolean,
      default: true
    },
    // 初始化时间值时是否查询
    initTimeValueNotQuery: {
      type: Boolean,
      default: true
    }
  }, defaultProps),
  components: {
    'form-items': formItems,
    'form-buttons': formButtons,
    'form-layout': formLayout
  },
  data: () => ({
    form: {
      ...defaultFormFields
    },
    enableItem: [],
    elFormItems: [],
    layout: false,
    layoutData: [],
    tempForm: {},
    showExtendFormItems: true,
    isButtonEnd: false,
    maxCondition: false,
    size: 4,
    firstCreated: true,
    originFormData: {},
    loading: false,
    created: false,
    queryCount: 0,
    showQueryCondition: false,
    showQueryButton: true,
    conditionIcon: '',
    queryConditionFormItems: [],
    outHosTimeDisabled: false,
    inHosTimeDisabled: false,
    seHosTimeDisabled: false,
    lastOutTime: [],
    lastInTime: [],
    lastSeTime: [],
    inOutTimeCheckBoxShow: true,
    outCheckbox: '',
    outTimeCheckBox: [],
    tempExtendFormItems: []
  }),
  model: {
    prop: 'modelVal',
    event: 'changeForm'
  },
  mounted() {
    this.createElement()
    this.initValue()
    this.emitChangeForm()
    // 保存源值
    this.originFormData = {}
    Object.assign(this.originFormData, this.form)
  },
  methods: {
    createElement() {
      this.loading = true
      this.$set(this.form.rules.ym[0], 'required', this.required(this.showIssue))
      this.$set(this.form.rules.deptCode[0], 'required', this.required(this.showHosDept))
      this.$set(this.form.rules.dipCodg[0], 'required', this.required(this.showDip))
      this.$set(this.form.rules.drgCodg[0], 'required', this.required(this.showDrg))
      this.$set(this.form.rules.cdCodg[0], 'required', this.required(this.showCd))
      this.$set(this.form.rules.dateRange[0], 'required', this.required(this.showDateRange))
      this.$set(this.form.rules.inDateRange[0], 'required', this.required(this.showInDateRange))
      this.$set(this.form.rules.seDateRange[0], 'required', this.required(this.showSeDateRange))
      this.$set(this.form.rules.medcasCodg[0], 'required', this.required(this.showPatientNum))

      // 填充循环 form-item
      this.elFormItems = [
        {
          label: '出院时间',
          prop: 'dateRange',
          show: false,
          defaultSort: 2,
          sort: -1,
          clearable: false,
          disabled: this.outHosTimeDisabled,
          ...this.custom(this.showDateRange)
        },
        {
          label: '入院时间',
          prop: 'inDateRange',
          show: false,
          defaultSort: 3,
          sort: -1,
          clearable: false,
          disabled: this.inHosTimeDisabled,
          ...this.custom(this.showInDateRange)
        },
        {
          label: '结算时间',
          prop: 'seDateRange',
          show: false,
          defaultSort: 4,
          sort: -1,
          clearable: false,
          disabled: this.seHosTimeDisabled,
          ...this.custom(this.showSeDateRange)
        },
        {
          label: '数据期号',
          prop: 'ym',
          show: false,
          defaultSort: 1,
          sort: -1,
          disabled: false,
          ...this.custom(this.showIssue)
        },
        {
          label: '病案号',
          prop: 'medcasCodg',
          show: false,
          defaultSort: 5,
          sort: -1,
          disabled: false,
          ...this.custom(this.showPatientNum)
        },
        {
          label: '出院科室',
          prop: 'deptCode',
          show: false,
          defaultSort: 6,
          sort: -1,
          disabled: false,
          ...this.custom(this.showHosDept)
        },
        {
          label: 'DIP组',
          prop: 'dipCodg',
          show: false,
          defaultSort: 8,
          sort: -1,
          disabled: false,
          ...this.custom(this.showDip)
        },
        {
          label: 'DRG组',
          prop: 'drgCodg',
          show: false,
          defaultSort: 9,
          sort: -1,
          disabled: false,
          ...this.custom(this.showDrg)
        },
        {
          label: 'CD组',
          prop: 'cdCodg',
          show: false,
          defaultSort: 10,
          sort: -1,
          disabled: false,
          ...this.custom(this.showCd)
        }
      ]

      this.outTimeCheckBox = [
        {
          label: '出院',
          disabled: false
        },
        {
          label: '入院',
          disabled: false
        },
        {
          label: '结算',
          disabled: false
        }
      ]
      // 判断是否显示多选
      let showCheckBox = 0
      for (let i = 0; i < 3; i++) {
        this.outTimeCheckBox[i].disabled = !this.elFormItems[i].show
        if (this.elFormItems[i].show) {
          showCheckBox++
        }
      }

      if (showCheckBox > 1) {
        this.inOutTimeCheckBoxShow = true
      } else {
        this.inOutTimeCheckBoxShow = false
      }

      // 排序功能和扩展form代码 begin
      let allFormItems
      let formItems = [...this.elFormItems.filter(item => item.show)]
      formItems.sort((a, b) => a.defaultSort - b.defaultSort)
      for (let i = 0; i < formItems.length; i++) {
        formItems[i].defaultSort = i + 1
      }

      allFormItems = [...formItems]
      let extendFormItems = []

      // 获取插槽数据
      if (this.$slots.extendFormItems && this.$slots.extendFormItems != undefined) {
        let extendItems = [...this.$slots.extendFormItems.filter(formItem =>
            formItem.componentOptions && formItem.componentOptions && formItem.componentOptions.tag == 'el-form-item')]
        let maxDefaultSort = 0
        if (allFormItems.length > 0) {
          maxDefaultSort = allFormItems.sort((a, b) => a.defaultSort - b.defaultSort)[allFormItems.length - 1].defaultSort
        }
        extendFormItems = extendItems.map((formItem, index) => {
          maxDefaultSort++
          return {
            label: formItem.componentOptions.propsData.label,
            prop: formItem.componentOptions.propsData.prop,
            show: true,
            defaultSort: maxDefaultSort,
            dom: formItem.componentOptions.children,
            sort: this.getExtendFormItemIndex(index)
          }
        })
        // this.tempExtendFormItems = [...extendFormItems]
      }
      // 防止页面点击查询时没有默认的条件会隐藏掉
      // if(this.tempExtendFormItems.length > 0 && extendFormItems.length === 0){
      //   extendFormItems = [...this.tempExtendFormItems]
      // }

      // 添加扩展的表单元素
      if (extendFormItems.length > 0) {
        allFormItems = [...allFormItems, ...extendFormItems]
      }
      // 如果传入排序则按传入为准
      for (let i = 0; i < allFormItems.length; i++) {
        if (allFormItems[i].sort == -1) {
          allFormItems[i].inputSort = false
          allFormItems[i].sort = allFormItems[i].defaultSort
        } else {
          allFormItems[i].inputSort = true
        }
      }

      let sorts = []
      // 去重
      for (let formItem of allFormItems) {
        if (!sorts.includes(formItem.sort)) {
          sorts.push(formItem.sort)
        }
      }
      // 升序
      sorts.sort((a, b) => a - b)

      // 排序，以 sort 为准，如果 sort = -1 则以 defaultSort 为准
      let allSortFormItems = []
      for (let i = 0; i < sorts.length; i++) {
        let sort = sorts[i]
        let tempSortFormItems = []
        // 获取当前序号的数据
        allFormItems.map(formItem => {
          if (formItem.sort == sort) {
            tempSortFormItems.push(formItem)
          }
        })

        if (tempSortFormItems != undefined && tempSortFormItems.length > 0) {
          if (tempSortFormItems.length == 1) {
            allSortFormItems = [...allSortFormItems, ...tempSortFormItems]
          } else {
            let inputFormItems = tempSortFormItems.filter(formItem => formItem.inputSort)
            let notInputFormItems = tempSortFormItems.filter(formItem => !formItem.inputSort)
            // 输入两个一致则使用 defaultSort 排序
            inputFormItems.sort((a, b) => a.defaultSort - b.defaultSort)
            this.addNotExistsFormItems(allSortFormItems, inputFormItems)

            if (inputFormItems.length > 0 && i != sorts.length - 1) {
              // 更改不是输入的formItem的 sort 为下一个 sort 值，优先输入的formItem的顺序
              notInputFormItems.map(item => {
                for (let formItem of allFormItems) {
                  if (formItem.prop == item.prop) {
                    formItem.sort = sorts[i + 1]
                    break
                  }
                }
              })
            } else {
              this.addNotExistsFormItems(allSortFormItems, notInputFormItems)
            }
          }
        }
      }
      let sliceLength = this.showItemSize
      // if(allSortFormItems.length < 3){
      //   sliceLength = allSortFormItems.length
      // }

      // 排序功能和扩展form代码 end
      this.queryConditionFormItems = allSortFormItems.slice(sliceLength, allSortFormItems.length)
      if (this.queryConditionFormItems.length == 0) {
        this.showQueryButton = false
      } else {
        this.showQueryButton = true
      }
      allSortFormItems = allSortFormItems.slice(0, sliceLength)
      this.elFormItems = allSortFormItems

      // 防止有数据却判断不了情况
      setTimeout(() => {
        if (this.layoutData.length > 0) {
          this.layoutData = []
        }
        let size = this.size
        if (allSortFormItems.length >= size && this.showLayout) {
          let pgSize = allSortFormItems.length / size
          for (let i = 0; i < pgSize; i++) {
            this.layoutData.push({data: allSortFormItems.slice((i * size), (i + 1) * size)})
          }
          // 最后一个data小于size则补足到size长度
          let lastData = this.layoutData[this.layoutData.length - 1].data
          if (lastData.length < size) {
            for (let i = lastData.length; i < size; i++) {
              lastData.push({})
            }
          }
          this.layout = true
        } else {
          this.layout = false
        }
        this.showExtendFormItems = false
      })
    },
    custom(prop) {
      if (typeof (prop) == 'object') {
        return prop
      }
      if (typeof (prop) == 'boolean') {
        return {show: prop}
      }
      return {}
    },
    required(prop) {
      if (typeof (prop) == 'object' && prop.required) {
        return prop.required
      }
      return false
    },
    initValue() {
      if (!this.form.ym) {
        this.form.ym = this.$somms.getDate('yyyy-MM', 0, -1, 0, false)
      }
      // 出院时间
      this.initTime('dateRange', 'begnDate', 'expiDate')
      // 入院时间
      this.initTime('inDateRange', 'inStartTime', 'inEndTime')
      // 结算时间
      if( process.env.seTimeLastToThisMonth){
        this.initEsTime('seDateRange', 'seStartTime', 'seEndTime')
      }else{
        this.initTime('seDateRange', 'seStartTime', 'seEndTime')
      }
      this.rememberTime('out')
      this.changeCheckBoxTime(['出院'])
      if (this.queryCount === 0 && this.initTimeValueNotQuery) {
        this.emitQuery()
      }
    },
    query() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.emitQuery()
        } else {
          return false
        }
      })
    },
    resetForm() {
      let types = [
        {type: String, default: ''},
        {type: Array, default: []},
        {type: Object, default: {}},
        {type: Boolean, default: false}
      ]
      // 如果未点击“精确查询”就重置会重置不了未渲染的元素，所以只能记录后在点击“精确查询”后再重置
      if (!this.showQueryCondition) {
        for (let key of Object.keys(this.form)) {
          if (!Object.keys(defaultFormFields).includes(key)) {
            let reset = true
            for (let type of types) {
              if (this.form[key].constructor === type.type) {
                this.$set(this.form, key, type.default)
                reset = false
                break
              }
            }
            if (reset) {
              this.$set(this.form, key, '')
            }
          }
        }
      }

      let fields = this.$refs.form.fields
      if (fields) {
        fields.map(fld => {
          if (!this.notClearableProps.includes(fld.$options.propsData.prop)) {
            let checked = true
            if (this.form[fld.$options.propsData.prop] != undefined) {
              for (let type of types) {
                if (this.form[fld.$options.propsData.prop].constructor === type.type) {
                  if (fld.$options.propsData.prop == 'dateRange') {
                    // 出院时间
                    this.initTime('dateRange', 'begnDate', 'expiDate', true)
                  } else if (fld.$options.propsData.prop == 'inDateRange') {
                    // 入院时间
                    this.initTime('inDateRange', 'inStartTime', 'inEndTime', true)
                  } else if (fld.$options.propsData.prop == 'seDateRange') {
                    // 入院时间
                    this.initTime('seDateRange', 'seStartTime', 'seEndTime', true)
                  } else {
                    this.$set(this.form, fld.$options.propsData.prop, type.default)
                  }
                  checked = false

                  break
                }
              }
            }
            if (checked) {
              this.$set(this.form, fld.$options.propsData.prop, '')
            }
          } else {
            this.$set(this.form, fld.$options.propsData.prop, this.originFormData[fld.$options.propsData.prop])
          }
        })
      }

      Object.keys(this.form).forEach(key => {
        if (!this.form[key] && !Object.keys(defaultFormFields).includes(key)) {
          for (let type of types) {
            if (this.form[key].constructor === type.type) {
              this.$set(this.form, key, type.default)
              break
            }
          }
        }
      })

      this.$refs.form.clearValidate()
      this.initValue()
      this.emitChangeForm()
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.emitQuery()
        } else {
          return false
        }
      })
      this.$emit('reset', this.getParams(this.form))
    },
    // 初始化出院时间
    initTime(rangeField, startTimeField, endTimeField, reset = false) {
      if (!this.form[startTimeField] || reset) {
        this.$set(this.form, startTimeField, moment(this.$somms.getYearMonthStartTime()).format('YYYY-MM-DD'))
      }
      if (!this.form[endTimeField] || reset) {
        this.$set(this.form, endTimeField, moment(this.$somms.getYearMonthEndTime()).format('YYYY-MM-DD'))
      }
      if (this.form[rangeField].length == 0 || reset) {
        this.$set(this.form, rangeField, [this.form[startTimeField], this.form[endTimeField]])
      }
    },
    initEsTime(rangeField, startTimeField, endTimeField, reset = false) {
      if (!this.form[startTimeField] || reset) {
        var s = moment(this.$somms.getFirstDayOfPreviousMonth()).format('YYYY-MM-DD');

        this.$set(this.form, startTimeField, s)
      }
      if (!this.form[endTimeField] || reset) {
        this.$set(this.form, endTimeField, moment(this.$somms.getYearMonthEndTime()).format('YYYY-MM-DD'))
      }
      if (this.form[rangeField].length == 0 || reset) {
        this.$set(this.form, rangeField, [this.form[startTimeField], this.form[endTimeField]])
      }
    },
    // 初始化入院时间
    initInTime() {

    },
    emitQuery() {
      this.$emit('changeForm', this.getParams(this.form))
      this.$emit('query', this.getParams(this.form))
      this.queryCount++
    },
    emitChangeForm() {
      this.$emit('changeForm', this.getParams(this.form))
    },
    handlerExportExcel() {
      this.somExportExcel(this.exportExcel.tableId, this.exportExcel.exportName)
    },
    // 复杂查询
    queryCondition(flag = undefined) {
      this.$nextTick(() => {
        setTimeout(() => {
          if (flag !== undefined) {
            this.showQueryCondition = flag
          } else {
            this.showQueryCondition = !this.showQueryCondition
          }
        }, 1)
      })
    },
    handlerExportAll() {
      let params = this.$parent.getParams()
      params.pageSize = this.totalNum
      let childrenList = []
      if (this.exportExcelHasChild) {
        if (this.$parent.$refs.dataTable instanceof Array) {
          this.$parent.$refs.dataTable[0].setTableObj()
        } else {
          this.$parent.$refs.dataTable.setTableObj()
        }
        childrenList = this.$parent.tableObj.$children
      } else {
        childrenList = this.$parent.$refs.dataTable.$children
      }
      let tableNodes = document.getElementById(this.$parent.tableId).children[1].children[0].children[1].children[0].childNodes
      let columnPropList = []
      let exportTableList = []
      let exportTableColumnNameList = []

      tableNodes.forEach(child => {
        if (child.innerText !== '序号') {
          if (child.innerText !== '') {
            exportTableColumnNameList.push(child.innerText)
          } else {
            if (child.children[0] && child.children[0].innerHTML !== '序号' && !child.children[0].innerHTML.includes('el-checkbox')) {
              exportTableColumnNameList.push(child.children[0].innerHTML)
            }
          }
        }
      })

      let deleteArr = []
      let tableColumnIndex = 0
      exportTableColumnNameList.forEach(columnName => {
        for (let i = 0; i < childrenList.length; i++) {
          if (childrenList[i].label === columnName) {
            if (childrenList[i].prop === undefined) {
              deleteArr.push(tableColumnIndex)
            }
            columnPropList.push(childrenList[i].prop)
            break
          }
        }
        tableColumnIndex++
      })

      for (let i = deleteArr.length - 1; i >= 0; i--) {
        exportTableColumnNameList.splice(deleteArr[i], 1)
        columnPropList.splice(deleteArr[i], 1)
      }

      params.pageNum = 1
      this.exportExcelFun(params).then(res => {
        if (res.code == 200) {
          let resDataList = res.data.list
          if (resDataList && resDataList.length > 0) {
            resDataList.forEach(data => {
              let exportTableRow = []
              columnPropList.forEach(column => {
                exportTableRow.push(data[column])
              })
              exportTableList.push(exportTableRow)
            })
          }

          if (exportTableList.length > 0 && exportTableColumnNameList.length > 0) {
            let exportParams = {}
            exportParams.columns = exportTableColumnNameList
            exportParams.data = exportTableList
            exportAllExcel(exportParams).then(res => {
              this.$somms.download(res, this.exportExcel.exportName + '_所有.xlsx', 'application/vnd.ms-excel')
            })
          }
        }
      })
    },
    // 清除时间
    clearTime(type = '') {
      this.form.dateRange = []
      this.form.inDateRange = []
      this.form.seDateRange = []
      this.form.begnDate = ''
      this.form.inStartTime = ''
      this.form.expiDate = ''
      this.form.inEndTime = ''
      this.form.seStartTime = ''
      this.form.seEndTime = ''
    },
    getParams(form) {
      let params = {}
      Object.assign(params, form)
      return params
    },
    getExtendFormItemIndex(index) {
      if (this.extendFormIndex && this.extendFormIndex.length > 0) {
        return this.extendFormIndex[index]
      }
      return -1
    },
    addNotExistsFormItems(targetFormItems, originFormItems) {
      originFormItems.map(formItem => {
        let temp = targetFormItems.filter(item => item.prop == formItem.prop)
        if (temp == undefined || temp.length == 0) {
          targetFormItems.push(formItem)
        }
      })
    },
    watchPropChange() {
      this.$nextTick(() => {
        this.firstCreated = false
        this.createElement()
      })
    },
    handleSizeChange(val) {
      this.form.pageNum = 1
      this.form.pageSize = val
      this.emitQuery()
    },
    handleCurrentChange(val) {
      this.form.pageNum = val
      this.emitQuery()
    },
    formItemChange(flag) {
      if (flag) {
        this.$nextTick(() => {
          this.emitQuery()
        })
      }
    },
    change(propName) {
      this.rememberTime('in')
      this.rememberTime('out')
      this.rememberTime('se')
      if (['dateRange', 'deptCode', 'patientNum', 'dipCode', 'drgCode', 'inDateRange', 'seDateRange'].includes(propName)) {
        this.formItemChange(true)
      }
    },
    // 更改出院入院时间多选
    changeCheckBoxTime(val) {
      if (val.includes('入院')) {
        this.queryCondition(true)
        this.setLastTime('in', 'inDateRange', 'inStartTime', 'inEndTime')
        this.inHosTimeDisabled = false
        this.form.inHosFlag = '2'
      } else {
        this.rememberTime('in')
        this.form.inStartTime = ''
        this.form.inEndTime = ''
        this.inHosTimeDisabled = true
      }

      if (val.includes('出院')) {
        this.setLastTime('out', 'dateRange', 'begnDate', 'expiDate')
        this.outHosTimeDisabled = false
        this.form.inHosFlag = '1'
      } else {
        this.rememberTime('out')
        this.form.begnDate = ''
        this.form.expiDate = ''
        this.outHosTimeDisabled = true
      }

      if (val.includes('结算')) {
        this.queryCondition(true)
        this.setLastTime('se', 'seDateRange', 'seStartTime', 'seEndTime')
        this.seHosTimeDisabled = false
        this.form.inHosFlag = '3'
      } else {
        this.rememberTime('se')
        this.form.seStartTime = ''
        this.form.seEndTime = ''
        this.seHosTimeDisabled = true
      }

      // this.$emit("changeForm", this.getParams(this.form))
      if (val.length === 3) {
        this.form.inHosFlag = '4'
      }
      this.createElement()
    },
    // 跳转

    setLastTime(type, dateRangeField, startTileField, endTileField) {
      let time = []
      if (type === 'out') {
        time = this.lastOutTime
      } else if (type === 'in') {
        time = this.lastInTime
      } else {
        time = this.lastSeTime
      }
      if (time.length === 0) {
        return
      }

      this.form[dateRangeField] = time
      this.form[startTileField] = time[0]
      this.form[endTileField] = time[1]
    },
    jumpTimeChange(type, query, form) {
      Object.assign(this.form, form)
      if (type === 'out') {
        this.form.begnDate = query.begnDate
        this.form.expiDate = query.expiDate
        this.form.dateRange = [query.begnDate, query.expiDate]
        this.rememberTime(type, query.begnDate, query.expiDate)
        this.setButtonCheckbox('出院')

        this.form.inStartTime = ''
        this.form.inEndTime = ''
        this.form.seStartTime = ''
        this.form.seEndTime = ''
      }
      if (type === 'in') {
        this.form.inStartTime = query.inStartTime
        this.form.inEndTime = query.inEndTime
        this.form.dateRange = [query.inStartTime, query.inEndTime]
        this.rememberTime(type, query.inStartTime, query.inEndTime)
        this.setButtonCheckbox('入院')

        this.form.begnDate = ''
        this.form.expiDate = ''
        this.form.seStartTime = ''
        this.form.seEndTime = ''
      }
      if (type === 'se') {
        this.form.seStartTime = query.seStartTime
        this.form.seEndTime = query.seEndTime
        this.form.seDateRange = [query.seStartTime, query.seEndTime]
        this.rememberTime(type, query.seStartTime, query.seEndTime)
        this.setButtonCheckbox('结算')

        this.form.begnDate = ''
        this.form.expiDate = ''
        this.form.inStartTime = ''
        this.form.inEndTime = ''
      }
      this.emitChangeForm()
    },
    rememberTime(type, begnDate, expiDate) {
      if (this.form.inStartTime && this.form.inEndTime && type === 'in') {
        this.lastInTime = begnDate && expiDate ? [begnDate, expiDate] : [this.form.inStartTime, this.form.inEndTime]
      }
      if (begnDate && expiDate && type === 'in') {
        this.lastInTime = [begnDate, expiDate]
      }
      if (this.form.begnDate && this.form.expiDate && type === 'out') {
        this.lastOutTime = begnDate && expiDate ? [begnDate, expiDate] : [this.form.begnDate, this.form.expiDate]
      }
      if (begnDate && expiDate && type === 'out') {
        this.lastOutTime = [begnDate, expiDate]
      }
      if (this.form.seStartTime && this.form.seEndTime && type === 'se') {
        this.lastSeTime = [this.form.seStartTime, this.form.seEndTime]
      }
      if (begnDate && expiDate && type === 'se') {
        this.lastSeTime = [begnDate, expiDate]
      }
    },
    // 设置按钮出院和入院时间选择框
    setButtonCheckbox(val) {
      if (typeof (val) === 'string') {
        this.outCheckbox = val
      }
    },
    // 设置高度
    setHeight() {
      this.$refs.somContainer.setHeight()
    }
  },
  watch: Object.assign({
    form: {
      immediate: true,
      deep: true,
      handler: function (form) {
        this.$emit('changeForm', this.getParams(form))
      }
    },
    modelVal: {
      immediate: true,
      deep: true,
      handler: function (val) {
        Object.assign(this.form, val)
        this.createElement()
      }
    },
    selectOptions: {
      immediate: true,
      deep: true,
      handler: function (val) {
        this.createElement()
      }
    },
    showAccurate: {
      handler: function (val) {
        this.queryCondition(val)
      }
    },
    seDateRange:{
      handler: function (val) {
        console.log(val);
        debugger
      }
    }
  }, watch)
}
</script>

<style scoped>
.block {
  display: inline-block;
}

.el-form-item {
  margin-bottom: 3px;
}

.container-content {
  padding-top: 5px;
  overflow-y: auto;
  overflow-x: hidden;
}

.cur-pagination-container {
  margin-top: 4px;
  height: 3%;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  margin-bottom: 8px;
}

.form-item-label {
  font-size: 14px;
}

/deep/ .el-col {
  border-radius: 4px;
  margin-bottom: 10px;
}

/deep/ .el-form-item__content {
  width: 100%;
}

/deep/ .el-tag--info {
  color: black;
  font-size: 13px;
  padding: 5px;
  height: 28px;
}
</style>
