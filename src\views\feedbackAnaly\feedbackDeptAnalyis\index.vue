<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="form"
             :show-date-range="{ show: true, clearable: true }"
             :show-se-date-range="{ show: true, clearable: true }"
             :container="true"
             :showPagination="true"
             headerTitle="查询条件"
             contentTitle="数据列表"
             :totalNum="total"
             :exportExcel="{ 'tableId': tableId, exportName: '科室反馈分析'}"
             :exportExcelFun="selectFeedbackDeptData"
             :exportExcelHasChild="false"
             @query="queryData">

      <template slot="extendFormItems">
        <el-form-item label="科室名称">
        <el-select v-model="dept" clearable placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        </el-form-item>
      </template>

      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  size="mini"
                  stripe
                  :id="tableId"
                  height="100%"
                  :data="dataList"
                  style="width: 100%;"
                  border>
          <el-table-column label="出院科室" prop="deptName" align="left" >
          </el-table-column>
          <el-table-column label="病案数" prop="countMedical" align="center" >
            <template slot-scope="scope">
              <div :class="scope.row.countMedical === 0 ? '' : 'skip'" @click="scope.row.countMedical === 0 ? '' : jumpPersonData(scope.row)">
                {{ scope.row.countMedical }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="结算点数" prop="selPointAll" align="center" sortable>
          </el-table-column>
          <el-table-column label="总费用" prop="totalCostAll" align="center" sortable>
          </el-table-column>
          <el-table-column label="统筹费用" prop="overallCostAll" align="center" sortable>
          </el-table-column>
          <el-table-column label="病组总费用" prop="groupTotalCostAll" align="center" sortable>
          </el-table-column>
          <el-table-column label="病组统筹费用" prop="groupOverallCostAll" align="center" sortable>
          </el-table-column>
          <el-table-column label="盈亏" prop="profitAll" align="center" sortable>
          </el-table-column>
        </el-table>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { selectFeedbackDeptData, selectFeedbackDeptOptions } from '@/api/newBusiness/feebackAnalyse'
export default {
  name: 'feedbackDeptAnalyis',
  data: () => ({
    queryForm: {
      deptName: ''
    },
    dataList: [],
    options: [],
    tableId: 'dataTable',
    total: null,
    dept: ''
  }),
  mounted () {
    selectFeedbackDeptOptions(this.getParams()).then(res => {
      this.options = res.data
    })
  },
  methods: {
    selectFeedbackDeptData,
    queryData () {
      selectFeedbackDeptData(this.getParams()).then(res => {
        this.dataList = res.data.list
        this.total = res.data.total
      })
    },
    // 获取参数
    getParams () {
      let params = {}
      this.queryForm.deptName = this.dept
      Object.assign(params, this.queryForm)
      return params
    },
    // 跳转患者数据
    jumpPersonData (rowData) {
      this.$router.push({
        path: '/feedbackAnaly/feedbackPersonAnalyis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime,
          deptName: rowData.deptName
        }
      })
    }
  }
}
</script>
