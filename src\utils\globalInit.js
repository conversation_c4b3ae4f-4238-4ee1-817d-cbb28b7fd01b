import { queryDictionary,
  querySelectTreeAndSelectList,
  querySettleListDictionary,
  queryUserInfo
} from '@/api/common/drgCommon'
// import { queryGroupType } from '@/api/common/sysCommon'
import store from '../store'
import { getToken } from '@/utils/auth' // 验权

/**
 * 初始化码表
 */
function initDictionary () {
  let params = new URLSearchParams()
  params.append('codeKeys', 'ALL')
  // 通用码表
  queryDictionary(params).then((response) => {
    if (response.code === 200) {
      store.commit('setDictionaries', response.data)
    }
  })

  // 清单码表
  querySettleListDictionary(params).then((response) => {
    if (response.code === 200) {
      store.commit('setSettleListDict', response.data)
    }
  })
}

/**
 * 初始化科室
 */
function initDepartment () {
  querySelectTreeAndSelectList().then((response) => {
    if (response.data) {
      store.commit('setDepartments', response.data)
    }
  })
}

// /**
//  * 初始化系统配置
//  */
// function initSysConfig () {
//   queryGroupType({ configKey: 'FZLX+FZLX' }).then(res => {
//     if (res.data) {
//       store.commit('setFzlx', res.data.value)
//     }
//   })
// }

/**
 * 初始化角色权限
 */
function initRoleAuth () {
  if (getToken()) {
    queryUserInfo().then(res => {
      if (res.code === 200) {
        store.commit('del_roles', null)
        store.commit('set_roles', res.data.transferRuleIds)
        if (res.data.b16c) {
          store.commit('setDeptCode', res.data.b16c)
          store.commit('setDeptName', store.getters.getDeptNameByCode(res.data.b16c))
        }
      }
    })
  }
}

/**
 * 初始化
 */
export function init () {
  if (getToken() && store.getters.getAllDepartments.length === 0) {
    initDictionary()
    initDepartment()
    initRoleAuth()
    // initSysConfig()
  }
}

init()
