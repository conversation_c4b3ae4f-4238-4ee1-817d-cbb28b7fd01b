<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-date-range
             headerTitle="查询条件"
             contentTitle="科室全面分析"
             :container="true"
             @query="queryData">

      <!-- 内容 -->
      <template slot="containerContent">
        <drg-echarts :options="options" ref="crossCharts" />
      </template>
    </drg-form>
  </div>
</template>

<script>
import { queryDeptCRSData } from '@/api/newBusiness/newDipDeptCRSAnalysis'

export default {
  name: 'newDipDeptCRSAnalysis',
  data: () => ({
    queryForm: {},
    options: {}
  }),
  methods: {
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },

    queryData () {
      queryDeptCRSData(this.getParams()).then(res => {
        if (res.data) {
          this.getOptions(res.data)
        }
      })
    },

    getOptions (item) {
      let cmiMax = 0
      let balanceCostMax = 0
      let balanceCostMin = 0
      let max = 0
      let marksData = []
      if (item) {
        // 循环数据
        for (const res of item) {
          // x轴定位
          if (cmiMax <= res.cmi) {
            cmiMax = Math.ceil(res.cmi)
          }
          // y轴定位
          if (balanceCostMax <= res.balanceCost) {
            balanceCostMax = Math.ceil(res.balanceCost)
          }
          if (balanceCostMin >= res.balanceCost) {
            balanceCostMin = Math.floor(res.balanceCost)
          }
          // 散点数据
          marksData.push({
            name: res.deptName,
            value: [res.cmi, res.balanceCost],
            symbolSize: this.getSymbolSize(res.totalNum)
          })
        }
        // y轴对称
        if (Math.abs(balanceCostMax) > Math.abs(balanceCostMin)) {
          max = balanceCostMax
        } else {
          max = Math.abs(balanceCostMin)
        }
        // 中心线
        let centerLine = [
          {
            name: '盈亏', xAxis: 1
          },
          {
            name: 'CMI', yAxis: 0
          }
        ]
        // 中心点
        let centerMark = [
          {
            value: '中心点',
            coord: [1, 0]
          }
        ]
        this.options = {
          grid: {
            left: 50,
            top: '6%',
            right: 50,
            bottom: '4%',
            containLabel: true
          },
          xAxis: {
            axisLine: {
              lineStyle: {
                color: '#ddd'
              }
            },
            axisLabel: {
              color: '#666'
            },
            splitLine: {
              lineStyle: {
                color: '#eee'
              }
            }
          },
          yAxis: {
            min: -max,
            max: max,
            axisLine: {
              lineStyle: {
                color: '#ddd'
              }
            },
            axisLabel: {
              color: '#666'
            },
            splitLine: {
              lineStyle: {
                color: '#eee'
              }
            }
          },
          tooltip: {
            show: true,
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#555'
              },
              lineStyle: {
                width: 1,
                type: 'dashed'
              }
            },
            formatter: (params) => {
              return params.name + '<br />cmi:' + params.value[0] + ',<br />逆差:' + params.value[1]
            }
          },
          series: [{
            type: 'scatter',
            label: {
              show: true,
              position: 'bottom',
              formatter: '{b}'
            },
            itemStyle: {
              shadowBlur: 2,
              shadowColor: 'rgba(120, 36, 50, 0.5)',
              shadowOffsetY: 1
            },
            data: marksData,
            // 中心点
            // markPoint: {
            //   symbol: 'roundRect',
            //   symbolSize: 15,
            //   label: {
            //     position: 'top',
            //     formatter: '{b}'
            //   },
            //   itemStyle: {
            //     color: 'rgba(234, 85, 6, .8)'
            //   },
            //   data: centerMark
            // },
            // 中心点交集象限轴
            markLine: {
              // 不响应和出发鼠标事件
              silent: true,
              // 精度
              precision: 2,
              label: {
                position: 'end',
                formatter: '{b}',
                color: '#00aca6'
              },
              lineStyle: {
                color: '#00aca6',
                type: 'solid'
              },
              data: centerLine
            },
            // 各象限区域
            markArea: {
              // 不响应和出发鼠标事件
              silent: true,
              data: [
                // 第一象限-右上
                [
                  {
                    // x轴开始位置
                    xAxis: 1,
                    // y轴开始位置
                    yAxis: 0,
                    itemStyle: {
                      color: 'rgba(56, 180, 139, .1)'
                    },
                    label: {
                      position: 'inside',
                      color: 'rgba(0, 0, 0, .1)',
                      fontSize: 22
                    }
                  },
                  {
                    // x轴结束位置
                    xAxis: cmiMax,
                    // y轴结算位置
                    yAxis: balanceCostMax
                  }
                ],
                // 第二象限-左上
                [
                  {
                    // x轴开始位置
                    xAxis: 0,
                    // y轴开始位置
                    yAxis: 0,
                    itemStyle: {
                      color: 'rgba(68, 97, 123, .1)'
                    },
                    label: {
                      position: 'inside',
                      color: 'rgba(0, 0, 0, .1)',
                      fontSize: 22
                    }
                  },
                  {
                    // x轴结束位置
                    xAxis: 1,
                    // y轴结算位置
                    yAxis: balanceCostMax
                  }
                ],
                // 第三象限-左下
                [
                  {
                    // x轴开始位置
                    xAxis: 0,
                    // y轴开始位置
                    yAxis: 0,
                    itemStyle: {
                      color: 'rgba(191, 120, 58, .1)'
                    },
                    label: {
                      position: 'inside',
                      color: 'rgba(0, 0, 0, .1)',
                      fontSize: 22
                    }
                  },
                  {
                    // x轴结束位置
                    xAxis: 1,
                    // y轴结算位置
                    // yAxis: balanceCostMin
                    yAxis: -max
                  }
                ],
                // 第四象限-右下
                [
                  {
                    // x轴开始位置
                    xAxis: 1,
                    // y轴开始位置
                    yAxis: 0,
                    itemStyle: {
                      color: 'rgba(116, 83, 153, .1)'
                    },
                    label: {
                      position: 'inside',
                      color: 'rgba(0, 0, 0, .1)',
                      fontSize: 22
                    }
                  },
                  {
                    // x轴结束位置
                    xAxis: cmiMax,
                    // y轴结算位置
                    // yAxis: balanceCostMin
                    yAxis: -max
                  }
                ]
              ]
            }
          }]
        }
      }
    },

    getSymbolSize (item) {
      if (item) {
        if (item < 100) {
          return 10
        } else if (item >= 100 && item < 500) {
          return 16
        } else if (item >= 500 && item < 1000) {
          return 20
        } else {
          return 25
        }
      }
    }
  }
}
</script>
