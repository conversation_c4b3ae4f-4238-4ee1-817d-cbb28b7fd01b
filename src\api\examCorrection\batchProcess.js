import request from '@/utils/request'

/**
 * 执行自查自纠流程
 * @param params
 * @returns {*}
 */
export function runProcess(params) {
  return request({
    url: '/examCorrectionProcessController/runProcHcs',
    method: 'post',
    params: params
  })
}

/**
 * 查询自查自纠执行日志
 * @param params
 * @returns {*}
 */
export function queryHcsProcList(params) {
  return request({
    url: '/examCorrectionProcessController/queryHcsProcList',
    method: 'post',
    params: params
  })
}
