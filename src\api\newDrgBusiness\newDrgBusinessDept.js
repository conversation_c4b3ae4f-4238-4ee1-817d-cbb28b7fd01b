import request from '@/utils/request'

/**
 * 查询Kpi指标数据
 * @param params
 * @returns {*}
 */
export function queryKpiData (params) {
  return request({
    url: '/newDrgBusinessDeptAnalysisController/queryKpiData',
    method: 'post',
    params: params
  })
}

/**
 * 查询预测数据
 * @param params
 * @returns {*}
 */
export function queryForecastData (params) {
  return request({
    url: '/newDrgBusinessDeptAnalysisController/queryForecastData',
    method: 'post',
    params: params
  })
}

export function queryCountData (params) {
  return request({
    url: '/newDrgBusinessDeptAnalysisController/queryCountData',
    method: 'post',
    params: params
  })
}
export function queryYCCountData (params) {
  return request({
    url: '/newDrgBusinessDeptAnalysisController/queryYCCountData',
    method: 'post',
    params: params
  })
}
export function selectDrgSickGroupQuadrant (params) {
  return request({
    url: '/newDrgBusinessDeptAnalysisController/selectDrgSickGroupQuadrant',
    method: 'post',
    params: params
  })
}
