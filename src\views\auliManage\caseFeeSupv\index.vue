<template>
  <div class="app-container">
    <drg-form :container="true"
             header-title="查询条件"
             show-date-range
             show-se-date-range
             show-hos-dept
             show-pagination
             :totalNum="total"
             :export-excel="{ 'tableId' : tableId, exportName: exportName }"
             :export-excel-fun="queryPatientInfo"
             :export-excel-has-child="true"
             v-model="queryForm"
             @query="queryData"
             content-title="内容">

      <!-- 查询条件 -->
      <template slot="extendFormItems">
        <el-form-item label="差异比">
          <el-input v-model="queryForm.superVisionRatio" placeholder="请输入" @input="queryData" />
        </el-form-item>
      </template>

      <!-- 按钮 -->
      <template slot="buttons"></template>

      <!-- 内容标题 -->
      <template slot="contentTitle"></template>

      <!-- 内容 -->
      <template slot="containerContent">
        <div style="height: 30%;margin-bottom: 1%">
          <el-row style="height: 100%;width: 100%">
            <!-- 点数 -->
            <el-col :span="8" style="height: 100%;width: 30%;margin: 1%">
              <div class="som-h-fifty som-w-one-hundred">
                <div class="som-w-one-hundred ptp-left-item">
                  <div class="ptp-left-item-title som-wd-one-hundred">
                    <div class="ptp-circle-score"></div>
                    原总点数:
                  </div>
                  <div class="ptp-left-item-value" v-html="formatCostNum(totalScoreOld,true)">
                    {{ totalScoreOld }}
                  </div>
                </div>
              </div>
              <div class="som-h-fifty som-w-one-hundred">
                <div class="som-w-one-hundred ptp-left-item">
                  <div class="ptp-left-item-title som-wd-one-hundred">
                    <div class="ptp-circle-score"></div>
                    现总点数:
                  </div>
                  <div class="ptp-left-item-value" v-html="formatCostNum(totalScoreNow,true)">
                    {{ totalScoreNow }}
                  </div>
                </div>
              </div>
            </el-col>
            <!-- 差异 -->
            <el-col :span="8" style="height: 100%;width: 30%;margin: 1%">
              <div class="som-h-fifty som-w-one-hundred">
                <div class="som-w-one-hundred ptp-left-item">
                  <div class="ptp-left-item-title som-wd-one-hundred">
                    <div class="ptp-circle-cost"></div>
                    原总差异:
                  </div>
                  <div class="ptp-left-item-value" v-html="formatCost(totalDiffOld,true)">
                    {{ totalDiffOld }}
                  </div>
                </div>
              </div>
              <div class="som-h-fifty som-w-one-hundred">
                <div class="som-w-one-hundred ptp-left-item">
                  <div class="ptp-left-item-title som-wd-one-hundred">
                    <div class="ptp-circle-cost"></div>
                    现总差异:
                  </div>
                  <div class="ptp-left-item-value" v-html="formatCost(totalDiffNow,true)">
                    {{ totalDiffNow }}
                  </div>
                </div>
              </div>
            </el-col>

            <el-col :span="8" style="height: 100%;width: 30%;margin: 1%">
              <div class="som-h-fifty som-w-one-hundred">
                <div class="som-w-one-hundred ptp-left-item">
                  <div class="ptp-left-item-title som-wd-one-hundred">
                    <div class="ptp-circle"></div>
                    差异值:
                  </div>
                  <div class="ptp-left-item-value" v-html="formatCost(totalDiffValue,true)">
                    {{ totalDiffValue }}
                  </div>
                </div>
              </div>
              <div class="som-h-fifty som-w-one-hundred">
                <div class="som-w-one-hundred ptp-left-item">
                  <div class="ptp-left-item-title som-wd-one-hundred">
                    <div class="ptp-circle"></div>
                    差异比:
                  </div>
                  <div class="ptp-left-item-value">
                    {{ totalDiffRatio }}%
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div style="height: 67.5%">
          <!-- 患者修改记录表格 -->
          <patient-modify-table :table-id="tableId" ref="dataTable" :loading="patientTableDataLoading"
                                :data="patientTableDataList" @setRefObj="(obj) => this.tableObj = obj" />
        </div>
      </template>
    </drg-form>
  </div>
</template>

<script>
import PatientModifyTable from '@/views/auliManage/caseFeeSupv/comps/patientModifyTable.vue'
import { queryPatientInfo } from '@/api/supervision/medicalRecordSupervision'
import { formatCost, formatCostNum } from '@/utils/common'

export default {
  name: 'caseFeeSupv',
  components: {
    PatientModifyTable
  },
  data: () => ({
    queryForm: {
      superVisionRatio: ''
    },
    total: 0,
    tableId: 'tableId',
    exportName: '修改记录',
    tableObj: {},
    patientTableDataLoading: false,
    patientTableDataList: [],
    totalScoreOld: 0,
    totalScoreNow: 0,
    totalDiffOld: 0,
    totalDiffNow: 0,
    totalDiffValue: 0,
    totalDiffRatio: 0
  }),
  mounted () {
  },
  methods: {
    formatCost,
    formatCostNum,
    queryPatientInfo,
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      if (params.superVisionRatio) {
        params.superVisionRatio = params.superVisionRatio / 100
      }
      params.grperType = this.$somms.getGroupType()
      return params
    },
    queryData () {
      this.getPatientTableData()
    },

    getPatientTableData () {
      this.patientTableDataLoading = true
      queryPatientInfo(this.getParams()).then(res => {
        if (res.data) {
          this.patientTableDataList = res.data.list
          this.total = res.data.total
          this.summary(res.data.list)
          this.patientTableDataLoading = false
        }
      })
    },
    summary (item) {
      this.totalScoreOld = 0
      this.totalScoreNow = 0
      this.totalDiffOld = 0
      this.totalDiffNow = 0
      for (let i = 0; i < item.length; i++) {
        this.totalScoreOld = this.totalScoreOld + (item[i].initSco ? item[i].initSco : 0)
        this.totalScoreNow = this.totalScoreNow + (item[i].currSco ? item[i].currSco : 0)
        this.totalDiffOld = this.totalDiffOld + (item[i].initDif ? item[i].initDif : 0)
        this.totalDiffNow = this.totalDiffNow + (item[i].currDif ? item[i].currDif : 0)
      }
      this.totalDiffValue = this.totalDiffNow - this.totalDiffOld
      this.totalDiffRatio = (this.totalDiffValue / Math.abs(this.totalDiffOld) * 100).toFixed(2)
    }
  }
}
</script>

<style lang="scss">
.ptp-left-item {
  height: 33%;
  display: flex;
  align-items: center;
}

.ptp-left-item-title {
  display: inline-block;
  text-align: left;
  color: gray;
  font-size: var(--biggerSize);
  display: flex;
  align-items: center;
}

.ptp-left-item-value {
  width: 100%;
  display: inline-block;
  text-align: right;
  color: #469ca5;
  font-weight: 400;
  font-size: var(--smallTitleSize);
}

.ptp-circle-score {
  height: 2rem;
  width: 2rem;
  background-color: #c4e7b2;
  border-radius: 50%;
  display: inline-block;
  margin-right: 0.5rem;
}
.ptp-circle-cost {
  height: 2rem;
  width: 2rem;
  background-color: #e6a23c;
  border-radius: 50%;
  display: inline-block;
  margin-right: 0.5rem;
}
.ptp-circle {
  height: 2rem;
  width: 2rem;
  background-color: #bee3e8;
  border-radius: 50%;
  display: inline-block;
  margin-right: 0.5rem;
}
</style>
