import request from '@/utils/request'

/**
 * 查询自查自纠工作台图形化数据
 * @param params
 * @returns {*}
 */
export function queryAnalysisGraphic(params) {
  return request({
    url: '/examWorkBenchController/queryAnalysisGraphic',
    method: 'post',
    params: params
  })
}

/**
 * 查询自查自纠工作台报表数据
 * @param params
 * @returns {*}
 */
export function queryAnalysisDataList(params) {
  return request({
    url: '/examWorkBenchController/queryAnalysisDataList',
    method: 'post',
    params: params
  })
}
