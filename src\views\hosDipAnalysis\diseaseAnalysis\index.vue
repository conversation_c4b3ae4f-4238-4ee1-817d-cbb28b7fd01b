<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-hos-dept
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="医院疾病分布图表"
             :container="true"
             @query="changeSelectQueryType" @reset="refresh">

      <template slot="extendFormItems">
        <el-form-item label="病种编码" class="som-el-form-item-margin-left">
          <el-autocomplete
            popper-class="my-autocomplete"
            size="mini"
            v-model="listQuery.queryIcd"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入病种编码或者名称"
            @select="handleSelect"
            @clear="getList"
            :popper-append-to-body="true"
            :clearable="true"
            :trigger-on-focus="false"
            ref='elautocomplete'>
            <template slot-scope="{ item }">
              <div class="code">{{ item.icdCodg }}</div>
              <span class="name">{{ item.icdName }}</span>
            </template>
          </el-autocomplete>
        </el-form-item>
      </template>

      <template slot="buttons">
        <el-button class="expBtn" @click="exportExcel()" size="mini">导出Excel</el-button>
      </template>

      <template slot="buttonsMiddle">
        <el-radio-group v-model="listQuery.queryType" size="mini" @change="changeSelectQueryType" class="som-el-form-item-margin-left">
          <el-radio-button :label="1">按科室查询</el-radio-button>
          <el-radio-button :label="2">按病种查询</el-radio-button>
        </el-radio-group>
      </template>

      <template slot="containerContent">
        <div style="height:34%">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="24" style="height: 100%">
              <div id="diseaseClass" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="height: 65%">
          <el-table ref="diseaseAnalysisTable"
                    id="diseaseTable"
                    size="mini"
                    :header-cell-style = "{'text-align' : 'center'}"
                    height="100%"
                    stripe
                    :data="list"
                    v-loading="listLoading"
                    @sort-change='sortChange'
                    :key=Math.random()
                    border>
            <el-table-column
              label="序号"
              align="right"
              type="index"
              width="50">
            </el-table-column>
            <el-table-column  label="出院科室编码" v-if="false">
              <template slot-scope="scope">{{scope.row.priOutHosDeptCode | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column  label="出院科室名称"  align="left" width="340" v-if=deptHide(listQuery.queryType)>
              <template slot-scope="scope">{{scope.row.priOutHosDeptName }}</template>
            </el-table-column>
            <el-table-column  label="病种编码"  align="center" width="100" v-if="false">
              <template slot-scope="scope">{{scope.row.icdCodg }}</template>
            </el-table-column>
            <el-table-column  label="病种名称" prop="icdName"  align="left"  :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.totalPatients)>0" class='skip' @click="queryTotalPatients(scope.row)">
                  {{scope.row.icdName | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.totalPatients)==0" style="color:#000000">
                  {{scope.row.icdName | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="病种大类"  align="left"  :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.diagnosisType }}</template>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.totalPatients)>0">
                  {{scope.row.diagnosisType | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.totalPatients)==0" style="color:#000000">
                  {{scope.row.diagnosisType | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="总人次"  align="right" width="100" prop="totalPatients" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.totalPatients)>0" class='skip' @click="queryTotalPatients(scope.row)">
                  {{scope.row.totalPatients | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.totalPatients)==0" style="color:#000000">
                  {{scope.row.totalPatients | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="占比" align="right" width="80" >
              <template slot-scope="scope">{{scope.row.totalPatientsRate }}</template>
            </el-table-column>
            <el-table-column label="住院总费用"   align="right" width="120" prop="totalInHosCost" sortable='custom' >
              <template slot-scope="scope">{{scope.row.totalInHosCost }}</template>
            </el-table-column>
            <el-table-column label="占比"  align="right" width="80" >
              <template slot-scope="scope">{{scope.row.totalInHosCostRate }}</template>
            </el-table-column>
            <el-table-column label="例均费用"  align="right" width="130" prop="avgCost" sortable='custom' >
              <template slot-scope="scope">{{scope.row.avgCost }}</template>
            </el-table-column>
            <el-table-column label="住院总天数"  align="right" width="120" prop="inHosDays" sortable='custom' >
              <template slot-scope="scope">{{scope.row.inHosDays }}</template>
            </el-table-column>
            <el-table-column label="例均住院天数"  align="right" width="140" prop="avgInHosDays" sortable='custom' >
              <template slot-scope="scope">{{scope.row.avgInHosDays }}</template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, querySelectTreeAndSelectList, queryLikeIcdsByPram } from '@/api/common/drgCommon'
import { getCountInfo, fetchList } from '@/api/dipBusiness/dipDiseaseAnalysis'
import { elExportExcel } from '@/utils/exportExcel'
import echarts from 'echarts'
import { sortChange } from '@/utils/common'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  b16c: null,
  queryIcd: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date
}
export default {
  name: 'diseaseAnalysis',
  components: { },
  inject: ['reload'],
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      list: null,
      total: null,
      cy_start_date: null,
      cy_end_date: null,
      listQuery: { ...Object.assign({}, defaultListQuery), queryType: this.$somms.hasHosRole() ? '1' : '2' },
      submitListQuery: Object.assign({}, defaultListQuery),
      b16c: null,
      queryIcd: null,
      queryType: '1',
      tableHeight: 0,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  watch: {
    b16c: function () {
      this.getList()
      this.getCount()
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.diseaseAnalysisTable.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.diseaseAnalysisTable.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.diseaseAnalysisTable.$el.offsetTop - 35
      }
    })
  },
  methods: {
    sortChange,
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    deptHide (queryType) {
      if (queryType == '1') {
        return true
      } else if (queryType == '2') {
        return false
      }
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
        this.getCount()
      })
    },

    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryIcd = this.listQuery.queryIcd
      this.submitListQuery.queryType = this.listQuery.queryType
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      fetchList(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    getCount () {
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryIcd = this.listQuery.queryIcd
      this.submitListQuery.queryType = this.listQuery.queryType
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      getCountInfo(this.submitListQuery).then(response => {
        let result = response.data
        let barData = [] // 病种数据
        let xAxisData = [] // 病种名称
        let patientsRate = [] // 病种人次占比
        let lineData = [] // 累计占比
        if (result) {
          let totalPatients = Number(result[result.length - 1].value) // 总人次
          let accumulativePatients = 0 // 累计人次
          for (let i = 0; i < result.length - 1; i++) {
            accumulativePatients += Number(result[i].value)
            barData.push(Number(result[i].value))
            xAxisData.push(result[i].name)
            patientsRate.push((Number(result[i].value) / totalPatients * 100).toFixed(2)) // 人次占比
            lineData.push((accumulativePatients / totalPatients * 100).toFixed(2)) // 累计人次占比
          }
        }
        let legendData = ['人次', '累计占比']
        // let profttl = "全院疾病大类分布柏拉图"
        this.getChart(barData, xAxisData, lineData, patientsRate, legendData)
      })
    },
    // 下转
    queryTotalPatients (row) {
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            queryIcdCode: row.icdCodg,
            queryIcdName: row.icdName,
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1',
            inHosFlag: this.listQuery.inHosFlag
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            queryIcdCode: row.icdCodg,
            queryIcdName: row.icdName,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1',
            inHosFlag: this.listQuery.inHosFlag
          }
        })
      }
    },
    queryDiagnosisType (row) {
      let queryDiagnosisTypeStr = ''
      switch (row.diagnosisType) {
        case '某些传染病和寄生虫病':queryDiagnosisTypeStr = 'a00b99'; break
        case '肿瘤':queryDiagnosisTypeStr = 'c00d48'; break
        case '血液及造血器官疾病和涉及免疫机制的某些疾患':queryDiagnosisTypeStr = 'd50d89'; break
        case '内分泌、营养和代谢疾病':queryDiagnosisTypeStr = 'e00e90'; break
        case '精神和行为障碍':queryDiagnosisTypeStr = 'f00f99'; break
        case '神经系统疾病':queryDiagnosisTypeStr = 'g00g99'; break
        case '眼和附器疾病及耳和乳突疾病':queryDiagnosisTypeStr = 'h00h59'; break
        case '耳和乳突疾病':queryDiagnosisTypeStr = 'h60h95'; break
        case '循环系统疾病':queryDiagnosisTypeStr = 'i00i99'; break
        case '呼吸系统疾病':queryDiagnosisTypeStr = 'j00j99'; break
        case '消化系统疾病':queryDiagnosisTypeStr = 'k00k93'; break
        case '皮肤和皮下组织疾病':queryDiagnosisTypeStr = 'l00l99'; break
        case '肌肉骨骼系统和结缔组织疾病':queryDiagnosisTypeStr = 'm00m99'; break
        case '泌尿生殖系统疾病':queryDiagnosisTypeStr = 'n00n99'; break
        case '妊娠、分娩和产褥期':queryDiagnosisTypeStr = 'o00o99'; break
        case '起源于围生期的某些情况':queryDiagnosisTypeStr = 'p00p96'; break
        case '先天性畸形、变形和染色体异常':queryDiagnosisTypeStr = 'q00q99'; break
        case '症状、体征和临床与实验室异常所见，不可归类在他处者':queryDiagnosisTypeStr = 'r00r99'; break
        case '损伤、中毒和外因的某些其他后果':queryDiagnosisTypeStr = 's00t98'; break
        case '疾病和死亡的外因':queryDiagnosisTypeStr = 'v01y98'; break
        case '用于特殊目的的编码':queryDiagnosisTypeStr = 'u00u99'; break
        case '影响健康状态和与保健机构接触的因素':queryDiagnosisTypeStr = 'z00z99'; break
        case '其他':queryDiagnosisTypeStr = 'other'; break
      }
      this.$router.push({ path: '/common/queryMedicalDetail',
        query: {
          queryDiagnosisType: queryDiagnosisTypeStr,
          queryIcdName: row.diagnosisType,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    changeSelectQueryType (value) {
      this.getList()
      this.getCount()
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString,
        icd_type: 'ICD-10' // 只查询疾病信息
      }
      queryLikeIcdsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSelect (item) {
      this.listQuery.queryIcd = item.icdCodg
      this.getList()
      this.getCount()
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
      this.getCount()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleResetSearch () {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.cy_start_date = null
      this.cy_end_date = null
      this.getList()
      this.getCount()
    },
    getChart (barData, xAxisData, lineData, patientsRate, legendData, profttl) {
      let option = {
        title: [{ text: profttl, left: '10', top: '1', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }],
        tooltip: {
          trigger: 'item',
          formatter: function (param) {
            return '病种名称：' + xAxisData[param.dataIndex] + '</br>' +
              '病种人次：' + '：' + barData[param.dataIndex] + '</br>' +
              '人次占比：' + patientsRate[param.dataIndex] + '%' + '</br>' +
              '累计占比：' + lineData[param.dataIndex] + '%'
          }
        },
        legend: [{
          data: legendData,
          top: '1',
          left: 'right',
          selectedMode: false,
          formatter: function (name) {
            if (name == '人次') {
              return name
            } else if (name == '累计占比') {
              return name + '（红点：累计前80%）'
            }
          }
        }],
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            interval: 0,
            fontSize: 10,
            rotate: 20,
            formatter: function (value) {
              return (value.length > 8 ? (value.slice(0, 8) + '..') : value)
            }
          }
        },
        yAxis: [
          { type: 'value',
            position: 'left',
            name: '单位：人',
            axisLabel: {
              formatter: function (value) {
                if (value > 10000) {
                  return (Number(value) / 10000).toFixed(1) + '万'
                } else {
                  return Number(value).toFixed(1)
                }
              }
            }
          },
          { type: 'value', position: 'right', name: '单位：%', splitLine: { show: false } }
        ],
        series: [{
          name: legendData[0],
          data: barData,
          type: 'bar',
          yAxisIndex: 0,
          label: {
            show: true,
            fontSize: 9,
            position: 'top',
            formatter: function (param) {
              if (param.value > 10000) {
                return (Number(param.value) / 10000).toFixed(1) + '万人'
              } else {
                return param.value + '人'
              }
            }
          },
          itemStyle: {
            color: 'rgba(40,138,242,0.7)'
          }
        },
        {
          name: legendData[1],
          data: lineData,
          type: 'line',
          symbol: 'circle',
          yAxisIndex: 1,
          label: {
            show: true,
            position: 'top',
            fontSize: 10,
            formatter: function (param) {
              return Number(param.value).toFixed(2)
            }
          },
          itemStyle: {
            color: function (param) {
              if (param.value < 80) {
                return '#FD5E51'
              } else {
                return '#516FFF'
              }
            }

          }
        }],
        grid: {
          top: '55',
          bottom: '45',
          left: '60',
          right: '30'
        }
      }
      let diseaseClass = echarts.getInstanceByDom(document.getElementById('diseaseClass'))
      if (diseaseClass) {
        diseaseClass.clear()
      } else {
        diseaseClass = echarts.init(document.getElementById('diseaseClass'))
      }
      diseaseClass.setOption(option)
      return diseaseClass
    },
    exportExcel () {
      let tableId = 'diseaseTable'
      let fileName = '全院病种分析'
      elExportExcel(tableId, fileName)
    },
    refresh () {
      this.reload()
    }
  }
}
</script>
<style scoped>
/deep/ .el-scrollbar__wrap {
  max-height: 450px;
}
/deep/ .el-autocomplete-suggestion li {
  line-height: 20px;
}
.code {
  font-size: 12px;
  color: #000000;
  text-overflow: ellipsis;
  overflow: hidden;
}
.name {
  font-size: 10px;
  color: #9b9b9b;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
