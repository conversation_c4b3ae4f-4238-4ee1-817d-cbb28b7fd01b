import request from '@/utils/request'

let PREFIX = '/firstPage2/'

/**
 * 查询首页 "首页基本情况"
 * @param params
 * @returns {*}
 */
export function getBaseInfo (params) {
  return request({
    url: PREFIX + 'queryBaseInfo',
    method: 'post',
    params: params
  })
}

/**
 * 查询首页 "逆差预测"
 * @param params
 * @returns {*}
 */
export function getForecastInfo (params) {
  return request({
    url: PREFIX + 'queryForecastInfo',
    method: 'post',
    params: params
  })
}

/**
 * 查询首页 "病例质控"
 * @param params
 * @returns {*}
 */
export function getControlInfo (params) {
  return request({
    url: PREFIX + 'queryControlInfo',
    method: 'post',
    params: params
  })
}
