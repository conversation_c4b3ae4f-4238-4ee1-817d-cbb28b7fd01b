<template>
  <el-form :inline="true" :model="value" size="mini" style="margin-top:-18px;">

    <el-header
        style="height: 30px;width:140px;line-height: 30px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 10px">
      <span style="margin-left:10px;">基本住院信息</span>
    </el-header>
    <el-row v-show="value.showJsqd">
      <el-form-item label="住院医疗类型">
        <div id="b38" style="border-radius: 5px">
          <el-radio v-model="value.somHiInvyBasInfo.b38" label="1" border size="mini">住院</el-radio>
          <el-radio v-model="value.somHiInvyBasInfo.b38" label="2" border size="mini">日间手术</el-radio>
        </div>
        <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b38 }}</div>
      </el-form-item>
    </el-row>
    <el-row v-show="value.showJsqdAndBa">
      <el-form-item label="入院途径">
        <el-radio-group id="b11c" style="border-radius: 5px" v-model="value.somHiInvyBasInfo.b11c">
          <el-radio label="1" border size="mini">急诊</el-radio>
          <el-radio label="2" border size="mini">门诊</el-radio>
          <el-radio label="3" border size="mini">其他医疗机构</el-radio>
          <el-radio label="9" border size="mini">其他</el-radio>
        </el-radio-group>
        <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b11c }}</div>
        <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.b11c }}</div>
      </el-form-item>
    </el-row>
    <el-row v-show="value.showJsqd">
      <el-form-item label="治疗类别">
        <el-radio-group id="b39" v-model="value.somHiInvyBasInfo.b39" style="border-radius: 5px">
          <el-radio label="1" border size="mini">西医</el-radio>
          <el-radio label="2" border size="mini">中医</el-radio>
          <el-radio label="2.1" border size="mini">中医（中医）</el-radio>
          <el-radio label="2.2" border size="mini">中医（名族医）</el-radio>
          <el-radio label="3" border size="mini">中西医</el-radio>
        </el-radio-group>
        <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b39 }}</div>
      </el-form-item>
    </el-row>
    <el-row>
      <el-col :span="6" v-show="value.showJsqdAndBa">
        <el-form-item label="入院时间">
          <el-date-picker id="b12" class="formInput"
                          v-model="value.somHiInvyBasInfo.b12" size="mini"
                          type="datetime"
                          placeholder="请选择入院时间">
          </el-date-picker>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b12 }}</div>
          <div v-if="value.logicErrorsMap!=null" class="errorNote">{{ value.logicErrorsMap.b12 }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.b12 }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6" v-show="value.showJsqdAndBa">
        <el-form-item label="入院科别">
          <div id="b13c" style="height: 30px;border-radius: 5px">
            <select-tree v-model="b13c" :options="depts" :props="defaultProps" placeholder="请选择入院科别"/>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b13c }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.b13c }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6" v-show="value.showJsqdAndBa">
        <el-form-item label="转科科别">
          <div id="b21c" style="height: 30px;border-radius: 5px">
            <select-tree v-model="b21c" :options="depts" :props="defaultProps" placeholder="请选择转科科别"/>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b21c }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.b21c }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6" v-show="value.showBa">
        <el-form-item label="入院病房">
          <el-input v-model="value.somHiInvyBasInfo.b14n" placeholder="请选择入院病房" class="formInput"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6" v-show="value.showJsqdAndBa">
        <el-form-item label="出院时间">
          <el-date-picker id="b15" class="formInput"
                          v-model="value.somHiInvyBasInfo.b15" size="mini"
                          type="datetime"
                          placeholder="请选择出院时间">
          </el-date-picker>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b15 }}</div>
          <div v-if="value.logicErrorsMap!=null" class="errorNote">{{ value.logicErrorsMap.b15 }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.b15 }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6" v-show="value.showJsqdAndBa">
        <el-form-item label="出院科别">
          <div id="b16c" style="height: 30px;border-radius: 5px">
            <select-tree v-model="b16c" :options="depts" :props="defaultProps" placeholder="请选择出院科别"/>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b16c }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.b16c }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6" v-show="value.showJsqdAndBa">
        <el-form-item>
          <div style="display: flex">
            <span style="width:65px;font-size: 13px;color: #606266;">实际住院</span>
            <el-input id="b20" v-model="value.somHiInvyBasInfo.b20" type="number" min="0" placeholder="请输入实际天数"
                      style="width: 66%;"></el-input>
            <span style="width: 18px;font-size: 13px;color: #606266;margin-left:5px;">天</span>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b20 }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.b20 }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6" v-show="value.showBa">
        <el-form-item label="出院病房">
          <el-input v-model="value.somHiInvyBasInfo.b17n" placeholder="请选择出院病房" class="formInput"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-show="value.showBa">
      <el-col :span="6">
        <el-form-item label="质控日期">
          <el-date-picker id="b33" class="formInput"
                          v-model="value.somHiInvyBasInfo.b33" size="mini"
                          type="datetime"
                          placeholder="请选择质控日期">
          </el-date-picker>
          <div v-if="value.logicErrorsMap!=null" class="errorNote">{{ value.logicErrorsMap.b33 }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="病案质量">
          <el-input v-model="value.somHiInvyBasInfo.b30c" placeholder="请输入病案质量" class="formInput"></el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <el-header
        style="height: 30px;width:168px;line-height: 30px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 10px">
      <span style="margin-left:10px;">中西医诊断及手术</span>
    </el-header>
    <el-row :gutter="0" v-show="value.showJsqdAndBa">
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 15px;">门（急）诊诊断（西医诊断）</span>
            <el-input id="c02n" v-model="value.somHiInvyBasInfo.c02n" style="width:133px"
                      placeholder="西医门诊疾病诊断"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.c02n }}</div>
          <div v-if="value.logicErrorsMap!=null" class="errorNote">{{ value.logicErrorsMap.c02n }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.c02n }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 15px;">西医门诊疾病代码</span>
            <el-input id="c01c" v-model="value.somHiInvyBasInfo.c01c" style="width:133px"
                      placeholder="西医门诊疾病代码"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.c01c }}</div>
          <div v-if="value.logicErrorsMap!=null" class="errorNote">{{ value.logicErrorsMap.c01c }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.c01c }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 15px;">门（急）诊诊断（中医诊断）</span>
            <el-input id="c36n" v-model="value.somHiInvyBasInfo.c36n" style="width:133px;"
                      placeholder="中医门诊疾病诊断"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.c36n }}</div>
          <div v-if="value.logicErrorsMap!=null" class="errorNote">{{ value.logicErrorsMap.c36n }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.c36n }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 15px;">中医门诊疾病代码</span>
            <el-input id="c35c" v-model="value.somHiInvyBasInfo.c35c" style="width:133px"
                      placeholder="请输入中医门诊疾病代码"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.c35c }}</div>
          <div v-if="value.logicErrorsMap!=null" class="errorNote">{{ value.logicErrorsMap.c35c }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.c35c }}</div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="0" v-show="value.showBa">
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 28px;">病理诊断名称</span>
            <el-input id="c10n" v-model="value.somHiInvyBasInfo.c10n" style="width:133px"
                      placeholder="病理诊断名称"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.c10n }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.c10n }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 28px;">病理诊断编码</span>
            <el-input id="c09c" v-model="value.somHiInvyBasInfo.c09c" style="width:133px"
                      placeholder="病理诊断编码"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.c09c }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.c09c }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 15px;">损伤、中毒外部原因名称</span>
            <el-input id="c13n" v-model="value.somHiInvyBasInfo.c13n" style="width:133px;"
                      placeholder="损伤、中毒外部原因名称"></el-input>
          </div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.c13n }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 15px;">损伤、中毒外部原因编码</span>
            <el-input id="c12c" v-model="value.somHiInvyBasInfo.c12c" style="width:133px;"
                      placeholder="损伤、中毒外部原因编码"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.c12c }}</div>
          <div v-if="value.logicErrorsMap!=null" class="errorNote">{{ value.logicErrorsMap.c12c }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.c12c }}</div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="0" v-show="value.showBa">
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 28px;">病理号</span>
            <el-input id="c11" v-model="value.somHiInvyBasInfo.c11" style="width:133px" placeholder="病理号"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.c11 }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.c11 }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 28px;">有无药物过敏</span>
            <el-input id="c24c" v-model="value.somHiInvyBasInfo.c24c" style="width:133px"
                      placeholder="有无药物过敏"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.c24c }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.c24c }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 15px;">过敏药物名称</span>
            <el-input v-model="value.somHiInvyBasInfo.c25" style="width:133px;" placeholder="过敏药物名称"></el-input>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 15px;">ABO血型</span>
            <el-input id="c26c" v-model="value.somHiInvyBasInfo.c26c" style="width:133px;"
                      placeholder="ABO血型"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.c26c }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.c26c }}</div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="0" v-show="value.showBa">
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 28px;">Rh血型</span>
            <el-input id="c27c" v-model="value.somHiInvyBasInfo.c27c" style="width:133px" placeholder="Rh血型"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.c27c }}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.c27c }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:100px;font-size: 13px;color: #606266;line-height: 28px;">死亡患者尸检</span>
            <el-input v-model="value.somHiInvyBasInfo.c34c" style="width:133px" placeholder="死亡患者尸检"></el-input>
          </div>
        </el-form-item>
      </el-col>
    </el-row>

    <el-tabs type="border-card">
      <el-tab-pane>
        <span slot="label"><svg-icon icon-class="medicalQuality-settleListDisease"></svg-icon> 疾病</span>
        <div class="table-container" style="margin-top:-10px;">
          <el-table ref="zxyzdbListTable"
                    stripe
                    size="mini"
                    :data="value.busDiseaseDiagnosisTrimList"
                    max-height="300"
                    style="width: 100%"
                    border>
            <el-table-column
                label="序号"
                type="index"
                width="50">
            </el-table-column>
            <el-table-column label="诊断类别" align="center" width="90px">
              <template slot-scope="scope">{{ scope.row.seq | formatSno }}</template>
            </el-table-column>
            <el-table-column label="出院西医诊断" align="center" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <el-select v-model="scope.row.c07n1" size="mini" filterable remote clearable placeholder="请输入诊断"
                           :remote-method="queryDiagnosisList" @change="getChangeForName">
                  <el-option
                      v-for="item in diagnosisList"
                      :key="item.icdName"
                      :label="item.icdName"
                      :value="item.icdName">
                    <span style="float: left">{{ item.icdName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.icdCodg }}</span>
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="疾病代码（西医）" align="center">
              <template slot-scope="scope">
                <el-select v-model="scope.row.c06c1" size="mini" filterable remote clearable placeholder="请输入编码"
                           :remote-method="queryDiagnosisList" @change="getChangeForCode">
                  <el-option
                      v-for="item in diagnosisList"
                      :key="item.icdCodg"
                      :label="item.icdCodg"
                      :value="item.icdCodg">
                    <span style="float: left">{{ item.icdCodg }}</span>
                    <span style="float: right; color: #8492a6; font-size: 12px">{{ item.icdName }}</span>
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="入院病情（西医）" align="center" width="130px">
              <template slot-scope="scope">
                <el-select v-model="scope.row.c08c1" size="mini" placeholder="请选择入院病情">
                  <el-option
                      v-for="item in dictVoList.RYBQ"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="出院中医诊断" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.c07n2" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="疾病代码（中医）" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.c06c2" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="入院病情（中医）" align="center" width="130px">
              <template slot-scope="scope">
                <el-select v-model="scope.row.c08c2" size="mini" placeholder="请选择入院病情">
                  <el-option
                      v-for="item in dictVoList.RYBQ"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="danger"
                    @click="deleteDiseaseList(scope.$index, scope.row)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button type="success" size="mini" icon="el-icon-plus" style="margin-top: 3px;" @click="addDiseaseList">
            添加一行
          </el-button>
        </div>
        <div v-if="value.completeErrorsMap!=null" class="codeErrorNote">
          {{ value.completeErrorsMap.c03c }};{{ value.completeErrorsMap.c04n }};{{ value.completeErrorsMap.c05c }};
        </div>
        <div v-if="value.logicErrorsMap!=null" class="codeErrorNote">
          {{ value.logicErrorsMap.dscg_diag_codg }};
        </div>
        <div v-if="value.scoreErrorsMap!=null" class="codeErrorNote">
          {{ value.scoreErrorsMap.c03c }};{{ value.scoreErrorsMap.c04n }};{{ value.scoreErrorsMap.c05c }};
        </div>
      </el-tab-pane>
      <el-tab-pane>
        <span slot="label"><svg-icon icon-class="medicalQuality-settleListOperate"></svg-icon>手术</span>
        <div class="table-container" style="margin-top:-10px;">
          <el-table ref="ssxxListTable"
                    stripe
                    size="mini"
                    :data="value.busOperateDiagnosisList"
                    max-height="300"
                    style="width: 100%"
                    border>
            <el-table-column
                label="序号"
                type="index"
                width="50">
            </el-table-column>
            <el-table-column label="手术及操作名称" align="center">
              <template slot-scope="scope">
                <el-select v-model="scope.row.c36n" size="mini" filterable remote placeholder="请输入名称"
                           :remote-method="queryOperateList" @change="getChangeForOprName">
                  <el-option
                      v-for="item in operateList"
                      :key="item.icdName"
                      :label="item.icdName"
                      :value="item.icdName">
                    <span style="float: left">{{ item.icdName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.icdCodg }}</span>
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="手术及操作代码" align="center">
              <template slot-scope="scope">
                <el-select v-model="scope.row.c35c" size="mini" filterable remote placeholder="请输入编码"
                           :remote-method="queryOperateList" @change="getChangeForOprCode">
                  <el-option
                      v-for="item in operateList"
                      :key="item.icdCodg"
                      :label="item.icdCodg"
                      :value="item.icdCodg">
                    <span style="float: left">{{ item.icdCodg }}</span>
                    <span style="float: right; color: #8492a6; font-size: 12px">{{ item.icdName }}</span>
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="手术及操作日期" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.oprn_oprt_date" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="麻醉方式" align="center">
              <template slot-scope="scope">
                <el-select v-model="scope.row.c43" size="mini" placeholder="请选择麻醉方式">
                  <el-option
                      v-for="item in dictVoList.MZFS"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="术者医师姓名" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.oprn_oprt_oper_name" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="术者医师代码" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.c39c" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="麻醉医师姓名" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.oprn_oprt_anst_dr_name" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="麻醉医师代码" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.oprn_oprt_anst_dr_code" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="danger"
                    @click="deleteOperateList(scope.$index, scope.row)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button type="success" size="mini" icon="el-icon-plus" style="margin-top: 3px;" @click="addOperateList">
            添加一行
          </el-button>
        </div>
        <div v-if="value.completeErrorsMap!=null" class="codeErrorNote">
          {{ value.completeErrorsMap.c14x01c }};{{ value.completeErrorsMap.c15x01n }};{{ value.completeErrorsMap.c17x01 }};
          {{ value.completeErrorsMap.c21x01c }};{{ value.completeErrorsMap.c22x01c }};
        </div>
        <div v-if="value.logicErrorsMap!=null" class="codeErrorNote">
          {{ value.logicErrorsMap.oprn_oprt_date }};
        </div>
        <div v-if="value.scoreErrorsMap!=null" class="codeErrorNote">
          {{ value.scoreErrorsMap.c35c }};
        </div>
      </el-tab-pane>
    </el-tabs>

    <el-header
        style="height: 30px;width:168px;line-height: 30px;margin-left:-25px;color:white;background-color: #1e6abc;margin-top: 10px;margin-bottom: 10px">
      <span style="margin-left:10px;">诊疗及重症监护室</span>
    </el-header>
    <el-row :gutter="0" v-show="value.showJsqd">
      <el-form-item>
        <span style="width:100px;font-size: 13px;color: #606266;">呼吸机使用时间</span>
        <el-input v-model="value.somHiInvyBasInfo.c42" style="width:80px;height:20px;"></el-input>
        <span style="width:15px;font-size: 13px;color: #606266;">天</span>
        <el-input v-model="value.somHiInvyBasInfo.c43" style="width:80px;height:20px;"></el-input>
        <span style="width:30px;font-size: 13px;color: #606266;">小时</span>
        <el-input v-model="value.somHiInvyBasInfo.c44" style="width:80px;height:20px;"></el-input>
        <span style="width:30px;font-size: 13px;color: #606266;">分钟</span>
      </el-form-item>
    </el-row>
    <el-row :gutter="0">
      <el-form-item>
        <span style="font-size: 13px;color: #606266;">颅脑损伤患者昏迷时间</span>
        <span style="font-size: 13px;color: #606266;margin-left: 20px;">入院前</span>
        <el-input v-model="value.somHiInvyBasInfo.c28" style="width:80px;height:20px;"></el-input>
        <span style="font-size: 13px;color: #606266;">天</span>
        <el-input v-model="value.somHiInvyBasInfo.c29" style="width:80px;height:20px;"></el-input>
        <span style="font-size: 13px;color: #606266;">小时</span>
        <el-input v-model="value.somHiInvyBasInfo.c30" style="width:80px;height:20px;"></el-input>
        <span style="font-size: 13px;color: #606266;">分钟</span>
        <span style="font-size: 13px;color: #606266;margin-left: 25px;">入院后</span>
        <el-input v-model="value.somHiInvyBasInfo.c31" style="width:80px;height:20px;"></el-input>
        <span style="font-size: 13px;color: #606266;">天</span>
        <el-input v-model="value.somHiInvyBasInfo.c32" style="width:80px;height:20px;"></el-input>
        <span style="font-size: 13px;color: #606266;">小时</span>
        <el-input v-model="value.somHiInvyBasInfo.c33" style="width:80px;height:20px;"></el-input>
        <span style="font-size: 13px;color: #606266;">分钟</span>
        <div v-if="value.logicErrorsMap!=null" class="errorNote">
          {{ value.logicErrorsMap.c28 }};{{ value.logicErrorsMap.c31 }}
        </div>
      </el-form-item>
    </el-row>
    <div class="table-container" style="margin-top:-10px;" v-show="value.showJsqd">
      <el-table ref="zzjhxxListTable"
                size="mini"
                stripe
                :data="value.busIcuList"
                max-height="200"
                style="width: 100%"
                border>
        <el-table-column
            label="序号"
            type="index"
            width="50">
        </el-table-column>
        <el-table-column label="重症监护病房类型（CCU、NICU、EICU、SICU、PICU、RICU、其他）" align="center">
          <template slot-scope="scope">{{ scope.row.b40 | formatIsEmpty }}</template>
        </el-table-column>
        <el-table-column label="进重症监护室时间（_年_月_日_时_分）" align="center">
          <template slot-scope="scope">{{ scope.row.b41 | formatDateFormat }}</template>
        </el-table-column>
        <el-table-column label="出重症监护室时间（_年_月_日_时_分）" align="center">
          <template slot-scope="scope">{{ scope.row.b42 | formatIsEmpty }}</template>
        </el-table-column>
        <el-table-column label="合计（小时）" width="100" align="center">
          <template slot-scope="scope">{{ scope.row.b43 | formatIsEmpty }}</template>
        </el-table-column>
      </el-table>
    </div>
    <el-row :gutter="0" style="margin-top:10px;" v-show="value.showJsqd">
      <el-col :span="6">
        <el-form-item label="输血品种">
          <el-input v-model="value.somHiInvyBasInfo.c45" placeholder="请输入输血品种" class="formInput"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="输血量">
          <el-input v-model="value.somHiInvyBasInfo.c46" placeholder="请输入输血量" class="formInput"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="输血计量单位">
          <div style="display: flex">
            <el-input v-model="value.somHiInvyBasInfo.c47" placeholder="输血计量单位" style="width: 133px;"></el-input>
          </div>
        </el-form-item>
      </el-col>
    </el-row>

    <el-header
        style="height: 30px;width:160px;line-height: 30px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 10px">
      <span style="margin-left:10px;">护理及离院信息</span>
    </el-header>
    <el-row :gutter="0" v-show="value.showJsqd">
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:100px;font-size: 13px;color: #606266;">特殊级护理天数</span>
            <el-input v-model="value.somHiInvyBasInfo.xymzzd" style="width:133px" placeholder="特殊级护理天数"></el-input>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">一级护理天数</span>
            <el-input v-model="value.somHiInvyBasInfo.b44" style="width:133px" placeholder="一级护理天数"></el-input>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex">
            <span style="width:88px;font-size: 13px;color: #606266;">二级护理天数</span>
            <el-input v-model="value.somHiInvyBasInfo.b45" style="width:133px;" placeholder="二级护理天数"></el-input>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex">
            <span style="width:88px;font-size: 13px;color: #606266;">三级护理天数</span>
            <el-input v-model="value.somHiInvyBasInfo.b46" style="width:133px;" placeholder="三级护理天数"></el-input>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-form-item label="离院方式" v-show="value.showJsqdAndBa">
        <el-radio-group id="b34c" v-model="value.somHiInvyBasInfo.b34c" style="border-radius: 5px">
          <el-radio label="1" border size="mini">医嘱离院</el-radio>
          <el-radio label="2" border size="mini">医嘱转院</el-radio>
          <el-radio label="3" border size="mini">医嘱转社区卫生服务机构/乡镇卫生院</el-radio>
          <el-radio label="4" border size="mini">非医嘱离院</el-radio>
          <el-radio label="5" border size="mini">死亡</el-radio>
          <el-radio label="9" border size="mini">其他</el-radio>
        </el-radio-group>
        <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b34c }}</div>
        <div v-if="value.logicErrorsMap!=null" class="errorNote">{{ value.logicErrorsMap.b34c }}</div>
        <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.b34c }}</div>
      </el-form-item>
    </el-row>
    <el-row v-show="value.showJsqdAndBa">
      <el-form-item label="拟接收机构名称">
        <div style="display: flex">
          <el-input v-model="value.somHiInvyBasInfo.b49" placeholder="机构名称" style="width: 130px;"></el-input>
        </div>
      </el-form-item>
      <el-form-item label="拟接收机构代码">
        <div style="display: flex">
          <el-input v-model="value.somHiInvyBasInfo.b48" placeholder="机构代码" style="width: 140px;"></el-input>
        </div>
      </el-form-item>
    </el-row>
    <el-row v-show="value.showJsqdAndBa">
      <el-form-item>
        <div style="display: flex;">
          <span style="width:100px;font-size: 13px;color: #606266;line-height: 15px;">是否有出院31天内再住院计划</span>
          <el-radio-group id="b36c" v-model="value.somHiInvyBasInfo.b36c" style="border-radius: 5px">
            <el-radio label="1" border size="mini" style="width:62px">无</el-radio>
            <el-radio label="2" border size="mini" style="width:62px">有</el-radio>
          </el-radio-group>
        </div>
        <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b36c }}</div>
        <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{ value.scoreErrorsMap.b36c }}</div>
      </el-form-item>
      <el-form-item label="目的">
        <div style="display: flex">
          <el-input v-model="value.somHiInvyBasInfo.b37" placeholder="目的" style="width: 133px;"></el-input>
        </div>
      </el-form-item>
    </el-row>
    <el-header
        style="height: 30px;width:110px;line-height: 30px;margin-left:-25px;color:white;background-color: #1e6abc;margin-top: 10px;margin-bottom: 10px">
      <span style="margin-left:10px;">医师信息</span>
    </el-header>
    <el-row :gutter="0">
      <el-col :span="6" v-show="value.showJsqdAndBa">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">主治医师姓名</span>
            <el-input v-model="value.somHiInvyBasInfo.b52n" style="width:148px" placeholder="主治医师姓名"></el-input>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6" v-show="value.showJsqdAndBa">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">主治医师代码</span>
            <el-input v-model="value.somHiInvyBasInfo.b51c" style="width:134px" placeholder="主治医师代码"></el-input>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6" show="value.showBa">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">科主任姓名</span>
            <el-input id="b22n" v-model="value.somHiInvyBasInfo.b22n" style="width:148px"
                      placeholder="科主任姓名"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b22n }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6" show="value.showBa">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">科主任代码</span>
            <el-input v-model="value.somHiInvyBasInfo.b22c" style="width:134px" placeholder="科主任代码"></el-input>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="0" v-show="value.showBa">
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex">
            <span style="width:90px;font-size: 13px;color: #606266;line-height: 15px;">主（副主）任医师姓名</span>
            <el-input id="b23n" v-model="value.somHiInvyBasInfo.b23n" style="width:145px;"
                      placeholder="主（副主）任医师姓名"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b23n }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex">
            <span style="width:90px;font-size: 13px;color: #606266;line-height: 15px;">主（副主）任医师代码</span>
            <el-input v-model="value.somHiInvyBasInfo.b23c" style="width:132px;"
                      placeholder="主（副主）任医师代码"></el-input>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">住院医师姓名</span>
            <el-input id="b25n" v-model="value.somHiInvyBasInfo.b25n" style="width:148px"
                      placeholder="住院医师姓名"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b25n }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">住院医师代码</span>
            <el-input v-model="value.somHiInvyBasInfo.b25c" style="width:134px" placeholder="住院医师代码"></el-input>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="0" v-show="value.showBa">
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">责任护士姓名</span>
            <el-input id="b26n" v-model="value.somHiInvyBasInfo.b26n" style="width:148px"
                      placeholder="责任护士姓名"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b26n }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">责任护士代码</span>
            <el-input v-model="value.somHiInvyBasInfo.b26c" style="width:134px" placeholder="责任护士代码"></el-input>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">进修医师姓名</span>
            <el-input v-model="value.somHiInvyBasInfo.b27n" style="width:148px" placeholder="进修医师姓名"></el-input>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">进修医师代码</span>
            <el-input v-model="value.somHiInvyBasInfo.b27c" style="width:134px" placeholder="进修医师代码"></el-input>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="0" v-show="value.showBa">
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">实习医师</span>
            <el-input v-model="value.somHiInvyBasInfo.b28" style="width:148px" placeholder="实习医师"></el-input>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">编码员姓名</span>
            <el-input id="b29n" v-model="value.somHiInvyBasInfo.b29n" style="width:134px"
                      placeholder="编码员姓名"></el-input>
          </div>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{ value.completeErrorsMap.b29n }}</div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">编码员代码</span>
            <el-input v-model="value.somHiInvyBasInfo.b29c" style="width:148px" placeholder="编码员代码"></el-input>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">质控医师姓名</span>
            <el-input v-model="value.somHiInvyBasInfo.b31n" style="width:134px" placeholder="质控医师姓名"></el-input>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="0" v-show="value.showBa">
      <el-col :span="6">
        <el-form-item>
          <div style="display: flex;">
            <span style="width:88px;font-size: 13px;color: #606266;">质控医师代码</span>
            <el-input v-model="value.somHiInvyBasInfo.b31c" style="width:148px" placeholder="质控医师代码"></el-input>
          </div>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="6" v-show="false">
        <el-form-item>
          <!--隐藏框用于触发查看不到字段的隐藏和展示-->
          <el-input v-model="value.showFlag"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import SelectTree from '@/components/SelectTree/index'
import { queryLikeIcdsByPram, querySelectTreeAndSelectList } from '@/api/common/drgCommon'
import { formaterDict } from '@/utils/dict'
import { formatDate } from '@/utils/date'

export default {
  name: 'SettleListZyzlInfo',
  components: { SelectTree },
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      b13c: null,
      b16c: null,
      b21c: null,
      diagnosisList: {}, // 诊断
      operateList: {} // 手术
    }
  },
  props: {
    value: Object
  },
  watch: {
    b13c: function () {
      this.value.somHiInvyBasInfo.b13c = this.b13c
    },
    b16c: function () {
      this.value.somHiInvyBasInfo.b16c = this.b16c
    },
    b21c: function () {
      this.value.somHiInvyBasInfo.b21c = this.b21c
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatSno (value) {
      if (value == 0) {
        return '主要诊断'
      } else if (value > 0) {
        return '其他诊断' + value
      } else {
        return '未填诊断'
      }
    },
    formatDateFormat (value) {
      if (value) {
        let updt_date = new Date(value)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    }
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'RYBQ,MZFS')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
        this.b13c = this.value.somHiInvyBasInfo.b13c
        this.b16c = this.value.somHiInvyBasInfo.b16c
        this.b21c = this.value.somHiInvyBasInfo.b21c
      })
    },
    /**
     * 码表渲染方法
     */
    dictFormatter (col, row, dic_type) {
      return formaterDict(row[col.property], this.dictVoList[dic_type])
    },
    addDiseaseList () {
      const bddtl = this.value.busDiseaseDiagnosisTrimList
      // 新增一个诊断类别
      if (bddtl.length == 0) {
        bddtl.push({
          seq: 0,
          c06c1: null,
          c07n1: null,
          c08c1: null,
          c50c1: null,
          c06c2: null,
          c07n2: null,
          c08c2: null,
          c50c2: null
        })
      } else {
        bddtl.push({
          seq: bddtl[bddtl.length - 1].seq + 1,
          c06c1: null,
          c07n1: null,
          c08c1: null,
          c50c1: null,
          c06c2: null,
          c07n2: null,
          c08c2: null,
          c50c2: null
        })
      }
      const bddtlMap = { busDiseaseDiagnosisTrimList: bddtl }
      Object.assign(this.value, bddtlMap)
    },
    deleteDiseaseList (index, row) {
      this.$confirm('是否要进行删除操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const bddtl = this.value.busDiseaseDiagnosisTrimList
        for (let i = 0; i < bddtl.length; i++) {
          if (i == index - 1) {
            bddtl.splice(index, 1)
          }
        }
      })
    },
    addOperateList () {
      const bodl = this.value.busOperateDiagnosisList
      if (bodl.length == 0) {
        bodl.push({
          hiSetlInvyId: this.$route.query.id,
          seq: 0,
          c35c: null,
          c36n: null,
          oprn_oprt_date: null,
          oprn_oprt_lv: null,
          c39c: null,
          oprn_oprt_oper_name: null,
          oprn_oprt_1_asit: null,
          oprn_oprt_2_asit: null,
          c42: null,
          c43: null,
          oprn_oprt_anst_dr_code: null,
          oprn_oprt_anst_dr_name: null
        })
      } else {
        bodl.push({
          hiSetlInvyId: this.$route.query.id,
          seq: bodl[bodl.length - 1].seq + 1,
          c35c: null,
          c36n: null,
          oprn_oprt_date: null,
          oprn_oprt_lv: null,
          c39c: null,
          oprn_oprt_oper_name: null,
          oprn_oprt_1_asit: null,
          oprn_oprt_2_asit: null,
          c42: null,
          c43: null,
          oprn_oprt_anst_dr_code: null,
          oprn_oprt_anst_dr_name: null
        })
      }
      const bodlMap = { busOperateDiagnosisList: bodl }
      Object.assign(this.value, bodlMap)
    },
    deleteOperateList (index, row) {
      this.$confirm('是否要进行删除操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const bodl = this.value.busOperateDiagnosisList
        for (let i = 0; i < bodl.length; i++) {
          if (i == index - 1) {
            bodl.splice(index, 1)
          }
        }
      })
    },
    queryDiagnosisList (queryString) {
      const param = {
        likeQueryString: queryString,
        icd_type: 'ICD-10', // 只查询疾病信息
        icd_codg_ver: '5' // 只查询医保版本编码
      }
      queryLikeIcdsByPram(param).then(response => {
        this.diagnosisList = response.data
      })
    },
    getChangeForName (item) {
      let list = this.value.busDiseaseDiagnosisTrimList
      for (let i = 0; i <= list.length - 1; i++) {
        if (item == list[i].c07n1) {
          for (let j = 0; j <= this.diagnosisList.length - 1; j++) {
            if (item == this.diagnosisList[j].icdName) {
              list[i].c06c1 = this.diagnosisList[j].icdCodg
            }
          }
        }
      }
    },
    getChangeForCode (item) {
      let list = this.value.busDiseaseDiagnosisTrimList
      for (let i = 0; i <= list.length - 1; i++) {
        if (item == list[i].c06c1) {
          for (let j = 0; j <= this.diagnosisList.length - 1; j++) {
            if (item == this.diagnosisList[j].icdCodg) {
              list[i].c07n1 = this.diagnosisList[j].icdName
            }
          }
        }
      }
    },
    queryOperateList (queryString) {
      const param = {
        likeQueryString: queryString,
        icd_type: 'ICD-9', // 只查询手术信息
        icd_codg_ver: '5' // 只查询医保版本编码
      }
      queryLikeIcdsByPram(param).then(response => {
        this.operateList = response.data
      })
    },
    getChangeForOprName (item) {
      let list = this.value.busOperateDiagnosisList
      for (let i = 0; i <= list.length - 1; i++) {
        if (item == list[i].c36n) {
          for (let j = 0; j <= this.operateList.length - 1; j++) {
            if (item == this.operateList[j].icdName) {
              list[i].c35c = this.operateList[j].icdCodg
            }
          }
        }
      }
    },
    getChangeForOprCode (item) {
      let list = this.value.busOperateDiagnosisList
      for (let i = 0; i <= list.length - 1; i++) {
        if (item == list[i].c35c) {
          for (let j = 0; j <= this.operateList.length - 1; j++) {
            if (item == this.operateList[j].icdCodg) {
              list[i].c36n = this.operateList[j].icdName
            }
          }
        }
      }
    }
  }

}
</script>

<style scoped>
.formInput {
  width: 170px;
}

.errorNote {
  color: red;
  font-size: 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 170px;
}

.codeErrorNote {
  color: red;
  font-size: 13px;
  margin-left: 20px;
}

.el-select-dropdown__list .el-select-dropdown__item {
  font-size: 12px;
  height: 30px;
  line-height: 30px;
  text-overflow: ellipsis;
}

</style>
