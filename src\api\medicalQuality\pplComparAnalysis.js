import request from '@/utils/request'

export function getList (params) {
  return request({
    url: '/pplCompareAnalysisController/getList',
    method: 'post',
    data: params
  })
}

export function getInfo (params) {
  return request({
    url: '/pplCompareAnalysisController/getInfo',
    method: 'post',
    data: params
  })
}
export function getIngroup (params) {
  return request({
    url: '/pplCompareAnalysisController/getIngroup',
    method: 'post',
    data: params
  })
}

export function getCost (params) {
  return request({
    url: '/pplCompareAnalysisController/getCost',
    method: 'post',
    data: params
  })
}

export function getCostPay (params) {
  return request({
    url: '/pplCompareAnalysisController/getCostPay',
    method: 'post',
    data: params
  })
}
