<template>
  <div class="app-container">
    <drg-container :headerPercent="11">
        <template slot="header">
          <drg-title-line title="查询条件"/>
          <hospital-cost-report-select @queryData="queryData" />
        </template>
        <template slot="content">
          <iframe :src="url" width="100%" height="100%"></iframe>
        </template>
    </drg-container>
  </div>
</template>
<script>
import { preview, downloadWord } from '@/api/hospitalCost/report/report'
import hospitalCostReportSelect from '@/views/common/hospitalCostReport/hospitalCostReportSelect'
export default {
  name: 'hospitalCostReportCommon',
  props: {
    type: {
      type: String,
      required: true
    },
    downloadName: {
      type: String,
      default: 'report.pdf'
    }
  },
  components: {
    'hospital-cost-report-select': hospitalCostReportSelect
  },
  data: () => ({
    url: ''
  }),
  methods: {
    getReport (queryForm = {}) {
      queryForm.type = this.type
      preview(queryForm).then(result => {
        this.url = this.$somms.getPdfJsUrl(result.data, this.downloadName)
        // const binaryData = []
        // binaryData.push(result.data)
        // //获取blob链接
        // let pdfUrl = window.URL.createObjectURL(
        //   new Blob(binaryData, { type: "application/pdf" })
        // )
        // let baseUrl = 'static/pdfjs/web/viewer.html?file=' + pdfUrl + '&downloadName=' + this.downloadName
        //
        // downloadWord(queryForm).then(result => {
        //   const blob = new Blob([result], { type: 'application/msword' })
        //   this.url = baseUrl + '&wordFile=' + URL.createObjectURL(blob)
        // })
      })
    },
    queryData (queryForm) {
      this.getReport(queryForm)
    }
  }
}
</script>
