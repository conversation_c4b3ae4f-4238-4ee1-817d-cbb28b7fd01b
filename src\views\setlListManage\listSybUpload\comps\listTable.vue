<template>
  <div class="app-container">
    <el-table :id="id"
              ref="elTable"
              :data="data"
              v-loading="loading"
              height="100%"
              :header-cell-style="{'text-align':'center'}"
              @selection-change="handleSelectionChange"
              stripe
              border>
      <el-table-column type="selection" align="center" width="40" :selectable="selectable"/>
      <el-table-column label="序号" type="index" align="center" width="50" />
      <el-table-column label="病案号" prop="medcasCodg" align="right" width="110" v-if="condition3" :key="1"/>
      <el-table-column label="医生名称" prop="drName" align="left" width="80" v-if="condition3" :key="21"/>
      <el-table-column label="姓名" prop="name" width="80" v-if="condition3" :key="2"/>
      <el-table-column label="性别" prop="gend" :formatter="judgeSex" width="50" v-if="condition3" :key="3"/>
      <el-table-column label="年龄" prop="age" align="right" width="50" v-if="condition3" :key="4"/>
      <el-table-column :label="disGpName + '编码'" prop="dipCodg" show-overflow-tooltip v-if="condition1 && isDip" :key="5"/>
      <el-table-column :label="disGpName + '名称'" prop="dipName" show-overflow-tooltip v-if="condition1 && isDip" :key="6"/>
      <el-table-column :label="disGpName + '编码'" prop="drgCodg" show-overflow-tooltip v-if="condition1 && isDrg" :key="5"/>
      <el-table-column :label="disGpName + '名称'" prop="drgName" show-overflow-tooltip v-if="condition1 && isDrg" :key="6"/>
      <el-table-column label="主要诊断" prop="mainDiseaseName" show-overflow-tooltip v-if="condition1" :key="7"/>
      <el-table-column label="主要手术" prop="mainOperatorName" show-overflow-tooltip v-if="condition1" :key="8"/>
      <el-table-column label="住院总费用" prop="inHosTotalCost" align="right" width="100" v-if="condition1" :key="9"/>
      <el-table-column label="出院科室" prop="deptName" width="200" show-overflow-tooltip v-if="condition3" :key="10"/>
      <el-table-column label="出院时间" prop="outHosTime" align="right" width="135" show-overflow-tooltip v-if="condition3" :key="11"/>
      <!--      <el-table-column label="请求参数" prop="reqtPara" align="right" show-overflow-tooltip v-if="condition2" :key="12"/>-->
      <el-table-column label="错误信息" prop="errMsg" align="right" show-overflow-tooltip v-if="condition2" :key="13"/>
      <el-table-column label="上传时间" prop="upldTime" align="right" show-overflow-tooltip v-if="condition2 || condition4 || condition5" :key="14"/>
      <el-table-column label="标识状态"  align="center" show-overflow-tooltip v-if="condition1" :key="16" fixed="right">
        <template slot-scope="scope">
          <i class="som-icon-error-waring som-icon-big" v-if="scope.row.lookOver === '0'"></i>
          <i class="som-icon-success som-icon-big" v-else></i>
        </template>
      </el-table-column>
      <el-table-column label="完成状态"  align="center" width="160" show-overflow-tooltip v-if="condition5" :key="20" fixed="right">
        <template slot-scope="scope">
          <i class="som-icon-success som-icon-big" v-if="scope.row.stasType === '1'"></i>
        </template>
      </el-table-column>

      <el-table-column label="清单校验状态"  align="center" width="160" show-overflow-tooltip v-if="condition1" :key="17" fixed="right">
        <template slot-scope="scope">
          <i class="som-icon-error-waring som-icon-big" v-if="scope.row.chkStas === '0'"></i>
          <i class="som-icon-success som-icon-big" v-else></i>
        </template>
      </el-table-column>
      <!--      <el-table-column label="标识状态记录查看" align="center" width="160" :key="18" fixed="right">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button type="primary" size="mini" icon="el-icon-search" @click="showDialog(scope.row)" circle />-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="复制请求参数" align="center" width="160" :key="19" fixed="right" v-if="condition2 || condition4">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-document-copy" @click="seeRequestParams(scope.row)" circle />
        </template>
      </el-table-column>
      <el-table-column label="查看详情" align="center" width="100" :key="15" fixed="right">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-search" @click="jumpDetails(scope.row)" circle />
        </template>
      </el-table-column>
      <el-table-column label="编辑清单" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-search" @click="editDetails(scope.row)" circle />
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :title="profttl"
      :visible.sync="dialogVisible"
      width="50%">
      <el-table
        :data="form"
        style="width: 100%">
        <el-table-column
          prop="userName"
          label="用户名"
          width="180">
        </el-table-column>
        <el-table-column
          prop="nknm"
          label="昵称"
          width="180">
        </el-table-column>
        <el-table-column
          prop="crteTime"
          label="时间">
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <span>{{scope.row.oprt == 0?'修改标识为未完成' : '修改标识为完成'}}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { querySettleListOpeLog } from '../../../../api/medicalQuality/settleListDetail'

export default {
  name: 'listTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    updateStatus: {
      type: String,
      default: '未上传'
    },
    tabName: {
      type: String,
      default: 'dataTable'
    },
    id: {
      type: String
    },
    // 是否通过校验才能上传
    passValidateUpload: {
      type: Boolean,
      default: true
    }
  },
  data: () => ({
    dialogVisible: false,
    profttl: '标识状态记录查看',
    form: {}
  }),
  updated () {
    this.updateTable()
  },
  computed: {
    // 未上传
    condition1 () {
      return this.updateStarus === '未上传'
    },
    // 上传失败
    condition2 () {
      return this.updateStarus === '上传失败'
    },
    // 上传成功
    condition4 () {
      return this.updateStarus === '上传成功'
    },
    // 状态修改
    condition5 () {
      return this.updateStarus === '状态修改'
    },
    // 所有
    condition3 () {
      return this.condition1 || this.condition2 || this.condition4 || this.condition5
    },
    // 病种分组类型名称
    disGpName () {
      return this.$somms.getGroupTypeName()
    },
    // 是否是DIP
    isDip () {
      return this.$somms.isDIP()
    },
    // 是否是DRG
    isDrg () {
      return this.$somms.isDRG()
    }
  },
  methods: {
    updateTable () {
      this.$nextTick(() => {
        if (this.$refs.elTable) {
          setTimeout(() => {
            this.$refs.elTable.doLayout()
          }, 400)
        }
      })
    },
    jumpDetails (row) {
      this.$router.push({
        path: '/setlListManage/setlListInfo2',
        query: { id: row.id, k00: row.k00, see: true }
      })
    },
    judgeSex (row) {
      switch (row.gend) {
        case '1':
          return '男'
        case '2':
          return '女'
      }
    },
    handleSelectionChange (val) {
      this.$emit('selectData', val)
    },
    seeRequestParams (row) {
      const textarea = document.createElement('textarea')
      textarea.setAttribute('readonly', 'readonly')
      textarea.value = row.reqtPara
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      this.$message.success('复制成功')
    },
    selectable (row) {
      if (this.condition5) {
        if (row.stasType === '1') {
          return false
        }
      } else {
        if ((row.chkStas === '0' || row.lookOver === '0') && this.passValidateUpload) {
          return false
        }
      }
      return true
    },
    showDialog (row) {
      let params = { k00: '' }
      params.k00 = row.k00
      querySettleListOpeLog(params).then(res => {
        this.form = res.data
        this.dialogVisible = true
      })
    },
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },

    editDetails (item) {
      this.$router.push({
        path: '/setlListManage/setlListInfo2',
        query: { id: item.id, k00: item.k00, see: false }
      })
    }
  }
}
</script>

<style scoped>
/deep/ .el-tooltip__popper, .el-tooltip__popper.is-dark{
  max-height: 90%;
}
</style>
