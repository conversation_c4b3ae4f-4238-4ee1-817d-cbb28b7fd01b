<template>
  <div class="sidebar-container som-wd-one-hundred">
    <div class="company-logo">
      <div class="logo" @click="toggleSideBar">
        <div class="som-flex-center" style="width: 100%" v-show="lessen">
          <!--          <img class="logo-image" v-show="lessen"-->
          <!--               :style="iconFullStyle"-->
          <!--               :src="require('@/assets/images/login/jly.png')" />-->
<!--          <img class="logo-image"-->
<!--               :src="require('@/assets/images/login/jly2.png')"/>-->
          <div class="logo-title">
<!--                        <div style="height: 17px;">DRG运营决策分析平台</div>-->
                        <div style="height: 17px;">DIP运营决策分析平台</div>
            <div style="font-size: 10px;color: rgba(183, 185, 204,0.8);height: 38px"></div>
          </div>
          <!--          <div class="logo-title">控费管理平台</div>-->
        </div>

        <!--        <div class="logo-title" v-show="!lessen">控费管理</div>-->

        <img class="logo-image" v-show="!lessen"
             :src="require('@/assets/images/login/jly2.png')"/>
      </div>
    </div>
    <!-- menu -->
    <div class="sidebar-content" style="position: relative">
      <template v-for="item in subMenus">
        <template v-if="subMenus&&subMenus.length > 0">
          <router-link v-if="item && !item.is_hide" :to="'/'+item.path"
                       :key="item.name">
            <div :class="['menu-item', item.active ? 'active' : '']" ref="menuItem" :id="'a' + item.meta.index">
              <div v-if="sidebarShow" style="margin-right: .8rem">
                <i v-if="item.meta&&item.meta.icon" :class="item.meta.icon"></i>
              </div>
              <el-tooltip v-if="!sidebarShow && item.meta&&item.meta.profttl" class="item" effect="dark"
                          :content="item.meta.profttl" placement="right">
                <i v-if="item.meta&&item.meta.icon" :class="item.meta.icon"
                   style="margin: 10px 0;height: 30px;width: 50px;font-size: 30px"></i>
              </el-tooltip>
              <div>
                <span v-if="sidebarShow && item.meta&&item.meta.profttl">{{ item.meta.profttl }}</span>
              </div>
            </div>
          </router-link>
        </template>
      </template>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name:'Sidebar',
  components: {},
  props: {
    mainMenuIndex: {
      type: String,
      default: () => ''
    }
  },
  data: () => ({
    iconFullStyle: {
      // width: '35%',
      width: '88%',
      height: '120%'
    }
  }),
  computed: {
    ...mapGetters([
      'sidebar'
    ]),
    sidebarShow () {
      let sidebar = this.$store.state.app.sidebar
      return sidebar.opened
    },
    routes () {
      let routers = this.$store.state.user.routers
      this.initRouters(routers)
      return routers
    },
    lessen () {
      return this.$store.state.app.sidebar.opened
    },
    subMenus () {
      if (this.mainMenuIndex) {
        let mainMenu = this.routes.find(a => {
          return a.meta && a.meta.index === Number(this.mainMenuIndex)
        })
        if (mainMenu) {
          return mainMenu.children
        }
      }
      return []
    }
  },
  methods: {
    initRouters (routers) {
      if (routers.length > 0) {
        routers.map(router => {
          router.active = false
          if (router.children && router.children.length > 0) {
            this.initRouters(router.children)
          }
        })
      }
    },
    toggleSideBar () {
      this.$store.dispatch('ToggleSideBar')
    }
  }
}

</script>
<style lang="scss" scoped>
@import '@/styles/variables.scss';

.logo {
  cursor: pointer;
  display: flex;
  /*position: fixed;*/
  height: 100%;
  width: 100%;
  /*padding:0 10px;*/
}

.logo-image {
  width: 65px;
  height: 50px;
}

.logo-title {
  display: flex;
  align-items: flex-start;
  justify-content: left;
  flex-direction: column;
  line-height: 50px;
  font-weight: bold;
  font-size: 16px;
  color: #ffffff;
}

.company-logo {
  height: 8%;
  //background-color: $frameworkBg;
}

.sidebar-container {
  //background-color: #224abe;
  background-color: #1b65b9;
  //background-image: linear-gradient(180deg, #689a90 10%,#83bbad 100%);
  background-size: cover;
}

.sidebar-content {
  height: 92%;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none; /* IE 10+ */
}

.sidebar-content::-webkit-scrollbar {
  display: none;
}

.menu-item {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, .7);
  height: 50px;
  width: 100%;
  font-size: 14px;
  padding-left: 20px;

  &:hover {
    color: white;
    background-color: #0a3574;
  }
}

.active {
  color: #fafafa;
  background-color: #225195;
}

.router-link-exact-active.router-link-active .menu-item {
  color: #fafafa;
  background-color: #225195;
}
</style>
