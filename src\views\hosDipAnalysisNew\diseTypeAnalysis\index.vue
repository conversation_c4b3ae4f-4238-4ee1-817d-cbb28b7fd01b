<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="somForm"
             headerTitle="查询条件"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-pagination
             :totalNum="total"
             :container="true"
              :showCoustemContentTitle="true"
             @query="radioChange(radioMode)"
             @reset="reset"
    >

      <template slot="extendFormItems">
        <el-form-item label="病种编码" prop="queryIcd" class="som-form-extend-form-item">
          <el-autocomplete
            popper-class="my-autocomplete"
            size="mini"
            v-model="queryForm.queryIcd"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入病种编码或者名称"
            @select="handleSelect"
            :popper-append-to-body="true"
            :clearable="true"
            :trigger-on-focus="false"
            ref='elautocomplete'>
            <template slot-scope="{ item }">
              <div class="code">{{ item.icdCodg }}</div>
              <span class="name">{{ item.icdName }}</span>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="参保类型">
          <drg-dict-select dicType="INSURANCE_TYPE" placeholder="请选择参保类型" v-model="queryForm.categories"
                           @change="radioChange(radioMode)"/>
        </el-form-item>
      </template>

      <!-- profttl -->
      <template slot="contentTitle">
        <drg-title-line :title="profttl" :wrapStyle="{ width: 'calc(80% - 10px)'}">
          <template slot="rightSide">
            <div style="display: flex">
              <div>
                <el-radio-group v-model="radioMode" @change="radioChange">
                  <el-radio-button :label="1">指标</el-radio-button>
                  <el-radio-button :label="2">预测</el-radio-button>
                  <el-radio-button :label="3">分析</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <template slot="titleRight">
            <el-select v-model="columnVal"
                       multiple
                       collapse-tags
                       :multiple-limit="3"
                       placeholder="请选择固定列">
              <el-option
                v-for="item in columnOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </template>
        </drg-title-line>
      </template>

      <!--   内容   -->
      <template slot="containerContent">
        <div class="content-wrapper">
          <!-- 左侧 -->
          <div class="content-wrapper-left" v-if="!analysis">

            <kpi-table :data="tableData"
                       ref="dataTable"
                       :loading="loading"
                       :fixed-columns="columnVal"
                       :id="kpiId"
                       :query-form="queryForm"
                       :columnOptions="columnOptions"
                       v-if="radioMode == 1"
                       @setRefObj="(obj) => this.tableObj = obj"
                       @showSuspension="showSuspension"/>

            <forecast-table :data="tableData"
                            ref="dataTable"
                            :loading="loading"
                            :query-form="queryForm"
                            :fixed-columns="columnVal"
                            :columnOptions="columnOptions"
                            :id="forecastId"
                            v-if="radioMode == 2"
                            @setRefObj="(obj) => this.tableObj = obj"
                            @showSuspension="showSuspension"/>

          </div>

          <!--悬浮框-->
          <suspension-frame :zbData="zbData" :ycData="ycData"/>

          <div class="content-wrapper-analysis" v-if="analysis">

            <analysis-echart ref="dataTable"
                             :data="analysisPageData"
                             :id="analysisId"
                             :dropdown="diseaseDropdownList"
                             :dropdown-check-val="dropdownVal"
                             :loading="loading"
                             v-if="showAnalysis"
                             :is-loss="isLoss"
                             :queryForm="queryForm"
                             @setRefObj="(obj) => this.tableObj = obj"
                             @switchLossOrProfit="switchLossOrProfit"
                             @dropdownChange="analysisDropdownChange"
                             @checkTypeChange="analysisTypeChange"/>

            <analysis-table :data="analysisTableData"
                            ref="dataTable"
                            :loading="loading"
                            :id="analysisId"
                            :is-loss="isLoss"
                            v-if="!showAnalysis"
                            :queryForm="queryForm"
                            @showAnalysisPage="switchChartTable"
                            @setRefObj="(obj) => this.tableObj = obj" />
          </div>
          <div v-if="radioMode == 3">
          <!-- 盈利或亏损 -->
          <div class="content-wrapper-analysis-loss-profit">
            <!-- profit -->
            <i class="som-icon-profit som-iconTool"
               title="盈利"
               v-if="!isLoss"
               @click="switchLossOrProfit(true)"
               style="height: 1.2rem;width: 1.2rem"></i>

            <!-- loss -->
            <i class="som-icon-loss som-iconTool"
               title="亏损"
               v-if="isLoss"
               @click="switchLossOrProfit(false)"
               style="height: 1.2rem;width: 1.2rem"></i>
          </div>

          <!-- 分析页面 - 切换图表按钮 -->
          <div class="content-wrapper-analysis-chart-table">
            <!--table-->
            <i class="som-icon-table som-iconTool"
               title="表格"
               v-if="showAnalysis"
               @click="switchChartTable(null)"
               style="height: 1.2rem;width: 1.2rem"></i>

            <!-- analysis -->
            <i class="som-icon-analysis som-iconTool"
               title="分析"
               v-if="!showAnalysis"
               @click="switchChartTable(null)"
               style="height: 1.2rem; width: 1.2rem"></i>
          </div>
          </div>

          <div class="content-wrapper-right" v-if="!analysis" >

            <div class="content-wrapper-right-top">
              <div style="display: flex;">
                <!-- 排序字段选择 -->
                <div style="width: 60%; padding-right: 2%">
                  <el-select v-model="selectVal" placeholder="请选择" @change="selectChange">
                    <el-option
                       v-for="item in options"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                    ></el-option>
                  </el-select>
                </div>

                <!-- top -->
                <div style="width: 40%">
                  <el-select v-model="topVal" placeholder="请选择" @change="generateChartData">
                    <el-option
                    v-for="item in topOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"></el-option>
                  </el-select>
                </div>

              </div>
              <div class="content-wrapper-right-icon" @click="sortClick">
                <i class="el-icon-sort"></i>
              </div>
            </div>

            <div style="height: 93%;width: 100%;position: absolute;top: 7%">
              <!-- 图 -->
              <drg-echarts :options="chartOption" ref="chart"/>
            </div>

          </div>

        </div>
      </template>
    </drg-form>
  </div>
</template>

<script>
import analysisTable from './comps/newDipDiseaseTypeAnalysissTable'
import analysisComp from './comps/newDipDiseaseTypeAnalysisComp'
import kpiTable from './comps/newDipDiseaseTypeKpiTable'
import { queryData, queryIcd } from '../../../api/dipBusiness/dipDiseaseTypeAnalysis'
import { queryLikeIcdsByPram } from '../../../api/common/drgCommon'
import SuspensionFrame from '../deptAnalysisNew/comps/newSuspensionFrame'
import analysisEchart from './comps/newDipDiseaseTypeAnalysisEchart'
import { queryPatientLoss, queryDropdown, queryMedError, queryAnalysisSummary, updateSwitchState } from '@/api/newBusiness/newBusinessCommon'

let kpiOptions = [
  { value: 'drgInGroupMedcasVal', label: '入组病案数' },
  { value: 'avgInHosCost', label: '平均住院费用' },
  { value: 'avgInHosDays', label: '平均住院天数' },
  { value: 'drugRatio', label: '药占比' },
  { value: 'timeIndex', label: '时间消耗指数' },
  { value: 'costIndex', label: '费用消耗指数' }
]
let forecastOptions = [
  { value: 'sumfee', label: '住院总费用' },
  { value: 'forecastAmountDiff', label: '预测金额差异' },
  { value: 'oeVal', label: 'O/E值' }
]

export default {
  name: 'diseTypeAnalysis',
  components: {
    kpiTable,
    analysisTable,
    'forecast-table': analysisComp,
    'suspension-frame': SuspensionFrame,
    'analysis-echart': analysisEchart
    // analysisTabel,
    // costItem
  },
  data: () => ({
    queryForm: {
      feeStas: '0',
      categories:'',
      queryIcd: ''
    },
    profttl: '病种指标分析',
    radioMode: 1,
    columnVal: [],
    columnOptions: [],
    tableObj: {},
    topVal: 10,
    showDip: true,
    topOptions: [
      { value: 10, label: 'TOP10' },
      { value: 20, label: 'TOP20' },
      { value: 30, label: 'TOP30' }
    ],
    options: kpiOptions,
    analysis: false,
    kpiId: 'kpiId',
    forecastId: 'forecastId',
    analysisId: 'analysisId',
    tableData: [],
    analysisPageData: [],
    analysisTableData: [],
    diseaseDropdownList: [],
    dropdownVal: '',
    loading: false,
    showAnalysis: false,
    exportTableName: '',
    selectVal: 'drgInGroupMedcasVal',
    total: 0,
    isLoss: true,
    chartOption: {},
    zbData: [],
    ycData: [],
    tempList: [],
    tempVal: this.radioMode
  }),

  methods: {
    selectChange () {
      this.generateChartData()
    },
    sortClick () {
      this.sort = !this.sort
      this.generateChartData()
    },
    showSuspension (item) {
      if (item) {
        if (item[0].type == 1) {
          this.zbData = item
        } else if (item[0].type == 2) {
          this.ycData = item
        }
      }
    },
    handleSelect (item) {
      this.queryForm.queryIcd = item.icdCodg
      this.getList()
      this.getCount()
      this.queryPageIndex()
    },
    // 选择盈利还是亏损
    switchLossOrProfit (isLoss) {
      this.isLoss = isLoss
      this.queryAnalysisData()
    },
    // 分析页面下拉选改变
    analysisDropdownChange (val) {
      this.dropdownVal = val
      this.queryAnalysisData()
    },
    // 切图表
    switchChartTable (dipCodg) {
      this.showAnalysis = !this.showAnalysis
      this.queryAnalysisData(dipCodg)
    },

    // 分析页面模块点击
    analysisTypeChange (item) {
      let type = item.type
      this.queryForm.pageNum = item.pageNum
      this.tempAnalysisPageData = item.data.map(i => {
        return {
          total: i.value,
          data: i.data,
          pageNum: i.pageNum
        }
      })
      this.analysisPageType = type
      if (this.analysisPageData.length > 0) {
        if (type == 'med') {
          this.total = this.analysisPageData[0].total
        }
      }
    },
    radioChange (val) {
      if (this.tempVal != val) {
        this.selectVal = ''
        this.tempVal = val
      }
      if (val == 1) {
        this.profttl = '病种指标分析'
        this.queryPageIndex()
        this.options = kpiOptions
        if (this.selectVal == '') {
          this.selectVal = 'drgInGroupMedcasVal'
        }
        this.exportTableName = '病种指标分析'
        this.analysis = false
      }
      if (val == 2) {
        this.profttl = '病种预测情况'
        this.queryIcdData()
        this.tableData = []
        this.analysis = false
        this.options = forecastOptions
        if (this.selectVal == '') {
          this.selectVal = 'sumfee'
        }
        this.exportTableName = '病种预测情况'
        this.showAnalysis = false
      }
      if (val == 3) {
        this.profttl = '病种分析'
        this.queryAnalysisData()
        this.tableId = this.analysisId
        this.exportTableName = '病种分析'
        this.analysis = true
        this.showAnalysis = true
      }
      this.selectDisease()
    },
    queryPageIndex () {
      this.loading = true
      queryData(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
        this.generateFixedColumns()
        this.generateChartData()
      })
      this.clearRouteQuery()
    },
    queryIcdData () {
      this.loading = true
      queryIcd(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
        this.generateFixedColumns()
        this.generateChartData()
      })
      this.clearRouteQuery()
    },
    reset () {
      this.radioChange(this.selectVal)
    },
    generateFixedColumns () {
      this.columnOptions = []
      let children = this.$refs.dataTable.$children[0].$children
      if (children) {
        children.forEach(item => {
          if (item.$options.propsData.label &&
            item.$options.propsData.label != '序号' &&
            item.$options.propsData.label != '病种名称' &&
            item.$options.propsData.label != '病种大类') {
            this.columnOptions.push({
              value: item.$options.propsData.prop,
              label: item.$options.propsData.label
            })
          }
        })
      }
    },
    initChart () {
      this.chartOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            interval: 0,
            rotate: 40,
            formatter: params => {
              return this.formatCost(params, 0)
            }
          },
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: this.yAxisData,
          axisLabel: {
            formatter: function (value, index) {
              if (value.length < 4) {
                return value
              }
              return value.substring(0, 4) + '...'
            }
          }
        },
        series: [
          {
            name: '数值',
            type: 'bar',
            label: {
              formatter: params => {
                return this.formatCost(params.value)
              }
            },
            data: this.seriesData.map((item, index) => {
              return {
                value: item,
                label: {
                  show: false
                }
              }
            })
          }
        ]
      }
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString,
        icd_type: 'ICD-10' // 只查询疾病信息
      }
      queryLikeIcdsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    formatCost (val, fixed = 2) {
      let prefix = ''
      let resVal = val
      if (String(val).includes('-')) {
        prefix = '-'
        val = Math.abs(val)
        resVal = val
      }
      if (Math.abs((parseFloat(val) / 10000)) > 1) {
        resVal = (parseFloat(val) / 10000).toFixed(fixed) + '/万'
      } else {
        resVal = val
      }
      return prefix + resVal
    },
    // 病种下拉选择查询
    selectDisease () {
      queryDropdown(this.getParams()).then(res => {
        this.diseaseDropdownList = res.data
      })
    },
    // 获取参数
    getParams () {
      let params = {}
      this.queryForm.feeStas = this.$store.getters.feeStas
      params.auth = true
      // 下拉列表类型 3:病组
      params.dropdownType = '3'
      // 亏损还是盈利
      params.isLoss = this.isLoss
      params.dipCodg = this.queryForm.dipCodg
      Object.assign(params, this.queryForm)
      params.icdCodg = this.queryForm.queryIcd
      return params
    },
    clearRouteQuery () {
      if (this.$route.query) {
        this.$router.push({ query: {
        } }).catch(() => {})
      }
    },



    // 查询分析数据
    async queryAnalysisData (dipCodg) {
      this.loading = true
      let params = this.getParams()



      if (!this.showAnalysis) {
        this.analysisTableData = []
        await queryAnalysisSummary(params).then(res => {
          this.analysisTableData = res.data
        })
      } else {
        this.analysisPageData = []
        if (this.diseaseDropdownList.length == 0) {
          await queryDropdown(params).then(res => {
            this.diseaseDropdownList = res.data
          })
        }

        if (dipCodg) {
          // 跳转到分析页面
          this.dropdownVal = dipCodg
        } else {
          // 第一次进入页面没有选择下拉选
          if (this.diseaseDropdownList.length > 0 && !this.dropdownVal) {
            this.dropdownVal = this.diseaseDropdownList[0].value
          } else if (this.diseaseDropdownList.length > 0 && this.dropdownVal) {
            let tempList = []
            for (let i = 0; i < this.diseaseDropdownList.length; i++) {
              tempList.push(this.diseaseDropdownList[i].value)
            }
            if (!tempList.includes(this.dropdownVal)) {
              this.dropdownVal = this.diseaseDropdownList[0].value
            }
          }
        }

        // 子页面下拉选改变传入科室编码
        if (this.dropdownVal) {
          params.dipCodg = this.dropdownVal
          this.tempAnalysisPageData = []
        }

        await this.getAnalysisPageData('med', 0, queryPatientLoss, params)
        // 错误病例
        await this.getAnalysisPageData('errorMed', 1, queryMedError, params, true)

        if (this.analysisPageData.length > 0) {
          this.tempAnalysisPageData = [...this.analysisPageData]
        }
      }

      this.loading = false
    },

    // 获取分析数据
    async getAnalysisPageData (type, index, queryMethod, params, noPaging = false) {
      if (this.analysisPageType == type || this.tempDataIsNull(index)) {
        await queryMethod(params).then(res => {
          if ((type == 'med' && res.data.list && res.data.list.length > 0) || (type == 'errorMed' && res.data && res.data.length > 0)) {
            this.addAnalysisData(res, index, false, noPaging)
            if (this.analysisPageType == type && !['errorMed'].includes(type)) this.total = res.data.total
          } else {
            this.analysisPageData.push(
              {
                data: [],
                total: 0,
                pageNum: 1
              }
            )
          }
        })
      } else {
        this.addAnalysisData(null, index, true)
      }
    },

    // 添加分析数据
    addAnalysisData (res, index, useTempData, noPaging = false) {
      if (useTempData) {
        this.analysisPageData.push(this.tempAnalysisPageData[index])
      } else {
        if (noPaging) {
          let total = 0
          if (index == 1) {
            total = res.data[0].errorSummaryNum + '-' + res.data[0].compeleteErrorNum + '-' + res.data[0].logicErrorNum
          }
          // 无分页情况
          this.analysisPageData.push({
            data: res.data,
            total: total,
            pageNum: 1
          })
        } else {
          this.analysisPageData.push({
            data: res.data.list,
            total: res.data.total,
            pageNum: this.queryForm.pageNum
          })
        }
      }
    },
    tempDataIsNull (index) {
      if (this.tempAnalysisPageData.length > 0) {
        if (this.tempAnalysisPageData[index].data != undefined) {
          return this.tempAnalysisPageData[index].data.length == 0
        } else {
          return true
        }
      }
      return this.tempAnalysisPageData.length == 0
    },
    // 生成图数据
    generateChartData () {
      let params = this.getParams()
      Object.assign(params, {
        pageNum: 1,
        pageSize: 10 ** 4
      })
      if (this.radioMode == 1) {
        queryData(params).then(res => {
          this.tempList = res.data.list
          this.yAxisData = []
          this.seriesData = []
          let sortData = []
          for (let i = 0; i < this.tempList.length; i++) {
            let item = this.tempList[i]
            sortData.push({
              y: (item.icdName && item.icdName != undefined) ? item.icdName : '-',
              value: item[this.selectVal]
            })
          }

          if (this.sort) {
            sortData = sortData.sort((a, b) => a.value - b.value)
          } else {
            sortData = sortData.sort((a, b) => b.value - a.value)
          }

          for (let i = 0; i < sortData.length; i++) {
            if (i == this.topVal) {
              break
            }
            this.yAxisData.push(sortData[i].y)
            this.seriesData.push(sortData[i].value)
          }
          this.yAxisData.reverse()
          this.seriesData.reverse()
          this.initChart()
        })
      }
      if (this.radioMode == 2) {
        queryIcd(params).then(res => {
          this.tempList = res.data.list
          this.yAxisData = []
          this.seriesData = []
          let sortData = []
          for (let i = 0; i < this.tempList.length; i++) {
            let item = this.tempList[i]
            sortData.push({
              y: (item.icdName && item.icdName != undefined) ? item.icdName : '-',
              value: item[this.selectVal]
            })
          }

          if (this.sort) {
            sortData = sortData.sort((a, b) => a.value - b.value)
          } else {
            sortData = sortData.sort((a, b) => b.value - a.value)
          }

          for (let i = 0; i < sortData.length; i++) {
            if (i == this.topVal) {
              break
            }
            this.yAxisData.push(sortData[i].y)
            this.seriesData.push(sortData[i].value)
          }
          this.yAxisData.reverse()
          this.seriesData.reverse()
          this.initChart()
        })
      }
    }
    // generateChartData(){
    //   this.yAxisData = []
    //   this.seriesData = []
    //   let sortData = []
    //   for (let i = 0; i < this.tableData.length; i++) {
    //     let item = this.tableData[i]
    //     sortData.push({
    //       y: item.icdName,
    //       value: item[this.selectVal]
    //     })
    //   }
    //
    //   if(this.sort){
    //     sortData = sortData.sort((a, b) => a.value - b.value)
    //   } else {
    //     sortData = sortData.sort((a, b) => b.value - a.value)
    //   }
    //
    //   for (let i = 0; i < sortData.length; i++) {
    //     if(i == this.topVal){
    //       break
    //     }
    //     this.yAxisData.push(sortData[i].y)
    //     this.seriesData.push(sortData[i].value)
    //   }
    //   this.yAxisData.reverse()
    //   this.seriesData.reverse()
    //   this.initChart()
    // },
  }
}
</script>

<style scoped lang="scss">
.content-wrapper{
  height: 100%;
  width: 100%;
  display: flex;

  &-left{
    width: 80%;
    height: 96%;
    padding-right: 10px;
    box-sizing: border-box;
    position: relative;

    &-fixed-column{
      position: absolute;
      left: 10%;
      top: -4.5%;
    }

    &-analysis{
      width: 100%;
      height: 100%;
    }
  }

  &-right{
    width: 20%;
    height: 100%;
    position: relative;

    &-select{
      position: absolute;
      right: 0;
      top: -4.5%;
    }

    &-top{
      height: 10%;
      width: 100%;
      position: absolute;
      top: 0%
    }

    &-icon{
      z-index: 2;
      font-size: 18px;
      width: 20px;
      height: 40px;
      cursor: pointer;
      position: absolute;
      right: 0;
    }
  }

  &-analysis{
    width: 100%;
    height: 100%;
    position: relative;

    &-chart-table{
      position: absolute;
      top: -5px;
      right: 5px;
    }

    &-loss-profit{
      position: absolute;
      top: -2px;
      right: 35px;
    }
  }
  &-loss-profit{
    position: absolute;
    top: -5px;
    right: 5px;
  }
}
.analysis-wrapper{
  height: 100%;
  width: 100%;
  position: relative;

  &-left{
    width: 100%;
    height: 100%;
  }

  &-analysis{
    width: 100%;
    height: 80%;
    position: relative;
    display: flex;
  }

  &-right{
    width: 20%;
    height: 100%;
    background-color: rgba(131,175,155,.3);
    padding: 1% 1% 0 1%;
    border-radius: 1%;
  }

  &-no-table{
    width: 100%;
    height: 80%;
  }
}
/deep/ .pagination-container{
  right: 21%;
}
/deep/ .content-wrapper-right-top>.el-select{
  width: 84px;
}

.content-wrapper-left{
  height: 100%;
}

</style>
