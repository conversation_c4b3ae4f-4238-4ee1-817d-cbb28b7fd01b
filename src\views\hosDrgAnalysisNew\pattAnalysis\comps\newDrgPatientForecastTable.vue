<template>
  <el-table ref="elTable"
            :id="id"
            height="100%"
            stripe
            :header-cell-style="{'text-align':'center'}"
            :data="data"
            :summary-method="getSummaries"
            show-summary
            v-loading="loading"
            border>
    <el-table-column label="序号" type="index" width="50" fixed align="center"></el-table-column>
    <el-table-column label="姓名" prop="name" align="center" width="130" :fixed="include('name')"></el-table-column>
    <el-table-column label="病案号" prop="bah" width="130" :fixed="include('bah')" align="center"></el-table-column>
    <el-table-column label="出院科室" prop="deptName" width="130" :fixed="include('deptName')"
                     align="left"></el-table-column>
    <el-table-column label="DRG编码" prop="drgCodg" align="center" width="130" :fixed="include('dipCode')"
                     :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="DRG名称" prop="drgName" align="left" width="130" :fixed="include('dipName')"
                     :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="参保类型" prop="insuType" align="center" width="130" :fixed="include('insType')">
      <template slot-scope="scope">
        {{ $somms.getDictValueByType(scope.row.insuType, "INSURANCE_TYPE") }}
      </template>
    </el-table-column>
    <el-table-column label="住院总费用" prop="inHosTotalCost" width="130" :fixed="include('inHosTotalCost')"
                     align="right"></el-table-column>
    <el-table-column label="预测费用" prop="forecastAmount" width="130" :fixed="include('forecastAmount')"
                     align="right"></el-table-column>
    <el-table-column label="预测金额差异" prop="forecastAmountDiff" width="130" :fixed="include('forecastAmountDiff')"
                     align="right"></el-table-column>
    <el-table-column label="病例类型" prop="ratioRange" align="center" width="130" :fixed="include('ratioRange')">
      <template slot-scope="scope">
        {{ $somms.getDictValueByType(scope.row.ratioRange, "CASE_TYPE") }}
      </template>
    </el-table-column>
    <el-table-column label="O/E值" prop="oeVal" width="130" :fixed="include('oeVal')" align="center"></el-table-column>
    <el-table-column label="总分值" prop="totlSco" width="130" :fixed="include('totalScore')"
                     align="center"></el-table-column>
    <el-table-column label="单价" prop="price" width="130" :fixed="include('price')" align="center"></el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'newDrgPatientForecastTable',
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    },
    // 表格id
    id: {
      type: String
    }
  },
  updated () {
    this.$nextTick(() => {
      this.$refs.elTable.doLayout()
    })
  },
  methods: {
    include (column) {
      return this.fixedColumns.includes(column)
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length
      }
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计'
          return
        }
        const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value))
        if (index === 7 || index === 8 || index === 9) {
          sums[index] = calculations.sum(values).toFixed(2)
        } else if (index === 11 || index === 12 || index === 13) {
          sums[index] = calculations.average(values).toFixed(2)
        } else {
          sums[index] = ' '
        }
      })
      return sums
    }
  }
}
</script>
