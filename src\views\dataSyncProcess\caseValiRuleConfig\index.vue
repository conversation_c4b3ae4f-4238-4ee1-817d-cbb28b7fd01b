<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
              :container="true"
              headerTitle="查询条件"
              @query="handleTabChange">
      <template slot="contentTitle">
        <el-tabs v-model="activeTab" @tab-click="handleTabChange">
          <el-tab-pane label="病案首页规则" name="first"></el-tab-pane>
          <el-tab-pane label="医保清单规则" name="second"></el-tab-pane>
        </el-tabs>
      </template>
      <!-- 扩展formitem -->
      <template slot="extendFormItems">

        <el-form-item label="字段名称:">
          <div v-if="activeTab === 'first'">
            <el-input class="som-form-item" v-model="queryForm.chkName" placeholder="字段名称" clearable
                      @input="findMedical"></el-input>
          </div>
          <div v-if="activeTab === 'second'">
            <el-input class="som-form-item" v-model="queryForm.checkFiled" placeholder="字段名称" clearable
                      @input="selectlist"></el-input>
          </div>
        </el-form-item>

        <!-- <el-form-item label="字段名称：">
          <el-input class="som-form-item" v-model="queryForm.chkName" placeholder="字段名称" clearable  @input="findMedical"></el-input>
        </el-form-item>
        <el-form-item label="启用状态：">
          <el-select v-model="queryForm.enabFlag" placeholder="启用状态" clearable @change="findMedical">
            <el-option v-for="item in startFlagResult"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
      </template>

      <template slot="buttons">
        <div v-if="activeTab === 'first'">
          <el-button @click="add" type="primary" class="som-button-margin-right" size="mini">新增</el-button>
          <el-button @click="reduction" type="warning" class="som-button-margin-right" size="mini">还原</el-button>
        </div>
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <div v-if="activeTab === 'first'">
          <el-table ref="elTable"
                    :data="list"
                    size="mini"
                    :header-cell-style="{'text-align':'center'}"
                    row-key="seq"
                    :tree-props="{ children : 'children', hasChildren : 'hasChildren' }"
                    stripe
                    width="100%"
                    height=575px
                    v-loading="listLoading"
                    border>

            <el-table-column fixed
                             label="序号"
                             type="index"
                             align="right"
                             width="50">
            </el-table-column>

            <el-table-column label="类型" prop="ruleType" width="160"/>
            <el-table-column label="字段代码" prop="fldCode" :show-overflow-tooltip="true"/>
            <el-table-column label="字段名称" prop="chkName" :show-overflow-tooltip="true"/>
            <el-table-column label="验证规则" prop="verfRule" :show-overflow-tooltip="true"/>
            <el-table-column label="错误描述" prop="errDscr" :show-overflow-tooltip="true"/>
            <drg-table-column prop="chkTypeType" dicType="VALIDATE_TYPE" label="校验类型"/>
            <el-table-column label="码表代码" prop="dictCode" :show-overflow-tooltip="true" align="center">
              <template slot-scope="scope" v-if="scope.row.dictCode">
                <div>{{ scope.row.dictCode[0] }}</div>
                <el-switch v-model="scope.row.dictCode[1]"
                           @change="update(scope.row)"
                           active-color="#13ce66"
                           inactive-color="#ff4949"
                           active-value="Y"
                           inactive-value="N">
                </el-switch>
              </template>
            </el-table-column>
            <el-table-column label="字段长度" prop="length" :show-overflow-tooltip="true" align="center">
              <template slot-scope="scope" v-if="scope.row.length">
                <div>{{ scope.row.length[0] }}</div>
                <el-switch v-model="scope.row.length[1]"
                           @change="update(scope.row)"
                           active-color="#13ce66"
                           inactive-color="#ff4949"
                           active-value="Y"
                           inactive-value="N"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="校验正则" prop="chkRegl" :show-overflow-tooltip="true" align="center">
              <template slot-scope="scope" v-if="scope.row.chkRegl">
                <div>{{ scope.row.chkRegl[0] }}</div>
                <el-switch v-model="scope.row.chkRegl[1]"
                           @change="update(scope.row)"
                           active-color="#13ce66"
                           inactive-color="#ff4949"
                           active-value="Y"
                           inactive-value="N"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="转换值" prop="converter" :show-overflow-tooltip="true" align="center">
              <template slot-scope="scope" v-if="scope.row.converter">
                <div>{{ scope.row.converter[0] }}</div>
                <el-switch v-model="scope.row.converter[1]"
                           @change="update(scope.row)"
                           active-color="#13ce66"
                           inactive-color="#ff4949"
                           active-value="Y"
                           inactive-value="N"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="-转NULL" prop="converter" :show-overflow-tooltip="true" align="center">
              <template slot-scope="scope" v-if="scope.row.transformation">
                <div>{{ scope.row.transformation[0] }}</div>
                <el-switch v-model="scope.row.transformation[1]"
                           @change="update(scope.row)"
                           active-color="#13ce66"
                           inactive-color="#ff4949"
                           active-value="Y"
                           inactive-value="N"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="启用状态" align="center" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enabFlag"
                           @change="updateMedical(scope.row)"
                           active-color="#13ce66"
                           inactive-color="#ff4949"
                           active-value="1"
                           inactive-value="0">
                </el-switch>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="160" fixed="right">
              <template slot-scope="scope" v-if="!scope.row.children">
                <el-button type="primary" icon="el-icon-edit" circle @click="xxx(scope.row)"></el-button>
                <el-button type="danger" icon="el-icon-delete" circle @click="deleteId(scope.row)"></el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-dialog :title="profttl" :visible.sync="dialogFormVisible" destroy-on-close width="30%"
                     close-on-press-escape v-som-dialog-drag>
            <div style="height: 1000px;overflow: auto">
              <el-form :model="dataForm" label-width="80px" :rules="rules" style="height: 100%;width: 100%"
                       ref="dataForm">
                <el-form-item label="类型" prop="type" required>
                  <el-select v-model="dataForm.type" placeholder="请选择" class="som-form-item">
                    <el-option
                        v-for="item in optionTypeResult"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="字段代码" class="som-form-item">
                  <el-input v-model="dataForm.fldCode" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="字段名称" prop="chkName" class="som-form-item">
                  <el-input v-model="dataForm.chkName" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="字段长度" prop="length" class="som-form-item">
                  <el-input v-model="dataForm.length" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="校验规则" class="som-form-item">
                  <el-input v-model="dataForm.verfRule" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="错误描述" class="som-form-item">
                  <el-input v-model="dataForm.errDscr" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="校验类型" prop="chkTypeType" required class="som-form-item">
                  <el-select v-model="dataForm.chkTypeType" clearable placeholder="请选择" style="width: 99%">
                    <el-option
                        v-for="item in optionResult"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="码表代码" class="som-form-item">
                  <el-input v-model="dataForm.dictCode" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="校验正则" class="som-form-item">
                  <el-input v-model="dataForm.chkRegl" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="转化值" class="som-form-item">
                  <el-input v-model="dataForm.converter" autocomplete="off"/>
                </el-form-item>
                <el-form-item label="-转NULL" class="som-form-item">
                  <el-input v-model="dataForm.transformation" autocomplete="off"/>
                </el-form-item>
              </el-form>
            </div>

            <div slot="footer" class="dialog-footer">
              <el-button @click="dialogFormVisible = false">取消</el-button>
              <el-button @click="resetForm('dataForm')">重置</el-button>
              <el-button type="primary" @click="insertOrUpdateAllMedical">确认</el-button>
            </div>
          </el-dialog>

          <el-dialog title="提示 " :visible.sync="dialogVisible" width="25%">
            <span>是否删除</span>
            <span slot="footer" class="dialog-footer">
              <template>
              <el-button @click="dialogVisible=false">取消</el-button>
              <el-button type="primary" @click="deleteMedical">确认</el-button>
              </template>
            </span>
          </el-dialog>

          <el-dialog
              title="提示"
              :visible.sync="dialogReduction"
              width="30%"
          >
            <span>是否还原</span>
            <span slot="footer" class="dialog-footer">
    <el-button @click="dialogReduction = false">取 消</el-button>
    <el-button type="primary" @click="reduce">确 定</el-button>
  </span>
          </el-dialog>
        </div>
        <div v-if="activeTab === 'second'">
          <el-table ref="elTable"
                    :data="list2"
                    size="mini"
                    :header-cell-style="{'text-align':'center'}"
                    row-key="seq"
                    :tree-props="{ children : 'children', hasChildren : 'hasChildren' }"
                    stripe
                    width="100%"
                    height=575px
                    v-loading="listLoading"
                    border>

            <el-table-column fixed
                             label="序号"
                             type="index"
                             align="right"
                             width="50">
            </el-table-column>
            <el-table-column align="center" label="规则分类" prop="checkType" :show-overflow-tooltip="true"/>
            <el-table-column label="规则字段" prop="checkFiled" :show-overflow-tooltip="true"/>
            <el-table-column label="规则名称" prop="checkRule" :show-overflow-tooltip="true"/>
            <el-table-column label="规则类型" align="center" prop="checkTime" :show-overflow-tooltip="true"/>
            <el-table-column label="启用状态" align="center" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable"
                           @change="updateInspection(scope.row)"
                           active-color="#13ce66"
                           inactive-color="#ff4949"
                           active-value="1"
                           inactive-value="0">
                </el-switch>
              </template>
            </el-table-column>
            <el-table-column label="脚本代码" prop="scriptId" :show-overflow-tooltip="true"/>

          </el-table>

          <el-dialog :title="profttl" :visible.sync="dialogFormVisible" destroy-on-close width="30%"
                     close-on-press-escape v-som-dialog-drag>
            <div style="height: 500px;overflow: auto">
              <el-form :model="dataForm2" label-width="80px" :rules="rules" style="height: 100%;width: 100%"
                       ref="dataForm2">
                <el-form-item label="检测字段" class="som-form-item ">
                  <el-input v-model="dataForm2.checkFiled" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="检查规则" prop="chkName" class="som-form-item">
                  <el-input v-model="dataForm2.checkRule" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="检测时间" prop="length" class="som-form-item">
                  <el-input :value="dataForm2.checkTime" autocomplete="off" readonly></el-input>
                </el-form-item>
                <el-form-item label="校验规则" class="som-form-item">
                  <el-input v-model="dataForm2.chkName" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="检测分类" class="som-form-item">
                  <el-input v-model="dataForm2.checkType" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="描述代码" class="som-form-item">
                  <el-input v-model="dataForm2.scriptId" autocomplete="off"></el-input>
                </el-form-item>
              </el-form>
            </div>
          </el-dialog>
        </div>
      </template>
    </drg-form>

  </div>
</template>
<script>
import {
  deacidizing,
  deleteMedical,
  insertMedical,
  QueryMedical,
  QueryQualityList,
  reduct,
  updateAllMedical,
  updateMedical,
  updateInspection,
  updateOther,
  updateStart
} from '@/api/dataHandle/Listexclusionrule'

export default {
  name: 'caseValiRuleConfig',
  inject: ['reload'],
  data () {
    return {
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      options: [{
        value: '1',
        label: '完整型'
      }, {
        value: '2',
        label: '深度校验'
      }],
      value: '',
      queryForm: {},
      activeTab: 'first',
      dialogFormVisible: false,
      dialogReduction: false,
      dialogVisible: false,
      listLoading: true,
      listLoading2: true,
      list: [],
      list2: [],

      dataForm: {
        type: '',
        errDscr: '',
        fldCode: '',
        chkName: '',
        verfRule: '',
        enabFlag: '',
        chkTypeType: '',
        dictCode: '',
        chkRegl: '',
        converter: '',
        transformation: '',
        length: 0
      },
      dataForm2: {
        type: '',
        checkFiled: '',
        checkRule: '',
        checkTime: '',
        chkName: '',
        checkType: '',
        enable: '',
        scriptId: ''
      },

      optionTypeResult: this.$store.getters.getSettleListDictByKey('SETTLE_LIST_VALIDATE_TYPE'),
      formLabelWidth: '120px',
      rules: {
        type: [{ required: true, message: '请选择类型', trigger: 'change' }],
        chkTypeType: [{ required: true, message: '请选择校验类型', trigger: 'change' }]
      },
      id: '',
      profttl: '',
      editType: '',
      startFlagResult: [
        {
          value: '1',
          label: '启用'
        },
        {
          value: '0',
          label: '停用'
        }
      ],
      optionResult: []
    }
  },

  mounted () {
    this.optionResult = this.$somms.getDictValue('VALIDATE_TYPE')
    this.findMedical()
    // handleTabChange('first')
  },
  refresh () {
    this.reload()
  },
  methods: {
    handleTabChange (tab) {
      if (tab.name === 'first') {
        this.findMedical()
        this.$data.activeTab = 'first'
      } else if (tab.name === 'second') {
        this.selectlist()
        this.$data.activeTab = 'second'
      }
    },
    findMedical () {
      this.listLoading = true
      QueryMedical(this.getParams()).then(res => {
        for (let i = 0; i < res.data.length; i++) {
          if (res.data[i].children) {
            for (let j = 0; j < res.data[i].children.length; j++) {
              // res.data[i].children[j].type = ""
              if (res.data[i].children[j].dictCode) {
                let num1 = res.data[i].children[j].dictCode.substring(0, res.data[i].children[j].dictCode.length - 2)
                let num2 = res.data[i].children[j].dictCode.substring(res.data[i].children[j].dictCode.length - 1, res.data[i].children[j].dictCode.length)
                res.data[i].children[j].dictCode = [num1, num2]
              }
              if (res.data[i].children[j].length) {
                let num1 = res.data[i].children[j].length.substring(0, res.data[i].children[j].length.length - 2)
                let num2 = res.data[i].children[j].length.substring(res.data[i].children[j].length.length - 1, res.data[i].children[j].length.length)
                res.data[i].children[j].length = [num1, num2]
              }
              if (res.data[i].children[j].chkRegl) {
                let num1 = res.data[i].children[j].chkRegl.substring(0, res.data[i].children[j].chkRegl.length - 2)
                let num2 = res.data[i].children[j].chkRegl.substring(res.data[i].children[j].chkRegl.length - 1, res.data[i].children[j].chkRegl.length)
                res.data[i].children[j].chkRegl = [num1, num2]
              }
              if (res.data[i].children[j].converter) {
                let num1 = res.data[i].children[j].converter.substring(0, res.data[i].children[j].converter.length - 2)
                let num2 = res.data[i].children[j].converter.substring(res.data[i].children[j].converter.length - 1, res.data[i].children[j].converter.length)
                res.data[i].children[j].converter = [num1, num2]
              }
              if (res.data[i].children[j].transformation) {
                let num1 = res.data[i].children[j].transformation.substring(0, res.data[i].children[j].transformation.length - 2)
                let num2 = res.data[i].children[j].transformation.substring(res.data[i].children[j].transformation.length - 1, res.data[i].children[j].transformation.length)
                res.data[i].children[j].transformation = [num1, num2]
              }
            }
          }
        }
        this.adjustmentStatus(res.data)
        this.listLoading = false
        this.list = res.data
      })
    },
    selectlist () {
      this.listLoading2 = true
      QueryQualityList(this.getParams()).then(res => {
        this.adjustmentStatus(res.data)
        this.listLoading2 = false

        // 转换为列表（数组）
        const list2 = Object.values(res.data).flat()
        this.list2 = list2
        for (let i = 0; i < list2.length; i++) {
          list2[i].checkTime = list2[i].checkTime == '0' ? '事前' : '事后'
        }
        this.list2 = list2
      })
    },
    adjustmentStatus (item) {
      if (item) {
        for (let i = 0; i < item.length; i++) {
          let count = 0
          if (item[i].children) {
            for (let j = 0; j < item[i].children.length; j++) {
              if (item[i].children[j].enabFlag == '1') {
                item[i].enabFlag = '1'
                break
              } else {
                count++
              }
            }
            if (count == item[i].children.length) {
              item[i].enabFlag = '0'
            }
          }
        }
      }
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    add () {
      this.profttl = '新增校验规则'
      this.dialogFormVisible = true
      this.dataForm = {}
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
      this.editType = 1
    },
    reduction () {
      this.profttl = '还原数据'
      this.dialogReduction = true
    },
    xxx (item) {
      this.profttl = '修改校验规则'
      let params = {}
      Object.assign(params, item)
      if (params.dictCode) {
        params.dictCode = params.dictCode[0]
      }
      if (params.length) {
        params.length = params.length[0]
      }
      if (params.chkRegl) {
        params.chkRegl = params.chkRegl[0]
      }
      this.dataForm = params
      this.dialogFormVisible = true
      this.editType = 2
    },
    insertOrUpdateAllMedical () {
      if (this.editType == 1) {
        this.$refs['dataForm'].validate((valida) => {
          if (valida) {
            insertMedical(this.dataForm).then(res => {
              if (res.code == 200) {
                this.$message.success('新增成功')
                this.dialogFormVisible = false
              }
              this.findMedical()
            })
          }
        })
      } else if (this.editType == 2) {
        this.$refs['dataForm'].validate((valida) => {
          if (valida) {
            updateAllMedical(this.dataForm).then(res => {
              if (res.code == 200) {
                this.$message.success('修改成功')
                this.dialogFormVisible = false
              }
              this.findMedical()
            })
          }
        })
      }
    },
    insertMedical () {
      let params = {}
      Object.assign(params, this.dataForm)
      insertMedical(params).then(res => {
        if (res.code == 200) {
          this.$message.success('新增成功')
          this.dialogFormVisible = false
        }
        this.findMedical()
      })
    },
    updateMedical (item) {
      let params = {}
      Object.assign(params, item)
      if (item.children != null) {
        params.type = item.dataVal
        params.children = []
        if (params.enabFlag == 1) {
          this.$message.success('启用成功')
        } else if (params.enabFlag == 0) {
          this.$message.success('停用成功')
        }
        updateStart(params).then(res => {
          if (res.code == 200) {
            this.findMedical()
          }
        })
      } else {
        if (params.dictCode && params.dictCode) {
          params.dictCode = params.dictCode[1]
        }
        if (params.length) {
          params.length = params.length[1]
        }
        if (params.chkRegl) {
          params.chkRegl = params.chkRegl[1]
        }
        if (params.converter) {
          params.converter = params.converter[1]
        }
        if (params.transformation) {
          params.transformation = params.transformation[1]
        }
        if (params.enabFlag == 1) {
          this.$message.success('启用成功')
        } else if (params.enabFlag == 0) {
          this.$message.success('停用成功')
        }
        updateMedical(params).then(res => {
          if (res.code == 200) {
            this.findMedical()
          }
        })
      }
    },
    updateInspection (item) {
      let params = {}
      Object.assign(params, item)
      // eslint-disable-next-line no-self-assign
      params.checkType = params.checkType
      params.checkFiled = params.checkFile
      // eslint-disable-next-line no-self-assign
      params.checkRule = params.checkRule
      params.checkTime = params.checkTime == '事前' ? 0 : 1
      // eslint-disable-next-line no-self-assign
      params.scriptId = params.scriptId

      if (params.enable == 0) {
        this.$message.success('停用成功')
      } else if (params.enable == 1) {
        this.$message.success('启用成功')
      }
      updateInspection(params).then(res => {
        if (res.code == 200) {
          // this.selectlist()
        }
      })
    },
    update (item) {
      let params = {}
      Object.assign(params, item)
      if (params.dictCode) {
        params.dictCode = params.dictCode[1]
      }
      if (params.length) {
        params.length = params.length[1]
      }
      if (params.chkRegl) {
        params.chkRegl = params.chkRegl[1]
      }
      if (params.converter) {
        params.converter = params.converter[1]
      }
      if (params.transformation) {
        params.transformation = params.transformation[1]
      }
      updateOther(params).then(res => {
        if (res.code == 200) {
          this.$message.success('设置成功')
          this.findMedical()
        }
      })
    },
    updateAllMedical (row) {
      let params = {}
      Object.assign(params, row)
      updateAllMedical(params).then(res => {
        if (res.code == 200) {
          this.$message.success('修改成功')
          this.dialogFormVisible = false
        }
      })
    },
    deleteId (row) {
      this.dialogVisible = true
      this.id = row.id
    },
    reduce () {
      deacidizing().then(res => {
        if (res.code == 200) {
          this.$message.success('还原成功')
          this.dialogReduction = false
        }
        this.findMedical()
      })
    },
    deleteMedical () {
      let params = {}
      params.id = this.id
      deleteMedical(params).then(res => {
        if (res.code == 200) {
          this.$message.success('删除成功')
          this.dialogVisible = false
        }
        this.findMedical()
      })
    },
    getValue (item) {
      let data = item.substring(item.length - 1, item.length)
      return data
    },
    resetForm (formName) {
      this.$refs[formName].resetFields()
    }

  }
}
</script>

<style scoped>
.custom-init {
  text-align: center;
}
</style>
