<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             headerTitle="查询条件"
             contentTitle="医院查看"
             @query="queryData">

      <template slot="extendFormItems">
        <el-form-item label="医院">
          <el-input v-model="queryForm.hospital" placeholder="请输入医院编码或者医院名称" />
        </el-form-item>
      </template>

      <template slot="buttons">
        <el-button @click="showDialog(null,2)" type="primary" size="mini">新增</el-button>
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <el-table
          :data="tableData"
          style="width: 100%">
          <el-table-column
            prop="hospitalId"
            label="医院id">
          </el-table-column>
          <el-table-column
            prop="medinsName"
            label="医院名称">
          </el-table-column>
          <drg-table-column
            prop="hospLv"
            dicType="YYJB"
            label="医院级别">
          </drg-table-column>
          <el-table-column
            prop="hospCof"
            label="医院系数">
          </el-table-column>
          <el-table-column
            prop="admdvsCode"
            label="所在地区">
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-edit"
                type="primary"
                circle
                @click="showDialog(scope.row,1)"></el-button>
            </template>
          </el-table-column>
          <el-table-column label="删除" align="center" fixed="right">
            <template slot-scope="scope">
              <el-popconfirm style="cursor: pointer;display: block" @confirm="deleteHospital(scope.row)"
                             title="是否删除？">
                <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog :title="profttl" :visible.sync="dialogFormVisible">
          <el-form ref="form" :model="form" label-width="90px">
            <el-form-item label="医院id" >
              <el-input v-model="form.hospitalId"  :disabled="xx" placeholder="请输入医院id"></el-input>
            </el-form-item>
            <el-form-item label="医院名称">
              <el-input v-model="form.medinsName" :disabled="xx" placeholder="请输入医院名称"></el-input>
            </el-form-item>
            <el-form-item label="医院级别" prop="hospLv">
              <drg-dict-select dicType="YYJB"
                              v-model="form.hospLv"
                              placeholder="请输入医院级别中文"
                              :useClass="false"
                              :selectStyle="{ width: '100%' }"/>
            </el-form-item>
            <el-form-item label="医院系数">
              <el-input v-model="form.hospCof" placeholder="请输入医院系数"></el-input>
            </el-form-item>
            <el-form-item label="地区">
              <el-input v-model="form.admdvsCode" placeholder="请输入医院地区"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取 消</el-button>
            <el-button type="primary" @click="commitData()">确 定</el-button>
          </div>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import {
  deleteHospitalInfo,
  insertHospitalInfo,
  queryHospitalInfo,
  updateHospitalInfo
} from '@/api/dataConfig/hospitalView'

export default {
  name: 'hospitalView',
  data: () => ({
    queryForm: {},
    tableData: [],
    dialogFormVisible: false,
    form: {},
    profttl: '',
    dialogType: '1',
    xx: true
  }),
  mounted () {
    this.queryData()
  },
  methods: {
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    queryData () {
      queryHospitalInfo(this.getParams()).then(res => {
        this.tableData = res.data
      })
    },
    showDialog (row, type) {
      this.dialogFormVisible = true
      if (type == 1) {
        this.profttl = '编辑'
        this.dialogType = type
        this.form = row
        this.xx = true
      } else {
        this.profttl = '新增'
        this.dialogType = type
        this.form = {}
        this.xx = false
      }
    },
    commitData () {
      if (this.dialogType == 1) {
        updateHospitalInfo(this.form)
        this.dialogFormVisible = false
      } else {
        insertHospitalInfo(this.form).then(res => {
          this.queryData()
        })
        this.dialogFormVisible = false
      }
    },
    deleteHospital (row) {
      deleteHospitalInfo(row).then(res => {
        this.queryData()
      })
    }
  }
}
</script>
