import request from '@/utils/request'

/*
 * 字典管理模块
 */

// 保存
export function save (data) {
  return request({
    url: '/dict/save',
    method: 'post',
    data: data
  })
}
// 刷新缓存
export function refreshCache (data) {
  return request({
    url: '/dict/refreshCache',
    method: 'post',
    data: data
  })
}
// 删除
export function batchDelete (data) {
  return request({
    url: '/dict/delete',
    method: 'post',
    data: data
  })
}
// 分页查询
export function findPage (data) {
  return request({
    url: '/dict/findPage',
    method: 'post',
    data: data
  })
}
