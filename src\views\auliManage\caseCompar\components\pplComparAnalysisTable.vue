<template>
  <div class="som-comps-container">
    <el-table :data="tableData"
              border
              id="dataTableId"
              highlight-current-row
              :header-cell-style="{'text-align':'center'}"
              size="mini"
              height="100%"
              @row-click="fnTableRowClick"
              v-loading="tableLoading">
      <drg-table-column
          type="index"
          align="right"
          label="序号">
      </drg-table-column>

      <drg-table-column
          prop="id"
          v-if="false"
          label="settle_list_id">
      </drg-table-column>

      <drg-table-column
          prop="patientId"
          width="110"
          align="center"
          label="病案号">
      </drg-table-column>

      <el-table-column
          prop="name"
          width="90"
          label="姓名">
      </el-table-column>

      <el-table-column
          prop="deptName"
          width="100"
          label="科室">
      </el-table-column>

      <el-table-column
          prop="inHosTime"
          label="入院时间">
      </el-table-column>

      <el-table-column
          prop="outHosTime"
          label="出院时间">
      </el-table-column>

      <el-table-column fixed="right" label="选择" align="center" width="100">
        <template slot-scope="scope">
          <el-button icon="el-icon-check"
                     @click="fnTableColumnCheck(scope.row)"
                     :class="[scope.row.checked ? 'el-button--success' : '']"
                     circle/>
        </template>
      </el-table-column>

    </el-table>
    <div class="check-info" v-if="checkPatientId != ''"
         :style="[ rightZero ? {right: '0'} : '' ]">
      当前选择病案号：
      <span>
        {{ checkPatientId }}
      </span>
    </div>
  </div>
</template>
<script>
export default {
  name: 'pplComparAnalysisTable',
  props: {
    tableData: [],
    tableLoading: Boolean,
    queryForm: {},
    total: {
      type: Number,
      default: () => 0
    },
    // 显示病案号，是否是靠右边
    rightZero: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    checkPatientId: ''
  }),
  methods: {
    handleSizeChange (val) {
      this.queryForm.pageNum = 1
      this.queryForm.pageSize = val
      this.$emit('changePackage', '')
    },
    handleCurrentChange (val) {
      this.queryForm.pageNum = val
      this.$emit('changePackage', '')
    },
    fnTableColumnCheck (row) {
      this.fnRowClick(row)
    },
    emitCheckState (type) {
      this.$emit('checkState', type)
    },
    fnTableRowClick (row, column, event) {
      this.fnRowClick(row)
    },
    fnRowClick (row) {
      if (row.checked) {
        // row.checked = false
        // this.checkPatientId = ''
        // this.emitCheckState(false)
      } else {
        this.tableData.map(data => {
          data.checked = false
          return data
        })
        row.checked = true
        this.checkPatientId = row.patientId
        this.emitCheckState(true)
        this.$emit('changeCheck', row.id)
      }
    }
  }
}
</script>
<style scoped>
.pagination {
  text-align: right;
}

.check-info {
  position: absolute;
  top: -1.6rem;
  font-size: var(--textSize);
}
</style>
