<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-date-range show-in-date-range show-se-date-range
             show-patient-num
             :show-hos-dept="{ show: this.$somms.hasHosRole() }"
             show-dip
             show-pagination
             :extendFormIndex="[3]"
             :totalNum="total"
             :container="true"
             :exportExcel="{ 'tableId': tableId, exportName: 'DIP患者分析'}"
             :exportExcelFun="selectPatientData"
             :exportExcelHasChild="false"
             headerTitle="查询条件"
             contentTitle="患者分析"
             @query="queryData" >
      <template slot="extendFormItems">
        <el-form-item label="姓名" prop="name">
          <el-input placeholder="请输入姓名" v-model="queryForm.name" class="som-form-extend-form-item" />
        </el-form-item>
        <el-form-item label="病种类型" prop="diseType">
          <el-select v-model="queryForm.diseType" multiple placeholder="请选择类型" class="som-form-extend-form-item">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否死亡病例" prop="isDead">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择" v-model="queryForm.isDead" @change="queryData"/>
        </el-form-item>
        <el-form-item label="是否31日再入院" prop="isReAdm">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择" v-model="queryForm.isReAdm" @change="queryData"/>
        </el-form-item>

      </template>



      <!-- 内容 -->
      <template slot="containerContent" >
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  :id="tableId"
                  size="mini"
                  stripe
                  height="100%"
                  :data="tableData"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>

            <el-table-column label="序号" type="index" prop="index" ></el-table-column>
            <el-table-column label="病案号" prop="bah">
              <template slot-scope="scope">{{scope.row.bah}}</template>
            </el-table-column>
            <el-table-column label="就诊标识" prop="jzFlag">
              <template slot-scope="scope">{{scope.row.jzFlag}}</template>
            </el-table-column>
            <el-table-column label="结算id" prop="jsid">
              <template slot-scope="scope">{{scope.row.jsid}}</template>
            </el-table-column>
            <el-table-column label="姓名" prop="name">
              <template slot-scope="scope">{{scope.row.name}}</template>
            </el-table-column>
            <el-table-column label="年龄" align="right" prop="age">
              <template slot-scope="scope">{{scope.row.age}}</template>
            </el-table-column>
            <el-table-column label="科室" :show-overflow-tooltip="true" prop="deptName">
              <template slot-scope="scope">{{scope.row.deptName}}</template>
            </el-table-column>
            <el-table-column label="出院时间" :show-overflow-tooltip="true" prop="outHosTime">
              <template slot-scope="scope">{{scope.row.outHosTime}}</template>
            </el-table-column>
            <el-table-column label="DIP编码" :show-overflow-tooltip="true" prop="dipCodg">
              <template slot-scope="scope">{{scope.row.dipCodg}}</template>
            </el-table-column>
            <el-table-column label="DIP名称" :show-overflow-tooltip="true" prop="dipName">
              <template slot-scope="scope">{{scope.row.dipName}}</template>
            </el-table-column>
            <el-table-column label="是否入组" :show-overflow-tooltip="true" prop="inDipGroup">
              <template slot-scope="scope">
                <span>{{ scope.row.inDipGroup }}</span>
              </template>
            </el-table-column>
            <el-table-column label="预测费用" :show-overflow-tooltip="true" prop="forecastFee" >
              <template slot-scope="scope">{{scope.row.forecastFee}}</template>
            </el-table-column>
            <el-table-column label="总费用" :show-overflow-tooltip="true" prop="sumfee">
              <template slot-scope="scope">{{scope.row.sumfee}}</template>
            </el-table-column>
            <el-table-column label="盈亏" :show-overflow-tooltip="true" prop="profitloss">
              <template slot-scope="scope">{{scope.row.profitloss}}</template>
            </el-table-column>
            <el-table-column label="平均住院日" :show-overflow-tooltip="true" prop="avgDays">
              <template slot-scope="scope">{{scope.row.avgDays}}</template>
            </el-table-column>
            <el-table-column label="平均费用" :show-overflow-tooltip="true" prop="avgCost">
              <template slot-scope="scope">{{scope.row.avgCost}}</template>
            </el-table-column>
            <el-table-column label="31天再入院" :show-overflow-tooltip="true" prop="isReAdm">
              <template slot-scope="scope">{{scope.row.isReAdm}}</template>
            </el-table-column>
            <el-table-column label="是否死亡病例" :show-overflow-tooltip="true" prop="isDead">
              <template slot-scope="scope">{{scope.row.isDead}}</template>
            </el-table-column>
            <el-table-column label="辅助目录" prop="isUsedAsstList"  />

            <el-table-column label="年龄段" prop="asstListAgeGrp">
              <template slot-scope="scope">{{scope.row.asstListAgeGrp}}</template>
            </el-table-column>
            <el-table-column label="疾病严重程度" prop="asstListDiseSevDeg">
              <template slot-scope="scope">{{scope.row.asstListDiseSevDeg}}</template>
            </el-table-column>
            <el-table-column label="肿瘤严重程度" prop="asstListTmorSevDeg">
              <template slot-scope="scope">{{scope.row.asstListTmorSevDeg}}</template>
            </el-table-column>
              <el-table-column label="烧伤" prop="auxiliaryBurn">
                <template slot-scope="scope">{{scope.row.auxiliaryBurn}}</template>
              </el-table-column>

              <el-table-column label="基层病种" width="115" prop="baseDisease">
                <template slot-scope="scope">{{ scope.row.baseDisease == '1' ? '是' : '否' }}</template>
              </el-table-column>
              <el-table-column label="中医优势病种" width="115"prop="baseDisease">
                <template slot-scope="scope">{{ scope.row.dominantDisease == '1' ? '是' : '否' }}</template>
              </el-table-column>
              <el-table-column label="重点专科病种"width="115" prop="professionalDisease">
                <template slot-scope="scope">{{ scope.row.professionalDisease == '1' ? '是' : '否' }}</template>
              </el-table-column>
              <el-table-column label="低龄病种" width="115"prop="youngerDisease">
                <template slot-scope="scope">{{ scope.row.youngerDisease == '1' ? '是' : '否' }}</template>
              </el-table-column>
              <el-table-column label="病种类型及系数" width="115" prop="recommend">
                <template slot-scope="scope">{{ scope.row.recommend }}</template>
              </el-table-column>

          </el-table>

      </template>
    </drg-form>
  </div>
</template>
<script>
import { selectPatientData } from '@/api/dipBusiness/dipPatientAnalysis'
import { elExportExcel } from '@/utils/exportExcel'
export default {
  name: 'patientAnalysis',
  data: () => ({
    tableId: 'pplTable',
    queryForm: {
      diseType: [],
      isDead: null,
      isReAdm: null
    },
    options: [{
      value: '1',
      label: '中医优势病种'
    }, {
      value: '2',
      label: '基层病种'
    }, {
      value: '3',
      label: '低龄病种'
    }, {
      value: '4',
      label: '重点专科病种'
    }],
    tableData: null,
    total: 0,
    listLoading: false
  }),
  mounted () {
    this.$nextTick(() => {
      if (Object.keys(this.$route.query).length > 0) {
        this.queryForm.begnDate = this.$route.query.begnDate
        this.queryForm.expiDate = this.$route.query.expiDate
        this.queryForm.dateRange = this.$route.query.dateRange
        this.queryForm.deptCode = this.$route.query.deptCode
        if (this.$route.query.diseType) {
          this.queryForm.diseType = this.$route.query.diseType
        }
      }
      this.queryData()
    })
  },
  methods: {
    selectPatientData,
    allExcel () {
      this.$somms.exportExcelAll(this.getParams(), this.total, this.$refs.settleListTable.$children, document.getElementById('slTable').children[0].children[1].children[0].children[1].children[0].childNodes, selectPatientData, 'DIP患者分析')
    },

    exportExcel () {
      let tableId = 'slTable'
      let fileName = 'DIP患者分析'
      elExportExcel(tableId, fileName)
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    queryData () {
      this.listLoading = true
      selectPatientData(this.getParams()).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.total
          this.listLoading = false
        }
      })
    },
    clearRouteQuery () {
      if (Object.keys(this.$route.query).length > 0) {
        this.$router.push({ query: {} }).catch(() => {})
      }
    }
  },
  watch: {
    list: function () {
      this.$nextTick(() => {
        if (this.$refs.settleListTable) {
          this.$refs.settleListTable.doLayout()
        }
      })
    }
  },
}
</script>

<style>
/deep/ .c-content{
  height:81%;
}
</style>
