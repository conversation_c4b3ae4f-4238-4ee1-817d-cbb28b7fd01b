* {
  outline-width: 0;
  font-family: "Nunito" !important;
}

.loginContainer {
  height: 100vh;
  width: 100vw;
  background: #5191f5 center/cover fixed;
  display: flex;
  justify-content: center;
  align-items: center;
}

#formContainer {
  display: flex;
  transition: 0.2s ease;
  height: 60vh;
  width: 70vw;
  min-width: 700px;
  max-width: 1080px;
  transition-delay: 0.3s;
  background: #FFFFFF center/cover fixed;
  background-clip: content-box;
}

.form-left {
  width: 50%;
  position: relative;
  overflow: hidden;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.form-left:before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  filter: blur(5px);
}

.form-left form {
  position: relative;
  height: 100%;
  padding: 40px 25px;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.form-left form header {
  color: #6c8ded;
  text-align: left;
  margin-bottom: 15px;
}

.form-left form header h1 {
  margin: 0;
  font-weight: 600;
  font-size: 30px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.form-left form header p {
  margin: 5px 0 0;
  opacity: 0.5;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.form-left form section {
  flex: 1 1 1px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.form-left form section label {
  display: block;
  margin-bottom: 15px;
  position: relative;
}

.form-left form section label p {
  color: #000;
  margin: 0 0 10px 0;
  font-weight: 600;
  font-size: 12px;
  opacity: 0.5;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.form-left form section label input:focus ~ .border {
  transform: scale(1, 1);
}

.form-left form section label input:not(:-moz-placeholder-shown) ~ .border {
  transform: scale(1, 1);
}

.form-left form section label input:not(:-ms-input-placeholder) ~ .border {
  transform: scale(1, 1);
}

.form-left form section label input:not(:placeholder-shown) ~ .border {
  transform: scale(1, 1);
}

.form-left form section label:last-child {
  margin-bottom: 0;
}

.form-left form footer {
  margin-top: 15px;
  display: flex;
}

.form-left form footer button {
  background: transparent;
  padding: 0;
  border: none;
  color: #fff;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  flex: 1;
  opacity: 0.5;
}

.form-left form footer button:hover {
  opacity: 1;
}


/deep/ .el-button {
  position: relative;
  background: #5191f5;
  border: none;
  width: 100%;
  padding: 10px 0;
  font-weight: 600;
  font-size: 30px;
  color: #fff;
  cursor: pointer;
}

/deep/ .el-button:hover {
  color: white;
  background: #5191f5;
}

/deep/ .el-button:focus {
  color: white;
  background: #5191f5;
}

.btn_init{font-size: 26px}
