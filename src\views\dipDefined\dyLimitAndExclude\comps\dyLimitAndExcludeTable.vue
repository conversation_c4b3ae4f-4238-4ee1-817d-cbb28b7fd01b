<template>
  <div style="height: 100%">
    <el-table
      :data="tableData"
      :header-cell-style="{'text-align':'center'}"
      height="96%"
      ref="elTable"
      v-loading="tableLoading"
      border>
      <el-table-column label="序号" type="index" align="center"/>
      <el-table-column label="1.0版手术操作代码" prop="oneOperationTechniqueCode" align="center" v-if="this.type == 1"/>
      <el-table-column label="1.0版手术操作名称" prop="oneOperationTechniqueName" align="center" v-if="this.type == 1"/>
      <el-table-column label="2.0版手术操作代码" prop="twoOperationTechniqueCode" align="center" v-if="this.type == 1"/>
      <el-table-column label="2.0版手术操作名称" prop="twoOperationTechniqueName" align="center" v-if="this.type == 1"/>
      <el-table-column label="1.0版诊断代码" prop="oneDiagnoseCode" align="center" v-if="this.type == 2"/>
      <el-table-column label="1.0版诊断名称" prop="oneDiagnoseName" align="center" v-if="this.type == 2"/>
      <el-table-column label="2.0版诊断代码" prop="twoDiagnoseCode" align="center" v-if="this.type == 2"/>
      <el-table-column label="2.0版诊断名称" prop="twoDiagnoseName" align="center" v-if="this.type == 2"/>
      <el-table-column label="操作编码" prop="oprnOprtCode" align="center" v-if="this.type == 3"/>
      <el-table-column label="操作名称" prop="oprnOprtName" align="center" v-if="this.type == 3"/>
      <el-table-column label="该操作限定核心病种的主诊断亚目编码" prop="mainDiagnoseSuborderCode" align="center" v-if="this.type == 3"/>
      <el-table-column label="该操作限定核心病种的主诊断亚目名称" prop="mainDiagnoseSuborderName" align="center" v-if="this.type == 3"/>
      <el-table-column label="限定核心病种编码" prop="limitCoreDiseaseCode" align="center" v-if="this.type == 3"/>
    </el-table>
  </div>
</template>
<script>
export default {
  name: 'dipDiseaseTypeConfigTable',
  props: {
    tableData: {
      type: Array,
      required: true
    },
    tableLoading: {
      type: Boolean,
      required: true,
      default: false
    },
    type: {
      type: Number
    }
  },
  updated () {
    this.$nextTick(() => {
      this.$refs.elTable.doLayout()
    })
  },
  methods: {

  }
}
</script>
