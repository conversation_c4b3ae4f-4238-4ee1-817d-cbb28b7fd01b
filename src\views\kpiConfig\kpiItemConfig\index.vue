<template>
  <div class="app-container">
    <drg-form
      v-model="queryForm"
      :totalNum="total"
      container
      :showPagination="false"
      headerTitle="查询条件"
      contentTitle="国考数据项配置"
      @query="query"
    >
      <!-- 查询条件插槽 -->
      <template slot="extendFormItems">
        <el-form-item label="版本" prop="ver">
          <drg-dict-select dicType="PA_KPI_VERSION" v-model="queryForm.ver" />
        </el-form-item>
      </template>
      <!-- 按钮插槽 -->
      <template slot="buttons">
        <el-button @click="processData(true)" type="primary" class="som-button-margin-right">新增</el-button>
      </template>
      <!-- 内容插槽 -->
      <template slot="containerContent">
        <el-table
          :data="tableData"
          height="100%"
          row-key="kpiItemCode"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :row-style="{ cursor: 'pointer' }"
          v-loading="loading"
        >
          <el-table-column fixed label="序号" type="index" width="50"> </el-table-column>
          <!--          <el-table-column label="版本" prop="ver" />-->
          <el-table-column label="指标项名称" prop="kpiItemName" />
          <el-table-column label="指标项编码" prop="kpiItemCode" />
          <el-table-column label="计算公式" prop="displayKpiCalcFormula" />
          <el-table-column label="上级指标项编码" prop="parentKpiItemCode" />
          <el-table-column label="说明" prop="desc" show-overflow-tooltip />
          <drg-table-column label="指标类型" prop="kpiType" dict-type="KPI_TYPE" />
          <drg-table-column label="启用标准" prop="activeFlag" dict-type="STATUS" />
          <el-table-column label="创建时间" prop="crteTime" />
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-tooltip content="修改" placement="top" effect="light">
                <i class="el-icon-edit" style="font-size: 20px" @click="processData(false, scope.row)"></i>
              </el-tooltip>
              <el-tooltip content="删除" placement="top" effect="light">
                <i class="el-icon-delete" style="font-size: 20px" @click="remove(scope.row)"></i>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>

        <!-- 新增或编辑 -->
        <el-dialog title="提示" :visible.sync="showVisible" width="40%" :before-close="beforeCloseAction">
          <el-form :model="actionData.form" :rules="actionData.rules" ref="actionForm" label-width="120px">
            <el-form-item label="版本" prop="ver" required>
              <drg-dict-select dicType="PA_KPI_VERSION" v-model="actionData.form.ver" />
            </el-form-item>
            <el-form-item label="指标类型" prop="kpiType">
              <drg-dict-select dicType="KPI_TYPE" v-model="actionData.form.kpiType" @change="changeDataSource" />
            </el-form-item>
            <el-form-item label="指标项编码" prop="kpiItemCode" required>
              <el-input v-model="actionData.form.kpiItemCode" @input="changeValue"></el-input>
            </el-form-item>
            <el-form-item label="指标项名称" prop="kpiItemName" required>
              <el-input v-model="actionData.form.kpiItemName" @input="changeValue"></el-input>
            </el-form-item>
            <el-form-item label="计算公式" prop="kpiCalcFormula" v-if="actionData.form.kpiType === '2'">
              <el-input disabled v-model="actionData.form.kpiCalcFormula" @input="changeValue"></el-input>
              <div style="margin-top: 5px; display: flex">
                <el-dropdown @command="v => selectDsItem(v, false)">
                  <el-button >选择数据项</el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-input v-model="filterItemStr" @input="inputFilterItemStr" />
                    <el-dropdown-item :command="item.dsItemCode" v-for="item in dropdownList" :key="item.dsItemCode">{{
                      item.dsItemName
                    }}</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-button class="space" type="danger" @click="resetDsItem">重置</el-button>
                <el-tag
                  size="medium"
                  style="cursor: pointer"
                  @click="selectDsItem(s)"
                  v-for="s in operator"
                  :key="s"
                  class="space"
                  >{{ s }}</el-tag
                >
              </div>
            </el-form-item>
            <el-form-item label="上级指标项编码" prop="parentKpiItemCode">
              <el-tree
                :data="tableData"
                :props="{ label: 'kpiItemName' }"
                style="max-height: 100px"
                @node-click="handleNodeClick"
              ></el-tree>
            </el-form-item>
            <el-form-item label="说明" prop="desc">
              <el-input v-model="actionData.form.desc" @input="changeValue" type="textarea"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="beforeCloseAction">取 消</el-button>
            <el-button type="primary" @click="handlerItem">{{ state ? '新 增' : '更 新' }}</el-button>
          </span>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>

<script>
import { elExportExcel } from '@/utils/exportExcel'
import moment from 'moment'
import { queryData as queryDsData } from '@/api/hosPerfAppraisal/dsItemConfig'
import { queryData, addDsItem, updateDsItem, removeDsItem } from '@/api/hosPerfAppraisal/kpiItemConfig'
import * as math from 'mathjs'

export default {
  name: 'kpiItemConfig',
  data: () => ({
    total: 0,
    filterItemStr: '',
    showVisible: false,
    state: false, // true: 新增 false: 修改
    loading: false,
    actionData: {
      form: {},
      rules: {
        ver: [{ required: true, message: '请选择版本', trigger: 'change' }],
        kpiType: [{ required: true, message: '请选择类型', trigger: 'change' }],
        kpiItemCode: [{ required: true, message: '请输入编码', trigger: 'change' }],
        kpiItemName: [{ required: true, message: '请输入名称', trigger: 'change' }],
        kpiCalcFormula: [{ required: true, message: '请输入计算公示', trigger: 'blur' }]
      }
    },
    queryForm: {
      ver: '',
      desc: ''
    },
    tableData: [],
    dsItems: [],
    operator: ['+', '-', '*', '/', '(', ')', '100']
  }),
  mounted () {
    this.queryDsItemData()
  },
  computed: {
    dropdownList () {
      if (this.dsItems.length === 0) {
        return []
      }
      return this.dsItems.filter(
        a => a.dsItemCode.includes(this.filterItemStr) || a.dsItemName.includes(this.filterItemStr)
      )
    }
  },
  methods: {
    resetDsItem () {
      this.actionData.form.kpiCalcFormula = null
      this.$forceUpdate()
    },
    selectDsItem (v, flag = true) {
      if (flag) {
        let formula = this.actionData.form.kpiCalcFormula
        if (formula && v !== '100') {
          let last = formula.slice(-1)
          if (!['+', '-', '*', '/', '('].includes(last) && v.includes('(')) {
            this.$message.error('输入的公式有误')
            return
          }
          if (!v.includes('(') && ['+', '-', '*', '/', '('].includes(last)) {
            this.$message.error('输入的公式有误')
            return
          }
        }
      }
      this.actionData.form.kpiCalcFormula =
        (this.actionData.form.kpiCalcFormula ? this.actionData.form.kpiCalcFormula : '') + v
      this.$forceUpdate()
    },
    inputFilterItemStr (val) {
      this.queryDsItemData()
    },
    handleNodeClick (data) {
      this.actionData.form.parentKpiItemCode = data.kpiItemCode
    },
    queryDsItemData () {
      queryDsData({ pageSize: 30, pageNum: 1, searchText: this.filterItemStr }).then(res => {
        this.dsItems = res.data.list
      })
    },
    query () {
      this.loading = true
      queryData(this.queryForm)
        .then(res => {
          this.tableData = this.buildTree(res.data, 'kpiItemCode', 'parentKpiItemCode')
          this.loading = false
        })
        .catch(() => (this.loading = false))
    },
    buildTree (data, fld, parentField) {
      const map = new Map()
      const result = []

      // 将每个节点及其父节点ID存储在Map中便于查找
      data.forEach(node => {
        map.set(node[fld], node)
      })

      data.forEach(node => {
        const parent = map.get(node[parentField])
        if (parent) {
          // 若存在父节点，则为其创建children属性并添加当前节点
          if (!parent.children) {
            parent.children = []
          }
          parent.children.push(node)
        } else {
          // 若没有父节点，则认为是根节点，直接添加至结果数组
          result.push(node)
        }
      })

      // 返回仅包含根节点（即无父节点的节点）的树结构数组
      return result
    },
    // 删除
    remove (row) {
      removeDsItem(row).then(res => {
        this.$confirm('是否删除？').then(_ => {
          this.$message.success('移除成功')
          this.close()
        })
      })
    },
    // 点击新增/编辑按钮
    processData (flag, row) {
      if (flag) {
        this.actionData.form = {}
      } else {
        this.actionData.form = row
      }
      this.state = flag
      this.showVisible = true
    },
    // 新增或编辑
    handlerItem () {
      // 判断公式是否可用
      let formula = this.actionData.form.kpiCalcFormula
      if (formula) {
        let list = formula.split(/[\+\-\*\/]/)
        for (const listElement of list) {
          formula = formula.replace(listElement, '20')
        }
        if (!/^[\d\+\-\*\//\(\)]*$/.test(formula)) {
          this.$message.error('非法公式')
          return
        }
        try {
          let val = math.evaluate(formula)
        } catch (e) {
          this.$message.error('公式配置有误，请检查')
          return
        }
      }
      this.$refs.actionForm.validate(valid => {
        if (valid) {
          if (this.state) {
            addDsItem(this.actionData.form).then(res => {
              this.$message.success('新增成功')
              this.close()
            })
          } else {
            updateDsItem(this.actionData.form).then(res => {
              this.$message.success('更新成功')
              this.close()
            })
          }
        }
      })
    },
    // 关闭
    close () {
      this.showVisible = false
      this.query()
    },
    beforeCloseAction () {
      this.close()
    },
    // 输入没有内容
    changeValue (e) {
      this.$forceUpdate()
    },
    // 改变
    changeDataSource (val) {
      if (val === '1') {
        this.actionData.form.kpiCalcFormula = null
      }
      this.actionData.form.kpiType = val
    }
  }
}
</script>
<style scoped>
.space {
  margin-left: 5px;
}
/deep/ .el-form-item__content {
  width: calc(100% - 120px) !important;
}
</style>
