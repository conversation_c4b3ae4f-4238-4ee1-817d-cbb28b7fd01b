<template>
  <el-menu :default-active="'home'"
           ref="menu-panel"
           class="el-menu-demo"
           mode="horizontal"
           background-color="#1b65b9"
           text-color="#CAD8F3"
           active-text-color="#fafafa"
           @select="handleSelect">
    <template v-for="(item, index) in showMenuList">
      <el-menu-item :index="item.meta.index+''" class="my-menu" :id="item.meta.index" :key="index">
        <div><i v-if="item.meta&&item.meta.icon" :class="item.meta.icon"></i>{{ item.meta.profttl }}</div>
      </el-menu-item>
    </template>
    <div v-if="isOverFlow" class="more-menu">
      <i class="el-icon-arrow-down"></i>
      <div class="more-menu-panel">
        <template v-for="(item,index) in moreMenu">
          <div class="more-menu-item" @click="handleSelect(item.meta.index+'')" :key="index">
            <i v-if="item.meta&&item.meta.icon" :class="item.meta.icon"></i>
            {{ item.meta.profttl }}
          </div>
        </template>
      </div>
    </div>
  </el-menu>
</template>

<script>
export default {
  name:'mainMenu',
  props: {
    menuList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data: function () {
    return {
      isOverFlow: false,
      moreMenu: [],
      showMenuList: []
    }
  },
  mounted () {
    // this.$nextTick(()=>{
    this.showMenuList = this.noHiddenMenuList
    this.handleSelect(this.showMenuList[0].meta.index + '')
    this.$nextTick(() => {
      this.fnSetIsOverFlow()
    })
    //   // this.fnSetIsOverFlow()
    // })
  },
  computed: {
    noHiddenMenuList () {
      if (this.menuList && this.menuList.length > 0) {
        return this.menuList.filter((a) => !a.is_hide && a.children && a.meta)
      }
      return []
    }
  },
  methods: {
    handleSelect (menuIndex, indexPath) {
      this.$emit('select-menue', menuIndex)
    },
    menuPanelWidth () {
      const menuPanel = this.$refs['menu-panel']
      if (menuPanel) {
        return menuPanel.$el.offsetWidth
      }
      return 0
    },
    fnSetIsOverFlow () {
      const menuDomList = document.getElementsByClassName('my-menu')
      const menuPanelWidth = this.menuPanelWidth()
      let width = 0
      let showMenuList = []
      this.moreMenu = []
      this.showMenuList = []
      if (menuDomList && menuDomList.length > 0) {
        for (let i = 0; i < menuDomList.length; i++) {
          width += menuDomList[i].offsetWidth
          if (width > menuPanelWidth) {
            this.isOverFlow = true
            this.moreMenu.push(this.noHiddenMenuList[i])
          } else {
            showMenuList.push(this.noHiddenMenuList[i])
          }
        }
        this.showMenuList = showMenuList
      }
    }
  },
  watch: {
    // noHiddenMenuList:{
    //   handler: function(newVal,oldVal){
    //     this.showMenuList = this.noHiddenMenuList
    //     this.$nextTick(()=>{
    //       this.fnSetIsOverFlow()
    //     })
    //   }
    // }
  }
}
</script>

<style scoped>
.el-menu-demo {
  width: 100%;
  /*overflow: hidden;*/
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
  position: relative;
}

.el-menu-demo .el-menu-item {
}

el-menu-demo:before {
  width: 0;
  margin: 0;
  display: none;
  clear: both;
}

.swipe-menu {
  width: 100%;
}

.more-menu {
  width: 20px;
  height: 100%;
  color: #FFF;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
  text-align: center;
}

.more-menu .more-menu-panel {
  position: absolute;
  width: 200px;
  right: -100px;
  top: calc(100% + 5px);
  display: flex;
  flex-flow: column;
  visibility: hidden;
  transition: visibility 0.5s ease-in-out;
  background-color: #FFFFFF;
  z-index: 9999;
}

.more-menu .more-menu-panel {
  color: #000;
  font-size: 14px;
  padding: 5px 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.more-menu .more-menu-panel .more-menu-item {
  text-align: left;
  height: 40px;
}

.more-menu .more-menu-panel .more-menu-item:hover {
  color: #165194;
}

.more-menu:hover {
  background-color: #165194
}

.more-menu:hover .more-menu-panel {
  visibility: visible;
}

.el-menu-item.my-menu.is-active{
  color: #fafafa;
  background-color: #225195;
}

</style>
