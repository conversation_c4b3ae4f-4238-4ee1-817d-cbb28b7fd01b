<template>
  <el-dialog
    title="结算患者类型"
    :visible.sync="dialogVisible"
    width="57%"
    @closed="handleClose"
    @opened="initPieData">
    <div id="pieChart" style="height: 550px">
      <el-card class="box-card" v-for="i in 4" :key="i">
        <drg-echarts :options="getMoreOption(i)"/>
      </el-card>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>

export default {
  name: 'moreDialog',
  props: {
    moreDialogVisible: {
      type: Boolean
    },
    data: {
      type: Object,
      defaultL: {
        totalCostPieList: [],
        overallPieList: [],
        groupOverallPieList: [],
        diffPieList: []
      }
    }
  },
  data () {
    return {
      moreOption1: {},
      moreOption2: {},
      moreOption3: {},
      moreOption4: {},
      dialogVisible: false
    }
  },
  watch: {
    moreDialogVisible: function (newVal) {
      this.editShowDialog(newVal)
    }
  },
  methods: {
    editShowDialog (val) {
      this.dialogVisible = val
    },
    getMoreOption (index) {
      return this['moreOption' + index]
    },
    initPieData () {
      let option
      option = {
        title: {
          text: '结算患者类型',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        color: this.$somms.generateColor(),
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: []
          }
        ]
      }
      let names = ['总费用', '统筹费用', '病组统筹费用', '差异']
      for (let i = 0; i < names.length; i++) {
        let name = names[i]
        let tempOption = this.$somms.cloneObj(option)
        tempOption.profttl.text = name
        if (name === '总费用') {
          tempOption.series[0].data = this.data.totalCostPieList
        } else if (name === '统筹费用') {
          tempOption.series[0].data = this.data.overallPieList
        } else if (name === '病组统筹费用') {
          tempOption.series[0].data = this.data.groupOverallPieList
        } else if (name === '差异') {
          tempOption.series[0].data = this.data.diffPieList
        }
        this['moreOption' + (i + 1)] = tempOption
      }
    },
    handleClose () {
      this.$emit('closed', false)
    }
  }
}
</script>
<style scoped>
.box-card {
  width: 400px;
  height: 280px;
  margin-top: 10px;
  margin-left: 10px;
  float: left;
}
</style>
