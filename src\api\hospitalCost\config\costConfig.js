import request from '@/utils/request'
// 系统配置

export function queryData (params) {
  return request({
    url: '/costConfigController/queryData',
    method: 'post',
    params: params
  })
}

export function modifyConfig (params) {
  return request({
    url: '/costConfigController/updateCost',
    method: 'post',
    params: params
  })
}

export function updateCostConfigBatch (params) {
  return request({
    url: '/costConfigController/updateCostConfigBatch',
    method: 'post',
    data: params
  })
}
// 查询成本科室
export function selectStandardDeptUnitData (params) {
  return request({
    url: '/deptCodeCompareController/selectStandardDeptUnitData',
    method: 'post',
    data: params
  })
}
// 插入数据
export function insertDeptCode (params) {
  return request({
    url: '/deptCodeCompareController/insertDeptCode',
    method: 'post',
    params: params
  })
}
// 查询对照表
export function selectDeptCode (params) {
  return request({
    url: '/deptCodeCompareController/selectDeptCode',
    method: 'post',
    params: params
  })
}
/**
 * 删除科室对照
 * @param params
 * @returns {*}
 */
export function deleteDeptCode (params) {
  return request({
    url: '/deptCodeCompareController/deleteDeptCode',
    method: 'post',
    params: params
  })
}
