<template>
  <div class="som-wd-one-hundred sl-validate-container">
    <div class="sl-validate-show-all-null" v-if="curTab === '1' && tabs.length !== 0">
      <el-button @click="showAllNull">显示所有{{ this.errorName }}</el-button>
    </div>
    <div class="som-h-one-hundred sl-validate-success som-flex-center" v-if="listStes && listinfo">
      请稍等，正在质控校验数据
    </div>
    <div class="som-h-one-hundred sl-validate-success som-flex-center" v-else-if="tabs.length === 0 && otherNotError ">
      您已完成清单质控！
    </div>
    <el-tabs v-model="curTab" class="som-h-one-hundred" style="overflow-y: hidden" v-if="tabs.length > 0 || !otherNotError">
      <el-tab-pane v-for="(item,index) in controlTabs"
                   :key=" 'a' + index"
                   :label="item.label"
                   :name="item.value"
                   class="som-h-one-hundred">

        <!-- 基础信息 -->
        <el-tabs v-model="activeName" v-if="item.value === '1'" class="sl-validate-item" @tab-click="tabClick">
          <el-tab-pane v-for="(baseItem,index) in tabs"
                       :key=" 'b' + index"
                       :label="baseItem.label"
                       :name="baseItem.value"
                       style="height: 100%">
            <el-table
              :data="baseItem.data"
              @row-click="rowClick"
              height="80%">
              <el-table-column label="序号" type="index" align="center" />
              <el-table-column prop="error" label="错误原因" />
            </el-table>
          </el-tab-pane>
        </el-tabs>

        <!-- 深度/逻辑质控 -->
          <template v-for="(cdItem, cdIndex) in deepAndLogicCards">
        <div class="sl-validate-item" style="overflow-y: auto;"  v-if="item.value === cdItem.type" :key="'e' + cdIndex">
          <div v-if="cdItem.value.length > 0" class="sl-validate-item-deep-item">
            <el-card v-for="(deepItem,index) in cdItem.value" :key=" 'c' + index"  class="sl-validate-item-card"
                     shadow="hover">
              <div slot="header" class="sl-validate-item-card-header">
                <span>{{ deepItem.label }}</span>
              </div>
              <div v-for="(itemDeep,index) in deepItem.data" :key="'d'+index">
                {{ (deepItem.data.length === 1 ? '' : (index + 1) + '、') + itemDeep.error }}
              </div>
            </el-card>
          </div>
          <div class="som-h-one-hundred sl-validate-success som-flex-center" v-else>
            已完成{{ item.label }}
          </div>
        </div>
          </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import {selectBusSettleLIstError } from '@/api/medicalQuality/settleListDetail'
import {selectBusSettleErrorLIst} from "@/api/medicalQuality/settleListDetail";
export default {
  props: {
    params: [Object],
    show: [Boolean]
  },
  data: () => ({
    activeName: '',
    controlTabs: [
      {
        label: '基础质控',
        value: '1'
      },
      {
        label: '编码质控',
        value: '2'
      },
      {
        label: '逻辑质控',
        value: '3'
      }
    ],
    curTab: '',
    listStes: true,
    listinfo: true,
    tabs: [],
    deepAndLogicCards: [],
    errorName: ''
  }),
  computed: {
    otherNotError () {
      let flag = true
      if (this.deepAndLogicCards.length !== 0) {
        this.deepAndLogicCards.forEach(item => {
          if (item.value.length !== 0) {
            flag = false
          }
        })
      }
      return flag
    }
  },
  methods: {
    // 查询
    query () {
      let data = JSON.parse(JSON.stringify(this.$somms.getDictValue('QDJYCWLX', '2')))
      let tabs = []
      let cards = []
      let logicCards = []
      selectBusSettleLIstError(this.params).then(res => {
        if (res.data) {
          let activeTab = []
          Object.keys(res.data).map(key => {
            activeTab.push(key)
          })
          for (let tab of data) {
            if (activeTab.includes(tab.value)) {
              tab.data = res.data[tab.value]
              if (tab.value < 5) {
                tabs.push(tab)
              } else {
                if (tab.value > 300 && tab.value < 400) {
                  cards.push(tab)
                } else {
                  logicCards.push(tab)
                }
              }
            }
          }
        }

        if (Object.keys(res.data).length !== 0){
          this.listinfo = false
        }
        if (tabs.length > 0) {
          this.activeName = tabs[0].value
          this.tabClick()
        }
        this.curTab = this.controlTabs[0].value
        this.tabs = tabs
        this.deepAndLogicCards = [
          {type: '2', value: cards},
          {type: '3', value: logicCards}
        ]
      })

      this.getlistStes()
    },
    getlistStes(){
      selectBusSettleErrorLIst(this.params).then(res => {
        if (true === res.data) {
          this.$emit('updateScriptStas',true)
         this.listStes = false
        }
      })
    },

    tabClick () {
      this.errorName = this.$somms.getDictValueByType(this.activeName, 'QDJYCWLX', '2')
    },
    // 表格行点击
    rowClick (row) {
      let key = row.fld + '-' + this.activeName
      this.$emit('focusKeyChange', key)
    },
    // 显示所有空值
    showAllNull () {
      let params = { data: this.tabs, type: this.activeName }
      this.$emit('showAllNull', params)
    }
  },
  watch: {
    show: {
      immediate: true,
      handler: function (val) {
        if (val) {
          this.query()
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
.sl-validate-container{
  padding: 1rem;
  position: relative;
}
.sl-validate-show-all-null{
  z-index: 99;
  position: absolute;
  right: 1rem;
  top: 1rem;
}
.sl-validate-success{
  font-size: 20px;
  font-weight: 600;
}
.sl-validate-item{
  height: 94%;
  width: 100%;

  &-deep-item{
    overflow-y: auto;
  }

  &-card{
    width: 100%;
    height: 20%;
    margin-bottom: 1rem;

    &-header{
      color: #e6a23c;
    }
  }
}

/deep/ .el-tabs__content{
  height: 100%;
}
/deep/ .el-card__body{
  padding: 10px 17px 10px 10px !important;
}
</style>
