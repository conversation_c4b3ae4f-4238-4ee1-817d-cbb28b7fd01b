import request from '@/utils/request'

/**
 * 查询科室成本费用项配置
 * @param params
 * @returns {*}
 */
export function queryStandardCostItem (params) {
  return request({
    url: '/costStandardCostItemController/queryStandardCostItem',
    method: 'post',
    params: params
  })
}

/**
 * 新增成本费用项配置
 * @param params
 * @returns {*}
 */
export function addStandardCostItem (params) {
  return request({
    url: '/costStandardCostItemController/addStandardCostItem',
    method: 'post',
    params: params
  })
}

/**
 * 查询费用项编码是否存在
 * @param params
 * @returns {*}
 */
export function queryCostItemCodeExists (params) {
  return request({
    url: '/costStandardCostItemController/queryCostItemCodeExists',
    method: 'post',
    params: params
  })
}

/**
 * 修改费用项状态
 * @param params
 * @returns {*}
 */
export function modifyCostItemStatus (params) {
  return request({
    url: '/costStandardCostItemController/modifyCostItemStatus',
    method: 'post',
    params: params
  })
}

/**
 * 删除费用项状态
 * @param params
 * @returns {*}
 */
export function deleteCostItem (params) {
  return request({
    url: '/costStandardCostItemController/deleteCostItem',
    method: 'post',
    params: params
  })
}

/**
 * 查询科室成本费用项配置
 * @param params
 * @returns {*}
 */
export function queryCostItem (params) {
  return request({
    url: '/costStandardCostItemController/queryCostItem',
    method: 'post',
    params: params
  })
}
