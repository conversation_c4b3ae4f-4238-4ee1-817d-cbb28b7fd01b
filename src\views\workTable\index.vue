<template>
  <div class="app-container">
    <div style="height: 100%;overflow: hidden">
      <div class="fp-container">
        <div style="display: flex;align-items: center;justify-content: end">
<!--          <el-tooltip class="item" style="margin-right: 3rem" effect="dark" content="开启后会只查询医保结算数据" placement="top">-->
<!--            <el-switch-->
<!--              v-model="enableAns"-->
<!--              active-color="#13ce66"-->
<!--              inactive-color="#ff4949"-->
<!--              active-text="开启"-->
<!--              inactive-text="不开启"-->
<!--              @change="enableSeAnsChange">-->
<!--            </el-switch>-->
<!--          </el-tooltip>-->
          <el-radio-group v-model="type" @change="radioChange" style="margin-right: 20px;">
            <el-radio-button :label="1">出院</el-radio-button>
            <el-radio-button :label="2">结算</el-radio-button>
          </el-radio-group>
          <el-date-picker
            format="yyyy-MM"
            v-model="curTime"
            :clearable="false"
            @change="fnTimeChange"
            type="month"
            size="small">
          </el-date-picker>
        </div>
        <el-row type="flex" justify="space-between" class="base-row">
          <el-col :span="15" class="middle" style="width:100%;height:100%;">
            <!-- 清单上传-->
            <div class="settle-list-upload-warp">
              <el-card class="settle-list-upload-item" v-for="(item,index) in settleListUploadList" :key="index">
                <div class="settle-list-upload-content">
                  <h5 class="settle-list-upload-content-title title-icon">{{ item.profttl }}</h5>
                  <h2 class="settle-list-upload-content-value">{{ item.value }}</h2>
                  <p class="uploaded-state-title">{{ item.label }}</p>
                  <div class="progress-line">
                    <el-progress :percentage=parseFloat(item.ratio) :color="item.color" :stroke-width="20"
                                 :text-inside="true"></el-progress>
                  </div>
                </div>
              </el-card>
            </div>
            <!-- 入组情况-->
            <div class="aside">
              <el-card class="aside-left">
                <div class="el-card-header title-icon">
                  <h6>病例入组情况</h6>
                </div>
                <div class="settle-list-row">
                    <el-card class="settle-list-row-item" shadow="hover"  @click.native="jumpClick(item)"
                             v-for="(item,index) in inGroupData"
                             :key="index" >
                      <i :class="item.icon" style="height: 35px;width: 35px;cursor:pointer;" ></i>
                      <div class="content">
                        <h6 class="">{{ item.label }}</h6>
                        <span class="paient-casenum-title">{{ item.value }}</span>
                      </div>
                    </el-card>
                </div>
              </el-card>
              <el-card class="aside-right">
                <drg-echarts :options="inGroupOption"/>
              </el-card>
            </div>
            <!-- 分权限显示近一周审核情况-->
            <div class="aside" style="height: 55%">
              <el-card class="aside-left">
                <div class="hos-analysis-footer som-h-one-hundred">
                  <drg-echarts :options="getLossOrProfitOrderOption(1)" v-if="existsSortData"/>
                  <el-empty :description="'暂无' + sortName + '排名情况'" class="has-data-remind" v-else/>
                </div>
                <div slot="header" class="clearfix title-icon" style="font-size: 14px;">
                  <span>{{ sortName }}亏损排名情况</span>
                </div>
              </el-card>
              <el-card class="aside-right">
                <div slot="header" class="clearfix title-icon" style="font-size: 14px;">
                  <span>清单修改情况</span>
                </div>
                <el-card v-if="settleListModifyData.length === 0" shadow="never" class="timeline-content">
                  <el-empty description="在当前时间内暂无科室修改清单数据" class="has-data-remind"></el-empty>
                </el-card>
                <el-timeline v-else>
                  <el-timeline-item :timestamp="item.modifyTime" placement="top"
                                    v-for="(item,index) in settleListModifyData"
                                    :key="index" class="timeline-item">
                    <el-card shadow="never">
                      <h4>
                        共{{ item.modifyedSettleListNumByDept }}个科室，
                        {{ item.modifyedSettleListUserNum }}个医生修改了{{ item.modifyedSettleListNumByDay }}条病例，
                        <span style="margin-left:5px;">
                               预测总费用从
                                <span v-html="commonFormatCost(item.oldForcastCost,true)"></span>
                                变为
                                <span v-html="commonFormatCost(item.modifyedForcastCost,true)"></span>
                              </span>
                      </h4>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
              </el-card>
            </div>
          </el-col>
          <!-- 右侧-->
          <el-col :span="8" class="right">

            <div class="som-h-one-hundred">
              <!-- 预测/反馈-->
              <div class="predict-box">
                <div class="predict-feedback-item" v-for="(item,index) in costSickCaseData" :key="index">
                  <h1 v-if="index<=2" class="big-val base-col-title" v-html="commonFormatCost(item.value, true)"></h1>
                  <h1 v-else class="big-val base-col-title">{{ item.value }}</h1>
                  <p class="base-col-title">{{ item.label }}</p>
                </div>
              </div>
              <!-- 病例数-->
              <el-card v-if="hasData" shadow="never" class="fp-warning-box">
                <el-empty description="无可调整数据" class="has-data-remind"></el-empty>
              </el-card>
              <div v-else>
                <el-card class="paient-casenum-card fp-warning-box"
                         v-for="(noHandleItem,index) in noHanldeSettleListData" :key="index" v-show="noHandleItem.value !== 0">
                  <div class="paient-casenum-box">
                    <div class="grid-content">
                      <i :class="noHandleItem.icon" style="height: 35px;width: 35px"></i>
                    </div>
                    <div class="grid-content paient-casenum">
                      <h6 class="paient-casenum-item">{{ noHandleItem.value }}</h6>
                      <span class="paient-casenum-title">{{ noHandleItem.label }}</span>
                    </div>
                    <template v-if="type===1&&(index===2 || index===3)"></template>
                    <div style="position: absolute;right: 2%;font-size: 24px;cursor: pointer" @click="jumpClick(noHandleItem)" v-else>
                      <i :class="noHandleItem.linkIcon"  ></i>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </el-col>
        </el-row>
        <div style="height: 10%"></div>
      </div>
    </div>
  </div>
</template>
<script>

import { getSummaryInfo, getSettleListUploadList, countSettleListModify } from '@/api/firstPage3.js'
import {
  formatCost as commonFormatCost
} from '@/utils/common'
import { queryDataIsuue } from '@/api/common/drgCommon'
import { queryOrderData as queryDipOrderData } from '@/api/newBusiness/newBusinessHos'
import { queryOrderData as queryDrgOrderData } from '@/api/newDrgBusiness/newDrgBusinessHos'
import moment from 'moment'
import { getInfo, modifySysUserInfo } from '@/api/login'
import { queryGroupType } from '@/api/common/sysCommon'
import { init } from '@/utils/globalInit'
import store from '@/store'

let errorData = [
  { label: '清单未标记数量', value: 0, icon: '', linkIcon: 'el-icon-position', jumpUrl: '/setlListManage/mdcsListManage', param: 'unmarked' },
  { label: '清单质控未通过数量', value: 0, icon: '', linkIcon: 'el-icon-position', jumpUrl: '/setlListManage/mdcsCheck', param: 'unpassed' },
  { label: '清单未上传数量', value: 0, icon: '', linkIcon: 'el-icon-position', jumpUrl: '/setlListManage/mdcsUpload', param: 'unupload' },
  { label: '清单上传失败数量', value: 0, icon: '', linkIcon: 'el-icon-position', jumpUrl: '/setlListManage/mdcsUpload', param: 'failed' },
  { label: '可优化病例数量', value: 0, icon: '', linkIcon: 'el-icon-position', jumpUrl: '/auliManage/grpOptm', param: 'adjust' },
  { label: '超高病例数量', value: 0, icon: '', linkIcon: 'el-icon-position', jumpUrl: '/oprelDecimmak/pattExtrAnalysis', param: 'high' },
  { label: '超低病例数量', value: 0, icon: '', linkIcon: 'el-icon-position', jumpUrl: '/oprelDecimmak/pattExtrAnalysis', param: 'low' }
]
export default {
  name: 'workTable',
  inject: ['reload'],
  data () {
    return {
      queryDate: '',
      // 是否是亏损排名
      isLoss: true,
      colors: ['#6eccfc', '#f79478', '#7f7cfb', '#fdbd3e'], // 颜色
      curTime: new Date(),
      params: {
        grperType: this.$somms.getGroupType(),
        hospitalId: ''
      },
      inGroupOption: {},
      inGroupSuccessRatio: 0,
      settleListUploadList: [
        { profttl: '清单上传情况', label: '已上传数量', totalUploadSuccessNum: 0, ratio: 0, color: '#7888fc' },
        { profttl: '清单标记情况', label: '已确认数量', confirmNum: 0, ratio: 0, color: '#6bcac2' },
        { profttl: '清单质控情况', label: '质控通过数量', passedValidateNum: 0, ratio: 0, color: '#f75d6fd8' }
      ],
      noHanldeSettleListData: errorData,
      costSickCaseData: [
        { label: '总费用', value: 0 }, { label: '预测总费用', value: 0 }, { label: '预测差异', value: 0 },
        { label: '入院病例总数', value: 0 }, { label: '出院病例总数', value: 0 }, { label: '结算病例总数', value: 0 }
      ],
      hasData: true,
      settleListModifyData: [],
      feeStas: '0',
      type: 2,
      inGroupData: [],
      timer: '', // 定时器
      lossOrProfitOrderOptions1: {},
      // 排名情况名称
      sortName: '',
      // 是否存在排名数据
      existsSortData: false,
      // 是否开启只查询医保数据
      enableAns: false
    }
  },
  beforeMount () {
    init()
  },
  mounted () {
    if (!this.params.grperType) {
      queryGroupType({ configKey: 'FZLX+FZLX' }).then(res => {
        if (res.data) {
          store.commit('setFzlx', res.data.value)
          this.params.grperType = res.data.value
          this.init()
        }
      })
    } else {
      this.init()
    }
  },
  created () {
    this.inGroupData = [
      { label: '入组数量', value: 0, icon: 'som-icon-comb', jumpUrl: this.$somms.isDIP() ? '/hosDipAnalysis/groupInAnalysis' : '/hosDrgAnalysis/groupInAnalysis', param: 'inGroup' },
      { label: '未入组数量', value: 0, icon: 'som-icon-ccomb', jumpUrl: this.$somms.isDIP() ? '/hosDipAnalysis/groupInAnalysis' : '/hosDrgAnalysis/groupInAnalysis', param: 'notInGroup' }
    ]
  },
  methods: {
    commonFormatCost,
    init () {
      this.querySummaryInfo()
      this.querySettleListUploadList()
      this.countSettleListModify()
      this.getOrderData()

      getInfo().then(res => {
        if (res.data.enableSeAns && res.data.enableSeAns === '1') {
          this.enableAns = true
        } else {
          this.enableAns = false
        }
      })
    },
    // 获取有数据的期号
    getDataIssue () {
      queryDataIsuue().then(response => {
        let time = this.formatDate(response.data.cy_start_date)
        this.curTime = time.substring(0, 7)
      })
    },
    // 获取参数
    getParams () {
      let params = {}
      Object.assign(params, this.params)
      params.ym = moment(this.curTime).format('YYYY-MM')
      params.type = this.type
      params.dataAuth = true
      // 是否为医保结算数据
      // params.enableSeAns=this.type===2?1:''
      return params
    },
    hasRightData () {
      this.hasData = true
      for (let i = 0; i < this.noHanldeSettleListData.length; i++) {
        if (this.noHanldeSettleListData[i].value != 0) {
          this.hasData = false
          break
        }
      }
    },
    // 改变期号
    fnTimeChange () {
      this.noHanldeSettleListData = errorData
      this.init()
    },
    /* 格式化日期 */
    formatDate (val) {
      return moment(val).format('YYYY-MM-DD')
    },
    /* 切换结算/出院 */
    radioChange (val) {
      this.params.type = this.type
      this.params.dateType = val
      this.init()
    },
    /* 统计前三天科室修改清单情况 */
    countSettleListModify () {
      let params = this.getParams()
      let curTime1 = new Date()
      params.expiDate = this.formatDate(curTime1)
      params.begnDate = this.formatDate(curTime1.setTime(curTime1.getTime() - 3600 * 1000 * 24 * 2))
      countSettleListModify(params).then(res => {
        if (res.code == 200) {
          this.settleListModifyData = res.data
        } else {
          this.$somms.message.error('调用异常')
        }
      })
    },
    /* 清单上传数据 病例入组情况 汇总 */
    querySettleListUploadList () {
      getSettleListUploadList(this.getParams()).then(res => {
        if (res.code == 200) {
          let tempList = res.data.settleListUploadList1
          let neededUploadNum = res.data.neededUploadNum
          let succCnt = res.data.totalUploadSuccessNum
          let confirmNum = res.data.confirmNum
          let passedNum = res.data.passedValidateNum
          let inGroupSuccessNum = res.data.inGroupSuccessNum
          let noGroupNum = res.data.noGroupNum

          this.inGroupData[0].value = inGroupSuccessNum
          this.inGroupData[1].value = noGroupNum
          this.settleListUploadList[0].value = succCnt
          this.settleListUploadList[1].value = confirmNum
          this.settleListUploadList[2].value = passedNum

          this.setSummaryVal(0, res.data.noConfirmNum)
          this.setSummaryVal(1, res.data.noPassedValidateNum)
          this.setSummaryVal(2, res.data.noUploadNum)
          this.setSummaryVal(3, res.data.uploadFailedNum)
          this.summaryOrder()
          if (neededUploadNum != 0) {
            this.settleListUploadList[0].ratio = ((succCnt / neededUploadNum) * 100).toFixed(2)
            this.settleListUploadList[1].ratio = ((confirmNum / neededUploadNum) * 100).toFixed(2)
            this.settleListUploadList[2].ratio = ((passedNum / neededUploadNum) * 100).toFixed(2)
            this.inGroupSuccessRatio = inGroupSuccessNum / (noGroupNum + inGroupSuccessNum)
          } else {
            this.settleListUploadList.map(item => {
              item.ratio = 0
            })
            this.inGroupSuccessRatio = 0
          }
          this.hasRightData()
          this.createCirclePie()
        } else {
          this.$somms.message.error('调用异常')
        }
      })
    },
    /* 超高 超低 可优化 出院 入院 结算 费用信息汇总 */
    querySummaryInfo () {
      getSummaryInfo(this.getParams()).then(res => {
        if (res.code == 200) {
          let adjustableAllCount = res.data.optimizableCount
          let ultraLowNum = res.data.ultraLowNum
          let ultrahighNum = res.data.ultrahighNum
          this.setSummaryVal(4, adjustableAllCount || 0)
          this.setSummaryVal(5, ultrahighNum)
          this.setSummaryVal(6, ultraLowNum)
          this.costSickCaseData[0].value = res.data.inHospitalTotalCost
          this.costSickCaseData[1].value = res.data.forecastAmount
          this.costSickCaseData[2].value = res.data.forecastAmountDiff
          this.costSickCaseData[3].value = res.data.inHospitalCount
          this.costSickCaseData[4].value = res.data.leaveHospitalCount
          this.costSickCaseData[5].value = res.data.payCount
          this.hasRightData()
        } else {
          this.$somms.message.error('调用异常')
        }
      })
    },
    setSummaryVal (idx, val) {
      if (val === 0) {
        this.noHanldeSettleListData[idx].icon = 'som-icon-success'
      } else {
        this.noHanldeSettleListData[idx].icon = 'som-icon-waring2'
      }
      this.noHanldeSettleListData[idx].value = val
    },
    summaryOrder () {
      // let arr = this.$somms.cloneObj(this.noHanldeSettleListData)
      // arr.sort((a, b) => b.value - a.value)
      // this.noHanldeSettleListData = arr
      // this.noHanldeSettleListData.sort((a, b) => b.value - a.value)
      // this.noHanldeSettleListData = this.noHanldeSettleListData.sort((a, b) => b.value - a.value)
    },
    // 环形图
    createCirclePie () {
      this.inGroupOption = {
        title: {
          text: (this.inGroupSuccessRatio ? (this.inGroupSuccessRatio * 100).toFixed(2) : 0) + '%',
          left: 'center',
          top: 'center',
          textStyle: {
            color: '#000',
            fontSize: 20
          }
        },
        tooltip: { // 提示框，可以在全局也可以在
          trigger: 'item', // 提示框的样式
          formatter: '{a} <br/>{b}: {c} ({d}%)',
          backgroundColor: 'rgba(0,0,0,0.8)',
          color: '#fff', // 提示框的背景色
          textStyle: { // 提示的字体样式
            color: 'white'
          }
        },
        legend: { // 图例
          orient: 'vertical', // 图例的布局，竖直    horizontal为水平
          x: 'left', // 图例显示在右边
          data: ['入组数量', '未入组数量'],
          textStyle: { // 图例文字的样式
            color: '#333', // 文字颜色
            fontSize: 12 // 文字大小
          }
        },
        series: [{
          name: '',
          type: 'pie', // 环形图的type和饼图相同
          radius: ['50%', '70%'], // 饼图的半径，第一个为内半径，第二个为外半径
          avoidLabelOverlap: false,
          color: ['#6eccfc', '#afafaf'],
          emphasis: { // 使用emphasis
            disable: false,
            scale: false, // 不缩放
            scaleSize: 0 // 为了防止失效直接设置未0
          },
          label: {
            normal: { // 正常的样式
              show: true,
              position: 'left'
            },
            emphasis: { // 选中时候的样式
              show: true,
              textStyle: {
                fontSize: '20',
                fontWeight: 'bold'
              }
            }
          },
          labelLine: {
            normal: {
              show: false
            }
          },
          data: [
            { value: this.inGroupData[0].value, name: '入组数量' },
            { value: this.inGroupData[1].value, name: '未入组数量' }
          ]
        }]
      }
    },
    // 清除定时
    clearTimer () {
      clearInterval(this.timer)
      this.timer = ''
    },
    // 获取排名option
    getLossOrProfitOrderOption (index) {
      return this['lossOrProfitOrderOptions' + index]
    },
    // 获取排序信息
    getOrderData () {
      // 清除数据
      // this.generateOrderOptions([])
      let params = this.getParams()
      params.sort = false
      params.isLoss = true
      params.analysisType = 'dept'
      params.feeStas = 0
      params.limit = 5
      params.begnDate = params.ym + '-01'
      params.expiDate = params.ym + '-31'
      params.dateType = this.type
      let method = queryDipOrderData
      if (this.$somms.isDRG()) {
        method = queryDrgOrderData
      }
      method(params).then(res => {
        if (res.data) {
          this.generateOrderOptions(res.data)
        }
      })
    },
    // 生成排序图
    generateOrderOptions (data) {
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            interval: 0,
            rotate: 40,
            formatter: params => {
              return this.formatCost(params, 0)
            }
          }
        },
        yAxis: {
          type: 'category',
          axisLabel: {
            formatter: params => {
              return params.length > 8 ? params.substr(0, 8) + '...' : params
            }
          },
          data: []
        },
        series: [
          {
            name: '科室',
            type: 'bar',
            data: [
              {
                code: '',
                url: ''
              }
            ]

          }
        ]
      }

      let names = []
      let dataList = []
      if (this.$somms.hasHosRole() || this.$somms.isDev()) {
        this.sortName = '科室'
        dataList.push(this.generateOrderData(data.deptList, 'deptName'))
      } else if (this.$somms.hasDeptRole()) {
        this.sortName = '医生'
        dataList.push(this.generateOrderData(data.doctorList, 'doctorName'))
      } else if (this.$somms.hasDoctorRole()) {
        this.sortName = '患者'
        dataList.push(this.generateOrderData(data.medList, 'name'))
      }
      names.push(this.sortName)

      for (let i = 0; i < names.length; i++) {
        let tempOption = this.$somms.cloneObj(option)
        tempOption.yAxis.data = dataList[i].yAxisData
        tempOption.series[0].data = dataList[i].seriesData
        tempOption.series[0].name = names[i]
        tempOption.color = this.colors[i]
        this.existsSortData = dataList[i].seriesData.length > 0
        this.$nextTick(() => {
          this['lossOrProfitOrderOptions' + (i + 1)] = tempOption
        })
      }
    },
    // 格式化
    formatCost (val, fixed = 2) {
      let prefix = ''
      let resVal = val
      if (String(val).includes('-')) {
        prefix = '-'
        val = Math.abs(val)
        resVal = val
      }
      if (Math.abs((parseFloat(val) / 10000)) > 1) {
        resVal = (parseFloat(val) / 10000).toFixed(fixed) + '/万'
      } else {
        resVal = val
      }
      return prefix + resVal
    },

    /* 克隆对象 */
    cloneObj (obj = {}) {
      if (typeof obj !== 'object') {
        return obj
      } else {
        let newObj = obj.constructor === Array ? [] : {}
        for (let i in obj) {
          newObj[i] = typeof obj[i] === 'object' ? this.cloneObj(obj[i]) : obj[i]
        }
        return newObj
      }
    },
    // 生成排序数据
    generateOrderData (data, fld) {
      let res = {}
      res.yAxisData = []
      res.seriesData = []
      if (data) {
        data.forEach(item => {
          res.yAxisData.push((item[fld] && item[fld] != undefined) ? item[fld] : '-')
          if (fld == 'deptName') {
            res.seriesData.push({
              value: item.diff,
              code: item.deptCode,
              url: '/hosDipAnalysisNew/deptAnalysisNew'
            })
          } else if (fld == 'doctorName') {
            res.seriesData.push({
              value: item.diff,
              code: item.drCodg,
              url: '/hosDipAnalysisNew/doctAnalysis'
            })
          } else if (fld == 'dipName') {
            res.seriesData.push({
              value: item.diff,
              code: item.dipCodg,
              url: '/hosDipAnalysisNew/disenalysis'
            })
          } else {
            res.seriesData.push({
              value: item.diff,
              code: item.patientId,
              url: '/hosDipAnalysis/predictPay'
            })
          }
        })
      }
      return res
    },
    // 获取排序信息
    /*  getOrderData() {
        // 清除数据
        //this.generateOrderOptions([])
        queryOrderData(this.getParams()).then(res => {
          if (res.data) {
            this.generateOrderOptions(res.data)
          }
        })
      }, */

    click (params) {
      this.$router.push({
        path: params.data.url,
        query: {
          ym: this.queryDate,
          code: params.data.code,
          begnDate: moment(this.params.begnDate).format('YYYY-MM-DD'),
          expiDate: moment(this.params.expiDate).format('YYYY-MM-DD'),
          radioMode: '3',
          isLoss: this.isLoss ? 1 : 0,
          feeStas: this.feeStas
        }
      })
    },
    // 选择亏损还是盈利排名
    switchLossOrProfit (isLoss) {
      this.clearTimer()
      this.isLoss = isLoss
      this.getOrderData()
    },
    // 根据期号返回开始日期和结束日期
    getStartTimeAndEndTime () {
      const endDay = ['31', '28', '31', '30', '31', '30', '31', '31', '30', '31', '30', '31']
      let curTime = moment(this.curTime).format('YYYY-MM-DD')
      let year = this.curTime.getFullYear()
      let curMonth = this.curTime.getMonth() + 1
      if (curMonth < 10) {
        curMonth = '0' + curMonth
      }
      if (year % 4 == 0 && year % 100 != 0) {
        endDay[1] = 29
      }
      let begnDate = year + '-' + curMonth + '-01'
      let expiDate = year + '-' + curMonth + '-' + endDay[curMonth - 1]
      return [begnDate, expiDate]
    },
    // 错误项跳转
    jumpClick (p) {
      let seTime = this.getStartTimeAndEndTime()
      let tempParams = {
        begnDate: this.type === 1 ? seTime[0] : '',
        expiDate: this.type === 1 ? seTime[1] : '',
        inHosFlag: this.type == 1 ? 1 : 3,
        ym: moment(this.curTime).format('YYYY-MM'),
        group: this.$somms.getGroupType(),
        costSection1: p.param === 'high' ? '1' : (p.param === 'low' ? '2' : ''),
        isInGroup1: p.param === 'inGroup' ? '1' : (p.param === 'notInGroup' ? '2' : ''),
        tabName1: p.param === 'failed' ? '上传失败' : (p.param === 'unupload' ? '未上传' : ''),
        lookOver: p.param === 'unmarked' ? 0 : ''
      }
      if (this.type == 2) {
        tempParams.begnDate = ''
        tempParams.expiDate = ''
        tempParams.seStartTime = seTime[0]
        tempParams.seEndTime = seTime[1]
      } else if (this.type == 1) {
        tempParams.seStartTime = ''
        tempParams.seEndTime = ''
      }
      switch (p.param) {
        case 'adjust':
          this.linkPage(p.jumpUrl, tempParams)
        case 'failed':
          tempParams.begnDate = seTime[0]
          tempParams.expiDate = seTime[1]
          this.linkPage(p.jumpUrl, tempParams)
        case 'unpassed':
          this.linkPage(p.jumpUrl, tempParams)
        case 'unmarked':
          tempParams.showAnalysis = '1'
          this.linkPage(p.jumpUrl, tempParams)
        case 'unupload':
          tempParams.begnDate = seTime[0]
          tempParams.expiDate = seTime[1]
          this.linkPage(p.jumpUrl, tempParams)
        case 'low':
          this.linkPage(p.jumpUrl, tempParams)
        case 'high':
          this.linkPage(p.jumpUrl, tempParams)
        case 'inGroup':
        case 'notInGroup':
          this.linkPage(p.jumpUrl, tempParams)
      }
    },
    linkPage (url, param) {
      this.goto(url, param)
    },
    enableSeAnsChange () {
      modifySysUserInfo({ activeFlag: (this.enableAns ? '1' : '0') }).then(res => {
        if (res.code === 200) {
          this.$message.success(this.enableAns ? '开启成功' : '取消成功')
          this.reload()
        }
      })
    }
  }
}
</script>
<style scoped>
.first-page-title {
  padding-bottom: 1.8rem !important;
}

.el-card {
  color: none;
}

.fp-container {
  height: 100%;
  overflow-y: auto;
  margin-bottom: 10%;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.fp-container::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

.base-row {
  height: 100%;
  width: 100%;
  margin-top: 5px;
}

.middle,
.right {
  height: 100%;
}

.middle {
  margin-right: 1rem;
}

/* 右侧 */
.predict-box {
  margin-bottom: 1rem;
  width: 100%;
  height: 10rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 20px;
  border-radius: 4px;
  align-items: center;
  background-color: #ebf5f9;
  /* border: 1px solid rgb(110, 204, 252); */
}

.predict-feedback-item {
  display: flex;
  flex-direction: column;
  width: 33.3%;
  color: #000;
}

.base-col-title {
  padding: 0.6rem 0 0rem 0.6rem;
  margin: 0;
  color: #000;
}

.big-val {
  font-size: 1.3rem;
}

.aside {
  display: flex;
  flex-direction: row;
  margin-bottom: 1rem;
  margin-top: 1rem;
  height: 30%;
}

.aside-left,
.aside-right {
  display: flex;
  flex-direction: column;
}

.aside-left {
  width: 32.4%;
}

.aside-right {
  width: 66.2%;
  margin-left: 1.1rem;
}

.el-row {
  margin-bottom: 20px;
}
.base-col-icon {
  /* font-size: var(--biggerSize); */
  font-size: 26px;
}

.paient-casenum-card {
  padding: 16px !important;
  /* margin-top:1.5rem; */
  color: none !important;
  display: flex;
  flex-direction: row;
  width: 100%;
  margin-bottom: 2%;
}

.paient-casenum-box {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.grid-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.paient-casenum-item {
  color: #212529;
  margin: 0px;
  font-size: calc(1.275rem + 0.3vw) !important;
  line-height: calc(1.275rem + 0.3vw) !important;
}

.paient-casenum {
  padding-left: 1rem !important
}

.paient-casenum-title {
  color: #9a9b9d !important;
  font-size: 14px !important;
}

.settle-list-row {
  display: flex;
  flex-direction: row;
  width: 100%;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
}

.el-card-body {
  padding:1rem;
}

.el-card-header h6 {
  font-size: 14px;
}

.settle-list-row-item {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 0 10px 10px 0;
  /*width: 50%;
  padding: 1rem 1rem;*/
  width: 50%;
  padding: 0.5rem 0.5rem;
}

.settle-list-row-item i {
  font-size: calc(1.3rem + 0.6vw) !important;
  line-height: calc(1.3rem + 0.6vw) !important;
}

/* 重构页面样式 */
.settle-list-upload-warp {
  width: 100%;
  height: 10rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.settle-list-upload-item {
  overflow: none;
  background-color: #fff;
  heigth: 100%;
  width: 33.3%;
  margin-right: 1rem;
}

.settle-list-upload-item:last-child {
  margin-right: 0px;
}

.settle-list-upload-content {
  margin: 20px;
}

.settle-list-upload-content-title {
  font-size: 18px;
  color: #5b5b5b;
  margin-bottom: 20px;
  margin-top: 10px!important;
}

.settle-list-upload-content-value {
  font-size: 28px;
  float: right;
  margin: 0px 0px 7px;
}

.uploaded-state-title {
  color: #5B5B5B;
  margin: 0px 0px 14px;
  font-size: 14px;
}

.has-data-remind {
  font-size: 2.5rem;
  padding: 1.6rem 0px;
}
.fp-warning-box {
  height: 82.5%;
}
.timeline-content{
    padding:0px;
     border:none;
  }
  /*.timeline-item{*/
  /*  padding-bottom: 1%;*/
  /*  margin: 0.3%;*/
  /*}*/
/*/deep/ .el-card__body{*/
/*  padding: 20px!important;*/
/*}*/
</style>
