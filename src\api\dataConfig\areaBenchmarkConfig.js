import request from '@/utils/request'

/**
 * 区域标杆信息查询
 * @param params
 * @returns {*}
 */
export function queryAreaBenchmarkInfo (params) {
  return request({
    url: '/AreaBenchmarkConfigController/queryAreaBenchmarkInfo',
    method: 'post',
    params: params
  })
}

/**
 * 区域标杆删除
 * @param params
 * @returns {*}
 */
export function deleteAreaBenchmarkInfo (params) {
  return request({
    url: '/AreaBenchmarkConfigController/deleteAreaBenchmarkInfo',
    method: 'post',
    params: params
  })
}

/**
 * 区域标杆修改
 * @param params
 * @returns {*}
 */
export function updateAreaBenchmarkInfo (params) {
  return request({
    url: '/AreaBenchmarkConfigController/updateAreaBenchmarkInfo',
    method: 'post',
    params: params
  })
}

/**
 * 区域标杆新增
 * @param params
 * @returns {*}
 */
export function insertAreaBenchmarkInfo (params) {
  return request({
    url: '/AreaBenchmarkConfigController/insertAreaBenchmarkInfo',
    method: 'post',
    params: params
  })
}

/**
 * 上传区域标杆文件
 * @param params
 * @param group
 * @returns {*}
 */
export function areaBenchmarkUpload (params) {
  return request({
    url: '/AreaBenchmarkConfigController/areaBenchmarkUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}
/**
 * 下载科室模板
 * @param params
 * @returns {*}
 */
export function downloadAreaBenchmarkTemplate (params) {
  return request({
    url: '/AreaBenchmarkConfigController/downloadAreaBenchmarkTemplate',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}
