<template>
  <el-table ref="elTable"
            :id="id"
            height="100%"
            stripe
            :header-cell-style="{'text-align':'center'}"
            :data="data"
            :summary-method="getSummaries"
            show-summary
            v-loading="loading"
            border>
    <el-table-column label="序号" type="index" width="50" fixed align="center"></el-table-column>
    <el-table-column label="病种编码" prop="icdCodg" width="200" :show-overflow-tooltip="true" fixed align="left"></el-table-column>
    <el-table-column label="病种名称" prop="icdName" width="200" :show-overflow-tooltip="true" fixedalign="left"></el-table-column>
    <el-table-column label="入组病案数" prop="drgInGroupMedcasVal" width="125" align="center" :fixed="include('inGroupNum')">
      <template slot-scope="scope">
        <div :class="scope.row.drgInGroupMedcasVal == 0 ? '' : 'skip'"
             @click="scope.row.drgInGroupMedcasVal == 0 ? '' : queryGroupPatient(scope.row)">
          {{ scope.row.drgInGroupMedcasVal }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="总费用" prop="sumfee" width="130" align="right" :fixed="include('totalCost')"></el-table-column>
    <!--    <template slot-scope="scope">-->
    <!--      <div :class="scope.row.drgInGroupMedcasVal == 0 ? '' : 'skip'" @click="scope.row.drgInGroupMedcasVal == 0 ? '' : queryGroupPatient(scope.row)">-->
    <!--        {{ scope.row.drgInGroupMedcasVal }}-->
    <!--      </div>-->
    <!--    </template>-->
    <el-table-column :label="this.$somms.getFCOrFBName(this.$store.getters.feeStas,0)" align="right" prop="forecastAmount" width="130"
                     :fixed="include('forecastAmount')"></el-table-column>
    <el-table-column :label="this.$somms.getFCOrFBName(this.$store.getters.feeStas,1)" prop="forecastAmountDiff"
                     width="130" align="right" :fixed="include('forecastAmountDiff')"></el-table-column>
    <el-table-column label="O/E值" prop="oeVal" width="80" align="center" :fixed="include('oeVal')"></el-table-column>
    <el-table-column label="悬浮" width="75" align="center">
      <template slot-scope="scope">
        <el-button size="mini" type="primary" @click="showSuspension(scope.row)">悬浮</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'newDrgIlnessForecastTable',
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    },
    // 表格id
    id: {
      type: String
    },
    queryForm: {
      type: Object
    },
    columnOptions: {
      type: Array,
      default: () => []
    }
  },
  updated () {
    this.$nextTick(() => {
      this.$refs.elTable.doLayout()
    })
  },
  methods: {
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length
      }
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计'
          return
        }
        const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value))
        if (index === 3 || index === 4 || index === 5 || index === 6) {
          sums[index] = calculations.sum(values).toFixed(2)
        } else if (index === 7) {
          sums[index] = calculations.average(values).toFixed(2)
        } else {
          sums[index] = ' '
        }
      })
      return sums
    },
    include (column) {
      return this.fixedColumns.includes(column)
    },
    queryGroupPatient (item) {
      this.$router.push({
        path: '/hosDrgAnalysisNew/pattAnalysis',
        query: {
          drgCodg: this.queryForm.drgCodg,
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          icdCodg: item.icdCodg,
          deptCode: this.queryForm.deptCode,
          isInGroup: 1,
          feeStas: this.queryForm.feeStas,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    // 显示悬浮
    showSuspension (scope) {
      this.scopeData = scope
      let scopeList = []
      for (let i in this.scopeData) {
        let o = {}
        o.key = i
        o.value = this.scopeData[i]
        scopeList.push(o)
      }
      let res = []
      if (scopeList.length) {
        for (let i = 0; i < scopeList.length; i++) {
          for (let j = 0; j < this.columnOptions.length; j++) {
            if (scopeList[i].key == this.columnOptions[j].value) {
              res.push({
                key: scopeList[i].key,
                label: this.columnOptions[j].label,
                value: scopeList[i].value,
                type: 2,
                show: true
              })
            }
          }
        }
      }
      if (scope) {
        this.$emit('showSuspension', res)
      }
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    }
  }
}
</script>

<style scoped>

</style>
