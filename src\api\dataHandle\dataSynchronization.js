import request from '@/utils/request'

export function QueryData (params) {
  return request({
    url: '/dataSynchronizationController/queryData',
    method: 'post',
    data: params
  })
}

export function QueryResident (params) {
  return request({
    url: '/dataSynchronizationController/queryResident',
    method: 'post',
    data: params
  })
}

export function QuerySyncName () {
  return request({
    url: '/dataSynchronizationController/querySyncName',
    method: 'post'
  })
}

export function ManualSyncData (params) {
  return request({
    url: '/dataSynchronizationController/manualSyncData',
    method: 'post',
    data: params
  })
}

// 同步数据
export function syncData (params) {
  return request({
    url: '/dataSynchronizationController/sync',
    method: 'post',
    data: params
  })
}

// 同步所有数据
export function syncAllData (params) {
  return request({
    url: '/dataSynchronizationController/syncAll',
    method: 'post',
    data: params
  })
}

// 差量同步
export function diffSyncData (params) {
  return request({
    url: '/dataSynchronizationController/diffSync',
    method: 'post',
    data: params
  })
}

// 查询同步数据
export function querySyncData (params) {
  return request({
    url: '/dataSynchronizationController/querySyncData',
    method: 'post',
    data: params
  })
}

// 文件上传需同步数据
export function uploadData (params) {
  return request({
    url: '/dataSynchronizationController/uploadData',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}
