import request from '@/utils/request'

export function generateMSPCost (params) {
  return request({
    url: '/hospitalCostDeptCostDetailAnalysisController/generateMSPCost',
    method: 'post',
    params: params
  })
}

export function selectPatientTotalCost (params) {
  return request({
    url: '/hospitalCostDeptCostDetailAnalysisController/selectPatientTotalCost',
    method: 'post',
    params: params
  })
}

export function selectMSPCostData (params) {
  return request({
    url: '/hospitalCostDeptCostDetailAnalysisController/selectMSPCostData',
    method: 'post',
    params: params
  })
}

export function selectHospitalYearData (params) {
  return request({
    url: '/hospitalCostDeptCostDetailAnalysisController/selectHospitalYearData',
    method: 'post',
    params: params
  })
}

export function selectDeptUnitTopData (params) {
  return request({
    url: '/hospitalCostDeptCostDetailAnalysisController/selectDeptUnitTopData',
    method: 'post',
    params: params
  })
}
