.som-align-center{
  text-align: center;
}
.som-el-form-item-margin-left {
  margin-left: 2rem;
}
.som-button-margin-right {
 margin-right: 1rem;
}
.som-form-item {
  width: 90%!important;
}
.som-wd-one-hundred{
  width: 100%;
  height: 100%;
}
.som-w-one-hundred{
  width: 100%;
}
.som-h-one-hundred{
  height: 100%;
}
.som-w-fifty{
  width: 50%;
}
.som-h-fifty{
  height: 50%;
}
.som-margin-left{
  margin-left: 0.8rem;
}
.som-department-height{
  height: 27px;
}
.som-table-height{
  height: 100%;
}
.som-form-extend-form-item{
  width: 100%;
}
.som-tab-pane{
  height: 100%;
}

.som-color-error{
  color: rgb(246,114,114);
}

.som-color-success{
  color: rgb(111,196,68);
}

.som-color-warning{
    color: rgb(231,166,70);
}

.som-flex-center{
  display: flex;
  justify-content: center;
  align-items: center;
}

// icon
[class*=' som-icon-'], [class^=som-icon-]{
  font-family: element-icons!important;
  speak: none;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: baseline;
  display: inline-block;
  width: 1rem;
  height: 1rem;
  background-repeat: no-repeat;
  background-size: 100%;
  -webkit-font-smoothing: antialiased;
}
[class^=som-icon-]:hover{
  height: 1.3rem;
  width: 1.3rem;
}

.som-iconTool{
  cursor: pointer;
  position: relative;
  top: 10px;
}
.som-icon-big{
  height: 1.3rem;
  width: 1.3rem;
}

.som-icon-analysis{
  background-image: url("../assets/images/icon/Analysis.png");
}

.som-icon-profit{
  background-image: url("../assets/images/icon/Profit.png");
}

.som-icon-loss{
  background-image: url("../assets/images/icon/Loss.png");
}

.som-icon-compare{
  background-image: url("../assets/images/icon/Compare.png");
}

.som-icon-splashes{
  background-image: url("../assets/images/icon/Splashes.png");
}

.som-icon-table{
  background-image: url("../assets/images/icon/Table.png");
}

.som-icon-pie{
  background-image: url("../assets/images/icon/Pie.png");
}

.som-icon-card{
  background-image: url("../assets/images/icon/Card.png");
}

.som-icon-error-waring{
  background-image: url("../assets/images/icon/ErrorWarning.png");
}

.som-icon-waring{
  background-image: url("../assets/images/icon/Warning.png");
}

.som-icon-waring2{
  background-image: url("../assets/images/icon/Warn2.png");
}

.som-icon-comb{
  background-image: url("../assets/images/icon/Comb.png");
}

.som-icon-ccomb{
  background-image: url("../assets/images/icon/CComb.png");
}

.som-icon-success{
  background-image: url("../assets/images/icon/Success.png");
}

.som-icon-high{
  background-image: url("../assets/images/icon/High.png");
}

.som-icon-high-white{
  background-image: url("../assets/images/icon/HighWhite.png");
}

.som-icon-api {
  background-image: url("../assets/images/icon/Api.png");
}

.som-icon-low{
  background-image: url("../assets/images/icon/Low.png");
}

.som-icon-low-white{
  background-image: url("../assets/images/icon/LowWhite.png");
}

.som-icon-message{
  background-image: url("../assets/images/icon/message.png");
}

.som-icon-total-cost{
  background-image: url("../assets/images/icon/TotalCost.png");
}

.som-icon-money-forecast{
  background-image: url("../assets/images/icon/MoneyForecast.png");
}

.som-icon-money-balance{
  background-image: url("../assets/images/icon/MoneyBalance.png");
}

.som-icon-sort{
  background-image: url("../assets/images/icon/Sort.png");
}

.som-icon-time{
  background-image: url("../assets/images/icon/Time.png");
}

.som-icon-close-time{
  background-image: url("../assets/images/icon/CloseTime.png");
}
.som-icon-logout{
  background-image: url("../assets/images/icon/Logout.png");
}
.som-icon-home{
  background-image: url("../assets/images/icon/Home.png");
}
.som-icon-modify{
  background-image: url("../assets/images/icon/Modify.png");
}
.som-icon-tip{
  background-image: url("../assets/images/icon/Tip.png");
}
.som-icon-add{
  background-image: url("../assets/images/icon/Add.png");
}
.som-icon-remove{
  background-image: url("../assets/images/icon/Remove.png");
}
.som-icon-refresh{
  background-image: url("../assets/images/icon/Refresh.png");
}

.som-icon-about{
  background-image: url("../assets/images/icon/About.png");
}

.som-icon-tip:hover{
  background-image: url("../assets/images/icon/Tip_Blue.png");
}

.som-icon-CloseEye {
  //pointer-events: none;
  height: 1.3rem;
  width: 1.3rem;
  background-image: url("../assets/images/icon/CloseEye.png");
}


// 容器
.som-comps-container{
  height: 100%;
  width: 100%;
  position: relative
}

// 覆盖
.el-card__body{
  height: 100%!important;
  padding: 10px 15px 0px 10px!important;
}

.el-form-item__label {
  float: left;
  font-size: var(--textSize)!important;
}

// elment-ui 固定列 高度对不齐问题解决方法
.el-table__header {
  height: 100% !important;
}

// 解决tooltip超长导致页面一直闪
.el-tooltip__popper, .el-tooltip__popper.is-dark{
  max-height: 90%!important;
}

