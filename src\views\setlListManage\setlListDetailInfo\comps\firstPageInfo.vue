<template>
  <div style="width: 100%;height: 100%" :model="value">
    <div style="display: flex">
      <div style="padding-left: 45%">医疗机构 {{value.somHiInvyBasInfo.a02}} </div>
      <div style="padding-left: 10%">（组织机构代码：{{value.somHiInvyBasInfo.a01}}）</div>
    </div>
    <div style="text-align: center;font-size: 30px;letter-spacing:30px">
      住院病案首页
    </div>
    <div style="padding-top: 5px">
      <div style="width: 25%;display: flex;">
        <div >医疗付款方式：</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a46c" class="numInputStyle">
      </div>
    </div>
    <div style="display: flex;padding-top: 5px">
      <div style="width: 47%" :style="{color:checkData(value.somHiInvyBasInfo.a47)}">健康卡号：{{value.somHiInvyBasInfo.a47}}</div>
      <div style="width: 37%">第 {{value.somHiInvyBasInfo.a49}} 次住院</div>
      <div style="width: 17%" :style="{color:checkData(value.somHiInvyBasInfo.a48)}" >病案号：{{value.somHiInvyBasInfo.a48}}</div>
    </div>
    <div style="border: 1px black solid;height: 100%;width: 100%">
      <div class="divMargin">
        <div style="width: 20%;display: flex;">
          <div :style="{color:checkData(value.somHiInvyBasInfo.a11)}" class="fontWeight">姓名</div>
          <input readOnly type="text" style="width: 50%" v-model="value.somHiInvyBasInfo.a11" class="inputStyle">
        </div>
        <div style="width: 20%;display: flex;">
          <div :style="{color:checkData(value.somHiInvyBasInfo.a12c)}" class="fontWeight" >性别:</div>
          <input readOnly type="text" v-model="value.somHiInvyBasInfo.a12c" class="numInputStyle">
          <div  style="padding-left: 10px">1、男</div>
          <div style="padding-left: 10px">2、女</div>
        </div>
        <div style="width: 20%;display: flex;">
          <div :style="{color:checkData(value.somHiInvyBasInfo.a13)}" class="fontWeight" >出生日期</div>
          <input readOnly type="text" style="width: 50%" v-model="value.somHiInvyBasInfo.a13" class="inputStyle">
          <!--        <div>年</div>-->
          <!--        <input readOnly type="text" style="width: 5%" v-model="value.somHiInvyBasInfo.name" class="inputStyle">-->
          <!--        <div>月</div>-->
          <!--        <input readOnly type="text" style="width: 5%" v-model="value.somHiInvyBasInfo.name" class="inputStyle">-->
          <!--        <div>日</div>-->
        </div>
        <div style="width: 20%;display: flex;">
          <div :style="{color:checkData(value.somHiInvyBasInfo.a14)}" class="fontWeight" >年龄</div>
          <input readOnly type="text" style="width: 20%"  v-model="value.somHiInvyBasInfo.a14" class="inputStyle">
          <div>岁</div>
        </div>
        <div style="width: 20%;display: flex;">
          <div :style="{color:checkData(value.somHiInvyBasInfo.a15c)}" class="fontWeight" >国籍</div>
          <input readOnly type="text" style="width: 40%" v-model="value.somHiInvyBasInfo.a15c" class="inputStyle">
        </div>
      </div>
      <div class="divMargin">
        <div>(年龄不足1周岁的)</div>
        <div class="fontWeight" style="padding-left: 1%" :style="{color:checkData(value.somHiInvyBasInfo.a16)}">年龄</div>
        <input readOnly type="text" style="width: 5%" v-model="value.somHiInvyBasInfo.a16" class="inputStyle">
        <div>天</div>
        <div class="fontWeight" style="padding-left: 12.5%" :style="{color:checkData(value.somHiInvyBasInfo.a18)}">新生儿出生体重</div>
        <input readOnly type="text" style="width: 10%" v-model="value.somHiInvyBasInfo.a18" class="inputStyle">
        <div>克</div>
        <div class="fontWeight" style="padding-left: 16%" :style="{color:checkData(value.somHiInvyBasInfo.a17)}">新生儿入院体重</div>
        <input readOnly type="text"  style="width: 12%" v-model="value.somHiInvyBasInfo.a17" class="inputStyle">
        <div>克</div>
      </div>
      <div class="divMargin">
        <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.a22)}">出生地</div>
        <input readOnly type="text" style="width: 15%" v-model="value.somHiInvyBasInfo.a22" class="inputStyle">
        <div class="fontWeight" style="padding-left: 8.7%" :style="{color:checkData(value.somHiInvyBasInfo.a23c)}">籍贯</div>
        <input readOnly type="text" style="width: 15%" v-model="value.somHiInvyBasInfo.a23c" class="inputStyle">
        <div class="fontWeight" style="padding-left: 14.5%" :style="{color:checkData(value.somHiInvyBasInfo.a19c)}">民族</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a19c" style="width: 20.5%" class="inputStyle">
      </div>
      <div class="divMargin">
        <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.a20)}">身份证号</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a20" style="width: 26.5%;" class="inputStyle">
        <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.a38c)}">职业</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a38c" style="width: 13%" class="inputStyle">
        <div class="fontWeight" style="padding-left: 10%" :style="{color:checkData(value.somHiInvyBasInfo.a21c)}">婚姻:</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a21c" class="numInputStyle">
        <div style="display: flex">
          <div style="padding-left: 10px">1、未婚</div>
          <div style="padding-left: 10px">2、已婚</div>
          <div style="padding-left: 10px">3、丧偶</div>
          <div style="padding-left: 10px">4、离婚</div>
          <div style="padding-left: 10px">9、其他</div>
        </div>
      </div>
      <div class="divMargin">
        <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.a26)}">现住址</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a26" style="width: 58%" class="inputStyle">
        <div  class="fontWeight"  :style="{color:checkData(value.somHiInvyBasInfo.a35)}" >电话</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a35" style="width: 15%" class="inputStyle">
        <div  class="fontWeight" style="padding-left: 5%" >邮编</div>
        <input readOnly type="text"  style="width: 9%" class="inputStyle">
      </div>
      <div class="divMargin">
        <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.a24)}">户口地址</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a24" style="width: 79.5%" class="inputStyle">
        <div  class="fontWeight"  :style="{color:checkData(value.somHiInvyBasInfo.a25c)}">邮编</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a25c"  style="width: 9%" class="inputStyle">
      </div>
      <div class="divMargin">
        <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.a29)}">工作单位及地址</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a29" style="width: 51.5%" class="inputStyle">
        <div  class="fontWeight"  :style="{color:checkData(value.somHiInvyBasInfo.a30)}">单位电话</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a30" style="width: 17%" class="inputStyle">
        <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.a31c)}">邮编</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a31c" style="width: 9%" class="inputStyle">
      </div>
      <div class="divMargin">
        <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.a32)}">联系人姓名</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a32" style="width: 10%" class="inputStyle">
        <div  class="fontWeight" style="padding-left: 5%" :style="{color:checkData(value.somHiInvyBasInfo.a33c)}">关系</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a33c" style="width: 10%" class="inputStyle">
        <div  class="fontWeight" style="padding-left: 5%" :style="{color:checkData(value.somHiInvyBasInfo.a34)}">地址</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a34" style="width: 35%" class="inputStyle">
        <div  class="fontWeight" style="padding-left: 2.5%" :style="{color:checkData(value.somHiInvyBasInfo.a35)}">电话</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.a35" style="width: 12%" class="inputStyle">
      </div>
      <div class="divMargin">
        <div  class="fontWeight" >入院途径:</div>
        <input readOnly type="text" v-model="value.somHiInvyBasInfo.b11c" class="numInputStyle">
        <div style="display: flex">
          <div style="padding-left: 20px">1、急诊</div>
          <div style="padding-left: 20px">2、门诊 </div>
          <div style="padding-left: 20px">3、其他医疗机构转入</div>
          <div style="padding-left: 20px">9、其他</div>
        </div>
      </div>
      <!--          <div class="divMargin">-->
      <!--            <div  class="fontWeight">治疗类别:</div>-->
      <!--            <input readOnly type="text" v-model="form.name" class="numInputStyle">-->
      <!--            <div style="display: flex">-->
      <!--              <div>1、中医（1.1 中医  1.2民族医） </div>-->
      <!--              <div> 2、中西医  </div>-->
      <!--              <div>3、西医   </div>-->
      <!--            </div>-->
      <!--          </div>-->
      <div class="divMargin">
        <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.b12)}">入院时间</div>
        <input readOnly type="text" style="width: 25%" v-model="value.somHiInvyBasInfo.b12" class="inputStyle">
<!--        <div class="fontWeight">年</div>-->
<!--        <input readOnly type="text" style="width: 5%" v-model="value.somHiInvyBasInfo.b12.substring(5,7)" class="inputStyle">-->
<!--        <div class="fontWeight">月</div>-->
<!--        <input readOnly type="text" style="width: 5%" v-model="value.somHiInvyBasInfo.b12.substring(8,10)" class="inputStyle">-->
<!--        <div class="fontWeight">日</div>-->
<!--        <input readOnly type="text"  style="width: 5%" v-model="value.somHiInvyBasInfo.b12.substring(11,13)" class="inputStyle">-->
<!--        <div class="fontWeight">时</div>-->
<!--        <input readOnly type="text" style="width: 5%" v-model="form.name" class="inputStyle">-->
        <div class="fontWeight" style="padding-left: 3%;" :style="{color:checkData(value.somHiInvyBasInfo.b13n)}">入院科别</div>
        <input readOnly type="text" style="width: 20%" v-model="value.somHiInvyBasInfo.b13n" class="inputStyle">
        <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.b14n)}">病房</div>
        <input readOnly type="text" style="width: 13.5%" v-model="value.somHiInvyBasInfo.b14n" class="inputStyle">
        <div class="fontWeight" style="padding-left: 4%;" :style="{color:checkData(value.somHiInvyBasInfo.b21c)}" >转科科别</div>
        <input readOnly type="text" style="width: 15%" v-model="value.somHiInvyBasInfo.b21c" class="inputStyle">
      </div>
      <div class="divMargin">
        <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.b15)}">出院时间</div>
        <input readOnly type="text" style="width: 25%" v-model="value.somHiInvyBasInfo.b15" class="inputStyle">
<!--        <div class="fontWeight">年</div>-->
<!--        <input readOnly type="text" style="width: 5%" v-model="form.name" class="inputStyle">-->
<!--        <div class="fontWeight">月</div>-->
<!--        <input readOnly type="text" style="width: 5%" v-model="form.name" class="inputStyle">-->
<!--        <div class="fontWeight">日</div>-->
<!--        <input readOnly type="text" style="width: 5%" v-model="form.name" class="inputStyle">-->
<!--        <div class="fontWeight">时</div>-->
<!--        <input readOnly type="text" style="width: 5%" v-model="form.name" class="inputStyle">-->
        <div class="fontWeight" style="padding-left: 3%;" :style="{color:checkData(value.somHiInvyBasInfo.b16n)}">出院科别</div>
        <input readOnly type="text" style="width: 20%" v-model="value.somHiInvyBasInfo.b16n" class="inputStyle">
        <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.b17n)}">病房</div>
        <input readOnly type="text" style="width: 13.5%" v-model="value.somHiInvyBasInfo.b17n" class="inputStyle">
        <div class="fontWeight" style="padding-left: 4%;" :style="{color:checkData(value.somHiInvyBasInfo.b20)}">实际住院</div>
        <input readOnly type="text" style="width: 13%" v-model="value.somHiInvyBasInfo.b20" class="inputStyle">
        <div class="fontWeight">天</div>
      </div>
      <div class="divMargin">
        <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.c36n)}">门（急）诊诊断（中医诊断）:</div>
        <input readOnly type="text" style="width: 42%" v-model="value.somHiInvyBasInfo.c36n" class="inputStyle">
        <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.c35c)}">疾病编码:</div>
        <input readOnly type="text" style="width: 29%" v-model="value.somHiInvyBasInfo.c35c" class="inputStyle">
      </div>
      <div class="divMargin">
        <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.c02n)}">门（急）诊诊断（西医诊断）:</div>
        <input readOnly type="text" style="width: 42%" v-model="value.somHiInvyBasInfo.c02n" class="inputStyle">
        <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.c01c)}">疾病编码:</div>
        <input readOnly type="text" style="width: 29%" v-model="value.somHiInvyBasInfo.c01c" class="inputStyle">
      </div>
      <!--          <div class="divMargin">-->
      <!--            <div  class="fontWeight">实施临床路径:</div>-->
      <!--            <input readOnly type="text" v-model="form.name" class="numInputStyle">-->
      <!--            <div style="display: flex">-->
      <!--              <div>1、中医 </div>-->
      <!--              <div>2、西医 </div>-->
      <!--              <div>3、否 </div>-->
      <!--            </div>-->
      <!--            <div  class="fontWeight">使用医疗机构中药制剂:</div>-->
      <!--            <input readOnly type="text" v-model="form.name" class="numInputStyle">-->
      <!--            <div style="display: flex">-->
      <!--              <div>1、是 </div>-->
      <!--              <div>2、否 </div>-->
      <!--            </div>-->
      <!--          </div>-->
      <!--          <div class="divMargin">-->
      <!--            <div  class="fontWeight">使用中医诊疗设备:</div>-->
      <!--            <input readOnly type="text" v-model="form.name" class="numInputStyle">-->
      <!--            <div style="display: flex">-->
      <!--              <div>1、是 </div>-->
      <!--              <div>2、否 </div>-->
      <!--            </div>-->
      <!--            <div  class="fontWeight">使用中医诊疗技术:</div>-->
      <!--            <input readOnly type="text" v-model="form.name" class="numInputStyle">-->
      <!--            <div style="display: flex">-->
      <!--              <div>1、是 </div>-->
      <!--              <div>2、否 </div>-->
      <!--            </div>-->
      <!--            <div  class="fontWeight">辩证施护:</div>-->
      <!--            <input readOnly type="text" v-model="form.name" class="numInputStyle">-->
      <!--            <div style="display: flex">-->
      <!--              <div>1、是 </div>-->
      <!--              <div>2、否 </div>-->
      <!--            </div>-->
      <!--          </div>-->
      <div>
        <table style="border: 1px black solid;border-collapse: collapse;width: 100%;border-width: 0 0 1px 0">
          <tr >
            <th style="width: 31%">出院诊断</th>
            <th style="width: 15%">疾病编码</th>
            <th style="width: 4%">入院病情</th>
            <th style="width: 31%">出院诊断</th>
            <th style="width: 15%">疾病编码</th>
            <th style="width: 4%">入院病情</th>
          </tr>
          <tr >
            <td>{{value.DiseaseDiagnosisTrimListBa[0].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[0].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[0].c08c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[1].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[1].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[1].c08c1}}</td>
          </tr>
          <tr >
            <td>{{value.DiseaseDiagnosisTrimListBa[2].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[2].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[2].c08c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[3].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[3].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[3].c08c1}}</td>
          </tr>
          <tr >
            <td>{{value.DiseaseDiagnosisTrimListBa[4].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[4].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[4].c08c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[5].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[5].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[5].c08c1}}</td>
          </tr>
          <tr >
            <td>{{value.DiseaseDiagnosisTrimListBa[6].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[6].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[6].c08c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[7].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[7].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[7].c08c1}}</td>
          </tr>
          <tr >
            <td>{{value.DiseaseDiagnosisTrimListBa[8].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[8].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[8].c08c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[9].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[9].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[9].c08c1}}</td>
          </tr>
          <tr >
            <td>{{value.DiseaseDiagnosisTrimListBa[10].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[10].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[10].c08c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[11].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[11].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[11].c08c1}}</td>
          </tr>
          <tr>
            <td>{{value.DiseaseDiagnosisTrimListBa[12].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[12].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[12].c08c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[13].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[13].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[13].c08c1}}</td>
          </tr>
          <tr >
            <td colspan="3">入院病情：1、有 2、临床未确定 3、情况不妙 4、无</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[14].c07n1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[14].c06c1}}</td>
            <td>{{value.DiseaseDiagnosisTrimListBa[14].c08c1}}</td>
          </tr>
        </table>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.c13n)}">损伤、中毒的外部原因</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.c13n" style="width: 50%" class="inputStyle">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.c12c)}">疾病编码</div>
            <input readOnly type="text" style="width: 27%" v-model="value.somHiInvyBasInfo.c12c" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.c10n)}">病理诊断</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.c10n" style="width: 48%" class="inputStyle">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.c09c)}">疾病编码</div>
            <input readOnly type="text"   style="width: 20%" v-model="value.somHiInvyBasInfo.c09c" class="inputStyle">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.c11)}">病理号</div>
            <input readOnly type="text"  style="width: 14%"  v-model="value.somHiInvyBasInfo.c11" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.c24c)}">药物过敏:</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.c24c" class="numInputStyle">
            <div style="display: flex">
              <div style="padding-left: 10px">1、无 </div>
              <div style="padding-left: 10px">2、有 </div>
            </div>
            <div >过敏药物</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.c25" style="width: 30%;" class="inputStyle">
            <div class="fontWeight" style="padding-left: 15%" :style="{color:checkData(value.somHiInvyBasInfo.c34c)}">死亡患者尸检:</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.c34c" class="numInputStyle">
            <div style="display: flex">
              <div style="padding-left: 10px">1、是 </div>
              <div style="padding-left: 10px">2、否 </div>
            </div>
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.c26c)}">血型:</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.c26c" class="numInputStyle">
            <div style="display: flex">
              <div style="padding-left: 20px">1、A </div>
              <div style="padding-left: 20px">2、B </div>
              <div style="padding-left: 20px">3、O </div>
              <div style="padding-left: 20px">4、AB </div>
              <div style="padding-left: 20px">5、不详 </div>
              <div style="padding-left: 20px">6、未查 </div>
            </div>
            <div  class="fontWeight" style="padding-left: 15%" :style="{color:checkData(value.somHiInvyBasInfo.c27c)}">Rh:</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.c27c" style="width: 5%" class="inputStyle">
            <div style="display: flex">
              <div style="padding-left: 20px">1、阴 </div>
              <div style="padding-left: 20px">2、阳 </div>
              <div style="padding-left: 20px">3、不详 </div>
              <div style="padding-left: 20px">4、未查 </div>
            </div>
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;" >
          <div style="display: flex;margin: 10px;">
            <div style="display: flex;width: 20%">
              <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.b22n)}">科主任</div>
              <input readOnly type="text" v-model="value.somHiInvyBasInfo.b22n" style="width: 40%" class="inputStyle">
            </div>
            <div style="display: flex;width: 30%">
              <div class="fontWeight"  :style="{color:checkData(value.somHiInvyBasInfo.b23n)}">主任（副主任）医师</div>
              <input readOnly type="text"  v-model="value.somHiInvyBasInfo.b23n" style="width: 35%" class="inputStyle">
            </div>
            <div style="display: flex;width: 25%">
              <div class="fontWeight"  :style="{color:checkData(value.somHiInvyBasInfo.b52n)}">主治医师</div>
              <input readOnly type="text"   v-model="value.somHiInvyBasInfo.b52n" style="width: 40%" class="inputStyle">
            </div>
            <div style="display: flex;width: 25%">
              <div class="fontWeight"  :style="{color:checkData(value.somHiInvyBasInfo.b25n)}">住院医师</div>
              <input readOnly type="text"   v-model="value.somHiInvyBasInfo.b25n" style="width: 40%" class="inputStyle">
            </div>
          </div>
          <div style="display: flex;margin: 10px;">
            <div style="display: flex;width: 20%">
              <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.b26n)}" >责任护士</div>
              <input readOnly type="text" v-model="value.somHiInvyBasInfo.b26n" style="width: 40%" class="inputStyle">
            </div>
            <div style="display: flex;width: 30%">
              <div class="fontWeight"  :style="{color:checkData(value.somHiInvyBasInfo.b27n)}">进修医师</div>
              <input readOnly type="text"   v-model="value.somHiInvyBasInfo.b27n" style="width: 35%" class="inputStyle">
            </div>
            <div style="display: flex;width: 25%">
              <div class="fontWeight"  :style="{color:checkData(value.somHiInvyBasInfo.b28)}">实习医师</div>
              <input readOnly type="text"   v-model="value.somHiInvyBasInfo.b28" style="width: 40%" class="inputStyle">
            </div>
            <div style="display: flex;width: 25%">
              <div class="fontWeight"  :style="{color:checkData(value.somHiInvyBasInfo.b29n)}">编码员</div>
              <input readOnly type="text"   v-model="value.somHiInvyBasInfo.b29n" style="width: 40%" class="inputStyle">
            </div>
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.b30c)}">病案质量:</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.b30c" style="width: 5%" class="inputStyle">
            <div style="display: flex">
              <div style="padding-left: 10px">1、甲 </div>
              <div style="padding-left: 10px">2、乙 </div>
              <div style="padding-left: 10px">3、丙 </div>
            </div>
            <div class="fontWeight" style="padding-left: 18px" :style="{color:checkData(value.somHiInvyBasInfo.b31n)}">质控医师</div>
            <input readOnly type="text"   v-model="value.somHiInvyBasInfo.b31n" style="width: 15%" class="inputStyle">
            <div class="fontWeight" style="padding-left: 40px" >质控护士</div>
            <input readOnly type="text"  style="width: 15%" v-model="form.name" class="inputStyle">
            <div style="padding-left: 25px" :style="{color:checkData(value.somHiInvyBasInfo.b33)}">质控日期</div>
            <input readOnly type="text" style="width: 10%" v-model="value.somHiInvyBasInfo.b33" class="inputStyle">
<!--            <div >年</div>-->
<!--            <input readOnly type="text" style="width: 5%" v-model="form.name" class="inputStyle">-->
<!--            <div >月</div>-->
<!--            <input readOnly type="text" style="width: 5%" v-model="form.name" class="inputStyle">-->
<!--            <div >日</div>-->
<!--            <input readOnly type="text" style="width: 5%" v-model="form.name" class="inputStyle">-->
          </div>
        </div>
        <div>
          <el-table
            :data="value.busOperateDiagnosisList"
            :cell-style="{borderColor:'rgba(220,223,230,0)'}"
            style="width: 100%">
            <el-table-column
              prop="c35c"
              align="center"
              label="手术及操作编码"
            >
            </el-table-column>
            <el-table-column
              prop="oprn_oprt_date"
              align="center"
              label="手术及操作日期"
            >
            </el-table-column>
            <el-table-column
              prop="oprn_oprt_lv"
              align="center"
              label="手术级别"
            >
            </el-table-column>
            <el-table-column
              prop="c36n"
              align="center"
              label="手术及操作名称"
            >
            </el-table-column>
            <el-table-column label="手术、操作医师"
                             align="center">
              <el-table-column
                prop="oprn_oprt_oper_name"
                align="center"
                label="术者"
              >
              </el-table-column>
              <el-table-column
                prop="oprn_oprt_1_asit"
                label="I助"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="oprn_oprt_2_asit"
                label="II助"
                align="center"
              >
              </el-table-column>
            </el-table-column>
            <el-table-column
              prop="c42"
              align="center"
              label="切口愈合等级"
            >
            </el-table-column>
            <el-table-column
              prop="c43"
              align="center"
              label="麻醉方式"
            >
            </el-table-column>
            <el-table-column
              prop="oprn_oprt_anst_dr_name"
              align="center"
              label="麻醉医师"
            >
            </el-table-column>
          </el-table>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.b34c)}">离院方式:</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.b34c" class="numInputStyle">
            <div style="display: flex">
              <div style="padding-left: 10px">1、医嘱离院 </div>
              <div style="padding-left: 10px">2、医嘱转院，拟诊接受医疗机构名称 </div>
            </div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.b49" style="width: 50%" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div  style="padding-left: 10px">3、医嘱转社区卫生服务机构/乡镇卫生院，拟诊接受医疗机构名称:</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.b49" style="width: 33%" class="inputStyle">
            <div style="padding-left: 10px" >4、非医嘱转院 </div>
            <div style="padding-left: 10px" >5、其他</div>
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.b36c)}">是否有出院31天再住院计划:</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.b36c" style="width: 5%" class="inputStyle">
            <div style="display: flex">
              <div style="padding-left: 20px">1、无 </div>
              <div style="padding-left: 20px">2、有，目的:</div>
            </div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.b37" style="width: 60%;" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.c28)}">颅脑损伤患者昏迷时间:</div>
            <div  class="fontWeight" style="padding-left: 20px">入院前</div>
            <input readOnly type="text" style="width: 5%" v-model="value.somHiInvyBasInfo.c28" class="inputStyle">
            <div class="fontWeight">天</div>
            <input readOnly type="text" style="width: 5%"  v-model="value.somHiInvyBasInfo.c29" class="inputStyle">
            <div class="fontWeight" >小时</div>
            <input readOnly type="text" style="width: 5%"  v-model="value.somHiInvyBasInfo.c30" class="inputStyle">
            <div class="fontWeight">分钟</div>
            <div  class="fontWeight" style="padding-left: 20px">入院后</div>
            <input readOnly type="text" style="width: 5%" v-model="value.somHiInvyBasInfo.c31" class="inputStyle">
            <div class="fontWeight">天</div>
            <input readOnly type="text" style="width: 5%"  v-model="value.somHiInvyBasInfo.c32" class="inputStyle">
            <div class="fontWeight">小时</div>
            <input readOnly type="text" style="width: 5%"  v-model="value.somHiInvyBasInfo.c33" class="inputStyle">
            <div class="fontWeight">分钟</div>
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d01)}">住院费用（元）：总费用</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d01" class="inputStyle">
            <div  class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d09)}">（自付金额：）</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d09" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d11)}">1、综合医疗服务类:(1)一般医疗服务费</div>
            <input readOnly type="text"  style="width: 8%" v-model="value.somHiInvyBasInfo.d11" class="inputStyle">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d12)}" style="padding-left: 4%">(2)一般治疗操作费</div>
            <input readOnly type="text"  style="width: 8%" v-model="value.somHiInvyBasInfo.d12" class="inputStyle">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d13)}" style="padding-left: 4%">(3)护理费</div>
            <input readOnly type="text"  style="width: 8%" v-model="value.somHiInvyBasInfo.d13" class="inputStyle">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d14)}" style="padding-left: 4%">(4)其他费</div>
            <input readOnly type="text"  style="width: 8%" v-model="value.somHiInvyBasInfo.d14" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d15)}">2、诊断类:(5)病理诊断费</div>
            <input readOnly type="text" style="width: 10%" v-model="value.somHiInvyBasInfo.d15" class="inputStyle">
            <div class="fontWeight" style="padding-left: 2%" :style="{color:checkData(value.somHiInvyBasInfo.d16)}">(6)实验室诊断费</div>
            <input readOnly type="text" style="width: 10%" v-model="value.somHiInvyBasInfo.d16" class="inputStyle">
            <div class="fontWeight" style="padding-left: 2%" :style="{color:checkData(value.somHiInvyBasInfo.d17)}">(7)影像学诊断费</div>
            <input readOnly type="text" style="width: 10%" v-model="value.somHiInvyBasInfo.d17" class="inputStyle">
            <div class="fontWeight" style="padding-left: 2%" :style="{color:checkData(value.somHiInvyBasInfo.d18)}">(8)临床诊断项目费</div>
            <input readOnly type="text" style="width: 10%" v-model="value.somHiInvyBasInfo.d18" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d19)}">3、治疗类:(9)非手术治疗项目费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d19"  style="width: 10%" class="inputStyle">
            <div class="fontWeight" style="padding-left: 4%" :style="{color:checkData(value.somHiInvyBasInfo.d20)}">(10)手术治疗费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d20" style="width: 10%" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d21)}">4、康复类:(11)康复费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d21" style="width: 10%" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d22)}">5、中医类:(12)中医治疗费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d22" style="width: 10%" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d23)}">6、西药类:(13)西医费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d23" style="width: 10%" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d24)}">7、中药类:(14)中成药费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d24" style="width: 10%" class="inputStyle">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d25)}" style="padding-left: 4%">(15)中草药费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d25" style="width: 10%" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d26)}">8、血液和血液制品类:(16)血费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d26"  style="width: 10%" class="inputStyle">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d27)}" style="padding-left: 4%">(17)白蛋白类制品费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d27" style="width: 10%" class="inputStyle">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d28)}" style="padding-left: 4%">(18)球蛋白制品费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d28" style="width: 10%" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d29)}">(19)凝血因子类制品费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d29" style="width: 10%" class="inputStyle">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d30)}" style="padding-left: 4%">(20)细胞因子类制品费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d30" style="width: 10%" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d31)}">9、耗材类:(21)检查用一次性医用材料费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d31" style="width: 10%" class="inputStyle">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d32)}" style="padding-left: 4%">(22)医疗用一次性医用材料费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d32" style="width: 10%" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d33)}">(23)手术用一次性医用材料费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d33" style="width: 10%" class="inputStyle">
          </div>
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div class="fontWeight" :style="{color:checkData(value.somHiInvyBasInfo.d34)}">10、其他类:(24)其他费</div>
            <input readOnly type="text" v-model="value.somHiInvyBasInfo.d34" style="width: 10%" class="inputStyle">
          </div>
        </div>
        <div style="display: flex;border: 1px black solid;border-width: 0 0 1px 0;height: 50px;width: 100%" >
        </div>
        <div style="border: 1px black solid;border-width: 0 0 1px 0;">
          <div style="margin: 10px;display: flex;">
            <div >说明：（一）医疗付费方式： 1、城镇职工基本医疗保险  2、城镇居民基木医疗保险  3、新型农村合作医疗  4、贫困救助  5、
              商业医疗保险  6、全公费  7、全自费  8、其他社会保险  9、 其他
              (二)凡可由医院信息系统提供住院费用清单的，住院病案首页中可不填写“住院费用”。</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'firstPageInfo',
  props: {
    value: Object
  },
  data () {
    return {
      form: {
        name: '张三'
      },
      busDiseaseListLength: ''
    }
  },
  methods: {
    judgeNull (item) {
      if (item == null || item == '') {
        return '-'
      } else {
        return item
      }
    },
    checkData (value) {
      if (value != '' && value != undefined || value === 0) {
        return 'black'
      } else {
        return 'red'
      }
    }
  }
}
</script>
<style scoped>
.inputStyle{
  border-top-style: none;
  border-right-style: none;
  border-left-style: none;
  border-bottom-style: solid
}
.fontWeight{
  font-weight: bold;
}
.numInputStyle{
  height: 17px;
  width: 17px
}
/*/deep/.el-table thead.is-group th {*/
/*  background-color: white;*/
/*}*/
tr td {
  border: 1px black solid;

}
tr{
  height: 36px;
}
th {
  border: 1px black solid;

}
.divMargin{
  display: flex;
  margin: 10px
}
</style>
