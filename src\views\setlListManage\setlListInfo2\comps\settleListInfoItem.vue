<template>
  <div v-if="show">
    <div class="sl-base-item" :style="getStyle(items)" v-for="items in modelVal" :key="items.key">
      <el-form-item :style="{ width: item.allWidth }" :label-width="item.labelWidth"
                    :label="item.label" :prop="item.key" v-for="(item, index) in getItems(items)" :key="index">
        <!-- 类型为输入框 -->
        <el-input v-model="item.value"
                  @input="inputChange($event, item)"
                  :readonly="item.disabled"
                  :style="{ width: item.width * 100 + '%' }"
                  :ref="item.key"
                  v-if="item.type === 'input'">
          <modify-icon :show="item.modify" slot="suffix"/>
        </el-input>
        <span v-if="item.type === 'input'" class="sl-base-item-suffix">{{ item.suffixText }}</span>

        <!-- 类型为下拉选 -->
        <drg-dict-select :dicType="item.dicType"
                        :placeholder="''"
                        :clearable="false"
                        :useClass="false"
                        :disabled="item.disabled"
                        @change="inputChange($event, item)"
                        :selectStyle="{ width: item.width * 100 + '%' }"
                        v-model="item.value"
                        :ref="item.key"
                        :type="item.dictSelectType"
                        v-if="item.type === 'select'">
          <span slot="suffix">
            <modify-icon :show="item.modify"/>
            <span class="sl-base-item-suffix">{{ item.suffixText }}</span>
          </span>
        </drg-dict-select>

        <!-- 通用下拉选，指定数据源 -->
        <common-select v-model="item.value"
                       v-if="item.type === 'commonSelect'"
                       :data="getSelectData(item.selectDataField, item.keyProps.label,item.replace)"
                       :key-props="item.keyProps"
                       :label-or-value="item.labelOrValue"
                       :actual-key-props="item.actualKeyProps"
                       :width="item.width * 100 + '%'"
                       :replace-bracket="item.replace"
                       :show-icon="item.modify"
                       @change="inputChange($event, item)"/>

        <div class="el-form-item__error" v-if="item.error">
          {{ item.error }}
        </div>
      </el-form-item>
      <el-divider v-if="showDivider(items)"/>
    </div>
  </div>
</template>
<script>
import ModifyIcon from './settleListInfoModifyIcon'
import CommonSelect from './settleListInfoSelect'

export default {
  name: 'settleListInfoItem',
  components: {
    'modify-icon': ModifyIcon,
    'common-select': CommonSelect
  },
  props: {
    // v-model 绑定值
    modelVal: [Array, Object],
    // 聚焦的key
    focusKey: [String],
    // 修改字段
    modifyField: [String]
  },
  data () {
    return {
      show: true
    }
  },
  model: {
    prop: 'modelVal',
    event: 'selected'
  },
  methods: {
    // 获取数据
    getItems (items) {
      if (items instanceof Array) {
        return items
      } else {
        return items.data
      }
    },
    // 获取样式
    getStyle (items) {
      if (items instanceof Array) {
        return {}
      } else {
        return {
          justifyContent: items.align
        }
      }
    },
    // 是否显示分割线
    showDivider (items) {
      if (items instanceof Array) {
        return false
      } else {
        return items.showDivider
      }
    },
    // 输入框改变
    inputChange (val, item) {
      let flag = false
      // 更严格的比较，处理null、undefined、空字符串等情况
      const currentValue = val === null || val === undefined ? '' : String(val).trim()
      const originalValue = item.copyValue === null || item.copyValue === undefined ? '' : String(item.copyValue).trim()

      if (currentValue !== originalValue) {
        flag = true
      } else {
        flag = false
      }
      item.modify = flag
      this.$emit('change', { key: item.key, value: val })
      this.$forceUpdate()
    },
    // 刷新
    refresh () {
      this.show = false
      this.show = true
    },
    // 获取下拉选数据
    getSelectData (fld, label, replace) {
      let data = JSON.parse(JSON.stringify(this.$parent.$parent[fld]))
      data.forEach((item) => {
        item[label] = replace ? item[label].replace(/\([^\)]*\)/g, '') : item[label]
      })
      return data
    }
  },
  watch: {
    focusKey (key) {
      this.refresh()
      if (key && this.$refs[key]) {
        let tag = this.$refs[key][0].$options._componentTag
        if (tag === 'el-input') {
          this.$refs[key][0].focus()
          this.$emit('closeValidate', key)
        } else if (tag === 'som-dict-select') {
          if (this.$refs[key][0].$children[0].$children[0].$options._componentTag === 'el-input') {
            this.$refs[key][0].$children[0].$children[0].focus()
          }
          this.$emit('closeValidate', key)
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
.sl-base-item{
  width: 100%;
  height: 20%;
  display: flex;
  align-items: center;
  //justify-content: space-between;
  flex-wrap: wrap;
  justify-items: left;

  &-suffix {
    font-size: 12px;
  }
}
/deep/ .el-divider--horizontal{
  margin: 6px 0;
}

/deep/ .el-input__inner{
  padding: 0;
  border: none;
  border-bottom: 1px solid black;
}
</style>
