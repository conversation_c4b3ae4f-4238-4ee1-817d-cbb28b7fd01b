<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-date-range
             headerTitle="查询条件"
             contentTitle="疾病全面分析"
             :container="true"
             @query="queryData">

      <!-- 内容 -->
      <template slot="containerContent">
        <drg-echarts :options="options" ref="radarCharts" />
      </template>
    </drg-form>
  </div>
</template>

<script>
import { queryDiseaseCRSData } from '@/api/newDrgBusiness/newDrgDiseaseCRSAnalysis'

export default {
  name: 'newDrgDiseaseCRSAnalysis',
  data: () => ({
    queryForm: {},
    options: {}
  }),
  methods: {
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },

    queryData () {
      queryDiseaseCRSData(this.getParams()).then(res => {
        if (res.data) {
          this.getOptions(res.data)
        }
      })
      // this.options = {
      //   title: {
      //     text: '学科覆盖情况',
      //     align: 'center',
      //     left: '20%'
      //   },
      //   legend: {
      //     data: ['2021', '2022'],
      //   },
      //   tooltip: {
      //     formatter: function(params){
      //
      //       return 'MDCG-消化系统疾病及功能障碍<br/>上年度病组覆盖:78%<br/>今年度病组覆盖: 80%'
      //     }
      //   },
      //   radar: {
      //     // shape: 'circle',
      //     indicator: [
      //       { name: 'MDCA', max: 100 },
      //       { name: 'MDCZ', max: 100 },
      //       { name: 'MDCY', max: 100 },
      //       { name: 'MDCX', max: 100 },
      //       { name: 'MDCW', max: 100 },
      //       { name: 'MDCV', max: 100 },
      //       { name: 'MDCU', max: 100 },
      //       { name: 'MDCT', max: 100 },
      //       { name: 'MDCS', max: 100 },
      //       { name: 'MDCR', max: 100 },
      //       { name: 'MDCQ', max: 100 },
      //       { name: 'MDCP', max: 100 },
      //       { name: 'MDCO', max: 100 },
      //       { name: 'MDCN', max: 100 },
      //       { name: 'MDCM', max: 100 },
      //       { name: 'MDCL', max: 100 },
      //       { name: 'MDCK', max: 100 },
      //       { name: 'MDCJ', max: 100 },
      //       { name: 'MDCI', max: 100 },
      //       { name: 'MDCH', max: 100 },
      //       { name: 'MDCG', max: 100 },
      //       { name: 'MDCF', max: 100 },
      //       { name: 'MDCE', max: 100 },
      //       { name: 'MDCD', max: 100 },
      //       { name: 'MDCC', max: 100 },
      //       { name: 'MDCB', max: 100 },
      //     ]
      //   },
      //   series: [
      //     {
      //       name: 'Budget vs spending',
      //       type: 'radar',
      //       data: [
      //         {
      //           value: [26, 40, 87, 60, 53, 66, 76, 82, 90, 65, 67, 86, 68, 42, 54, 56, 66, 77, 88, 76, 80, 55, 66, 90, 60, 40],
      //           name: '2022'
      //         },
      //         {
      //           value: [13, 23, 67, 32, 53, 45, 78, 80, 80, 61, 57, 85, 45, 25, 41, 45, 47, 60, 81, 65, 78, 47, 63, 84, 41, 36],
      //           name: '2021'
      //         }
      //       ]
      //     }
      //   ]
      // };
    },

    getOptions (item) {
      let max = 100
      let indicatorData = []
      let radarData = []
      let coverageData = []
      let mdcCodg = []
      let mdcName = []
      if (item) {
        for (const res of item) {
          indicatorData.push({ name: res.mdcCodg, max: max })
          coverageData.push(res.coverage)
          mdcCodg.push(res.mdcCodg)
          mdcName.push(res.mdcName)
        }
        radarData.push({ value: coverageData, name: '2022' })
        this.options = {
          tooltip: {
          },
          radar: {
            indicator: indicatorData
          },
          series: [
            {
              type: 'radar',
              data: radarData
            }
          ]
        }
      }
    }
  }
}
</script>
