<template>
  <div class="app-container">
    <drg-form v-model="queryFrom"
             show-hos-dept
             show-se-date-range
             show-date-range
             headerTitle="查询条件"
             contentTitle="费用预警"
             :container="true"
             @query="query(true)" ref="somForm">
      <template slot="extendFormItems">
       <!-- <el-form-item label="出院时间" prop="time_range">
          <el-date-picker
            v-model="queryFrom.updt_date"
            :clearable="false"
            type="monthrange"
            range-separator="-"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            @change="query(true)">
          </el-date-picker>
        </el-form-item>-->
        <el-form-item label="盈亏比" class="som-el-form-item-margin-left">
          <el-button round
                     class="dcew-query-button"
                     :class="[yk_check.yz.checked ? 'decw-query-check-yz' : 'decw-query-uncheck-yz',
                                  yk_check.yz.mouseover ? 'decw-query-check-yz-mouseover': '']"
                     @mouseover.native="yk_check.yz.mouseover = true"
                     @mouseleave.native="yk_check.yz.mouseover = false"
                     @click="fnProfitAndLossQuery(1)">严重超支</el-button>
          <el-button round
                     class="dcew-query-button"
                     :class="[yk_check.cz.checked ? 'decw-query-check-cz' : 'decw-query-uncheck-cz',
                                  yk_check.cz.mouseover ? 'decw-query-check-cz-mouseover': '']"
                     @mouseover.native="yk_check.cz.mouseover = true"
                     @mouseleave.native="yk_check.cz.mouseover = false"
                     @click="fnProfitAndLossQuery(2)">超支</el-button>
          <el-button round
                     class="dcew-query-button"
                     :class="[yk_check.cp.checked ? 'decw-query-check-cp' : 'decw-query-uncheck-cp',
                                  yk_check.cp.mouseover ? 'decw-query-check-cp-mouseover': '']"
                     @mouseover.native="yk_check.cp.mouseover = true"
                     @mouseleave.native="yk_check.cp.mouseover = false"
                     @click="fnProfitAndLossQuery(3)">持平</el-button>
          <el-button round
                     class="dcew-query-button"
                     :class="[yk_check.jy.checked ? 'decw-query-check-jy' : 'decw-query-uncheck-jy',
                                  yk_check.jy.mouseover ? 'decw-query-check-jy-mouseover': '']"
                     @mouseover.native="yk_check.jy.mouseover = true"
                     @mouseleave.native="yk_check.jy.mouseover = false"
                     @click="fnProfitAndLossQuery(4)">结余</el-button>
        </el-form-item>
      </template>

      <template slot="containerContent">
        <el-empty v-show="empty" style="position: absolute;top: 25%;left: 45%" description="暂无数据"></el-empty>
        <drg-loading :loading="loading" style="position: absolute;top: 50%;left: 45%;"/>
        <el-card v-for="(item, index) in data" :key="index"
                 :body-style="{'padding': '0px!important'}"
                 shadow="hover"
                 class="dcew-content-box"
                 :class="['dcew-content-box-left', range(item)]">
          <div class="dcew-content-box-title">
            {{ item.deptName }}
          </div>
          <div class="dcew-content-box-item">
            <div class="dcew-content-box-item-title">
              盈亏比
            </div>
            <span class="dcew-content-box-item-val">
              {{ item.balanceRate }}%
            </span>
          </div>
          <div class="dcew-content-box-item">
            <div class="dcew-content-box-item-title">
              盈亏金额
            </div>
            <span class="dcew-content-box-item-val" v-html="formatCost(item.balanceCost,true)"/>
          </div>
          <div style="width: 100%;height: 40%;">
            <div class="dcew-content-box-item-end">
              <div class="dcew-content-box-item-end-title">
                入组病案数
              </div>
              <div v-if="Number(item.allCount)>0" class="dcew-content-box-item-val dcew-content-box-item-end-val" @click="queryAllCount(item)">
                {{ item.allCount }}
              </div>
              <div v-if="Number(item.allCount)==0">
                {{item.allCount}}
              </div>
            </div>

            <div class="dcew-content-box-item-end">
              <div class="dcew-content-box-item-end-title">
                病组
              </div>
              <div v-if="Number(item.groupCount)>0" class="dcew-content-box-item-val dcew-content-box-item-end-val"  @click="queryGroupCount(item)">
                {{ item.groupCount }}
              </div>
              <div v-if="Number(item.groupCount)==0">
                {{item.groupCount}}
              </div>
            </div>

            <!--            <div class="dcew-content-box-item-end">-->
            <!--              <div class="dcew-content-box-item-end-title">-->
            <!--                医生数-->
            <!--              </div>-->
            <!--              <div class="dcew-content-box-item-val dcew-content-box-item-end-val">-->
            <!--                {{ item.medicalCount }}-->
            <!--              </div>-->
            <!--            </div>-->
            <div class="dcew-content-box-item-end">
              <div class="dcew-content-box-item-end-title">
                超高病案数
              </div>
              <div v-if="Number(item.upNum)>0" class="dcew-content-box-item-val dcew-content-box-item-end-val" @click="queryUpCount(item)">
                {{ item.upNum }}
              </div>
              <div v-if="Number(item.upNum)==0" class="dcew-content-box-item-val dcew-content-box-item-end-val">
                {{item.upNum}}
              </div>
            </div>
            <div class="dcew-content-box-item-end">
              <div class="dcew-content-box-item-end-title">
                超低病案数
              </div>
              <div v-if="Number(item.lowNum)>0" class="dcew-content-box-item-val dcew-content-box-item-end-val" @click="queryLowCount(item)">
                {{ item.lowNum }}
              </div>
              <div v-if="Number(item.lowNum)==0" class="dcew-content-box-item-val dcew-content-box-item-end-val">
                {{item.lowNum}}
              </div>
            </div>
          </div>
        </el-card>
      </template>
    </drg-form>
  </div>
</template>

<script>
import { queryDataIsuue, querySelectTreeAndSelectList } from '@/api/common/drgCommon'
import { getList } from '@/api/hospitalAnalysis/drgsdeptCostEarlyWarning'
import { formatCost } from '@/utils/common'
import moment from 'moment'

export default {
  name: 'deptFeeWarn',
  components: { },
  inject: ['reload'],
  data () {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick (picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick (picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      rules: {
        // time_range: [
        //   { type: 'date', required: true, message: '请选择时间范围', trigger: 'change' }
        // ]
      },
      depts: [],
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      queryFrom: {
        updt_date: [this.cy_start_date, this.cy_end_date], // 日期
        deptCode: '', // 部门
        rate: 0, // 盈亏比
        feeStas: '0'
      },
      data: [],
      yk_check: {
        yz: {
          index: 1,
          checked: false,
          mouseover: false
        },
        cz: {
          index: 2,
          checked: false,
          mouseover: false
        },
        cp: {
          index: 3,
          checked: false,
          mouseover: false
        },
        jy: {
          index: 4,
          checked: false,
          mouseover: false
        }
      },
      empty: false,
      loading: false
    }
  },
  created () {
    this.queryFrom.feeStas = String(this.$store.getters.feeStas)
    this.findSelectTreeAndSelectList()
    this.getDataIsuue()
  },
  methods: {
    queryUpCount (row) {
      this.goto('/oprelDecimmak/pattExtrAnalysis', {
        ym: this.queryFrom.ym,
        deptCode: row.deptCode,
        begnDate: moment(this.getYearMonthStartTimeByData()).format('YYYY-MM-DD'),
        expiDate: moment(this.getYearMonthEndTimeByData()).format('YYYY-MM-DD'),
        // dateRange: [moment(this.getYearMonthStartTimeByData()).format('YYYY-MM-DD'),moment(this.getYearMonthEndTimeByData()).format('YYYY-MM-DD')],
        seStartTime: moment(this.getYearMonthSeStartTimeByData()).format('YYYY-MM-DD'),
        seEndTime: moment(this.getYearMonthSeEndTimeByData()).format('YYYY-MM-DD'),
        // seDateRange:[moment(this.getYearMonthSeStartTimeByData()).format('YYYY-MM-DD'),moment(this.getYearMonthSeEndTimeByData()).format('YYYY-MM-DD')],
        costSection: 1,
        group: 3
      })
    },
    queryLowCount (row) {
      let begnDate = moment(this.getYearMonthStartTimeByData()).format('YYYY-MM-DD')
      this.goto('/oprelDecimmak/pattExtrAnalysis', {
        // begnDate: this.queryFrom.cy_start_date,
        // expiDate: this.queryFrom.cy_end_date,
        deptCode: row.deptCode,
        begnDate: moment(this.getYearMonthStartTimeByData()).format('YYYY-MM-DD'),
        expiDate: moment(this.getYearMonthEndTimeByData()).format('YYYY-MM-DD'),
        // dateRange: [moment(this.getYearMonthStartTimeByData()).format('YYYY-MM-DD'),moment(this.getYearMonthEndTimeByData()).format('YYYY-MM-DD')],
        seStartTime: moment(this.getYearMonthSeStartTimeByData()).format('YYYY-MM-DD'),
        seEndTime: moment(this.getYearMonthSeEndTimeByData()).format('YYYY-MM-DD'),
        // seDateRange:[moment(this.getYearMonthSeStartTimeByData()).format('YYYY-MM-DD'),moment(this.getYearMonthSeEndTimeByData()).format('YYYY-MM-DD')],
        costSection: 2,
        group: 3
      })
    },
    formatCost,
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'ERROR_TYPE')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
      })
    },

    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.queryFrom.updt_date = [this.cy_start_date, this.cy_end_date]

        // 查询数据
        this.init()
      })
    },

    getDataData (timeDate) {
      let now = timeDate()
      if (now + ''.indexOf('1899') > -1) {
        return false
      }
      let nowMonth = now.substr(5, 2)
      let nowYear = now.substr(0, 4)
      return new Date(nowYear, nowMonth - 1, 1)
    },
    getYearMonthStartTimeByData () {
      let now = this.queryFrom.begnDate
      if (now + ''.indexOf('1899') > -1) {
        return false
      }
      // let nowMonth = now.substr(5,2);
      // let nowYear = now.substr(0,4);
      return now
    },
    getYearMonthSeEndTimeByData () {
      let now = this.queryFrom.seEndTime

      if (now + ''.indexOf('1899') > -1) {
        return false
      }
      // let nowMonth = now.substr(5,2);
      // let nowYear = now.substr(0,4);
      // return now;
      // return nowMonth;
      // return nowYear;
      return now
    },
    getYearMonthSeStartTimeByData () {
      let now = this.queryFrom.seStartTime
      if (now + ''.indexOf('1899') > -1) {
        return false
      }
      // let nowMonth = now.substr(5,2);
      // let nowYear = now.substr(0,4);
      // return now;
      // return nowMonth;
      // return nowYear;
      return now
    },
    getYearMonthEndTimeByData () {
      let now = this.queryFrom.expiDate
      // if (now+''.indexOf('1899')>-1){
      //   return false
      // }
      // let nowMonth = now.substr(5,2);
      // let nowYear = now.substr(0,4);
      // return now;
      // return nowMonth;
      // return nowYear;
      return now
    },
    queryAllCount (item) {
      // let startTime=getDataData()
      this.$router.push({
        // path: '/common/queryMedicalDetail', query: {
        //   queryType: 'groupNum',
        //   priOutHosDeptCode: item.deptCode,
        //   priOutHosDeptName: item.deptName,
        //   cy_start_date: moment(this.getYearMonthStartTimeByData()).format('YYYY-MM-DD'),
        //   cy_end_date: moment(this.getYearMonthEndTimeByData()).format('YYYY-MM-DD'),
        //   type: '2',
        // }
        path: '/oprelDecimmak/pattExtrAnalysis',
        query: {
          queryType: 'groupNum',
          priOutHosDeptCode: item.deptCode,
          deptCode: item.deptCode,
          priOutHosDeptName: item.deptName,
          ym: this.queryFrom.ym,

          begnDate: moment(this.getYearMonthStartTimeByData()).format('YYYY-MM-DD'),
          expiDate: moment(this.getYearMonthEndTimeByData()).format('YYYY-MM-DD'),
          seStartTime: moment(this.getYearMonthSeStartTimeByData()).format('YYYY-MM-DD'),
          seEndTime: moment(this.getYearMonthSeEndTimeByData()).format('YYYY-MM-DD'),
          // seDateRange:[moment(this.getYearMonthSeStartTimeByData()).format('YYYY-MM-DD'),moment(this.getYearMonthSeEndTimeByData()).format('YYYY-MM-DD')],
          /// dateRange: [moment(this.getYearMonthStartTimeByData()).format('YYYY-MM-DD'),moment(this.getYearMonthEndTimeByData()).format('YYYY-MM-DD')],
          group: '3',
          type: '2'
        }
      })
    },
    queryGroupCount (item) {
      this.$router.push({
        // path: '/common/queryDrgDetail', query: {
        //   queryType: 'groupNum',
        //   priOutHosDeptCode: item.deptCode,
        //   priOutHosDeptName: item.deptName,
        //   cy_start_date: moment(this.getYearMonthStartTimeByData()).format('YYYY-MM-DD'),
        //   cy_end_date: moment(this.getYearMonthEndTimeByData()).format('YYYY-MM-DD'),
        //   type: '2',
        // }
        path: '/oprelDecimmak/diseExtrAnalysis',
        query: {
          queryType: 'groupNum',
          priOutHosDeptCode: item.deptCode,
          deptCode: item.deptCode,
          priOutHosDeptName: item.deptName,
          // cy_start_date: moment(this.getYearMonthStartTimeByData()).format('YYYY-MM-DD'),
          // cy_end_date: moment(this.getYearMonthEndTimeByData()).format('YYYY-MM-DD'),
          begnDate: moment(this.getYearMonthStartTimeByData()).format('YYYY-MM-DD'),
          expiDate: moment(this.getYearMonthEndTimeByData()).format('YYYY-MM-DD'),
          dateRange: [moment(this.getYearMonthStartTimeByData()).format('YYYY-MM-DD'), moment(this.getYearMonthEndTimeByData()).format('YYYY-MM-DD')],
          seStartTime: moment(this.getYearMonthSeStartTimeByData()).format('YYYY-MM-DD'),
          seEndTime: moment(this.getYearMonthSeEndTimeByData()).format('YYYY-MM-DD'),
          seDateRange: [moment(this.getYearMonthSeStartTimeByData()).format('YYYY-MM-DD'), moment(this.getYearMonthSeEndTimeByData()).format('YYYY-MM-DD')],
          group: '3',
          type: '2'
        }
      })
    },
    refresh () {
      this.reload()
    },
    query (isQuery = false) {
      let params = this.queryFrom
      if (isQuery) {
        params.rate = 0
      }
      /* 不需要再设置时间 */
      /* if (params.updt_date) { */
      /*   let startMonth = moment(params.updt_date[0]).format('yyyy-MM')
        let endMonth = moment(params.updt_date[1]).format('yyyy-MM')
        params.begnDate = startMonth
        params.expiDate = endMonth */
      /* } */
      params.dataAuth = true
      this.loading = true
      getList(params).then((res) => {
        if (res.code == 200) {
          if (res.data) {
            if (res.data.length == 0) {
              this.empty = true
            } else {
              this.empty = false
            }
            this.data = res.data
          }
          this.loading = false
        }
      }).catch(reason => {
        this.loading = false
      })
    },
    // 盈亏比查询
    fnProfitAndLossQuery (data) {
      if (data) {
        let flag = true
        Object.keys(this.yk_check).forEach(key => {
          if (this.yk_check[key].index == data) {
            if (this.yk_check[key].checked) {
              this.yk_check[key].checked = false
              this.queryFrom.rate = 0
              flag = false
            } else {
              this.yk_check[key].checked = true
            }
          } else {
            this.yk_check[key].checked = false
          }
        })
        if (flag) {
          this.queryFrom.rate = data
        }
        this.query()
      }
    },
    init () {
      this.query()
    },
    range (item) {
      if (item && item.balanceRate) {
        let ykb = parseFloat(item.balanceRate)
        if (ykb <= -10) {
          return 'seriousness'
        }
        if (ykb > -10 && ykb <= -1) {
          return 'warning'
        }
        if (ykb > -1 && ykb <= 1) {
          return 'steady'
        }
        if (ykb > 1) {
          return 'surplus'
        }
      }
      // -0.0 情况
      return 'steady'
    },
    fnClearTree () {
      this.queryFrom.rate = 0
      this.queryFrom.deptCode = ''
      this.query()
    },
    fnChangeDept () {
      this.query()
    },
    queryMedicalTotalNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.cy_start_date,
          cy_end_date: this.cy_end_date,
          seStartTime: moment(this.getYearMonthSeStartTimeByData()).format('YYYY-MM-DD'),
          seEndTime: moment(this.getYearMonthSeEndTimeByData()).format('YYYY-MM-DD'),
          inHosFlag: this.queryForm.inHosFlag,
          type: '2'
        }
      })
    },
    queryDrgsNum (row) {
      this.$router.push({
        path: '/common/queryDrgDetail',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.cy_start_date,
          cy_end_date: this.cy_end_date,
          seStartTime: moment(this.getYearMonthSeStartTimeByData()).format('YYYY-MM-DD'),
          seEndTime: moment(this.getYearMonthSeEndTimeByData()).format('YYYY-MM-DD'),
          inHosFlag: this.queryForm.inHosFlag,
          type: '2'
        }
      })
    }
  }
}
</script>

<style scoped>
.dcew-query-button {
  width: 5rem;
}
.dcew-content-box {
  width: 24.5%;
  display: inline-block;
}
.dcew-content-box-title {
  width: 100%;
  height: 10%;
  padding: 1rem;
  font-size: var(--biggerSize);
  font-weight: bold;
}
.dcew-content-box-item {
  margin: 1rem 1rem 1rem 1rem;
  width: 25%;
  display: inline-block;
  text-align: center;
}
.dcew-content-box-item-title {
  color: gray;
  font-size: var(--biggerSmallSize);
  margin-bottom: 0.5rem
}
.dcew-content-box-item-val {
  font-size: var(--biggerSize);
  font-weight: bold;
}
.dcew-content-box-item-end{
  width: 20%;
  height: 100%;
  margin: 0.5rem 0 1rem 0.5rem;
  text-align: center;
  display: inline-block
}
.dcew-content-box-item-end-title{
  margin-bottom: 0.75rem;
  color: gray;
  font-size: var(--biggerSmallSize)
}
.dcew-content-box-item-end-val {
  cursor: pointer;
  text-decoration: underline;
  font-size: var(--biggerSize);
}
.dcew-content-box-item-end-val:hover{
  color: rgb(77,162,255);
  text-decoration: underline;
}
.seriousness {
  /*<!-- rgba(246,114,114,0.5) #f67272 -->*/
  background: linear-gradient(to bottom, rgba(246,114,114,0.5),white);
}
.warning {
  /*<!-- e7a646 rgba(231,166,70,0.5) -->*/
  background: linear-gradient(to bottom, rgba(231,166,70,0.5),white);
}
.steady {
  /** rgba(77,162,255,0.5) 4da2ff*/
  background: linear-gradient(to bottom, rgba(77,162,255,0.5),white);
}
.surplus {
  /** rgba(111,196,68,0.5) 6fc444*/
  background: linear-gradient(to bottom, rgba(111,196,68,0.5),white);
}
.decw-query-check-yz {
  background-color: rgb(246,114,114);
  color: white;
}
.decw-query-uncheck-yz{
  background-color: rgba(246,114,114,0.5);
  color: white
}
.decw-query-check-cz {
  background-color: rgb(231,166,70);
  color: white;
}
.decw-query-uncheck-cz{
  background-color: rgba(231,166,70,0.5);
  color: white
}
.decw-query-check-cp {
  background-color: rgb(77,162,255);
  color: white;
}
.decw-query-uncheck-cp{
  background-color: rgba(77,162,255,0.5);
  color: white
}
.decw-query-check-jy {
  background-color: rgb(111,196,68);
  color: white;
}
.decw-query-uncheck-jy{
  background-color: rgba(111,196,68,0.5);
  color: white
}
.decw-query-check-yz-mouseover {
  border: 1px solid rgb(246,114,114);
}
.decw-query-check-cz-mouseover {
  border: 1px solid rgb(231,166,70);
}
.decw-query-check-cp-mouseover{
  border: 1px solid rgb(77,162,255);
}
.decw-query-check-jy-mouseover{
  border: 1px solid rgb(111,196,68);
}
.dcew-content-box-left {
  margin-left: 0.5%;
}
</style>
