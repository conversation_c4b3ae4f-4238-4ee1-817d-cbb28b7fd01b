<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-hos-dept
             show-dip
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="医院病组分析"
             :container="true"
             @query="handleSearchList" @reset="handleResetSearch">
      <template slot="buttons">
        <el-popconfirm
          confirm-button-text='确定'
          cancel-button-text='导出全部'
          icon="el-icon-info"
          icon-color="red"
          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>

      <template slot="containerContent">
        <div style="height:35%">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="15" style="height: 100%">
              <el-table v-loading="analysisLoading"
                        ref="leftTable"
                        size="mini"
                        :header-cell-style = "{'text-align' : 'center',}"
                        :cell-style="{padding:'0.2rem'}"
                        stripe
                        height="100%"
                        :data="ifComplicationGroupList"
                        style="width: 100%;height: 100%"
                        border>
                <el-table-column label="指标"  align="left" :show-overflow-tooltip="true">
                  <template slot-scope="scope">{{scope.row.index}}</template>
                </el-table-column>
                <el-table-column label="核心病种"  align="right"  :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.hxbz>0" class='skip' @click="queryMedicalNumByType(scope.row,'col1')">
                      {{scope.row.hxbz | formatIsEmpty}}
                    </div>
                    <div v-if="scope.row.index=='DIP组数'&&scope.row.hxbz>0" class='skip' @click="queryDrgGroupNumByType(scope.row,'col1')">
                      {{scope.row.hxbz | formatIsEmpty}}
                    </div>
                    <div v-if="(scope.row.index!='病案数'&&scope.row.index!='DIP组数') || scope.row.hxbz==0" style="color:#000000">
                      {{scope.row.hxbz | formatIsEmpty}}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="综合病种">
                  <el-table-column label="治疗性操作"  align="right" :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <div v-if="scope.row.index=='病案数'&&scope.row.zlxcz>0" class='skip' @click="queryMedicalNumByType(scope.row,'col2')">
                        {{scope.row.zlxcz | formatIsEmpty}}
                      </div>
                      <div v-if="scope.row.index=='DIP组数'&&scope.row.zlxcz>0" class='skip' @click="queryDrgGroupNumByType(scope.row,'col2')">
                        {{scope.row.zlxcz | formatIsEmpty}}
                      </div>
                      <div v-if="(scope.row.index!='病案数'&&scope.row.index!='DIP组数') || scope.row.zlxcz==0" style="color:#000000">
                        {{scope.row.zlxcz | formatIsEmpty}}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="诊断性操作"  align="right"  :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <div v-if="scope.row.index=='病案数'&&scope.row.zdxcz>0" class='skip' @click="queryMedicalNumByType(scope.row,'col3')">
                        {{scope.row.zdxcz | formatIsEmpty}}
                      </div>
                      <div v-if="scope.row.index=='DIP组数'&&scope.row.zdxcz>0" class='skip' @click="queryDrgGroupNumByType(scope.row,'col3')">
                        {{scope.row.zdxcz | formatIsEmpty}}
                      </div>
                      <div v-if="(scope.row.index!='病案数'&&scope.row.index!='DIP组数') || scope.row.zdxcz==0" style="color:#000000">
                        {{scope.row.zdxcz | formatIsEmpty}}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="相关手术"  align="right"  :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <div v-if="scope.row.index=='病案数'&&scope.row.xgss>0" class='skip' @click="queryMedicalNumByType(scope.row,'col4')">
                        {{scope.row.xgss | formatIsEmpty}}
                      </div>
                      <div v-if="scope.row.index=='DIP组数'&&scope.row.xgss>0" class='skip' @click="queryDrgGroupNumByType(scope.row,'col4')">
                        {{scope.row.xgss | formatIsEmpty}}
                      </div>
                      <div v-if="(scope.row.index!='病案数'&&scope.row.index!='DIP组数') || scope.row.xgss==0" style="color:#000000">
                        {{scope.row.xgss | formatIsEmpty}}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="保守治疗"  align="right"  :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <div v-if="scope.row.index=='病案数'&&scope.row.bszl>0" class='skip' @click="queryMedicalNumByType(scope.row,'col5')">
                        {{scope.row.bszl | formatIsEmpty}}
                      </div>
                      <div v-if="scope.row.index=='DIP组数'&&scope.row.bszl>0" class='skip' @click="queryDrgGroupNumByType(scope.row,'col5')">
                        {{scope.row.bszl | formatIsEmpty}}
                      </div>
                      <div v-if="(scope.row.index!='病案数'&&scope.row.index!='DIP组数') || scope.row.bszl==0" style="color:#000000">
                        {{scope.row.bszl | formatIsEmpty}}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="综合病种合计"  align="right"  :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <div v-if="scope.row.index=='病案数'&&scope.row.hj>0" class='skip' @click="queryMedicalNumByType(scope.row,'col6')">
                        {{scope.row.hj | formatIsEmpty}}
                      </div>
                      <div v-if="scope.row.index=='DIP组数'&&scope.row.hj>0" class='skip' @click="queryDrgGroupNumByType(scope.row,'col6')">
                        {{scope.row.hj | formatIsEmpty}}
                      </div>
                      <div v-if="(scope.row.index!='病案数'&&scope.row.index!='DIP组数') || scope.row.hj==0" style="color:#000000">
                        {{scope.row.hj | formatIsEmpty}}
                      </div>
                    </template>
                  </el-table-column>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="9" style="height: 100%">
              <el-table ref="rightTable"
                        size="mini"
                        :header-cell-style = "{'text-align' : 'center'}"
                        :cell-style="{padding:'0.425rem'}"
                        stripe
                        height="100%"
                        :data="deptGroupList"
                        style="width: 100%;"
                        v-loading="analysisLoading"
                        border>
                <el-table-column label="指标"  align="left" :show-overflow-tooltip="true">
                  <template slot-scope="scope">{{scope.row.index}}</template>
                </el-table-column>
                <el-table-column label="内科组"  align="right" :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.medicalDeptDrgs>0" class='skip' @click="queryMedicalNumByType(scope.row,'col7')">
                      {{scope.row.medicalDeptDrgs | formatIsEmpty}}
                    </div>
                    <div v-if="scope.row.index=='DIP组数'&&scope.row.medicalDeptDrgs>0" class='skip' @click="queryDrgGroupNumByType(scope.row,'col7')">
                      {{scope.row.medicalDeptDrgs | formatIsEmpty}}
                    </div>
                    <div v-if="(scope.row.index!='病案数'&&scope.row.index!='DIP组数') || scope.row.medicalDeptDrgs==0" style="color:#000000">
                      {{scope.row.medicalDeptDrgs | formatIsEmpty}}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="非手术室操作组"  align="right" width="150" :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.notOperationDrgs>0" class='skip' @click="queryMedicalNumByType(scope.row,'col8')">
                      {{scope.row.notOperationDrgs | formatIsEmpty}}
                    </div>
                    <div v-if="scope.row.index=='DIP组数'&&scope.row.notOperationDrgs>0" class='skip' @click="queryDrgGroupNumByType(scope.row,'col8')">
                      {{scope.row.notOperationDrgs | formatIsEmpty}}
                    </div>
                    <div v-if="(scope.row.index!='病案数'&&scope.row.index!='DIP组数') || scope.row.notOperationDrgs==0" style="color:#000000">
                      {{scope.row.notOperationDrgs | formatIsEmpty}}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="外科组"  align="right" :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.surgeryDeptDrgs>0" class='skip' @click="queryMedicalNumByType(scope.row,'col9')">
                      {{scope.row.surgeryDeptDrgs | formatIsEmpty}}
                    </div>
                    <div v-if="scope.row.index=='DIP组数'&&scope.row.surgeryDeptDrgs>0" class='skip' @click="queryDrgGroupNumByType(scope.row,'col9')">
                      {{scope.row.surgeryDeptDrgs | formatIsEmpty}}
                    </div>
                    <div v-if="(scope.row.index!='病案数'&&scope.row.index!='DIP组数') || scope.row.surgeryDeptDrgs==0" style="color:#000000">
                      {{scope.row.surgeryDeptDrgs | formatIsEmpty}}
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="height: 64%">
          <el-table
            id="dipTable"
            size="mini"
            :header-cell-style = "{'text-align' : 'center'}"
            height="100%"
            stripe
            :data="list"
            v-loading="listLoading"
            @sort-change='sortChange'
            @selection-change="handleSelectionChange"
            ref="dataTable"
            border>
            <el-table-column
              label="序号"
              type="index"
              align="right"
              width="50">
            </el-table-column>
            <el-table-column  label="DIP编码" prop="dipCodg" align="left" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div class='skip' @click="queryMedicalNum(scope.row)">
                  {{scope.row.dipCodg | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="DIP名称" prop="dipName" align="left" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div class='skip' @click="queryMedicalNum(scope.row)">
                  {{scope.row.dipName | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="入组病案数"  align="right" width="120" prop="inGroupMedicalNum" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.inGroupMedicalNum)>0" class='skip' @click="queryMedicalNum(scope.row)">
                  {{scope.row.inGroupMedicalNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.inGroupMedicalNum)==0" style="color:#000000">
                  {{scope.row.inGroupMedicalNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="科室数"  align="right" width="90" prop="deptNum" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.deptNum)>0" class='skip' @click="queryDeptNum(scope.row)">
                  {{scope.row.deptNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.deptNum)==0" style="color:#000000">
                  {{scope.row.deptNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="标杆年份" prop="standardYear" align="right" width="80" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.standardYear | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="权重" prop="dipWt" align="right" width="100" :show-overflow-tooltip="true" sortable='custom'>
              <template slot-scope="scope">{{scope.row.dipWt | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="总权重"  align="right" width="110" :show-overflow-tooltip="true" prop="totalDipWeight" sortable='custom'>
              <template slot-scope="scope">{{scope.row.totalDipWeight | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院费用(本院)"  align="right" width="100" :show-overflow-tooltip="true" prop="avgCost" >
              <template slot-scope="scope">{{scope.row.avgCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院日(本院)"  align="right" width="100" :show-overflow-tooltip="true" prop="avgDays" >
              <template slot-scope="scope">{{scope.row.avgDays | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="标杆住院费用(同级)"  align="right" width="110" fixed="right" :show-overflow-tooltip="true" prop="peerBenchmarkCost">
              <template slot-scope="scope">{{scope.row.peerBenchmarkCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="标杆住院日(同级)"  align="right" width="100" :show-overflow-tooltip="true" prop="peerBenchmarkDays">
              <template slot-scope="scope">{{scope.row.peerBenchmarkDays | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="时间消耗指数(同级)"  align="right" width="100" :show-overflow-tooltip="true" prop="peerBenchmarkTimeIndex">
              <template slot-scope="scope">{{scope.row.peerBenchmarkTimeIndex | formatIsEmpty}}</template>no
            </el-table-column>
            <el-table-column label="费用消耗指数(同级)"  align="right" width="100" :show-overflow-tooltip="true" prop="peerBenchmarkCostIndex">
              <template slot-scope="scope">{{scope.row.peerBenchmarkCostIndex | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="标杆住院费用(区域)"  align="right" width="110" fixed="right" :show-overflow-tooltip="true" prop="areaCost">
              <template slot-scope="scope">{{scope.row.areaCost | formatIsEmpty}}</template>
            </el-table-column>
            <!--            <el-table-column label="标杆住院日(区域)"  align="right" width="100" :show-overflow-tooltip="true">-->
            <!--              <template slot-scope="scope">{{scope.row.areaDays | formatIsEmpty}}</template>-->
            <!--            </el-table-column>-->
            <el-table-column label="时间消耗指数(区域)"  align="right" width="100" :show-overflow-tooltip="true" prop="areaTimeIndex">
              <template slot-scope="scope">{{scope.row.areaTimeIndex | formatIsEmpty}}</template>no
            </el-table-column>
            <el-table-column label="费用消耗指数(区域)"  align="right" width="100" :show-overflow-tooltip="true" prop="areaCostIndex">
              <template slot-scope="scope">{{scope.row.areaCostIndex | formatIsEmpty}}</template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, querySelectTreeAndSelectList, queryLikeDipGroupByPram, queryDiagnosis } from '@/api/common/drgCommon'
import { fetchList as queryPageData, getCountInfo } from '@/api/dipBusiness/dipAnalysis'
import { elExportExcel } from '@/utils/exportExcel'
import { sortChange } from '@/utils/common'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  b16c: null,
  queryDrg: '',
  diagnosis: '',
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date
}
export default {
  name: 'groupAnalysis',
  inject: ['reload'],
  components: { },
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      list: null,
      tempList: [],
      ifComplicationGroupList: null,
      deptGroupList: null,
      total: null,
      cy_start_date: null,
      cy_end_date: null,
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      deptName: null,
      judgmentValue: false,
      tableHeight: 0,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      analysisLoading: true
    }
  },
  created () {
    if (!this.$somms.hasDeptRole()) {
      this.deptName = this.$store.getters.getDeptName
    }
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },

  methods: {
    sortChange,
    handleSelectionChange (val) {
      if (val.length == 0) {
        this.tempList = []
      } else {
        this.tempList = this.list.filter(data => {
          for (const v of val) {
            if (data.dipCodg == v.dipCodg) {
              return true
            }
          }
          return false
        })
      }
    },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
        this.getCount()
      })
    },
    // 下面表统计结果
    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryDrg = this.listQuery.dipCodg
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.deptCode = this.listQuery.b16c
      this.submitListQuery.dataAuth = true
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.tempList = this.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.dataTable.$children, document.getElementById('dipTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DIP支付预测')
    },
    // 上面两张表统计结果
    getCount () {
      this.analysisLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryDrg = this.listQuery.dipCodg
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      getCountInfo(this.submitListQuery).then(response => {
        this.analysisLoading = false
        this.ifComplicationGroupList = response.data
        this.deptGroupList = response.data
      })
    },
    // 下转
    queryMedicalNum (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      this.$router.push({ path: '/common/queryMedicalDetail',
        query: {
          queryDrgsCode: row.dipCodg,
          queryDrgsName: row.dipName,
          priOutHosDeptCode: this.submitListQuery.b16c,
          priOutHosDeptName: deptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          standardYear: row.standardYear,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '1'
        }
      })
    },
    queryDeptNum (row) {
      this.$router.push({ path: '/common/queryDeptDetail',
        query: {
          queryDrgsCode: row.dipCodg,
          queryDrgsName: row.dipName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '1'
        }
      })
    },
    queryMedicalNumByType (row, col) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      let queryDrgsNameStr = ''
      if (this.list.length == 1) {
        queryDrgsNameStr = this.list.dipName
      }
      let queryTypeStr = ''
      switch (col) {
        case 'col1': queryTypeStr = 'hxbz'; break
        case 'col2': queryTypeStr = 'zlxcz'; break
        case 'col3': queryTypeStr = 'zdxcz'; break
        case 'col4': queryTypeStr = 'xgss'; break
        case 'col5': queryTypeStr = 'bszl'; break
        case 'col6': queryTypeStr = 'hj'; break

        case 'col7': queryTypeStr = 'medicalDeptDrgsMedicalNum'; break
        case 'col8': queryTypeStr = 'notOperationDrgsMedicalNum'; break
        case 'col9': queryTypeStr = 'surgeryDeptDrgsMedicalNum'; break
      }
      this.$router.push({ path: '/common/queryMedicalDetail',
        query: {
          queryType: queryTypeStr,
          priOutHosDeptCode: this.submitListQuery.b16c,
          priOutHosDeptName: deptName,
          queryDrgsCode: this.submitListQuery.queryDrg,
          queryDrgsName: queryDrgsNameStr,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '1'
        }
      })
    },
    queryDrgGroupNumByType (row, col) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      let queryDrgsNameStr = ''
      if (this.list.length == 1) {
        queryDrgsNameStr = this.list.dipName
      }
      let queryTypeStr = ''
      switch (col) {
        case 'col1': queryTypeStr = 'hxbz'; break
        case 'col2': queryTypeStr = 'zlxcz'; break
        case 'col3': queryTypeStr = 'zdxcz'; break
        case 'col4': queryTypeStr = 'xgss'; break
        case 'col5': queryTypeStr = 'bszl'; break
        case 'col6': queryTypeStr = 'hj'; break

        case 'col7': queryTypeStr = 'medicalDeptDrgsDrgNum'; break
        case 'col8': queryTypeStr = 'notOperationDrgsDrgNum'; break
        case 'col9': queryTypeStr = 'surgeryDeptDrgsDrgsDrgNum'; break
      }
      this.$router.push({ path: '/common/queryDrgDetail',
        query: {
          queryType: queryTypeStr,
          priOutHosDeptCode: this.submitListQuery.b16c,
          priOutHosDeptName: deptName,
          queryDrgsCode: this.submitListQuery.queryDrg,
          queryDrgsName: queryDrgsNameStr,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '1'
        }
      })
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    queryDetails (row) {
      this.$router.push({
        path: '/auliManage/diseGrpCompar',
        query: {
          deptCode: this.listQuery.b16c,
          id: row.dipCodg,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1',
          queryType: (this.listQuery.b16c != null && this.judgmentValue) ? '1' : '3'
        }
      })
    },
    handleSelect (item) {
      this.listQuery.queryDrg = item.dipCodg
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
      this.getCount()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.judgmentValue = true
      this.listQuery.pageNum = 1
      if (this.listQuery.b16c) {
        this.deptName = document.getElementById('b13c').getElementsByTagName('input')[0].value
      } else {
        this.deptName = null
      }
      this.getList()
      this.getCount()
    },
    nodeClick () {
      this.judgmentValue = false
    },
    // 重置页面
    handleResetSearch () {
      // this.listQuery = Object.assign({}, defaultListQuery);
      // this.getDataIsuue();
      this.reload()
    },
    exportExcel () {
      let tableId = 'dipTable'
      let fileName = 'DIP病组分析'
      elExportExcel(tableId, fileName)
    },

    queryDiagnosisList (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryDiagnosis(param).then(response => {
        cb(response.data)
        this.$refs.diagnosisElAutocomplete.handleFocus()
      })
    },
    selectDiagnosis (item) {
      this.listQuery.diagnosis = item.diagCode
    }
  }
}
</script>
<style scoped>
  /deep/ .el-table__body td {
    padding: 0;
    height: 32px;
  }
  /deep/ .el-scrollbar__wrap {
    max-height: 450px;
  }
  /deep/ .el-autocomplete-suggestion li {
    line-height: 27px;
  }
  .code {
    font-size: 12px;
    color: #000000;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .name {
    font-size: 10px;
    color: #9b9b9b;
    text-overflow: ellipsis;
    overflow: hidden;
  }
</style>
