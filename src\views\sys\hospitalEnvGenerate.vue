<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
              :container="true"
              headerTitle="查询条件"
              contentTitle="待办列表"
              @query="queryData">
      <template slot="extendFormItems">
        <el-form-item label="年份">
          <el-date-picker
              v-model="queryForm.standardYear"
              align="left"
              style="width: 100%"
              type="year"
              value-format="yyyy"
              placeholder="选择年份">
          </el-date-picker>
        </el-form-item>
      </template>
      <!-- 按钮 -->
      <template slot="buttons">
        <el-button type="primary" class="som-button-margin-right" @click="generate">生成</el-button>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <hospital-env-generate-dialog :show="showGenerateDialog" @closed="showGenerateDialog = false"/>

        <!-- 任务列表 -->
        <div style="width: 100%;height: 100%;">
          <div style="">
            <div style="">
              <el-row :gutter="10">
                <el-col :span="8" v-for="(item, idx) in todo2List" :key="idx">
                  <el-card v-if="!item.tabName">
                    <div slot="header" class="clearfix">
                      <div style="display: flex;align-items: center;justify-content: space-between">
                        <div style="display: flex;align-items: center">
                          <div class="todo-item" style="border: 2px solid gray;" v-if="!item.complete"
                               @click="changeCompleteState(item)"></div>
                          <i class="som-icon-success som-icon-big todo-item" @click="changeCompleteState(item)"
                             v-else></i>
                          <span>{{ item.name }}</span>
                        </div>
                        <div style="font-size: 12px;color: rgb(192, 192, 192)">
                          {{ item.updtTime }}
                        </div>
                      </div>
                    </div>
                    <div style="padding: 10px">
                      {{ item.memo_info }}
                    </div>
                  </el-card>
                  <el-card v-else>
                    <!--                      <div slot="header" class="clearfix">-->
                    <!--                        <span>{{ item.name }}</span>-->
                    <!--                        <div style="">-->
                    <!--                          <i class="som-icon-error-waring som-icon-big" v-if="!item.complete"></i>-->
                    <!--                          <i class="som-icon-success som-icon-big" v-else></i>-->
                    <!--                        </div>-->
                    <!--                      </div>-->
                    <div slot="header" class="clearfix">
                      <div style="display: flex;align-items: center;">
                        <div style="display: flex;align-items: center;justify-content: space-between">
                          <span>{{ item.name }}</span>
                        </div>
                        <div style="display: flex;flex:1;align-items: center;justify-content: end;">
                          <i class="som-icon-error-waring som-icon-big" v-if="!item.complete"></i>
                          <i class="som-icon-success som-icon-big" v-else></i>
                        </div>
                        <div style="font-size: 12px;color: rgb(192, 192, 192)">
                          {{ item.updtTime }}
                        </div>
                      </div>
                    </div>

                    <div style="padding: 10px">
                      {{ item.tabName }}
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </div>

          <!--          <div style="height: 80%">-->
          <!--            <div style="width: 100%">-->
          <!--              <el-row :gutter="20">-->
          <!--                <el-col :span="8" v-for="(item, idx) in todoList" :key="idx">-->
          <!--                            <el-card>-->
          <!--                              <div slot="header" class="clearfix">-->
          <!--                                <span>{{ item.name }}</span>-->
          <!--                                <div style="float: right; padding: 3px 0">-->
          <!--                                  <i class="som-icon-error-waring som-icon-big" v-if="!item.complete"></i>-->
          <!--                                  <i class="som-icon-success som-icon-big" v-else></i>-->
          <!--                                </div>-->
          <!--                              </div>-->
          <!--                              <div style="padding: 10px">-->
          <!--                                {{ item.tabName }}-->
          <!--                              </div>-->
          <!--                            </el-card>-->
          <!--                </el-col>-->
          <!--              </el-row>-->
          <!--            </div>-->
          <!--          </div>-->
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import hospitalEnvGenerateDialog from './hospitalEnvGenerateComps/hospitalEnvGenerateDialog'
import {selectTableCount, updateConfirm, queryConfirm} from '@/api/hospitalEnvGenerate'

export default {
  name: 'hospitalEnvGenerate',
  components: {
    'hospital-env-generate-dialog': hospitalEnvGenerateDialog
  },
  data: () => ({
    queryForm: {
      standardYear: ''
    },
    tableData: [],
    showGenerateDialog: false,
    todoList: [
      {name: '住院医生信息', tabName: 'som_medstff_info', complete: false},
      {name: '科室信息', tabName: 'som_dept', complete: false},
      {name: '医生编码对照', tabName: 'som_hi_dr_crsp', complete: false},
      {name: '护士编码对照', tabName: 'som_hi_nurs_codg_crsp', complete: false},
      {name: '当前年度标杆', tabName: 'som_hi_nurs_codg_crsp', complete: false, code: 'year'},
      {name: '当前年度som_std_fee表', tabName: 'som_std_fee', complete: false, code: 'year'},
      {name: '清单上传表信息', tabName: 'som_setl_invy_upld_cfg', complete: false, code: 'sel'},
      {name: 'som_drg_name表', tabName: 'som_drg_name', complete: false}
    ],
    todo2List: []
  }),
  mounted() {
    this.queryForm.standardYear = this.$somms.getDate('yyyy', 0, 0, 0)
    this.todoList[4].tabName = this.$somms.isDIP() ? 'som_dip_standard' : 'som_drg_standard'
    this.queryData()
  },
  methods: {
    queryData() {
      selectTableCount({todoList: this.todoList}).then(res => {
        this.todoList = res.data
      })
      queryConfirm({}).then(res => {
        this.todo2List = res.data
        //pushall todoList
        this.todoList.forEach(v => {
          this.todo2List.push(v)
        })
      })
    },
    range(item) {
      if (item && item.days) {
        if (item.specialFlag == '1') {
          if (item.days <= 90) {
            return 'steady'
          }
          if (item.days > 90 && item.days < 180) {
            return 'warning'
          }
          if (item.days >= 180) {
            return 'seriousness'
          }
        } else {
          if (item.days <= 180) {
            return 'steady'
          }
          if (item.days > 180 && item.days < 365) {
            return 'warning'
          }
          if (item.days >= 365) {
            return 'seriousness'
          }
        }
      }
      return 'steady'
    },
    // 生成
    generate() {
      this.showGenerateDialog = true
    },
    changeCompleteState(item) {
      item.complete = !item.complete
      updateConfirm({...item, complete: item.complete ? '1' : '0'}).then(res => {
        this.$message.success('修改状态成功')
        this.queryData()
      })
    }
  }
}
</script>
<style scoped>
.dcew-query-button {
  width: 5rem;
}

.dcew-content-box {
  height: 34%;
  width: 24.5%;
  display: inline-block;
}

.dcew-content-box-title {
  width: 100%;
  height: 10%;
  padding: 1rem;
  font-size: var(--biggerSize);
  font-weight: bold;
}

.dcew-content-box-item {
  /*margin: 1.5rem 2rem 1rem 1rem;*/
  margin: 4%;
  width: 40%;
  display: inline-block;
  text-align: center;
}

.dcew-content-box-item-title {
  color: gray;
  font-size: var(--biggerSmallSize);
  margin-bottom: 0.5rem
}

.dcew-content-box-item-val {
  font-size: var(--textSize);
  font-weight: bold;
}

.dcew-content-box-item-end {
  width: 30%;
  height: 100%;
  margin: 1.5rem 0 0 0.5rem;
  text-align: center;
  display: inline-block
}

.dcew-content-box-item-end-title {
  margin-bottom: 1rem;
  color: gray;
  font-size: var(--biggerSmallSize)
}

.dcew-content-box-item-end-val {
  cursor: pointer;
  text-decoration: underline;
  font-size: var(--margin);
}

.dcew-content-box-item-end-val:hover {
  color: rgb(77, 162, 255);
  text-decoration: underline;
}

.seriousness {
  /*<!-- rgba(246,114,114,0.5) #f67272 -->*/
  background: linear-gradient(to bottom, rgba(246, 114, 114, 0.5), white);
}

.warning {
  /*<!-- e7a646 rgba(231,166,70,0.5) -->*/
  background: linear-gradient(to bottom, rgba(231, 166, 70, 0.5), white);
}

.steady {
  /** rgba(77,162,255,0.5) 4da2ff*/
  background: linear-gradient(to bottom, rgba(77, 162, 255, 0.5), white);
}

.surplus {
  /** rgba(111,196,68,0.5) 6fc444*/
  background: linear-gradient(to bottom, rgba(111, 196, 68, 0.5), white);
}

.decw-query-check-yz {
  background-color: rgb(246, 114, 114);
  color: white;
}

.decw-query-uncheck-yz {
  background-color: rgba(246, 114, 114, 0.5);
  color: white
}

.decw-query-check-cz {
  background-color: rgb(231, 166, 70);
  color: white;
}

.decw-query-uncheck-cz {
  background-color: rgba(231, 166, 70, 0.5);
  color: white
}

.decw-query-check-cp {
  background-color: rgb(77, 162, 255);
  color: white;
}

.decw-query-uncheck-cp {
  background-color: rgba(77, 162, 255, 0.5);
  color: white
}

.decw-query-check-jy {
  background-color: rgb(111, 196, 68);
  color: white;
}

.decw-query-uncheck-jy {
  background-color: rgba(111, 196, 68, 0.5);
  color: white
}

.decw-query-check-yz-mouseover {
  border: 1px solid rgb(246, 114, 114);
}

.decw-query-check-cz-mouseover {
  border: 1px solid rgb(231, 166, 70);
}

.decw-query-check-cp-mouseover {
  border: 1px solid rgb(77, 162, 255);
}

.decw-query-check-jy-mouseover {
  border: 1px solid rgb(111, 196, 68);
}

.dcew-content-box-left {
  margin-left: 0.5%;
}

.todo-item {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 10px;
  cursor: pointer;
}
</style>
