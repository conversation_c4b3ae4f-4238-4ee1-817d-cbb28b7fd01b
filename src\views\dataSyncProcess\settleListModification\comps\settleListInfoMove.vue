<template>
  <div>
    <el-button type="primary" icon="el-icon-top" @click="move(true)">上移</el-button>
    <el-button type="primary" @click="move(false)">下移<i class="el-icon-bottom el-icon--right"></i></el-button>
    <el-button type="primary" @click="add">新增<i class="el-icon-circle-plus-outline el-icon--right"></i></el-button>
    <el-button type="danger" @click="remove">删除<i class="el-icon-remove-outline el-icon--right"></i></el-button>
  </div>
</template>
<script>
export default {
  props: {
    checkTableData: [Array],
    data: [Array]
  },
  data: () => ({
    tableData: [],
    log: []
  }),
  methods: {
    // 移除
    remove () {
      if (this.checkTableData.length > 0) {
        this.log.push('移除数据')
        this.checkTableData.map(item => {
          let index = this.tableData.indexOf(item)
          this.tableData.splice(index, 1)
          this.$emit('change', { data: this.tableData })
        })
      }
    },
    // 新增
    add () {
      this.log.push('新增数据')
      this.tableData.push({ seq: this.getSno(), id: this.getId() })
      this.$emit('change', { data: this.tableData })
    },
    getSno () {
      if (this.tableData.length > 0) {
        return this.tableData.length
      }
      return 0
    },
    getId () {
      let id = 0
      if (this.tableData.length > 0) {
        this.tableData.forEach(item => {
          if (item.id > id) {
            id = item.id
          }
        })
        id++
      }
      return id
    },
    // 上移/下移
    move (flag) {
      if (this.checkTableData.length === this.tableData.length) {
        return
      }
      if (this.checkTableData.length > 0) {
        let indexData = this.getIndex(this.checkTableData, flag)
        let tempData = JSON.parse(JSON.stringify(this.tableData))
        this.tableData = []
        let tempIds = []
        indexData.forEach(item => {
          this.log.push('数据' + (item.oldIndex + 1) + '移动到数据' + (item.newIndex + 1))
          let data1 = tempData[item.oldIndex]
          let data2 = tempData[item.newIndex]
          tempData[item.newIndex] = data1
          tempData[item.oldIndex] = data2
          tempIds.push(data1.id)
        })
        this.tableData = tempData
        let modifyData = []
        this.tableData.forEach(item => {
          if (tempIds.includes(item.id)) {
            modifyData.push(item)
          }
        })
        this.$emit('change', { data: this.tableData, modifyData })
      }
    },
    getIndex (data, flag) {
      let indexData = []
      if (this.tableData.length > 0) {
        for (let i = 0; i < this.tableData.length; i++) {
          let v = this.tableData[i]
          data.forEach(item => {
            if (v.id === item.id) {
              indexData.push({
                oldIndex: i,
                newIndex: flag ? (i === 0 ? i : i - 1) : (i === this.tableData.length - 1 ? i : i + 1)
              })
            }
          })
        }
      }
      // 如果是向下，并且存在有连续时就会错乱，需要反转数组
      if (!flag) {
        indexData = indexData.reverse()
      }
      return indexData
    },
    // 移除日志
    clearLog () {
      this.log = []
    }
  },
  watch: {
    data: {
      immediate: true,
      handler: function (val) {
        this.tableData = val
      }
    },
    log (val) {
      this.$emit('moveLog', val)
    }
  }
}
</script>
