import request from '@/utils/request'

export function save (params) {
  return request({
    url: '/settleListDict/save',
    method: 'post',
    params: params
  })
}

export function refreshCache (data) {
  return request({
    url: '/settleListDict/refreshCache',
    method: 'post',
    data: data
  })
}

export function batchDelete (data) {
  return request({
    url: '/settleListDict/delete',
    method: 'post',
    data: data
  })
}

export function queryPage (data) {
  return request({
    url: '/settleListDict/queryPage',
    method: 'post',
    data: data
  })
}
