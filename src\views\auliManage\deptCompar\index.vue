<template>
  <div class="app-container">
    <drg-container :headerPercent="10.5">
      <template slot="header">
        <drg-title-line title="查询条件" />
        <el-form :inline="true" :model="listQuery" size="mini" :rules="rules">
          <el-form-item label="出院时间">
            <el-date-picker
              v-model="listQuery.cysj"
              type="daterange"
              size="mini"
              start-placeholder="开始日期"
              range-separator="-"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              @change="dateChangeCysj">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="出院科别" class="som-el-form-item-margin-left som-department-height">
            <drg-department v-model="listQuery.deptCode"
                           placeholder="请选择科室"
                           @changeDept="clearDept"
                           :disabled="this.listQuery.radio == '1' && !this.$somms.hasDeptRole()" />
          </el-form-item>
          <el-form-item label="DIP组" :prop="this.props1" v-if="this.bool && this.props1 == 'dipGroup'">
            <el-autocomplete
              class="som-form-item"
              v-model="listQuery.dipGroup"
              :fetch-suggestions="querySearchAsync"
              placeholder="请输入DIP组编码或名称"
              @select="fnDipGroupSelect"
              :popper-append-to-body="true"
              :clearable="true"
              :trigger-on-focus="false"
              ref="elautocomplete">
              <template slot-scope="{ item }">
                <div class="code">{{ item.dipCodg }}</div>
                <span class="name">{{ item.dipName }}</span>
              </template>
            </el-autocomplete>
          </el-form-item>

          <el-form-item label="DRGs组" :prop="this.props3" v-if="this.bool && this.props3 == 'queryDrg'">
            <el-autocomplete
              popper-class="my-autocomplete"
              size="mini"
              v-model="listQuery.queryDrg"
              :fetch-suggestions="queryDRGSearchAsync"
              placeholder="请输入DRGs编码或名称"
              @select="handleDRGSelect"
              :popper-append-to-body="true"
              :clearable="true"
              :trigger-on-focus="false"
              ref='DRGelautocomplete' class="som-form-item">
              <template slot-scope="{ item }">
                <div class="code">{{ item.drgsCode }}</div>
                <span class="name">{{ item.drgsName }}</span>
              </template>
            </el-autocomplete>
          </el-form-item>

          <el-form-item label="组" prop="group">
            <el-select  v-model="group"
                        :disabled="dld"
                        placeholder="请选择组"
                        class="som-form-item">
<!--              @change="getList(null,true)"-->
              <!--              multiple-->
              <el-option
                v-for="item in groupOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
<!--              <el-option label="测试" value="1">-->

<!--              </el-option>-->
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-radio-group v-model="listQuery.radio" @change="radioChange">
              <el-radio label="1" class="som-el-form-item-margin-left">左页面</el-radio>
              <el-radio label="2">右页面</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button
              @click="handleSearchList()"
              type="primary"
              size="mini"
              class="som-el-form-item-margin-left">
              查询结果
            </el-button>
            <el-button @click="refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="对比分析" />
        <div style="height: auto;width: 100%;display: flex;flex-direction: row">
          <el-empty v-show="leftEmpty"
                    style="position: absolute;top: 25%;left: 20%"
                    :description="leftDescription"></el-empty>
          <div class="content-item">
            <dept-compar-analysis-description
                      :data="leftData"
                      :drgData="leftDrgData"
                      :cost="leftCost"
                      :pay-cost="leftPayCostData"
                      :otherPayCost="rightDrgPayCostData"
                      :otherDrgPayCost="rightDrgPayCostData"
                      :drg-pay-cost="leftDrgPayCostData"
                      v-show="!leftDataShow" />
          </div>
          <div style="width: 4%" :style="splitStyle">
            <div style="height: 100%;background-color: gray;width: 0.15rem;position: absolute;left: 50%"></div>
          </div>
          <el-empty v-show="rightEmpty"
                    style="position: absolute;top: 25%;left: 70%"
                    :description="rightDescription"></el-empty>
          <div class="content-item">
            <dept-compar-analysis-description
                      :data="rightData"
                      :drgData="rightDrgData"
                      :cost="rightCost"
                      :pay-cost="rightPayCostData"
                      :otherDrgPayCost="leftDrgPayCostData"
                      :drg-pay-cost="rightDrgPayCostData"
                      :otherPayCost="leftPayCostData"
                      v-show="!rightDataShow" />
                  </div>
        </div>
      </template>
    </drg-container>
  </div>
</template>
<script>
import { queryEnableGroup } from '@/api/common/sysCommon'
import { queryDataIsuue, queryLikeDipGroupByPram, queryLikeDrgsByPram } from '@/api/common/drgCommon'
import { getData, getCost, getDrgData, getPayCostData, getDrgPayCostData } from '@/api/medicalQuality/deptComparAnalysis'
import DeptComparAnalysisDescription from './components/deptComparAnalysisDescription'

export default {
  name: 'deptCompar',
  components: { DeptComparAnalysisDescription },
  inject: ['reload'],
  data () {
    return {
      cy_start_date: null,
      cy_end_date: null,
      listQuery: {
        cysj: null,
        deptCode: '',
        radio: '1',
        dipGroup: '',
        queryDrg: ''

      },
      splitStyle: {
        height: '',
        position: 'relative'
      },
      rules: {
        dipGroup: [
          { required: true, message: '该处为必填项', trigger: ['blur', 'change'] }
        ],
        queryDrg: [
          { required: true, message: '该处为必填项', trigger: ['blur', 'change'] }
        ],
        group: [
          { required: true, message: '该处为必填项', trigger: 'blur' }
        ]
      },
      bool: false,
      group: [],
      leftData: { },
      leftDrgData: { },
      rightData: { },
      rightDrgData: { },
      leftCost: {},
      rightCost: {},
      leftPayCostData: { },
      leftDrgPayCostData: {},
      rightPayCostData: { },
      rightDrgPayCostData: {},
      groupOptions: [],
      leftDataShow: true,
      rightDataShow: true,
      leftEmpty: true,
      rightEmpty: true,
      leftDescription: '请选择科室或编码',
      rightDescription: '请选择科室或编码',
      index: 0,
      props1: '',
      props3: '',
      dld: false
    }
  },
  created () {
    // 获取数据查询时间
    this.getDataIsuue()
    this.getEnabledGroup()
    this.getList(1)
  },
  methods: {
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        if (!this.$somms.hasDeptRole() && this.listQuery.radio == '1') {
          this.listQuery.dataAuth = true
          this.listQuery.deptCode = this.$store.getters.getDeptCode
          this.getList(1)
        }
        if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
          this.cy_start_date = this.$route.query.cy_start_date
          this.cy_end_date = this.$route.query.cy_end_date
          Object.assign(this.listQuery, { cysj: [this.cy_start_date, this.cy_end_date] })
        }
      })
    },
    radioChange () {
      if (this.listQuery.radio == '2') {
        this.dld = true
        this.listQuery.dataAuth = false
        if (!this.$somms.hasDeptRole() && Object.keys(this.rightData).length > 1) {
          this.listQuery.deptCode = this.rightData.deptCode
        }
        if (Object.keys(this.rightData).length > 1) {
          this.listQuery.deptCode = this.rightData.deptCode
        } else if (Object.keys(this.rightData).length == 1) {
          if (this.listQuery.deptCode == this.leftData.deptCode) {
            this.listQuery.deptCode = ''
          }
        }
      } else if (this.listQuery.radio == '1') {
        this.dld = false
        if (!this.$somms.hasDeptRole()) {
          this.listQuery.deptCode = this.$store.getters.getDeptCode
        } else {
          if (Object.keys(this.leftData).length > 1) {
            this.listQuery.deptCode = this.leftData.deptCode
          } else {
            if (this.listQuery.deptCode == this.rightData.deptCode) {
              this.listQuery.deptCode = ''
            }
          }
        }
      }
    },
    getEnabledGroup () {
      queryEnableGroup().then(res => {
        if (res.code == 200) {
          this.groupOptions = res.data
        }
      })
    },
    handleSearchList () {
      this.resetSearch()
      this.getList(this.listQuery.radio)
    },
    setGroupShow (index, flag) {
      if (index == 1) {
        this.$set(this.leftData, 'typeData', flag)
        this.$set(this.rightData, 'typeData', flag)
      } else if (index == 2) {
        this.$set(this.leftDrgData, 'typeData', flag)
        this.$set(this.rightDrgData, 'typeData', flag)
      } else if (index == 3) {
        this.$set(this.leftPayCostData, 'typeData', flag)
        this.$set(this.rightPayCostData, 'typeData', flag)
      } else if (index == 4) {
        this.$set(this.leftDrgPayCostData, 'typeData', flag)
        this.$set(this.rightDrgPayCostData, 'typeData', flag)
      }
    },
    clearDept () {
      if (this.listQuery.radio == '1' && Object.keys(this.leftData).length == 1) {
        if ((this.listQuery.dipGroup == '' && this.listQuery.dipGroup == null) || (this.listQuery.queryDrg == '' && this.listQuery.queryDrg == null)) { this.leftDescription = '未选择编码' } else {
          this.leftDescription = '请选择科室'
        }
      }
      if (this.listQuery.radio == '2' && Object.keys(this.rightData).length == 1) {
        if ((this.listQuery.dipGroup == '' && this.listQuery.dipGroup == null) || (this.listQuery.queryDrg == '' && this.listQuery.queryDrg == null)) { this.leftDescription = '未选择编码' } else {
          this.rightDescription = '请选择科室'
        }
      }
    },
    setDescription (data) {
      if (this.listQuery.deptCode && !data) {
        if (this.listQuery.radio == '1') {
          this.leftDescription = '暂无数据'
        }
        if (this.listQuery.radio == '2') {
          this.rightDescription = '暂无数据'
        }
      }
      if (this.listQuery.deptCode && (this.listQuery.dipGroup == '' || this.listQuery.queryDrg == '')) {
        if (this.listQuery.radio == '1') {
          this.leftDescription = '未选择编码'
        }
        if (this.listQuery.radio == '2') {
          this.rightDescription = '未选择编码'
        }
      }
    },
    getList (index, group_change = false) {
      let params = this.getParams()
      // if(this.$route.query.type){
      //   if (!group_change){
      //     this.group= this.$route.query.type;
      //   }
      // }
      if (this.group == 1) {
        params.queryDrg = ''
      }
      if (this.group == 3) {
        params.dipGroup = ''
      }
      if (this.group == 1 && (this.listQuery.dipGroup == '' || this.listQuery.dipGroup == null)) {} else if (this.group == 3 && (this.listQuery.queryDrg == '' || this.listQuery.queryDrg == null)) {} else {
        if (params.deptCode) {
          if (this.group == 1) {
            getData(params).then(response => {
              if (index == 1) {
                if (response.data) {
                  this.leftDataShow = false
                  this.leftData = response.data
                  this.leftEmpty = false
                }
              } else if (index == 2) {
                if (response.data) {
                  this.rightDataShow = false
                  this.rightData = response.data
                  this.rightEmpty = false
                }
              }
              if (this.group.includes('1')) {
                this.setGroupShow(1, false)
                this.setGroupShow(2, true)
              } else {
                if (this.group.includes('3')) {
                  this.setGroupShow(1, true)
                } else {
                  this.setGroupShow(1, false)
                }
              }
              if (response.data == undefined) {
                if (index == 1) {
                  this.leftDataShow = true
                  this.leftEmpty = true
                } else if (index == 2) {
                  this.rightDataShow = true
                  this.rightEmpty = true
                }
              }
              this.$nextTick(() => {
                this.setDescription(response.data)
              })
            })
          }
          getCost(params).then(response => {
            if (response.data) {
              if (index == 1) {
                this.leftCost = response.data
              } else if (index == 2) {
                this.rightCost = response.data
              }
            }
          })
          if (this.group == 3) {
            getDrgData(params).then(response => {
              if (index == 1) {
                if (response.data) {
                  this.leftDataShow = false
                  this.leftDrgData = response.data
                  this.leftEmpty = false
                }
              } else if (index == 2) {
                if (response.data) {
                  this.rightDataShow = false
                  this.rightDrgData = response.data
                  this.rightEmpty = false
                }
              }
              if (this.group.includes('3')) {
                this.setGroupShow(2, false)
                this.setGroupShow(1, true)
              } else {
                if (this.group.includes('1')) {
                  this.setGroupShow(2, true)
                } else {
                  this.setGroupShow(2, false)
                }
              }
              if (response.data == undefined) {
                if (index == 1) {
                  this.leftDataShow = true
                  this.leftEmpty = true
                } else if (index == 2) {
                  this.rightDataShow = true
                  this.rightEmpty = true
                }
              }
            })
          }
          this.index++
          getPayCostData(params).then(response => {
            if (response.data) {
              if (index == 1) {
                this.leftPayCostData = response.data
              } else if (index == 2) {
                this.rightPayCostData = response.data
              }
              this.$set(this.leftPayCostData, index, this.index)
              this.$set(this.leftPayCostData, index, this.index)
              if (this.group.includes('1')) {
                this.setGroupShow(3, false)
              } else {
                if (this.group.includes('3')) {
                  this.setGroupShow(3, true)
                } else {
                  this.setGroupShow(3, false)
                }
              }
            }
          })
          getDrgPayCostData(params).then(response => {
            if (response.data) {
              if (index == 1) {
                this.leftDrgPayCostData = response.data
              } else if (index == 2) {
                this.rightDrgPayCostData = response.data
              }
              this.$set(this.leftDrgPayCostData, index, this.index)
              this.$set(this.rightDrgPayCostData, index, this.index)
              if (this.group.includes('3')) {
                this.setGroupShow(4, false)
              } else {
                if (this.group.includes('1')) {
                  this.setGroupShow(4, true)
                } else {
                  this.setGroupShow(4, false)
                }
              }
            }
          })
        } else {
          if (index == 1) {
            this.leftDataShow = true
            this.leftEmpty = true
          } else if (index == 2) {
            this.rightDataShow = true
            this.rightEmpty = true
          }
        }
      }
    },
    // 清空下转内容
    resetSearch () {
      if (this.$route.query) {
        this.$router.push({ query: {} }).catch(() => {})
      }
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
    },
    refresh () {
      this.resetSearch()
      this.reload()
    },

    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    fnDipGroupSelect (item) {
      this.listQuery.dipGroup = item.dipCodg
    },
    queryDRGSearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.DRGelautocomplete.handleFocus()
      })
    },
    handleDRGSelect (item) {
      this.listQuery.queryDrg = item.drgsCode
    },
    groupChange () {
      if (this.group != '' || this.group != null) {
        this.bool = true
        if (this.group == 1) {
          this.props1 = 'dipGroup'
          this.props3 = ''
        }
        if (this.group == 3) {
          this.props3 = 'queryDrg'
          this.props1 = ''
        }
      }
    },

    getParams () {
      let params = {}
      if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
        Object.assign(this.listQuery, { cysj: [this.$route.query.cy_start_date, this.$route.query.cy_end_date] })
        this.listQuery.begnDate = this.$route.query.cy_start_date
        this.listQuery.expiDate = this.$route.query.cy_end_date
      }
      if (this.$route.query.priOutHosDeptCode) {
        this.listQuery.deptCode = this.$route.query.priOutHosDeptCode
      }
      if (this.$route.query.type) {
        this.group = this.$route.query.type
      }
      if (this.$route.query.dipGroup) {
        this.listQuery.dipGroup = this.$route.query.dipGroup
      }

      Object.assign(params, this.listQuery)

      if (this.listQuery.cysj) {
        params.begnDate = this.listQuery.cysj[0]
        params.expiDate = this.listQuery.cysj[1]
      } else {
        params.begnDate = ''
        params.expiDate = ''
      }
      params.group = this.group
      return params
    }
  },
  watch: {
    radio: function () {
      this.radioChange()
    },
    group: function () {
      this.groupChange()
    }
  }
}
</script>
<style scoped>
.content-item {
  width: 48%;
}
</style>
