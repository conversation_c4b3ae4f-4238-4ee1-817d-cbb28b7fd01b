<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
              :show-hos-dept="{ show: this.$somms.hasHosRole()}"
              showPagination
              :totalNum="total"
              headerTitle="查询条件"
              contentTitle="元素"
              :container="true"
              :initTimeValueNotQuery="false"
              ref="somForm"
              @query="handleSearchList">

      <template slot="extendFormItems">

        <el-form-item label="场景类型" prop="ruleScenType">
          <drg-dict-select dicType="RULE_SCEN_TYPE" placeholder="请选择场景类型" v-model="listQuery.ruleScenType"
                           @change="getDataIsuue"/>
        </el-form-item>

        <el-form-item label="明细名称" prop="dataName">
          <el-input v-model="listQuery.dataName" placeholder="请输入明细名称" @change="getDataIsuue"/>
        </el-form-item>
        <el-form-item label="数据元组ID" prop="dataGrpCode">
          <el-input v-model="listQuery.dataGrpCode" placeholder="请输入数据元组ID" @change="getDataIsuue"/>
        </el-form-item>

        <el-form-item label="细分组编码" prop="dataDetailCode">
          <el-input v-model="listQuery.dataDetailCode" placeholder="请输入细分组编码" @change="getDataIsuue"/>
        </el-form-item>

        <el-form-item label="明细编码" prop="dataCode">
          <el-input v-model="listQuery.dataCode" placeholder="请输入数据分组" @change="getDataIsuue"/>
        </el-form-item>

        <el-form-item label="规则年度" prop="ruleYear">
          <el-date-picker
              v-model="listQuery.ruleYear"
              type="year"
              placeholder="请输入规则年度"
              format="yyyy"
              @change="getDataIsuue"
              value-format="yyyy">
            </el-date-picker>
          <!-- <el-input v-model="listQuery.ruleYear" placeholder="请输入规则年度" @change="getDataIsuue"/> -->
        </el-form-item>
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  :id="tableId"
                  size="mini"
                  stripe
                  height="100%"
                  :data="list"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>
          <el-table-column label="序号" type="index" align="right">
          </el-table-column>
          <el-table-column label="规则年份" prop="ruleYear" align="center"/>
          <el-table-column label="数据元组ID" prop="dataGrpCode" align="center"/>
          <el-table-column label="细分组编码" prop="dataDetailCode" align="center"/>
          <el-table-column label="明细编码" prop="dataCode" align="center"/>
          <el-table-column label="明细名称" prop="dataName" width="300px" show-overflow-tooltip align="left"/>
        </el-table>
      </template>
    </drg-form>
  </div>
</template>
<script>
import {queryTupleList as queryPageData} from '@/api/examCorrection/ruleAndTuples'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  ruleScenType: '1',
  dataName: null,
  dataGrpCode: null,
  dataDetailCode: null,
  dataCode: null,
  ruleYear: null
}
export default {
  name: 'ruleTuples',
  components: {},
  inject: ['reload'],
  data() {
    return {
      showFlag: false,
      dialogVisible: false,
      tableId: 'dataTable',
      listLoading: true,
      list: null,
      total: null,
      dictVoList: {},
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
    }
  },

  watch: {
    list: function () {
      this.$nextTick(() => {
        if (this.$refs.settleListTable) {
          this.$refs.settleListTable.doLayout()
        }
      })
    }
  },

  mounted() {
    this.$nextTick(() => {

      this.getList()
    })
  },
  methods: {

    getDataIsuue() {
      this.listLoading = false
      this.getList()

    },
    getList() {
      this.listLoading = true
      this.getParamByBaseQuery(this.submitListQuery, this.listQuery)
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    handleSelect(item) {
      this.listQuery.dipCodg = item.dipCodg
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList() {
      this.getList()
    },
    refresh() {
      this.reload()
    },
    getParamByBaseQuery(targetParam, sourceParam) {
      if (targetParam) {
        const keys = Object.keys(targetParam)
        // 更新参数值
        keys.forEach(key => {
          targetParam[key] = sourceParam[key]
        })
      }
    },
    getParams() {
      return this.submitListQuery
    }
  }
}
</script>
<style scoped>
/*时间样式设置*/
/deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner {
  width: 200px;
}

.autoSelectInputWidth {
  width: 178px;
}

</style>
