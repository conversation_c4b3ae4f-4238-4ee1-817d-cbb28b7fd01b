import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken, removeMenuTree} from '@/utils/auth'
import {setCookie} from '@/utils/support';

const user = {
  state: {
    token: getToken(),
    name: '',
    nickname: '',
    id: '',
    avatar: '',
    roles: [],
    perms: [],//授权标识
    routers:[],
    weekPassWord: false,
    switchState: "0",
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMS: (state, perms) => {  // 用户权限标识集合
      state.perms = perms;
    },
    SET_ROUTERS: (state, routers) => {  // 保存路由
      state.routers = routers;
    },
    SET_NICKNAME: (state, nickname) => {
      state.nickname = nickname
    },
    SET_ID: (state, id) => {
      state.id = id
    },
    SET_WEEKPASSWORD: (state,val) => {
      state.weekPassWord = val
    },
    SET_SWITCHSTATE: (state,val) => {
      state.switchState = val
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      return new Promise((resolve, reject) => {
        login(username, userInfo.password, userInfo.hospitalId, userInfo.token).then(response => {
          if(username == 'developer') {
            commit('SET_WEEKPASSWORD',false)
          } else {
            let isWeek = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,18}$/;
            // 外部系统调用
            if(!userInfo.token){
              if(!isWeek.test(userInfo.password)) {
                commit('SET_WEEKPASSWORD',true)
              } else{
                commit('SET_WEEKPASSWORD',false)
              }
            }
          }
          const data = response.data
          const tokenStr = data.tokenHead+data.token
          setToken(tokenStr)
          setCookie("username", username, 15);
          commit('SET_TOKEN', tokenStr)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(response => {
          const data = response.data
          if (data.roles && data.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', data.roles)
          } else {
            reject('getInfo: roles must be a non-null array !')
          }
          commit('SET_NAME', data.username)
          commit('SET_ID', data.id)
          commit('SET_NICKNAME', data.nickname)
          commit('SET_AVATAR', data.icon)
          commit('SET_SWITCHSTATE',data.switchState)
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 登出
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          removeToken()
          removeMenuTree()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        commit('SET_PERMS', [])
        removeMenuTree()
        removeToken()
        resolve()
      })
    },
    //异步设置权限
    setPerms({commit},perms){
      return new Promise(resolve => {
        commit('SET_PERMS', perms)
        resolve()
      })
    },
    //异步设置路由
    setRouters({commit},routers){
      return new Promise(resolve => {
        commit('SET_ROUTERS', routers)
        resolve()
      })
    }
  }
}

export default user
