<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
              :container="true"
              :totalNum="total"
              show-pagination
              show-issue
              headerTitle="查询条件"
              contentTitle="区域标杆配置"
              @query="getData">

      <template slot="extendFormItems">
<!--        <el-form-item label="期号" prop="ym">-->
<!--          <el-date-picker-->
<!--            v-model="queryForm.ym"-->
<!--            type="month"-->
<!--            value-format="yyyy-MM"-->
<!--            placeholder="选择期号"-->
<!--            style="width: 180px"-->
<!--          >-->
<!--          </el-date-picker>-->
<!--        </el-form-item>-->
        <el-form-item
          prop="value"
          label="配置键">
          <el-input placeholder="配置键" v-model="queryForm.configKey"></el-input>
        </el-form-item>
        <el-form-item label="值" size="mini">
          <el-input placeholder="配置值" v-model="queryForm.configValue"></el-input>
        </el-form-item>
      </template>
      <template slot="buttons">
<!--        <el-button @click="addPriceCfg">新 增</el-button>-->
        <el-button type="primary" @click="addPriceCfg" class="som-button-margin-right"><i
          class="el-icon-plus el-icon--left"></i>新 增
        </el-button>
        <el-button type="primary" @click="feeCalculationCfg" class="som-button-margin-right">费用计算
        </el-button>
      </template>

      <drg-container :headerPercent="11"></drg-container>
      <!--        <template slot="header">-->
      <!--          <drg-title-line title="查询条件"/>-->
      <!--          <el-form :inline="true" :model="queryForm" size="mini">-->
      <!--            <el-form-item-->
      <!--              prop="value"-->
      <!--              label="配置键">-->
      <!--              <el-input placeholder="配置键" v-model="queryForm.configKey"></el-input>-->
      <!--            </el-form-item>-->
      <!--            <el-form-item label="值" size="mini">-->
      <!--              <el-input placeholder="配置值" v-model="queryForm.configValue"></el-input>-->
      <!--            </el-form-item>-->
      <!--            &lt;!&ndash;选择期号&ndash;&gt;-->
      <!--            <el-form-item label="期号" prop="ym">-->
      <!--              <el-date-picker-->
      <!--                v-model="queryForm.ym"-->
      <!--                type="month"-->
      <!--                value-format="yyyy-MM"-->
      <!--                placeholder="选择期号">-->
      <!--              </el-date-picker>-->
      <!--            </el-form-item>-->
      <!--            <el-button type="primary" @click="getData">查 询</el-button>-->
      <!--            <el-button @click="refresh">刷 新</el-button>-->
      <!--            <el-button @click="addPriceCfg">新 增</el-button>-->
      <!--          </el-form>-->
      <!--        </template>-->

      <template slot="containerContent">
        <el-tabs v-model="tab" @tab-click="DrgDip">
          <el-tab-pane label="DIP" name="1"></el-tab-pane>
          <el-tab-pane label="DRG" name="2"></el-tab-pane>
        </el-tabs>
          <el-table :data="tableData"
                    border
                    highlight-current-row
                    size="mini"
                    v-loading="tableLoading"
                    style="width: 100%; height: 100%; overflow-y: auto;">
            <el-table-column
              prop="id"
              label="ID" width="60">
            </el-table-column>
            <el-table-column
              prop="ym"
              label="期号">
            </el-table-column>
            <el-table-column
              prop="key"
              label="键">
            </el-table-column>
            <el-table-column
              prop="value"
              label="值">
            </el-table-column>
            <el-table-column
              prop="type"
              label="类型">
            </el-table-column>
            <el-table-column
              prop="description"
              label="描述">
            </el-table-column>
            <el-table-column
              prop="insuplcAdmdvs"
              label="参保地类型" >
            </el-table-column>
            <el-table-column label="操作" width="200" align="center">
              <template #default="scope">
                <el-tooltip class="item" effect="dark" content="复制" placement="top-start">
                  <el-button
                    size="mini"
                    icon="el-icon-document-copy"
                    type="primary"
                    circle
                    @click="copyVal2Add(scope.$index, scope.row)"
                  ></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="编辑" placement="top-start">
                  <el-button
                    size="mini"
                    icon="el-icon-edit"
                    type="primary"
                    circle
                    @click="handleEdit(scope.$index, scope.row)"></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
                  <el-button type="danger"
                             icon="el-icon-delete"
                             circle
                             @click="deleteDipOrDrg(scope.$index, scope.row)"></el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <!-- 编辑 -->
          <el-dialog
            title="编辑"
            ref="editForm"
            width="30%"
            :visible.sync="editVisible">
            <el-form :model="editForm" size="mini" label-width="auto">
              <el-form-item label="期号" prop="ym">
                <el-date-picker
                  style="width:100%"
                  v-model="editForm.ym"
                  type="month"
                  value-format="yyyyMM"
                  placeholder="选择期号"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item prop="configKey" label="配置键" required>
                <el-input placeholder="配置键" v-model="editForm.configKey"></el-input>
              </el-form-item>
              <el-form-item prop="configValue" label="配置值" required>
                <el-input placeholder="配置值" v-model="editForm.configValue"></el-input>
              </el-form-item>
              <el-form-item prop="description" label="描述" required>
                <el-input placeholder="描述" v-model="editForm.description"></el-input>
              </el-form-item>
            </el-form>

            <template #footer>
              <span class="dialog-footer">
                <el-button @click="editCancel" size="mini">取 消</el-button>
                <el-button type="primary" @click="saveEditConfig" size="mini" v-model="saveEnable">保 存</el-button>
              </span>
            </template>
          </el-dialog>

          <!-- 新增 -->
          <el-dialog
            title="新增"
            ref="addForm"
            width="30%"
            :visible.sync="addVisible">
            <el-form :model="addForm" size="mini" label-width="auto">
              <el-form-item label="期号" prop="ym">
                <el-date-picker
                  style="width:100%"
                  v-model="addForm.ym"
                  type="month"
                  value-format="yyyyMM"
                  placeholder="选择期号"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="选择医保地" prop="mode"   v-show="this.tab ==2">
                <el-select v-model="addForm.insuplcAdmdvs" placeholder="选择参保地信息"   v-show="this.tab ==2">
                  <el-option label="市医保" value="0"></el-option>
                  <el-option label="省医保" value="1" v-show="this.openInsuPlcaeSelection" ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="configKey" label="配置键" required>
                <el-select v-model="addForm.configKey" placeholder="请选择配置键">
                  <!-- 下拉框选项 -->
                  <el-option label="居民测算单价" value="CX_PRICE"></el-option>
                  <el-option label="职工测算单价" value="CZ_PRICE"></el-option>
                  <el-option label="测算单价" value="PRICE"></el-option>
                  <!-- 可以动态加载选项，例如从服务器获取配置键 -->
                </el-select>
              </el-form-item>
              <el-form-item prop="configValue" label="配置值" required>
                <el-input placeholder="配置值" v-model="addForm.configValue"></el-input>
              </el-form-item>
              <el-form-item prop="description" label="描述" required>
                <el-select v-model="addForm.description" placeholder="请选择配置键">
                  <!-- 下拉框选项 -->
                  <el-option label="居民测算单价" value="居民测算单价"></el-option>
                  <el-option label="职工测算单价" value="职工测算单价"></el-option>
                  <el-option label="测算单价" value="测算单价"></el-option>
                  <!-- 可以动态加载选项，例如从服务器获取配置键 -->
                </el-select>
              </el-form-item>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="addCancel" size="mini">取 消</el-button>
                <el-button type="primary" @click="saveAddConfig" size="mini" v-model="saveEnable">保 存</el-button>
              </span>
            </template>
          </el-dialog>

           <!-- 费用计算 -->
           <el-dialog
            title="费用计算"
            ref="feeCalculationForm"
            width="50%"
            :visible.sync="feeCalculationVisible">
            <el-form :model="feeCalculationForm" size="mini" label-width="auto">
              <el-form-item label="期号" prop="ym">
                <el-date-picker
                  style="width:100%"
                  v-model="feeCalculationForm.ym"
                  type="month"
                  value-format="yyyyMM"
                  placeholder="选择期号"
                   @change="queryPrice"
                >{{this.feeCalculationForm.ym}}
                </el-date-picker>
              </el-form-item>
              <el-form-item label="选择医保地" prop="insuplcAdmdvs" v-show="this.tab ==2">
                <el-select v-model="feeCalculationForm.insuplcAdmdvs" placeholder="选择参保地信息"   @change="queryPrice"  v-show="this.tab ==2">
                  <el-option label="市医保" value="0"></el-option>
                  <el-option label="省医保" value="1" v-show="this.openInsuPlcaeSelection"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="cxPrice" label="居民/城乡测算单价" required>
                <el-input placeholder="居民测算单价" v-model="feeCalculationForm.cxPrice" :disabled="false">{{this.feeCalculationForm.cxPrice}}</el-input>
              </el-form-item>
              <el-form-item prop="czPrice" label="职工/城镇测算单价" required>
                <el-input placeholder="职工/城镇测算单价" v-model="feeCalculationForm.czPrice" :disabled="false">{{this.feeCalculationForm.czPrice}}</el-input>
              </el-form-item>
              <el-form-item prop="price" label="测算单价" required>
                <el-input placeholder="测算单价" v-model="feeCalculationForm.price" :disabled="false">{{this.feeCalculationForm.price}}</el-input>
              </el-form-item>

            </el-form>
            <template #footer>
              <span class="dialog-footer">
                  <el-button @click="switchPreviosMouth(feeCalculationForm.ym)" size="mini">上一月</el-button>
                  <el-button @click="switchNextMouth(feeCalculationForm.ym)" size="mini">下一月</el-button>
                <el-button @click="feeCalculationCancel" size="mini">取 消</el-button>
                <el-button type="primary" @click="feeCalculationReset" size="mini" v-model="saveEnable">确定</el-button>
              </span>
            </template>
          </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>

import {
  queryData,
  updateDip,
  updateDrg,
  addDrgConfig,
  addDipConfig,
  queryDrgData,
  queryPrice,
  resetFee
} from '@/api/dataConfig/dipConfig'
import { deleteDip, deleteDrg } from '../../../api/dataConfig/dipConfig'

export default {
  name: 'dipPriceConfigManage',
  components: {},
  inject: ['reload'],
  data: () => ({
    tableData: [],
    openInsuPlcaeSelection: false,
    tableLoading: false,
    editVisible: false,
    addVisible: false,
    feeCalculationVisible: false,
    queryForm: {
      configKey: '',
      configValue: '',
      ym: ''
    },
    editForm: {
      id: '',
      configKey: '',
      configValue: '',
      ym: '',
      description: ''
    },
    addForm: {
      id: '',
      configKey: '',
      insuplcAdmdvs: '0',
      configValue: '',
      ym: '',
      description: ''
    },
    feeCalculationForm: {
      ym: '',
      type: '',
      cxPrice: '',
      czPrice: '',
      price: '',
      insuplcAdmdvs: '0'
    },
    configValue: 'null',
    saveEnable: true,
    tab: '1',
    total: 0
  }),
  mounted () {
    this.initOpenInsuPlcaeSelection(),
    this.getData()
  },
  methods: {
    initOpenInsuPlcaeSelection () {
      if (process.env.openInsuPlcaeSelection) {
        this.openInsuPlcaeSelection = true
      } else {
        this.openInsuPlcaeSelection = false
      }
    },
    getData () {
      this.tableLoading = true
      if (this.tab == 1) {
        queryData(this.queryForm).then((result) => {
          this.tableData = result.data.list
          this.total = result.data.total
          this.tableLoading = false
        })
      }
      if (this.tab == 2) {
        queryDrgData(this.queryForm).then((result) => {
          this.tableData = result.data.list
          this.total = result.data.total
          this.tableLoading = false
        })
      }
    },
    refresh () {
      this.reload()
    },
    handleEdit (index, row) {
      this.configValue = row.value
      this.editForm.id = row.id
      this.editForm.configKey = row.key
      this.editForm.configValue = row.value
      this.editForm.description = row.description
      this.editForm.ym = row.ym
      this.editVisible = true
    },
    copyVal2Add (index, row) {
      this.addForm.configKey = row.key
      this.addForm.configValue = row.value
      this.addForm.description = row.description
      this.addVisible = true
    },

    feeCalculationCfg () {
      this.feeCalculationForm.type = this.tab
      this.feeCalculationForm.ym = this.queryForm.ym
      queryPrice(this.feeCalculationForm).then((result) => {
        this.feeCalculationForm.cxPrice = result.data.cxPrice
        this.feeCalculationForm.czPrice = result.data.czPrice
        this.feeCalculationForm.price = result.data.price
        this.tableLoading = false
      })
      this.feeCalculationVisible = true
    },
    queryPrice (value) {
      this.feeCalculationForm.type = this.tab
      //this.feeCalculationForm.ym = value
      queryPrice(this.feeCalculationForm).then((result) => {
        if (result.data != null) {
          this.feeCalculationForm.cxPrice = result.data.cxPrice
          this.feeCalculationForm.czPrice = result.data.czPrice
          this.feeCalculationForm.price = result.data.price
        } else {
          this.feeCalculationForm.cxPrice = ''
          this.feeCalculationForm.czPrice = ''
          this.feeCalculationForm.price = ''
        }
        this.tableLoading = false
      })
    },

    addPriceCfg () {
      this.addVisible = true
    },
    feeCalculationCfgReSet () {
      this.tableLoading = true
      this.feeCalculationForm.type = this.tab
      this.feeCalculationForm.cxPrice = this.queryForm.cxPrice
      this.feeCalculationForm.czPrice = this.queryForm.czPrice
      this.feeCalculationForm.price = this.queryForm.price
      this.feeCalculationForm.insuplcAdmdvs = this.queryForm.insuplcAdmdvs
      if (!this.feeCalculationForm.ym ) {
        this.tableLoading = false
        this.$message({
          message: '请选择月份',
          type: 'warning'
        })
        return // 退出函数，避免后续操作
      }
      if (!this.feeCalculationForm.insuplcAdmdvs ) {
        this.tableLoading = false
        this.$message({
          message: '请选择参保地',
          type: 'warning'
        })
        return // 退出函数，避免后续操作
      }
      if (!this.feeCalculationForm.cxPrice ) {
        this.tableLoading = false
        this.$message({
          message: '请输入居民/城乡测算单价！',
          type: 'warning'
        })
        return // 退出函数，避免后续操作
      }
      if ( !this.feeCalculationForm.price ) {
        this.tableLoading = false
        this.$message({
          message: '请输入职工/城镇测算单价！',
          type: 'warning'
        })
        return // 退出函数，避免后续操作
      }
      if ( !this.feeCalculationForm.insuplcAdmdvs) {
        this.tableLoading = false
        this.$message({
          message: '请输入测算单价！',
          type: 'warning'
        })
        return // 退出函数，避免后续操作
      }
      resetFee(this.feeCalculationForm).then((result) => {
        this.tableLoading = false
        this.$message({
          message: '修改成功！',
          type: 'success'
        })
      })
      //  this.getData()
    },
    resetAddForm () {
      this.addForm = {
        id: '',
        configKey: '',
        configValue: '',
        ym: '',
        description: ''
      }
    },
    resetFeeForm () {
      this.feeCalculationForm = {
        ym: '',
        type: '',
        cxPrice: '',
        czPrice: '',
        price: ''
      }
    },
    editCancel () {
      this.$confirm('关闭后所做修改将不会保存,是否确认关闭？')
        .then(_ => {
          this.editVisible = false
        })
        .catch(_ => {
          this.editVisible = false
        })
    },
    addCancel () {
      this.$confirm('关闭后新增信息不会保存,是否确认关闭？')
        .then(_ => {
          this.addVisible = false
          this.resetAddForm()
        })
        .catch(_ => {
          this.addVisible = false
          this.resetAddForm()
        })
    },
    switchPreviosMouth (mouth) {
      let targetDate;

      // 如果mouth为空，使用当前日期
      if (!mouth) {
        targetDate = new Date();
      }
      // 如果mouth是字符串类型（假设格式为YYYYMM）
      else if (typeof mouth === 'string' && /^\d{6}$/.test(mouth)) {
        // 将字符串转换为Date对象
        const year = parseInt(mouth.substring(0, 4), 10);
        const month = parseInt(mouth.substring(4, 6), 10) - 1; // 月份从0开始
        targetDate = new Date(year, month);
      }
      // 如果mouth已经是Date对象
      else if (mouth instanceof Date) {
        targetDate = new Date(mouth);
      }
      else {
        throw new Error('Invalid mouth format');
      }

      // 获取上一个月的日期
      targetDate.setMonth(targetDate.getMonth() - 1);

      // 处理月份为0的情况（即上一年12月）
      if (targetDate.getMonth() === 11 && targetDate.getDate() !== new Date(targetDate.getFullYear(), 11, 1).getDate()) {
        // 如果当前日期在新月没有对应的天数（如31日），则调整为当月最后一天
        targetDate.setDate(0);
      }

      // 格式化为yyyy-MM
      const year = targetDate.getFullYear();
      const month = (targetDate.getMonth() + 1).toString().padStart(2, '0');
      const previousMonth = `${year}${month}`
      this.feeCalculationForm.ym = previousMonth;
      queryPrice(this.feeCalculationForm).then((result) => {
        if (result.data != null) {
          this.feeCalculationForm.cxPrice = result.data.cxPrice
          this.feeCalculationForm.czPrice = result.data.czPrice
          this.feeCalculationForm.price = result.data.price
        } else {
          this.feeCalculationForm.cxPrice = ''
          this.feeCalculationForm.czPrice = ''
          this.feeCalculationForm.price = ''
        }
        this.tableLoading = false
      })

    },
    switchNextMouth (mouth) {
      let targetDate;

      // 如果mouth为空，使用当前日期
      if (!mouth) {
        targetDate = new Date();
      }
      // 如果mouth是字符串类型（假设格式为YYYYMM）
      else if (typeof mouth === 'string' && /^\d{6}$/.test(mouth)) {
        // 将字符串转换为Date对象
        const year = parseInt(mouth.substring(0, 4), 10);
        const month = parseInt(mouth.substring(4, 6), 10) - 1; // 月份从0开始
        targetDate = new Date(year, month);
      }
      // 如果mouth已经是Date对象
      else if (mouth instanceof Date) {
        targetDate = new Date(mouth);
      }
      else {
        throw new Error('Invalid mouth format');
      }

      // 获取下一个月的日期
      targetDate.setMonth(targetDate.getMonth() + 1);

      // 处理月份超过12的情况（如1月31日加1个月会变成3月3日，我们希望得到2月28/29日）
      // 所以这里不需要额外处理，因为setMonth()会自动调整

      // 格式化为yyyy-MM
      const year = targetDate.getFullYear();
      const month = (targetDate.getMonth() + 1).toString().padStart(2, '0');
      const nextMonth = `${year}${month}`
      this.feeCalculationForm.ym = nextMonth;

      queryPrice(this.feeCalculationForm).then((result) => {
        if (result.data != null) {
          this.feeCalculationForm.cxPrice = result.data.cxPrice
          this.feeCalculationForm.czPrice = result.data.czPrice
          this.feeCalculationForm.price = result.data.price
        } else {
          this.feeCalculationForm.cxPrice = ''
          this.feeCalculationForm.czPrice = ''
          this.feeCalculationForm.price = ''
        }
        this.tableLoading = false
      })
    },
    feeCalculationCancel () {

      this.$confirm('关闭后将不会计算费用,是否确认关闭？')
        .then(_ => {
          this.feeCalculationVisible = false
          this.resetFeeForm()
        })
        .catch(_ => {
          this.feeCalculationVisible = false
          this.resetFeeForm()
        })
    },

    saveEditConfig () {
      if (this.saveEnable != null) {
        this.$confirm('这操作将重新生成数据,是否确认修改？')
          .then(_ => {
            if (this.tab == 1) {
              updateDip(this.editForm).then((result) => {
                this.tableData = result.data
                this.editVisible = false
                this.getData()
                this.$message({
                  message: '修改成功！',
                  type: 'success'
                })
              })
            }
            if (this.tab == 2) {
              updateDrg(this.editForm).then((result) => {
                this.tableData = result.data
                this.editVisible = false
                this.getData()
                this.$message({
                  message: '修改成功！',
                  type: 'success'
                })
              })
            }
          })
          .catch(_ => {
            this.editVisible = false
          })
      }
    },
    saveAddConfig () {
      if (this.saveEnable != null) {
        this.$confirm('是否确认提交？')
          .then(_ => {
            if (this.tab == 1) {
              if (this.addForm.configKey == null || this.addForm.configKey == '') {
                // 弹出提示 "单价不能为空"
                this.$message.error('dip配置见不能为空')
                return
              }
              if (this.addForm.configValue == null || this.addForm.configValue == '') {
                // 弹出提示 "单价不能为空"
                this.$message.error('dip配置值不能为空')
                return
              }
              addDipConfig(this.addForm).then((result) => {
                this.tableData = result.data
                this.addVisible = false
                this.getData()
                this.$message({
                  message: '新增成功！',
                  type: 'success'
                })
                this.resetAddForm()
              })
            }
            if (this.tab == 2) {
              if (this.addForm.configKey == null || this.addForm.configKey == '') {
                // 弹出提示 "单价不能为空"
                this.$message.error('DRG配置键不能为空')
                return
              }
              if (this.addForm.configValue == null || this.addForm.configValue == '') {
                // 弹出提示 "单价不能为空"
                this.$message.error('DRG配置值不能为空')
                return
              }
              if (this.addForm.insuplcAdmdvs == null || this.addForm.insuplcAdmdvs == '') {
                // 弹出提示 "单价不能为空"
                this.$message.error('DRG配置参保地类型不能为空');
                return
              }
              addDrgConfig(this.addForm).then((result) => {
                this.tableData = result.data
                this.addVisible = false
                this.getData()
                this.$message({
                  message: '新增成功！',
                  type: 'success'
                })
                this.resetAddForm()
              })
            }
          })
          .catch(_ => {
            this.addVisible = false
          })
      }
    },
    feeCalculationReset () {
      if (this.saveEnable != null) {
        this.$confirm('是否确认提交？')
          .then(_ => {
            if (!this.feeCalculationForm || !this.feeCalculationForm.price ||
              !this.feeCalculationForm.czPrice || !this.feeCalculationForm.cxPrice || !this.feeCalculationForm.type
            ) {
              this.$message({
                message: '参数不能为空，请检查输入。',
                type: 'warning'
              })
              return // 终止请求
            }
            resetFee(this.feeCalculationForm).then((result) => {
              this.tableData = result.data
            //  this.feeCalculationVisible = false
              this.getData()
              this.$message({
                message: '已完成费用计算',
                type: 'success'
              })
            //  this.resetFeeForm()
            })
          })
          .catch(_ => {
            this.feeCalculationVisible = false
          })
      }
    },
    DrgDip () {
      this.getData()
    },
    deleteDipOrDrg (index, row) {
      let params = {}
      params.id = parseInt(row.id)
      this.$confirm('是否确认删除？')
        .then(_ => {
          if (this.tab == 1) {
            deleteDip(params).then(res => {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getData()
            })
          }
          if (this.tab == 2) {
            deleteDrg(params).then(res => {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getData()
            })
          }
        })
    }
  }
}

</script>

<style scoped>
 /deep/ .el-form-item__content {
   width: 85% !important;
 }
</style>
