<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-date-range
             :container="true"
             :inline="true"
             headerTitle="查询条件"
             contentTitle="患者分析"
             @query="queryData">

      <template slot="extendFormItems">
        <el-form-item label="病案号" class="som-form-item">
          <el-input v-model="queryForm.patientId" placeholder="请输入病案号"  />
        </el-form-item >
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <el-table :data="tableData" border tableId="tableId">
          <el-table-column label="序号" prop="index"></el-table-column>
          <el-table-column label="姓名" prop="name"></el-table-column>
          <el-table-column label="出院时间" prop="outHosTime"></el-table-column>
          <el-table-column label="DIP名称" prop="dipCodg"></el-table-column>
          <el-table-column label="病案号" prop="bah"></el-table-column>
          <el-table-column label="出院科室" prop="deptName"></el-table-column>
          <el-table-column label="住院总费用" prop="inHosTotalCost"></el-table-column>
          <el-table-column label="标杆住院费用" prop="Cost"></el-table-column>
          <el-table-column label="费用差异" prop="chayi"></el-table-column>
          <el-table-column label="操作" >
            <el-button type="primary">查看详情</el-button>
          </el-table-column>
        </el-table>

        <el-dialog title="患者详细信息"></el-dialog>
      </template>
    </drg-form>
  </div>
</template>

<script>
export default {
  name: 'caseList',
  data: () => ({
    queryForm: {

    },
    tableData: [
      { name: '王得发', outHosTime: '2021-05-05', dipCodg: '123', bah: '2316', deptName: '123', inHosTotalCost: '231.2', Cost: '125', chayi: '231' },
      { name: '王得发', outHosTime: '2021-05-05', dipCodg: '123', bah: '2316', deptName: '123', inHosTotalCost: '231.2', Cost: '125', chayi: '231' }
    ]
  }),
  methods: {
    queryData () {

    }
  }
}
</script>

<style scoped>

</style>
