import request from '@/utils/request'

/**
 * 删除组织机构和用户信息
 * @param params
 * @returns {*}
 */
export function deleteUserConstruction (params) {
  return request({
    url: '/sysHospitalEnvGenerateController/deleteUserConstruction',
    method: 'post',
    params: params
  })
}

/**
 * 查询医生和科室信息
 * @param params
 * @returns {*}
 */
export function queryDeptAndDoctorInfo (params) {
  return request({
    url: '/sysHospitalEnvGenerateController/queryDeptAndDoctorInfo',
    method: 'post',
    params: params
  })
}

/**
 * 修改系统配置
 * @param params
 * @returns {*}
 */
export function modifySysConfig (params) {
  return request({
    url: '/sysHospitalEnvGenerateController/modifySysConfig',
    method: 'post',
    data: params
  })
}

/**
 * 清空环境
 * @param params
 * @returns {*}
 */
export function truncateAllEnv (params) {
  return request({
    url: '/sysHospitalEnvGenerateController/truncateAllEnv',
    method: 'post',
    data: params
  })
}

/**
 * 生成医院标杆
 * @param params
 * @returns {*}
 */
export function generateHosBenchmark (params) {
  return request({
    url: '/sysHospitalEnvGenerateController/generateHosBenchmark',
    method: 'post',
    params: params
  })
}

/**
 * 上传DRG相关信息
 * @param params
 * @returns {*}
 */
export function uploadDrgData (params) {
  return request({
    url: '/sysHospitalEnvGenerateController/uploadDrgData',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}

/**
 * 下载DRG信息模板
 * @param params
 * @returns {*}
 */
export function downloadDrgData (params) {
  return request({
    url: '/sysHospitalEnvGenerateController/downloadDrgData',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}

export function selectTableCount (params) {
  return request({
    url: '/sysHospitalEnvGenerateController/selectTableCount',
    method: 'post',
    data: params
  })
}

/**
 * 更改确认项
 * @param params
 * @returns {*}
 */
export function updateConfirm (params) {
  return request({
    url: '/sysHospitalEnvGenerateController/updateConfirm',
    method: 'post',
    data: params
  })
}

/**
 * 查询确认项
 * @param params
 * @returns {*}
 */
export function queryConfirm (params) {
  return request({
    url: '/sysHospitalEnvGenerateController/queryConfirm',
    method: 'post',
    data: params
  })
}
