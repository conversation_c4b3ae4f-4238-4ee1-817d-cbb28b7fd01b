<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-patient-num
             show-dip
             show-drg
             show-hos-dept
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="病案首页详情"
             :container="true"
             @query="handleSearchList">

      <template slot="extendFormItems">

        <el-form-item label="离院方式">
          <el-select v-model="listQuery.b34c" placeholder="请选择离院方式" class="som-form-item" @change="getDataIsuue" clearable>
            <el-option
              v-for="item in dictVoList.B34C"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="患者姓名">
          <el-input  v-model="listQuery.a11" placeholder="请输入患者姓名" class="som-form-item" />
        </el-form-item>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <el-table ref="settleListTable"
                  :header-cell-style="{'text-align':'center'}"
                  id="slTable"
                  size="mini"
                  stripe
                  height="100%"
                  :data="list"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>
          <el-table-column label="序号" type="index" align="right">
          </el-table-column>
          <el-table-column label="病案号" prop="a48" align="left" >
            <template slot-scope="scope">{{scope.row.a48 | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="姓名" prop="a11" align="left"  width="90" >
            <template slot-scope="scope">{{scope.row.a11 | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="性别" prop="a12c" align="left"  width="60"
                           :filters="[{ text: '男', value: '1' }, { text: '女', value: '2' }]"
                           :filter-method="filterSex">
            <template slot-scope="scope">{{scope.row.a12c | formatA12c}}</template>
          </el-table-column>
          <el-table-column prop="a14" label="年龄"  align="right" width="80" sortable>
            <template slot-scope="scope">{{scope.row.a14 | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="DRGs编码和名称" prop="drgsCodeAndName" align="left"  width="180" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.drgsCodeAndName | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="DIP编码和名称" prop="dipCodeAndName" align="left"  width="180" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.dipCodeAndName | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="入院科室"  align="left" prop="b13n"  :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.b13n | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="出院科室"  align="left" prop="b16n"  :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.b16n | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="疾病主诊段"  align="left" prop="c04n" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{ scope.row.c04n | formatIsEmpty }}</template>
          </el-table-column>
          <el-table-column label="手术主诊段"  align="left" prop="c15x01n" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.c15x01n | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column prop="b12" label="入院时间"  align="right"  width="140px" sortable>
          </el-table-column>
          <el-table-column prop="b15" label="出院时间"  align="right" width="140px"  sortable>
          </el-table-column>
<!--          <el-table-column fixed="right" label="查看详情(旧)" align="center" width="90px" >-->
<!--            <template slot-scope="scope">-->
<!--              <el-button type="primary" size="mini" icon="el-icon-search" @click="handleShowMedicalDetail(scope.$index, scope.row)" circle>-->
<!--              </el-button>-->
<!--            </template>-->
<!--          </el-table-column>-->

          <el-table-column fixed="right" label="查看详情" align="center" width="90px" >
            <template slot-scope="scope">
              <el-button type="primary" size="mini" icon="el-icon-search" @click="newHandleShowMedicalDetailFirst(scope.$index, scope.row)" circle>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </drg-form>
  </div>
</template>

<script>
import { formatDate } from '@/utils/date'
import {
  queryDataIsuue,
  queryLikeDipGroupByPram,
  queryLikeDrgsByPram,
  querySelectTreeAndSelectList
} from '@/api/common/drgCommon'
import { deleteDataById, fetchList as queryPageData, recoveryDataById } from '@/api/medicalQuality/settleList'
import { elExportExcel } from '@/utils/exportExcel'
import { medicalDeleteDataUpload } from '@/api/medicalQuality/medicalDeleteDataUpload'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  a48: null,
  a11: null,
  b16c: null,
  b34c: null,
  settle_start_date: null,
  settle_end_date: null,
  ry_start_date: null,
  ry_end_date: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date,
  queryDipGroup: '',
  queryDrg: '',
  tableHeight: 0
}
export default {
  name: 'caseManage',
  components: { },
  inject: ['reload'],
  data () {
    return {
      dialogVisible: false,

      listLoading: true,
      list: null,
      total: null,
      settle_start_date: null,
      settle_end_date: null,
      ry_start_date: null,
      ry_end_date: null,
      cy_start_date: null,
      cy_end_date: null,
      dictVoList: {},
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      depts: [],
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }

    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  watch: {
    list: function () {
      this.$nextTick(() => {
        this.$refs.settleListTable.doLayout()
      })
    }
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime (time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },
    formatA12c (value) {
      if (value == '1') {
        return '男'
      } else if (value == '2') {
        return '女'
      }
    }
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'B34C')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.a48 = this.listQuery.medcasCodg
      this.submitListQuery.a11 = this.listQuery.a11
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.b34c = this.listQuery.b34c
      this.submitListQuery.queryDipGroup = this.listQuery.dipCodg
      this.submitListQuery.queryDrg = this.listQuery.drgCodg
      this.submitListQuery.settle_start_date = this.settle_start_date
      this.submitListQuery.settle_end_date = this.settle_end_date
      this.submitListQuery.ry_start_date = this.listQuery.inStartTime
      this.submitListQuery.ry_end_date = this.listQuery.inEndTime
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      // console.log(this.submitListQuery)
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.settleListTable.$children, document.getElementById('slTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病案数据')
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSelect (item) {
      this.listQuery.dipCodg = item.dipCodg
    },
    querySearchAsyncForDrg (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocompleteForDrg.handleFocus()
      })
    },
    handleSelectForDrg (item) {
      this.listQuery.drgCodg = item.drgsCode
    },
    dateChangeSettle_date (val) {
      if (val) {
        this.settle_start_date = val[0]
        this.settle_end_date = val[1]
      } else {
        this.settle_start_date = null
        this.settle_end_date = null
      }
    },
    dateChangeRysj (val) {
      if (val) {
        this.ry_start_date = val[0]
        this.ry_end_date = val[1]
      } else {
        this.ry_start_date = null
        this.ry_end_date = null
      }
      this.getList()
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.getList()
    },
    handleResetSearch () {
      this.getDataIsuue()
    },
    handleShowMedicalDetail (index, row) {
      this.$router.push({ path: '/setlListManage/setlListDetail', query: { k00: row.k00, id: row.id } })
    },
    newHandleShowMedicalDetailFirst (index, row) {
      this.$router.push({ path: '/caseQual/caseDetail', query: { k00: row.k00, id: row.id } })
    },
    filterSex (value, row) {
      return row.a12c === value
    },
    exportExcel () {
      let tableId = 'slTable'
      let fileName = '病案数据'
      elExportExcel(tableId, fileName)
    },
    refresh () {
      this.reload()
    },

    upload (data) {
      let params = new FormData()
      params.append('file', data.file)
      medicalDeleteDataUpload(params).then(res => {
        if (res.code == 200) {
          this.dialogVisible = false
          this.$refs.upload.clearFiles()
          this.$message.success('上传成功')
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
      })
    },
    deleteData (index, item) {
      let params = {
        ids: []
      }
      params.ids.push(item.id)
      deleteDataById(params).then(res => {
        if (res.code == 200) {
          this.$message.success('删除成功')
          this.handleSearchList()
        }
      })
    },
    recoveryData (index, item) {
      let params = {
        ids: []
      }
      params.ids.push(item.id)
      recoveryDataById(params).then(res => {
        if (res.code == 200) {
          this.$message.success('恢复成功')
          this.handleSearchList()
        }
      })
    }
  }
}
</script>

<style>
/*时间样式设置*/
/deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner{
  width: 200px;
}
.autoSelectInputWidth{
  width: 178px;
}
</style>
