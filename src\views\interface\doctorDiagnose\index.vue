<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span>提示说明</span>
      </div>
      <div style="font-size: 13px;height: 50px;">
            <li>
              此功能主要用于医生下诊断时，调智库给予提示和规范/次均费用/次均住院时间，包括该病种分组信息的展示；有利于医院规范诊疗行为！
            </li>
      </div>
    </el-card>

    <el-card class="box-card" style="height:100%;margin-top:5px;">
      <div>
        <el-form :inline="true" :model="listQuery" size="mini" >
          <el-row type="flex" style="margin-left:310px;">
           <div>
              <el-form-item label="主要诊断编码/名称">
                <el-autocomplete
                  popper-class="my-autocomplete"
                  size="small"
                  v-model="listQuery.queryIcd"
                  :fetch-suggestions="querySearchAsync"
                  placeholder="请输入主要诊断编码或者名称"
                  @select="handleSelect"
                  :popper-append-to-body="false"
                  :clearable="true"
                  :trigger-on-focus="false"
                  ref='elautocomplete'>
                  <template slot-scope="{ item }">
                    <div class="code">{{ item.icdCodg }}</div>
                    <span class="name">{{ item.icdName }}</span>
                  </template>
                </el-autocomplete>
              </el-form-item>
           </div>
            <div>
              <el-button
                @click="queryInfo()"
                type="primary"
                size="mini">
                搜索结果
              </el-button>
              <el-button
                @click="handleResetSearch()"
                size="mini">
                重置
              </el-button>
            </div>
          </el-row>
        </el-form>
      </div>
      <el-row type="flex" style="margin-left:310px;">
        <el-collapse v-model="activeNames" style="width:540px">
          <el-collapse-item name="1">
            <template slot="profttl">
              <span style="font-size:14px;font-weight:bold;color:  #FFA500">费用信息：</span>
            </template>
            <div style="margin-left:30px;" v-if="avgCost0!=null"><li>{{avgCost0}}</li></div>
            <div style="margin-left:30px;" v-if="avgCost1!=null"><li>{{avgCost1}}</li></div>
            <div style="margin-left:30px;" v-if="avgCost2!=null"><li>{{avgCost2}}</li></div>
            <div style="margin-left:30px;" v-if="avgCost3!=null"><li>{{avgCost3}}</li></div>
            <div style="margin-left:30px;" v-if="avgCost4!=null"><li>{{avgCost4}}</li></div>
            <div style="margin-left:30px;" v-if="notInfo==1"><li>该病种费用对照信息待引入！</li></div>
          </el-collapse-item>
          <el-collapse-item name="2">
            <template slot="profttl">
              <span style="font-size:14px;font-weight:bold;color:  #32CD32">时间信息：</span>
            </template>
            <div style="margin-left:30px;" v-if="avgDay1!=null"><li>{{avgDay1}}</li></div>
            <div style="margin-left:30px;" v-if="avgDay2!=null"><li>{{avgDay2}}</li></div>
            <div style="margin-left:30px;" v-if="avgDay3!=null"><li>{{avgDay3}}</li></div>
            <div style="margin-left:30px;" v-if="avgDay4!=null"><li>{{avgDay4}}</li></div>
            <div style="margin-left:30px;" v-if="notInfo==1"><li>该病种时间对照信息待引入！</li></div>
          </el-collapse-item>
          <el-collapse-item name="3">
            <template slot="profttl">
              <span style="font-size:14px;font-weight:bold;color:  #1E90FF">分组信息：</span>
            </template>
            <div style="margin-left:30px;" v-if="drgsCode!=null"><li>DRGs编码：{{drgsCode}}</li></div>
            <div style="margin-left:30px;" v-if="drgsName!=null"><li>DRGs名称：{{drgsName}}</li></div>
          </el-collapse-item>
        </el-collapse>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import { queryLikeIcdsByPram } from '@/api/common/drgCommon'
import { queryCostTimeAndGroupInfo } from '@/api/interface/doctorDiagnoseInterface'

const defaultListQuery = {
  queryIcd: ''
}
export default {
  name: 'doctorDiagnose',
  data () {
    return {
      icds: [],
      queryIcd: '',
      activeNames: ['1', '2', '3'],
      avgCost0: null,
      avgCost1: null,
      avgCost2: null,
      avgCost3: null,
      avgCost4: null,
      avgDay1: null,
      avgDay2: null,
      avgDay3: null,
      avgDay4: null,
      drgsCode: null,
      drgsName: null,
      notInfo: 0, // 0：医院有权限查看该病种，1：医院没权限查询该病种
      listQuery: Object.assign({}, defaultListQuery)
    }
  },
  methods: {
    // 查询费用、时间信息，分组信息
    queryInfo () {
      this.activeNames = ['1', '2', '3']
      if (this.listQuery.queryIcd === '') {
        this.avgCost0 = null
        this.avgCost1 = null
        this.avgCost2 = null
        this.avgCost3 = null
        this.avgCost4 = null
        this.avgDay1 = null
        this.avgDay2 = null
        this.avgDay3 = null
        this.avgDay4 = null
        this.drgsCode = null
        this.drgsName = null
        this.notInfo = 0
      } else {
        this.avgCost0 = null
        this.avgCost1 = null
        this.avgCost2 = null
        this.avgCost3 = null
        this.avgCost4 = null
        this.avgDay1 = null
        this.avgDay2 = null
        this.avgDay3 = null
        this.avgDay4 = null
        this.drgsCode = null
        this.drgsName = null
        this.notInfo = 0
        queryCostTimeAndGroupInfo(this.listQuery).then(response => {
          this.listLoading = false
          if (response.data.avgCost0 == null && response.data.avgCost1 == null && response.data.avgCost2 == null && response.data.avgCost3 == null && response.data.avgCost4 == null &&
              response.data.avgDay1 == null && response.data.avgDay2 == null && response.data.avgDay3 == null && response.data.avgDay4 == null
          ) {
            this.notInfo = 1
          } else {
            this.notInfo = 0
            if (response.data.avgCost0) {
              this.avgCost0 = '医疗机构例均费用：￥' + response.data.avgCost0
            }
            if (response.data.avgCost1) {
              this.avgCost1 = '群落值为1时群里例均费用：￥' + response.data.avgCost1
            }
            if (response.data.avgCost2) {
              this.avgCost2 = '群落值为2时群里例均费用：￥' + response.data.avgCost2
            }
            if (response.data.avgCost3) {
              this.avgCost3 = '群落值为3时群里例均费用：￥' + response.data.avgCost3
            }
            if (response.data.avgCost4) {
              this.avgCost4 = '群落值为4时群里例均费用：￥' + response.data.avgCost4
            }
            if (response.data.avgDay1) {
              this.avgDay1 = '群落值为1时群里例均住院天数：' + response.data.avgDay1 + '天'
            }
            if (response.data.avgDay2) {
              this.avgDay2 = '群落值为2时群里例均住院天数：' + response.data.avgDay2 + '天'
            }
            if (response.data.avgDay3) {
              this.avgDay3 = '群落值为3时群里例均住院天数：' + response.data.avgDay3 + '天'
            }
            if (response.data.avgDay4) {
              this.avgDay4 = '群落值为4时群里例均住院天数：' + response.data.avgDay4 + '天'
            }
          }
          if (response.data.drgsCode) {
            this.drgsCode = response.data.drgsCode
          }
          if (response.data.drgsName) {
            this.drgsName = response.data.drgsName
          }
        })
      }
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString,
        icd_type: 'ICD-10' // 只查询疾病信息
      }
      queryLikeIcdsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSelect (item) {
      this.listQuery.queryIcd = item.icdCodg
    },
    // 重置页面
    handleResetSearch () {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.queryInfo()
    }
  }
}
</script>
<style scoped>
  /deep/ .el-card__header{
    padding: 7px 10px ;
    color:#FFFFFF;
    font-size: 14px;
    background-color: #9b9b9b;
  }
  /deep/ .el-form-item--mini .el-form-item__label{
    line-height:35px;
    font-size: 15px;
    font-weight:bold;
  }
  /deep/ .el-autocomplete{
    width:400px;
  }
  /deep/ .el-scrollbar__wrap {
    max-height: 450px;
  }
  /deep/ .el-autocomplete-suggestion li {
    line-height: 27px;
  }
  .code {
  }
  .name {
    font-size: 13px;
    color: #9b9b9b;
    text-overflow: ellipsis;
    overflow: hidden;
  }
</style>
