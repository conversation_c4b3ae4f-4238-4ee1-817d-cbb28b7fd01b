<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="somForm"
             show-date-range
             show-in-date-range
             show-se-date-range
             :show-hos-dept="{ show: showDept}"
             :initTimeValueNotQuery="false"
             headerTitle="查询条件"
             :container="true"
             :showPagination="isTablePage"
             :totalNum="total"
              :showCoustemContentTitle="true"
              :exportExcel="{ 'tableId': tableId, exportName: exportTableName}"
             :exportExcelFun="exportExcelFun"
             :exportExcelHasChild="true"
             @query="radioChange(radioMode)"
             @reset="reset">
      <!-- profttl -->
      <template slot="contentTitle">
        <drg-title-line :title="profttl" :wrapStyle="{ width: 'calc(80% - 10px)'}">
          <template slot="rightSide">
            <div style="display: flex">
              <!--              <div style="position: absolute;top: 13%;width: 110%;right: 101%;margin-right: 4%">-->
              <!--                <el-switch v-model="queryForm.feeStas"-->
              <!--                           active-text="医保反馈" active-value="1" active-color="#13ce66"-->
              <!--                           inactive-text="预测数据" inactive-value="0" inactive-color="#79e0ff"-->
              <!--                           @change="changeSwitch"></el-switch>-->
              <!--              </div>-->
              <div>
                <el-radio-group v-model="radioMode" @change="radioChange">
<!--                  <el-radio-button label="指标"></el-radio-button>-->
<!--                  <el-radio-button label="预测"></el-radio-button>-->
<!--                  <el-radio-button label="分析"></el-radio-button>-->
                  <el-radio-button :label="1">指标</el-radio-button>
                  <el-radio-button :label="2">预测</el-radio-button>
                  <el-radio-button :label="3">分析</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <template slot="titleRight">
            <!-- 固定列 -->
            <el-select v-model="columnVal"
                       multiple
                       collapse-tags
                       :multiple-limit="3"
                       placeholder="请选择固定列">
              <el-option
                v-for="item in columnOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </template>
        </drg-title-line>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <div class="content-wrapper">

          <!-- 左侧 -->
          <div class="content-wrapper-left" v-if="!analysis">
            <!-- 指标table -->
            <kpi-table :data="tableData"
                       ref="dataTable"
                       :loading="loading"
                       :fixed-columns="columnVal"
                       :id="kipId"
                       :query-form="queryForm"
                       :columnOptions="columnOptions"
                       v-if="radioMode == '1'"
                       @setRefObj="(obj) => this.tableObj = obj"
                       @showSuspension="showSuspension"/>

            <!-- 预测table -->
            <forecast-table :data="tableData"
                            ref="dataTable"
                            :loading="loading"
                            :query-form="queryForm"
                            :fixed-columns="columnVal"
                            :columnOptions="columnOptions"
                            :id="forecastId"
                            v-if="radioMode == '2'"
                            @setRefObj="(obj) => this.tableObj = obj"
                            @showSuspension="showSuspension"/>

          </div>

          <!--悬浮框-->
          <suspension-frame
            :zbData="zbData"
            :ycData="ycData"/>

          <!-- 右侧 -->
          <div class="content-wrapper-right" v-if="!analysis">

            <div class="content-wrapper-right-top">
              <div style="display: flex;">
                <!-- 排序字段选择 -->
                <div style="width: 60%;padding-right: 2%">
                  <el-select v-model="selectVal" placeholder="请选择" @change="selectChange">
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>

                <!-- top -->
                <div style="width: 40%">
                  <el-select v-model="topVal" placeholder="请选择" @change="generateChartData">
                    <el-option
                      v-for="item in topOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>
              </div>

              <!-- 排序 -->
              <div class="content-wrapper-right-icon" @click="sortClick">
                <i class="el-icon-sort"></i>
              </div>
            </div>

            <div style="height: 93%;width: 100%;position: absolute;top: 7%">
              <!-- 图 -->
              <drg-echarts :options="chartOption" ref="chart"/>
            </div>
          </div>

          <!-- 左侧分析 -->
          <div class="content-wrapper-analysis" v-if="analysis">
            <analysis-comp ref="dataTable"
                           :data="analysisPageData"
                           :id="analysisId"
                           :dropdown="deptDropdownList"
                           :dropdown-check-val="dropdownVal"
                           :loading="loading"
                           v-if="showAnalysis && !showQuadrant"
                           :is-loss="isLoss"
                           :queryForm="queryForm"
                           @setRefObj="(obj) => this.tableObj = obj"
                           @switchLossOrProfit="switchLossOrProfit"
                           @dropdownChange="analysisDropdownChange"
                           @checkTypeChange="analysisTypeChange"/>

            <analysis-table :data="analysisTableData"
                            ref="dataTable"
                            :loading="loading"
                            :id="analysisId"
                            :query-form="queryForm"
                            :is-loss="isLoss"
                            v-if="!showAnalysis && !showQuadrant"
                            @showAnalysisPage="switchChartTable"
                            @setRefObj="(obj) => this.tableObj = obj"/>

            <div v-if="showQuadrant" style="height: 100%">
              <el-card  style="height: 100%">
                <div style="width: 100%;height: 100%;">
                  <drg-echarts :options="scatterOptions" />
                </div>
              </el-card>
            </div>
          </div>
        </div>

        <div v-if="radioMode === '分析'">
          <!--病组四象限图-->
          <div class="content-wrapper-analysis-quadrant-profit">
            <i class="el-icon-copy-document"
               title="象限"
               @click="switchQuadrant(null)"
               style="font-size: 20px;cursor: pointer">
            </i>
          </div>
          <!-- 盈利或亏损 -->
          <div class="content-wrapper-analysis-loss-profit">
            <!-- profit -->
            <i class="som-icon-profit som-iconTool"
               title="盈利"
               v-if="!isLoss"
               @click="switchLossOrProfit(true)"
               style="height: 1.2rem;width: 1.2rem"></i>

            <!-- loss -->
            <i class="som-icon-loss som-iconTool"
               title="亏损"
               v-if="isLoss"
               @click="switchLossOrProfit(false)"
               style="height: 1.2rem;width: 1.2rem"></i>
          </div>

          <!-- 分析页面 - 切换图表按钮 -->
          <div class="content-wrapper-analysis-chart-table">
            <!-- table -->
            <i class="som-icon-table som-iconTool"
               title="表格"
               v-if="showAnalysis"
               @click="switchChartTable(null)"
               style="height: 1.2rem;width: 1.2rem"></i>

            <!-- analysis -->
            <i class="som-icon-analysis som-iconTool"
               title="分析"
               v-if="!showAnalysis"
               @click="switchChartTable(null)"
               style="height: 1.2rem;width: 1.2rem"></i>
          </div>
        </div>
<!--          <div  style="display: flex" v-show="radioMode=='1'">-->
<!--            <div>总病例：{{countMedicalTotal}}</div>例，-->
<!--            <div>总入组病例：{{countInGroup}}</div>例，-->
<!--            <div>总未入组病例：{{countNoGroup}}</div>例，-->
<!--          </div>-->
<!--          <div style="display: flex" v-show="radioMode=='2'">-->
<!--            <div>预测正常付费总病例：{{normalNumCount}}</div>例，-->
<!--            <div>预测总超高病例：{{ultrahighNumCount}}</div>例，-->
<!--            <div>预测总超低病例：{{ultraLowNumCount}}</div>例。-->
<!--          </div>-->
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryKpiData, queryForecastData, queryCountData, queryYCCountData, selectDrgSickGroupQuadrant } from '@/api/newDrgBusiness/newDrgBusinessDept'
import { queryDrgDiseaseLoss,
  queryDrgDoctorLoss,
  queryDrgPatientLoss,
  queryDropdown,
  queryDrgMedError,
  queryDrgAnalysisSummary,
  updateSwitchState } from '@/api/newBusiness/newBusinessCommon'
import KpiTable from './comps/newDrgDeptKpiTable'
import ForecastTable from './comps/newDrgDeptForecastTable'
import AnalysisTable from './comps/newDrgDeptAnalysisTable'
import AnalysisComp from './comps/newDrgDeptAnalysisComp'
import SuspensionFrame from './comps/newSuspensionFrame'

let kpiOptions = [
  { value: 'medicalTotalNum', label: '病案数' },
  { value: 'groupNum', label: '组数' },
  { value: 'totalWeight', label: '权重' },
  { value: 'inGroupRate', label: '入组率' },
  { value: 'timeIndex', label: '时间消耗指数' },
  { value: 'costIndex', label: '费用消耗指数' }
]

let forecastOptions = [
  { value: 'forecastAmountDiff', label: '预测金额差异' },
  { value: 'forecastAmount', label: '预测金额' },
  { value: 'oeVal', label: 'O/E值' }
]
export default {
  name: 'deptDrgAnalysis',
  components: {
    'kpi-table': KpiTable,
    'forecast-table': ForecastTable,
    'analysis-comp': AnalysisComp,
    'analysis-table': AnalysisTable,
    'suspension-frame': SuspensionFrame
  },
  data: () => ({
    queryForm: {
      feeStas: '0'
    },
    radioMode: '1',
    chartOption: {},
    tableData: [],
    total: 0,
    loading: false,
    yAxisData: [],
    seriesData: [],
    selectVal: 'medicalTotalNum',
    options: kpiOptions,
    topVal: 10,
    topOptions: [
      { value: 10, label: 'TOP10' },
      { value: 20, label: 'TOP20' },
      { value: 30, label: 'TOP30' },
      { value: 10 ** 4, label: 'ALL' }
    ],
    columnVal: [],
    columnOptions: [],
    tableObj: {},
    kipId: 'kipId',
    forecastId: 'forecastId',
    analysisId: 'analysisId',
    exportTableName: '',
    tableId: '',
    exportExcelFun: queryKpiData,
    sort: true, // true：倒序 false：正序
    profttl: '科室指标分析',

    // 分析页面
    analysis: false,
    analysisPageType: '',
    analysisPageData: [],
    tempAnalysisPageData: [],
    deptDropdownList: [],
    dropdownVal: '',
    analysisTableData: [],
    showQuadrant: false,
    scatterOptions: {},
    selectSickGroupQuadrantList: [],
    isLoss: true,

    // 分析页面 - 错误病例
    showAnalysis: true,
    // 悬浮框
    zbData: [],
    ycData: [],
    // 总病例数量
    countMedicalTotal: '',
    // 总入组病例数量
    countInGroup: '',
    // 总未入组病例数量
    countNoGroup: '',
    // 正常缴费病例总数量
    normalNumCount: '',
    // 超高病例总数量
    ultrahighNumCount: '',
    // 超低病例总数量
    ultraLowNumCount: ''

  }),
  mounted () {
    this.queryForm.feeStas = String(this.$store.getters.feeStas)
    if (Object.keys(this.$route.query).length > 0) {
      if (this.$route.query.code != '' && this.$route.query.code != null) {
        this.queryForm.deptCode = this.$route.query.code
        this.queryForm.dateRange = [this.$route.query.begnDate, this.$route.query.expiDate]
        this.queryForm.begnDate = this.$route.query.begnDate
        this.queryForm.expiDate = this.$route.query.expiDate
        this.queryForm.feeStas = this.$route.query.feeStas
        if (this.$route.query.radioMode == '3') {
          this.radioMode = '3'
        }
        if (this.$route.query.dateType == 2) {
          this.$nextTick(() => {
            this.$refs.somForm.jumpTimeChange('se', { seStartTime: this.$route.query.begnDate, seEndTime: this.$route.query.expiDate }, this.queryForm)
            this.$refs.somForm.changeCheckBoxTime(['结算'])
            this.$refs.somForm.setButtonCheckbox('结算')
          })
        }
      }
      if (this.$route.query.begnDate && this.$route.query.expiDate) {
        this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.queryForm)
      }

      if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
        this.$refs.somForm.jumpTimeChange('in', this.$route.query, this.queryForm)
      }

      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.queryForm)
      }
    }

    this.$nextTick(() => {
      this.init()
    })
  },
  computed: {
    isTablePage () {
      return this.analysisPageType != 'errorMed' && this.showAnalysis
    },
    showDept () {
      return this.radioMode != '3' && this.$somms.hasHosRole()
    }
  },
  methods: {
    queryKpiData,
    queryForecastData,
    queryDrgPatientLoss,
    queryCountData () {
      queryCountData(this.queryForm).then(res => {
        if (res.data.list[0]) {
          this.countInGroup = res.data.list[0].countInGroup
          this.countMedicalTotal = res.data.list[0].countMedicalTotal
          this.countNoGroup = res.data.list[0].countNoGroup
        } else {
          this.countInGroup = ''
          this.countMedicalTotal = ''
          this.countNoGroup = ''
        }
      })
    },
    queryYCCountData () {
      queryYCCountData(this.queryForm).then(res => {
        if (res.data.list[0]) {
          this.normalNumCount = res.data.list[0].normalNumCount
          this.ultrahighNumCount = res.data.list[0].ultrahighNumCount
          this.ultraLowNumCount = res.data.list[0].ultraLowNumCount
        } else {
          this.normalNumCount = ''
          this.ultrahighNumCount = ''
          this.ultraLowNumCount = ''
        }
      })
    },
    init () {
      this.radioChange(this.radioMode)
      this.queryCountData()
      this.queryYCCountData()
    },

    changeSwitch (val) {
      this.$store.commit('SET_SWITCHSTATE', val)
      this.queryForm.feeStas = this.$store.getters.feeStas
      let params = {}
      Object.assign(params, this.queryForm)
      params.username = this.$store.getters.name
      updateSwitchState(params).then((res) => {
        if (res.code == 200) {
          this.init()
        }
      })
    },
    // 查询指标数据
    queryPageKpiData () {
      this.loading = true
      queryKpiData(this.queryForm).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
        this.generateChartData()
        this.generateFixedColumns()
      })
    },
    // 查询预测数据
    queryPageForecastData () {
      this.loading = true
      queryForecastData(this.queryForm).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
        this.generateChartData()
        this.generateFixedColumns()
      })
    },
    selectSickGroupQuadrantData () {
      selectDrgSickGroupQuadrant(this.queryForm).then(res => {
        this.selectSickGroupQuadrantList = res.data
        this.scatterChart()
      })
    },
    // 查询分析数据
    async queryPageAnalysisData (deptCode) {
      this.loading = true
      let params = this.getParams()

      // 如果是表格则生成数据
      if (!this.showAnalysis) {
        this.analysisTableData = []
        await queryDrgAnalysisSummary(params).then(res => {
          this.analysisTableData = res.data
        })
      } else {
        this.analysisPageData = []
        if (this.deptDropdownList.length == 0) {
          await queryDropdown(this.getParams()).then(res => {
            this.deptDropdownList = res.data
          })
        }

        if (deptCode) {
          // 跳转到分析页面
          this.dropdownVal = deptCode
        } else {
          // 第一次进入页面没有选择下拉选
          if (this.deptDropdownList.length > 0 && !this.dropdownVal) {
            this.dropdownVal = this.deptDropdownList[0].value
          }
        }

        // 子页面下拉选改变传入科室编码
        if (this.dropdownVal) {
          params.deptCode = this.dropdownVal
          this.tempAnalysisPageData = []
        }

        await this.getAnalysisPageData('dis', 0, queryDrgDiseaseLoss, params)
        await this.getAnalysisPageData('doctor', 1, queryDrgDoctorLoss, params)
        await this.getAnalysisPageData('med', 2, queryDrgPatientLoss, params)

        // 错误病例
        await this.getAnalysisPageData('errorMed', 3, queryDrgMedError, params, true)

        if (this.analysisPageData.length > 0) {
          this.tempAnalysisPageData = [...this.analysisPageData]
        }
      }

      this.loading = false
    },
    // 获取分析数据
    async getAnalysisPageData (type, index, queryMethod, params, noPaging = false) {
      if (this.analysisPageType == type || this.tempDataIsNull(index)) {
        await queryMethod(params).then(res => {
          if ((['dis', 'doctor', 'med'].includes(type) && res.data.list && res.data.list.length > 0) ||
            (type == 'errorMed' && res.data && res.data.length > 0)) {
            this.addAnalysisData(res, index, false, noPaging)
            if (this.analysisPageType == type && !['errorMed'].includes(type)) this.total = res.data.total
          } else {
            this.analysisPageData.push(
              {
                data: [],
                total: 0,
                pageNum: 1
              }
            )
          }
        })
      } else {
        this.addAnalysisData(null, index, true)
      }
    },
    tempDataIsNull (index) {
      if (this.tempAnalysisPageData.length > 0) {
        if (this.tempAnalysisPageData[index].data != undefined) {
          return this.tempAnalysisPageData[index].data.length == 0
        } else {
          return true
        }
      }

      return this.tempAnalysisPageData.length == 0
    },
    // 添加分析数据
    addAnalysisData (res, index, useTempData, noPaging = false) {
      if (useTempData) {
        this.analysisPageData.push(this.tempAnalysisPageData[index])
      } else {
        if (noPaging) {
          let total = 0
          if (index == 3) {
            total = res.data[0].errorSummaryNum + '-' + res.data[0].compeleteErrorNum + '-' + res.data[0].logicErrorNum
          }
          // 无分页情况
          this.analysisPageData.push({
            data: res.data,
            total: total,
            pageNum: 1
          })
        } else {
          this.analysisPageData.push({
            data: res.data.list,
            total: res.data.total,
            pageNum: this.queryForm.pageNum
          })
        }
      }
    },
    // 生成图数据
    generateChartData () {
      this.yAxisData = []
      this.seriesData = []
      let sortData = []
      for (let i = 0; i < this.tableData.length; i++) {
        let item = this.tableData[i]
        sortData.push({
          y: (item.deptName && item.deptName != undefined) ? item.deptName : '-',
          value: item[this.selectVal]
        })
      }

      if (this.sort) {
        sortData = sortData.sort((a, b) => a.value - b.value)
      } else {
        sortData = sortData.sort((a, b) => b.value - a.value)
      }

      for (let i = 0; i < sortData.length; i++) {
        if (i == this.topVal) {
          break
        }
        this.yAxisData.push(sortData[i].y)
        this.seriesData.push(sortData[i].value)
      }
      this.yAxisData.reverse()
      this.seriesData.reverse()
      this.initChart()
    },
    // 生成固定列
    generateFixedColumns () {
      this.columnOptions = []
      let children = this.$refs.dataTable.$children[0].$children
      if (children) {
        children.forEach(item => {
          if (item.$options.propsData.label && item.$options.propsData.label != '序号') {
            this.columnOptions.push({
              value: item.$options.propsData.prop,
              label: item.$options.propsData.label
            })
          }
        })
      }
    },
    // 初始化图
    initChart () {
      this.chartOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        dataZoom: [
          {
            type: 'inside',
            yAxisIndex: [0],
            orient: 'vertical'
          },
          {
            type: 'slider',
            yAxisIndex: [0],
            orient: 'vertical'
          }
        ],
        xAxis: {
          type: 'value',
          axisLabel: {
            interval: 0,
            rotate: 40,
            formatter: params => {
              return this.formatCost(params, 0)
            }
          },
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: this.yAxisData
        },
        series: [
          {
            name: '数值',
            type: 'bar',
            label: {
              formatter: params => {
                return this.formatCost(params.value)
              }
            },
            data: this.seriesData.map((item, index) => {
              return {
                value: item,
                label: {
                  show: false
                }
              }
            })
          }
        ]
      }
    },
    // label显示位置格式化
    labelPositionFormat (data) {
      let result = []
      if (data) {
        let count = 0
        data.forEach(item => {
          if (item > 0) {
            count++
          } else {
            count--
          }
          result.push({
            value: item,
            position: item > 0 ? 'right' : 'left'
          })
        })
        count = Math.abs(count)
        let limitCount = result.length * 2 / 10
        // 存在正负时
        if (count != data.length) {
          let first = result[0].value
          let last = result[result.length - 1].value
          for (let i = 0; i < result.length; i++) {
            if (last > 0) {
              result[i].position = 'right'
            }
          }
        } else {
          let flag = result[0].value > 0
          for (let i = 0; i < result.length; i++) {
            if (flag) {
              if (i > limitCount || String(result[i].value).length < 4) {
                continue
              }
            } else {
              if (i < (result.length - limitCount || String(result[i].value).length < 4)) {
                continue
              }
            }

            result[i].position = 'inside'
          }
        }
      }
      return result
    },
    // 格式化
    formatCost (val, fixed = 2) {
      let prefix = ''
      let resVal = val
      if (String(val).includes('-')) {
        prefix = '-'
        val = Math.abs(val)
        resVal = val
      }
      if (Math.abs((parseFloat(val) / 10000)) > 1) {
        resVal = (parseFloat(val) / 10000).toFixed(fixed) + '/万'
      } else {
        resVal = val
      }
      return prefix + resVal
    },
    // 选择改变
    radioChange (val) {
      // this.$route.query.radioMode = val
      if (val == '1') {
        this.queryPageKpiData()
        this.exportExcelFun = queryKpiData
        this.tableId = this.kipId
        this.exportTableName = '科室指标分析' + '(' + this.getParams().begnDate + '~' + this.getParams().expiDate + ')'
        this.options = kpiOptions
        this.selectVal = 'medicalTotalNum'
        this.profttl = '科室指标分析'
        this.analysis = false
      }

      if (val == '2') {
        this.queryPageForecastData()
        this.exportExcelFun = queryForecastData
        this.tableId = this.forecastId
        this.exportTableName = '科室预测分析' + '(' + this.getParams().begnDate + '~' + this.getParams().expiDate + ')'
        this.options = forecastOptions
        this.selectVal = 'forecastAmountDiff'
        this.profttl = '科室预测情况'
        this.analysis = false
      }

      if (val == '3') {
        if (this.showQuadrant) {
          this.selectSickGroupQuadrantData()
        }
        this.queryPageAnalysisData()
        this.exportExcelFun = queryDrgPatientLoss
        this.tableId = this.analysisId
        this.exportTableName = '科室分析' + '(' + this.getParams().begnDate + '~' + this.getParams().expiDate + ')'
        this.profttl = '科室分析'
        this.analysis = true
      }
      this.queryCountData()
      this.queryYCCountData()
    },
    // 图排序改变
    selectChange () {
      this.generateChartData()
    },
    // 点击排序
    sortClick () {
      this.sort = !this.sort
      this.generateChartData()
    },
    // 分析页面模块点击
    analysisTypeChange (item) {
      let type = item.type
      this.queryForm.pageNum = item.pageNum
      this.tempAnalysisPageData = item.data.map(i => {
        return {
          total: i.value,
          data: i.data,
          pageNum: i.pageNum
        }
      })
      this.analysisPageType = type
      if (this.analysisPageData.length > 0) {
        if (type == 'dis') {
          this.total = this.analysisPageData[0].total
        }
        if (type == 'doctor') {
          this.total = this.analysisPageData[1].total
        }
        if (type == 'med') {
          this.total = this.analysisPageData[2].total
        }
      }
    },
    // 分析页面下拉选改变
    analysisDropdownChange (val) {
      this.dropdownVal = val
      this.queryPageAnalysisData()
    },
    // 切换图表
    switchChartTable (deptCode) {
      this.showAnalysis = !this.showAnalysis
      this.queryPageAnalysisData(deptCode)
    },
    // 选择盈利还是亏损
    switchLossOrProfit (isLoss) {
      this.isLoss = isLoss
      this.queryPageAnalysisData()
    },
    // 切换象限图
    switchQuadrant (deptCode) {
      this.showQuadrant = !this.showQuadrant
      this.selectSickGroupQuadrantData(deptCode)
    },
    // 病组发展分析
    scatterChart () {
      let listTotal = []
      for (let i = 0; i < this.selectSickGroupQuadrantList.length; i++) {
        let quadrantData = this.selectSickGroupQuadrantList[i]
        listTotal.push(
          quadrantData.forecastAmountDiff,
          quadrantData.cmi,
          quadrantData.forecastAmount,
          quadrantData.medicalTotalNum,
          quadrantData.sumfee,
          quadrantData.totalWeight,
          quadrantData.drgName,
          quadrantData.drgCodg)
      }

      // 6个一组地将listTotal中的数据推入listCostData数组中
      let listCostData = []
      for (let i = 0; i < listTotal.length; i += 8) {
        listCostData.push(listTotal.slice(i, i + 8))
      }

      // 找到listCostData中所有数据medicalTotalNum的最大值和最小值
      let max = Math.max(...listCostData.map(item => item[3]))
      let min = Math.min(...listCostData.map(item => item[3]))

      // 根据需要指定最大、最小气泡大小
      let maxSize = 50
      let minSize = 10

      let schema = [
        { name: 'forecastAmountDiff', index: 0, text: '预测费用差异' },
        { name: 'cmi', index: 1, text: 'cmi' },
        { name: 'forecastAmount', index: 2, text: '预测费用' },
        { name: 'medicalTotalNum', index: 3, text: '总病例数' },
        { name: 'totalCost', index: 4, text: '总费用' },
        { name: 'totalWeight', index: 5, text: '总权重' },
        { name: 'drgName', index: 6, text: '病组' },
        { name: 'drgCode', index: 7, text: '病组编码' }
      ]
      // 自定义颜色
      let color_dept = [
        '#37A2DA',
        '#e06343',
        '#37a354',
        '#b55dba',
        '#b5bd48',
        '#8378EA'
      ]

      // 获取科室名称并赋予颜色
      let pieces = []
      for (let i = 0; i < listCostData.length; i++) {
        pieces.push({
          value: listCostData[i][6],
          label: listCostData[i][6],
          color: color_dept[i]
        })
      }

      let option = {
        title: {
          text: this.$store.getters.getDeptNameByCode(this.queryForm.deptCode)
        },
        grid: {
          left: '10%',
          right: '15%',
          top: '10%'
        },
        tooltip: {
          formatter: function (param) {
            let value = param.value
            let tooltip = ''
            for (let i = 0; i < schema.length; i++) {
              tooltip += schema[i].text + ': ' + value[i] + '<br/>'
            }
            return tooltip
          }
        },
        // visualMap: {
        //   right: 5,
        //   top: 'middle',
        //   min: min,
        //   max: max,
        //   type: 'piecewise',
        //   splitNumber: listCostData.length,
        //   pieces: pieces
        // },
        xAxis: {
          type: 'value',
          name: '预测费用差异',
          axisLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: 'cmi指数',
          max: 6
          // axisLine:{
          //   show:false
          // }
        },
        series: [
          {
            data: listCostData,
            // data: [],
            type: 'scatter',
            symbolSize:
              function (dataItem) {
                // 根据公式计算气泡大小
                let size = (dataItem[3] - min) / (max - min) * (maxSize - minSize) + minSize
                return size
              },
            // itemStyle:{
            //   color:function (params) {
            //     let deptName = params.data[6];
            //     let index = pieces.findIndex(item => item.value === deptName);
            //     return color_dept[index];
            //   }
            // },
            markLine: {
              symbol: ['none', 'none'], // ['none']表示是一条横线；['arrow', 'none']表示线的左边是箭头，右边没右箭头；['none','arrow']表示线的左边没有箭头，右边有箭头
              label: {
                position: 'start' // 将警示值放在哪个位置，三个值“start”,"middle","end" 开始 中点 结束
              },
              data: [
                {
                  label: { show: false },
                  silent: false, // 鼠标悬停事件 true没有，false有
                  lineStyle: { // 警戒线的样式 ，虚实 颜色
                    type: 'solid', // 样式  ‘solid’和'dotted'
                    color: '#000000',
                    width: 1 // 宽度
                  },
                  yAxis: 1 // 警戒线的标注值，可以有多个yAxis,多条警示线 或者采用 {type : 'average', name: '平均值'}，type值有 max min average，分为最大，最小，平均值
                }
              ]
            }
          }
        ]
      }
      this.scatterOptions = option
    },
    showSuspension (item) {
      if (item) {
        if (item[0].type == 1) {
          this.zbData = item
        } else if (item[0].type == 2) {
          this.ycData = item
        }
      }
    },
    // 获取参数
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.auth = true
      // 下拉列表类型 1:科室
      params.dropdownType = '1'
      // 亏损还是盈利
      params.isLoss = this.isLoss
      return params
    },
    // 重置
    reset () {
      this.radioChange(this.selectVal)
      this.tempAnalysisPageData = []
    }
  }
}
</script>
<style scoped lang="scss">
.content-wrapper{
  height: 100%;
  width: 100%;
  display: flex;

  &-left{
    width: 80%;
    height: 98%;
    padding-right: 10px;
    box-sizing: border-box;
    position: relative;

    &-fixed-column{
      position: absolute;
      left: 10%;
      top: -4.5%;
    }

    &-analysis{
      width: 100%;
      height: 100%;
    }
  }

  &-right{
    width: 20%;
    height: 100%;
    position: relative;

    &-top{
      height: 10%;
      width: 100%;
      position: absolute;
      top: 0%
    }

    &-icon{
      z-index: 2;
      font-size: 18px;
      width: 20px;
      height: 40px;
      cursor: pointer;
      position: absolute;
      right: 0;
      bottom: 0;
    }
  }

  &-analysis{
    width: 100%;
    height: 100%;
    position: relative;

    &-quadrant-profit{
      position: absolute;
      top: 3px;
      right: calc(3% + 1.2rem);
    }

    &-chart-table{
      position: absolute;
      top: -5px;
      right: 5px;
    }

    &-loss-profit{
      position: absolute;
      top: -2px;
      right: 35px;
    }
  }
}

/deep/ .pagination-container{
  right: 21%;
}
/deep/ .content-wrapper-right-top>.el-select{
  width: 84px;
}

</style>
