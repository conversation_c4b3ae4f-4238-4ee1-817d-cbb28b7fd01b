<template>
  <div style="position: relative">
    <template v-for="(item, index) in routers">
      <template v-if="!item.hidden&&item.children">
        <router-link v-if="hasOneShowingChildren(item.children) && !item.children[0].children&&!item.alwaysShow"
                     :to="item.path+'/'+item.children[0].path"
                     :key="item.children[0].name">
          <drg-menu-item :class="{'submenu-title-noDropdown':!isNest}">
            <i v-if="item.children[0].meta&&item.children[0].meta.icon" :class="item.children[0].meta.icon"
               slot="icon"></i>
            <span v-if="item.children[0].meta&&item.children[0].meta.profttl"
                  slot="profttl">{{ item.children[0].meta.profttl }}</span>
          </drg-menu-item>
        </router-link>

        <drg-submenu v-else :data="item" :borderBottom="index == 0 || index == routers.length - 1" ref="somSubmenu"
                     :key="index">
          <template slot="profttl">
            <i v-if="item.meta&&item.meta.icon" :class="item.meta.icon"
               :style="{ marginRight: lessen ? '.8rem' : '', marginBottom: !lessen ? '.5rem': ''}"></i>
            <span v-if="item.meta&&item.meta.profttl" slot="profttl">{{ item.meta.profttl }}</span>
          </template>
          <template v-for="(child,index) in item.children">
            <template v-if="!child.is_hide">
              <drg-menu :is-nest="true" class="nest-menu" v-if="child.children&&child.children.length>0" :key="index"
                        :data="[child]"/>
              <router-link :to="item.path+'/'+child.path" :key="child.name" @click.native="menuItemClick(child)" v-else>
                <drg-menu-item :data="child">
                  <i v-if="child.meta&&child.meta.icon" :class="child.meta.icon" slot="icon"></i>
                  <span v-if="child.meta&&child.meta.profttl" slot="profttl">{{ child.meta.profttl }}</span>
                </drg-menu-item>
              </router-link>
            </template>
          </template>
        </drg-submenu>

      </template>
    </template>
  </div>
</template>
<script>
import MenuItem from '@/components/drgComponents/Menu/menuItem.vue'
import SubMenu from '@/components/drgComponents/Menu/subMenu.vue'

export default {
  components: {
    'drg-menu-item': MenuItem,
    'drg-submenu': SubMenu
  },
  name: 'jpMenu',
  props: {
    data: {
      type: Array
    },
    isNest: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    routers: [],
    activeMenu: [],
    closeFlag: false
  }),
  mounted () {
    this.routers = this.data
  },
  computed: {
    lessen () {
      return this.$store.state.app.sidebar.opened
    },
    reduce () {
      return this.$store.state.tagsView.reduce
    }
  },
  methods: {
    hasOneShowingChildren (children) {
      const showingChildren = children.filter(item => {
        return !item.is_hide
      })
      if (showingChildren.length === 1) {
        return true
      }
      return false
    },
    // 菜单项点击
    menuItemClick (item) {
      // 直接修改item.active子页面不能监听，只能重新渲染
      if (!item.active) {
        this.setData(item, true, true)
      }
    },
    setData (item, flag, add) {
      let routers = JSON.parse(JSON.stringify(this.routers))
      this.routers = []
      this.setActive(item, routers, flag)
      // 设置顶层菜单选中状态
      this.setTopStatus(routers)
      this.routers = routers
      if (add) {
        this.activeMenu.push(item)
      }
    },
    setActive (item, routers, flag) {
      routers.map(router => {
        if (router.path == item.path) {
          router.active = flag
          return
        }
        if (router.children && router.children.length > 0) {
          this.setActive(item, router.children, flag)
        }
      })
    },
    // 设置顶层状态
    setTopStatus (routers) {
      routers.map(router => {
        let data = { flag: false }
        this.setTopFlag(router, data, true)
        router.active = data.flag
      })
    },
    setTopFlag (router, data, top) {
      if (router.children && router.children.length > 0) {
        router.children.map(child => {
          this.setTopFlag(child, data, false)
        })
      }
      if (router.active && !top) {
        data.flag = true
      }
    },
    // 设置菜单状态
    setMenuStatus (val) {
      if (this.reduce) {
        // 找到对应menu
        let notActiveMenu = []
        let activeMenu = []
        if (this.activeMenu.length > 0) {
          let pathArr = []
          val.map(view => pathArr.push(view.path))
          this.activeMenu.map(menu => {
            if (!pathArr.includes('/' + menu.path)) {
              notActiveMenu.push(menu)
            } else {
              activeMenu.push(menu)
            }
          })
          notActiveMenu.map(menu => this.setData(menu, false, false))
          this.activeMenu = JSON.parse(JSON.stringify(activeMenu))
        }
      }
    },
    // 隐藏子菜单悬浮
    hideMenu () {
      this.$refs.somSubmenu.map(menu => menu.lessenMouseleave())
    }
  },
  watch: {
    '$store.state.tagsView.visitedViews': {
      handler: function (val) {
        this.setMenuStatus(val)
      }
    }
  }
}
</script>
