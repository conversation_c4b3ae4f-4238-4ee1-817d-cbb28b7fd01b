<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="queryForm"
             show-date-range
             show-in-date-range
             show-se-date-range
             :show-hos-dept="{ show: this.$somms.hasHosRole()}"
             show-pagination
             :show-dip="{ show: showDip}"
             :show-drg="{ show: showDrg }"
             :show-cd="{ show: showCd }"
             :totalNum="total"
             :container="true"
              :showCoustemContentTitle="true"
             headerTitle="查询条件"
             :exportExcel="{ 'tableId': tableId, exportName: '运营决策人员分析' + '(' + this.queryForm.begnDate + '-' + this.queryForm.expiDate + ')' }"
             :exportExcelFun="queryPageData"
             :exportExcelHasChild="true"
             :initTimeValueNotQuery="false"
             @query="queryData"
             @reset="clearRouteQuery" >
      <template slot="extendFormItems" >
        <el-form-item label="病案号" class="som-form-item" prop="a48" >
          <el-input v-model="queryForm.a48" placeholder="请输入病案号"  />
        </el-form-item >
      </template>
      <template slot="extendFormItems">
        <el-form-item label="费用区间" prop="costSection">
          <drg-dict-select v-model="queryForm.costSection" placeholder="请选择费用区间" dicType="CASE_TYPE" @change="init"/>
        </el-form-item>
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <!-- 固定列 -->
        <div class="fixed-column">
          <el-select v-model="columnVal"
                     multiple
                     collapse-tags
                     :multiple-limit="3"
                     placeholder="请选择固定列">
            <el-option
              v-for="item in columnOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <el-tabs  v-if="!showSplashes" style="height: 100%" >
          <query-Ppl-Data-Table
            ref="dataTable"
            :table-data="tableData"
            :grper-type="queryForm.group"
            :table-loading="tableLoading"
            :id="tableId"
            :columnOptions="columnOptions"
            :fixed-columns="columnVal"
            @setRefObj="(obj) => this.tableObj = obj"/>
        </el-tabs>
      </template>

      <!-- 内容 profttl -->
      <template slot="contentTitle">
        <drg-title-line title="患者分析列表">
          <template slot="rightSide">

          </template>
        </drg-title-line>
      </template>
    </drg-form>
  </div>
</template>
<script>
import queryPplDataTable from './/comps/queryPplDataTable'
import { queryEnableGroup } from '@/api/common/sysCommon'
import { queryData as queryPageData } from '@/api/operationalDecision/analysis/operationalDecisionPplAnalysis'
import moment from 'moment'
import { cacheMixin } from '@/utils/mixin'
export default {
  name: 'pattExtrAnalysis',
  components: {
    'query-Ppl-Data-Table': queryPplDataTable
  },
  // mixins: [cacheMixin],
  data: () => ({
    queryForm: {
      costSection: '',
      dipCodg: '',
      drgCodg: '',
      cdCodg: ''
    },
    tableData: [],
    tableLoading: false,
    total: 0,
    showSplashes: false,
    splashesOptions: {},
    curCheckedTag: 'dipData',
    showDept: true,
    showDip: true,
    showDrg: false,
    showCd: false,
    groupOptions: [],
    tableId: 'pplTable',
    tableObj: {},
    columnVal: [],
    columnOptions: []

  }),
  created () {
    this.queryForm.group = this.$somms.getGroupType()
    this.typeConflict()
  },
  mounted () {
    this.$nextTick(() => {
      if (Object.keys(this.$route.query).length > 0) {
        if (this.$route.query.ym) {
          this.queryForm.ym = this.$route.query.ym
        }
        if (this.$route.query.inHosFlag) {
          this.queryForm.inHosFlag = Number(this.$route.query.inHosFlag)
        }
        if (!this.$somms.hasDeptRole()) {
          this.showDept = false
        }
        this.getEnabledGroup()
        this.queryForm.a48 = this.$route.query.a48
        this.queryForm.group = this.$route.query.group
        this.queryForm.deptCode = this.$route.query.deptCode
        this.queryForm.isGroup = this.$route.query.isGroup
        this.queryForm.inHosFlag = this.$route.query.inHosFlag
        if (this.$route.query.group == '1') {
          this.queryForm.dipCodg = this.$route.query.code != null ? this.$route.query.code : this.$route.query.dipCodg
          this.showDip = true
          this.showDrg = false
          this.showCd = false
        } else if (this.$route.query.group == '3') {
          this.queryForm.drgCodg = this.$route.query.code != null ? this.$route.query.code : this.$route.query.drgCodg
          this.showDip = false
          this.showDrg = true
          this.showCd = false
        } else if (this.$route.query.group == '2') {
          this.queryForm.cdCodg = this.$route.query.cdCodg
          this.showDip = false
          this.showDrg = false
          this.showCd = true
        }

        if (this.$route.query.costSection1) {
          this.queryForm.costSection = this.$route.query.costSection1
        }
        if (this.$route.query.costSection) {
          this.queryForm.costSection = this.$route.query.costSection
        }

        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          if (this.$route.query.begnDate.indexOf('1899') === -1 && this.$route.query.begnDate !== 'Invalid date') {
            this.$refs.queryForm.jumpTimeChange('out', this.$route.query, this.queryForm)
          }
        }

        if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
          if (this.$route.query.inStartTime.indexOf('1899') === -1 && this.$route.query.inStartTime !== 'Invalid date') {
            this.$refs.queryForm.jumpTimeChange('in', this.$route.query, this.queryForm)
          }
        }

        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          if (this.$route.query.seStartTime.indexOf('1899') === -1 && this.$route.query.seStartTime !== 'Invalid date') {
            this.$refs.queryForm.jumpTimeChange('se', this.$route.query, this.queryForm)
          }
        }
      }
      this.init()
    })
  },
  methods: {
    queryPageData,
    init () {
      this.queryData()
    },
    clearRouteQuery () {
      if (this.$route.query) {
        this.$router.push({ query: {} }).catch(() => {})
      }
    },
    // 生成固定列
    generateFixedColumns () {
      this.columnOptions = []
      let children = this.$refs.dataTable.$children[0].$children
      if (children) {
        children.forEach(item => {
          if (item.$options.propsData.label && item.$options.propsData.label != '序号') {
            this.columnOptions.push({
              value: item.$options.propsData.prop,
              label: item.$options.propsData.label
            })
          }
        })
      }
    },
    queryData () {
      this.tableLoading = true
      queryPageData(this.getParams()).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.total
          this.tableLoading = false
          this.generateFixedColumns()
        }
      })
    },
    createSplashes () {
      let highData = []
      let lowData = []
      let deptNames = []
      if (this.tableData.length > 0) {
        this.tableData.map(data => {
          deptNames.push(data.deptName)
          if (this.queryForm.group == 1) {
            highData.push([data.medcasVal, data.ultrahighRate, data.dipCodg, data.dipName])
            lowData.push([data.medcasVal, data.ultraLowRate, data.dipCodg, data.dipName])
          } else if (this.queryForm.group == 2) {
            highData.push([data.medcasVal, data.ultrahighRate, data.cdCodg, data.drgName])
            lowData.push([data.medcasVal, data.ultraLowRate, data.cdCodg, data.drgName])
          } else if (this.queryForm.group == 3) {
            highData.push([data.medcasVal, data.ultrahighRate, data.drgCodg, data.drgName])
            lowData.push([data.medcasVal, data.ultraLowRate, data.drgCodg, data.drgName])
          }
        })
      }
      this.splashesOptions = {
        title: {
          text: '病种超高超低分析',
          left: 'center',
          top: 0
        },
        tooltip: {
          // trigger: 'axis',
          showDelay: 0,
          formatter: function (params) {
            if (deptNames[params.dataIndex] == undefined) {
              return '编码:' + params.data[2] + '<br/>' +
                '名称:' + params.data[3] + '<br/>' +
                '病案数:' + params.data[0] + '<br/>' +
                (params.seriesIndex == 0 ? '  超高率:' : '   超低率') + params.data[1] + '%'
            } else {
              return deptNames[params.dataIndex] + '<br/>' +
                '编码:' + params.data[2] + '<br/>' +
                '名称:' + params.data[3] + '<br/>' +
                '病案数:' + params.data[0] + '<br/>' +
                (params.seriesIndex == 0 ? '  超高率:' : '   超低率') + params.data[1] + '%'
            }
          },
          axisPointer: {
            show: true,
            type: 'cross',
            lineStyle: {
              type: 'dashed',
              width: 1
            }
          }
        },
        color: ['#778dd1', '#a7d692'],
        legend: {
          data: ['高倍率', '低倍率'],
          right: 50,
          top: 50,
          width: 100
        },
        xAxis: {
          name: '病案数',
          splitLine: { show: false },
          axisLabel: {
            formatter: '{value}'
          }
        },
        yAxis: {
          name: '超高超低率',
          scale: true,
          splitLine: { show: false },
          axisLabel: {
            formatter: '{value} %'
          }
        },
        series: [
          // 高倍率点
          {
            name: '高倍率',
            type: 'scatter',
            emphasis: {
              focus: 'series'
            },
            symbolSize: 15,
            data: highData
          },
          // 低倍率点
          {
            name: '低倍率',
            type: 'scatter',
            emphasis: {
              focus: 'series'
            },
            symbolSize: 15,
            data: lowData
          }
        ]
      }
      if (this.showSplashes) {
        this.$nextTick(() => {
          this.$refs.splashesChart.initChart()
        })
      }
    },
    getEnabledGroup () {
      queryEnableGroup().then(res => {
        if (res.code == 200) {
          this.groupOptions = res.data
        }
      })
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      if (params.ym) {
        params.ym = params.ym.substring(0, 4) + '-' + params.ym.substring(4, 7)
        params.year = params.ym.substring(0, 4)
      }
      params.dataAuth = true

      params.cdCodg = this.queryForm.cdCodg
      return params
    },
    typeConflict () {
      if (this.queryForm.group == 1) {
        this.showDip = true
        this.showDrg = false
        this.showCd = false
        this.queryForm.drgCodg = ''
        this.queryForm.cdCodg = ''
        this.queryForm.dipCodg = ''
        this.tableData = []
      } else if (this.queryForm.group == 3) {
        this.showDip = false
        this.showDrg = true
        this.showCd = false
        this.queryForm.drgCodg = ''
        this.queryForm.cdCodg = ''
        this.queryForm.dipCodg = ''
      } else if (this.queryForm.group == 2) {
        this.showDip = false
        this.showDrg = false
        this.showCd = true
        this.queryForm.drgCodg = ''
        this.queryForm.cdCodg = ''
        this.queryForm.dipCodg = ''
      }
    },
    changeSplashesOrTable (index) {
      if (index == 1) {
        this.showSplashes = true
      } else {
        this.showSplashes = false
      }
      this.init()
    }
  }
}
</script>

<style scoped>
/*固定列*/
.fixed-column {
  position: absolute;
  left: 10%;
  top: 1%;
}
/deep/ .el-tab-pane{
  height: 100%;
}
/deep/ .el-tabs__content{
  height: 98%;
}
/deep/ .el-progress__text{
  font-size:10px;
}
/deep/ .el-progress-bar{
  width: 99%;
}
/deep/ .el-card__header{
  padding:5px 5px;
  font-size: 13px;
  background-color: #eef1f6;
}
.el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
  margin-bottom:20px;
  margin-right: 10px;
}
/deep/ .el-card__body{
  height:90%;
  padding-top: 0px;
  padding-right: 10px;
  padding-bottom: 0px;
  overflow-y:auto;
}
.note {
  font-size:12px;
}
.time{
  font-size:13px;
  font-weight: bold;
}
.scope{
  margin-top:10px;
}
/deep/ .is-process{
  color: #e6a23c;
  border-color: #e6a23c;
}

</style>
