<template>
    <el-form :inline="true" :model="value" size="mini"
             label-position="right" label-width="90px" style="margin-top:-18px;">
      <el-header style="height: 30px;width:120px;line-height: 30px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 10px">
        <span style="margin-left:10px;">个人信息</span>
      </el-header>
      <el-row :gutter="0" v-show="value.showJsqdAndBa">
        <el-col :span="6">
          <el-form-item label="病案号" >
            <el-input id="a48" v-model="value.somHiInvyBasInfo.a48" placeholder="请输入病案号" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote" :title="value.completeErrorsMap.a48">{{value.completeErrorsMap.a48}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote" :title="value.scoreErrorsMap.a48">{{value.scoreErrorsMap.a48}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="姓名">
            <el-input id="a11" v-model="value.somHiInvyBasInfo.a11" placeholder="请输入姓名" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!= null" class="errorNote">{{value.completeErrorsMap.a11}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a11}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="性别">
            <div id="a12c">
              <el-radio v-model="value.somHiInvyBasInfo.a12c" label="1" border size="mini">男</el-radio>
              <el-radio v-model="value.somHiInvyBasInfo.a12c" label="2" border size="mini">女</el-radio>
            </div>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a12c}}</div>
            <div v-if="value.logicErrorsMap!=null" class="errorNote">{{value.logicErrorsMap.a12c}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="出生日期" >
            <el-date-picker class="formInput" id="a13"
              v-model="value.somHiInvyBasInfo.a13" size="mini"
              type="updt_date" format="yyyy-MM-dd"
              placeholder="请选择出生日期">
            </el-date-picker>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a13}}</div>
            <div v-if="value.logicErrorsMap!=null" class="errorNote">{{value.logicErrorsMap.a13}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a13}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="0" v-show="value.showJsqdAndBa">
        <el-col :span="6">
          <el-form-item label="年龄">
            <el-input  id="a14" v-model="value.somHiInvyBasInfo.a14" placeholder="请输入年龄" type="number" min="1" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a14}}</div>
            <div v-if="value.logicErrorsMap!=null" class="errorNote">{{value.logicErrorsMap.a14}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a14}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="国籍">
            <el-input id="a15" v-model="value.somHiInvyBasInfo.a15c" placeholder="请输入国籍" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a15}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <div style="display: flex">
              <span style="width:165px;font-size: 13px;color: #606266;">（年龄不足以一周岁）年龄</span>
              <el-input id="a16" v-model="value.somHiInvyBasInfo.a16"  type="number" min="1" style="width: 75px;flex:1 auto"></el-input>
              <span style="width: 15px;font-size: 13px;color: #606266;margin-left:4px;">天</span>
            </div>
            <div v-if="value.logicErrorsMap!=null" class="errorNote">{{value.logicErrorsMap.a16}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="民族">
            <el-select id="a19c" v-model="value.somHiInvyBasInfo.a19c" placeholder="请输入民族" filterable class="formInput">
              <el-option
                v-for="item in dictVoList.MZ"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a19c}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a19c}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="0" v-show="value.showJsqdAndBa">
        <el-col :span="6">
          <el-form-item label="患者证件类别">
            <el-select id="a53" v-model="value.somHiInvyBasInfo.a53"  placeholder="请选择患者证件类别" class="formInput">
              <el-option
                v-for="item in dictVoList.ZJLB"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a53}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="证件号码">
            <el-input  id="a20" v-model="value.somHiInvyBasInfo.a20"  placeholder="请输入证件号码" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a20}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a20}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="职业">
            <el-select id="a38c" v-model="value.somHiInvyBasInfo.a38c"  placeholder="请选择职业" filterable class="formInput">
              <el-option
                v-for="item in dictVoList.ZY"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a38c}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a38c}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="现住址">
            <el-input id="a26" v-model="value.somHiInvyBasInfo.a26"  placeholder="请输入现住址" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a26}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a26}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="0" v-show="value.showJsqdAndBa">
        <el-col :span="6">
          <el-form-item label="工作单位名称">
            <el-input id="a29n" v-model="value.somHiInvyBasInfo.a29n"  placeholder="请输入工作单位名称" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a29n}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a29n}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="工作单位地址">
            <el-input id="a29" v-model="value.somHiInvyBasInfo.a29" placeholder="请输入工作单位地址" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a29}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a29}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="工作单位电话">
            <el-input id="a30" v-model="value.somHiInvyBasInfo.a30" placeholder="请输入工作单位电话" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a30}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a30}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="工作单位邮编">
            <el-input id="a31c" v-model="value.somHiInvyBasInfo.a31c" placeholder="请输入工作单位邮编" type="number" :minlength="6" :maxlength="6" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a31c}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a31c}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="0" v-show="value.showJsqdAndBa">
        <el-col :span="6">
          <el-form-item label="联系人姓名">
            <el-input id="a32" v-model="value.somHiInvyBasInfo.a32" placeholder="请输入联系人姓名" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a32}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a32}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="关系">
            <el-select id="a33c" v-model="value.somHiInvyBasInfo.a33c"  placeholder="请选择关系" class="formInput">
              <el-option
                v-for="item in dictVoList.GX"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a33c}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a33c}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="联系人地址">
            <el-input  id="a34" v-model="value.somHiInvyBasInfo.a34" placeholder="请输入联系人地址" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a34}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a34}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="联系人电话">
            <el-input  id="a35" v-model="value.somHiInvyBasInfo.a35" placeholder="请输入联系人电话" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a35}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a35}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="0" v-show="value.showJsqd">
        <el-col :span="6">
          <el-form-item label="清单流水号">
            <el-input  id="a58" v-model="value.somHiInvyBasInfo.a58" placeholder="请输入结算清单流水号" class="formInput"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="0" v-show="value.showBa">
        <el-col :span="6">
          <el-form-item label="婚姻">
            <el-select id="a21c" v-model="value.somHiInvyBasInfo.a21c" placeholder="请选择婚姻" class="formInput">
              <el-option
                v-for="item in dictVoList.HY"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
              <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a21c}}</div>
              <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a21c}}</div>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="出生地">
            <!--<el-input id="a22" v-model="value.somHiInvyBasInfo.a22" placeholder="请输入出生地址" class="formInput"></el-input>-->
            <el-select id="a22" v-model="value.somHiInvyBasInfo.a22" placeholder="请选择出生地" filterable class="formInput">
              <el-option
                v-for="item in dictVoList.XZQHDM"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a22}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a22}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="籍贯省">
            <el-select id="a23c" v-model="value.somHiInvyBasInfo.a23c" placeholder="请选择籍贯省" filterable class="formInput">
              <el-option
                v-for="item in dictVoList.XZQHDM"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a23c}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a23c}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="户口地址">
            <el-input id="a24" v-model="value.somHiInvyBasInfo.a24" placeholder="请输入户口地址" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a24}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a24}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="0" v-show="value.showBa">
        <el-col :span="6">
          <el-form-item label="户口地址邮编">
            <el-input id="a25c" v-model="value.somHiInvyBasInfo.a25c" placeholder="请输入户口地址邮政编码" :minlength="6" :maxlength="6" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a25c}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a25c}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="健康卡号">
            <el-input id="a47" v-model="value.somHiInvyBasInfo.a47" placeholder="请输入健康卡号" class="formInput"></el-input>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.a47}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="住院次数">
            <el-input id="a49" v-model="value.somHiInvyBasInfo.a49" type="number" min="1" placeholder="请输入住院次数" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.a49}}</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-header v-show="value.showJsqd" style="height: 30px;width:120px;line-height: 30px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 10px">
        <span style="margin-left:10px;">医保信息</span>
      </el-header>
      <el-row :gutter="0" v-show="value.showJsqd">
        <el-col :span="6">
          <el-form-item label="医保类型">
            <!--<el-cascader class="formInput"-->
              <!--v-model="value.somHiInvyBasInfo.a54"-->
              <!--:options="a54"-->
              <!--placeholder="请选择医保类型"></el-cascader>-->
            <el-input  v-model="value.somHiInvyBasInfo.a54" placeholder="请输入医保类型" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="医保编号">
            <el-input  v-model="value.somHiInvyBasInfo.a51" placeholder="请输入医保编号" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="参保地">
            <el-input  v-model="value.somHiInvyBasInfo.a56" placeholder="请输入参保地" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="医保结算等级">
            <el-input  v-model="value.somHiInvyBasInfo.a50" placeholder="请输入医保结算等级" class="formInput"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="0" v-show="value.showJsqd">
        <el-col :span="6">
          <el-form-item label="申报时间" >
            <el-date-picker class="formInput"
                v-model="value.somHiInvyBasInfo.a52" size="mini"
                type="updt_date"
                placeholder="请选择申报时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="特殊人员类型">
            <el-select  v-model="value.somHiInvyBasInfo.a55" placeholder="请选择特殊人员类型" class="formInput">
              <el-option
                v-for="item in dictVoList.TSRY"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-header  v-show="value.showJsqdAndBa" style="height: 30px;width:120px;line-height: 30px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 10px">
        <span style="margin-left:10px;">新生儿信息</span>
      </el-header>
      <el-row :gutter="0" v-show="value.showJsqdAndBa">
        <el-col :span="6">
          <el-form-item>
            <span style="width: 40%;font-size: 13px;color: #606266;">新生儿入院类型</span>
            <el-input  v-model="value.somHiInvyBasInfo.a57"  placeholder="新生儿入院类型" style="width: 58%;margin-left:8px;"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <span style="width: 39%;font-size: 13px;color: #606266;">新生儿出生体重</span>
            <el-input  v-model="value.somHiInvyBasInfo.a18" type="number" placeholder="请输入体重" style="width: 47%;margin-left:8px;"></el-input>
            <span style="width: 5%;font-size: 13px;color: #606266;">克</span>
            <div v-if="value.logicErrorsMap!=null" class="errorNote">{{value.logicErrorsMap.a18}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <span style="width: 39%;font-size: 13px;color: #606266;">新生儿入院体重</span>
            <el-input  v-model="value.somHiInvyBasInfo.a17"  type="number"  placeholder="请输入体重" style="width: 47%;margin-left:8px;"></el-input>
            <span style="width: 5%;font-size: 13px;color: #606266;">克</span>
            <div v-if="value.logicErrorsMap!=null" class="errorNote">{{value.logicErrorsMap.a17}}</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="6" v-show="false">
          <el-form-item>
          <!--隐藏框用于触发查看不到字段的隐藏和展示-->
            <el-input  v-model="value.showFlag"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
</template>

<script>
import { querySelectTreeAndSelectList } from '@/api/common/drgCommon'
export default {
  name: 'SettleListBaseInfo',
  props: {
    value: Object
  },
  data () {
    return {
      dictVoList: {} // 码表
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'MZ,ZJLB,ZY,GX,HY,XZQHDM,TSRY')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    }
  }
  // watch: {
  //   value: function (newVal) {
  //     console.log(newVal)
  //     this.value = newVal
  //   }
  // }
}
</script>

<style scoped>
  .formInput{
    width:170px;
  }
  .errorNote{
    color:red;
    font-size: 10px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width:170px;
  }
</style>
