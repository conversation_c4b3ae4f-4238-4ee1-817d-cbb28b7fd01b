<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
              show-date-range
              :show-in-date-range="false"
              :show-se-date-range="false"
              :show-hos-dept="{show: this.$somms.hasHosRole()}"
              showPagination
              :totalNum="total"
              headerTitle="查询条件"
              contentTitle="违规项目统计"
              :container="true"
              :exportExcel="{ 'tableId': tableId, exportName: '违规项目统计'}"
              :exportExcelFun="queryPageData"
              :exportExcelHasChild="false"
              :initTimeValueNotQuery="false"
              ref="somForm"
              @query="handleSearchList">

      <template slot="extendFormItems">

        <el-form-item label="场景类型" prop="ruleScenType">
          <drg-dict-select dicType="RULE_SCEN_TYPE" placeholder="请选择场景类型" v-model="listQuery.ruleScenType"
                           @change="getDataIsuue"/>
        </el-form-item>

      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  :id="tableId"
                  size="mini"
                  stripe
                  height="100%"
                  :data="list"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>
          <el-table-column label="序号" type="index" align="right"/>
          <el-table-column label="违规项目名称对" prop="violationName" width="160" align="left"/>
          <el-table-column label="违规内容" prop="ruleGrpName" align="left"/>
          <el-table-column label="总人次" prop="medSize" width="80" align="center"/>
          <el-table-column label="违规次数" prop="allViolationSize" width="80" align="center"/>
          <el-table-column label="项目总费用" prop="allSumfee" width="80"  align="center"/>
          <el-table-column label="明确违规费用" prop="clearlySumfee" width="80" align="center"/>
          <el-table-column label="疑似费用" prop="suspectSumfee" width="80" align="center"/>
        </el-table>
      </template>
    </drg-form>
  </div>
</template>
<script>
import {
  querySelectTreeAndSelectList,
  queryDataIsuue
} from '@/api/common/drgCommon'
import {getViolationItemSummary as queryPageData} from '@/api/examCorrection/ruleAndTuples'
import {formatDate} from '@/utils/date'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  ruleScenType: '1',
  a48: null,
  a11: null,
  b16c: null,
  b34c: null,
  begnDate: '',
  expiDate: '',
  seStartTime: '',
  seEndTime: ''
}
export default {
  name: 'violationItemSummary',
  components: {},
  inject: ['reload'],
  data() {
    return {
      showFlag: false,
      dialogVisible: false,
      tableId: 'dataTable',
      listLoading: true,
      list: null,
      total: null,
      dictVoList: {},
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      depts: [],

      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created() {

  },
  watch: {
    list: function () {
      this.$nextTick(() => {
        if (this.$refs.settleListTable) {
          this.$refs.settleListTable.doLayout()
        }
      })
    }
  },
  filters: {
    formatIsEmpty(value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime(time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },
    formatA12c(value) {
      if (value == '1') {
        return '男'
      } else if (value == '2') {
        return '女'
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.findSelectTreeAndSelectList()
      if (Object.keys(this.$route.query).length > 0) {
        // 时间需要在最下面设置
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.listQuery)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.listQuery)
        }
      }
      this.getList()
    })
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'B34C')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue() {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        this.getList()
      })
    },
    getList() {
      this.listLoading = true
      this.getParamByBaseQuery(this.submitListQuery, this.listQuery)
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },

    queryPageData,
    allExcel() {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.settleListTable.$children, document.getElementById('slTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病案数据')
    },
    handleSelect(item) {
      this.listQuery.dipCodg = item.dipCodg
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList() {
      this.getList()
    },
    handleResetSearch() {
      this.getDataIsuue()
    },
    refresh() {
      this.reload()
    },
    getParamByBaseQuery(targetParam, sourceParam) {
      if (targetParam) {
        const keys = Object.keys(targetParam)
        // 更新参数值
        keys.forEach(key => {
          targetParam[key] = sourceParam[key]
        })
      }
    },
    getParams() {
      return this.submitListQuery
    }
  }
}
</script>
<style scoped>
/*时间样式设置*/
/deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner {
  width: 200px;
}

.autoSelectInputWidth {
  width: 178px;
}

</style>
