<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
              show-date-range
              show-in-date-range
              show-se-date-range
              show-hos-dept
              show-drg
              showPagination
              :totalNum="total"
              headerTitle="查询条件"
              contentTitle="医院病组分析"
              :container="true"
              @query="handleSearchList" @reset="handleResetSearch">

<!--      <template slot="buttons">-->
<!--        <el-popconfirm-->
<!--          confirm-button-text='确定'-->
<!--          cancel-button-text='导出全部'-->
<!--          icon="el-icon-info"-->
<!--          icon-color="red"-->
<!--          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">-->
<!--          <el-button slot="reference" type="success">导出Excel</el-button>-->
<!--        </el-popconfirm>-->
<!--      </template>-->

      <template slot="buttons">
        <el-button type="success" @click="exportExcel" style="margin-right: 15px">导出Excel</el-button>
      </template>

      <template slot="containerContent">
        <div style="height:35%">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="15" style="height: 100%">
              <el-table ref="leftTable"
                        size="mini"
                        :header-cell-style="{'text-align' : 'center'}"
                        stripe
                        height="100%"
                        :data="ifComplicationGroupList"
                        style="width: 100%;"
                        v-loading="listLoading"
                        border>
                <el-table-column label="指标" align="left" width="100" :show-overflow-tooltip="true">
                  <template slot-scope="scope">{{ scope.row.index }}</template>
                </el-table-column>
                <el-table-column label="伴严重合并症及伴随病的DRGs组" align="right" :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.seriousComplicationDrgs>0" class='skip'
                         @click="queryMedicalNumByType(scope.row,'col1')">
                      {{ scope.row.seriousComplicationDrgs | formatIsEmpty }}
                    </div>
                    <div v-if="scope.row.index=='DRGs组数'&&scope.row.seriousComplicationDrgs>0" class='skip'
                         @click="queryDrgGroupNumByType(scope.row,'col1')">
                      {{ scope.row.seriousComplicationDrgs | formatIsEmpty }}
                    </div>
                    <div
                      v-if="(scope.row.index!='病案数'&&scope.row.index!='DRGs组数') || scope.row.seriousComplicationDrgs==0"
                      style="color:#000000">
                      {{ scope.row.seriousComplicationDrgs | formatIsEmpty }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="伴一般合并症及伴随病的DRGs组" align="right" :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.normalComplicationDrgs>0" class='skip'
                         @click="queryMedicalNumByType(scope.row,'col2')">
                      {{ scope.row.normalComplicationDrgs | formatIsEmpty }}
                    </div>
                    <div v-if="scope.row.index=='DRGs组数'&&scope.row.normalComplicationDrgs>0" class='skip'
                         @click="queryDrgGroupNumByType(scope.row,'col2')">
                      {{ scope.row.normalComplicationDrgs | formatIsEmpty }}
                    </div>
                    <div
                      v-if="(scope.row.index!='病案数'&&scope.row.index!='DRGs组数') || scope.row.normalComplicationDrgs==0"
                      style="color:#000000">
                      {{ scope.row.normalComplicationDrgs | formatIsEmpty }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="不伴合并症及伴随病的DRGs组" align="right" :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.noComplicationDrgs>0" class='skip'
                         @click="queryMedicalNumByType(scope.row,'col3')">
                      {{ scope.row.noComplicationDrgs | formatIsEmpty }}
                    </div>
                    <div v-if="scope.row.index=='DRGs组数'&&scope.row.noComplicationDrgs>0" class='skip'
                         @click="queryDrgGroupNumByType(scope.row,'col3')">
                      {{ scope.row.noComplicationDrgs | formatIsEmpty }}
                    </div>
                    <div
                      v-if="(scope.row.index!='病案数'&&scope.row.index!='DRGs组数') || scope.row.noComplicationDrgs==0"
                      style="color:#000000">
                      {{ scope.row.noComplicationDrgs | formatIsEmpty }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="伴合并症或并发症的DRGs组" align="right" :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.comorbiditiesComplicationDrgs>0" class='skip'
                         @click="queryMedicalNumByType(scope.row,'col7')">
                      {{ scope.row.comorbiditiesComplicationDrgs | formatIsEmpty }}
                    </div>
                    <div v-if="scope.row.index=='DRGs组数'&&scope.row.comorbiditiesComplicationDrgs>0" class='skip'
                         @click="queryDrgGroupNumByType(scope.row,'col7')">
                      {{ scope.row.comorbiditiesComplicationDrgs | formatIsEmpty }}
                    </div>
                    <div
                      v-if="(scope.row.index!='病案数'&&scope.row.index!='DRGs组数') || scope.row.comorbiditiesComplicationDrgs==0"
                      style="color:#000000">
                      {{ scope.row.comorbiditiesComplicationDrgs | formatIsEmpty }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="不伴严重合并症或并发症的DRGs组" align="right" :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.noSeriousComorbiditiesComplicationDrgs>0"
                         class='skip' @click="queryMedicalNumByType(scope.row,'col8')">
                      {{ scope.row.noSeriousComorbiditiesComplicationDrgs | formatIsEmpty }}
                    </div>
                    <div v-if="scope.row.index=='DRGs组数'&&scope.row.noSeriousComorbiditiesComplicationDrgs>0"
                         class='skip' @click="queryDrgGroupNumByType(scope.row,'col8')">
                      {{ scope.row.noSeriousComorbiditiesComplicationDrgs | formatIsEmpty }}
                    </div>
                    <div
                      v-if="(scope.row.index!='病案数'&&scope.row.index!='DRGs组数') || scope.row.noSeriousComorbiditiesComplicationDrgs==0"
                      style="color:#000000">
                      {{ scope.row.noSeriousComorbiditiesComplicationDrgs | formatIsEmpty }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="未作区分的DRGs组" align="right" :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.notDifferentiatedDrgs>0" class='skip'
                         @click="queryMedicalNumByType(scope.row,'col9')">
                      {{ scope.row.notDifferentiatedDrgs | formatIsEmpty }}
                    </div>
                    <div v-if="scope.row.index=='DRGs组数'&&scope.row.notDifferentiatedDrgs>0" class='skip'
                         @click="queryDrgGroupNumByType(scope.row,'col9')">
                      {{ scope.row.notDifferentiatedDrgs | formatIsEmpty }}
                    </div>
                    <div
                      v-if="(scope.row.index!='病案数'&&scope.row.index!='DRGs组数') || scope.row.notDifferentiatedDrgs==0"
                      style="color:#000000">
                      {{ scope.row.notDifferentiatedDrgs | formatIsEmpty }}
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="9" style="height: 100%">
              <el-table ref="rightTable"
                        size="mini"
                        :header-cell-style="{'text-align' : 'center'}"
                        stripe
                        height="100%"
                        :data="deptGroupList"
                        style="width: 100%;"
                        v-loading="listLoading"
                        border>
                <el-table-column label="指标" align="left" width="100" :show-overflow-tooltip="true">
                  <template slot-scope="scope">{{ scope.row.index }}</template>
                </el-table-column>
                <el-table-column label="内科组" align="right" :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.medicalDeptDrgs>0" class='skip'
                         @click="queryMedicalNumByType(scope.row,'col4')">
                      {{ scope.row.medicalDeptDrgs | formatIsEmpty }}
                    </div>
                    <div v-if="scope.row.index=='DRGs组数'&&scope.row.medicalDeptDrgs>0" class='skip'
                         @click="queryDrgGroupNumByType(scope.row,'col4')">
                      {{ scope.row.medicalDeptDrgs | formatIsEmpty }}
                    </div>
                    <div v-if="(scope.row.index!='病案数'&&scope.row.index!='DRGs组数') || scope.row.medicalDeptDrgs==0"
                         style="color:#000000">
                      {{ scope.row.medicalDeptDrgs | formatIsEmpty }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="非手术室操作组" align="right" :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.notOperationDrgs>0" class='skip'
                         @click="queryMedicalNumByType(scope.row,'col5')">
                      {{ scope.row.notOperationDrgs | formatIsEmpty }}
                    </div>
                    <div v-if="scope.row.index=='DRGs组数'&&scope.row.notOperationDrgs>0" class='skip'
                         @click="queryDrgGroupNumByType(scope.row,'col5')">
                      {{ scope.row.notOperationDrgs | formatIsEmpty }}
                    </div>
                    <div
                      v-if="(scope.row.index!='病案数'&&scope.row.index!='DRGs组数') || scope.row.notOperationDrgs==0"
                      style="color:#000000">
                      {{ scope.row.notOperationDrgs | formatIsEmpty }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="外科组" align="right" :show-overflow-tooltip="true">
                  <template slot-scope="scope">
                    <div v-if="scope.row.index=='病案数'&&scope.row.surgeryDeptDrgs>0" class='skip'
                         @click="queryMedicalNumByType(scope.row,'col6')">
                      {{ scope.row.surgeryDeptDrgs | formatIsEmpty }}
                    </div>
                    <div v-if="scope.row.index=='DRGs组数'&&scope.row.surgeryDeptDrgs>0" class='skip'
                         @click="queryDrgGroupNumByType(scope.row,'col6')">
                      {{ scope.row.surgeryDeptDrgs | formatIsEmpty }}
                    </div>
                    <div v-if="(scope.row.index!='病案数'&&scope.row.index!='DRGs组数') || scope.row.surgeryDeptDrgs==0"
                         style="color:#000000">
                      {{ scope.row.surgeryDeptDrgs | formatIsEmpty }}
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </div>

        <div class="table-container" style="height: 65%">
          <el-table ref="drgsListTable"
                    id="drgsTable"
                    size="mini"
                    :header-cell-style="{'text-align' : 'center'}"
                    height="100%"
                    stripe
                    :data="list"
                    :summary-method="getSummaries"
                    show-summary
                    v-loading="listLoading"
                    @sort-change='sortChange'
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column
              label="序号"
              type="index"
              align="center"
              width="50">
            </el-table-column>
            <el-table-column label="DRGs编码" prop="drgsCode" align="center" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div class='skip' @click="queryMedicalNum(scope.row)">
                  {{ scope.row.drgsCode | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="DRGs名称" prop="drgsName" width="200" align="left" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div class='skip' @click="queryMedicalNum(scope.row)">
                  {{ scope.row.drgsName | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="入组病案数" align="center" width="120" prop="medcasVal" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.medcasVal)>0" class='skip' @click="queryMedicalNum(scope.row)">
                  {{ scope.row.medcasVal | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.medcasVal)==0" style="color:#000000">
                  {{ scope.row.medcasVal | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="科室数" align="center" width="90" prop="deptNum" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.deptNum)>0" class='skip' @click="queryDeptNum(scope.row)">
                  {{ scope.row.deptNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.deptNum)==0" style="color:#000000">
                  {{ scope.row.deptNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="标杆年份" prop="standardYear" align="center" width="80"
                             :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.standardYear | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="权重" prop="drgWt" align="center" width="100" :show-overflow-tooltip="true"
                             sortable='custom'>
              <template slot-scope="scope">{{ scope.row.drgWt | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="总权重" align="center" width="110" :show-overflow-tooltip="true"
                             prop="totalDrgWeight" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.totalDrgWeight | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="平均住院费用" prop="byAvgCost" align="right" width="110"
                             :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.byAvgCost | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="标杆住院费用" prop="avgCost" align="right" width="110"
                             :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.avgCost | formatIsEmpty }}</template>
            </el-table-column>
            byAvgDays
            <el-table-column label="平均住院日" prop="byAvgDays" align="center" width="110"
                             :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.byAvgDays | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="标杆住院日" prop="avgDays" align="center" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.avgDays | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="时间消耗指数" prop="timeIndex" align="center" width="100"
                             :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.timeIndex | formatIsEmpty }}</template>
              no
            </el-table-column>
            <el-table-column label="费用消耗指数" prop="costIndex" align="center" width="100"
                             :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.costIndex | formatIsEmpty }}</template>
            </el-table-column>
            <!--  <el-table-column label="对比" width="100" align="center">
                <template slot-scope="scope">
                  <el-button type="text" icon="som-icon-compare" @click="queryDetails(scope.row)"></el-button>
                </template>
              </el-table-column>-->
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, queryLikeDrgsByPram, querySelectTreeAndSelectList } from '@/api/common/drgCommon'
import { fetchList as queryPageData, getCountInfo } from '@/api/hospitalAnalysis/drgsAnalysis'
import { elExportExcel } from '@/utils/exportExcel'
import { sortChange } from '@/utils/common'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  b16c: null,
  queryDrg: '',
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date
}
export default {
  name: 'groupAnalysis',
  inject: ['reload'],
  components: {},
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      tempList: [],
      listLoading: true,
      list: null,
      ifComplicationGroupList: null,
      deptGroupList: null,
      total: null,
      judgmentValue: false,
      cy_start_date: null,
      cy_end_date: null,
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      deptName: null,
      tableHeight: 0,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created () {
    if (!this.$somms.hasDeptRole()) {
      this.deptName = this.$store.getters.getDeptName
    }
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.drgsListTable.$el.offsetTop：表格距离浏览器的高度
      // 35（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.drgsListTable.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.drgsListTable.$el.offsetTop - 35
      }
    })
  },
  // 在vue生命周期updated中添加方法（使用该方法要给table里加ref="table"）
  updated: function () {
    this.$nextTick(() => {
      this.$refs['drgsListTable'].doLayout()
    })
  },
  methods: {
    sortChange,
    handleSelectionChange (val) {
      if (val.length == 0) {
        this.tempList = []
      } else {
        this.tempList = this.list.filter(data => {
          for (const v of val) {
            if (data.drgsCode == v.drgsCode) {
              return true
            }
          }
          return false
        })
      }
    },
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length,
        customAverage: (values) => {
          const sum = values.reduce((prev, curr) => prev + curr, 0)
          return sum / values.length
        }
      }
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计'
          return
        }
        const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value))
        if (index === 3 || index === 4) {
          sums[index] = calculations.sum(values)
        } else if (index === 6 || index === 7) {
          sums[index] = calculations.sum(values).toFixed(2)
        } else if (index === 5) {
          sums[index] = calculations.average(values)
        } else if (index === 8 || index === 9 || index === 10 || index === 11 || index === 12 || index === 13) {
          sums[index] = calculations.average(values).toFixed(2)
        } else {
          sums[index] = ' '
        }
      })
      return sums
    },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
        this.getCount()
      })
    },
    // 下面表统计结果
    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryDrg = this.listQuery.drgCodg
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.dataAuth = true
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.tempList = this.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.drgsListTable.$children, document.getElementById('drgsTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DRG病组分析')
    },
    // 上面两张表统计结果
    getCount () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryDrg = this.listQuery.drgCodg
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      getCountInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        this.ifComplicationGroupList = response.data
        this.deptGroupList = response.data
      })
    },
    // 下转
    queryMedicalNum (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryDrgsCode: row.drgsCode,
          queryDrgsName: row.drgsName,
          priOutHosDeptCode: this.submitListQuery.b16c,
          priOutHosDeptName: deptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2',
          standardYear: row.standardYear
        }
      })
    },
    queryDeptNum (row) {
      this.$router.push({
        path: '/common/queryDeptDetail',
        query: {
          queryDrgsCode: row.drgsCode,
          queryDrgsName: row.drgsName,
          priOutHosDeptCode: this.submitListQuery.b16c,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '2'
        }
      })
    },
    queryMedicalNumByType (row, col) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      let queryDrgsNameStr = ''
      if (this.list.length == 1) {
        queryDrgsNameStr = this.list.drgsName
      }
      let queryTypeStr = ''
      switch (col) {
        case 'col1':
          queryTypeStr = 'seriousComplicationDrgsMedicalNum'
          break
        case 'col2':
          queryTypeStr = 'normalComplicationDrgsMedicalNum'
          break
        case 'col3':
          queryTypeStr = 'noComplicationDrgsMedicalNum'
          break
        case 'col4':
          queryTypeStr = 'medicalDeptDrgsMedicalNum'
          break
        case 'col5':
          queryTypeStr = 'notOperationDrgsMedicalNum'
          break
        case 'col6':
          queryTypeStr = 'surgeryDeptDrgsMedicalNum'
          break
        case 'col7':
          queryTypeStr = 'comorbiditiesComplicationDrgsMedicalNum'
          break
        case 'col8':
          queryTypeStr = 'noSeriousComorbiditiesComplicationDrgsMedicalNum'
          break
        case 'col9':
          queryTypeStr = 'notDifferentiatedDrgsMedicalNum'
          break
      }
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: queryTypeStr,
          priOutHosDeptCode: this.submitListQuery.b16c,
          priOutHosDeptName: deptName,
          queryDrgsCode: this.submitListQuery.queryDrg,
          queryDrgsName: queryDrgsNameStr,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryDrgGroupNumByType (row, col) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      let queryDrgsNameStr = ''
      if (this.list.length == 1) {
        queryDrgsNameStr = this.list.drgsName
      }
      let queryTypeStr = ''
      switch (col) {
        case 'col1':
          queryTypeStr = 'seriousComplicationDrgsDrgNum'
          break
        case 'col2':
          queryTypeStr = 'normalComplicationDrgsDrgNum'
          break
        case 'col3':
          queryTypeStr = 'noComplicationDrgsDrgNum'
          break
        case 'col4':
          queryTypeStr = 'medicalDeptDrgsDrgNum'
          break
        case 'col5':
          queryTypeStr = 'notOperationDrgsDrgNum'
          break
        case 'col6':
          queryTypeStr = 'surgeryDeptDrgsDrgsDrgNum'
          break
        case 'col7':
          queryTypeStr = 'comorbiditiesComplicationDrgsMedicalNum'
          break
        case 'col8':
          queryTypeStr = 'noSeriousComorbiditiesComplicationDrgsMedicalNum'
          break
        case 'col9':
          queryTypeStr = 'notDifferentiatedDrgsMedicalNum'
          break
      }
      this.$router.push({
        path: '/common/queryDrgDetail',
        query: {
          queryType: queryTypeStr,
          priOutHosDeptCode: this.submitListQuery.b16c,
          priOutHosDeptName: deptName,
          queryDrgsCode: this.submitListQuery.queryDrg,
          queryDrgsName: queryDrgsNameStr,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '2'
        }
      })
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    queryDetails (row) {
      this.$router.push({
        path: '/auliManage/diseGrpCompar',
        query: {
          deptCode: this.listQuery.b16c,
          id: row.drgsCode,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '3',
          queryType: (this.listQuery.b16c != null && this.judgmentValue) ? '1' : '3'
        }
      })
    },
    handleSelect (item) {
      this.listQuery.queryDrg = item.drgsCode
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.handleSearchList()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.judgmentValue = true
      this.listQuery.pageNum = 1
      if (this.listQuery.b16c) {
        this.deptName = document.getElementById('b13c').getElementsByTagName('input')[0].value
      } else {
        this.deptName = null
      }
      this.getList()
      this.getCount()
    },
    nodeClick () {
      this.judgmentValue = false
    },
    // 重置页面
    handleResetSearch () {
      // this.listQuery = Object.assign({}, defaultListQuery);
      // this.getDataIsuue();
      this.reload()
    },
    exportExcel () {
      let tableId = 'drgsTable'
      let fileName = 'DRGs病组分析' + '(' + this.listQuery.begnDate + '-' + this.listQuery.expiDate + ')'
      elExportExcel(tableId, fileName)
    }
  }
}
</script>
<style scoped>
/deep/ .el-table__body td {
  padding: 0;
  height: 32px;
}

/deep/ .el-scrollbar__wrap {
  max-height: 450px;
}

/deep/ .el-autocomplete-suggestion li {
  line-height: 27px;
}

.code {
  font-size: 12px;
  color: #000000;
  text-overflow: ellipsis;
  overflow: hidden;
}

.name {
  font-size: 10px;
  color: #9b9b9b;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
