<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-hos-dept
             show-drg
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="费用分析"
             :container="true"
             @query="handleSearchList" @reset="handleResetSearch">

<!--      <template slot="buttons">-->
<!--        <el-popconfirm-->
<!--          confirm-button-text='确定'-->
<!--          cancel-button-text='导出全部'-->
<!--          icon="el-icon-info"-->
<!--          icon-color="red"-->
<!--          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">-->
<!--          <el-button slot="reference" type="success">导出Excel</el-button>-->
<!--        </el-popconfirm>-->
<!--      </template>-->

      <template slot="buttons">
        <el-button type="success" @click="exportExcel" style="margin-right: 15px">导出Excel</el-button>
      </template>

      <template slot="buttonsMiddle">
        <el-radio-group v-model="listQuery.queryType" size="mini" @change="changeSelectQueryType" v-if="this.$somms.hasHosRole()">
          <el-radio-button :label="1" >科室查询</el-radio-button>
          <el-radio-button :label="2">病组查询</el-radio-button>
        </el-radio-group>
        <el-radio-group v-model="listQuery.dataType" size="mini" @change="changeSelectDataType" class="som-el-form-item-margin-left">
          <el-radio-button :label="1">结算清单</el-radio-button>
          <el-radio-button :label="2">病案首页</el-radio-button>
        </el-radio-group>
      </template>

      <template slot="containerContent">
        <div style="height:34%">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="14" style="height: 100%">
              <div id="medicalTreatmentCostChart" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
            <el-col :span="10" style="height: 100%">
              <div id="fundCostChart" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="height: 66%">
          <el-table ref="costAnalysisTable"
                    id="costTable"
                    :key=Math.random()
                    size="mini"
                    :header-cell-style = "{'text-align' : 'center'}"
                    height="100%"
                    stripe
                    :data="list"
                    show-summary
                    v-loading="listLoading"
                    :default-sort = "{prop: 'sumfee', order: 'ascending'}"
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column fixed
                             label="序号"
                             align="center"
                             type="index"
                             width="50">
            </el-table-column>

            <el-table-column label="DRG编码" prop="drgCodg" align="center" width="150" :show-overflow-tooltip="true" v-if="false">
              <template slot-scope="scope">{{scope.row.drgCodg | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="DRG名称" prop="drgName" align="left" width="200" :show-overflow-tooltip="true" v-if="showDrgs">
              <template slot-scope="scope">{{scope.row.drgName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="出院科室编码" prop="priOutHosDeptCode" align="center" width="160" :show-overflow-tooltip="true"  v-if="false">
              <template slot-scope="scope">{{scope.row.priOutHosDeptCode | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="出院科室名称" fixed prop="priOutHosDeptName" align="left" width="160" :show-overflow-tooltip="true" v-if="showDept">
              <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="住院总费用"  prop="iptSumfee" align="right" width="120"  :show-overflow-tooltip='true' v-if="showJsqd" sortable>
              <template slot-scope="scope">{{scope.row.iptSumfee | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="医保报销基金"  prop="medInsFund" align="right" width="130"  :show-overflow-tooltip='true' v-if="showJsqd" sortable>
              <template slot-scope="scope">{{scope.row.medInsFund | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="个人自付" prop="psnSelfpay" align="right" width="120" :show-overflow-tooltip="true" v-if="showJsqd">
              <template slot-scope="scope">{{scope.row.psnSelfpay | formatIsEmpty}}</template>
            </el-table-column>
            <!--结算清单基金支付、个人支付-->
            <tr v-for="(col,index) in fundPayCols" :key="col.label">
              <el-table-column :prop="col.prop" :label="col.label" align="right" width="160" :show-overflow-tooltip='true' v-if="showJsqd"></el-table-column>
            </tr>

            <tr v-for="(col,index) in medicalCostCols" :key="col.label">
              <el-table-column :prop="col.prop" :label="col.label" align="right" width="120" :show-overflow-tooltip='true' v-if="showJsqd"></el-table-column>
            </tr>

            <el-table-column label="个人自费" prop="psnOwnpay" align="right" width="100" :show-overflow-tooltip="true" v-if="showJsqd">
              <template slot-scope="scope">{{scope.row.psnOwnpay | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="个人负担" align="center" width="120" :show-overflow-tooltip="true" v-if="showJsqd">
              <template slot-scope="scope">
                {{ ((scope.row.psnSelfpay || 0) + (scope.row.psnOwnpay || 0)).toFixed(2) }}
              </template>
            </el-table-column>

            <el-table-column label="个人账户支付" prop="acctPay" align="right" width="100" :show-overflow-tooltip="true" v-if="showJsqd">
              <template slot-scope="scope">{{scope.row.acctPay | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="个人现金支付" prop="psnCashpay" align="right" width="100" :show-overflow-tooltip="true" v-if="showJsqd">
              <template slot-scope="scope">{{scope.row.psnCashpay | formatIsEmpty}}</template>
            </el-table-column>

            <!--病案首页字段-->
            <el-table-column label="住院总费用"  prop="iptSumfee" align="right" width="120"  :show-overflow-tooltip='true' v-if="showBasy" sortable>
              <template slot-scope="scope">{{scope.row.iptSumfee | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="自付金额" prop="iptSumfeeInSelfpayAmt" align="right" width="120" v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.iptSumfeeInSelfpayAmt }}</template>
            </el-table-column>
            <el-table-column label="自付金额占比" prop="inhosSelfPayCostRate" align="center" width="120"  :show-overflow-tooltip='true' v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.inhosSelfPayCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="综合医疗服务费" prop="comMedServfee" align="right" width="120"  v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.comMedServfee }}</template>
            </el-table-column>
            <el-table-column label="综合医疗服务费占比" prop="serviceCostRate" align="center" width="120"  :show-overflow-tooltip='true' v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.serviceCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="康复费" prop="rhabFee" align="right" width="120"  v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.rhabFee }}</template>
            </el-table-column>
            <el-table-column label="康复费占比" prop="recoverCostRate" align="center" width="120"  :show-overflow-tooltip='true' v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.recoverCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="诊断费" prop="diagFee" align="right" width="120"  v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.diagFee }}</template>
            </el-table-column>
            <el-table-column label="诊断费占比" prop="diagnoseCostRate" align="center" width="120"  :show-overflow-tooltip='true' v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.diagnoseCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="治疗费" prop="treatFee" align="right" width="120"  v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.treatFee }}</template>
            </el-table-column>
            <el-table-column label="治疗费占比" prop="treatmentCostRate" align="center" width="120"  :show-overflow-tooltip='true' v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.treatmentCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="药品费" prop="drugfee" align="right" width="120"  v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.drugfee }}</template>
            </el-table-column>
            <el-table-column label="药品费占比" prop="medicalCostRate" align="center" width="120"  :show-overflow-tooltip='true' v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.medicalCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="血液和血液制品类费" prop="bloodBloPro" align="right" width="120"  v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.bloodBloPro }}</template>
            </el-table-column>
            <el-table-column label="血液和血液制品类费占比" prop="bloodCostRate" align="center" width="120"  :show-overflow-tooltip='true' v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.bloodCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="耗材费" prop="mcsFee" align="right" width="120"  v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.mcsFee }}</template>
            </el-table-column>
            <el-table-column label="耗材费占比" prop="materialCostRate" align="center" width="120"  :show-overflow-tooltip='true' v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.materialCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="中医其他" prop="tcmOth" align="right" width="120"  v-if="showBasy&&showZy" >
              <template slot-scope="scope">{{scope.row.tcmOth }}</template>
            </el-table-column>
            <el-table-column label="中医其他占比" prop="chineseOtherRate" align="center" width="120"  :show-overflow-tooltip='true' v-if="showZy&&showZy" >
              <template slot-scope="scope">{{scope.row.chineseOtherRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="其他费" prop="othFee" align="right" width="120"  v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.othFee }}</template>
            </el-table-column>
            <el-table-column label="其他费占比" prop="otherCostRate" align="center" width="120"  :show-overflow-tooltip='true' v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.otherCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="抗生素费" prop="abtFee" align="right" width="120"  v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.abtFee }}</template>
            </el-table-column>
            <el-table-column label="抗生素费占比" prop="antibioticCostRate" align="center" width="120"  :show-overflow-tooltip='true' v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.antibioticCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="检验费" prop="inspectFee" align="right" width="120"  v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.inspectFee }}</template>
            </el-table-column>
            <el-table-column label="检验费占比" prop="inspectionCostRate" align="center" width="120"  :show-overflow-tooltip='true' v-if="showBasy" >
              <template slot-scope="scope">{{scope.row.inspectionCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="对比" width="100" fixed="right" align="center" v-if="showDept">
              <template slot-scope="scope">
                <el-button type="text" icon="som-icon-compare" @click="queryDetails(scope.row)"></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, querySelectTreeAndSelectList, getCols, queryLikeDrgsByPram } from '@/api/common/drgCommon'
import { fetchList as queryPageData, getCountInfo } from '@/api/hospitalAnalysis/costAnalysis'
import { elExportExcel } from '@/utils/exportExcel'
import echarts from 'echarts'
import { sortChange } from '@/utils/common'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null, // [getDefultStartDate(),getDefultStartDate()]
  b16c: null,
  queryDrg: null,

  dataType: '1'// 默认医保结算清单
}
export default {
  name: 'feeAnalysis',
  inject: ['reload'],
  components: {},
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      tempList: [],
      listLoading: true,
      list: null,
      total: null,
      cy_start_date: null,
      cy_end_date: null,
      listQuery: { ...Object.assign({}, defaultListQuery), queryType: '1' }, // 默认科室查询
      submitListQuery: Object.assign({}, defaultListQuery),
      b16c: null,
      queryDrg: null,
      queryType: '1',
      dataType: '1',
      showDrgs: false,
      showDept: true,
      tableHeight: 0,
      showJsqd: true,
      showBasy: false,
      showZy: false,
      medicalCostCols: [],
      fundPayCols: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      k: 0
    }
  },
  updated () {
    this.$nextTick(() => {
      this.$refs.costAnalysisTable.doLayout()
    })
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(() => {
      this.findSelectTreeAndSelectList()
      // 获取数据查询时间
      this.getDataIsuue(() => {
        if (!this.$somms.hasHosRole()) {
          this.listQuery.queryType = '2'
          this.changeSelectQueryType(2)
        } else {
          this.listQuery.queryType = '1'
          this.changeSelectQueryType(1)
        }
      })
    })
  },
  methods: {
    handleSelectionChange (val) {
      if (val.length == 0) {
        this.tempList = []
      } else {
        this.tempList = this.list.filter(data => {
          for (const v of val) {
            if (data.priOutHosDeptCode == v.priOutHosDeptCode) {
              return true
            }
          }
          return false
        })
      }
    },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue (reject) {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        reject()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryDrg = this.listQuery.drgCodg
      this.submitListQuery.queryType = this.listQuery.queryType
      this.submitListQuery.dataType = this.listQuery.dataType
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      if (!this.submitListQuery.cy_start_date && !this.submitListQuery.seStartTime && !this.submitListQuery.inStartTime) {
        return
      }
      // 在查询数据报表前，先查询动态列并进行渲染
      getCols(this.submitListQuery).then(response => {
        this.medicalCostCols = response.data.medicalCostCols
        if (this.medicalCostCols.length > 0) {
          for (let i = 0; i < this.medicalCostCols.length; i++) {
            if (this.medicalCostCols[i].label == '01') {
              this.medicalCostCols[i].label = '床位费'
            }
            if (this.medicalCostCols[i].label == '02') {
              this.medicalCostCols[i].label = '诊察费'
            }
            if (this.medicalCostCols[i].label == '03') {
              this.medicalCostCols[i].label = '检查费'
            }
            if (this.medicalCostCols[i].label == '04') {
              this.medicalCostCols[i].label = '化验费'
            }
            if (this.medicalCostCols[i].label == '05') {
              this.medicalCostCols[i].label = '治疗费'
            }
            if (this.medicalCostCols[i].label == '06') {
              this.medicalCostCols[i].label = '手术费'
            }
            if (this.medicalCostCols[i].label == '07') {
              this.medicalCostCols[i].label = '护理费'
            }
            if (this.medicalCostCols[i].label == '08') {
              this.medicalCostCols[i].label = '卫生材料费'
            }
            if (this.medicalCostCols[i].label == '09') {
              this.medicalCostCols[i].label = '西药费'
            }
            if (this.medicalCostCols[i].label == '10') {
              this.medicalCostCols[i].label = '中药饮片费'
            }
            if (this.medicalCostCols[i].label == '11') {
              this.medicalCostCols[i].label = '中成药费'
            }
            if (this.medicalCostCols[i].label == '12') {
              this.medicalCostCols[i].label = '一般诊疗费'
            }
            if (this.medicalCostCols[i].label == '13') {
              this.medicalCostCols[i].label = '挂号费'
            }
            if (this.medicalCostCols[i].label == '14') {
              this.medicalCostCols[i].label = '其他费'
            }
            if (this.medicalCostCols[i].label == '15') {
              this.medicalCostCols[i].label = '氧费'
            }
          }
        }
        this.fundPayCols = response.data.fundPayCols
      })
      // 查询报表
      // if (this.tempList.length > 0 && this.submitListQuery.b16c==null){
      //   this.list = this.tempList
      //   this.listLoading = false;
      // } else {
      queryPageData(this.submitListQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.tempList = this.list
        this.listLoading = false
      })
      // }
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.costAnalysisTable.$children, document.getElementById('costTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DRG费用分析')
    },
    getCount () {
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryDrg = this.listQuery.drgCodg
      this.submitListQuery.queryType = this.listQuery.queryType
      this.submitListQuery.dataType = this.listQuery.dataType
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      getCountInfo(this.submitListQuery).then(response => {
        let result = response.data
        let barData = [] // 费用数据
        let xAxisData = [] // 费用名称
        let costRate = [] // 费用占比
        let lineData = [] // 累计占比

        if (result) {
          this.k = 0 // 分割前半部分的基本数据和后半部分的基金支付和个人支付信息标志
          for (let i = 0; i < result.length - 1; i++) {
            if (result[i].name == '个人账户支付') {
              this.k = i
            }
          }
          let sumfee = Number(result[result.length - 1].value) // 总费用
          let accumulativeCost = 0 // 累计费用
          for (let i = 0; i < this.k; i++) { // 基本医疗费用
            if (result[i].name != '抗生素费' || result[i].name != '检验费') {
              accumulativeCost += Number(result[i].value)
            }
            barData.push(Number(result[i].value))
            xAxisData.push(result[i].name)
            costRate.push((Number(result[i].value) / sumfee * 100).toFixed(2)) // 费用占比
            lineData.push((accumulativeCost / sumfee * 100).toFixed(2)) // 累计费用占比
          }
        }
        let legendData = ['费用', '累计占比']
        let profttl = this.showJsqd ? '全院医保结算清单费用分布柏拉图' : '全院病案首页费用分布柏拉图'
        this.leftChart(barData, xAxisData, lineData, costRate, legendData, profttl)
        let fundCost = []
        let totalFund = 0
        if (result && result.length > 0) {
          for (let i = this.k; i < result.length - 1; i++) { // 基金支付和个人支付
            fundCost.push(result[i])
            totalFund += Number(result[i].value)
          }
        }
        this.rightChart(fundCost, totalFund)
      })
    },
    changeSelectQueryType (value) {
      if (value == 1) {
        this.showDrgs = false
        this.showDept = true
        this.queryDrg = null
      } else if (value == 2) {
        this.showDrgs = true
        this.showDept = false
        this.b16c = null
      }
      this.getList()
      this.getCount()
    },
    changeSelectDataType (value) {
      if (value == 1) {
        this.showJsqd = true
        this.showBasy = false
      } else if (value == 2) {
        this.showJsqd = false
        this.showBasy = true
      }
      this.getList()
      this.getCount()
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSelect (item) {
      this.listQuery.queryDrg = item.drgsCode
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.handleSearchList()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.getList()
      this.getCount()
    },
    handleResetSearch () {
      this.reload()
    },
    // 下转
    queryDetails (row) {
      this.$router.push({
        path: '/auliManage/deptCompar',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          cy_start_date: this.cy_start_date,
          cy_end_date: this.cy_end_date,
          type: '3'
        }
      })
    },
    leftChart (barData, xAxisData, lineData, costRate, legendData, profttl) {
      let option = {
        title: [{ text: profttl, left: 'left', top: '1', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }],
        tooltip: {
          trigger: 'item',
          formatter: function (param) {
            return '费用名称：' + xAxisData[param.dataIndex] + '</br>' +
                '费用额度：' + '：' + barData[param.dataIndex] + '元' + '</br>' +
                '费用占比：' + costRate[param.dataIndex] + '%' + '</br>' +
                '累计占比：' + lineData[param.dataIndex] + '%'
          }
        },
        legend: [{
          data: legendData,
          top: '1',
          left: 'right',
          selectedMode: false,
          formatter: function (name) {
            if (name == '费用') {
              return name + '（绿柱：排名前5）'
            } else if (name == '累计占比') {
              return name + '（红点：累计前80%）'
            }
          }
        }],
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            interval: 0,
            fontSize: 10,
            rotate: 40,
            formatter: function (value) {
              return (value.length > 4 ? (value.slice(0, 4) + '..') : value)
            }
          }
        },
        yAxis: [
          { type: 'value',
            position: 'left',
            name: '单位：元',
            axisLabel: {
              formatter: function (value) {
                if (value > 10000) {
                  return (Number(value) / 10000).toFixed(1) + '万'
                } else {
                  return Number(value).toFixed(1)
                }
              }
            }
          },
          { type: 'value', position: 'right', name: '单位：%', splitLine: { show: false } }
        ],
        series: [{
          name: legendData[0],
          data: barData,
          type: 'bar',
          color: 'red',
          yAxisIndex: 0,
          label: {
            show: true,
            fontSize: 9,
            position: 'top',
            formatter: function (param) {
              if (param.value >= 10000) {
                return (Number(param.value) / 10000).toFixed(1) + '万'
              } else {
                return Number(param.value).toFixed(2)
              }
            }
          },
          itemStyle: {
            color: function (param) {
              if (param.dataIndex < 5) {
                return 'rgba(36,185,179,0.7)'
              } else {
                return 'rgba(40,138,242,0.7)'
              }
            }
          }
        },
        {
          name: legendData[1],
          data: lineData,
          type: 'line',
          symbol: 'circle',
          yAxisIndex: 1,
          label: {
            show: true,
            position: 'top',
            fontSize: 10,
            formatter: function (param) {
              return Number(param.value).toFixed(2)
            }
          },
          itemStyle: {
            color: function (param) {
              if (param.value < 80) {
                return '#FD5E51'
              } else {
                return '#516FFF'
              }
            }

          }
        }],
        grid: {
          top: '55',
          bottom: '40',
          left: '60',
          right: '30'
        }
      }
      let medicalTreatmentCostChart = echarts.getInstanceByDom(document.getElementById('medicalTreatmentCostChart'))
      if (medicalTreatmentCostChart) {
        medicalTreatmentCostChart.clear()
      } else {
        medicalTreatmentCostChart = echarts.init(document.getElementById('medicalTreatmentCostChart'))
      }
      medicalTreatmentCostChart.setOption(option)
      return medicalTreatmentCostChart
    },
    rightChart (fundCost, totalFund) {
      let colors = ['#288AF2', '#4BA2FF', '#0BA2B3', '#26B9B5', '#A0D240', '#F3BE4C', '#E38F3B', '#D3D3D3', '#3945DA', '#4C6DE8']
      let get = function (e) {
        let newStr = ''
        let name_len = e.name.length // 每个内容名称的长度
        let max_name = 4 // 每行最多显示的字数
        if (name_len > max_name) {
          newStr = e.name.slice(0, max_name) + '...' // 拼接字符串,超出部分省略号
        } else {
          newStr = e.name
        }
        let rate = ((Number(e.value) / (Number(totalFund) == 0 ? 1 : Number(totalFund))) * 100).toFixed(1) + '%'
        let result = newStr + '(' + rate + ')'
        return result
      }
      let option = {
        title: [
          { text: '医保基金支付各项费用统计图', left: 'center', top: 1, textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: '10',
          top: '15',
          bottom: '0',
          type: 'scroll',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: { color: '#666666' },
          formatter: function (name) {
            let strs
            for (let i = 0; i < fundCost.length; i++) {
              if (name == fundCost[i].name) {
                strs = fundCost[i].name
                break
              }
            }
            if (strs) {
              let str = ''
              for (let i = 0; i < strs.length; i++) {
                str += strs[i]
                if (i !== 0 && i % 5 === 0) {
                  str += '\n'
                }
              }
              return str
            } else {
              return ''
            }
          }
        },
        series: [
          {
            type: 'pie',
            radius: '60%', // 设置饼图大小
            center: ['60%', '55%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'outside',
                backgroundColor: '#F0F8FF',
                borderColor: '#aaa',
                borderWidth: 1,
                borderRadius: 4,
                formatter: get,
                fontFamily: 'Microsoft YaHei',
                fontSize: 12,
                color: '#000000',
                padding: [5, 5]
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % fundCost.length]
                }
              }
            },
            data: fundCost
          }
        ]
      }

      let fundCostChart = echarts.getInstanceByDom(document.getElementById('fundCostChart'))
      if (fundCostChart) {
        fundCostChart.clear()
      } else {
        fundCostChart = echarts.init(document.getElementById('fundCostChart'))
      }
      fundCostChart.setOption(option)
      return fundCostChart
    },
    exportExcel () {
      let tableId = 'costTable'
      let fileName = '医院费用分析' + '(' + this.listQuery.begnDate + '-' + this.listQuery.expiDate + ')'
      elExportExcel(tableId, fileName)
    }
  }
}
</script>
<style scoped>
  /deep/ .el-scrollbar__wrap {
    max-height: 450px;
  }
  /deep/ .el-autocomplete-suggestion li {
    line-height: 20px;
  }
  .code {
    font-size: 12px;
    color: #000000;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .name {
    font-size: 10px;
    color: #9b9b9b;
    text-overflow: ellipsis;
    overflow: hidden;
  }
</style>
