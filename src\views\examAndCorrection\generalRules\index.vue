<template>
  <div class="rule-config-container">
    <drg-title-line title="规则信息"/>
    <!-- 顶部规则配置表单 -->
    <el-form class="top-form" :model="formData" label-width="150px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="规则场景:">
            <drg-dict-select v-model="formData.ruleScenType" placeholder="请选择" dicType="RULE_SCEN_TYPE">
              <!-- 可添加更多选项 -->
            </drg-dict-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="规则类型:">
            <drg-dict-select v-model="formData.ruleTypeCodg" placeholder="请选择" dicType="RULE_TYPE_CODE" :type="2">
              <!-- 可添加更多选项 -->
            </drg-dict-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="算子类型">
            <drg-dict-select v-model="formData.opraType" placeholder="请选择" dicType="OPRA_TYPE_CODE" :type="2">
              <!-- 可添加更多选项 -->
            </drg-dict-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="数据分组:">
            <drg-dict-select v-model="formData.dataGrp" placeholder="请选择" dicType="DATA_TYPE_CODE">
              <!-- 可添加更多选项 -->
            </drg-dict-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="规则年度:">
            <el-date-picker
              v-model="formData.ruleYear"
              type="year"
              placeholder="选择年份"
              format="yyyy"
              value-format="yyyy">
            </el-date-picker>
            <!-- <el-input v-model="formData.ruleYear" placeholder="请输入"></el-input> -->
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="校验目标字段:">
            <el-input v-model="formData.targetField" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="规则分组名称:">
            <el-input v-model="formData.ruleGrpName" placeholder="请输入" type="textarea"
                      :autosize="{ minRows: 3, maxRows: 4 }"

            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="规则明细名称:">

            <el-input
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 4 }"
              v-model="formData.ruleDetlName"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>

    <!-- 规则元组管理区域 -->
    <drg-title-line title="元组信息"/>
    <div class="tuple-container">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="tuple-header">
            <h3>规则元组A新增</h3>
            <el-button type="primary" size="mini" @click="addTupleA">新增
            </el-button>
          </div>

          <el-table :data="formData.tupleA" border style="width: 100%" height="300">
            <el-table-column type="index" label="序号" width="80"></el-table-column>
            <el-table-column label="明细编码【明细名称】">
              <template slot-scope="scope">
                <el-select v-model="scope.row.code" placeholder="请选择" filterable>
                  <el-option
                    v-for="item in tupleList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.dataCodeAndName">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="removeTupleA(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>

        <el-col :span="12">
          <div class="tuple-header">
            <h3>规则元组B新增</h3>
            <el-button type="primary" size="mini" @click="addTupleB">新增
            </el-button>
          </div>

          <!-- 当opraType为SameclockTime或OverclockTime时显示输入次数和单位 -->
          <el-table v-if="formData.opraType === 'SameclockTime' || formData.opraType === 'OverclockTime'" 
                    :data="formData.tupleB" border style="width: 100%" height="300">
            <el-table-column type="index" label="序号" width="80"></el-table-column>
            <el-table-column label="输入次数" width="150">
              <template slot-scope="scope">
                <el-input-number v-model="scope.row.count" :min="1" placeholder="次数"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="单位">
              <template slot-scope="scope">
                <el-input v-model="scope.row.unit" placeholder="请输入单位"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="removeTupleB(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 当opraType为DoubleExistOperator或FindDependencyExist时显示新增规则元组 -->
          <el-table v-else-if="formData.opraType === 'DoubleExistOperator' || formData.opraType === 'FindDependencyExist'" 
                    :data="formData.tupleB" border style="width: 100%" height="300">
            <el-table-column type="index" label="序号" width="80"></el-table-column>
            <el-table-column label="明细编码【明细名称】">
              <template slot-scope="scope">
                <el-select v-model="scope.row.code" placeholder="请选择" filterable>
                  <el-option
                    v-for="item in tupleList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.dataCodeAndName">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="removeTupleB(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 其他opraType时不显示规则元组B -->
          <div v-else style="height: 300px; display: flex; align-items: center; justify-content: center; border: 1px solid #dcdfe6; color: #909399;">
            当前算子类型不支持规则元组B
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 底部保存按钮 -->
    <div class="footer">
      <el-button type="primary" size="large" @click="saveForm">保存</el-button>
    </div>
  </div>
</template>

<script>
import {queryDataGroup as getTupleTypeList} from '@/api/examCorrection/ruleAndTuples'
import {saveNewRule} from '@/api/examCorrection/ruleAndTuples'
import { init } from '@/utils/globalInit'

export default {
  name: 'generalRules',
  data() {
    return {
      val: '',
      tupleList: [],
      formData: {
        ruleScenType: '1',
        ruleTypeCodg: '',
        opraType: '',
        dataGrp: '',
        ruleYear: '',
        targetField: null,
        ruleGrpName: '',
        ruleDetlName: null,

        tupleA: [],
        tupleB: []
      }
    };
  },
  mounted() {
    this.getTupleTypeList()

  },
  
  created() {
    // 强制重新初始化字典
    init()
  },
  methods: {
    filterTupleList(val) {
      if (!val) return;
      this.tupleList = this.tupleList.filter(item => item.code && item.code.includes(val));
    },
    // 添加规则元组A的行
    addTupleA() {
      if (this.formData.tupleA.length === 0 || this.formData.tupleA[this.formData.tupleA.length - 1].code) {
        this.formData.tupleA.push({code: ''});
      } else {
        this.$message.warning('请先填写当前行的明细编码');
      }
    },

    // 删除规则元组A的行
    removeTupleA(index) {
      this.formData.tupleA.splice(index, 1);
    },

    getTupleTypeList() {
      getTupleTypeList().then(response => {
        this.tupleList = response.data
      })
    },
    // 添加规则元组B的行
    addTupleB() {
      if (this.formData.opraType === 'SameclockTime' || this.formData.opraType === 'OverclockTime') {
        // 对于时间相关的算子类型，添加次数和单位字段
        if (this.formData.tupleB.length === 0 || (this.formData.tupleB[this.formData.tupleB.length - 1].count && this.formData.tupleB[this.formData.tupleB.length - 1].unit)) {
          this.formData.tupleB.push({count: null, unit: ''});
        } else {
          this.$message.warning('请先填写当前行的次数和单位');
        }
      } else if (this.formData.opraType === 'DoubleExistOperator' || this.formData.opraType === 'FindDependencyExist') {
        // 对于双重存在和依赖存在算子类型，添加明细编码字段
        if (this.formData.tupleB.length === 0 || this.formData.tupleB[this.formData.tupleB.length - 1].code) {
          this.formData.tupleB.push({code: ''});
        } else {
          this.$message.warning('请先填写当前行的明细编码');
        }
      } else {
        this.$message.warning('当前算子类型不允许添加规则元组B');
      }
    },

    // 删除规则元组B的行
    removeTupleB(index) {
      if (this.formData.opraType === 'SameclockTime' || this.formData.opraType === 'OverclockTime' ||
          this.formData.opraType === 'DoubleExistOperator' || this.formData.opraType === 'FindDependencyExist') {
        this.formData.tupleB.splice(index, 1);
      } else {
        this.$message.warning('当前算子类型不允许删除规则元组B');
      }
    },

    // 检查输入框长度
    checkLength(field, maxLength) {
      if (this.formData[field] && this.formData[field].length > maxLength) {
        this.formData[field] = this.formData[field].substring(0, maxLength);
      }
    },

    // 保存表单
    saveForm() {
      // 合并表单数据
      const postData = {};
      postData.ruleScenType = this.formData.ruleScenType;
      postData.ruleTypeCodg = this.formData.ruleTypeCodg;
      postData.opraType = this.formData.opraType;
      postData.dataGrp = this.formData.dataGrp;
      postData.ruleYear = this.formData.ruleYear;
      postData.targetField = this.formData.targetField;
      postData.ruleGrpName = this.formData.ruleGrpName;
      postData.ruleDetlName = this.formData.ruleDetlName;
      postData.tupleDataA = this.formData.tupleA.map(item => item.code).join(',');
      
      // 根据算子类型处理规则元组B
      if (this.formData.opraType === 'SameclockTime' || this.formData.opraType === 'OverclockTime') {
        // 对于时间相关算子，将次数和单位组合成字符串，单位加上【】
        postData.tupleDataB = this.formData.tupleB.map(item => `${item.count}【${item.unit}】`).join(',');
      } else if (this.formData.opraType === 'DoubleExistOperator' || this.formData.opraType === 'FindDependencyExist') {
        // 对于双重存在和依赖存在算子，使用明细编码
        postData.tupleDataB = this.formData.tupleB.map(item => item.code).join(',');
      } else {
        // 其他算子类型不需要规则元组B
        postData.tupleDataB = '';
      }
      
      this.$confirm('是否确认提交？')
        .then(_ => {
          if (!this.formData.ruleScenType) {
            this.$message({
              message: '规则场景不能为空，请选择。',
              type: 'warning'
            })
            return // 终止请求
          }
          if (!this.formData.ruleTypeCodg) {
            this.$message({
              message: '规则类型不能为空，请选择。',
              type: 'warning'
            })
            return // 终止请求
          }
          if (!this.formData.opraType) {
            this.$message({
              message: '算子类型不能为空，请选择。',
              type: 'warning'
            })
            return // 终止请求
          }
          if (!this.formData.dataGrp) {
            this.$message({
              message: '数据分组不能为空，请选择。',
              type: 'warning'
            })
            return // 终止请求
          }
          if (!this.formData.ruleYear) {
            this.$message({
              message: '规则年度不能为空，请输入。',
              type: 'warning'
            })
            return // 终止请求
          }
          if (!/^\d{4}$/.test(this.formData.ruleYear)) {
            this.$message({
              message: '规则年度格式不正确，请输入4位数字的年份。',
              type: 'warning'
            })
            return // 终止请求
          }
          if (!this.formData.ruleGrpName) {
            this.$message({
              message: '规则分组名称不能为空，请输入。',
              type: 'warning'
            })
            return // 终止请求
          }

          if (!postData.tupleDataA) {
            this.$message({
              message: '规则元组A不能为空，请先选择对应的明细编码。',
              type: 'warning'
            })
            return // 终止请求
          }
          
          // 根据算子类型验证规则元组B
          if (this.formData.opraType === 'SameclockTime' || this.formData.opraType === 'OverclockTime') {
            // 验证时间相关算子的次数和单位
            const hasIncompleteData = this.formData.tupleB.some(item => !item.count || !item.unit);
            if (hasIncompleteData) {
              this.$message({
                message: '规则元组B的次数和单位不能为空，请完善信息。',
                type: 'warning'
              })
              return // 终止请求
            }
          } else if (this.formData.opraType === 'DoubleExistOperator' || this.formData.opraType === 'FindDependencyExist') {
            // 验证双重存在和依赖存在算子的明细编码
            if (!postData.tupleDataB) {
              this.$message({
                message: '当前算子类型需要规则元组B，请先选择对应的明细编码。',
                type: 'warning'
              })
              return // 终止请求
            }
          }
          // 其他算子类型不需要规则元组B
          
          saveNewRule(postData).then((result) => {
            this.$message({
              message: '保存成功！',
              type: 'success'
            })
          }).catch(error => {
            this.$message({
              message: '保存失败，请检查数据后重试',
              type: 'error'
            })
          })
        })
        .catch(_ => {
          this.feeCalculationVisible = false
        })

      // 提交表单数据
      console.log('保存的表单数据:', postData);

    }
  }
};
</script>

<style scoped>
.rule-config-container {
  padding: 20px;
}

.top-form {
  margin-bottom: 20px;
}

.el-form-item {
  margin-bottom: 15px;
}

.tuple-container {
  margin-bottom: 20px;
}

.tuple-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

h3 {
  font-size: 16px;
  font-weight: bold;
  margin: 0;
}

.counter {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-bottom: 5px;
}

.footer {
  text-align: center;
  margin-top: 20px;
}

.el-input, .el-select, .el-textarea {
  width: 100%;
}
</style>
