import Cookies from 'js-cookie'
import store from '../store'
import { queryUserInfo } from '@/api/common/drgCommon'

const TokenKey = 'loginToken'

const userNameKey = 'username'

const meunTreeKey = 'menuTree'

export function getToken () {
  return Cookies.get(TokenKey)
}

export function getUserName () {
  return Cookies.get(userNameKey)
}

export function setToken (token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken () {
  return Cookies.remove(TokenKey)
}

export function setMenuTree (menuLoed) {
  return window.localStorage.setItem(meunTreeKey, menuLoed)
}
export function getMenuTree () {
  return window.localStorage.getItem(meunTreeKey)
}
export function removeMenuTree () {
  return window.localStorage.removeItem(meunTreeKey)
}
