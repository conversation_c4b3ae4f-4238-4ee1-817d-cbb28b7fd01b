import request from '@/utils/request'
export function fetchList (params) {
  return request({
    url: '/inGroupAnalysis/mainInfoList',
    method: 'post',
    params: params
  })
}

export function getTopCountInfo (params) {
  return request({
    url: '/inGroupAnalysis/getTopCountInfo',
    method: 'post',
    params: params
  })
}

export function getNoGroupResonCountInfo (params) {
  return request({
    url: '/inGroupAnalysis/getNoGroupResonCountInfo',
    method: 'post',
    params: params
  })
}
