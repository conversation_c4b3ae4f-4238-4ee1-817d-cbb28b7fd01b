const state = {
  visitedViews: [],
  cachedViews: [],
  tempCachedViews: [],
  tempView: {},
  tempCloseView: {},
  doubleCacheViews: [],
  reduce: false
}

const mutations = {
  ADD_VISITED_VIEW: (state, view) => {
    if (state.visitedViews.some(v => v.path === view.path)) return
    state.reduce = false
    state.visitedViews.push(
      Object.assign({}, view, {
        profttl: view.meta.profttl || 'no-name'
      })
    )
  },
  ADD_CACHED_VIEW: (state, view) => {
    let name = view.path.substring(view.path.lastIndexOf('/') + 1, view.path.length)
    if (!view.meta.keepAlive || state.cachedViews.includes(name) || state.tempCachedViews.includes(name)) return
    if (!view.meta.noCache) {
      state.cachedViews.push(name)
    }
  },
  ADD_TEMP_CACHED_VIEW: (state, view) => {
    let name = view.path.substring(view.path.lastIndexOf('/') + 1, view.path.length)
    if (state.tempCachedViews.includes(name)) return
    state.tempCachedViews.push(name)
  },
  ADD_TEMP_VIEW: (state, view) => {
    state.tempView = view
  },
  ADD_CLOSE_VIEW: (state, view) => {
    state.tempCloseView = view
  },

  DEL_VISITED_VIEW: (state, view) => {
    state.reduce = true
    for (const [i, v] of state.visitedViews.entries()) {
      if (v.path === view.path) {
        state.visitedViews.splice(i, 1)
        break
      }
    }
  },
  DEL_CACHED_VIEW: (state, view) => {
    let name = view.path.substring(view.path.lastIndexOf('/') + 1, view.path.length)
    for (const i of state.cachedViews) {
      if (i === name) {
        const index = state.cachedViews.indexOf(i)
        state.cachedViews.splice(index, 1)
        break
      }
    }
  },
  DEL_TEMP_CACHED_VIEW: (state, view) => {
    let name = view.path.substring(view.path.lastIndexOf('/') + 1, view.path.length)
    for (const i of state.tempCachedViews) {
      if (i === name) {
        const index = state.tempCachedViews.indexOf(i)
        state.tempCachedViews.splice(index, 1)
        break
      }
    }
  },
  DEL_TEMP_VIEW: (state) => {
    state.tempView = {}
  },
  DEL_CLOSE_VIEW: (state) => {
    state.tempCloseView = {}
  },

  DEL_OTHERS_VISITED_VIEWS: (state, view) => {
    state.reduce = true
    state.visitedViews = state.visitedViews.filter(v => {
      return v.meta.affix || v.path === view.path
    })
  },
  DEL_OTHERS_CACHED_VIEWS: (state, view) => {
    for (const i of state.cachedViews) {
      if (i === view.name) {
        const index = state.cachedViews.indexOf(i)
        state.cachedViews = state.cachedViews.slice(index, index + 1)
        break
      }
    }
  },

  DEL_ALL_VISITED_VIEWS: state => {
    // keep affix tags
    const affixTags = state.visitedViews.filter(tag => tag.meta.affix)
    state.visitedViews = affixTags
    state.reduce = true
  },
  DEL_ALL_CACHED_VIEWS: state => {
    state.cachedViews = []
  },
  DEL_DOUBLE_CACHE_VIEWS: state => {
    state.doubleCacheViews = []
  },

  UPDATE_VISITED_VIEW: (state, view) => {
    for (let v of state.visitedViews) {
      if (v.path === view.path) {
        v = Object.assign(v, view)
        break
      }
    }
  },
  SET_DOUBLE_CACHE_VIEWS: (state, view) => {
    state.doubleCacheViews = view
  }
}
const actions = {
  addView ({ dispatch }, view) {
    dispatch('addVisitedView', view)
    dispatch('addCachedView', view)
  },
  addVisitedView ({ commit }, view) {
    commit('ADD_VISITED_VIEW', view)
  },
  addCachedView ({ commit }, view) {
    commit('ADD_CACHED_VIEW', view)
  },
  addTempCachedView ({ commit }, view) {
    commit('ADD_TEMP_CACHED_VIEW', view)
  },
  addTempView ({ commit }, view) {
    commit('ADD_TEMP_VIEW', view)
  },

  delView ({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delVisitedView', view)
      dispatch('delCachedView', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delVisitedView ({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_VISITED_VIEW', view)
      resolve([...state.visitedViews])
    })
  },
  delCachedView ({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_CACHED_VIEW', view)
      resolve([...state.cachedViews])
    })
  },
  delTempCachedView ({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_TEMP_CACHED_VIEW', view)
      resolve([...state.tempCachedViews])
    })
  },
  delTempView ({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_TEMP_VIEW')
      resolve([...state.tempView])
    })
  },
  delOthersViews ({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delOthersVisitedViews', view)
      dispatch('delOthersCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delOthersVisitedViews ({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_VISITED_VIEWS', view)
      resolve([...state.visitedViews])
    })
  },
  delOthersCachedViews ({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_CACHED_VIEWS', view)
      resolve([...state.cachedViews])
    })
  },

  delAllViews ({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delAllVisitedViews', view)
      dispatch('delAllCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delAllVisitedViews ({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_VISITED_VIEWS')
      resolve([...state.visitedViews])
    })
  },
  delAllCachedViews ({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_CACHED_VIEWS')
      resolve([...state.cachedViews])
    })
  },
  updateVisitedView ({ commit }, view) {
    commit('UPDATE_VISITED_VIEW', view)
  }
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}
