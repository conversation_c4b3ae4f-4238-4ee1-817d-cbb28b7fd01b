<template>
  <div class="loginContainer">
    <div id="formContainer" class="dwo">
      <div class="form-left">
        <!-- Login form -->
        <el-form id="login"
                 autoComplete="on"
                 :model="loginForm"
                 :rules="loginRules"
                 ref="loginForm"
                 label-position="left">
          <header>
            <h1 class="login_welcome_tips">欢迎您,请登录！</h1>
          </header>
          <section>

            <label>
              <el-form-item prop="username">
                <el-input name="username"
                          type="text"
                          class="big-input custom_input_init"
                          v-model="loginForm.username"
                          autoComplete="on"
                          @change="changeUserName"
                          placeholder="请输入账号"
                          size="medium"
                          @keyup.enter.native="enterDownHandle"
                >
                  <div slot="prefix" class="custom_prefix_init">
                    <i class="el-input__icon el-icon-user custom_login_icon">
                    </i>
                    <div class="custom_line_init"></div>
                  </div>
                </el-input>
              </el-form-item>
            </label>

            <label>
              <el-form-item prop="password">
                <el-input name="password"
                          ref="passwordRef"
                          class="big-input custom_input_init "
                          :type="hiddenPassword?'password':'text'"
                          @keyup.enter.native="handleLogin"
                          v-model="loginForm.password"
                          autoComplete="on"
                          placeholder="请输入密码"
                          size="medium"
                >
                  <i slot="suffix"
                     :class="[hiddenPassword?'som-icon-CloseEye':'el-icon-view']"
                     style="margin-top: 8px;font-size: 18px;font-family: 'element-icons'!important;"
                     autocomplete="auto"
                     @click="hiddenPassword = !hiddenPassword"/>

                  <div slot="prefix" class="custom_prefix_init">
                    <i class="el-input__icon el-icon-lock custom_login_icon pass_word_init">
                    </i>
                    <div class="custom_line_init"></div>
                  </div>

                </el-input>
              </el-form-item>
            </label>
            <el-button @click="handleLogin" class="btn_init">登 录</el-button>
          </section>
        </el-form>
      </div>
      <div class="title-right">
<!--        <p>DRG付费辅助决策系统</p>-->
        <p>DIP付费辅助决策系统</p>
      </div>
    </div>
  </div>
</template>

<script>
import '@/styles/loginStyles/login.css'
import { isvalidUsername } from '@/utils/validate'
import { getCookie, setCookie, setSupport } from '@/utils/support'
import { queryHospitalInfo, querySysOutOfDate } from '@/api/login'

export default {
  name: 'login',
  data () {
    const validateHospitalId = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择所属医院'))
      } else {
        callback()
      }
    }
    const validateUsername = (rule, value, callback) => {
      if (!isvalidUsername(value)) {
        callback(new Error('请输入正确的用户名'))
      } else {
        callback()
      }
    }
    const validatePass = (rule, value, callback) => {
      if (value.length < 3) {
        callback(new Error('密码不能为空'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: '',
        hospitalId: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePass }],
        hospitalId: [{ required: true, trigger: 'change', validator: validateHospitalId }]
      },
      loading: false,
      pwdType: 'password',
      dialogVisible: false,
      supportDialogVisible: false,
      expireWarnInfoHidden: false,
      expireErrorInfoHidden: false,
      expireWarnInfo: '',
      expireErrorInfo: '',
      hiddenPassword: true,

      showHospital: true,
      hospitalList: []
    }
  },
  created () {
    // 查询系统是否过期
    this.sysOutOfDate()
    this.queryHospitalInfo()
    if (getCookie('username')) {
      if (getCookie('username') === 'developer') {
        this.loginForm.username = getCookie('username')
      } else {
        this.loginForm.username = getCookie('username').substring(12)
      }
    }
    this.loginForm.password = getCookie('password')
    if (this.loginForm.username === undefined || this.loginForm.username == null || this.loginForm.username === '') {
      this.loginForm.username = ''
    }
    if (this.loginForm.password === undefined || this.loginForm.password == null) {
      this.loginForm.password = ''
    }
  },
  methods: {
    showPwd () {
      if (this.pwdType === 'password') {
        this.pwdType = ''
      } else {
        this.pwdType = 'password'
      }
    },
    handleLogin () {
      let isWeek = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,18}$/
      if (this.loginForm.username === 'developer') {
        this.$nextTick(() => {
          this.$refs.loginForm.clearValidate('hospitalId')
        })
      }
      this.$refs.loginForm.validate(valid => {
        if (this.loginForm.username === 'developer') {
          valid = true
        }
        if (valid) {
          this.loading = true
          let params = {}
          Object.assign(params, this.loginForm)
          if (this.loginForm.username !== 'developer' || !this.showHospital) {
            params.username = this.loginForm.hospitalId + this.loginForm.username
          }
          this.$store.dispatch('Login', params).then(() => {
            setCookie('username', params.username, 15)
            // setCookie("username",this.loginForm.username,15);
            // setCookie("password",this.loginForm.password,15);
            // this.$message.success("登录成功，加载缓存数据...")
            // this.$store.dispatch("cacheAll").then(() => {
            //   this.loading = false;
            // })
            this.$router.push({ path: '/' })
          }).catch(() => {
            this.loading = false
          })
        } else {
          console.log('参数验证不合法！')
          return false
        }
      })
    },
    sysOutOfDate () {
      querySysOutOfDate().then(response => {
        let data = response.data
        if (data) {
          if (data.errorInfo) {
            this.expireErrorInfoHidden = true
            this.expireErrorInfo = data.errorInfo
          } else if (data.warningInfo) {
            this.expireWarnInfoHidden = true
            this.expireWarnInfo = data.warningInfo
          }
        }
      }).catch(() => {
        this.loading = false
      })
    },
    changeUserName () {
      if (this.loginForm.username == 'developer') {
        this.expireErrorInfoHidden = false
        this.expireWarnInfoHidden = false
      } else {
        this.sysOutOfDate()
      }
    },
    enterDownHandle () {
      let input = this.$refs.passwordRef
      input.focus()
    },
    handleTry () {
      this.dialogVisible = true
    },
    dialogConfirm () {
      this.dialogVisible = false
      setSupport(true)
    },
    dialogCancel () {
      this.dialogVisible = false
      setSupport(false)
    },
    // openUserInfo() {
    //   this.showEditPassWord = true
    // },
    // closeUserInfo(){
    //   this.showEditPassWord = false
    // }

    queryHospitalInfo () {
      queryHospitalInfo().then(res => {
        if (res.data) {
          if (res.data.length > 1) {
            this.showHospital = true
            this.hospitalList = res.data
          } else {
            this.showHospital = false
          }
        }
      })
    }
  }
}
</script>

<style scoped src="../../styles/loginStyles/style.css"/>
<style scoped src="../../styles/loginStyles/normalize.min.css"/>
