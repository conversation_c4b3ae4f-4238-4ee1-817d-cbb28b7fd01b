<template>
  <el-table ref="elTable"
            :id="id"
            height="100%"
            stripe
            :header-cell-style="{'text-align' : 'center'}"
            :data="data"
            :summary-method="getSummaries"
            show-summary
            v-loading="loading"
            border>
    <el-table-column label="序号" type="index" width="50" align="center" fixed></el-table-column>
    <el-table-column label="住院医师姓名" prop="drName" align="center" width="100" fixed></el-table-column>
    <el-table-column label="病案数" prop="medicalTotalNum" align="center" width="110" :fixed="include('medicalTotalNum')"
                     sortable>
      <template slot-scope="scope">
        <div :class="scope.row.medicalTotalNum == 0 ? '' : 'skip'"
             @click="scope.row.medicalTotalNum == 0 ? '' : queryTotalPatient(scope.row)">
          {{ scope.row.medicalTotalNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="组数" prop="groupNum" width="110" align="center" sortable :fixed="include('groupNum')">
      <template slot-scope="scope">
        <div :class="scope.row.groupNum == 0 ? '' : 'skip'"
             @click="scope.row.groupNum == 0 ? '' : queryGroup(scope.row)">
          {{ scope.row.groupNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="入组病案数" prop="drgInGroupMedcasVal" width="110" align="center" sortable
                     :fixed="include('inGroupNum')">
      <template slot-scope="scope">
        <div :class="scope.row.drgInGroupMedcasVal == 0 ? '' : 'skip'"
             @click="scope.row.drgInGroupMedcasVal == 0 ? '' : queryInGroupPatient(scope.row)">
          {{ scope.row.drgInGroupMedcasVal }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="未入组病案数" prop="notGroupNum" width="130" align="center" sortable
                     :fixed="include('notGroupNum')">
      <template slot-scope="scope">
        <div :class="scope.row.notGroupNum == 0 ? '' : 'skip'"
             @click="scope.row.notGroupNum == 0 ? '' : queryNotGroupPatient(scope.row)">
          {{ scope.row.notGroupNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="入组率" prop="inGroupRate" width="195" :fixed="include('inGroupRate')">
      <template slot-scope="scope">
        <el-progress :text-inside="true" :stroke-width="16" v-if="!isNaN(parseInt(scope.row.inGroupRate))"
                     :percentage="Number(scope.row.inGroupRate)"
                     :color="$somms.getPercentageColor(scope.row.inGroupRate)"/>
      </template>
    </el-table-column>
    <el-table-column label="CMI指数" align="center" width="120" prop="cmi" sortable='custom'>
      <template slot-scope="scope">{{ scope.row.cmi | formatIsEmpty }}</template>
    </el-table-column>
    <el-table-column label="总权重" prop="totalWeight" :fixed="include('totalWeight')" width="130" align="center"
                     sortable/>
    <el-table-column label="平均权重" prop="avgWeight" :fixed="include('avgWeight')" width="130" align="center"
                     sortable/>
    <el-table-column label="时间消耗指数" prop="timeIndex" width="130" align="center" sortable
                     :fixed="include('timeIndex')"></el-table-column>
    <el-table-column label="费用消耗指数" prop="costIndex" width="130" align="center" sortable
                     :fixed="include('costIndex')"></el-table-column>
    <el-table-column label="总费用" prop="sumfee" width="130" :fixed="include('sumfee')"
                     align="right" sortable></el-table-column>
    <el-table-column label="预测金额" prop="forecastAmount" width="130" :fixed="include('forecastAmount')"
                     align="right" sortable></el-table-column>
    <el-table-column label="预测差异" prop="forecastAmountDiff" width="130" :fixed="include('forecastAmountDiff')"
                     align="right" sortable></el-table-column>
    <el-table-column label="企业补充医疗保险基金" prop="amount370100" width="180" :fixed="include('amount370100')"
                     align="right" sortable></el-table-column>
    <el-table-column label="个人自付" prop="psnSelfpay" width="130" :fixed="include('psnSelfpay')" align="right"
                     sortable></el-table-column>
    <el-table-column label="个人自费" prop="psnOwnpay" width="130" :fixed="include('psnOwnpay')" align="right"
                     sortable></el-table-column>
    <el-table-column label="个人账户" prop="acctPay" width="130" :fixed="include('acctPay')" align="right"
                     sortable></el-table-column>
    <el-table-column label="个人现金" prop="psnCashpay" width="130" :fixed="include('psnCashpay')" align="right"
                     sortable></el-table-column>
    <el-table-column label="药品费" prop="drugFee" width="130" :fixed="include('drugFee')" align="right"
                     sortable></el-table-column>
    <el-table-column label="药占比" prop="drugRatio" width="130" align="center" sortable :fixed="include('drugRatio')">
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.drugRatio) }}
      </template>
    </el-table-column>
    <el-table-column label="耗材费" prop="consumeFee" width="130"  :fixed="include('consumeFee')" align="right"
                     sortable></el-table-column>
    <el-table-column label="耗占比" prop="consumeRatio" width="130" align="center" sortable :fixed="include('consumeRatio')">
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.consumeRatio) }}
      </template>
    </el-table-column>
<!--    <el-table-column label="单价" prop="price" width="130"  :fixed="include('price')" align="right"-->
<!--    sortable></el-table-column>-->
    <el-table-column label="总分值" prop="totlSco" width="130"  :fixed="include('totlSco')" align="right"
    sortable></el-table-column>
    <el-table-column label="悬浮" align="center">
      <template slot-scope="scope">
        <el-button size="mini" type="primary" @click="showSuspension(scope.row)">悬浮</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'newDrgDoctorKpiTable',
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    },
    // 表格id
    id: {
      type: String
    },
    queryForm: {
      type: Object
    },
    columnOptions: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    scopeData: {}
  }),
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  updated () {
    this.$nextTick(() => {
      this.$refs.elTable.doLayout()
    })
  },
  methods: {
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length,
        customAverage: (values) => {
          const sum = values.reduce((prev, curr) => prev + curr, 0)
          return sum / values.length
        }
      }
      let sum20 = 0, sum22 = 0, sum12 = 0
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计'
          return
        }
        const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value))
        if (index === 2 || index === 3 || index === 4 || index === 5) {
          sums[index] = calculations.sum(values)
        } else if (index === 8 || index === 9 || index === 12 || index === 13 || index === 14 || index === 15 || index === 16 || index === 17 || index === 18 || index === 19 || index === 20 || index === 22|| index === 25) {
          sums[index] = calculations.sum(values).toFixed(2)
          if (index === 20) sum20 = sums[index]
          else if (index === 22) sum22 = sums[index]
          else if (index === 12) sum12 = sums[index]
        } else if (index === 7 || index === 10 || index === 11 || index === 24) {
          sums[index] = calculations.average(values).toFixed(2)
        } else if (index === 6) {
          sums[index] = calculations.average(values).toFixed(2) + '%'
        } else {
          sums[index] = ' '
        }
      })
      sums[21] = calculatePercentage(sum20, sum12)
      sums[23] = calculatePercentage(sum22, sum12)
      return sums

      function calculatePercentage (numerator, denominator) {
        if (denominator !== 0) {
          return ((numerator / denominator) * 100).toFixed(2) + '%'
        }
        return '0.00%'
      }
    },
    include (column) {
      return this.fixedColumns.includes(column)
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    // 显示悬浮
    showSuspension (scope) {
      this.scopeData = scope
      let scopeList = []
      for (let i in this.scopeData) {
        let o = {}
        o.key = i
        o.value = this.scopeData[i]
        scopeList.push(o)
      }
      let res = []
      for (let i = 0; i < scopeList.length; i++) {
        for (let j = 0; j < this.columnOptions.length; j++) {
          if (scopeList[i].key == this.columnOptions[j].value) {
            res.push({
              key: scopeList[i].key,
              label: this.columnOptions[j].label,
              value: scopeList[i].value,
              type: 1,
              show: true
            })
          }
        }
      }
      if (scope) {
        this.$emit('showSuspension', res)
      }
    },
    queryTotalPatient (item) {
      this.$router.push({
        path: '/hosDrgAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: item.drCodg,
          deptCode: this.queryForm.deptCode,
          drgCodg: this.queryForm.drgCodg,
          feeStas: this.queryForm.feeStas,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryInGroupPatient (item) {
      this.$router.push({
        path: '/hosDrgAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: item.drCodg,
          deptCode: this.queryForm.deptCode,
          drgCodg: this.queryForm.drgCodg,
          isInGroup: 1,
          feeStas: this.queryForm.feeStas,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryNotGroupPatient (item) {
      this.$router.push({
        path: '/hosDrgAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: item.drCodg,
          deptCode: this.queryForm.deptCode,
          drgCodg: this.queryForm.drgCodg,
          isInGroup: 0,
          feeStas: this.queryForm.feeStas,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryGroup (item) {
      this.$router.push({
        path: '/hosDrgAnalysisNew/gropAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: item.drCodg,
          deptCode: this.queryForm.deptCode,
          feeStas: this.queryForm.feeStas,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    }
  }
}
</script>
