import request from '@/utils/request'

/**
 * 查询数据
 * @param params
 * @returns {AxiosPromise}
 */
export function queryData (params) {
  return request({
    url: '/hosPerfAppraisalDsItem/queryList',
    method: 'post',
    data: params
  })
}

/**
 * 新增数据
 * @param params
 * @returns {AxiosPromise}
 */
export function addDsItem (params) {
  return request({
    url: '/hosPerfAppraisalDsItem/add',
    method: 'post',
    data: params
  })
}

/**
 * 修改数据
 * @param params
 * @returns {AxiosPromise}
 */
export function updateDsItem (params) {
  return request({
    url: '/hosPerfAppraisalDsItem/update',
    method: 'post',
    data: params
  })
}

/**
 * 删除数据
 * @param params
 * @returns {AxiosPromise}
 */
export function removeDsItem (params) {
  return request({
    url: '/hosPerfAppraisalDsItem/remove',
    method: 'post',
    data: params
  })
}
