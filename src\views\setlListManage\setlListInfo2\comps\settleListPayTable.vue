<template>
  <div class="sl-pay-container">
    <div class="sl-pay-fund">
      <div class="sl-pay-vertical">基金支付</div>
      <el-table border
                :data="fundPayData"
                :header-cell-style="{ textAlign: 'center' }"
                :cell-style="{ textAlign: 'center' }"
                style="width: 100%">
        <el-table-column label="基金支付类型" prop="fundPayType"/>
        <el-table-column label="金额" prop="fundPayamt"/>
      </el-table>
    </div>
    <div class="sl-pay-self">
      <div class="sl-pay-vertical" style="border-left: none">个人支付</div>
      <div class="sl-pay-self-item-wrapper">
        <div class="sl-pay-self-item" v-for="(item, index) in selfPayData" :key="index"
             :style="{ borderBottom: index !== selfPayData.length - 1 ? 'none' : '' }">
          <div class="sl-pay-self-item-label"> {{ item.label }} </div>
          <div class="sl-pay-self-item-value"> {{ item.value }} </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    // 基金支付数据
    fundPayData: [Array],
    // 个人自付数据
    selfPayData: [Array]
  }
}
</script>
<style scoped lang="scss">
@mixin center {
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
}

@mixin border-no-top{
  border-left: $border!important;
  border-right: $border!important;
  border-bottom: $border!important;
}
$color: #8c939d;
$border: 1px solid $color;
.sl-pay{
  &-container{
    width: 100%;
    height: 20rem;
    display: flex;
  }

  &-fund{
    width: 50%;
    height: 100%;
    display: flex;
  }

  &-self{
    width: 50%;
    height: 100%;
    display: flex;

    &-item{
      height: 25%;
      width: 100%;
      display: flex;
      @include border-no-top;

      &-label{
        width: 30%;
        height: 100%;
        border-right: $border;
        @include center;
      }

      &-value{
        width: 70%;
        height: 100%;
        @include center;
      }
    }

    &-item-wrapper{
      height: 100%;
      width: calc(100% - 36px);
    }
  }

  &-vertical{
    width: 34px;
    line-height: 34px;
    font-size: 20px;
    height: 100%;
    border-bottom: $border;
    border-left: $border;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
  }
}

/deep/.el-table--border, .el-table--group {
  @include border-no-top;
}
/**
改变表格内竖线颜色
 */
/deep/.el-table--border  td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
  border-right: $border!important;
}
/**
改变表格内行线颜色
 */
/deep/.el-table  td, .el-table th.is-leaf  {
  border-bottom: $border!important;
}

/deep/.el-table thead tr th {
  border-color: $color;
}
</style>
