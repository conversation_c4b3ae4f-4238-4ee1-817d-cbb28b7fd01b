<template>
  <el-container style="height:100%;width:100%">
    <el-aside width="170px" style="height:100%;border: 1px solid #DCDFE6">
      <el-menu :default-active="activeMenu"  @select="handleSelectMean">
        <el-menu-item index="1" :style="`background-color: ${showBaseInfoColor}`">
          <svg-icon icon-class="medicalQuality-patientInfo"></svg-icon>
          患者基本信息
        </el-menu-item>
        <el-menu-item  index="2">
          <svg-icon icon-class="medicalQuality-outpatientMtb"></svg-icon>
          门诊慢特病信息
        </el-menu-item>
        <el-menu-item index="3">
          <svg-icon icon-class="medicalQuality-treatment"></svg-icon>
          住院诊疗信息
        </el-menu-item>
        <el-menu-item index="4">
          <svg-icon icon-class="medicalQuality-medicalCost"></svg-icon>
          医疗费用信息
        </el-menu-item>
      </el-menu>

      <el-container>
        <el-header style="text-align:center; font-size: 14px;height: 35px;line-height: 35px;background-color: #B3C0D1">
          <span>病案质量结果</span>
        </el-header>
        <el-main style="font-size: 13px;" align="center">
          <div>
            <span>完整性校验错误数</span>
          </div>
          <el-popover
            placement="right"
            title="完整性校验错误详情"
            width="500"
            trigger="hover"
            :content="completeErrorMsg">
            <el-button slot="reference" size="mini" class="validateCountError" @click="showCompleteErrorDic()">{{completeError}}</el-button>
          </el-popover>
          <div style="margin-top:5px;">
            <span>逻辑性校验错误数</span>
          </div>
          <el-popover
            placement="right"
            title="逻辑性校验错误详情"
            width="500"
            trigger="hover"
            :content="logicErrorMsg">
            <el-button slot="reference" size="mini" class="validateCountError" @click="showLogicErrorDic()">{{logicError}}</el-button>
          </el-popover>
          <div style="margin-top:5px;">
            <span>病案质量得分</span>
          </div>
          <el-popover
            placement="right"
            title="病案质量扣分详情"
            width="500"
            trigger="hover"
            :content="scoreMsg">
            <el-button slot="reference" size="mini" class="validateCountScore" @click="showScoreErrorDic()"  >{{refer_sco}}</el-button>
          </el-popover>
          <span v-if="Number(refer_sco)<100" @click="showScoreErrorDetail()" style="font-size:10px;color:blue;text-decoration:underline;cursor: pointer;">扣分详情</span>
        </el-main>
      </el-container>

      <el-container>
        <el-header style="text-align:center; font-size: 11px;height: 35px;line-height: 35px;background-color: #B3C0D1">
          <el-radio-group v-model="selectGroup" size="mini"  >
            <el-radio-button :label="1">DIP组</el-radio-button>
            <el-radio-button :label="2">DRG组</el-radio-button>
          </el-radio-group>
        </el-header>
        <el-main align="center" v-if="selectGroup=='1'">
          <div cssClass="count-panel" style="margin-top: -10px;">
            <div class="count-item" :style="`background:url(${countBkgrd}) bottom no-repeat`">
              <div class="title">平均住院费用：</div>
              <div class="value">
                <span>{{dipAvgInHosCost}}</span>
              </div>
            </div>
          </div>
          <div cssClass="count-panel" style="margin-top: 10px;">
            <div class="count-item" :style="`background:url(${countBkgrd}) bottom no-repeat`">
              <div class="title">平均住院时间：</div>
              <div class="value">
                <span>{{dipAvgDays}}</span>
                <span class="others">天</span>
              </div>
            </div>
          </div>
          <div cssClass="count-panel" style="margin-top: 10px;">
            <div class="count-item" :style="`background:url(${countBkgrd}) bottom no-repeat`">
              <div class="title">分组结果：</div>
              <div class="drgValue" :title="dipCodg+dipName">
                <div style="font-size: 12px;" v-if="dipCode&&dipCode.length>18">{{dipCodg.substring(0,16)+".."}}</div>
                <div style="font-size: 12px;" v-if="dipCode&&dipCode.length<=18">{{dipCodg}}</div>
                <div style="word-break: break-all;word-wrap: break-word;" v-if="dipName&&dipName.length<=20">{{dipName}}</div>
                <div style="word-break: break-all;word-wrap: break-word;" v-if="dipName&&dipName.length>20">{{dipName.substring(0,20)+".."}}</div>
              </div>
            </div>
          </div>
        </el-main>

        <el-main align="center" v-if="selectGroup=='2'">
          <div cssClass="count-panel" style="margin-top: -10px;">
            <div class="count-item" :style="`background:url(${countBkgrd}) bottom no-repeat`">
              <div class="title">平均住院费用：</div>
              <div class="value">
                <span>{{avgInHosCost}}</span>
              </div>
            </div>
          </div>
          <div cssClass="count-panel" style="margin-top: 10px;">
            <div class="count-item" :style="`background:url(${countBkgrd}) bottom no-repeat`">
              <div class="title">平均住院时间：</div>
              <div class="value">
                <span>{{avgDays}}</span>
                <span class="others">天</span>
              </div>
            </div>
          </div>
          <div cssClass="count-panel" style="margin-top: 10px;">
            <div class="count-item" :style="`background:url(${countBkgrd}) bottom no-repeat`">
              <div class="title">分组结果：</div>
              <div class="drgValue" :title="drgCodg+drgName">
                <div style="font-size: 14px;">{{drgCodg}}</div>
                <div style="word-break: break-all;word-wrap: break-word;" v-if="drgName&&drgName.length<=20">{{drgName}}</div>
                <div style="word-break: break-all;word-wrap: break-word;" v-if="drgName&&drgName.length>20">{{drgName.substring(0,18)+".."}}</div>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
    </el-aside>

    <el-container>
      <!--DRG+DIP诊断预警悬浮框-->
      <div class="diagnose-float-area" v-drag draggable="false" v-if="diagnoseToolShow">
        <el-collapse v-model="active">
          <el-collapse-item title="DIP入组推荐" name="diagnose">
            <el-card class="suggestGroup">
              <div class="suggestGroup-title">
                <i class="el-icon-menu"></i>推荐入组
              </div>
              <div v-if="dipPreGroupTop1 && dipPreGroupTop1.normal==1">
                <div class="suggestGroup-item" :title="dipPreGroupTop1.dipCodg" >{{dipPreGroupTop1.dipCodg}}</div>
                <div class="suggestGroup-item" :title="dipPreGroupTop1.dipName" >{{dipPreGroupTop1.dipName}}</div>
                <div class="suggestGroup-item" :title="dipPreGroupTop1.dipStandardInHosCostLevel">例均费用:{{dipPreGroupTop1.dipStandardInHosCostLevel}}</div>
                <div class="suggestGroup-item" :title="dipPreGroupTop1.inhosCost">住院总费用:{{dipPreGroupTop1.inhosCost}}</div>
              </div>
              <div class="currentGroup-item" v-else>暂无推荐</div>
<!--              <div class="suggestGroup-costdays" v-if="suggestDipCode">{{suggestDipAvgInHosCost}}/{{suggestDipAvgDays}}天</div>-->
            </el-card>

            <el-card class="suggestGroup">
              <div class="suggestGroup-title">
                <i class="el-icon-menu"></i>可入DIP组(红色标注为当前DIP组)
              </div>
              <div v-if="dipPreGroup.length>0" style="height: 400px;overflow: auto">
                <div v-for="(item,index) in dipPreGroup" style="padding-top: 10px" :key="index">
                  <el-card shadow="hover" :style="{'color': dipCodg == item.dipCodg ? 'red' : ''}">
                    <div class="suggestGroup-item" :title="item.diagnoseCode"  >诊断编码:{{item.diagnoseCode}}</div>
                    <div class="suggestGroup-item" :title="item.dipCodg"  >{{item.dipCodg}}</div>
                    <div class="suggestGroup-item" :title="item.dipName"  >{{item.dipName}}</div>
                    <div class="suggestGroup-item" :title="item.dipStandardInHosCostLevel">例均费用:{{item.dipStandardInHosCostLevel}}</div>
    <!--                <div class="suggestGroup-item" :title="suggestDrgCode+'—'+suggestDrgName"  v-if="suggestDrgCode&&suggestDrgName&&suggestDrgName.length>15">{{suggestDrgCode}}—{{suggestDrgName.substring(0,14)+".."}}</div>-->
                  </el-card>
                </div>
              </div>
              <div class="currentGroup-item" v-else>暂无数据</div>
<!--              <div class="suggestGroup-costdays" v-if="suggestDrgCode">{{suggestAvgInHosCost}}/{{suggestAvgDays}}天</div>-->
            </el-card>
<!--            <el-card class="suggestGroup">-->
<!--              <div class="suggestGroup-title">-->
<!--                <i class="el-icon-menu"></i>建议Drg入组-->
<!--              </div>-->
<!--              <div class="suggestGroup-item" :title="suggestDrgCode"  v-if="suggestDrgCode&&suggestDrgName&&suggestDrgName.length<=15">{{suggestDrgCode}}</div>-->
<!--              <div class="suggestGroup-item" :title="suggestDrgName"  v-if="suggestDrgCode&&suggestDrgName&&suggestDrgName.length<=15">{{suggestDrgName}}</div>-->
<!--              <div class="suggestGroup-item" :title="suggestDrgCode+'—'+suggestDrgName"  v-if="suggestDrgCode&&suggestDrgName&&suggestDrgName.length>15">{{suggestDrgCode}}—{{suggestDrgName.substring(0,14)+".."}}</div>-->
<!--              <div class="currentGroup-item" v-if="!suggestDrgCode">暂无数据</div>-->
<!--              <div class="suggestGroup-costdays" v-if="suggestDrgCode">{{suggestAvgInHosCost}}/{{suggestAvgDays}}天</div>-->
<!--            </el-card>-->
<!--            <el-card class="diagnoseResourceRank">-->
<!--              <div class="diagnoseResourceRank-title">-->
<!--                <i class="el-icon-menu"></i>疾病诊断资源消耗排名-->
<!--              </div>-->
<!--              <div v-if="!diagnoseCostRankList" class="diagnoseResourceRank-otherItem">暂无数据</div>-->
<!--              <div v-if="diagnoseCostRankList" v-for="(item,index) in diagnoseCostRankList">-->
<!--                <div class="diagnoseResourceRank-firstItem" v-if="!item" >第{{index+1}}名：未填诊断</div>-->
<!--                <div class="diagnoseResourceRank-firstItem" v-if="item.sno=='0'" >第{{index+1}}名：主要诊断</div>-->
<!--                <div class="diagnoseResourceRank-otherItem" v-if="item.seq!='0'&&item.cost!='0'">第{{index+1}}名：其他诊断{{item.seq}}</div>-->
<!--                <div class="diagnoseResourceRank-otherItem" v-if="item.seq!='0'&&item.cost=='0'">第{{index+1}}名：其他诊断{{item.seq}} (无此诊断数据)</div>-->
<!--              </div>-->
<!--            </el-card>-->
<!--            <el-card class="operateResourceRank">-->
<!--              <div class="operateResourceRank-title">-->
<!--                <i class="el-icon-menu"></i>手术操作资源消耗排名-->
<!--              </div>-->
<!--              <div v-if="!operateCostRankList" class="operateResourceRank-otherItem">暂无数据</div>-->
<!--              <div v-if="operateCostRankList" v-for="(item,index) in operateCostRankList">-->
<!--                <div class="operateResourceRank-firstItem" v-if="!item" >第{{index+1}}名：未填手术</div>-->
<!--                <div class="operateResourceRank-firstItem" v-if="item.sno=='1'" >第{{index+1}}名：主要手术1</div>-->
<!--                <div class="operateResourceRank-otherItem" v-if="item.seq!='1'&&item.cost!='0'">第{{index+1}}名：其他手术{{item.seq}} </div>-->
<!--                <div class="operateResourceRank-otherItem" v-if="item.seq!='1'&&item.cost=='0'">第{{index+1}}名：其他手术{{item.seq}} (无此操作数据)</div>-->
<!--              </div>-->
<!--            </el-card>-->
          </el-collapse-item>
        </el-collapse>
      </div>

      <el-header style="height: 35px;line-height: 35px">
        <span style="float: left;font-size: 17px;font-weight: bold">医疗保障基金结算清单</span>
        <el-checkbox v-model="xy" disabled style="margin-left:20px;">西医病案</el-checkbox>
        <el-checkbox v-model="zy" disabled style="margin-left:-20px;">中医病案</el-checkbox>

        <el-radio-group v-model="selectDic" style="margin-top:5px;margin-left:45px;" size="mini"  @change="changeSelectDic">
          <el-radio-button :label="1" >结算清单</el-radio-button>
          <el-radio-button :label="2">病案首页</el-radio-button>
          <el-radio-button :label="9">全部字段</el-radio-button>
        </el-radio-group>

        <!--<el-button  size="mini" icon="el-icon-download" auth="settleList:detail:export" style="float: right;margin-top:5px;color:white;background-color: #67C23A" @click="exportWord()">导出病案</el-button>-->
        <!--<el-button  size="mini" icon="el-icon-refresh" style="float: right;margin-top:5px;color:white;background-color: #67C23A" @click="reset()">重置页面</el-button>-->
        <!--<el-button  size="mini" icon="el-icon-edit" auth="settleList:detail:update" style="float: right;margin-top:5px;color:white;background-color: #67C23A" @click="save()">保存上传</el-button>-->
        <el-button  size="mini" icon="el-icon-refresh" style="float: right;margin-top:5px;margin-right:15px;color:white;background-color: #67C23A" @click="getGroup()">预测分组</el-button>
<!--        <el-button  size="mini" icon="el-icon-share" auth="settleList:detail:validate" style="float: right;margin-top:5px;margin-right:9px;color:white;background-color: #67C23A" @click="validate()">病案校验</el-button>-->
      </el-header>

      <el-main>
        <!--基本信息-->
        <settle-list-base-info
          v-show="showBaseInfo"
          v-model="settleListParam">
        </settle-list-base-info>
        <!--门诊曼特病信息-->
        <settle-list-mzmtb-info
          v-show="showMzmtbInfo"
          v-model="settleListParam">
        </settle-list-mzmtb-info>
        <!--住院诊疗信息-->
        <settle-list-zyzl-info
          v-show="showZyzlInfo"
          v-model="settleListParam">
        </settle-list-zyzl-info>
        <!--医疗费用信息-->
        <settle-list-ylfy-info
          v-show="showYlfyInfo"
          v-model="settleListParam">
        </settle-list-ylfy-info>
      </el-main>
    </el-container>
  </el-container>

</template>
<script>

import SettleListBaseInfo from './components/SettleListBaseInfo'
import SettleListMzmtbInfo from './components/SettleListMzmtbInfo'
import SettleListZyzlInfo from './components/SettleListZyzlInfo'
import SettleListYlfyInfo from './components/SettleListYlfyInfo'
import { getSettleListAllInfo, updateSettleListAllInfo, validateMedical, getStandCostAndDayAndGroupInfo } from '@/api/medicalQuality/settleListDetail'

const defaultSettleListParam = {
  somHiInvyBasInfo: {},
  busOutpatientClinicDiagnosisList: [],
  busDiseaseDiagnosisTrimList: [],
  busOperateDiagnosisList: []
}
const compareListParam = {
}
export default {
  name: 'setlListDetail',
  components: { SettleListBaseInfo, SettleListMzmtbInfo, SettleListZyzlInfo, SettleListYlfyInfo },
  data () {
    return {
      xy: true, // 默认西医病案
      zy: false,
      selectDic: 1, // 默认展示结算清单涉及字段
      selectGroup: 1, // 默认展示DIP组别信息
      dictVoList: {},
      settleListParam: Object.assign({}, defaultSettleListParam),
      showBaseInfo: true,
      activeMenu: '1',
      showBaseInfoColor: '#ecf5ff',
      showMzmtbInfo: false,
      showZyzlInfo: false,
      showYlfyInfo: false,
      completeError: 0,
      completeErrorMsg: '无完整性校验错误！',
      logicError: 0,
      logicErrorMsg: '无逻辑性校验错误！',
      refer_sco: 100.0,
      scoreMsg: '病案质量完整，无相应扣分！',
      completeErrors: {},
      logicErrors: {},
      scoreErrors: {},
      countBkgrd: require('@/assets/images/medicalQuality/countBkgrd.png'),
      avgInHosCost: 0,
      avgDays: 0,
      drgCodg: null,
      drgName: null,
      dipAvgInHosCost: 0,
      dipAvgDays: 0,
      dipCodg: null,
      dipName: null,

      diagnoseToolShow: false,
      active: ['drg', 'diagnose'],
      suggestAvgInHosCost: 0,
      suggestAvgDays: 0,
      suggestDrgCode: null,
      suggestDrgName: null,
      suggestDipAvgInHosCost: 0,
      suggestDipAvgDays: 0,
      suggestDipCode: null,
      suggestDipName: null,
      diagnoseCostRankList: null,
      operateCostRankList: null,
      dipPreGroupTop1: [], // 预入组top1
      dipPreGroup: [] // 预入组所有数据
    }
  },
  created () {
    let params = new URLSearchParams()
    // params.append('id', this.$route.query.id);
    params.append('id', this.$route.query.id)
    params.append('k00', this.$route.query.k00)
    this.getInfo(params)
  },
  methods: {
    // 左侧菜单选择事件
    handleSelectMean (key) {
      if (key === '1') {
        this.showBaseInfo = true
        this.showMzmtbInfo = false
        this.showZyzlInfo = false
        this.showYlfyInfo = false
        this.showBaseInfoColor = '#ecf5ff'
        this.diagnoseToolShow = false
      } else if (key === '2') {
        this.showBaseInfo = false
        this.showMzmtbInfo = true
        this.showZyzlInfo = false
        this.showYlfyInfo = false
        this.showBaseInfoColor = '#ffffff'
        this.diagnoseToolShow = false
      } else if (key === '3') {
        this.showBaseInfo = false
        this.showMzmtbInfo = false
        this.showZyzlInfo = true
        this.showYlfyInfo = false
        this.showBaseInfoColor = '#ffffff'
        this.diagnoseToolShow = true
      } else if (key === '4') {
        this.showBaseInfo = false
        this.showMzmtbInfo = false
        this.showZyzlInfo = false
        this.showYlfyInfo = true
        this.showBaseInfoColor = '#ffffff'
        this.diagnoseToolShow = false
      } else {
        this.showBaseInfo = true
        this.showMzmtbInfo = false
        this.showZyzlInfo = false
        this.showYlfyInfo = false
        this.showBaseInfoColor = '#ecf5ff'
        this.diagnoseToolShow = false
      }
    },
    getInfo (params) {
      if (this.$route.query.activeMenu) {
        this.activeMenu = this.$route.query.activeMenu
        this.showBaseInfo = false
        this.showMzmtbInfo = false
        this.showZyzlInfo = true
        this.showYlfyInfo = false
        this.showBaseInfoColor = '#ffffff'
        this.diagnoseToolShow = true
      }
      getSettleListAllInfo(params).then(response => {
        this.settleListParam = response.data
        const busSettleBaseInfoList = response.data.somHiInvyBasInfo
        if (busSettleBaseInfoList.a03 == '2') {
          this.xy = false
          this.zy = true
          Object.assign(this.settleListParam, { showZy: true })
        } else {
          Object.assign(this.settleListParam, { showZy: false })// 中医病案特有的字段，默认展示西医病案的字段
        }
        Object.assign(this.settleListParam, { showBa: false })// 病案首页特有的字段是否展示，默认不展示
        Object.assign(this.settleListParam, { showJsqd: true })// 结算清单特有的字段，默认展示
        Object.assign(this.settleListParam, { showJsqdAndBa: true })// 结算清单特有的字段，默认展示
        // 重症监护信息转为list回显
        // if(busSettleBaseInfoList){
        //   const zzjhxxListValues = [];
        //   const zzjhxx1 = {b40:busSettleBaseInfoList.b40x01,b41:busSettleBaseInfoList.b41x01,b42:busSettleBaseInfoList.b42x01,b43:busSettleBaseInfoList.b43x01};
        //   const zzjhxx2 = {b40:busSettleBaseInfoList.b40x02,b41:busSettleBaseInfoList.b41x02,b42:busSettleBaseInfoList.b42x02,b43:busSettleBaseInfoList.b43x02};
        //   const zzjhxx3 = {b40:busSettleBaseInfoList.b40x03,b41:busSettleBaseInfoList.b41x03,b42:busSettleBaseInfoList.b42x03,b43:busSettleBaseInfoList.b43x03};
        //   zzjhxxListValues.push(zzjhxx1);
        //   zzjhxxListValues.push(zzjhxx2);
        //   zzjhxxListValues.push(zzjhxx3);
        //   const zzjhxxListMap = {zzjhxxList:zzjhxxListValues};
        //   Object.assign(this.settleListParam,zzjhxxListMap);
        // }
        // 获取门诊慢特病科室和就诊日期信息
        const busOutpatientClinicDiagnosisList = response.data.busOutpatientClinicDiagnosisList
        if (busOutpatientClinicDiagnosisList.length > 0) {
          const deptCode = { deptCode: busOutpatientClinicDiagnosisList[0].deptCode }
          Object.assign(this.settleListParam, deptCode)
          const mdtrtDate = { mdtrtDate: busOutpatientClinicDiagnosisList[0].mdtrtDate }
          Object.assign(this.settleListParam, mdtrtDate)
        }
        // 结算期间
        const settleDateRange = []
        settleDateRange.push(busSettleBaseInfoList.d36)
        settleDateRange.push(busSettleBaseInfoList.d37)
        const settleDateRangeMap = { settleDateRange: settleDateRange }
        Object.assign(this.settleListParam, settleDateRangeMap)
        // 基本费用合计
        const sumBaseCost = busSettleBaseInfoList.d38 + busSettleBaseInfoList.d39 + busSettleBaseInfoList.d40 + busSettleBaseInfoList.d41 +
            busSettleBaseInfoList.d42 + busSettleBaseInfoList.d20x02 + busSettleBaseInfoList.d13 + busSettleBaseInfoList.d43 +
            busSettleBaseInfoList.d23 + busSettleBaseInfoList.d44 + busSettleBaseInfoList.d24 + busSettleBaseInfoList.d45 +
            busSettleBaseInfoList.d46 + busSettleBaseInfoList.d34
        const hjMap = { sumBaseCost: sumBaseCost }
        Object.assign(this.settleListParam, hjMap)
        // 基金费用表格信息
        // 基金合计
        const jjhj = busSettleBaseInfoList.d47 + busSettleBaseInfoList.d48 + busSettleBaseInfoList.d49 + busSettleBaseInfoList.d50 + busSettleBaseInfoList.d51 +
            busSettleBaseInfoList.d52 + busSettleBaseInfoList.d53
        // 个人支付合计
        const grzfhj = busSettleBaseInfoList.d54 > 0 ? busSettleBaseInfoList.d54 : 0 + busSettleBaseInfoList.d55 > 0 ? busSettleBaseInfoList.d55 : 0 +
            busSettleBaseInfoList.d56 > 0 ? busSettleBaseInfoList.d56 : 0 + busSettleBaseInfoList.d57 > 0 ? busSettleBaseInfoList.d57 : 0
        const zftableData = [
          { col1: '个人支付', col2: '个人自付', col3: busSettleBaseInfoList.d54 > 0 ? busSettleBaseInfoList.d54 : 0 },
          { col1: null, col2: '个人自费', col3: busSettleBaseInfoList.d55 > 0 ? busSettleBaseInfoList.d55 : 0 },
          { col1: null, col2: '个人账户支付', col3: busSettleBaseInfoList.d56 > 0 ? busSettleBaseInfoList.d56 : 0 },
          { col1: null, col2: '个人现金支付', col3: busSettleBaseInfoList.d57 > 0 ? busSettleBaseInfoList.d57 : 0 },
          { col1: null, col2: '合计', col3: grzfhj }
        ]
        const zftableDataMap = { zftableData: zftableData }
        Object.assign(this.settleListParam, zftableDataMap)
        this.validate()
        this.groupDiseaseInfo()
        Object.assign(this.settleListParam, { completeErrorsMap: null })
        Object.assign(this.settleListParam, { logicErrorsMap: null })
        Object.assign(this.settleListParam, { scoreErrorsMap: null })
      })
    },
    // 保存编辑
    save () {
      // 获取基本结算清单信息
      // let params = new URLSearchParams();
      // params.append('id', this.$route.query.id);
      // getSettleListAllInfo(params).then(response=>{
      //   const busSettleBaseInfoList = response.data.somHiInvyBasInfo;
      //   const busOutpatientClinicDiagnosisList = response.data.busOutpatientClinicDiagnosisList;
      //   Object.assign(compareListParam,response.data);
      //   //重症监护信息转为list回显
      //   if(busSettleBaseInfoList){
      //     const zzjhxxListValues = [];
      //     const zzjhxx1 = {b40:busSettleBaseInfoList.b40x01,b41:busSettleBaseInfoList.b41x01,b42:busSettleBaseInfoList.b42x01,b43:busSettleBaseInfoList.b43x01};
      //     const zzjhxx2 = {b40:busSettleBaseInfoList.b40x02,b41:busSettleBaseInfoList.b41x02,b42:busSettleBaseInfoList.b42x02,b43:busSettleBaseInfoList.b43x02};
      //     const zzjhxx3 = {b40:busSettleBaseInfoList.b40x03,b41:busSettleBaseInfoList.b41x03,b42:busSettleBaseInfoList.b42x03,b43:busSettleBaseInfoList.b43x03};
      //     zzjhxxListValues.push(zzjhxx1);
      //     zzjhxxListValues.push(zzjhxx2);
      //     zzjhxxListValues.push(zzjhxx3);
      //     const zzjhxxListMap = {zzjhxxList:zzjhxxListValues};
      //     Object.assign(compareListParam,zzjhxxListMap);
      //   }
      //   //获取门诊慢特病科室和就诊日期信息
      //   if(busOutpatientClinicDiagnosisList.length>0){
      //     const deptCode = {deptCode:busOutpatientClinicDiagnosisList[0].deptCode};
      //     Object.assign(compareListParam,deptCode);
      //     const mdtrtDate = {diagnosticDate:busOutpatientClinicDiagnosisList[0].mdtrtDate};
      //     Object.assign(compareListParam,mdtrtDate);
      //   }
      //   //费用信息转为list回显
      //   const settleDateRange = [];
      //   settleDateRange.push(busSettleBaseInfoList.d36);
      //   settleDateRange.push(busSettleBaseInfoList.d37);
      //   const settleDateRangeMap = {settleDateRange:settleDateRange};
      //   Object.assign(compareListParam,settleDateRangeMap);
      //   //基本费用合计
      //   const sumBaseCost = busSettleBaseInfoList.d38 + busSettleBaseInfoList.d39 +busSettleBaseInfoList.d40 + busSettleBaseInfoList.d41
      //     + busSettleBaseInfoList.d42 + busSettleBaseInfoList.d20x02 + busSettleBaseInfoList.d13 + busSettleBaseInfoList.d43
      //     + busSettleBaseInfoList.d23 + busSettleBaseInfoList.d44 + busSettleBaseInfoList.d24 + busSettleBaseInfoList.d45
      //     + busSettleBaseInfoList.d46 + busSettleBaseInfoList.d34;
      //   const hjMap = {sumBaseCost:sumBaseCost};
      //   Object.assign(compareListParam,hjMap);
      //   //基金费用表格信息
      //   //基金合计
      //   const jjhj = busSettleBaseInfoList.d47 + busSettleBaseInfoList.d48 +busSettleBaseInfoList.d49 +busSettleBaseInfoList.d50 +busSettleBaseInfoList.d51
      //     + busSettleBaseInfoList.d52 + busSettleBaseInfoList.d53;
      //   //个人支付合计
      //   const grzfhj = busSettleBaseInfoList.d54 + busSettleBaseInfoList.d55 +busSettleBaseInfoList.d56 +busSettleBaseInfoList.d57;
      //   //总合计
      //   const zhj = jjhj + grzfhj;
      //   const  zftableData= [
      //     {col1: '基金支付',col2: '医保统筹基金支付',col3: busSettleBaseInfoList.d47>0?busSettleBaseInfoList.d47:0,col4: '个人支付',col5: '个人自付',col6: busSettleBaseInfoList.d54>0?busSettleBaseInfoList.d54:0},
      //     {col1: null,col2: '其他支付：大病保险',col3: busSettleBaseInfoList.d48>0?busSettleBaseInfoList.d48:0,col4: null,col5: '个人自费',col6: busSettleBaseInfoList.d55>0?busSettleBaseInfoList.d55:0},
      //     {col1: null,col2: '医疗救助',col3: busSettleBaseInfoList.d49>0?busSettleBaseInfoList.d49:0,col4: null,col5: null,col6: null},
      //     {col1: null,col2: '公务员医疗补助',col3: busSettleBaseInfoList.d50>0?busSettleBaseInfoList.d50:0,col4: null,col5: '个人账户支付',col6: busSettleBaseInfoList.d56>0?busSettleBaseInfoList.d56:0},
      //     {col1: null, col2: '大额补充', col3: busSettleBaseInfoList.d51>0?busSettleBaseInfoList.d51:0, col4: null, col5: null, col6: null},
      //     {col1: null, col2: '企业补充', col3: busSettleBaseInfoList.d52>0?busSettleBaseInfoList.d52:0, col4: null, col5: '个人现金支付', col6: busSettleBaseInfoList.d57>0?busSettleBaseInfoList.d57:0},
      //     {col1: null, col2: '其他', col3: busSettleBaseInfoList.d53>0?busSettleBaseInfoList.d53:0, col4: null, col5: null, col6: null},
      //     {col1: null, col2: '合计', col3: jjhj, col4: null, col5: '合计', col6: grzfhj},
      //     {col1: '合计', col2: zhj>0?zhj:0, col3: null, col4: null, col5: null, col6: null}
      //   ];
      //   const zftableDataMap = {zftableData:zftableData};
      //   Object.assign(compareListParam,zftableDataMap);

      // console.log(compareListParam);
      // console.log(this.settleListParam);
      // console.log(this.isObjectValueEqual(this.settleListParam.somHiInvyBasInfo,compareListParam.somHiInvyBasInfo));
      // console.log(this.isObjectValueEqual(this.settleListParam.somHiInvyBasInfo,compareListParam.somHiInvyBasInfo));

      // 首先判断是否修改过数据，如果修改，那么提交，否则不提交
      // if(!this.isObjectValueEqual(this.settleListParam.somHiInvyBasInfo,compareListParam.somHiInvyBasInfo)){
      this.listLoading = true
      updateSettleListAllInfo(this.settleListParam).then(response => {
        this.listLoading = false
        this.$message({
          type: 'success',
          message: '保存成功',
          duration: 1000
        })
        location.reload()
      })
      // }else{
      //   this.$message({
      //     type: 'success',
      //     message: '未修改数据，无需保存！',
      //     duration:1000
      //   });
      // }
      // });
    },
    // 导出病案
    exportWord () {

    },
    fontColor () {
      if (dipCodg == item.dipCodg) {
        return red
      } else {
        return black
      }
    },
    // 校验得分
    validate () {
      this.listLoading = true
      validateMedical(this.settleListParam).then(response => {
        this.listLoading = false
        this.completeError = response.data.completeError
        this.completeErrorMsg = response.data.completeErrorMsg
        this.logicError = response.data.logicError
        this.logicErrorMsg = response.data.logicErrorMsg
        this.refer_sco = response.data.refer_sco
        this.scoreMsg = response.data.scoreMsg
        this.completeErrors = response.data.completeErrors
        this.logicErrors = response.data.logicErrors
        this.scoreErrors = response.data.scoreErrors
        this.$message({
          type: 'success',
          message: '已完成校验',
          duration: 1000
        })
      })
    },
    // 分组、病种时间费用信息查询、疾病诊断资源消耗查询
    groupDiseaseInfo () {
      // getStandCostAndDayAndGroupInfo(this.settleListParam).then(response => {
      //   // 获取该病种平均参考费用和参考住院时间
      //   // DRG部分
      //   if (response.data.avgInHosCost) {
      //     this.avgInHosCost = response.data.avgInHosCost
      //   } else {
      //     this.avgInHosCost = '-'
      //   }
      //   if (response.data.avgDays) {
      //     if (response.data.avgDays) {
      //       this.avgDays = Number(response.data.avgDays).toFixed(2)
      //     }
      //   } else {
      //     this.avgDays = '-'
      //   }
      //   /// /获取建议病组平均参考费用和参考住院时间
      //   if (response.data.suggestAvgInHosCost) {
      //     this.suggestAvgInHosCost = response.data.suggestAvgInHosCost
      //   } else {
      //     this.suggestAvgInHosCost = '-'
      //   }
      //   if (response.data.suggestAvgDays) {
      //     if (response.data.suggestAvgDays) {
      //       this.suggestAvgDays = Number(response.data.suggestAvgDays).toFixed(2)
      //     }
      //   } else {
      //     this.suggestAvgDays = '-'
      //   }
      //   // DIP部分
      //   if (response.data.dipAvgInHosCost) {
      //     this.dipAvgInHosCost = response.data.dipAvgInHosCost
      //   } else {
      //     this.dipAvgInHosCost = '-'
      //   }
      //   if (response.data.dipAvgDays) {
      //     if (response.data.dipAvgDays) {
      //       this.dipAvgDays = Number(response.data.dipAvgDays).toFixed(2)
      //     }
      //   } else {
      //     this.dipAvgDays = '-'
      //   }
      //   if (response.data.suggestDipAvgInHosCost) {
      //     this.suggestDipAvgInHosCost = response.data.suggestDipAvgInHosCost
      //   } else {
      //     this.suggestDipAvgInHosCost = '-'
      //   }
      //   if (response.data.suggestDipAvgDays) {
      //     if (response.data.suggestDipAvgDays) {
      //       this.suggestDipAvgDays = Number(response.data.suggestDipAvgDays).toFixed(2)
      //     }
      //   } else {
      //     this.suggestDipAvgDays = '-'
      //   }
      //   //   0000 字段类型错误
      //   //   0001 主要诊断为NULL
      //   //   0002 该诊断不能为主要诊断
      //   //   0003 性别填写错误，请填写 ‘男’ 或者 ‘女’
      //   //    0004 总费用不得小于5元
      //   //   0005 返回多个DRG组的情况请联系管理员
      //   //   0006 没有查询到DRG组的情况请联系管理员
      //   //   0007 分组方案无此编码
      //   //   0008 主要诊断编码不规范
      //   switch (response.data.drgCodg) {
      //     case '0001': this.drgCodg = '主要诊断为空'; break
      //     case '0004': this.drgCodg = '总费用不得小于5元'; break
      //     case '0007': this.drgCodg = '分组方案无此编码'; break
      //     case '0008': this.drgCodg = '主要诊断编码不规范'; break
      //     default:this.drgCodg = response.data.drgCodg; this.drgName = response.data.drgName
      //   }
      //   switch (response.data.suggestDrgCode) {
      //     case '0001': this.suggestDrgCode = '主要诊断为空'; break
      //     case '0004': this.suggestDrgCode = '总费用不得小于5元'; break
      //     case '0007': this.suggestDrgCode = '分组方案无此编码'; break
      //     case '0008': this.suggestDrgCode = '主要诊断编码不规范'; break
      //     default:this.suggestDrgCode = response.data.suggestDrgCode
      //       this.suggestDrgName = response.data.suggestDrgName
      //   }
      //   if (response.data.dipCodg) {
      //     this.dipCodg = response.data.dipCodg
      //     this.dipName = response.data.dipName
      //   }
      //   if (response.data.dipAi) {
      //     this.dipPreGroupTop1 = response.data.dipAi[0]
      //     this.dipPreGroup = response.data.dipAi
      //   }
      //   // if(response.data.suggestDipCode){
      //   //   this.suggestDipCode = response.data.suggestDipCode;
      //   //   this.suggestDipName = response.data.suggestDipName;
      //   // }
      //   if (response.data.diagnoseCostRankList) {
      //     this.diagnoseCostRankList = response.data.diagnoseCostRankList
      //   }
      //   if (response.data.operateCostRankList) {
      //     this.operateCostRankList = response.data.operateCostRankList
      //   }
      // })
    },
    changeSelectDic (value) {
      if (value == 1) {
        this.settleListParam.showFlag = '1'
        this.settleListParam.showBa = false
        this.settleListParam.showJsqd = true
        this.settleListParam.showJsqdAndBa = true
      } else if (value == 2) {
        this.settleListParam.showFlag = '2'
        this.settleListParam.showBa = true
        this.settleListParam.showJsqd = false
        this.settleListParam.showJsqdAndBa = true
      } else if (value == 9) {
        this.settleListParam.showFlag = '9'
        this.settleListParam.showBa = true
        this.settleListParam.showJsqd = true
        this.settleListParam.showJsqdAndBa = true
      }
    },

    showCompleteErrorDic () {
      // 点击某一种错误的按钮时，不能展示其他错误的情况
      if (this.settleListParam.logicErrorsMap !== null || this.settleListParam.scoreErrorsMap !== null) {
        this.settleListParam.showFlag = '11'
        for (let key in this.settleListParam.logicErrorsMap) {
          if (document.getElementById(key)) {
            document.getElementById(key).style.border = ''
          }
        }
        for (let key in this.settleListParam.scoreErrorsMap) {
          if (document.getElementById(key)) {
            document.getElementById(key).style.border = ''
          }
        }
        Object.assign(this.settleListParam, { logicErrorsMap: null })
        Object.assign(this.settleListParam, { scoreErrorsMap: null })
      }
      // 错误信息赋值
      if (this.settleListParam.completeErrorsMap !== null) {
        this.selectDic = '1'
        this.settleListParam.showFlag = '1'
        this.settleListParam.showBa = false
        this.settleListParam.showJsqd = true
        this.settleListParam.showJsqdAndBa = true
        Object.assign(this.settleListParam, { completeErrorsMap: null })
      } else {
        this.selectDic = '9'
        this.settleListParam.showFlag = '9'
        this.settleListParam.showBa = true
        this.settleListParam.showJsqd = true
        this.settleListParam.showJsqdAndBa = true
        Object.assign(this.settleListParam, { completeErrorsMap: this.completeErrors })
      }
      // 错误提醒样式
      for (let my_key in this.completeErrors) {
        if (document.getElementById(my_key)) {
          let color = document.getElementById(my_key).style.border
          if (color == '') {
            document.getElementById(my_key).style.border = '1px solid red'
          } else {
            document.getElementById(my_key).style.border = ''
          }
        }
      }
    },
    showLogicErrorDic () {
      // 点击某一种错误的按钮时，不能展示其他错误的情况
      if (this.settleListParam.completeErrorsMap !== null || this.settleListParam.scoreErrorsMap !== null) {
        this.settleListParam.showFlag = '22'
        for (let my_key in this.settleListParam.completeErrorsMap) {
          if (document.getElementById(my_key)) {
            document.getElementById(my_key).style.border = ''
          }
        }
        for (let my_key in this.settleListParam.scoreErrorsMap) {
          if (document.getElementById(my_key)) {
            document.getElementById(my_key).style.border = ''
          }
        }
        Object.assign(this.settleListParam, { completeErrorsMap: null })
        Object.assign(this.settleListParam, { scoreErrorsMap: null })
      }
      // 错误信息赋值
      if (this.settleListParam.logicErrorsMap !== null) {
        this.selectDic = '1'
        this.settleListParam.showFlag = '1'
        this.settleListParam.showBa = false
        this.settleListParam.showJsqd = true
        this.settleListParam.showJsqdAndBa = true
        Object.assign(this.settleListParam, { logicErrorsMap: null })
      } else {
        this.selectDic = '9'
        this.settleListParam.showFlag = '9'
        this.settleListParam.showBa = true
        this.settleListParam.showJsqd = true
        this.settleListParam.showJsqdAndBa = true
        Object.assign(this.settleListParam, { logicErrorsMap: this.logicErrors })
      }
      // 错误提醒样式渲染
      for (let my_key in this.logicErrors) {
        if (document.getElementById(my_key)) {
          let color = document.getElementById(my_key).style.border
          if (color == '') {
            document.getElementById(my_key).style.border = '1px solid red'
          } else {
            document.getElementById(my_key).style.border = ''
          }
        }
      }
    },
    showScoreErrorDic () {
      // 点击某一种错误的按钮时，不能展示其他错误的情况
      if (this.settleListParam.completeErrorsMap !== null || this.settleListParam.logicErrorsMap !== null) {
        this.settleListParam.showFlag = '99'
        for (let my_key in this.settleListParam.completeErrorsMap) {
          if (document.getElementById(my_key)) {
            document.getElementById(my_key).style.border = ''
          }
        }
        for (let my_key in this.settleListParam.logicErrorsMap) {
          if (document.getElementById(my_key)) {
            document.getElementById(my_key).style.border = ''
          }
        }
        Object.assign(this.settleListParam, { completeErrorsMap: null })
        Object.assign(this.settleListParam, { logicErrorsMap: null })
      }
      // 错误信息赋值
      if (this.settleListParam.scoreErrorsMap !== null) {
        this.selectDic = '1'
        this.settleListParam.showFlag = '1'
        this.settleListParam.showBa = false
        this.settleListParam.showJsqd = true
        this.settleListParam.showJsqdAndBa = true
        Object.assign(this.settleListParam, { scoreErrorsMap: null })
      } else {
        this.selectDic = '9'
        this.settleListParam.showFlag = '9'
        this.settleListParam.showBa = true
        this.settleListParam.showJsqd = true
        this.settleListParam.showJsqdAndBa = true
        Object.assign(this.settleListParam, { scoreErrorsMap: this.scoreErrors })
      }
      // 错误提醒样式渲染
      for (let my_key in this.scoreErrors) {
        if (document.getElementById(my_key)) {
          let color = document.getElementById(my_key).style.border
          if (color == '') {
            document.getElementById(my_key).style.border = '1px solid red'
          } else {
            document.getElementById(my_key).style.border = ''
          }
        }
      }
    },
    showScoreErrorDetail () {
      this.$router.push({ path: '/caseQual/caseQualScoreDet', query: { id: this.$route.query.id, a11: this.settleListParam.somHiInvyBasInfo.a11, deduPointRea: this.scoreMsg } })
    },
    getGroup () {
      this.diagnoseToolShow = !this.diagnoseToolShow
      // this.groupDiseaseInfo();
    },
    // 重置页面
    reset () {
      this.selectDic = 1
      this.showBaseInfo = true
      this.showBaseInfoColor = '#ecf5ff'
      this.showMzmtbInfo = false
      this.showZyzlInfo = false
      this.showYlfyInfo = false
    },
    // 判断两个对象内容是否一致
    isObjectValueEqual (a, b) {
      // 取对象a和b的属性名
      let aProps = Object.getOwnPropertyNames(a)
      let bProps = Object.getOwnPropertyNames(b)
      // 判断属性名的length是否一致
      if (aProps.length != bProps.length) {
        return false
      }
      // 循环取出属性名，再判断属性值是否一致
      for (let i = 0; i < aProps.length; i++) {
        let propName = aProps[i]
        if (a[propName] !== b[propName]) {
          return false
        }
      }
      return true
    },
    // 判断两个数组(元素是对象)内容是否一致
    isArrayObjectValueEqual (a, b) {
      if (a.length != b.length) {
        return false
      } else {
        let f = 0
        for (let i = 0; i < a.length; i++) {
          if (!isObjectValueEqual(a[i], b[i])) {
            f++
          }
        }
        if (f == 0) {
          return true
        } else {
          return false
        }
      }
    },

    mousedowm (e) { // 鼠标按下时的鼠标所在的X，Y坐标
      this.mouseDownX = e.pageX
      this.mouseDownY = e.pageY
      // 初始位置的X，Y 坐标
      // this.initX = obj.offsetLeft;
      // this.initY = obj.offsetTop;
      console.log('e', e)
      // 表示鼠标已按下
      this.flag = true
    },
    mousemove (e) {
      if (this.flag) {
        console.log('e :', e)
      }
    }

  },
  directives: {
    drag (el) {
      let oDiv = el // 当前元素
      // let self = this // 上下文
      // 禁止选择网页上的文字
      document.onselectstart = function () {
        return false
      }
      oDiv.onmousedown = function (e) {
        // 鼠标按下，计算当前元素距离可视区的距离
        let disX = e.clientX - oDiv.offsetLeft
        let disY = e.clientY - oDiv.offsetTop
        document.onmousemove = function (e) {
          // 通过事件委托，计算移动的距离
          let l = e.clientX - disX
          let t = e.clientY - disY
          // 移动当前元素
          oDiv.style.left = l + 'px'
          oDiv.style.top = t + 'px'
        }
        document.onmouseup = function (e) {
          document.onmousemove = null
          document.onmouseup = null
        }
        // return false不加的话可能导致黏连，就是拖到一个地方时div粘在鼠标上不下来，相当于onmouseup失效
        return false
      }
    }
  }
}
</script>
<style scoped>
  .validateCountError{
    margin-top: 5px;width: 55px;font-weight:bold;font-size:13px;color:red;background-color: lavender;
  }
  .validateCountScore{
    margin-top: 5px;width: 55px;font-weight:bold;font-size:13px;color:#33CC33;background-color: lavender;
  }
  .count-panel{
    border: 1px solid red;
    display: flex;
    flex-flow:row nowrap;
    justify-content:space-around;
    align-items:center;
    align-content:space-around;
  }
  .count-panel>.count-item{
    height: 20px;
    width: 1px;
    flex: 1 1 1px;
    border-radius: 4px;
    border: solid 1px #0ba2b3;
    margin: 5px;
    background-size: 100% auto;
  }
  .count-item .title{
    width: 100%;
    text-align: left;
    font-family: MicrosoftYaHei;
    font-size: 12px;
  }
  .count-item .value,.count-item .value *{
    width: 100%;
    text-align: center;
    font-family: "290-CAI978";
    font-size: 16px;
    font-style: normal;
    color: #0ba2b3;
  }
  .count-item .value,.count-item .drgValue{
    width:100%;
    margin-top:8px;
    text-align: center;
    font-size: 12px;
    font-style: normal;
    color: #0ba2b3;
  }
  .count-item .value .others{
    font-family: MicrosoftYaHei;
    font-size: 12px;
    font-style:normal;
    margin: 0px 3px;
  }

  /deep/ .el-header{
      padding:0 5px;
  }

  /*DRG辅助提示悬浮框*/
  .drg-float-area {
    position:relative;
    width: 280px;
    top: 115px;
    border:1.5px solid #988e19;
    border-radius: 3px;
    position: fixed;
    right: 50px;
    cursor: pointer;
    z-index: 1000;
  }
  /deep/ .el-collapse-item__header{
    height:40px;
    line-height:40px;
    font-size:15px;
    color:#FFFFFF;
    font-weight:600;
    background-color: rgb(30, 106, 188);
  }
  /deep/ .el-collapse-item__content{
    padding-bottom:0px;
  }
  .currentGroup{

  }
  .suggestGroup{

  }
  .otherGroup{

  }

  /*编码资源消耗悬浮框*/
  .diagnose-float-area {
    position:relative;
    width: 280px;
    top: 115px;
    border:1.5px solid #988e19;
    border-radius: 3px;
    position: fixed;
    right: 50px;
    cursor: pointer;
    z-index: 1000;
  }
  /deep/ .el-card__body{
    padding:0px;
  }
  .currentGroup-title{
    font-weight:600;
  }
  .currentGroup-item{
    font-size: 12px;
    padding:1px 26px;
  }
  .currentGroup-costdays{
    font-size: 12px;
    padding:1px 26px;
  }
  .suggestGroup-title{
    font-weight:600;
  }
  .suggestGroup-item{
    font-size: 12px;
    padding:1px 26px;
    overflow: hidden;
    white-space:nowrap;
    text-overflow: ellipsis;
  }
  .suggestGroup-costdays{
    font-size: 12px;
    padding:1px 26px;
  }
  .diagnoseResourceRank{
    max-height: 170px;
    overflow-y:auto;
  }
  .diagnoseResourceRank-title{
    font-weight:600;
  }
  .diagnoseResourceRank-firstItem{
    font-size: 12px;
    color:#1e6abc ;
    font-weight:600;
    padding:1px 26px;
  }
  .diagnoseResourceRank-otherItem{
    font-size: 12px;
    padding:1px 26px;
  }

  .operateResourceRank{
    max-height: 170px;
    overflow-y:auto;
  }
  .operateResourceRank-title{
    font-weight:600;
  }
  .operateResourceRank-firstItem{
    font-size: 12px;
    color:#1e6abc ;
    font-weight:600;
    padding:1px 26px;
  }
  .operateResourceRank-otherItem{
    font-size: 12px;
    padding:1px 26px;
  }

</style>
