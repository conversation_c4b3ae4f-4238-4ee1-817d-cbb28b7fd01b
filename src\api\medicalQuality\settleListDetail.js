import request from '@/utils/request'
export function getSettleListAllInfo (id) {
  return request({
    url: '/settleListManage/getSettleListAllInfo',
    method: 'post',
    params: id
  })
}

export function getErrorInfo (id) {
  return request({
    url: '/settleListManage/getErrorInfo',
    method: 'post',
    params: id
  })
}

export function updateSettleListAllInfo (data) {
  return request({
    url: '/settleListManage/updateSettleListAllInfo',
    method: 'post',
    data: data
  })
}

export function validateMedical (data) {
  return request({
    url: '/settleListManage/validateMedical',
    method: 'post',
    data: data
  })
}

export function getStandCostAndDayAndGroupInfo (data) {
  return request({
    url: '/settleListManage/getStandCostAndDayAndGroupInfo',
    method: 'post',
    data: data
  })
}

export function selectBusSettleLIstError (params) {
  return request({
    url: '/settleListManage/selectBusSettleLIstError',
    method: 'post',
    params: params
  })
}

export function selectBusSettleErrorLIst (params) {
  return request({
    url: '/settleListManage/selectBusSettleErrorLIst',
    method: 'post',
    params: params
  })
}
export function preGroup (params) {
  return request({
    url: '/preAiPaymentPrediction/preGroupSettle',
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

export function restartCheck (params) {
  return request({
    url: '/settleListManage/restartCheck',
    method: 'post',
    data: params
  })
}

export function updateSettleList (params) {
  return request({
    url: '/settleListManage/updateSettleList',
    method: 'post',
    data: params
  })
}

export function restoreHistoryBusSettle (params) {
  return request({
    url: '/settleListManage/restoreHistoryBusSettle',
    method: 'post',
    data: params
  })
}

export function insertHistory (params) {
  return request({
    url: '/settleListManage/insertHistory',
    method: 'post',
    data: params
  })
}

export function updateSettleListHisState (params) {
  return request({
    url: '/settleListManage/updateSettleListHisState',
    method: 'post',
    params: params
  })
}

export function updateSettleListLookOver (params) {
  return request({
    url: '/settleListManage/updateSettleListLookOver',
    method: 'post',
    params: params
  })
}

export function selectProcessResult (params) {
  return request({
    url: '/settleListManage/selectProcessResult',
    method: 'post',
    params: params
  })
}

/**
 * 查询icd编码
 * @param params
 * @returns {*}
 */
export function queryICDCode (params) {
  return request({
    url: '/settleListManage/queryICDCode',
    method: 'post',
    params: params
  })
}

/**
 * 修改数据
 * @param params
 * @returns {*}
 */
export function modifySettleListInfo (params) {
  return request({
    url: '/settleListManage/modifySettleListInfo',
    method: 'post',
    data: params
  })
}
export function querySettleListOpeLog (params) {
  return request({
    url: '/settleListManage/querySettleListOpeLog',
    method: 'post',
    data: params
  })
}

/**
 * 更改锁状态
 * @param params
 * @returns {*}
 */
export function updateLockState (params) {
  return request({
    url: '/settleListManage/updateLockState',
    method: 'post',
    data: params
  })
}

/**
 * 查询锁状态
 * @param params
 * @returns {*}
 */
export function queryLockState (params) {
  return request({
    url: '/settleListManage/queryLockState',
    method: 'post',
    data: params
  })
}

/**
 * 查询跳转数据
 * @param params
 * @returns {*}
 */
export function querySkipData (params) {
  return request({
    url: '/settleListManage/querySkipData',
    method: 'post',
    data: params
  })
}

/**
 * 数据同步
 * @param params
 * @returns {*}
 */
export function dataSynchronization (params) {
  return request({
    url: '/settleListManage/dataSynchronization',
    method: 'post',
    data: params
  })
}


/**
 * 众阳数据同步
 * @param params
 * @returns {*}
 */
export function get2_23_4 (params) {
  return request({
    url: '/zhongyangApi/get2_23_4',
    method: 'post',
    data: params
  })
}
