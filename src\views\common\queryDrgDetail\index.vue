<template>
  <div class="app-container">
    <drg-container :headerPercent="10">
      <template slot="header">
        <drg-title-line title="查询条件" />
        <el-form :inline="true" :model="listQuery" size="mini" label-width="80px">

              <el-form-item :label="timeName">
                <el-date-picker :disabled="true"
                                class="som-form-item"
                                v-model="listQuery.cysj"
                                type="daterange"
                                size="mini"
                                unlink-panels
                                range-separator="-"
                                value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>

              <el-form-item label="出院科室">
                <el-input class="som-form-item" v-model="listQuery.priOutHosDeptName" :disabled="true"></el-input>
              </el-form-item>

              <el-form-item :label="prefix + '名称'">
                <el-input class="som-form-item" v-model="listQuery.queryDrgsName" :disabled="true"></el-input>
              </el-form-item>

              <el-form-item label="医生姓名">
                <el-input class="som-form-item" v-model="listQuery.drName" :disabled="true"></el-input>
              </el-form-item>

              <el-popconfirm
                confirm-button-text='确定'
                cancel-button-text='导出全部'
                icon="el-icon-info"
                icon-color="red"
                title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
                <el-button slot="reference" type="success">导出Excel</el-button>
              </el-popconfirm>

        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="病组详情" />
        <div class="table-container" style="height: 90%;width: 100%">
          <el-table ref="drgDetail"
                    id="drgTable"
                    :key=Math.random()
                    size="mini"
                    height="100%"
                    stripe
                    :data="list"
                    style="width: 100%;"
                    v-loading="listLoading"
                    border>
            <el-table-column fixed
                             label="序号"
                             type="index"
                             width="50">
            </el-table-column>
            <el-table-column fixed :label="prefix + '编码'" prop="drgsCode" align="left" width="110" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.drgsCode | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column :label="prefix + '名称'"  align="left" prop="drgsName" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.drgsName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="病案数"  prop="medcasVal" align="center" width="100" >
              <template slot-scope="scope">
                <div v-if="Number(scope.row.medcasVal)>0" class='skip' @click="queryMedicalNum(scope.row)">
                  {{scope.row.medcasVal | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.medcasVal)==0" style="color:#000000">
                  {{scope.row.medcasVal | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="科室数" prop="deptNum" align="center" width="90">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.deptNum)>0" class='skip' @click="queryDeptNum(scope.row)">
                  {{scope.row.deptNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.deptNum)==0" style="color:#000000">
                  {{scope.row.deptNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="权重" prop="drgWt" align="center" width="100">
              <template slot-scope="scope">{{scope.row.drgWt | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="总权重" prop="totalDrgWeight" align="center" width="120">
              <template slot-scope="scope">{{scope.row.totalDrgWeight | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院费用" prop="avgCost" align="center" width="130">
              <template slot-scope="scope">{{scope.row.avgCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院日" prop="avgDays" align="center" width="100">
              <template slot-scope="scope">{{scope.row.avgDays | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="时间消耗指数" prop="timeIndex" align="center" width="110">
              <template slot-scope="scope">{{scope.row.timeIndex | formatIsEmpty}}</template>no
            </el-table-column>
            <el-table-column label="费用消耗指数" prop="costIndex" align="center" width="110">
              <template slot-scope="scope">{{scope.row.costIndex | formatIsEmpty}}</template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            layout="total, sizes,prev, pager, next,jumper"
            :page-size="listQuery.pageSize"
            :page-sizes="[200,1000,5000,10000]"
            :current-page.sync="listQuery.pageNum"
            :total="total">
          </el-pagination>
        </div>
      </template>
    </drg-container>
  </div>
</template>

<script>
import { queryDrgDetailList as queryPageData } from '@/api/common/drgCommon'
import { elExportExcel } from '@/utils/exportExcel'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  priOutHosDeptCode: null,
  priOutHosDeptName: null,
  queryDrgsCode: null,
  queryDrgsName: null,
  drCodg: null,
  drName: null,
  cy_start_date: null,
  cy_end_date: null,
  queryType: null,
  type: null
}
export default {
  name: 'queryDrgDetail',
  data () {
    return {
      listLoading: true,
      list: null,
      total: null,
      listQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      prefix: '',
      timeName: '出院时间'
    }
  },
  // created() {
  //   this.getList();
  // },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.drgDetail.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.drgDetail.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.drgDetail.$el.offsetTop - 35
      }
    })

    if (Object.keys(this.$route.query).length > 0) {
      if (this.$route.query.inHosFlag) {
        this.listQuery.inHosFlag = this.$route.query.inHosFlag
        if (this.$route.query.inHosFlag == '1') {
          this.timeName = '出院时间'
          Object.assign(this.listQuery, { cysj: [this.$route.query.cy_start_date, this.$route.query.cy_end_date] })
        } else if (this.$route.query.inHosFlag == '2') {
          this.timeName = '入院时间'
          Object.assign(this.listQuery, { cysj: [this.$route.query.inStartTime, this.$route.query.inEndTime] })
        } else if (this.$route.query.inHosFlag == '3') {
          this.timeName = '结算时间'
          Object.assign(this.listQuery, { cysj: [this.$route.query.seStartTime, this.$route.query.seEndTime] })
        }
      }
      if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
        Object.assign(this.listQuery, { cy_start_date: this.$route.query.cy_start_date })
        Object.assign(this.listQuery, { cy_end_date: this.$route.query.cy_end_date })
        this.setTimeToNull('1')
      }
      if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
        Object.assign(this.listQuery, { inStartTime: this.$route.query.inStartTime })
        Object.assign(this.listQuery, { inEndTime: this.$route.query.inEndTime })
        this.setTimeToNull('2')
      }
      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        Object.assign(this.listQuery, { seStartTime: this.$route.query.seStartTime })
        Object.assign(this.listQuery, { seEndTime: this.$route.query.seEndTime })
        this.setTimeToNull('3')
      }
    }
    this.getList()
  },
  methods: {
    getList () {
      // 回显
      Object.assign(this.listQuery, { priOutHosDeptCode: this.$route.query.priOutHosDeptCode })
      Object.assign(this.listQuery, { priOutHosDeptName: this.$route.query.priOutHosDeptName })
      Object.assign(this.listQuery, { queryDrgsCode: this.$route.query.queryDrgsCode })
      Object.assign(this.listQuery, { queryDrgsName: this.$route.query.queryDrgsName })
      Object.assign(this.listQuery, { drCodg: this.$route.query.drCodg })
      Object.assign(this.listQuery, { drName: this.$route.query.drName })
      Object.assign(this.listQuery, { queryType: this.$route.query.queryType })
      Object.assign(this.listQuery, { type: this.$route.query.type })
      Object.assign(this.listQuery, { inHosFlag: this.$route.query.inHosFlag })
      Object.assign(this.listQuery, { cy_start_date: this.$route.query.cy_start_date })
      Object.assign(this.listQuery, { cy_end_date: this.$route.query.cy_end_date })
      Object.assign(this.listQuery, { inStartTime: this.$route.query.inStartTime })
      Object.assign(this.listQuery, { inEndTime: this.$route.query.inEndTime })
      Object.assign(this.listQuery, { seStartTime: this.$route.query.seStartTime })
      Object.assign(this.listQuery, { seEndTime: this.$route.query.seEndTime })
      this.prefix = this.$somms.getCodePrefixByType(this.$route.query.type)
      this.listLoading = true
      queryPageData(this.listQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.listQuery, this.total, this.$refs.drgDetail.$children, document.getElementById('drgTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病组详细信息')
    },
    // 下转
    queryMedicalNum (row) {
      this.$router.push({ path: '/common/queryMedicalDetail',
        query: {
          queryDrgsCode: row.drgsCode,
          queryDrgsName: row.drgsName,
          drCodg: this.listQuery.drCodg,
          drName: this.listQuery.drName,
          priOutHosDeptCode: this.listQuery.priOutHosDeptCode,
          priOutHosDeptName: this.listQuery.priOutHosDeptName,
          cy_start_date: this.listQuery.cy_start_date,
          cy_end_date: this.listQuery.cy_end_date,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: this.listQuery.type
        }
      })
    },
    queryDeptNum (row) {
      this.$router.push({ path: '/common/queryDeptDetail',
        query: {
          queryDrgsCode: row.drgsCode,
          queryDrgsName: row.drgsName,
          cy_start_date: this.listQuery.cy_start_date,
          cy_end_date: this.listQuery.cy_end_date,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: this.listQuery.type
        }
      })
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    exportExcel () {
      let tableId = 'drgTable'
      let fileName = '病组详细信息'
      elExportExcel(tableId, fileName)
    },

    setTimeToNull (type) {
      if (type != '1') {
        this.listQuery.cy_start_date = ''
        this.listQuery.cy_end_date = ''
      }
      if (type != '2') {
        this.listQuery.inStartTime = ''
        this.listQuery.inEndTime = ''
      }
      if (type != '3') {
        this.listQuery.seStartTime = ''
        this.listQuery.seEndTime = ''
      }
    }
  },
  watch: {
    '$route.query': function () {
      this.getList()
    }
  }
}
</script>
<style>
</style>
