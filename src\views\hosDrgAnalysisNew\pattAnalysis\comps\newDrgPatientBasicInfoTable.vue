<template>
  <el-table ref="elTable"
            :id="id"
            height="100%"
            stripe
            :header-cell-style="{'text-align':'center'}"
            :data="data"
            :summary-method="getSummaries"
            show-summary
            v-loading="loading"
            border>
    <el-table-column label="序号" type="index" width="50" fixed align="center"></el-table-column>
    <el-table-column label="姓名" prop="name" width="80" align="center" :fixed="include('name')"></el-table-column>
    <el-table-column label="是否异地" prop="isRemote" width="80" :fixed="include('isRemote')" align="center"></el-table-column>
    <el-table-column label="是否结算" prop="listSerialNumFlag" width="80" :fixed="include('listSerialNumFlag')" align="center"></el-table-column>
    <el-table-column label="医保类型" prop="insuType" width="80" :fixed="include('listSerialNumFlag')" align="center"></el-table-column>
    <el-table-column label="病案号" prop="bah" width="130" :fixed="include('bah')" align="center"></el-table-column>
    <el-table-column label="结算ID" prop="settlementId" width="200" :fixed="include('settlementId')" align="center"></el-table-column>
    <el-table-column label="出院科室" prop="deptName" width="80" align="left" :fixed="include('deptName')"
                     :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="住院医师姓名" prop="drName" width="130" align="center" :fixed="include('doctorName')"
                     :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="DRG编码" prop="drgCodg" width="130" align="center" :fixed="include('drgCode')"
                     :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="DRG名称" prop="drgName" width="200" align="left" :fixed="include('drgName')"
                     :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="主要诊断" prop="mainDiagnoseCodeAndName" align="center" width="140" :show-overflow-tooltip="true">
      <template slot-scope="scope">{{scope.row.mainDiagnoseCodeAndName | formatIsEmpty}}</template>
    </el-table-column>
    <el-table-column label="成本系数" prop="adjmCof" width="200" align="left" :fixed="include('adjmCof')"
                     :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="未入组原因" prop="grpFaleRea" width="120" align="left" :fixed="include('groupFailReason')"
                     :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="入院诊断" prop="admDiag" width="300" align="left" :fixed="include('admDiag')"
                     :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="出院诊断" prop="mainDiag" width="300" align="left" :fixed="include('mainDiagnosis')"
                     :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="手术信息" prop="majorSurgery" width="300" align="left" :fixed="include('majorSurgery')"
                     :show-overflow-tooltip="true"></el-table-column>

    <el-table-column label="入院时间" prop="inHosTime" width="150" :fixed="include('inHosTime')"
                     :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="出院时间" prop="outHosTime" width="150" :fixed="include('outHosTime')"
                     :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="住院天数" prop="inHosDays" width="100" :fixed="include('inHosDays')" align="center"
                     sortable></el-table-column>
    <el-table-column label="DRG标杆天数" prop="standardDays" width="140" :fixed="include('DRG标杆天数')" align="center"
                     sortable></el-table-column>
    <el-table-column label="天数差值"  align="center" prop="dayDifference" width="80">
      <template slot-scope="scope">
        <div v-if="scope.row.inHosDays==null||scope.row.inHosDays==''||scope.row.inHosDays==0||scope.row.standardInHosDays==null||scope.row.standardInHosDays==''||scope.row.standardInHosDays==0">
          -
        </div>
        <div v-else-if="Number(scope.row.inHosDays)>Number(scope.row.standardInHosDays)">
          <!--{{scope.row.inHosDays | formatIsEmpty}}&emsp;-->
          <i class="el-icon-caret-bottom"></i>
          <span style="font-size: 10px;color:#FF0000;font-weight: bold">
                {{(Number(scope.row.standardInHosDays)-Number(scope.row.inHosDays)).toFixed(2)}}
              </span>
        </div>
        <div v-else-if="Number(scope.row.inHosDays)<=Number(scope.row.standardInHosDays)">
          <!--{{scope.row.inHosDays | formatIsEmpty}}&emsp;-->
          <i class="el-icon-caret-top"></i>
          <span style="font-size: 10px;color:#00CC00;font-weight: bold">
                +{{(Number(scope.row.standardInHosDays)-Number(scope.row.inHosDays)).toFixed(2)}}
              </span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="DRG标杆费用" prop="standardInHosTotalCost" width="130" :fixed="include('inHosTotalCost')"
                     align="right" sortable></el-table-column>
    <el-table-column label="住院总费用" prop="inHosTotalCost" width="130" :fixed="include('inHosTotalCost')"
                     align="right" sortable></el-table-column>
    <el-table-column label="单价" prop="price" width="130" :fixed="include('price')"
                     align="right" sortable></el-table-column>
    <el-table-column label="结算分值" prop="totlSco" width="130" :fixed="include('totlSco')"
                     align="right" sortable></el-table-column>
    <el-table-column label="预测金额" prop="forecastAmount" width="130" :fixed="include('forecastAmount')"
                     align="right" sortable></el-table-column>

    <el-table-column label="预测差异" prop="forecastAmountDiff" width="130" :fixed="include('forecastAmountDiff')"
                     align="right" sortable></el-table-column>
    <el-table-column label="企业补充医疗保险基金" prop="amount370100" width="130" :fixed="include('amount370100')"
                     align="right" sortable></el-table-column>
    <el-table-column label="个人自付" prop="psnSelfpay" width="130" :fixed="include('psnSelfpay')"
                     align="right" sortable></el-table-column>
    <el-table-column label="个人自费" prop="psnOwnpay" width="130" :fixed="include('psnOwnpay')"
                     align="right" sortable></el-table-column>
    <el-table-column label="个人账户" prop="acctPay" width="130" :fixed="include('acctPay')"
                     align="right" sortable></el-table-column>

    <el-table-column label="个人现金" prop="psnCashpay" width="130" :fixed="include('psnCashpay')"
                     align="right" sortable></el-table-column>

    <el-table-column label="药品费" prop="medicalCost" width="130" :fixed="include('medicalCost')" align="right"
                     sortable>
    </el-table-column>
    <el-table-column label="药占比" prop="medicalCostRate" width="130" :fixed="include('medicalCostRate')" align="center"
                     sortable>
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.medicalCostRate) }}
      </template>
    </el-table-column>
    <el-table-column label="耗材费" prop="materialCost" width="130" :fixed="include('materialCost')"
                     align="right" sortable>
    </el-table-column>
    <el-table-column label="耗占比" prop="materialCostRate" width="130" :fixed="include('materialCostRate')"
                     align="center" sortable>
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.materialCostRate) }}
      </template>
    </el-table-column>
    <el-table-column label="离院方式" prop="dscgWay" width="130" :fixed="include('dscgWay')"
                     align="left" sortable></el-table-column>
    <el-table-column label="疾病诊断个数" prop="digsNum" width="130" :fixed="include('digsNum')"
                     align="left" sortable></el-table-column>
    <el-table-column label="手术例数" prop="oprNum" width="130" :fixed="include('oprNum')"
                     align="left" sortable></el-table-column>
    <el-table-column label="主要手术" prop="mainOperativeCodeAndName" width="130" :fixed="include('mainOperativeCodeAndName')":show-overflow-tooltip="true"
                     align="left" sortable></el-table-column>
    <el-table-column label="一级手术" prop="oneLevelOprCodeAndName" width="130" :fixed="include('oneLevelOprCodeAndName')":show-overflow-tooltip="true"
                     align="left" sortable></el-table-column>

    <el-table-column label="一级标准手术" prop="oneLevelStanOprCodeAndName" width="130" :fixed="include('oneLevelStanOprCodeAndName')":show-overflow-tooltip="true"
                     align="left" sortable></el-table-column>
    <el-table-column label="二级手术" prop="twoLevelOprCodeAndName" width="130" :fixed="include('twoLevelOprCodeAndName')":show-overflow-tooltip="true"
                     align="left" sortable></el-table-column>
    <el-table-column label="二级标准手术" prop="twoLevelStanOprCodeAndName" width="130" :fixed="include('twoLevelStanOprCodeAndName')":show-overflow-tooltip="true"
                     align="left" sortable></el-table-column>
    <el-table-column label="三级手术" prop="threeLevelOprCodeAndName" width="130" :fixed="include('threeLevelOprCodeAndName')":show-overflow-tooltip="true"
                     align="left" sortable></el-table-column>
    <el-table-column label="三级标准手术" prop="threeLevelStanOprCodeAndName" width="130" :fixed="include('threeLevelStanOprCodeAndName')":show-overflow-tooltip="true"
                     align="left" sortable></el-table-column>
    <el-table-column label="四级手术" prop="fourLevelOprCodeAndName" width="130" :fixed="include('fourLevelOprCodeAndName')" :show-overflow-tooltip="true"
                     align="left" sortable></el-table-column>
    <el-table-column label="四级标准手术" prop="fourLevelStanOprCodeAndName" width="130" :fixed="include('fourLevelStanOprCodeAndName')" :show-overflow-tooltip="true"
                     align="left" sortable></el-table-column>
  </el-table>
</template>

<script>

export default {
  name: 'newDrgPatientBasicInfoTable',
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    },
    // 表格id
    id: {
      type: String
    }
  },
  filters: {
    formatIsEmpty(value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  updated () {
    this.$nextTick(() => {
      this.$refs.elTable.doLayout()
    })
  },
  methods: {
    include (column) {
      return this.fixedColumns.includes(column)
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },

    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length,
        customAverage: (values) => {
          const sum = values.reduce((prev, curr) => prev + curr, 0)
          return sum / values.length
        }
      }
      let sum13 = 0, sum21 = 0, sum23 = 0
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计'
          return
        }
        const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value))
        if (index === 12) {
          sums[index] = calculations.sum(values)
        } else if (index === 13 || index === 16 || index === 17 || index === 18 || index === 19 || index === 20 || index === 21 || index === 22 || index === 23 || index === 25) {
          sums[index] = calculations.sum(values).toFixed(2)
          if (index === 13) sum13 = sums[index]
          else if (index === 23) sum21 = sums[index]
          else if (index === 25) sum23 = sums[index]
        } else {
          sums[index] = ' '
        }
      })
      sums[22] = calculatePercentage(sum21, sum13)
      sums[24] = calculatePercentage(sum23, sum13)
      return sums

      function calculatePercentage (numerator, denominator) {
        if (denominator !== 0) {
          return ((numerator / denominator) * 100).toFixed(2) + '%'
        }
        return '0.00%'
      }
    }
  }
}
</script>
