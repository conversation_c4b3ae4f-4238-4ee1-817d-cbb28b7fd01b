<template>
  <div class="app-container fs-13 flex app-container-row">
    <el-container>
      <el-header height="50px">
        <el-row>
          <el-col :span="12">
            <div class="grid-content bg-purple-dark flex flex-align-center">
              <el-input placeholder="请输入内容，支持模糊" size="mini" v-model="searchText" class="input-with-select">
                <el-select v-model="searchType" size="mini" slot="prepend" placeholder="请选择" class="costom-select">
                  <el-option label="账户" value="1"></el-option>
                  <el-option label="昵称" value="2"></el-option>
                </el-select>
                <el-button slot="append" size="mini" icon="el-icon-search" type="primary" @click="queryUserList"></el-button>
              </el-input>
            </div>
          </el-col>
          <el-col :span="6">
            <el-button size="mini" type="success" icon="el-icon-refresh" style="margin-top:5px;margin-left:20px;" @click="recoverSys()">系统恢复</el-button>
          </el-col>
        </el-row>
      </el-header>
      <el-main class="flex flex-1 flex-col">
        <el-table :data="tableData" class="grid-list" >
          <el-table-column width="55" prop="id" v-if="false">
          </el-table-column>
          <el-table-column prop="userType" label="用户类别" align="center" width="130">
            <template slot-scope="scope">
              <div v-if="scope.row.expireTime!='-85276934'" >
                <el-tag size="medium" style='font-weight:bold;color: #FF9900'>试用账户</el-tag>
              </div>
              <div v-if="scope.row.expireTime=='-85276934'">
                <el-tag size="medium" style='font-weight:bold;color: #00CC00'>上线账户</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="nknm" label="姓名" align="center" width="150">
          </el-table-column>
          <el-table-column prop="username" label="登录账户" align="center" width="120">
          </el-table-column>
          <el-table-column prop="blngOrgOrgName" label="所属组织" align="center" :show-overflow-tooltip="true">
          </el-table-column>
          <el-table-column prop="crteTime" label="用户创建时间" align="center" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.crteTime | formatTime}}</template>
          </el-table-column>
          <el-table-column prop="expireTime" label="用户过期时间" align="center" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.expireTime | formatTime}}</template>
          </el-table-column>
          <el-table-column prop="do" label="操作" width="120" align="center">
            <template slot-scope="scope">
              <el-button icon="el-icon-edit" type="text"  @click="editUser(scope.$index, scope.row)">设置</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-row>
          <el-col  class="pageHeder-text-align">
            <div class="pagination-container">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="listQuery.pageNum"
                :page-size="listQuery.pageSize"
                layout="total, prev, pager, next, jumper"
                :total="listQuery.total">
              </el-pagination>
            </div>
          </el-col>
        </el-row>
      </el-main>
    </el-container>

    <!--用户信息-->
    <el-dialog title="设置" :visible.sync="dialogFormVisible" >
        <el-form :model="userForm" label-position="left" :label-width="formLabelWidth">
          <el-form-item v-if="false">
            <el-input v-model="userForm.id"></el-input>
          </el-form-item>
          <el-form-item label="用户昵称">
            <el-input v-model="userForm.nknm" size="mini" disabled></el-input>
          </el-form-item>
          <el-form-item label="用户类别">
            <el-radio-group v-model="userForm.userType" @change="userTypeChange">
              <el-radio label="1" border size="mini">试用账户</el-radio>
              <el-radio label="2" border size="mini">上线账户</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="用户过期时间" >
            <el-date-picker
              v-model="userForm.expireTime"
              type="datetime"
              size="mini"
              placeholder="选择日期时间"
              :disabled="expireTimeDisable">
            </el-date-picker>
          </el-form-item>
        </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="()=>submitForm()">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { format } from '@/utils/datetime'
import { queryUserByOrgId, editUserRequest, recoverSystem } from '@/api/orgManamement'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 50,
  total: 0
}
export default {
  name: 'userConfig',
  created () {
    // 查询所有用户
    this.queryAllUserList()
  },
  data () {
    let userForm = {
      id: '',
      nknm: '',
      userType: null,
      expireTime: null
    }
    return {
      listQuery: Object.assign({}, defaultListQuery),
      searchText: '',
      searchType: '1',
      tableData: [],
      dialogFormVisible: false,
      userForm: userForm,
      formLabelWidth: '120px',
      currMainOrgId: '',
      currMainOrgName: '',
      loading: false,
      expireTimeDisable: false
    }
  },
  filters: {
    formatTime (time) {
      if (time) {
        if (time == '-85276934') {
          return '无过期时间'
        }
        return format(time)
      } else {
        return '-'
      }
    }
  },
  methods: {
    queryAllUserList () {
      let param = {
        blngOrgOrgId: '',
        hasSubOrg: 1
      }
      // 组装搜索参数
      if (this.searchText) {
        let key = this.searchType == '1' ? 'username' : 'nickName'
        param[key] = this.searchText
      }
      queryUserByOrgId(param).then(response => {
        let data = response.data
        this.tableData = data.list
        this.listQuery = {
          pageNum: data.pageNum,
          pageSize: data.pageSize,
          total: data.total
        }
      })
    },
    queryUserList () {
      let param = {
        blngOrgOrgId: '',
        hasSubOrg: 1
      }
      // 组装搜索参数
      if (this.searchText) {
        let key = this.searchType == '1' ? 'username' : 'nickName'
        param[key] = this.searchText
      }
      queryUserByOrgId(param).then(response => {
        let data = response.data
        this.tableData = data.list
        this.listQuery = {
          pageNum: data.pageNum,
          pageSize: data.pageSize,
          total: data.total
        }
      })
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    editUser (index, row) {
      this.dialogFormVisible = true
      this.userForm.id = row.id
      this.userForm.nknm = row.nknm
      this.userForm.userType = row.expireTime == '-85276934' ? '2' : '1'
      this.expireTimeDisable = row.expireTime == '-85276934'
      this.userForm.expireTime = row.expireTime
    },
    userTypeChange (val) {
      if (val == '1') {
        this.expireTimeDisable = false
      } else if (val == '2') {
        this.expireTimeDisable = true
      }
    },
    recoverSys () {
      recoverSystem().then(res => {
        if (res.code == 200) {
          this.$message({ message: '操作成功', type: 'success' })
          this.dialogFormVisible = false
        } else {
          this.$message({
            message: '操作失败, ' + res.msg,
            type: 'error'
          })
        }
      })
    },
    // 表单提交
    submitForm () {
      this.$confirm('确认提交吗？', '提示', {}).then(() => {
        this.editLoading = true
        if (this.userForm.userType == '1' && !this.userForm.expireTime) {
          this.$message({
            message: '试用账户必须填写试用日期!',
            type: 'error'
          })
        }
        if (this.userForm.userType == '1' && this.userForm.expireTime) {
          this.userForm.expireTime = format(this.userForm.expireTime)
        }
        if (this.userForm.userType == '2') { // 上线账户试用期固定
          this.userForm.expireTime = '-85276934'
        }
        let params = Object.assign({}, this.userForm)
        editUserRequest(params.id, params).then(res => {
          this.editLoading = false
          if (res.code == 200) {
            this.$message({ message: '操作成功', type: 'success' })
            this.dialogFormVisible = false
          } else {
            this.$message({
              message: '操作失败, ' + res.msg,
              type: 'error'
            })
          }
          this.queryUserList()
        })
      })
    }
  }
}
</script>

<style scoped>
  .app-container{height: 680px;}
  .el-header {
    background-color: #fafafa;
    color: #333;
    padding: 10px;
  }
  .el-aside {
    color: #333;
    width: 200px;
    border-right: 1px solid #eeeeee;
  }
  /*.el-container{
    height: 500px; border: 1px solid #eee;
  }*/
  .el-dropdown.mg-l2{
    margin-left:5px;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409EFF;
  }
  .el-dropdown-link.el-icon--right:before{
    margin-left:5px;
  }
  .el-icon-setting {
    font-size: 12px;
    padding-left:10px;
  }
  .el-tree-customer{
    color: #333;
  }
  .el-icon-s-platform{padding-right: 5px;}
  .aside-content .el-header{color: #409EFF;
    display: flex;align-items: center;}
  .aside-content .el-input {padding: 10px;}
  .aside-content .el-input__inner{height: 30px;}
  .el-select .el-input {
    width: 90px;
  }
  .input-with-select .el-input-group__prepend {
    background-color: #fff;
  }
  .selectOrg.show-selectOrg-name{
    margin-left: 3px;
    border-bottom: 1px solid #999;
    overflow-y: auto;
    height: 18px;
  }
  .grid-content{height: 40px;}
  .input-with-select .el-button:hover{background-color: #f2f6fc;border-top-left-radius: 0;border-bottom-left-radius: 0;}
  .grid-list{font-size: 13px;}
  .el-button-coustme{width: 60px;}
  .el-select.org-type-style .el-input{width: 100%;}
  .show-org-title{width: 64px;}

  .custom-tree-node{
    display: flex;
    align-items: center;
  }
  .custom-tree-node .node-name{max-width: 150px;}
  .app-container.app-container-row{flex-direction: row;}

  .user-drawer__content{padding: 0 20px;}
  .user-drawer__footer{display: flex;}
  .user-drawer__footer .el-button{flex: 1 auto;}

  .user-drawer__content .el-select{width: 100%;}
  .el-select.el-select-multiple .el-input{width: 100%;}
  .el-select.costom-select{width: 80px;}
  .pageHeder-text-align{text-align: right;}
  .custom-el-drawer #el-drawer__title>span{outline: none;}
  .orgTitle{font-size: 14px;}
  .el-dialog__body{padding:0px 40px;}
  .el-main{padding:5px 10px 0px 10px;}
  /deep/.el-table th{padding:5px 0;}
  /deep/.el-table td{padding:5px 0;}

</style>
