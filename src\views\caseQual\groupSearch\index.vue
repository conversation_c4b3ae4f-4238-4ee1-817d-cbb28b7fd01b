<template>
  <div class="box-card">
    <el-card class="box-wrap">
      <div class="box-content">
        <!-- 病组搜索条件 -->
        <div class="box-left">
          <el-button-group style="margin-bottom: 10px">
            <el-button type="primary" @click="query">查询</el-button>
            <el-button type="primary" @click="add('disArr', 'c06c_', '其他诊断')">添加诊断</el-button>
            <el-button type="primary" @click="add('oprnArr', 'c35c_', '手术编码')">添加手术</el-button>
          </el-button-group>
          <el-form ref="form" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="主要诊断" prop="dscg_diag_codg">
              <icd-select v-model="form.dscg_diag_codg" icd-type="ICD-10" />
            </el-form-item>

            <!-- 其他诊断 -->
            <el-form-item :label="item.label + (idx + 1)" v-for="(item, idx) in disArr" :key="'zd'+idx" :prop="item.key">
              <icd-select v-model="form[item.key]" icd-type="ICD-10" />
              <el-button @click.prevent="del(idx, 'disArr',item.key)">删除</el-button>
            </el-form-item>

            <!-- 手术 -->
            <el-form-item :label="item.label + (idx + 1)" v-for="(item, idx) in oprnArr" :key="'ss'+idx" :prop="item.key">
              <icd-select v-model="form[item.key]" icd-type="ICD-9" />
              <el-button @click.prevent="del(idx, 'oprnArr',item.key)">删除</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 组合 -->
        <div class="box-right">
          <div class="box-right-item">
            <el-card header="可入组" style="height: 100%;" :body-style="{height:'calc( 100% - 52px ) !important',overflowY:'auto'}">
              <div v-for="(item, idx) in groupArr" :key="idx" @click="fnQueryRecords(item.dip.disease_code)" style="cursor: pointer" >

                  <el-card  style="margin-bottom: 10px" >
                    <div slot="header" class="clearfix" >
                      <el-popover
                        placement="top-start"
                        :visible-arrow="false"
                        trigger="hover"
                        content="点击可以查看历史入组情况">
                      <p slot="reference">{{ item.dip.disease_name }}</p>
                      </el-popover>
                      <p style="color: gray;font-size: 12px">{{ item.dip.disease_code }}</p>
                    </div>
                    <el-descriptions>
                      <el-descriptions-item label="基准分值">{{ item.line.refer_sco }}</el-descriptions-item>
                      <el-descriptions-item label="调节系数">{{ item.line.adjm_cof }}</el-descriptions-item>
                      <el-descriptions-item label="平均费用">{{ item.line.dipStandardInpf }}</el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions>
                      <el-descriptions-item label="排除编码">
                       <div v-for="(m, idx2) in item.delCodes" :key="idx2">{{ m }}/</div>
                      </el-descriptions-item>
                    </el-descriptions>
                  </el-card>
              </div>

            </el-card>
          </div>
          <div class="box-right-item" v-loading="cardLoading" element-loading-text="拼命加载中">
            <el-card :header="'历史入组   ' + dipCodg" style="height: 100%;" :body-style="{height:'calc( 100% - 52px ) !important',overflowY:'auto'}">
              <el-card v-for="(item, idx) in recordsArr" :key="idx" style="margin-bottom: 10px" :header="'病例数：'+ item.ct">
                <el-descriptions :column="1">
                  <el-descriptions-item :label="'主要诊断'" >{{ item.mainDiagDiseCodg + ' | ' + item.mainDiagDiseName }}</el-descriptions-item>
                </el-descriptions>
                <el-descriptions :column="1">
                  <el-descriptions-item :label="'主要手术'" >{{ item.mainOperCode ? (item.mainOperCode + ' | ' + item.mainOperName) : '' }}</el-descriptions-item>
                </el-descriptions>
                <el-descriptions :column="1" v-if="item.oper && item.oper.length > 0">
                  <el-descriptions-item :label="'其他手术' + (idx1 + 1)" v-for="(d, idx1) in item.oper" :key="idx1">{{ d }}</el-descriptions-item>
                </el-descriptions>
              </el-card>
            </el-card>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getGroupData, queryRecords } from '@/api/medicalQuality/groupCompQuery'
import IcdSearch from './comps/icdSearch.vue'
export default {
  name:'groupSearch',
  components: {
    'icd-select': IcdSearch
  },
  data () {
    return {
      form: {
        dscg_diag_codg: ''
      },
      rules: {
        dscg_diag_codg: [
          { required: true, message: '请输入主要诊断', trigger: 'blur' }
        ]
      },
      disArr: [],
      oprnArr: [],
      groupArr: [],
      recordsArr: [],
      dipCodg: '',
      cardLoading: false
    }
  },
  methods: {
    // 添加手术
    add (name, codePrefix, codesName) {
      let key = codePrefix + this[name].length
      let label = codesName
      this.$set(this.form, key, '')
      this.rules[key] = [
        { required: true, message: '请输入' + label, trigger: 'blur' }
      ]
      this[name].push({ label, key })
    },
    // 删除
    del (idx, name, key) {
      delete this.form[key]
      delete this.rules[key]
      this[name].splice(idx, 1)
    },
    // 查询
    query () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dipCodg = ''
          this.recordsArr = []
          getGroupData(this.form).then(res => {
            this.groupArr = res.data.groupData
          })
        }
      })
    },
    fnQueryRecords (data) {
      this.dipCodg = data
      this.cardLoading = true
      queryRecords({ dipCodg: data }).then(res => {
        this.recordsArr = res.data
        this.cardLoading = false
      }).catch(() => {
        this.cardLoading = false
      })
    }
  }
}

</script>

<style lang="scss" scoped>
@mixin wd{
  height: 100%;
  width: 100%;
}
.box{

  &-card{
    @include wd;
    box-sizing: border-box;
    padding: 5px;
  }

  &-wrap{
    @include wd;
  }

  &-content{
    @include wd;
    display: flex;
  }

  &-left{
    width: 30%;
    height: 100%;
  }

  &-right{
    padding-left: 20px;
    width: 70%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    &-item{
      width: 49%;
      height: 100%;
    }
  }
}
/deep/ .el-form-item__content{
  display: flex;
}
</style>
