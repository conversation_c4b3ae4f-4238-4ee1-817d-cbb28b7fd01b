<template>
  <el-table ref="elTable"
            :id="id"
            height="100%"
            stripe
            :header-cell-style="{'text-align':'center'}"
            :data="data"
            v-loading="loading"
            border>
    <el-table-column label="序号" type="index" fixed align="center" />
    <el-table-column label="DIP编码" prop="dipCodg" :show-overflow-tooltip="true" />
    <el-table-column label="DIP名称" prop="dipName" :show-overflow-tooltip="true" />
    <el-table-column :label="prefix + '医生数'" prop="doctorLossOrProfitNum" align="right" sortable>
      <template slot-scope="scope">
        <div :class="scope.row.doctorLossOrProfitNum == 0 ? '' : 'skip'"
             @click="scope.row.doctorLossOrProfitNum == 0 ? '' : queryDoctor(scope.row)">
          {{ scope.row.doctorLossOrProfitNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column :label="prefix + '病案数'" prop="medLossOrProfitNum" align="right" sortable>
      <template slot-scope="scope">
        <div :class="scope.row.medLossOrProfitNum == 0 ? '' : 'skip'"
             @click="scope.row.medLossOrProfitNum == 0 ? '' : queryPayToPredict(scope.row)">
          {{ scope.row.medLossOrProfitNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="分组错误数量" prop="groupErrorNum" align="right" sortable>
      <template slot-scope="scope">
        <div :class="scope.row.groupErrorNum == 0 ? '' : 'skip'"
             @click="scope.row.groupErrorNum == 0 ? '' : queryGroupError(scope.row)">
          {{ scope.row.groupErrorNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="完整性错误数量" prop="comErrorNum" align="right" sortable>
      <template slot-scope="scope">
        <div :class="scope.row.comErrorNum == 0 ? '' : 'skip'"
             @click="scope.row.comErrorNum == 0 ? '' : queryComError(scope.row)">
          {{ scope.row.comErrorNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="逻辑性错误数量" prop="logicErrorNum" align="right" sortable>
      <template slot-scope="scope">
        <div :class="scope.row.logicErrorNum == 0 ? '' : 'skip'"
             @click="scope.row.logicErrorNum == 0 ? '' : queryLogicError(scope.row)">
          {{ scope.row.logicErrorNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="分析" fixed="right" align="center" width="90px">
      <template slot-scope="scope">
        <i class="som-icon-analysis som-iconTool"
           title="分析"
           @click="showAnalysis(scope.row.dipCodg)"
           style="height: 1.2rem;width: 1.2rem"></i>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 表格id
    id: {
      type: String
    },
    // 亏损或盈利
    isLoss: {
      type: Boolean,
      default: true
    },
    queryForm: {
      type: Object
    }
  },
  data: () => ({
  }),
  computed: {
    prefix () {
      return this.isLoss ? '亏损' : '盈利'
    }
  },
  methods: {
    // 显示分析页面
    showAnalysis (dipCodg) {
      this.$emit('showAnalysisPage', dipCodg)
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    queryDoctor (item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/doctAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          dipCodg: item.dipCodg,
          deptCode: this.queryForm.deptCode,
          isLoss: this.isLoss ? 1 : 0,
          radioMode: 2,
          feeStas: this.queryForm.feeStas,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryPayToPredict (item) {
      this.$router.push({
        path: '/hosDipAnalysis/predictPay',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          dipCodg: item.dipCodg,
          deptCode: this.queryForm.deptCode,
          isLoss: this.isLoss ? 1 : 0,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryGroupError (item) {
    },
    queryComError (item) {
      this.$router.push({
        path: '/caseQual/compVali',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          dipCodg: item.dipCodg,
          deptCode: this.queryForm.deptCode,
          feeStas: this.queryForm.feeStas,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryLogicError (item) {
      this.$router.push({
        path: '/caseQual/logiVali',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          dipCodg: item.dipCodg,
          deptCode: this.queryForm.deptCode,
          resultType: 1,
          feeStas: this.queryForm.feeStas,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    }
  }
}
</script>
