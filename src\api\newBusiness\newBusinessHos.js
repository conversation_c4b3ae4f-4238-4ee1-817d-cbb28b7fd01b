import request from '@/utils/request'

/**
 * 查询头部汇总数据
 * @param params
 * @returns {*}
 */
export function querySummaryData (params) {
  return request({
    url: '/newBusinessHosAnalysisController/querySummaryData',
    method: 'post',
    params: params
  })
}

/**
 * 查询错误数据
 * @param params
 * @returns {*}
 */
export function queryErrorData (params) {
  return request({
    url: '/newBusinessHosAnalysisController/queryErrorData',
    method: 'post',
    params: params
  })
}

/**
 * 查询排序数据
 * @param params
 * @returns {*}
 */
export function queryOrderData (params) {
  return request({
    url: '/newBusinessHosAnalysisController/queryOrderData',
    method: 'post',
    params: params
  })
}

export function queryTrendData (params) {
  return request({
    url: '/newBusinessHosAnalysisController/queryTrendData',
    method: 'post',
    params: params
  })
}
// 查询dip象限图数据
export function queryQuadrantDipData (params) {
  return request({
    url: '/newDrgBusinessHosAnalysisController/queryQuadrantDipData',
    method: 'post',
    params: params
  })
}

// 查询dip象限图医生数据
export function queryQuadrantDipDoctorData (params) {
  return request({
    url: '/newDrgBusinessHosAnalysisController/queryQuadrantDipDoctorData',
    method: 'post',
    params: params
  })
}
