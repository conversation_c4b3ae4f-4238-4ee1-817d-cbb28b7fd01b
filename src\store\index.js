import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import user from './modules/user'
import menu from './modules/menu'
import iframe from './modules/iframe'
import tagsView from './modules/tagsView'
import dataAuth from './modules/dataAuth'
import common from './modules/common'
import cache from './modules/cache'
import getters from './getters'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    user,
    menu,
    iframe,
    tagsView,
    dataAuth,
    common,
    cache
  },
  getters
})

export default store
