<template>
  <div style="width: 100%; height: 80%;display: flex;">
    <div style="width: 100%;height: 100%;display: flex;">
      <div style="width: 20%">
        <drg-title-line title="选择系统默认支付方式" />
        <el-radio-group v-model="group" @change="changeGroup">
          <el-radio label="DIP" border>DIP</el-radio>
          <el-radio label="DRG" border>DRG</el-radio>
        </el-radio-group>

        <div class="drg-upload som-flex-center">
          <el-upload
            v-if="group == 'DRG'"
            style="text-align: center"
            drag
            ref="upload"
            :limit="1"
            action="customize"
            accept=".xlsx,.xls"
            :http-request="uploadDrgInfo">
            <i class="el-icon-upload"></i>
            <div class="el-upload__tip" slot="tip">
              <span style="color: #0a84ff;cursor: pointer" @click="tpdDrgsDownload">点击下载模板</span>
            </div>
          </el-upload>
        </div>
      </div>

      <div style="width: 25%;height: 100%;overflow: auto">
        <drg-title-line title="选择对应角色权限" />
        <div style="display: flex;align-items: center;margin-bottom: 1.5%;"
             v-for="(item, index) in authData" :key=" item.value + index">
          <div style="margin-right: 5%;width: 20%;text-align: right">
            {{ item.description }}
          </div>
          <el-select style="margin-right: 2%" v-model="item.value" placeholder="请选择" @change="(val) => changeAuth(val, item.id)">
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <i class="el-icon-check" v-show="item.modify" style="color: green"></i>
        </div>
      </div>

      <div style="width: 20%;height: 100%;overflow: auto">
        <drg-title-line title="确认高低倍率区间" />
        <el-form :model="rateForm" ref="rateForm" label-width="100px" class="demo-ruleForm">
          <el-form-item :label="item.label" :prop="item.prop" v-for="(item, index) in rateData" :key="index">
            <el-input v-model="item.value" @input="inputChange($event,item)">
              <i slot="suffix" class="el-icon-check" v-show="item.modify" style="color: green"></i>
            </el-input>
          </el-form-item>
        </el-form>
      </div>

      <div style="width: 24%;margin-left: 2%;height: 100%;overflow: auto">
        <drg-title-line title="分组器配置" />
        <el-form :model="bursterForm" ref="bursterForm" label-width="100px" class="demo-ruleForm">
          <el-form-item :label="item.label" :prop="item.prop" v-for="(item, index) in bursterData" :key="index">
            <el-input v-model="item.value" @input="inputChange($event,item)">
              <i slot="suffix" class="el-icon-check" v-show="item.modify" style="color: green"></i>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 按钮 -->
    <div style="position: absolute; bottom: 5%;right: 2%">
      <el-button type="primary" @click="prevStep">上一步</el-button>
      <el-button type="primary" :disabled="nextDisabled" @click="next">{{ nextText }}</el-button>
    </div>
  </div>
</template>
<script>
import { queryData } from '@/api/dataConfig/commonConfig'
import { findAll } from '@/api/role'
import { downloadDrgData, modifySysConfig, uploadDrgData } from '@/api/hospitalEnvGenerate'
import { queryData as bursterQuery, update as updateBurster } from '@/api/dataConfig/bursterConfig'
import { queryGroupType } from '@/api/common/sysCommon'
import store from '../../../store'

export default {
  name: 'hospitalEnvGenerateStep3',
  props: {
    step: {
      type: Number
    },
    hospitalInfo: {
      type: Object,
      default: () => {}
    }
  },
  mounted () {

  },
  data: () => ({
    nextText: '下一步',
    nextDisabled: false,
    modifyFlag: false,
    group: '',
    copyGroup: '',
    authData: [],
    roleOptions: [],
    rateForm: {
      downRate: '',
      upRate: '',
      scoreRange: '',
      rateRange: '',
      symbol: ''
    },
    rateData: [],
    bursterForm: {},
    bursterData: []
  }),
  methods: {
    // 初始化
    init () {
      if (this.$somms.hasHosRole()) {
        this.$message({
          type: 'warning',
          message: '检测到当前为医院账号，跳过步骤【修改系统配置】'
        })
        this.success()
        return
      }

      this.roleOptions = []
      this.authData = []
      this.rateData = []
      this.bursterData = []
      this.group = ''
      queryData({}).then(res => {
        // auth处理
        findAll({}).then(roleRes => {
          roleRes.data.map(role => {
            this.roleOptions.push({
              label: role.memo_info,
              value: '' + role.id
            })
          })

          let tempData = this.getDataByType(res.data, 'AUTH')
          tempData.map(t => {
            t.copyValue = t.value
            t.modify = false
            this.authData.push(t)
          })
        })

        // 系统默认类型处理
        let group = this.getDataByType(res.data, 'FZLX')
        if (group.length > 0) {
          this.group = group[0].value
          this.copyGroup = group[0]
        }

        // 倍率区间范围
        let tempRangeData = this.getDataByType(res.data, 'RATE_SECTION')
        if (tempRangeData.length > 0) {
          tempRangeData.map(t => this.rateData.push({
            id: t.id,
            label: t.description,
            prop: t.key,
            value: t.value,
            copyValue: t.value,
            modify: false
          }))
        }
      })

      // 分组器
      bursterQuery({}).then(res => {
        res.data.map(d => {
          // let url = d.url
          // let start = url.indexOf("//") + 2
          // let end = url.lastIndexOf(":")
          // let ip = url.substring(start, end)
          // let suffix = url.substring(url.lastIndexOf("/") + 1)
          this.bursterData.push({
            id: d.id,
            label: d.ver,
            value: d.url,
            copyValue: d.url,
            modify: false
          })
        })
      })
    },
    // 通过类型获取数据
    getDataByType (data, type) {
      let result = []
      if (data && data.length > 0) {
        data.map(d => {
          if (d.type == type) {
            result.push(d)
          }
        })
      }
      return result
    },
    // 改变权限
    changeAuth (val, id) {
      let flag = false
      this.authData.map(auth => {
        if (auth.id == id) {
          auth.value = val
          auth.modify = auth.value != auth.copyValue
        }

        if (auth.modify) {
          flag = true
        }
      })

      this.modifyStatus(flag)
    },
    // 改变组
    changeGroup (val) {
      this.modifyStatus(val != this.copyGroup.value)
    },
    // 修改状态
    modifyStatus (flag) {
      if (flag) {
        this.modifyFlag = true
      } else {
        this.modifyFlag = false
      }
    },
    // 输入
    inputChange (val, item) {
      let flag = false
      if (item.value != item.copyValue) {
        flag = true
      } else {
        flag = false
      }
      item.modify = flag
      this.modifyStatus(flag)
    },
    // 上传DRG信息
    uploadDrgInfo (data) {
      let params = new FormData()
      params.append('file', data.file)
      params.append('group_type', '3')
      // TODO 上传功能
      uploadDrgData(params).then(result => {
        if (result.code == 200) {
          this.$message.success('上传成功')
          this.$refs.upload.clearFiles()
        }
      })
    },
    tpdDrgsDownload () {
      downloadDrgData().then(result => {
        this.$somms.download(result, 'drgs表信息模板', 'application/vnd.ms-excel')
      })
    },
    // 上一步
    prevStep () {
      this.$emit('prevStep', null)
    },
    // 下一步
    async next () {
      if (this.modifyFlag) {
        let configModifyData = []
        let initFlag = false
        let groupModifyFlag = false
        // 判断分组是否改变
        if (this.group != this.copyGroup.value) {
          configModifyData.push({
            id: this.copyGroup.id,
            value: this.group
          })
          groupModifyFlag = true
        }

        // 判断权限修改
        this.authData.map(auth => {
          if (auth.modify) {
            configModifyData.push({
              id: auth.id,
              value: auth.value
            })
          }
        })

        // 判断高低倍率
        this.rateData.map(rate => {
          if (rate.modify) {
            configModifyData.push({
              id: rate.id,
              value: rate.value
            })
          }
        })

        // 系统配置
        if (configModifyData.length > 0) {
          await modifySysConfig({ configs: configModifyData }).then(res => {
            this.modifyFlag = false
            initFlag = true
          })
        }

        // 分组器
        let bursterModifyData = []

        this.bursterData.map(burster => {
          if (burster.modify) {
            bursterModifyData.push({
              configKey: burster.label,
              configValue: burster.value
            })
          }
        })

        if (bursterModifyData.length > 0) {
          for (const burster of bursterModifyData) {
            const index = bursterModifyData.indexOf(burster)
            await updateBurster(burster).then(res => {
              if (index == bursterModifyData.length - 1) {
                this.modifyFlag = false
                initFlag = true
              }
            })
          }
        }

        if (initFlag) {
          this.$message({
            type: 'success',
            message: '修改成功'
          })

          // 重新设置分组类型到vuex，以便后面使用
          if (groupModifyFlag) {
            queryGroupType({ configKey: 'FZLX+FZLX' }).then(res => {
              if (res.data) {
                store.commit('setFzlx', res.data.value)
              }
            })
          }
          this.init()
        }
      } else {
        this.success()
      }
    },
    success () {
      this.$emit('success', { msg: '选择完成' })
    }
  },
  watch: {
    step: {
      immediate: true,
      handler: function (val) {
        if (val && val == 2) {
          this.init()
        }
      }
    },
    modifyFlag (val) {
      if (val) {
        this.nextText = '修改'
      } else {
        this.nextText = '下一步'
      }
    }
  }
}
</script>
<style scoped lang="scss">
.switch {
  width: 100%;
  height: 60%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  &> .el-switch{
    height: 20%;
  }
}

/deep/ .el-upload-dragger{
  width: 100px;
  height: 100px;
}
.drg-upload{
  margin-top: 2%;
  height: 80%;
  width: 70%;
}
</style>
