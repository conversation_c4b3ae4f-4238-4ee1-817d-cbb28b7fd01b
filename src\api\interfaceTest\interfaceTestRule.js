import request from '@/utils/request'

/**
 * 查询数据
 * @param params
 * @returns {*}
 */
export function queryData (params) {
  return request({
    url: '/interfaceTestRuleController/queryData',
    method: 'post',
    data: params
  })
}

/**
 * 更新规则
 * @param params
 * @returns {*}
 */
export function updateRule (params) {
  return request({
    url: '/interfaceTestRuleController/updateRule',
    method: 'post',
    data: params
  })
}

/**
 * 新增规则
 * @param params
 * @returns {*}
 */
export function addRule (params) {
  return request({
    url: '/interfaceTestRuleController/addRule',
    method: 'post',
    data: params
  })
}

export function queryLog (params) {
  return request({
    url: '/interfaceTestRuleController/queryLog',
    method: 'post',
    data: params
  })
}

/**
 * 查询校验日志
 * @param params
 * @returns {*}
 */
export function queryValLog (params) {
  return request({
    url: '/interfaceTestRuleController/queryValLog',
    method: 'post',
    data: params
  })
}

/**
 * 刷新配置
 * @param params
 * @returns {*}
 */
export function refreshConfig (params) {
  return request({
    url: '/interfaceTestRuleController/refreshConfig',
    method: 'post',
    data: params
  })
}
