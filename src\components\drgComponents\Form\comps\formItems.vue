<template>
  <div style="width: 100%;height: 28px">

    <!-- 医院科室 -->
    <drg-department v-model="form.deptCode" class="item-width" v-if="item.prop == 'deptCode'" @changeDept="compChange"/>
    <!-- DIP组 -->
    <drg-group v-model="form.dipCodg" type="DIP" class="item-width" v-if="item.prop == 'dipCode'||item.prop == 'dipCodg'" @changeGroup="compChange"/>

    <!-- DRG组 -->
    <drg-group v-model="form.drgCodg" type="DRG" class="item-width" v-if="item.prop == 'drgCode'||item.prop == 'drgCodg'" @changeGroup="compChange"/>

    <!-- cdG组 -->
    <drg-group v-model="form.cdCodg" type="CD" class="item-width" v-if="item.prop == 'cdCode'||item.prop == 'cdCodg'" @changeGroup="compChange"/>

    <!-- 病案号 -->
    <el-input v-model="form.medcasCodg" class="item-width" v-if="item.prop == 'medcasCodg'" placeholder="请输入病案号" :clearable="item.clearable"/>

    <!-- 扩展 -->
    <form-extend :dom="item.dom" v-if="item.dom && item.dom != undefined"/>

    <!-- 期号 -->
    <el-date-picker
      v-model="form.ym"
      v-if="item.prop == 'ym'"
      type="month"
      value-format="yyyyMM"
      format="yyyyMM"
      class="item-width"
      placeholder="选择期号">
    </el-date-picker>

    <!-- 出院时间/ 时间范围 -->
    <el-date-picker
      v-model="form.dateRange"
      v-if="item.prop == 'dateRange'"
      type="daterange"
      class="item-width"
      unlink-panels
      :disabled="item.disabled"
      range-separator="-"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      value-format="yyyy-MM-dd"
      :clearable="item.clearable"
      @change="changeDateRange($event, 'out')"
      :picker-options="pickerOptions">
    </el-date-picker>

    <!-- 入院时间/ 时间范围 -->
    <el-date-picker
      v-model="form.inDateRange"
      v-if="item.prop == 'inDateRange'"
      type="daterange"
      class="item-width"
      unlink-panels
      :disabled="item.disabled"
      range-separator="-"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      value-format="yyyy-MM-dd"
      :clearable="item.clearable"
      @change="changeDateRange($event, 'in')"
      :picker-options="pickerOptions">
    </el-date-picker>

    <!-- 结算时间/ 时间范围 -->
    <el-date-picker
      v-model="form.seDateRange"
      v-if="item.prop == 'seDateRange'"
      type="daterange"
      class="item-width"
      unlink-panels
      :disabled="item.disabled"
      range-separator="-"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      value-format="yyyy-MM-dd"
      :clearable="item.clearable"
      @change="changeDateRange($event, 'se')"
      :picker-options="pickerOptions">
    </el-date-picker>

  </div>
</template>
<script>
export default {
  name: 'formItems',
  props: {
    item: Object,
    form: Object,
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    pickerOptions: {
      shortcuts: [{
        text: '当月',
        onClick (picker) {
          const end = new Date()
          const start = new Date()
          start.setDate(1)
          picker.$emit('pick', [start, end])
        }
      }, {
        text: '上月',
        onClick (picker) {
          let end = new Date()
          let start = new Date()
          let lastMonth = start.getMonth() - 1
          start = new Date(start.getFullYear(), lastMonth, 1)
          end = new Date(end.getFullYear(), lastMonth, new Date(start.getFullYear(), end.getMonth(), 0).getDate())
          picker.$emit('pick', [start, end])
        }
      }, {
        text: '当年',
        onClick (picker) {
          const end = new Date()
          const start = new Date()
          start.setDate(1)
          start.setMonth(0)
          end.setFullYear(end.getFullYear() + 1)
          end.setDate(31)
          end.setMonth(-1)
          picker.$emit('pick', [start, end])
        }
      }
      //   {
      //   text: '测试',
      //   onClick(picker) {
      //     const end = new Date();
      //     const start = new Date();
      //     start.setFullYear(2021)
      //     start.setDate(1)
      //     start.setMonth(11)
      //     end.setFullYear(2021);
      //     end.setDate(0);
      //     end.setMonth(11);
      //     picker.$emit('pick', [start, end]);
      //   }
      // }
      ]
    }
  }),
  mounted () {
  },
  methods: {
    changeDateRange (val, type) {
      if (type == 'out') {
        if (val) {
          this.form.begnDate = val[0]
          this.form.expiDate = val[1]
        } else {
          this.form.begnDate = null
          this.form.expiDate = null
        }
      } else if (type === 'in') {
        if (val) {
          this.form.inStartTime = val[0]
          this.form.inEndTime = val[1]
        } else {
          this.form.inStartTime = null
          this.form.inEndTime = null
        }
      } else {
        if (val) {
          this.form.seStartTime = val[0]
          this.form.seEndTime = val[1]
        } else {
          this.form.seStartTime = null
          this.form.seEndTime = null
        }
      }
      this.compChange()
    },
    compChange () {
      this.$emit('change', null)
    }
  }
}
</script>
<style scoped>
.item-width{
  width: 100%;
  height: 100%;
}
</style>
