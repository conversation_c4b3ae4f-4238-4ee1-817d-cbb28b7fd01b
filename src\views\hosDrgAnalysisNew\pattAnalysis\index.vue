<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="somForm"
             show-date-range
             show-in-date-range
             show-se-date-range
             :show-hos-dept="{show:this.$somms.hasHosRole()}"
             :initTimeValueNotQuery="false"
             headerTitle="查询条件"
             :container="true"
             show-pagination
             :select-options="[...doctorList]"
             :totalNum="total"
              :showCoustemContentTitle="true"
             :exportExcel="{ 'tableId': tableId, exportName: exportTableName}"
             :exportExcelFun="exportExcelFun"
             :exportExcelHasChild="true"
             @query="radioChange(radioMode)"
             @reset="reset"
             show-drg>

      <template slot="extendFormItems">
        <el-form-item label="病案号" >
          <el-input v-model="queryForm.bah" placeholder="请输入病案号" clearable />
        </el-form-item>
        <el-form-item label="费用区间" v-if="radioMode == 2">
          <drg-dict-select v-model="queryForm.costSection" placeholder="请选择费用区间" dicType="CASE_TYPE" />
        </el-form-item>
        <el-form-item label="费用状态" v-if="radioMode == 2">
          <el-select v-model="queryForm.isLoss" placeholder="请选择费用状态" class="som-form-extend-form-item" >
            <el-option v-for="item in isBooleanCost"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否入组" >
          <el-select v-model="queryForm.isInGroup" clearable placeholder="请选择" @change="radioChange(radioMode)" class="som-form-extend-form-item">
            <el-option v-for="item in isBoolean"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="未入组原因" >
          <!--          <el-select v-model="queryForm.notGroupReason"-->
          <!--                     clearable-->
          <!--                     multiple-->
          <!--                     collapse-tags-->
          <!--                     placeholder="请选择"-->
          <!--                     class="som-form-item"-->
          <!--                     @change="radioChange(radioMode)">-->
          <!--            <el-option v-for="item in errorReason"-->
          <!--                       :key="item.value"-->
          <!--                       :label="item.label"-->
          <!--                       :value="item.value"></el-option>-->
          <!--          </el-select>-->
          <drg-dict-select multiple v-model="queryForm.notGroupReason" placeholder="请选择" dicType="GROUP_ERROR_MSG_DRG" />
        </el-form-item>
        <el-form-item label="住院医师" >
          <el-select v-model="queryForm.drCodg"
                     clearable
                     filterable
                     placeholder="请选择"
                     class="som-form-extend-form-item"
                     @change="radioChange(radioMode)">
            <el-option v-for="item in doctorList"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="结算ID" >
          <el-input v-model="queryForm.settlementId" placeholder="请输入结算ID" clearable />
        </el-form-item>
        <el-form-item label="是否异地" >
          <drg-dict-select v-model="queryForm.isRemote" placeholder="请选择异地区域" dicType="INSUPLCADMDVS_TYPE"
                           />

        </el-form-item>
      </template>

      <!-- profttl -->
      <template slot="contentTitle">
        <drg-title-line :title="profttl">
          <template slot="rightSide">
            <div style="display: flex">
              <div>
                <el-radio-group v-model="radioMode" @change="radioChange">
                  <el-radio-button :label="1">基本信息</el-radio-button>
                  <el-radio-button :label="2">费用信息</el-radio-button>
                  <el-radio-button :label="3">预测</el-radio-button>
                  <!--                  <el-radio-button :label="4">对比</el-radio-button>-->
                </el-radio-group>
              </div>
            </div>
          </template>
          <template slot="titleRight">
            <!-- 固定列 -->
            <el-select v-model="columnVal"
                       multiple
                       collapse-tags
                       :multiple-limit="3"
                       placeholder="请选择固定列">
              <el-option
                v-for="item in columnOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </template>
        </drg-title-line>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <div class="content-wrapper">

          <!-- 左侧 -->
          <div class="content-wrapper-left" v-if="!analysis">

            <!-- 基本信息table -->
            <basic-info-table :data="tableData"
                              ref="dataTable"
                              :loading="loading"
                              :fixed-columns="columnVal"
                              :id="baseInfoId"
                              v-if="radioMode == 1"
                              @setRefObj="(obj) => this.tableObj = obj" />

            <!-- 预测table -->
            <forecast-table :data="tableData"
                            ref="dataTable"
                            :loading="loading"
                            :fixed-columns="columnVal"
                            :id="forecastId"
                            v-if="radioMode == 3"
                            @setRefObj="(obj) => this.tableObj = obj" />
          </div>

          <!--  右侧 -->
          <div class="content-wrapper-right" v-if="!analysis">

            <div class="content-wrapper-right-top">
              <div style="display: flex;">
                <!-- 排序字段选择 -->
                <div style="width: 60%;padding-right: 2%">
                  <el-select v-model="selectVal" placeholder="请选择" @change="selectChange">
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>

                <!-- top -->
                <div style="width: 40%">
                  <el-select v-model="topVal" placeholder="请选择" @change="generateChartData">
                    <el-option
                      v-for="item in topOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>
              </div>

              <!-- 排序 -->
              <div class="content-wrapper-right-icon" @click="sortClick">
                <i class="el-icon-sort"></i>
              </div>
            </div>

            <div style="height: 93%;width: 100%;position: absolute;top: 7%">
              <!-- 图 -->
              <drg-echarts :options="chartOption" ref="chart"/>
            </div>
          </div>

          <div class="content-wrapper-analysis" v-if="analysis">
            <!-- 费用信息table-->
            <cost-info-table :data="tableData"
                             ref="dataTable"
                             :loading="loading"
                             :fixed-columns="columnVal"
                             :id="costInfoId"
                             v-if="radioMode == 2"
                             :queryForm="queryForm"
                             @setRefObj="(obj) => this.tableObj = obj" />
          </div>

        </div>
      </template>
    </drg-form>
  </div>
</template>

<script>
import basicInfoTable from './comps/newDrgPatientBasicInfoTable'
import costInfoTable from './comps/newDrgPatientCostInfoTable'
import forecastTable from './comps/newDrgPatientForecastTable'

import {
  queryDrgPatientBasicInfoData,
  queryDrgPatientCostInfoData,
  queryDrgPatientForecastData } from '../../../api/newBusiness/newBusinessPatient'
import { queryDoctorDropDown, updateSwitchState,
  selectPatientContrastData, queryDiseaseDropdown
} from '@/api/newBusiness/newBusinessCommon'
import { formaterDict } from '@/utils/dict'
let basicInfoOptions = [
  { value: 'inHosDays', label: '住院天数' },
  { value: 'inHosTotalCost', label: '住院总费用' },
  { value: 'medicalCostRate', label: '药占比' },
  { value: 'materialCostRate', label: '耗占比' }
]

let costInfoOptions = [
  { value: 'inHosTotalCost', label: '住院总费用' },
  { value: 'zhylfwf', label: '综合医疗服务费' },
  { value: 'zdf', label: '诊断费' },
  { value: 'zlf', label: '治疗费' },
  { value: 'kff', label: '康复费' },
  { value: 'zyf', label: '中医费' },
  { value: 'xyf', label: '西药费' },
  { value: 'zyf1', label: '中药费' },
  { value: 'xyhxyzpf', label: '血液和血液制品费' },
  { value: 'hcf', label: '耗材费' },
  { value: 'qtf', label: '其他费' }
]

let forecastOptions = [
  { value: 'inHosTotalCost', label: '住院总费用' },
  { value: 'forecastAmountDiff', label: '预测金额差异' },
  { value: 'oeVal', label: 'O/E值' }
]

export default {
  name: 'pattAnalysis',
  components: {
    basicInfoTable,
    costInfoTable,
    forecastTable
  },
  data: () => ({
    queryForm: {
      isInGroup: '',
      drCodg: '',
      notGroupReason: [],
      feeStas: '0',
      settlementId: '',
      isRemote: ''
    },
    radioMode: 1,
    chartOption: {},
    tableData: [],
    total: 0,
    loading: false,
    yAxisData: [],
    seriesData: [],
    selectVal: '',
    options: basicInfoOptions,
    topVal: 10,
    topOptions: [
      { value: 10, label: 'TPP10' },
      { value: 20, label: 'TPP20' },
      { value: 30, label: 'TPP30' },
      { value: 10 ** 4, label: 'ALL' }
    ],
    columnVal: [],
    columnOptions: [],
    tableObj: {},
    baseInfoId: 'baseInfoId',
    costInfoId: 'costInfoId',
    forecastId: 'forecastId',

    exportTableName: '',
    tableId: '',
    exportExcelFun: null,
    sort: true, // true：倒序 false：正序
    profttl: '患者指标分析',

    analysis: false,

    isBoolean: [
      { value: 0, label: '否' },
      { value: 1, label: '是' }
    ],
    isBooleanCost: [
      { value: 0, label: '盈利' },
      { value: 1, label: '亏损' }
    ],
    errorReason: [
      { value: 'error1', label: '字段类型错误' },
      { value: 'error2', label: '主要诊断不能为空' },
      { value: 'error3', label: '该诊断不能为主要诊断' },
      { value: 'error4', label: '性别填写错误' },
      { value: 'error5', label: '总费用小于5元' },
      { value: 'error6', label: '住院天数大于60天或者小于0' },
      { value: 'error7', label: '无分组方案' },
      { value: 'error8', label: '主要诊断编码不规范' }
    ],
    doctorList: [],

    tempList: [],
    tempVal: this.radioMode
  }),
  mounted () {
    this.queryForm.feeStas = String(this.$store.getters.feeStas)
    if (Object.keys(this.$route.query.length > 0)) {
      if (this.$route.query.isInGroup) {
        this.queryForm.isInGroup = Number(this.$route.query.isInGroup)
      }
      if (this.$route.query.isInGroup1 === 'not') {
        this.queryForm.isInGroup = 0
      } else if (this.$route.query.isInGroup1 === 'in') {
        this.queryForm.isInGroup = 1
      }
      if (this.$route.query.drgCodg) {
        this.queryForm.drgCodg = this.$route.query.drgCodg
      }
      if (this.$route.query.deptCode) {
        this.queryForm.deptCode = this.$route.query.deptCode
      }

      if (this.$route.query.drCodg) {
        this.queryForm.drCodg = this.$route.query.drCodg
      }
      if (this.$route.query.grpFlag) {
        this.queryForm.grpFlag = this.$route.query.grpFlag
      }
      if (this.$route.query.notGroupReason) {
        this.queryForm.notGroupReason = this.$route.query.notGroupReason
      }
      if (this.$route.query.bah) {
        this.queryForm.bah = this.$route.query.bah
      }
      if (this.$route.query.patientIds) {
        this.queryForm.patientIds = this.$route.query.patientIds
      }
      if (this.$route.query.feeStas) {
        this.queryForm.feeStas = this.$route.query.feeStas
      }
      if (this.$route.query.icdCodg) {
        this.queryForm.icdCodg = this.$route.query.icdCodg
      }
      if (this.$route.query.begnDate && this.$route.query.expiDate) {
        this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.queryForm)
      }

      if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
        this.$refs.somForm.jumpTimeChange('in', this.$route.query, this.queryForm)
      }

      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.queryForm)
      }
    }
    this.$nextTick(() => {
      this.init()
      this.queryDoctorSelect()
    })
  },
  methods: {
    queryDrgPatientBasicInfoData,
    queryDrgPatientCostInfoData,
    queryDrgPatientForecastData,
    init () {
      this.radioChange(this.radioMode)
    },
    changeSwitch (val) {
      this.$store.commit('SET_SWITCHSTATE', val)
      this.queryForm.feeStas = this.$store.getters.feeStas
      let params = {}
      Object.assign(params, this.queryForm)
      params.username = this.$store.getters.name
      updateSwitchState(params).then((res) => {
        if (res.code == 200) {
          this.init()
        }
      })
    },
    // 查询基本信息数据
    queryPageBasicInfoData () {
      this.loading = true
      queryDrgPatientBasicInfoData(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
        this.generateChartData()
        this.generateFixedColumns()
      })
      // this.clearRouteQuery()
    },
    queryPageCostInfoData () {
      this.loading = true
      queryDrgPatientCostInfoData(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
        this.generateChartData()
        this.generateFixedColumns()
      })
      this.clearRouteQuery()
    },
    queryPageForecastData () {
      this.loading = true
      queryDrgPatientForecastData(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
        this.generateChartData()
        this.generateFixedColumns()
      })
      this.clearRouteQuery()
    },
    // 生成图数据
    generateChartData () {
      let params = this.getParams()
      Object.assign(params, {
        pageNum: 1,
        pageSize: 10 ** 4
      })
      if (this.radioMode == 1) {
        queryDrgPatientBasicInfoData(params).then(res => {
          this.tempList = res.data.list
          this.yAxisData = []
          this.seriesData = []
          let sortData = []
          for (let i = 0; i < this.tempList.length; i++) {
            let item = this.tempList[i]
            sortData.push({
              y: item.name,
              value: item[this.selectVal]
            })
          }

          if (this.sort) {
            sortData = sortData.sort((a, b) => a.value - b.value)
          } else {
            sortData = sortData.sort((a, b) => b.value - a.value)
          }

          for (let i = 0; i < sortData.length; i++) {
            if (i == this.topVal) {
              break
            }
            this.yAxisData.push(sortData[i].y)
            this.seriesData.push(sortData[i].value)
          }
          this.yAxisData.reverse()
          this.seriesData.reverse()
          this.initChart()
        })
      }
      if (this.radioMode == 3) {
        queryDrgPatientForecastData(params).then(res => {
          this.tempList = res.data.list
          this.yAxisData = []
          this.seriesData = []
          let sortData = []
          for (let i = 0; i < this.tempList.length; i++) {
            let item = this.tempList[i]
            sortData.push({
              y: item.name,
              value: item[this.selectVal]
            })
          }

          if (this.sort) {
            sortData = sortData.sort((a, b) => a.value - b.value)
          } else {
            sortData = sortData.sort((a, b) => b.value - a.value)
          }

          for (let i = 0; i < sortData.length; i++) {
            if (i == this.topVal) {
              break
            }
            this.yAxisData.push(sortData[i].y)
            this.seriesData.push(sortData[i].value)
          }
          this.yAxisData.reverse()
          this.seriesData.reverse()
          this.initChart()
        })
      }
    },
    // 生成固定列
    generateFixedColumns () {
      this.columnOptions = []
      let children = this.$refs.dataTable.$children[0].$children
      if (children) {
        children.forEach(item => {
          if (item.$options.propsData.label &&
            item.$options.propsData.label != '序号' &&
            item.$options.propsData.label != '综合医疗服务费' &&
            item.$options.propsData.label != '诊断费' &&
            item.$options.propsData.label != '治疗费' &&
            item.$options.propsData.label != '中药费' &&
            item.$options.propsData.label != '血液和血液制品费' &&
            item.$options.propsData.label != '耗材费') {
            this.columnOptions.push({
              value: item.$options.propsData.prop,
              label: item.$options.propsData.label
            })
          }
        })
      }
    },
    dictFormatter (col, row, dic_type) {
      return formaterDict(row[col.property], this.dictVoList[dic_type])
    },
    // 初始化图
    initChart () {
      this.chartOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        dataZoom: [
          {
            type: 'inside',
            yAxisIndex: [0],
            orient: 'vertical'
          },
          {
            type: 'slider',
            yAxisIndex: [0],
            orient: 'vertical'
          }
        ],
        xAxis: {
          type: 'value',
          axisLabel: {
            interval: 0,
            rotate: 40,
            formatter: params => {
              return this.formatCost(params, 0)
            }
          },
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: this.yAxisData
        },
        series: [
          {
            name: '数值',
            type: 'bar',
            label: {
              formatter: params => {
                return this.formatCost(params.value)
              }
            },
            data: this.seriesData.map((item, index) => {
              return {
                value: item,
                label: {
                  show: true,
                  position: item > 0 ? 'right' : 'left'
                }
              }
            })
          }
        ]
      }
    },
    // 格式化
    formatCost (val, fixed = 2) {
      let prefix = ''
      let resVal = val
      if (String(val).includes('-')) {
        prefix = '-'
        val = Math.abs(val)
        resVal = val
      }
      if (Math.abs((parseFloat(val) / 10000)) > 1) {
        resVal = (parseFloat(val) / 10000).toFixed(fixed) + '/万'
      }
      return prefix + resVal
    },
    // 选择改变
    radioChange (val) {
      if (this.tempVal != val) {
        this.selectVal = ''
        this.tempVal = val
      }
      if (val == 1) {
        this.queryPageBasicInfoData()
        this.exportExcelFun = queryDrgPatientBasicInfoData
        this.tableId = this.baseInfoId
        this.exportTableName = '患者基本信息' + '(' + this.getParams().begnDate + '~' + this.getParams().expiDate + ')'
        this.options = basicInfoOptions
        if (this.selectVal == '') {
          this.selectVal = 'inHosDays'
        }
        this.profttl = '患者基本信息'
        this.analysis = false
      }
      if (val == 2) {
        this.queryPageCostInfoData()
        this.exportExcelFun = queryDrgPatientCostInfoData
        this.tableId = this.costInfoId
        this.exportTableName = '患者费用信息' + '(' + this.getParams().begnDate + '~' + this.getParams().expiDate + ')'
        this.options = costInfoOptions
        if (this.selectVal == '') {
          this.selectVal = 'inHosTotalCost'
        }
        this.profttl = '患者费用信息'
        this.analysis = true
      }
      if (val == 3) {
        this.queryPageForecastData()
        this.exportExcelFun = queryDrgPatientForecastData
        this.tableId = this.forecastId
        this.exportTableName = '患者预测分析' + '(' + this.getParams().begnDate + '~' + this.getParams().expiDate + ')'
        this.options = forecastOptions
        if (this.selectVal == '') {
          this.selectVal = 'inHosTotalCost'
        }
        this.profttl = '患者预测分析'
        this.analysis = false
      }
    },
    // 图排序改变
    selectChange () {
      this.generateChartData()
    },
    // 点击排序
    sortClick () {
      this.sort = !this.sort
      this.generateChartData()
    },
    // 获取参数
    getParams () {
      let params = {}
      this.queryForm.feeStas = this.$store.getters.feeStas
      Object.assign(params, this.queryForm)
      return params
    },
    // 重置
    reset () {
      this.queryForm.drgCodg = ''
      this.queryForm.icdCodg = ''
      this.radioChange(this.selectVal)
    },
    clearRouteQuery () {
      if (this.$route.query) {
        this.$router.push({ query: {
        } }).catch(() => {})
      }
    },
    queryDoctorSelect () {
      queryDoctorDropDown().then(res => {
        this.doctorList = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content-wrapper{
  height: 100%;
  width: 100%;
  display: flex;

  &-left{
    width: 80%;
    height: 100%;
    padding-right: 16px;
    box-sizing: border-box;
    position: relative;

    &-fixed-column{
      position: absolute;
      left: 10%;
      top: -4.5%;
    }

    &-analysis{
      width: 100%;
      height: 100%;
    }
  }

  &-right{
    width: 20%;
    height: 100%;
    position: relative;

    &-top{
      height: 10%;
      width: 100%;
      position: absolute;
      top: 0%
    }

    &-icon{
      z-index: 2;
      font-size: 18px;
      width: 20px;
      height: 20px;
      cursor: pointer;
      position: absolute;
      right: 0;
      bottom: 0;
    }
  }

  &-analysis{
    width: 100%;
    height: 100%;
    position: relative;

    &-chart-table{
      position: absolute;
      top: -4.5%;
      right: 0;
    }
  }
}

/deep/ .pagination-container{
  right: 21%;
}
/deep/ .util{
  right: 21%;
  top: -0.25rem;
}
/deep/ .content-wrapper-right-top>.el-select{
  width: 84px;
}
</style>
