<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-hos-dept
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="DIP入组情况分析"
             :container="true"
             :initTimeValueNotQuery="false"
             @query="handleSearchList" @reset="handleResetSearch" ref="listQuery">

      <template slot="buttons">
        <el-popconfirm
          confirm-button-text='确定'
          cancel-button-text='导出全部'
          icon="el-icon-info"
          icon-color="red"
          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>

      <template slot="containerContent">
        <div style="height:4rem">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="6" style="height: 100%">
              <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                <div style="height:30%;width:100%;padding-left:5px;margin-top:5px;font-size: 12px;font-weight: bold;">全院病案总数</div>
                <div style="height:70%;width:100%;padding-left:10px;font-size: 12px;font-weight: bold;display: flex;">
                  <div style="width:40%;height:100%;display: flex;">
                    <div v-if="medicalRecordNum>=10000" class="number">
                      {{(medicalRecordNum/10000).toFixed(2)}}万
                    </div>
                    <div v-else class="number">
                      {{medicalRecordNum}}
                      <span class="fen">例</span>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同期:
                        <span class="compareNum">{{lastYearMedicalRecordNum}}</span>
                      </div>
                      <div class="compare2">
                        上期:
                        <span class="compareNum">{{lastMonthMedicalRecordNum}}</span>
                      </div>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同比:
                        <span class="compareRateIncrease" v-if="medicalRecordNumYOY>0">
                      {{medicalRecordNumYOY}}%↑
                    </span>
                        <span class="compareRateEqual" v-if="medicalRecordNumYOY==0">
                      0
                    </span>
                        <span class="compareRateDecrease" v-if="medicalRecordNumYOY<0">
                      {{Math.abs(medicalRecordNumYOY)}}%↓
                    </span>
                      </div>
                      <div class="compare2">
                        环比:
                        <span class="compareRateIncrease" v-if="medicalRecordNumRingRatio>0">
                      {{medicalRecordNumRingRatio}}%↑
                    </span>
                        <span class="compareRateEqual" v-if="medicalRecordNumRingRatio==0">
                      0
                    </span>
                        <span class="compareRateIncrease" v-if="medicalRecordNumRingRatio<0">
                      {{Math.abs(medicalRecordNumRingRatio)}}%↓
                    </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="6" style="height: 100%">
              <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                <div style="height:30%;width:100%;padding-left:5px;margin-top:5px;font-size: 12px;font-weight: bold;">全院DIP组数</div>
                <div style="height:70%;width:100%;padding-left:10px;font-size: 12px;font-weight: bold;display: flex;">
                  <div style="width:40%;height:100%;display: flex;">
                    <div v-if="dipNum>=10000" class="number">
                      {{(dipNum/10000).toFixed(2)}}万
                    </div>
                    <div v-else class="number">
                      {{dipNum}}
                      <span class="fen">组</span>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同期:
                        <span class="compareNum">{{lastYearDipNum}}</span>
                      </div>
                      <div class="compare2">
                        上期:
                        <span class="compareNum">{{lastMonthDipNum}}</span>
                      </div>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同比:
                        <span class="compareRateIncrease" v-if="dipNumYOY>0">
                      {{dipNumYOY}}%↑
                    </span>
                        <span class="compareRateEqual" v-if="dipNumYOY==0">
                      0
                    </span>
                        <span class="compareRateDecrease" v-if="dipNumYOY<0">
                      {{Math.abs(dipNumYOY)}}%↓
                    </span>
                      </div>
                      <div class="compare2">
                        环比:
                        <span class="compareRateIncrease" v-if="dipNumRingRatio>0">
                      {{dipNumRingRatio}}%↑
                    </span>
                        <span class="compareRateEqual" v-if="dipNumRingRatio==0">
                      0
                    </span>
                        <span class="compareRateIncrease" v-if="dipNumRingRatio<0">
                      {{Math.abs(dipNumRingRatio)}}%↓
                    </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="6" style="height: 100%">
              <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                <div style="height:30%;width:100%;padding-left:5px;margin-top:5px;font-size: 12px;font-weight: bold;">全院入组病案数</div>
                <div style="height:70%;width:100%;padding-left:10px;font-size: 12px;font-weight: bold;display: flex;">
                  <div style="width:40%;height:100%;display: flex;">
                    <div v-if="drgInGroupMedcasVal>=10000" class="number">
                      {{(drgInGroupMedcasVal/10000)}}万
                    </div>
                    <div v-else class="number">
                      {{drgInGroupMedcasVal}}
                      <span class="fen">例</span>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同期:
                        <span class="compareNum">{{lastYearInGroupNum}}</span>
                      </div>
                      <div class="compare2">
                        上期:
                        <span class="compareNum">{{lastMonthInGroupNum}}</span>
                      </div>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同比:
                        <span class="compareRateIncrease" v-if="inGroupNumYOY>0">
                      {{inGroupNumYOY}}%↑
                    </span>
                        <span class="compareRateEqual" v-if="inGroupNumYOY==0">
                      0
                    </span>
                        <span class="compareRateDecrease" v-if="inGroupNumYOY<0">
                      {{Math.abs(inGroupNumYOY)}}%↓
                    </span>
                      </div>
                      <div class="compare2">
                        环比:
                        <span class="compareRateIncrease" v-if="inGroupNumRatio>0">
                      {{inGroupNumRatio}}%↑
                    </span>
                        <span class="compareRateEqual" v-if="inGroupNumRatio==0">
                      0
                    </span>
                        <span class="compareRateIncrease" v-if="inGroupNumRatio<0">
                      {{Math.abs(inGroupNumRatio)}}%↓
                    </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="6" style="height: 100%">
              <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                <div style="height:30%;width:100%;padding-left:5px;margin-top:5px;font-size: 12px;font-weight: bold;">全院未入组病案数</div>
                <div style="height:70%;width:100%;padding-left:10px;font-size: 12px;font-weight: bold;display: flex;">
                  <div style="width:40%;height:100%;display: flex;">
                    <div v-if="notInGroupNum>=10000" class="number">
                      {{(notInGroupNum/10000)}}万
                    </div>
                    <div v-else class="number">
                      {{notInGroupNum}}
                      <span class="fen">例</span>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同期:
                        <span class="compareNum">{{lastYearNotInGroupNum}}</span>
                      </div>
                      <div class="compare2">
                        上期:
                        <span class="compareNum">{{lastMonthNotInGroupNum}}</span>
                      </div>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同比:
                        <span class="compareRateIncrease" v-if="notInGroupNumYOY>0">
                      {{Number(notInGroupNumYOY)}}%↑
                    </span>
                        <span class="compareRateEqual" v-if="notInGroupNumYOY==0">
                      0
                    </span>
                        <span class="compareRateDecrease" v-if="notInGroupNumYOY<0">
                      {{Math.abs(notInGroupNumYOY)}}%↓
                    </span>
                      </div>
                      <div class="compare2">
                        环比:
                        <span class="compareRateIncrease" v-if="notInGroupNumRatio>0">
                      {{notInGroupNumRatio}}%↑
                    </span>
                        <span class="compareRateEqual" v-if="notInGroupNumRatio==0">
                      0
                    </span>
                        <span class="compareRateIncrease" v-if="notInGroupNumRatio<0">
                      {{Math.abs(notInGroupNumRatio)}}%↓
                    </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div style="height:31%;width: 100%">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="12" style="height: 100%">
              <div id="notIngroupCount" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
            <el-col :span="12" style="height:100%">
              <el-table ref="notInGroupTable"
                        id="notInGroupTable"
                        size="mini"
                        stripe
                        height="100%"
                        :data="notInGroupList"
                        :span-method="objectSpanMethod"
                        v-loading="listLoading"
                        border>

                <el-table-column label="分类"  align="left" width="95">
                  <template slot-scope="scope">{{scope.row.notInGroupName | formatIsEmpty}}</template>
                </el-table-column>
                <el-table-column label="未入组 / 歧义原因"  align="left" >
                  <template slot-scope="scope">{{scope.row.notInGroupReason | formatIsEmpty}}</template>
                </el-table-column>
                <el-table-column label="病案数"  align="center" width="120">
                  <template slot-scope="scope">
                    <div v-if="Number(scope.row.medcasVal)>0" class='skip' @click="queryNoGroupCountMedicalNum(scope.row)">
                      {{scope.row.medcasVal | formatIsEmpty}}
                    </div>
                    <div v-if="Number(scope.row.medcasVal)==0" style="color:#000000">
                      {{scope.row.medcasVal | formatIsEmpty}}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="未入组占比"  align="center" width="110">
                  <template slot-scope="scope">{{scope.row.notInGroupRate | formatIsEmpty}}%</template>
                </el-table-column>
                <el-table-column label="总体占比"  align="center" width="110">
                  <template slot-scope="scope">{{scope.row.allRate | formatIsEmpty}}%</template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="height: 58%;width: 100%">
          <el-table ref="inGroupListTable"
                    id="inGroupTable"
                    highlight-current-row
                    size="mini"
                    :header-cell-style="{'text-align':'center'}"
                    height="97%"
                    :data="list"
                    v-loading="listLoading"
                    @sort-change='sort_change'
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column
              label="序号"
              align="center"
              type="index"
              width="50">
            </el-table-column>
            <el-table-column label="出院科室编码" prop="priOutHosDeptCode" v-if="false">
              <template slot-scope="scope">{{scope.row.priOutHosDeptCode | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="出院科室名称" prop="priOutHosDeptName" width="100"  align="left" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="入组率" align="center" width="194" prop="inGroupRate" sortable='custom'>
              <template slot-scope="scope">
                <el-progress :text-inside="true" :stroke-width="16" :percentage="Number(scope.row.inGroupRate)" :color="customColorMethod">
                </el-progress></template>
            </el-table-column>
            <el-table-column label="DIP组数" prop="dipNum" align="right" width="70" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.dipNum)>0" class='skip' @click="queryDipNum(scope.row)">
                  {{scope.row.dipNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.dipNum)==0" style="color:#000000">
                  {{scope.row.dipNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="病案总数" prop="medicalTotalNum" align="right" width="75" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.medicalTotalNum)>0" class='skip' @click="queryMedicalTotalNum(scope.row)">
                  {{scope.row.medicalTotalNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.medicalTotalNum)==0" style="color:#000000">
                  {{scope.row.medicalTotalNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="入组病案数" prop="groupNum" align="right" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.groupNum)>0" class='skip' @click="queryGroupNum(scope.row)">
                  {{scope.row.groupNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.groupNum)==0" style="color:#000000">
                  {{scope.row.groupNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="未入组病案数" prop="noGroupNum" align="right" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.noGroupNum)>0" class='skip' @click="queryNoGroupNum(scope.row)">
                  {{scope.row.noGroupNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.noGroupNum)==0" style="color:#000000">
                  {{scope.row.noGroupNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="未找到手术级别" prop="error1" align="right" width="115" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.error1)>0" class='skip' @click="queryError1Num(scope.row)">
                  {{scope.row.error1 | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.error1)==0" style="color:#000000">
                  {{scope.row.error1 | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="未找到主要诊断节代码" prop="error2" align="right" width="155" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.error2)>0" class='skip' @click="queryError2Num(scope.row)">
                  {{scope.row.error2 | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.error2)==0" style="color:#000000">
                  {{scope.row.error2 | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="不在支付体系" prop="error3" align="right" width="115" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.error3)>0" class='skip' @click="queryError3Num(scope.row)">
                  {{scope.row.error3 | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.error3)==0" style="color:#000000">
                  {{scope.row.error3 | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="诊断编码不是医保版编码" prop="error4" align="right" width="165" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.error4)>0" class='skip' @click="queryError4Num(scope.row)">
                  {{scope.row.error4 | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.error4)==0" style="color:#000000">
                  {{scope.row.error4 | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="手术及操作编码不是医保版编码" prop="error5" align="right" width="205" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.error5)>0" class='skip' @click="queryError5Num(scope.row)">
                  {{scope.row.error5 | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.error5)==0" style="color:#000000">
                  {{scope.row.error5 | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="主要诊断编码为空" prop="error6" align="right" width="150" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.error6)>0" class='skip' @click="queryError6Num(scope.row)">
                  {{scope.row.error6 | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.error6)==0" style="color:#000000">
                  {{scope.row.error6 | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="主要诊断编码不参与分组" prop="error7" align="right" width="150" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.error7)>0" class='skip' @click="queryError7Num(scope.row)">
                  {{scope.row.error7 | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.error7)==0" style="color:#000000">
                  {{scope.row.error7 | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="主要诊断填写不规范" prop="error8" align="right" width="150" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.error8)>0" class='skip' @click="queryError8Num(scope.row)">
                  {{scope.row.error8 | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.error8)==0" style="color:#000000">
                  {{scope.row.error8 | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, querySelectTreeAndSelectList, queryLikeDipGroupByPram } from '@/api/common/drgCommon'
import { getDataList as queryPageData, getNoGroupResonCountInfo, getTopCountInfo } from '@/api/dipBusiness/dipInGroupAnalysis'
import { formatDate } from '@/utils/date'
import { elExportExcel } from '@/utils/exportExcel'
import echarts from 'echarts'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  b16c: null,
  cysj: null,
  dipGroup: '',
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date,
  js_start_date: this.js_start_date,
  js_end_date: this.js_end_date
}
export default {
  name: 'groupInAnalysis',
  components: { },
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      list: null,
      tempList: [],
      total: null,
      cy_start_date: this.$somms.getYearMonthStartTime(),
      cy_end_date: this.$somms.getYearMonthEndTime(),
      seStartTime: this.$somms.getYearMonthStartTime(),
      seEndTime: this.$somms.getYearMonthEndTime(),
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      b16c: null,
      medicalRecordNum: 0,
      lastYearMedicalRecordNum: 0,
      lastMonthMedicalRecordNum: 0,
      dipNum: 0,
      lastYearDipNum: 0,
      lastMonthDipNum: 0,
      drgInGroupMedcasVal: 0,
      lastYearInGroupNum: 0,
      lastMonthInGroupNum: 0,
      medicalRecordNumYOY: 0,
      medicalRecordNumRingRatio: 0,
      dipNumYOY: 0,
      dipNumRingRatio: 0,
      inGroupNumYOY: 0,
      inGroupNumRatio: 0,
      notInGroupNumYOY: 0,
      notInGroupNumRatio: 0,
      notInGroupNum: 0,
      lastYearNotInGroupNum: 0,
      lastMonthNotInGroupNum: 0,
      tableHeight: 0,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      notInGroupList: null
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '0'
      }
    },
    formatType (value) {
      if (value == '1') {
        return '分组器结果'
      } else if (value == '2') {
        return '排除病案'
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.inGroupListTable.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.inGroupListTable.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.inGroupListTable.$el.offsetTop - 35
      }
      if (Object.keys(this.$route.query).length > 0) {
        if (this.$route.query.inHosFlag) {
          this.listQuery.inHosFlag = this.$route.query.inHosFlag
        }
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.listQuery.jumpTimeChange('out', this.$route.query, this.listQuery)
        }
        if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
          this.$refs.listQuery.jumpTimeChange('in', this.$route.query, this.listQuery)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.listQuery.jumpTimeChange('se', this.$route.query, this.listQuery)
        }
      }
      this.handleSearchList()
    })
  },
  methods: {
    handleSelectionChange (val) {
      if (val.length == 0) {
        this.tempList = []
      } else {
        this.tempList = this.list.filter(data => {
          for (const v of val) {
            if (data.priOutHosDeptCode == v.priOutHosDeptCode) {
              return true
            }
          }
          return false
        })
      }
    },
    my_desc_sort (a, b) {
      if (Number(a.inGroupRate) > Number(b.inGroupRate)) {
        return -1
      } else if (Number(a.inGroupRate) < Number(b.inGroupRate)) {
        return 1
      } else {
        return 0
      }
    },
    my_asc_sort (a, b) {
      if (Number(a.inGroupRate) < Number(b.inGroupRate)) {
        return -1
      } else if (Number(a.inGroupRate) > Number(b.inGroupRate)) {
        return 1
      } else {
        return 0
      }
    },
    sort_change (column) {
      // this.current_page = 1
      if (column.prop === 'groupRate') {
        if (column.order === 'descending') {
          this.list = this.list.sort(this.my_desc_sort)
        } else if (column.order === 'ascending') {
          this.list = this.list.sort(this.my_asc_sort)
        }
      }
      // this.list = this.filtered_data.slice(0, 200) // show only one page
    },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
        this.getTopCount()
        this.getNoGroupResonCount()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.dataAuth = true
      this.submitListQuery.dipGroup = this.listQuery.dipGroup
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.inGroupListTable.$children, document.getElementById('inGroupTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DIP入组分析')
    },
    getTopCount () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      getTopCountInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        this.medicalRecordNum = response.data.medicalRecordNum
        this.lastYearMedicalRecordNum = response.data.lastYearMedicalRecordNum
        this.lastMonthMedicalRecordNum = response.data.lastMonthMedicalRecordNum
        this.dipNum = response.data.dipNum
        this.lastYearDipNum = response.data.lastYearDipNum
        this.lastMonthDipNum = response.data.lastMonthDipNum
        this.drgInGroupMedcasVal = response.data.drgInGroupMedcasVal
        this.lastYearInGroupNum = response.data.lastYearInGroupNum
        this.lastMonthInGroupNum = response.data.lastMonthInGroupNum
        this.notInGroupNum = response.data.notInGroupNum
        this.lastYearNotInGroupNum = response.data.lastYearNotInGroupNum
        this.lastMonthNotInGroupNum = response.data.lastMonthNotInGroupNum
        this.medicalRecordNumYOY = response.data.medicalRecordNumYOY
        this.medicalRecordNumRingRatio = response.data.medicalRecordNumRingRatio
        this.dipNumYOY = response.data.dipNumYOY
        this.dipNumRingRatio = response.data.dipNumRingRatio
        this.inGroupNumYOY = response.data.inGroupNumYOY
        this.inGroupNumRatio = response.data.inGroupNumRatio
        this.notInGroupNumYOY = response.data.notInGroupNumYOY
        this.notInGroupNumRatio = response.data.notInGroupNumRatio
      })
    },
    getNoGroupResonCount () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      getNoGroupResonCountInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        this.notInGroupList = response.data
        this.pieCount()
      })
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.listQuery.cysj = [this.$somms.getYearMonthStartTime(), this.$somms.getYearMonthEndTime()]
        this.cy_start_date = this.$somms.getYearMonthStartTime()
        this.cy_end_date = this.$somms.getYearMonthEndTime()
        this.seStartTime = this.$somms.getYearMonthStartTime()
        this.seEndTime = this.$somms.getYearMonthEndTime()
      }
      this.getList()
      this.getTopCount()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.getList()
      this.getTopCount()
      this.getNoGroupResonCount()
    },
    handleResetSearch () {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.tempList = []
      this.getDataIsuue()
    },
    customColorMethod (percentage) {
      if (Number(percentage) < 80) {
        return '#FF0000'
      } else if (Number(percentage) < 85) {
        return '#FA8072'
      } else if (Number(percentage) < 90) {
        return '#FFA500'
      } else {
        return '#67c23a'
      }
    },
    // 下转详情点击
    queryDipNum (row) {
      this.$router.push({
        path: '/common/queryDrgDetail',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1',
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime
        }
      })
    },
    queryMedicalTotalNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1',
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime
        }
      })
    },
    queryGroupNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'groupNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1',
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime
        }
      })
    },
    queryNoGroupNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'noGroupNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryEliminateNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'paiChuNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryMainCodeErrorNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'mainCodeErrorNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryNoGroupPlanNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'noGroupPlanNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryMainCodeAndOperateErrorNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'mainCodeAndOperateErrorNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryCodeAndSexErrorNum (row) {
      this.pushPage(row, 'codeAndSexErrorNum')
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'codeAndSexErrorNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryError1Num (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'error1',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryError2Num (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'error2',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryError3Num (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'error3',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryError4Num (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'error4',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryError5Num (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'error5',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryError6Num (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'error6',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryError7Num (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'error7',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryError8Num (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'error8',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryNoGroupCountMedicalNum (row) {
      let deptName = '全院'
      if (this.list.length == 1) {
        deptName = this.$store.getters.getDeptName
      }
      let queryTypeStr = ''
      switch (row.notInGroupReason) {
        case '未找到手术级别':
          queryTypeStr = 'error1'
          break
        case '未找到主要诊断节代码':
          queryTypeStr = 'error2'
          break
        case '不在支付体系':
          queryTypeStr = 'error3'
          break
        case '诊断编码不是医保版编码':
          queryTypeStr = 'error4'
          break
        case '手术及操作编码不是医保版编码':
          queryTypeStr = 'error5'
          break
        case '主要诊断编码为空':
          queryTypeStr = 'error6'
          break
        case '主要诊断编码不参与分组':
          queryTypeStr = 'error7'
          break
        case '主要诊断填写不规范':
          queryTypeStr = 'error8'
          break
      }
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: queryTypeStr,
          priOutHosDeptCode: this.submitListQuery.b16c,
          priOutHosDeptName: deptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '1'
        }
      })
    },
    queryDetails (row) {
      this.$router.push({
        path: '/auliManage/deptCompar',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          dipGroup: this.listQuery.dipGroup,
          type: '1'
        }
      })
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      // 第一列合并
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return {
            rowspan: 7,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
        // else if (rowIndex === 3) {
        // return {
        //   rowspan: 4,
        //   colspan: 1
        // };
        // }
      }
    },
    pieCount () {
      let notInGroupReasons = []
      let result = []
      let totals = 0
      for (let i = 0; i < this.notInGroupList.length; i++) {
        notInGroupReasons.push(this.notInGroupList[i].notInGroupReason)
        result.push({ value: this.notInGroupList[i].medcasVal, name: this.notInGroupList[i].notInGroupReason })
        totals = totals + Number(this.notInGroupList[i].medcasVal)
      }
      // let colors = ['rgba(145,204,117,0.7)','#78c2df','rgba(204,255,102,0.8)','#f9ca60','#ed6d6d','rgb(250 127 255)'];
      let colors = this.$somms.generateColor()
      let get = function (e) {
        var
          newStr = e.name

        let rate = ((Number(e.value) / (Number(totals) == 0 ? 1 : Number(totals))) * 100).toFixed(1) + '%'
        return newStr + '(' + rate + ')'
      }
      let option = {

        title: [
          { text: 'DIP未入组/歧义原因', left: 10, top: 5, textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          top: 'center',
          data: notInGroupReasons
        },
        series: [
          {
            type: 'pie',
            radius: '65%', // 设置饼图大小
            center: ['65%', '54%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'outside',
                backgroundColor: '#F0F8FF',
                borderColor: '#aaa',
                borderWidth: 1,
                borderRadius: 4,
                formatter: get,
                lineHeight: 15,
                fontFamily: 'Microsoft YaHei',
                fontSize: 12,
                color: '#000000',
                padding: [5, 5]
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % result.length]
                }
              }
            },
            data: result
          }
        ]
      }

      let notIngroupCount = echarts.getInstanceByDom(document.getElementById('notIngroupCount'))
      if (notIngroupCount) {
        notIngroupCount.clear()
      } else {
        notIngroupCount = echarts.init(document.getElementById('notIngroupCount'))
      }
      notIngroupCount.setOption(option)
      return notIngroupCount
    },
    exportExcel () {
      let tableId = 'inGroupTable'
      let fileName = 'DIP入组分析'
      elExportExcel(tableId, fileName)
    },

    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    fnDipGroupSelect (item) {
      this.listQuery.dipGroup = item.dipCodg
    }
  }
}
</script>
<style scoped>
.number{
  margin-top:2px;
  font-size:28px;
  font-weight: bold;
  font-family:Microsoft YaHei
}
.fen{
  font-size: 10px;
}
.compare1{
  font-weight: normal;
  font-size: 10px;
  color:grey;
  width:100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}
.compare2{
  font-weight: normal;
  font-size: 10px;
  color:grey;
  margin-top:3px;
  width:100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}
.compareNum{
  font-weight: bold;
  margin-left:5px;
}
.compareRateIncrease{
  font-weight: bold;
  color:#409EFF;
  margin-left:5px;
}
.compareRateEqual{
  font-weight: bold;
  color:#000000;
  margin-left:5px;
}
.compareRateDecrease{
  font-weight: bold;
  color:red;
  margin-left:5px;
}
</style>
