<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
            show-date-range
             show-in-date-range
             show-se-date-range
             show-patient-num
             :show-dip="{show : this.$somms.getGroupType() === '1'}"
             :show-drg="{show : this.$somms.getGroupType() === '3'}"
             :show-hos-dept="{ show: this.$somms.hasHosRole()}"
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="结算清单详情"
             :container="true"
             :exportExcel="{ 'tableId': tableId, exportName: '结算清单'}"
             :exportExcelFun="queryPageData"
             :exportExcelHasChild="false"
             :initTimeValueNotQuery="false"
             ref="somForm"
             @query="handleSearchList">

      <template slot="extendFormItems">

        <el-form-item label="离院方式">
          <el-select v-model="listQuery.b34c" placeholder="请选择离院方式" @change="getDataIsuue" clearable class="som-w-one-hundred">
            <el-option
              v-for="item in dictVoList.B34C"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="患者姓名">
          <el-input  v-model="listQuery.a11" placeholder="请输入患者姓名" />
        </el-form-item>

        <el-form-item label="DIP组">
          <el-input  v-model="listQuery.queryDipGroup" placeholder="请输入dip组" />
        </el-form-item>

        <el-form-item label="是否标识" prop="lookOver">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择" v-model="listQuery.lookOver" @change="getDataIsuue"/>
        </el-form-item>

        <el-form-item label="可调整病例" prop="isAdjustable">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择" v-model="listQuery.isAdjustable" @change="getDataIsuue"/>
        </el-form-item>

        <el-form-item label="院前检查费是否为零" prop="isNullPreHosCost">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择" v-model="listQuery.isNullPreHosCost" @change="getDataIsuue"/>
        </el-form-item>

        <el-form-item label="是否结算" prop="listSerialNumFlag">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择" v-model="listQuery.listSerialNumFlag" @change="getDataIsuue"/>
        </el-form-item>

        <el-form-item label="是否异地" prop="isRemote" >
          <drg-dict-select v-model="listQuery.isRemote" placeholder="请选择是否异地" dicType="INSUPLCADMDVS_TYPE"
                           @change="getDataIsuue" />
        </el-form-item>
      </template>
<!--      <template slot="buttons">
      <el-button type="primary" @click="extractHisViueData" class="som-button-margin-right"><i
        class="el-icon-upload el-icon&#45;&#45;left"></i>抽取
      </el-button>
      </template>-->

      <!-- 内容 -->
      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  :id="tableId"
                  size="mini"
                  stripe
                  height="100%"
                  :data="list"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>
          <el-table-column label="序号" type="index" align="right">
          </el-table-column>

          <el-table-column label="是否结算" width="100px" align="center">
            <template slot-scope="scope">
              <i class="som-icon-error-waring som-icon-big" v-if="!scope.row.listSerialNumFlag"></i>
              <i class="som-icon-success som-icon-big" v-else></i>
            </template>
          </el-table-column>
          <el-table-column label="是否异地" prop="isRemote" align="center" width="90" />
          <el-table-column label="病案号" prop="a48" align="left" >
            <template slot-scope="scope">{{scope.row.a48 | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="姓名" prop="a11" align="left"  width="90" >
            <template slot-scope="scope">{{scope.row.a11 | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="性别" prop="a12c" align="left"  width="60"
                           :filters="[{ text: '男', value: '1' }, { text: '女', value: '2' }]"
                           :filter-method="filterSex">
            <template slot-scope="scope">{{scope.row.a12c | formatA12c}}</template>
          </el-table-column>
          <el-table-column prop="a14" label="年龄"  align="right" width="80" sortable>
            <template slot-scope="scope">{{scope.row.a14 | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="DRGs编码和名称" prop="drgsCodeAndName" align="left"  width="180" :show-overflow-tooltip="true" v-if="this.$somms.getGroupType() === '3'">
            <template slot-scope="scope">{{scope.row.drgsCodeAndName | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="DIP编码和名称" prop="dipCodeAndName" align="left"  width="180" :show-overflow-tooltip="true" v-if="this.$somms.getGroupType() === '1'">
            <template slot-scope="scope">{{scope.row.dipCodeAndName | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="入院科室"  align="left" prop="b13n"  :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.b13n | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="出院科室"  align="left" prop="b16n"  :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.b16n | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="主要诊断"  align="left" prop="c04n" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{ scope.row.c04n | formatIsEmpty }}</template>
          </el-table-column>
          <el-table-column label="主要手术"  align="left" prop="c15x01n" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.c15x01n | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column prop="b12" label="入院时间"  align="right"  width="140px" sortable>
          </el-table-column>
          <el-table-column prop="b15" label="出院时间"  align="right" width="140px"  sortable>
          </el-table-column>
<!--          <el-table-column fixed="right" label="查看详情(旧)" align="center" width="90px" >-->
<!--            <template slot-scope="scope">-->
<!--              <el-button type="primary" size="mini" icon="el-icon-search" @click="handleShowMedicalDetail(scope.$index, scope.row)" circle>-->
<!--              </el-button>-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column fixed="right" label="预览PDF" align="center" width="90px" >
            <template slot-scope="scope">
              <el-button type="primary" size="mini" icon="el-icon-search" @click="preview(scope.row)" circle>
              </el-button>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="编辑清单" align="center" width="90px" >
            <template slot-scope="scope">
              <el-button type="primary" size="mini" icon="el-icon-search" @click="newHandleShowMedicalDetail(scope.$index, scope.row, '1')" circle>
              </el-button>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="查看清单" align="center" width="90px" >
            <template slot-scope="scope">
              <el-button type="primary" size="mini" icon="el-icon-search" @click="newHandleShowMedicalDetail(scope.$index, scope.row, '2')" circle>
              </el-button>
            </template>
          </el-table-column>
<!--          <el-table-column fixed="right" label="删除" align="center" width="90px" >&ndash;&gt;-->
<!--            <template slot-scope="scope">-->
<!--              <el-popconfirm style="cursor: pointer;display: block" @confirm="deleteData(scope.$index, scope.row)" title="删除后数据无法恢复，请确认是否删除？">-->
<!--                <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>-->
<!--              </el-popconfirm>-->
<!--            </template>-->
<!--          </el-table-column>-->
        </el-table>

        <el-dialog title="上传文件"
                   :visible.sync="dialogVisible"
                   width="50%">
          <el-upload style="text-align: center"
                     drag
                     ref="upload"
                     :limit="1"
                     action="customize"
                     accept=".xlsx,.xls"
                     :http-request="upload">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip" style="color: red">文件中必须包含需删除数据病案号，入院时间，出院时间</div>
            <div class="el-upload__tip" slot="tip" style="color: red">文件上传后会即刻删除，且无法恢复，请确认好后再上传</div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
          </el-upload>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { querySelectTreeAndSelectList, queryLikeDipGroupByPram, queryLikeDrgsByPram, queryDataIsuue } from '@/api/common/drgCommon'
import { fetchList as queryPageData, deleteDataById, getHisDate } from '@/api/medicalQuality/settleList'
import { formatDate } from '@/utils/date'
import { elExportExcel } from '@/utils/exportExcel'
import { medicalDeleteDataUpload } from '@/api/medicalQuality/medicalDeleteDataUpload'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 50, // 减少默认分页大小以提升性能
  cysj: null,
  a48: null,
  a11: null,
  b16c: null,
  b34c: null,
  settle_start_date: null,
  settle_end_date: null,
  ry_start_date: null,
  ry_end_date: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date,
  queryDipGroup: '',
  queryDrg: '',
  tableHeight: 0,
  lookOver: '',
  isAdjustable: null,
  isRemote: null,
  isNullPreHosCost: null,
  listSerialNumFlag: null
}
export default {
  name: 'mdcsListManage',
  components: { },
  inject: ['reload'],
  data () {
    return {
      showFlag: false,
      dialogVisible: false,
      tableId: 'dataTable',
      listLoading: true,
      list: null,
      total: null,
      settle_start_date: null,
      settle_end_date: null,
      ry_start_date: null,
      ry_end_date: null,
      cy_start_date: null,
      cy_end_date: null,
      dictVoList: {},
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      depts: [],
      debounceTimer: null, // 防抖定时器
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created () {

  },
  beforeDestroy() {
    // 清理防抖定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
      this.debounceTimer = null
    }
  },
  watch: {
    list: {
      handler: function (newList, oldList) {
        // 只有在列表长度发生变化或首次加载时才重新布局
        if (!oldList || newList.length !== oldList.length) {
          this.$nextTick(() => {
            if (this.$refs.dataTable) {
              this.$refs.dataTable.doLayout()
            }
          })
        }
      },
      deep: false // 不需要深度监听，提升性能
    }
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime (time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },
    formatA12c (value) {
      if (value == '1') {
        return '男'
      } else if (value == '2') {
        return '女'
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.findSelectTreeAndSelectList()
      if (Object.keys(this.$route.query).length > 0) {
        if (this.$route.query.lookOver) {
          this.listQuery.lookOver = this.$route.query.lookOver
        }
        // 时间需要在最下面设置
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.listQuery)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.listQuery)
        }
      }
      this.getList()
    })
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'B34C')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      // 添加防抖处理
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer)
      }
      this.debounceTimer = setTimeout(() => {
        queryDataIsuue().then(response => {
          this.listLoading = false
          this.cy_start_date = response.data.cy_start_date
          this.cy_end_date = response.data.cy_end_date
          this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
          this.getList()
        })
      }, 300)
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.a48 = this.listQuery.medcasCodg
      this.submitListQuery.a11 = this.listQuery.a11
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.b34c = this.listQuery.b34c
      this.submitListQuery.queryDipGroup = this.listQuery.queryDipGroup
      this.submitListQuery.queryDrg = this.listQuery.drgCodg
      this.submitListQuery.settle_start_date = this.settle_start_date
      this.submitListQuery.settle_end_date = this.settle_end_date
      this.submitListQuery.ry_start_date = this.listQuery.inStartTime
      this.submitListQuery.ry_end_date = this.listQuery.inEndTime
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.lookOver = this.listQuery.lookOver
      this.submitListQuery.isAdjustable = this.listQuery.isAdjustable
      this.submitListQuery.isNullPreHosCost = this.listQuery.isNullPreHosCost
      this.submitListQuery.listSerialNumFlag = this.listQuery.listSerialNumFlag
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.isRemote = this.listQuery.isRemote

      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list || []
        this.total = response.data.total || 0
      }).catch(error => {
        this.listLoading = false
        this.list = []
        this.total = 0
        this.$message.error('查询数据失败，请重试')
        console.error('查询列表数据失败:', error)
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.settleListTable.$children, document.getElementById('slTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病案数据')
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSelect (item) {
      this.listQuery.dipCodg = item.dipCodg
    },
    querySearchAsyncForDrg (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocompleteForDrg.handleFocus()
      })
    },
    handleSelectForDrg (item) {
      this.listQuery.drgCodg = item.drgsCode
    },
    dateChangeSettle_date (val) {
      if (val) {
        this.settle_start_date = val[0]
        this.settle_end_date = val[1]
      } else {
        this.settle_start_date = null
        this.settle_end_date = null
      }
    },
    dateChangeRysj (val) {
      if (val) {
        this.ry_start_date = val[0]
        this.ry_end_date = val[1]
      } else {
        this.ry_start_date = null
        this.ry_end_date = null
      }
      this.getList()
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.getList()
    },
    handleResetSearch () {
      this.getDataIsuue()
    },
    preview (row) {
      this.$router.push({ path: '/setlListManage/mdcsListPreview', query: { k00: row.k00, id: row.id } })
    },
    handleShowMedicalDetail (index, row) {
      this.$router.push({ path: '/setlListManage/setlListDetail', query: { k00: row.k00, id: row.id } })
    },
    newHandleShowMedicalDetail (index, row, type) {
      let obj = { path: '/setlListManage/setlListInfo2', query: { k00: row.k00, id: row.id } }
      if (type === '2') {
        obj.query.see = true
      } else {
        obj.query.see = false
      }
      this.$router.push(obj)
    },
    filterSex (value, row) {
      return row.a12c === value
    },
    exportExcel () {
      let tableId = 'slTable'
      let fileName = '病案数据'
      elExportExcel(tableId, fileName)
    },
    refresh () {
      this.reload()
    },

    upload (data) {
      let params = new FormData()
      params.append('file', data.file)
      medicalDeleteDataUpload(params).then(res => {
        if (res.code == 200) {
          this.dialogVisible = false
          this.$refs.upload.clearFiles()
          this.$message.success('上传成功')
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
      })
    },
    deleteData (index, item) {
      let params = {
        ids: []
      }
      params.ids.push(item.id)
      deleteDataById(params).then(res => {
        if (res.code == 200) {
          this.$message.success('删除成功')
          this.handleSearchList()
        }
      })
    },
    recoveryData (index, item) {
      let params = {
        ids: []
      }
      params.ids.push(item.id)
      recoveryDataById(params).then(res => {
        if (res.code == 200) {
          this.$message.success('恢复成功')
          this.handleSearchList()
        }
      })
    },
    getParams () {
      return this.submitListQuery
    },
    extractHisViueData () {
      getHisDate({
        startTime: this.getParams().cy_start_date,
        endTime: this.getParams().cy_end_date,
        medcasno: this.getParams().a48
      })
    }
  }
  // beforeRouteLeave(to, from ,next) {
  //   let list = document.getElementsByClassName("el-tooltip__popper");
  //   // console.log("🚀 ~ file: index.vue ~ line 309 ~ deactivated ~ list", list)
  //   if (list.length > 0) {
  //     list[list.length - 1].style.display = "none";
  //   }
  //   next()
  // }
}
</script>
<style scoped>
  /*时间样式设置*/
  /deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner{
    width: 200px;
  }
  .autoSelectInputWidth{
    width: 178px;
  }

</style>
