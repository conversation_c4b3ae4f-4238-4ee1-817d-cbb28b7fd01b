<template>
  <div class="patient-violation-container">
    <!-- 基本信息部分 -->
    <section class="basic-info-section">
      <h2>基本信息</h2>
      <el-form label-position="left" label-width="120px" class="el-form-item" size="medium">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item>
              <template #label>
                <div class="form-item-label">
                  <span>姓名:</span>
                </div>
              </template>
              <span>{{ patientInfo.name }}</span>
            </el-form-item>
            <el-form-item>
              <template #label>
                <div class="form-item-label">
                  <span>年龄:</span>
                </div>
              </template>
              <span>{{ patientInfo.age }}</span>
            </el-form-item>
            <el-form-item>
              <template #label>
                <div class="form-item-label">
                  <span>住院总费用:</span>
                </div>
              </template>
              <span>{{ patientInfo.sumfee }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <template #label>
                <div class="form-item-label">
                  <span>入院时间:</span>
                </div>
              </template>
              <span>{{ patientInfo.b12 }}</span>
            </el-form-item>
            <el-form-item>
              <template #label>
                <div class="form-item-label">
                  <span>出院时间:</span>
                </div>
              </template>
              <span>{{ patientInfo.b15 }}</span>
            </el-form-item>
            <el-form-item>
              <template #label>
                <div class="form-item-label">
                  <span>住院天数:</span>
                </div>
              </template>
              <span>{{ patientInfo.iptDay }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <template #label>
                <div class="form-item-label">
                  <span>违规规则数:</span>
                </div>
              </template>
              <span>{{ patientInfo.totalRecords }}</span>
            </el-form-item>
            <el-form-item>
              <template #label>
                <div class="form-item-label">
                  <span>违规条数:</span>
                </div>
              </template>
              <span>{{ patientInfo.tupleNum }}</span>
            </el-form-item>
            <el-form-item>
              <template #label>
                <div class="form-item-label">
                  <span>违规总费用:</span>
                </div>
              </template>
              <span>{{ patientInfo.totalAmount }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <template #label>
                <div class="form-item-label">
                  <span>诊断信息:</span>
                </div>
              </template>
              <span>{{ patientInfo.diagCodeAndName }}</span>
            </el-form-item>
            <el-form-item>
              <template #label>
                <div class="form-item-label">
                  <span>手术信息:</span>
                </div>
              </template>
              <span>{{ patientInfo.surgery }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>

    <!-- 违规明细信息 -->
    <section class="basic-info-section">
      <h2>违规明细信息</h2>
      <div class="search-bar">
        <el-input
          placeholder="输入搜索关键词"
          v-model="searchKeywordViolation"
          class="search-custom"
          @keyup.enter.native="filterViolationItems"
        >
          <i slot="prefix" class="el-icon-search"></i>
        </el-input>
      </div>
      <el-table :data="filteredRuleViolationItems" border style="width: 100%" row-key="id"
                stripe
                :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                height="300"
      >
        <el-table-column prop="code" label="编码" width="260">
  <template slot-scope="scope">
    <el-link type="primary" v-if="scope.row.errorGroup" @click="handleCodeClick(scope.row)">{{ scope.row.code }}</el-link>
    <span v-else>{{ scope.row.code }}</span>
  </template>
</el-table-column>
        <el-table-column prop="name" label="名称" ></el-table-column>
        <el-table-column prop="ruleSize" label="违规规则数" width="100" align="center"></el-table-column>
        <el-table-column prop="tupleSize" label="违规明细数" width="100" align="center" ></el-table-column>
        <el-table-column prop="sumFee" label="金额" width="80" align="center"></el-table-column>
        <el-table-column prop="errorGroup" label="违规组编码" width="150"></el-table-column>
      </el-table>
      <!-- 可根据需要添加更多内容 -->
    </section>
    <!-- 右侧部分：收费项目明细信息 -->
    <section class="basic-info-section">
      <h2>收费项目明细信息</h2>
      <div class="search-bar">
        <el-input
          placeholder="输入搜索关键词"
          v-model="searchKeyword"
          class="search-custom"
          @keyup.enter.native="filterChargeItems"
        >
          <i slot="prefix" class="el-icon-search"></i>
        </el-input>
      </div>
      <el-table :data="filteredChargeItems" border style="width: 100%" height="300">
        <el-table-column prop="code" label="收费项目编码" width="250"></el-table-column>
        <el-table-column prop="name" label="名称" width="200"></el-table-column>
        <el-table-column prop="price" label="单价" width="100"></el-table-column>
        <el-table-column prop="count" label="次数" width="100"></el-table-column>
        <el-table-column prop="department" label="开单科室" width="150"></el-table-column>
        <el-table-column prop="bilgDrName" label="开单医生" width="150"></el-table-column>
        <el-table-column prop="detitemFeeSumamt" label="收费金额" width="150"></el-table-column>
        <el-table-column prop="feeOcurTime" label="收费时间"></el-table-column>
      </el-table>
    </section>
  </div>
</template>

<script>
import {
  fetchViolationRulesByUniqueId,
  filteredChargeItemsByUniqueId as filteredChargeItems,
  fetchPatientInfoByUniqueId
} from '@/api/examCorrection/ruleAndTuples'

export default {
  data() {
    return {
      params: {},
      k00: '',
      id: '',
      patientInfo: {
        name: '',
        medcasno: '',
        age: 0,
        sumfee: '',
        b12: '',
        b15: '',
        iptDay: 0,
        totalRecords: 0,
        tupleNum: 0,
        totalAmount: '',
        diagCodeAndName: '',
        surgery: ''
      },
      totalRecords: [
        {id: '1', label: '违规规则1'},
        {id: '2', label: '违规规则2'},
        {id: '3', label: '违规规则三'},
        {id: '4', label: '违规处置'}
      ],
      violationDisposals: [
        {id: 'disposal1', label: '第一段'},
        {id: 'disposal2', label: '第二段'},
        {id: 'disposal3', label: '第三段'}
      ],
      selectedtotalRecords: [],
      selectedDisposal: '',
      activeNames: ['1'], // 默认展开的折叠项
      searchKeyword: '',
      searchKeywordViolation: '',
      chargeItems: [],
      ruleViolationItems: [], // 违规明细
      filteredRuleViolationItems: [], // 过滤的违规明细
      filteredChargeItems: [],
      loading: false
    }
  },

  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.medicalDetail.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      // this.tableHeight = window.innerHeight - this.$refs.medicalDetail.$el.offsetTop -35;
      // 监听窗口大小变化
      // let self = this;
      // window.onresize = function() {
      //   self.tableHeight = window.innerHeight - self.$refs.medicalDetail.$el.offsetTop -35;
      // }
      console.log(this.$route.query)
      console.log("213231" + this.$route.query)
      this.k00 = this.$route.query.k00
      this.id = this.$route.query.id
      this.fetchtotalRecords() // 如果需要动态获取违规规则
    })
  },
  created() {
    this.fetchPatientInfoByUniqueId()
    this.fetchtotalRecords() // 如果需要动态获取违规规则
    this.fetchChargeItemsByUniqueId()
  },
  watch: {
    searchKeyword(newVal) {
      this.filterChargeItems(newVal)
    },
    searchKeywordViolation(newVal) {
      this.filterViolationItems(newVal)
    }
  },
  methods: {
    // 获取患者基本信息
    fetchPatientInfoByUniqueId() {
      this.params.id = this.id;
      this.params.uniqueId = this.k00;
      fetchPatientInfoByUniqueId(this.params).then(response => {
        this.patientInfo = response.data;
      }).catch(error => {
        this.$message.error('获取违规规则失败: ' + error.message);
      });
    },

    // 获取违规规则信息（如果需要动态获取）
    fetchtotalRecords() {
      this.params.id = this.id;
      this.params.uniqueId = this.k00;
      fetchViolationRulesByUniqueId(this.params).then(response => {
        this.list = response.data.data
        this.ruleViolationItems = response.data
        this.filteredRuleViolationItems = this.ruleViolationItems
      }).catch(error => {
        this.$message.error('获取违规规则失败: ' + error.message);
      });
    },

    // 获取收费项目信息
    fetchChargeItemsByUniqueId() {
      this.loading = true;
      this.params.id = this.id;
      this.params.uniqueId = this.k00;
      filteredChargeItems(this.params).then(response => {
        this.list = response.data.data
        this.chargeItems = response.data.list
        this.filteredChargeItems = response.data.list
        console.log("list" + response.data.list)
      }).catch(error => {
        this.$message.error('获取违规规则失败: ' + error.message);
      });
    },
    /**
     * 递归过滤树形结构数据
     * @param {Array} data 原始数据（树形结构，每个节点需有 children 字段）
     * @param {Function} predicate 过滤条件函数，返回 true/false
     * @returns {Array} 过滤后的树形结构
     */
    filterTree(data, predicate) {
      return data
        .filter(item => {
          // 递归处理子节点
          const children = item.children ? this.filterTree(item.children, predicate) : []

          // 如果当前节点符合条件，或子节点有内容，则保留该节点
          return predicate(item) || children.length > 0
        })
        .map(item => ({
          ...item,
          children: item.children ? this.filterTree(item.children, predicate) : [],
        }))
    },
    // 搜索违规项目
    filterViolationItems(keyword) {
      this.loading = true
      console.log(typeof keyword)
      if (typeof keyword !== 'string') {
        this.filteredRuleViolationItems = this.ruleViolationItems
        this.loading = false
        return
      }
      // 模拟搜索延迟
      setTimeout(() => {
        let currData = this.filterTree(this.ruleViolationItems, (node) => node.name.includes(keyword.toLowerCase()))
        this.filteredRuleViolationItems = currData
      }, 300)
    },
    // 处理编码点击事件
    handleCodeClick(row) {
      if (row.errorGroup) {
        this.searchKeyword = row.code;
        console.log('Clicked code:', row.code);
      }
    },

    // 搜索收费项目
    filterChargeItems(keyword) {
      this.loading = true
      if (!keyword) {
        this.filteredChargeItems = this.chargeItems
        this.loading = false
        return
      }
      // 模拟搜索延迟
      setTimeout(() => {
        this.filteredChargeItems = this.chargeItems.filter(item =>
          Object.values(item).some(value =>
            String(value).toLowerCase().includes(keyword.toLowerCase())
          )
        )
        this.loading = false
      }, 300)
    }
  }
};
</script>

<style scoped>
.patient-violation-container {
  padding: 20px;
}

.basic-info-section {
  margin-bottom: 20px;
  border: 1px solid #ebebeb;
  border-radius: 4px;
  padding: 15px;
  background-color: #fff;
}

.basic-info-section h2 {
  margin-top: 0;
  color: #333;
  font-size: 18px;
  margin-bottom: 15px;
}

.main-content {
  display: flex;
  gap: 20px; /* 左右部分之间的间距 */
}

.left-section, .right-section {
  flex: 1;
  border: 1px solid #ebebeb;
  border-radius: 4px;
  padding: 15px;
  background-color: #fff;
}

.left-section h2,
.right-section h2 {
  margin-top: 0;
  color: #333;
  font-size: 18px;
  margin-bottom: 15px;
}

.search-bar {
  margin-bottom: 15px;
}

.search-bar .el-input {
  width: 100%;
}

.search-custom /deep/ .el-input__prefix {
  display: flex;
  align-items: center;
}

.el-table {
  width: 100%;
  border-collapse: collapse;
}

.el-table th, .el-table td {
  border: 1px solid #ebebeb;
  padding: 10px;
  text-align: center;
}

.no-data, .loading {
  text-align: center;
  color: #999;
  margin-top: 15px;
}

.el-form-item.el-form-item--mini >>> .el-form-item {
  font-size: 16px;
}

.form-item-label {
  font-size: 14px
}

/* 响应式调整 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .left-section, .right-section {
    width: 100%;
    margin-right: 0;
  }
}
</style>
