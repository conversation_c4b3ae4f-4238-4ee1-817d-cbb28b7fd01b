<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :show-date-range="{ show: true, label: '上传时间' }"
             :container="true"
             headerTitle="查询条件"
             @query="queryData">

      <template slot="buttons">
        <el-button type="primary" @click="add" class="som-button-margin-right">新增记录</el-button>
      </template>

      <template slot="contentTitle">
        <drg-title-line title="上传日志">
          <template slot="rightSide">
<!--              <el-alert-->
<!--                title="请先上传分组信息后上传对应费用信息，如果多次上传数据会自动去重。"-->
<!--                type="warning"-->
<!--                :closable="false"-->
<!--                show-icon />-->
          </template>
        </drg-title-line>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">

        <div style="width: 100%;height: 100%" v-loading="rowLoading">
          <el-table :data="tableData"
                    border
                    highlight-current-row
                    size="mini"
                    :header-cell-style="{'text-align':'center'}"
                    v-loading="tableLoading"
                    style="width: 100%; height: 95%">
            <el-table-column  prop="batchNum" width="300" label="批次号" align="left" />

            <el-table-column prop="fileName" align="left" label="文件名称" />

            <el-table-column prop="fileUpldCnt" label="文件上传内容数量" align="right" />

            <el-table-column prop="fileUpldTimeTime" align="right" label="文件上传时间"/>

            <el-table-column prop="errMsg" align="left" label="错误信息" />

            <el-table-column prop="memo_info" align="left" label="备注" />

            <!-- 状态 -->
            <el-table-column prop="state" width="100" align="center" label="上传状态">
              <template slot-scope="scope">
                <i class="el-icon-success" v-if="scope.row.state == 1" style="font-size: 1.3rem;color: green"></i>
                <i class="el-icon-error" v-else style="font-size: 1.3rem;color: red"></i>
              </template>
            </el-table-column>

            <!-- 上传 -->
<!--            <el-table-column prop="state" width="180" align="center" label="是否上传费用信息">-->
<!--              <template slot-scope="scope">-->
<!--                <el-tag v-if="scope.row.fundsFlag == 1">已上传</el-tag>-->
<!--                <div @click.stop="clickRowUpload(scope.row)" v-else>-->
<!--                  <el-upload-->
<!--                    style="text-align: center"-->
<!--                    ref="upload"-->
<!--                    :limit="1"-->
<!--                    action="customize"-->
<!--                    accept=".xlsx,.xls"-->
<!--                    :show-file-list="false"-->
<!--                    :http-request="uploadFundsFile">-->
<!--                    <el-button size="small" type="primary">点击上传</el-button>-->
<!--                  </el-upload>-->
<!--                </div>-->
<!--              </template>-->
<!--            </el-table-column>-->

            <!-- 查看 -->
<!--            <el-table-column prop="query" width="120" align="center" label="查看上传明细">-->
<!--              <template slot-scope="scope">-->
<!--                <el-button v-if="scope.row.fundsFlag == 1" type="primary" icon="el-icon-search" circle @click="clickRowDetail(scope.row)"></el-button>-->
<!--              </template>-->
<!--            </el-table-column>-->
          </el-table>

          <!-- 上传 -->
          <el-dialog
            title="新增反馈记录"
            :visible.sync="dialogVisible"
            width="30%">
            <el-form :model="addForm" label-width="80px" v-loading="loading">
              <el-form-item label="批次号" prop="batchNum" style="width: 85%">
                <el-input v-model="addForm.batchNum" disabled></el-input>
              </el-form-item>
              <el-form-item label="上传文件" prop="file">
                <el-upload
                  drag
                  action="customize"
                  accept=".xlsx,.xls"
                  :limit="1"
                  :file-list="fileList"
                  :on-remove="removeFile"
                  :http-request="customFileUpload">
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                  <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
                </el-upload>
              </el-form-item>
              <el-form-item label="备注" prop="memo_info" style="width: 85%">
                <el-input type="textarea" v-model="addForm.memo_info"></el-input>
              </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="upload">上 传</el-button>
          </span>
          </el-dialog>
        </div>

      </template>
    </drg-form>
  </div>
</template>
<script>
import { feedbackUpload, queryFileUploadLog, feedbackUploadFunds } from '@/api/newBusiness/newBusinessUpload'
export default {
  name: 'feedbackUpload',
  data: () => ({
    queryForm: {},
    addForm: {
      batchNum: '',
      file: null,
      memo_info: ''
    },
    dialogVisible: false,
    tableData: [],
    tableLoading: false,
    uploadCheckRow: {},
    fileList: [],
    loading: false,
    rowLoading: false
  }),
  mounted () {
    this.queryData()
  },
  methods: {
    queryData () {
      this.tableLoading = true
      queryFileUploadLog(this.getParams()).then(res => {
        this.tableData = res.data
        this.tableLoading = false
      }).catch(() => { this.tableLoading = false })
    },
    // 添加记录
    add () {
      this.dialogVisible = true
      this.addForm.batchNum = this.getUUID()
    },
    // 上传
    upload () {
      if (this.addForm.file) {
        let params = new FormData()
        params.append('file', this.addForm.file)
        params.append('remark', this.addForm.memo_info)
        params.append('batchNum', this.addForm.batchNum)
        params.append('type', '1')
        this.loading = true
        feedbackUpload(params).then(res => {
          this.dialogVisible = false
          this.$message.success('上传成功')
          this.addForm.file = null
          this.addForm.memo_info = ''
          this.uploadBack()
        }).catch(() => {
          this.uploadBack()
        })
      } else {
        this.$message({ message: '请选择文件', type: 'warning' })
      }
    },
    uploadBack () {
      this.fileList = []
      this.loading = false
      this.queryData()
    },
    // 上传费用信息
    uploadFundsFile (data) {
      let params = new FormData()
      params.append('file', data.file)
      params.append('batchNum', this.uploadCheckRow.batchNum)
      params.append('type', '2')
      this.rowLoading = true
      feedbackUploadFunds(params).then(res => {
        this.$message.success('费用信息上传成功')
        this.rowLoading = false
        this.queryData()
      })
    },
    // 点击上传
    clickRowUpload (row) {
      this.uploadCheckRow = row
    },
    // 点击详情
    clickRowDetail (row) {
      this.$router.push({ path: '/feedbackAnaly/feedbackDetail', query: { batchNum: row.batchNum }
      })
    },
    // 自定义文件上传
    customFileUpload (data) {
      this.addForm.file = data.file
    },
    // 移除文件
    removeFile () {
      this.addForm.file = null
    },
    // 获取参数
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    // 获取UUID
    getUUID () {
      let s = []
      let hexDigits = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      let uuid = s.join('')
      return uuid
    }
  }
}
</script>
