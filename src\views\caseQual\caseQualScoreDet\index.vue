<template>
  <el-container style="height:100%;width:100%">
    <el-aside width="200px">
      <div class="personScoreImg" >
        <div class="personScore">
          <p style="color: rgba(236, 198, 69, 1);font-size:32px;">{{refer_sco}}</p>
          <p style="color: rgba(236, 198, 69, 1);font-size: 20px;">分</p>
        </div>
      </div>
    </el-aside>
    <el-container>
      <el-main style="margin-left:-25px;">
        <el-card class="box-card" style="margin-top:-10px">
          <div slot="header">
            <span style="font-size: 15px;font-weight: bold;">病案扣分原因（患者姓名:{{a11}}）</span>
            <!--<el-button style="float: right; margin-top:-10px;padding: 3px 0" type="text">导出扣分详情</el-button>-->
          </div>
          <div style="height:85px;padding: 10px;font-size: 13px;display: flex;overflow: auto;">
            {{deduPointRea}}
          </div>
        </el-card>

        <div class="table-container" style="flex:1; overflow-y:auto;">
          <el-table
            ref="loseScoreTable"
            size="mini"
            :data="minusScoretableData"
            style="width: 100%;"
            :row-style="tableRowClassName"
            :cell-style="tableCellStyle"
            :height="tableHeight"
            :span-method="objectSpanMethod"
            border>
            <el-table-column label="病案质量评分详情"  align="center">
              <el-table-column  label="检查项目" prop="col1"  width="200" align="center"></el-table-column>
              <el-table-column  label="项目类别" prop="col2" width="80" align="center"></el-table-column>
              <el-table-column  label="项目数" prop="col3" width="70" align="center"></el-table-column>
              <el-table-column  label="评分项" prop="col4" align="center"></el-table-column>
              <el-table-column  label="分值" prop="col5" width="150" align="center"></el-table-column>
              <el-table-column  label="扣分" prop="col6" width="100" align="center"></el-table-column>
            </el-table-column>
          </el-table>
        </div>
      </el-main>
    </el-container>
  </el-container>

</template>
<script>
import { getScoreDetailById } from '@/api/medicalQuality/score'

export default {
  name: 'caseQualScoreDet',
  data () {
    return {
      refer_sco: 0,
      deduPointRea: '无扣分原因',
      activeName: '1', // 展开name为1的折叠板
      a11: null,
      tableHeight: 0,
      minusScoretableData: [
        { col1: '患者基本信息（18分）', col2: 'A类', col3: '2', col4: '新生儿入院体重', col5: '4', col6: null },
        { col1: null, col2: null, col3: null, col4: '新生儿出生体重', col5: '4', col6: null },
        { col1: null, col2: 'B类', col3: '1', col4: '病案号', col5: '2', col6: null },
        { col1: null, col2: 'C类', col3: '4', col4: '性别', col5: '1', col6: null },
        { col1: null, col2: null, col3: null, col4: '出生日期', col5: '1', col6: null },
        { col1: null, col2: null, col3: null, col4: '年龄', col5: '1', col6: null },
        { col1: null, col2: null, col3: null, col4: '医疗付款方式', col5: '1', col6: null },
        { col1: null, col2: 'D类', col3: '20', col4: '健康卡号、患者姓名、出生地、籍贯、民族、身份 证号、职业、 婚姻状况、现住址、电话号码、邮编、 户口地址及邮编、工作单位及地址、单位电话及邮 编、 联系人姓名、关系、地址、电话号码。', col5: '0.5分/项，减至4分为止', col6: null },

        { col1: '住院过程信息（26分）', col2: 'A类', col3: '1', col4: '离院方式', col5: '4', col6: null },
        { col1: null, col2: 'B类', col3: '5', col4: '入院时间', col5: '2', col6: null },
        { col1: null, col2: null, col3: null, col4: '出院时间', col5: '2', col6: null },
        { col1: null, col2: null, col3: null, col4: '实际住院天数', col5: '2', col6: null },
        { col1: null, col2: null, col3: null, col4: '出院科别', col5: '2', col6: null },
        { col1: null, col2: null, col3: null, col4: '是否有31天内再住院计划', col5: '2', col6: null },
        { col1: null, col2: 'C类', col3: '3', col4: '入院途径', col5: '1', col6: null },
        { col1: null, col2: null, col3: null, col4: '入院科别', col5: '1', col6: null },
        { col1: null, col2: null, col3: null, col4: '转科科别', col5: '1', col6: null },

        { col1: '诊疗信息（50分）', col2: 'A类', col3: '6', col4: '出院主要诊断', col5: '4', col6: null },
        { col1: null, col2: null, col3: null, col4: '主要诊断编码', col5: '4', col6: null },
        { col1: null, col2: null, col3: null, col4: '其他诊断', col5: '1 分/项，减至 4 分为止', col6: null },
        { col1: null, col2: null, col3: null, col4: '其他诊断编码', col5: '1 分/项，减至 4 分为止', col6: null },
        { col1: null, col2: null, col3: null, col4: '主要手术或操作名称', col5: '4', col6: null },
        { col1: null, col2: null, col3: null, col4: '主要手术或操作编码', col5: '4', col6: null },
        { col1: null, col2: 'B类', col3: '8', col4: '入院病情', col5: '2', col6: null },
        { col1: null, col2: null, col3: null, col4: '病理诊断', col5: '2', col6: null },
        { col1: null, col2: null, col3: null, col4: '病理诊断编码', col5: '2', col6: null },
        { col1: null, col2: null, col3: null, col4: '切口愈合等级', col5: '2', col6: null },
        { col1: null, col2: null, col3: null, col4: '颅脑损伤患者昏迷时间', col5: '2', col6: null },
        { col1: null, col2: null, col3: null, col4: '其他手术或操作名称', col5: '0.5分/项，减至2分为止', col6: null },
        { col1: null, col2: null, col3: null, col4: '其他手术或操作编码', col5: '0.5分/项，减至2分为止', col6: null },
        { col1: null, col2: null, col3: null, col4: '手术及操作日期', col5: '2', col6: null },
        { col1: null, col2: 'C类', col3: '3', col4: '门（急）诊诊断', col5: '1', col6: null },
        { col1: null, col2: null, col3: null, col4: '门（急）诊诊断疾病编码', col5: '1', col6: null },
        { col1: null, col2: null, col3: null, col4: '麻醉方式', col5: '1', col6: null },
        { col1: null, col2: 'D类', col3: '12', col4: '损伤（中毒）外部原因及疾病编码、病理诊断及编 码和病理号、药物过敏史、尸检记录、血型及 Rh 标识、手术级别、术者、第一助手。', col5: '0.5/项，减至3分为止', col6: null },
        { col1: '费用信息（6分）', col2: 'A类', col3: '1', col4: '总费用', col5: '4', col6: null },
        { col1: null, col2: 'D类', col3: '10', col4: '综合医疗服务类、诊断类、治疗类、康复类、中医 类、西药类、中药类、血液和血制品类、耗材类、 其他类。', col5: '每项0.5分，减至2分为止', col6: null }
      ]
    }
  },
  created () {
    this.getScoreDetail()
  },
  methods: {
    getScoreDetail () {
      if (this.$route.query.a11) {
        this.a11 = this.$route.query.a11
      }
      // if(this.$route.query.deduPointRea){
      //   this.deduPointRea = this.$route.query.deduPointRea;
      // }
      // 获取基本结算清单信息
      let params = new URLSearchParams()
      params.append('id', this.$route.query.id)
      getScoreDetailById(params).then(response => {
        this.refer_sco = response.data.refer_sco
        this.deduPointRea = response.data.deduPointRea
        this.minusScoretableData[0].col6 = response.data.nwbAdmWtDeduPoint
        this.minusScoretableData[1].col6 = response.data.nwbBirWtDeduPoint
        this.minusScoretableData[2].col6 = response.data.medcasNoDeduPoint
        this.minusScoretableData[3].col6 = response.data.gendDeduPoint
        this.minusScoretableData[4].col6 = response.data.brdyDeduPoint
        this.minusScoretableData[5].col6 = response.data.ageDeduPoint
        this.minusScoretableData[6].col6 = response.data.medPayWayDeduPoint
        this.minusScoretableData[7].col6 = response.data.othPatnBasInfoDeduPoint
        this.minusScoretableData[8].col6 = response.data.dscgWayDeduPoint
        this.minusScoretableData[9].col6 = response.data.admTimeDeduPoint
        this.minusScoretableData[10].col6 = response.data.dscgTimeDeduPoint
        this.minusScoretableData[11].col6 = response.data.actIptDaysDeduPoint
        this.minusScoretableData[12].col6 = response.data.dscgCatyDeduPoint
        this.minusScoretableData[13].col6 = response.data.is31DayInIptPlanDeduPoint
        this.minusScoretableData[14].col6 = response.data.admWayDeduPoint
        this.minusScoretableData[15].col6 = response.data.admCatyDeduPoint
        this.minusScoretableData[16].col6 = response.data.refldeptCatyDeduPoint
        this.minusScoretableData[17].col6 = response.data.dscgMainDiagDeduPoint
        this.minusScoretableData[18].col6 = response.data.mainDiagCodgDeduPoint
        this.minusScoretableData[19].col6 = response.data.othDiagDeduPoint
        this.minusScoretableData[20].col6 = response.data.othDiagCodgDeduPoint
        this.minusScoretableData[21].col6 = response.data.mainOprnOprtNameDeduPoint
        this.minusScoretableData[22].col6 = response.data.mainOprnOprtCodgDeduPoint
        this.minusScoretableData[23].col6 = response.data.admCondDeduPoint
        this.minusScoretableData[24].col6 = response.data.palgDiagDeduPoint
        this.minusScoretableData[25].col6 = response.data.palgDiagCodgDeduPoint
        this.minusScoretableData[26].col6 = response.data.incsHealLvDeduPoint
        this.minusScoretableData[27].col6 = response.data.brnDamgPatnComaTimeDeduPoint
        this.minusScoretableData[28].col6 = response.data.othOprnOprtNameDeduPoint
        this.minusScoretableData[29].col6 = response.data.othOprnOprtCodgDeduPoint
        this.minusScoretableData[30].col6 = response.data.oprnOprtDateDeduPoint
        this.minusScoretableData[31].col6 = response.data.otpDiagDeduPoint
        this.minusScoretableData[32].col6 = response.data.otpDiagDiseCodgDeduPoint
        this.minusScoretableData[33].col6 = response.data.anstWayDeduPoint
        this.minusScoretableData[34].col6 = response.data.othTrtInfoDeduPoint
        this.minusScoretableData[35].col6 = response.data.sumfeeDeduPoint
        this.minusScoretableData[36].col6 = response.data.othAstInfoDeduPoint
      })
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      // 第一列合并
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return {
            rowspan: 8,
            colspan: 1
          }
        } else if (rowIndex === 8) {
          return {
            rowspan: 9,
            colspan: 1
          }
        } else if (rowIndex === 17) {
          return {
            rowspan: 18,
            colspan: 1
          }
        } else if (rowIndex === 35) {
          return {
            rowspan: 2,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      // 第二、三列合并
      if (columnIndex === 1 || columnIndex === 2) {
        if (rowIndex === 0) {
          return {
            rowspan: 2,
            colspan: 1
          }
        } else if (rowIndex === 2) {
          return {
            rowspan: 1,
            colspan: 1
          }
        } else if (rowIndex === 3) {
          return {
            rowspan: 4,
            colspan: 1
          }
        } else if (rowIndex === 7) {
          return {
            rowspan: 1,
            colspan: 1
          }
        } else if (rowIndex === 8) {
          return {
            rowspan: 1,
            colspan: 1
          }
        } else if (rowIndex === 9) {
          return {
            rowspan: 5,
            colspan: 1
          }
        } else if (rowIndex === 14) {
          return {
            rowspan: 3,
            colspan: 1
          }
        } else if (rowIndex === 17) {
          return {
            rowspan: 6,
            colspan: 1
          }
        } else if (rowIndex === 23) {
          return {
            rowspan: 8,
            colspan: 1
          }
        } else if (rowIndex === 31) {
          return {
            rowspan: 3,
            colspan: 1
          }
        } else if (rowIndex === 34) {
          return {
            rowspan: 1,
            colspan: 1
          }
        } else if (rowIndex === 35) {
          return {
            rowspan: 1,
            colspan: 1
          }
        } else if (rowIndex === 36) {
          return {
            rowspan: 1,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      // 表行合并
      // if (rowIndex === 8) {
      //   if (columnIndex >= 1) {
      //     return [1, 5];
      //   } else{
      //     return [1, 1];
      //   }
      // }
    },
    tableRowClassName (data) {
      let styleObj = {}
      if (data.row.col6 !== 0) {
        styleObj.color = 'red'
        return styleObj
      }
      return styleObj
    },
    tableCellStyle (row, column, rowIndex, columnIndex) {
      console.log(row)
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.settleListTable.$el.offsetTop：表格距离浏览器的高度
      // 50表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.loseScoreTable.$el.offsetTop - 20
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.loseScoreTable.$el.offsetTop - 20
      }
    })
  }
}
</script>
<style scoped>
  .hzxx{
    max-height: 150px;
    overflow-y:auto;
    font-size: 15px;
    font-weight: bold;
  }
  .personScoreImg{
    height: 200px;
    width: 200px;
    display: flex;
    margin: auto;
    background-position: center;
    background-repeat: no-repeat;
    background: url(../../../assets/images/medicalQuality/mainScore.png) center bottom / 80% no-repeat;
  }
  .personScore{
    height: 40%;
    width: 40%;
    display: flex;
    margin-left: auto;
    margin-right:auto;
    margin-top: 41%;
    align-items: center;
    align-content: center;
    justify-content: center;
    /*background-color: rgba(236, 198, 69,.8);*/
    background-position: center;
    border-radius: 100%;
  }
  .loseScoreNote{
    width:100%;
    height:100%;
    font-size: 13px;
    overflow: auto;
  }
  /deep/ .el-card__body{
    padding:5px;
  }
  /deep/ .el-card__header{
    padding: 8px 20px;
  }
  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }
</style>
