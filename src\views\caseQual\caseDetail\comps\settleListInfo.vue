<template>
  <div style="width: 100%;height: 100%" :model="value">
    <div style="text-align: center;font-size: 30px">
      {{ value.somHiInvyBasInfo.a02 }}医疗保障基金结算清单
    </div>
    <!--   循环展示表头-->
    <div v-for="(maxDiv,maxIndex) in titleHeadList" :key="'a'+maxIndex" :style="maxDiv.style">
      <div v-for="(item ,index) in titleHeadList[maxIndex].allValue" :key="item.name+index" :style="item.outDivStyle">
        <div :style="item.innerDivStyle">{{ item.name }}</div>
        <input :readonly="upldStas()" :type="item.inputType" v-model="item.value" :ref="item.ref"
               :style="item.inputStyle" @input="inputChange(item.value,item.ref,item.name)">
      </div>
    </div>
    <div style="border: 1px black solid;margin-top: 5px">
      <div class="subtitleStyle">一、基本信息</div>
      <div v-for="(maxDiv,basicIndex) in basicInfoList" :key="'b'+basicIndex" :style="maxDiv.style">
        <div v-for="(item ,baIndex) in basicInfoList[basicIndex].allValue" :key="item.name+baIndex"
             :style="item.outDivStyle">
          <div :style="item.innerDivStyle">{{ item.name }}</div>
          <!--          <drg-dict-select :dicType="item.dicType" :type="2" v-model="item.value" :useClass="false" :selectStyle="{width: '20%'}" v-if="item.jpDictType==1"/>-->
          <input :readonly="upldStas()" :type="item.inputType" v-model="item.value" :ref="item.ref"
                 :style="item.inputStyle" @input="inputChange(item.value,item.ref,item.name)">
          <div v-if="item.addDiv==1">{{ item.explain }}</div>
        </div>
      </div>
      <div class="subtitleStyle2">二、门诊慢特病诊疗信息</div>
      <div class="divMargin">
        <div :style="{color:checkData(value.deptCode)}" class="fontWeight">诊断科别</div>
        <input :readonly="upldStas()" type="text" v-model="value.deptCode" ref="deptname" style="width: 15%"
               @input="inputChange(value.deptCode,deptCode)" class="inputStyle">
        <div :style="{color:checkData(value.mdtrtDate)}" class="fontWeight" style="padding-left: 58%">就诊日期
        </div>
        <input :readonly="upldStas()" type="text" v-model="value.mdtrtDate" ref="diagnosticdate"
               style="width: 14%" @input="inputChange(value.mdtrtDate,mdtrtDate)" class="inputStyle">
      </div>
      <div class="table-container">
        <el-table ref="mzmtbListTable"
                  size="mini"
                  stripe
                  :data="value.busOutpatientListBa"
                  style="width: 100%"
                  border>
          <el-table-column label="诊断名称" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.diagName" :disabled=elUploadState() size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="诊断代码" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.diagCode" :disabled=elUploadState() size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="手术及操作名称" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.oprnOprtName" :disabled=elUploadState() size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="手术及操作代码" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.oprnOprtCode" size="mini" :disabled=elUploadState()></el-input>
            </template>
          </el-table-column>
          <!--          <el-table-column label="移动" fixed="right" align="center" >-->
          <!--            <template slot-scope="scope">-->
          <!--              <el-button type="text" icon="el-icon-top" @click="moveUp(scope.row.index,scope.row,value.xyDiseaseList)"></el-button>-->
          <!--              <el-button type="text" icon="el-icon-bottom" @click="moveDown(scope.row.index,scope.row,value.xyDiseaseList)"></el-button>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
        </el-table>
      </div>
      <div class="subtitleStyle">三、住院诊疗信息</div>
      <div style="border: 1px black solid;border-width: 0 0 1px 0">
        <div style="display:flex;margin: 10px">
          <div :style="{color:checkData(value.somHiInvyBasInfo.b38)}" class="fontWeight">住院医疗类型:</div>
          <input type="text" v-model="value.somHiInvyBasInfo.b38" ref="b38"
                 @input="inputChange(value.somHiInvyBasInfo.b38,'b38')" class="numInputStyle">
          <div style="display: flex">
            <div style="padding-left: 20px">1、住院</div>
            <div style="padding-left: 20px">2、日间手术</div>
          </div>
        </div>
      </div>
      <div style="border: 1px black solid;border-width: 0 0 1px 0">
        <div style="display:flex;margin: 10px">
          <div :style="{color:checkData(value.somHiInvyBasInfo.b11c)}" class="fontWeight">入院途径</div>
          <input type="text" v-model="value.somHiInvyBasInfo.b11c" ref="b11c"
                 @input="inputChange(value.somHiInvyBasInfo.b11c,'b11c')" class="numInputStyle">
          <div style="display: flex">
            <div style="padding-left: 20px">1、急诊</div>
            <div style="padding-left: 20px">2、门诊</div>
            <div style="padding-left: 20px">3、其他医疗机构转入</div>
            <div style="padding-left: 20px">9、其他</div>
          </div>
        </div>
      </div>
      <div style="border: 1px black solid;border-width: 0 0 1px 0">
        <div style="display:flex;margin: 10px">
          <div :style="{color:checkData(value.somHiInvyBasInfo.b39)}" class="fontWeight">治疗类别</div>
          <input :readonly="upldStas()" type="text" v-model="value.somHiInvyBasInfo.b39" ref="b39"
                 @input="inputChange(value.somHiInvyBasInfo.b39,'b39')" class="numInputStyle">
          <div style="display: flex">
            <div style="padding-left: 20px">1、西医</div>
            <div style="padding-left: 20px">2、中医（2.1 中医 2.2民族医）</div>
            <div style="padding-left: 20px">3、中西医</div>
          </div>
        </div>
      </div>
      <div v-for="(maxDiv,beIndex) in beInHospital" :key="'c'+beIndex" :style="maxDiv.style">
        <div v-for="(item ,beInHosIndex) in beInHospital[beIndex].allValue" :key="item.name+beInHosIndex"
             :style="item.outDivStyle">
          <div :style="item.innerDivStyle">{{ item.name }}</div>
          <input :readonly="upldStas()" :type="item.inputType" v-model="item.value" :ref="item.ref"
                 :style="item.inputStyle" @input="inputChange(item.value,item.ref,item.name)">
          <div v-if="item.addDiv==1">{{ item.explain }}</div>
        </div>
      </div>
      <div style="display: flex">
        <div class="table-container" style="width: 50%">
          <el-table ref="xyzdbListTable"
                    stripe
                    size="mini"
                    :data="value.xyDiseaseList"
                    :row-class-name="tableRowClassName"
                    highlight-current-row
                    @selection-change="handleSelectionChange"
                    style="width: 100%"
                    border>
            <!--            <el-table-column-->
            <!--              type="selection"-->
            <!--              width="55">-->
            <!--            </el-table-column>-->
            <el-table-column label="出院西医诊断" align="center" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <el-select v-model="scope.row.c07n1" :disabled=elUploadState() size="mini" filterable remote clearable
                           placeholder="请输入诊断" :remote-method="queryDiagnosisList">
                  <el-option
                      v-for="item in diagnosisList"
                      :key="item.icdName"
                      :label="item.icdName"
                      :value="item.icdName">
                    <span style="float: left">{{ item.icdName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.icdCodg }}</span>
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="疾病代码（西医）" align="center">
              <template slot-scope="scope">
                <el-select v-model="scope.row.c06c1" :disabled=elUploadState() size="mini" filterable remote clearable
                           placeholder="请输入编码" :remote-method="queryDiagnosisList">
                  <el-option
                      v-for="item in diagnosisList"
                      :key="item.icdCodg"
                      :label="item.icdCodg"
                      :value="item.icdCodg">
                    <span style="float: left">{{ item.icdCodg }}</span>
                    <span style="float: right; color: #8492a6; font-size: 12px">{{ item.icdName }}</span>
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="入院病情（西医）" align="center" width="130px">
              <template slot-scope="scope">
                <drg-dict-select dicType="RYBQ"
                                 placeholder="请选择入院病情"
                                 v-model="scope.row.c08c1"
                                 :disabled=elUploadState()
                                 :type="2"
                                 :useClass="false"
                                 :selectStyle="{width:'100%',height:'50%'}">
                </drg-dict-select>
              </template>
            </el-table-column>
            <el-table-column label="移动" fixed="right" align="center" width="100px">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-top"
                           @click="moveUp(scope.row.index,scope.row,value.xyDiseaseList)"></el-button>
                <el-button type="text" icon="el-icon-bottom"
                           @click="moveDown(scope.row.index,scope.row,value.xyDiseaseList)"></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="table-container" style="width: 50%">
          <el-table ref="zyzdbListTable"
                    stripe
                    size="mini"
                    :data="value.zyDiseaseList"
                    :row-class-name="tableRowClassName"
                    style="width: 100%"
                    border>
            <el-table-column label="出院中医诊断" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.c07n2" :disabled=elUploadState() size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="疾病代码（中医）" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.c06c2" :disabled=elUploadState() size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="入院病情（中医）" align="center" width="130px">
              <template slot-scope="scope">
                <el-select v-model="scope.row.c08c2" :disabled=elUploadState() size="mini" placeholder="请选择入院病情">
                  <el-option
                      v-for="item in dictVoList.RYBQ"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="移动" fixed="right" align="center" width="100px">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-top"
                           @click="moveUp(scope.row.index,scope.row,value.zyDiseaseList)"></el-button>
                <el-button type="text" icon="el-icon-bottom"
                           @click="moveDown(scope.row.index,scope.row,value.zyDiseaseList)"></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div style="border: 1px black solid;border-width: 0 0 1px 0;">
        <div style="margin: 10px;display: flex;">
          <div :style="{color:checkData(value.somHiInvyBasInfo.c02n)}" class="fontWeight">诊断代码计数</div>
          <input :readonly="upldStas()" type="text" style="width: 5%" v-model="value.busDiseaseLength"
                 @input="inputChange(value.busDiseaseLength,'c02n')" class="inputStyle">
        </div>
      </div>
      <div class="table-container">
        <el-table ref="ssxxListTable"
                  stripe
                  size="mini"
                  :data="value.busOperateDiagnosisListBa"
                  :row-class-name="tableRowClassName"
                  style="width: 100%"
                  border>
          <el-table-column label="手术及操作名称" align="center">
            <template slot-scope="scope">
              <!--              <el-input v-model="scope.row.c36n" :disabled="true" size="mini"></el-input>-->
              <el-select v-model="scope.row.c36n" :disabled=elUploadState() size="mini" filterable remote
                         placeholder="请输入名称" :remote-method="queryOperateList">
                <el-option
                    v-for="item in operateList"
                    :key="item.icdName"
                    :label="item.icdName"
                    :value="item.icdName">
                  <span style="float: left">{{ item.icdName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.icdCodg }}</span>
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="手术及操作代码" align="center">
            <template slot-scope="scope">
              <!--              <el-input v-model="scope.row.c35c" :disabled="true" size="mini"></el-input>-->
              <el-select v-model="scope.row.c35c" :disabled=elUploadState() size="mini" filterable remote
                         placeholder="请输入编码" :remote-method="queryOperateList">
                <el-option
                    v-for="item in operateList"
                    :key="item.icdCodg"
                    :label="item.icdCodg"
                    :value="item.icdCodg">
                  <span style="float: left">{{ item.icdCodg }}</span>
                  <span style="float: right; color: #8492a6; font-size: 12px">{{ item.icdName }}</span>
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="手术及操作日期" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.oprn_oprt_date" :disabled=elUploadState() ref="oprn_oprt_date" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="麻醉方式" align="center">
            <template slot-scope="scope">
              <!--                <el-input v-model="scope.row.c43" :disabled="true" size="mini"></el-input>-->
              <drg-dict-select dicType="MZFS"
                               placeholder="请选择麻醉方式"
                               v-model="scope.row.c43"
                               :disabled=elUploadState()
                               :type="2"
                               :useClass="false"
                               :selectStyle="{width:'100%'}">
              </drg-dict-select>
            </template>
          </el-table-column>
          <el-table-column label="术者医师姓名" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.oprn_oprt_oper_name" :disabled=elUploadState() ref="oprn_oprt_oper_name" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="术者医师代码" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.c39c" :disabled=elUploadState() ref="c39c" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="麻醉医师姓名" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.oprn_oprt_anst_dr_name" :disabled=elUploadState() ref="oprn_oprt_anst_dr_name" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="麻醉医师代码" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.oprn_oprt_anst_dr_code" :disabled=elUploadState() ref="oprn_oprt_anst_dr_code" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="移动" fixed="right" align="center" width="100px">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-top"
                         @click="moveUp(scope.row.index,scope.row,value.busOperateDiagnosisListBa)"></el-button>
              <el-button type="text" icon="el-icon-bottom"
                         @click="moveDown(scope.row.index,scope.row,value.busOperateDiagnosisListBa)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="border: 1px black solid;border-width: 0 0 1px 0;">
        <div style="margin: 10px;display: flex;">
          <div :style="{color:checkData(value.somHiInvyBasInfo.c02n)}" class="fontWeight">手术及操作代码计数</div>
          <input :readonly="upldStas()" type="text" style="width: 5%" v-model="value.busOperateDiagnosisList.length"
                 ref="ssjs" class="inputStyle">
        </div>
      </div>
      <div style="border: 1px black solid;border-width: 0 0 1px 0;">
        <div style="margin: 10px;display: flex;">
          <div :style="{color:checkData(value.somHiInvyBasInfo.c42)}" class="fontWeight">呼吸机使用时间</div>
          <input :readonly="upldStas()" v-model="value.somHiInvyBasInfo.c42" style="width: 10%;" ref="c42"
                 @input="inputChange(value.somHiInvyBasInfo.c42,'c42')" class="inputStyle">
          <div>天</div>
          <input :readonly="upldStas()" v-model="value.somHiInvyBasInfo.c43" style="width: 10%;" ref="c43"
                 @input="inputChange(value.somHiInvyBasInfo.c43,'c43')" class="inputStyle">
          <div>小时</div>
          <input :readonly="upldStas()" v-model="value.somHiInvyBasInfo.c44" style="width: 10%;" ref="c44"
                 @input="inputChange(value.somHiInvyBasInfo.c44,'c44')" class="inputStyle">
          <div>分钟</div>
        </div>
      </div>
      <div style="border: 1px black solid;border-width: 0 0 1px 0;">
        <div style="margin: 10px;display: flex">
          <div :style="{color:checkData(value.somHiInvyBasInfo.c28)}" class="fontWeight">颅脑损伤患者昏迷时间</div>
          <div class="fontWeight">入院前</div>
          <input v-model="value.somHiInvyBasInfo.c28" style="width: 10%" ref="c28"
                 @input="inputChange(value.somHiInvyBasInfo.c28,'c28')" class="inputStyle">
          <div>天</div>
          <input v-model="value.somHiInvyBasInfo.c29" style="width: 10%" ref="c29"
                 @input="inputChange(value.somHiInvyBasInfo.c29,'c29')" class="inputStyle">
          <div>小时</div>
          <input v-model="value.somHiInvyBasInfo.c30" style="width: 10%" ref="c30"
                 @input="inputChange(value.somHiInvyBasInfo.c30,'c30')" class="inputStyle">
          <div>分钟</div>
        </div>
        <div style="margin: 10px;display: flex">
          <div class="fontWeight" style="padding-left: 9.5%;">入院后</div>
          <input v-model="value.somHiInvyBasInfo.c31" style="width: 10%" ref="c31"
                 @input="inputChange(value.somHiInvyBasInfo.c31,'c31')" class="inputStyle">
          <div>天</div>
          <input v-model="value.somHiInvyBasInfo.c32" style="width: 10%" ref="c32"
                 @input="inputChange(value.somHiInvyBasInfo.c32,'c32')" class="inputStyle">
          <div>小时</div>
          <input v-model="value.somHiInvyBasInfo.c33" style="width: 10%" ref="c33"
                 @input="inputChange(value.somHiInvyBasInfo.c33,'c33')" class="inputStyle">
          <div>分钟</div>
        </div>
      </div>
      <div class="table-container">
        <el-table ref="zzjhxxListTable"
                  size="mini"
                  stripe
                  :data="value.busIcuListBa"
                  style="width: 100%"
                  border>
          <el-table-column label="重症监护病房类型（CCU、NICU、EICU、SICU、PICU、RICU、其他）" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.b40" :disabled=elUploadState() size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="进重症监护室时间（_年_月_日_时_分）" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.b41" :disabled=elUploadState() size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="出重症监护室时间（_年_月_日_时_分）" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.b42" :disabled=elUploadState() size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="合计（小时）" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.b43" :disabled=elUploadState() size="mini"></el-input>
            </template>
          </el-table-column>
          <!--          <el-table-column label="重症监护病房类型（CCU、NICU、EICU、SICU、PICU、RICU、其他）"  align="center">-->
          <!--            <template slot-scope="scope">{{scope.row.b40 | formatIsEmpty}}</template>-->
          <!--          </el-table-column>-->
          <!--          <el-table-column label="进重症监护室时间（_年_月_日_时_分）"  align="center">-->
          <!--            <template slot-scope="scope">{{scope.row.b41 | formatDateFormat}}</template>-->
          <!--          </el-table-column>-->
          <!--          <el-table-column label="出重症监护室时间（_年_月_日_时_分）"  align="center" >-->
          <!--            <template slot-scope="scope">{{scope.row.b42 | formatIsEmpty}}</template>-->
          <!--          </el-table-column>-->
          <!--          <el-table-column label="合计（小时）" width="100" align="center" >-->
          <!--            <template slot-scope="scope">{{scope.row.b43 | formatIsEmpty}}</template>-->
          <!--          </el-table-column>-->
        </el-table>
      </div>
      <div v-for="(maxDiv,endIndex) in beInHospitalEndList" :key="'d'+endIndex" :style="maxDiv.style">
        <div v-for="(item ,beInHosEndIndex) in beInHospitalEndList[endIndex].allValue" :key="item.name+beInHosEndIndex"
             :style="item.outDivStyle">
          <div :style="item.innerDivStyle">{{ item.name }}</div>
          <input :readonly="upldStas()" :type="item.inputType" v-model="item.value" :ref="item.ref"
                 :style="item.inputStyle" @input="inputChange(item.value,item.ref,item.name)">
          <div v-if="item.addDiv==1">{{ item.explain }}</div>
        </div>
      </div>
      <div class="subtitleStyle">四、医疗收费信息</div>
      <div style="border: 1px black solid;border-width: 0 0 1px 0;">
        <div style="display: flex">
          <div style="display: inline;width: 30%;margin: 10px;">
            <div style="display: flex">
              <div :style="{color:checkData(value.somHiInvyBasInfo.d35)}">业务流水号</div>
              <input :readonly="upldStas()" v-model="value.somHiInvyBasInfo.d35" ref="d35"
                     @input="inputChange(value.somHiInvyBasInfo.d35,'d35')" class="inputStyle">
            </div>
            <div style="display: flex;padding-top: 10px">
              <div :style="{color:checkData(value.somHiInvyBasInfo.d38)}">票据代码</div>
              <input :readonly="upldStas()" v-model="value.somHiInvyBasInfo.d38" ref="d38"
                     @input="inputChange(value.somHiInvyBasInfo.d38,'d38')" class="inputStyle">
            </div>
            <div style="display: flex;padding-top: 10px">
              <div :style="{color:checkData(value.somHiInvyBasInfo.d39)}">票据号码</div>
              <input :readonly="upldStas()" v-model="value.somHiInvyBasInfo.d39" ref="d39"
                     @input="inputChange(value.somHiInvyBasInfo.d39,'d39')" class="inputStyle">
            </div>
          </div>
          <div style="width: 70%;border: 1px black solid;border-width: 0 0 0 1px;display: -webkit-flex">
            <div style="display: flex;margin: auto;">
              <div :style="{color:checkData(value.somHiInvyBasInfo.d36)}">结算期间</div>
              <input :readonly="upldStas()" v-model="value.somHiInvyBasInfo.d36" ref="d36"
                     @input="inputChange(value.somHiInvyBasInfo.d36,'d36')" class="inputStyle">
              <div>-</div>
              <input :readonly="upldStas()" v-model="value.somHiInvyBasInfo.d37" ref="d37"
                     @input="inputChange(value.somHiInvyBasInfo.d37,'d37')" class="inputStyle">
            </div>
          </div>
        </div>
      </div>
      <div>
        <el-table ref="medicalCostTable"
                  size="mini"
                  stripe
                  :data="busMedicalCostList"
                  style="width: 100%"
                  border>
          <el-table-column label="项目名称" align="center">
            <template slot-scope="scope">{{ scope.row.medChrgItemname | formatIsEmpty }}</template>
          </el-table-column>
          <el-table-column label="金额" align="center">
            <template slot-scope="scope">{{ scope.row.amt | formatIsEmpty }}</template>
          </el-table-column>
          <el-table-column label="甲类" align="center">
            <template slot-scope="scope">{{ scope.row.claa | formatIsEmpty }}</template>
          </el-table-column>
          <el-table-column label="乙类" width="100" align="center">
            <template slot-scope="scope">{{ scope.row.clab | formatIsEmpty }}</template>
          </el-table-column>
          <el-table-column label="自费" align="center">
            <template slot-scope="scope">{{ scope.row.ownpay | formatIsEmpty }}</template>
          </el-table-column>
          <el-table-column label="其他" align="center">
            <template slot-scope="scope">{{ scope.row.oth | formatIsEmpty }}</template>
          </el-table-column>
        </el-table>
      </div>
      <div style="display: flex">
        <div style="width: 60%">
          <el-table ref="fundPayTable"
                    size="mini"
                    stripe
                    :data="value.busFundPayList"
                    max-height="180"
                    style="width: 100%"
                    :row-style="{height:0+'px'}"
                    :cell-style="{padding:0+'px'}"
                    :span-method="objectSpanMethod2"
                    border>
            <el-table-column label="基金支付信息" align="center">
              <template>{{ "基金支付" }}</template>
            </el-table-column>
            <el-table-column label="基金支付类型" prop="fundPayType" align="center">
              <template slot-scope="scope">{{ scope.row.fundPayType | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="金额" prop="fundPayAmt" align="center">
              <template slot-scope="scope">{{ scope.row.fundPayamt | formatIsEmpty }}</template>
            </el-table-column>
          </el-table>
        </div>
        <div style="width: 40%">
          <el-table
              size="mini"
              :data="value.zftableData"
              style="width: 100%"
              :row-style="{height:10+'px'}"
              :span-method="objectSpanMethod"
              border>
            <!--            <el-table-column label="个人支付信息"  align="center" >-->
            <el-table-column label="个人支付" prop="col1" align="center" width="100"></el-table-column>
            <el-table-column label="个人支付类型" prop="col2" align="center"></el-table-column>
            <el-table-column label="金额" prop="col3" align="center"></el-table-column>
            <!--            </el-table-column>-->
          </el-table>
        </div>
      </div>
      <div style="border: 1px black solid;border-width: 0 0 1px 0;">
        <div style="margin: 10px;display: flex">
          <div :style="{color:checkData(value.somHiInvyBasInfo.d58)}">医保支付方式</div>
          <input :readonly="upldStas()" type="text" v-model="value.somHiInvyBasInfo.d58" ref="d58"
                 @input="inputChange(value.somHiInvyBasInfo.d58,'d58')" class="numInputStyle">
          <div style="display: flex">
            <div style="padding-left: 20px">1、按项目</div>
            <div style="padding-left: 20px">2、单病种</div>
            <div style="padding-left: 20px">3、按病种分值</div>
            <div style="padding-left: 20px">4、疾病诊断相关分组（DRG）</div>
            <div style="padding-left: 20px">5、按床日</div>
            <div style="padding-left: 20px">6、按人头......</div>
          </div>
        </div>
      </div>
      <div class="divMargin">
        <div style="display: flex;width: 70%">
          <div :style="{color:checkData(value.somHiInvyBasInfo.d59)}">医疗机构填报部门</div>
          <input :readonly="upldStas()" type="text" v-model="value.somHiInvyBasInfo.d59" ref="d59"
                 @input="inputChange(value.somHiInvyBasInfo.d59,'d59')" class="inputStyle">
        </div>
        <div style="display: flex;width: 30%">
          <div style="padding-left: 39%;">医保机构</div>
          <input :readonly="upldStas()" type="text" style="width: 20%" ref="medinsorgan" class="inputStyle">
        </div>
      </div>
      <div class="divMargin">
        <div style="display: flex;width: 69.9%">
          <div :style="{color:checkData(value.somHiInvyBasInfo.d60)}">医疗机构填报人</div>
          <input :readonly="upldStas()" type="text" v-model="value.somHiInvyBasInfo.d60" ref="d60"
                 @input="inputChange(value.somHiInvyBasInfo.d60,'d60')" class="inputStyle">
        </div>
        <div style="display: flex;width: 29%">
          <div style="padding-left: 40.5%;">医保机构经办人</div>
          <input :readonly="upldStas()" type="text" class="inputStyle" ref="medinsorganoperator" style="width: 14%">
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { queryLikeIcdsByPram } from '@/api/common/drgCommon'
import { preGroup, restartCheck } from '@/api/medicalQuality/settleListDetail'

export default {
  name: 'settleListInfo',
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      b13c: null,
      b16c: null,
      b21c: null,
      diagnosisList: {}, // 诊断
      operateList: {}, // 手术
      titleHeadList: [],
      basicInfoList: [],
      beInHospital: [],
      beInHospitalEndList: [],
      modifyContent: [],
      historicalData: [],
      refs: [],
      changeOperationList: [],
      changeXyDiseaseList: [],
      changeZyDiseaseList: [],
      changeOutpatientList: [],
      changeBusIcuList: [],
      url: '',
      restart: {},
      sortable: null,
      newList: [],
      checkedGh: []
    }
  },
  props: {
    value: Object,
    // busMedicalCostList: Object
    busMedicalCostList: {
      type: Array
      // default: () => []
    },
    busSettleLErrorList: {
      type: Array
      // default: () => []
    },
    storeOperationList: {
      type: Array
      // default: () => []
    },
    storeXyDiseaseList: {
      type: Array
      // default: () => []
    },
    storeZyDiseaseList: {
      type: Array
      // default: () => []
    },
    storeOutpatientList: {
      type: Array
      // default: () => []
    },
    storeBusIcuList: {
      type: Array
      // default: () => []
    },
    deleteWatchData: String,
    data: Object
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatDateFormat (value) {
      if (value && value != '-') {
        let updt_date = new Date(value)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    }
  },
  mounted () {
    this.setSettleListData(this.value)
  },
  methods: {
    // 设置数据
    setSettleListData (val) {
      // if(val){
      this.value = val
      // }
      if (this.value) {
        this.titleHeadList = [
          {
            style: {},
            allValue: [
              {
                name: '清单流水号',
                value: '',
                maxDivStyle: '',
                outDivStyle: { display: 'flex', marginLeft: '74.2%', paddingTop: '3%', width: '20%' },
                innerDivStyle: {},
                inputStyle: {
                  width: '57%',
                  border: 'none',
                  borderBottom: '1px black solid'
                },
                ref: 'a58'
              }
            ]
          },
          {
            style: { display: 'flex', paddingTop: '5px', width: '100%' },
            allValue: [
              {
                name: '定点医疗机构名称',
                value: this.value.somHiInvyBasInfo.a02,
                outDivStyle: { width: '35%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '30%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a02'
              },
              {
                name: '定点医疗机构代码',
                value: this.value.somHiInvyBasInfo.a01,
                outDivStyle: { width: '40%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '25%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a01'
              },
              {
                name: '医保结算等级',
                value: this.value.somHiInvyBasInfo.a50,
                outDivStyle: { width: '26%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '43%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a50'
              }
            ]
          },
          {
            style: { display: 'flex', paddingTop: '5px', width: '100%' },
            allValue: [
              {
                name: '医保编号',
                value: this.value.somHiInvyBasInfo.a51,
                outDivStyle: { width: '35%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '41.5%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a51'
              },
              {
                name: '病案号',
                value: this.value.somHiInvyBasInfo.a48,
                outDivStyle: { width: '40%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '36.5%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a48'
              },
              {
                name: '申报时间',
                value: this.value.somHiInvyBasInfo.a52,
                outDivStyle: { width: '26%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '51%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a52'
              }
            ]
          }
        ]
        this.basicInfoList = [
          {
            style: { display: 'flex', margin: '10px' },
            allValue: [
              {
                name: '姓名',
                value: this.value.somHiInvyBasInfo.a11,
                outDivStyle: { width: '20%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a11'
              },
              {
                name: '性别',
                value: this.value.somHiInvyBasInfo.a12c,
                outDivStyle: { width: '20%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { height: '17px', width: '17px' },
                inputType: 'text',
                ref: 'a12c',
                explain: '1、男  2、女',
                addDiv: '1',
                jpDictType: '1',
                dicType: 'SEX'
              },
              {
                name: '出生日期',
                value: this.value.somHiInvyBasInfo.a13,
                outDivStyle: { width: '20%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a13'
              },
              {
                name: '年龄',
                value: this.value.somHiInvyBasInfo.a14,
                outDivStyle: { width: '20%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '20%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a14',
                explain: '岁',
                addDiv: '1'
              },
              {
                name: '国籍',
                value: this.value.somHiInvyBasInfo.a15c,
                outDivStyle: { width: '20%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '40%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a15c'
              }
            ]
          },
          {
            style: { display: 'flex', margin: '10px' },
            allValue: [
              {
                name: '(年龄不足1周岁的)年龄',
                value: this.value.somHiInvyBasInfo.a16,
                outDivStyle: { width: '25%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '10%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a16',
                explain: '天',
                addDiv: '1'
              },
              {
                name: '民族',
                value: this.value.somHiInvyBasInfo.a19c,
                outDivStyle: { width: '20%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '40%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a19c'
              },
              {
                name: '患者证件类别',
                value: this.value.somHiInvyBasInfo.a53,
                outDivStyle: { width: '25%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '25%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a53'
              },
              {
                name: '患者证件号码',
                value: this.value.somHiInvyBasInfo.a20,
                outDivStyle: { width: '30%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '60%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a20'
              }
            ]
          },
          {
            style: { display: 'flex', margin: '10px' },
            allValue: [
              {
                name: '职业',
                value: this.value.somHiInvyBasInfo.a38c,
                outDivStyle: { width: '30%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '20%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a38c'
              },
              {
                name: '现住址',
                value: this.value.somHiInvyBasInfo.a26,
                outDivStyle: { width: '30%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '80%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a26'
              }
            ]
          },
          {
            style: { display: 'flex', margin: '10px' },
            allValue: [
              {
                name: '工作单位名称',
                value: this.value.somHiInvyBasInfo.a29n,
                outDivStyle: { width: '30%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '60%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a29n'
              },
              {
                name: '工作单位地址',
                value: this.value.somHiInvyBasInfo.a29,
                outDivStyle: { width: '35%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '60%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a29'
              },
              {
                name: '单位电话',
                value: this.value.somHiInvyBasInfo.a30,
                outDivStyle: { width: '20%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '55%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a30'
              },
              {
                name: '邮编',
                value: this.value.somHiInvyBasInfo.a31c,
                outDivStyle: { width: '10%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '60%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a31c'
              }
            ]
          },
          {
            style: { display: 'flex', margin: '10px' },
            allValue: [
              {
                name: '联系人姓名',
                value: this.value.somHiInvyBasInfo.a32,
                outDivStyle: { width: '20%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '60%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a32'
              },
              {
                name: '关系',
                value: this.value.somHiInvyBasInfo.a33c,
                outDivStyle: { width: '10%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a33c'
              },
              {
                name: '地址',
                value: this.value.somHiInvyBasInfo.a34,
                outDivStyle: { width: '45%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '60%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a34'
              },
              {
                name: '电话',
                value: this.value.somHiInvyBasInfo.a35,
                outDivStyle: { width: '20%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '55%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a35'
              }
            ]
          },
          {
            style: { display: 'flex', margin: '10px' },
            allValue: [
              {
                name: '医保类型',
                value: this.value.somHiInvyBasInfo.a54,
                outDivStyle: { width: '30%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '45%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a54'
              },
              {
                name: '特殊人员类型',
                value: this.value.somHiInvyBasInfo.a55,
                outDivStyle: { width: '30%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '40%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a55'
              },
              {
                name: '参保地',
                value: this.value.somHiInvyBasInfo.a56,
                outDivStyle: { width: '30%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a56'
              }
            ]
          },
          {
            style: { display: 'flex', margin: '10px' },
            allValue: [
              {
                name: '新生儿入院类型',
                value: this.value.somHiInvyBasInfo.a57,
                outDivStyle: { width: '30%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '40%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a57'
              },
              {
                name: '新生儿出生体重',
                value: this.value.somHiInvyBasInfo.a18,
                outDivStyle: { width: '30%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '20%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a18',
                explain: '克',
                addDiv: '1'
              },
              {
                name: '新生儿入院体重',
                value: this.value.somHiInvyBasInfo.a17,
                outDivStyle: { width: '30%', display: 'flex' },
                innerDivStyle: {},
                inputStyle: { width: '20%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'a17',
                explain: '克',
                addDiv: '1'
              }
            ]
          }
        ]
        this.beInHospital = [
          {
            style: { border: '1px black solid', borderWidth: '0 0 1px 0', display: 'flex' },
            allValue: [
              {
                name: '入院时间',
                value: this.value.somHiInvyBasInfo.b12,
                outDivStyle: { display: 'flex', margin: '10px', width: '30%' },
                innerDivStyle: {},
                inputStyle: { width: '70%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b12'
              },
              {
                name: '入院科别',
                value: this.value.somHiInvyBasInfo.b13c,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '70%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b13c'
              },
              {
                name: '病房',
                value: this.value.somHiInvyBasInfo.b14n,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '54%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b14n'
              },
              {
                name: '转科科别',
                value: this.value.somHiInvyBasInfo.b21c,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b21c'
              }
            ]
          },
          {
            style: { border: '1px black solid', borderWidth: '0 0 1px 0', display: 'flex' },
            allValue: [
              {
                name: '出院时间',
                value: this.value.somHiInvyBasInfo.b15,
                outDivStyle: { display: 'flex', margin: '10px', width: '30%' },
                innerDivStyle: {},
                inputStyle: { width: '70%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b15'
              },
              {
                name: '出院科别',
                value: this.value.somHiInvyBasInfo.b16c,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '70%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b16c'
              },
              {
                name: '病房',
                value: this.value.somHiInvyBasInfo.b17n,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '54%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b17n'
              },
              {
                name: '实际住院',
                value: this.value.somHiInvyBasInfo.b20,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b20',
                explain: '天',
                addDiv: '1'
              }
            ]
          },
          {
            style: { border: '1px black solid', borderWidth: '0 0 1px 0', display: 'flex' },
            allValue: [
              {
                name: '门（急）诊诊断（中医诊断）',
                value: this.value.somHiInvyBasInfo.c36n,
                outDivStyle: { display: 'flex', margin: '10px', width: '60%' },
                innerDivStyle: {},
                inputStyle: { width: '70%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'c36n'
              },
              {
                name: '疾病编码',
                value: this.value.somHiInvyBasInfo.c35c,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '70%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'c35c'
              }
            ]
          },
          {
            style: { border: '1px black solid', borderWidth: '0 0 1px 0', display: 'flex' },
            allValue: [
              {
                name: '门（急）诊诊断（西医诊断）',
                value: this.value.somHiInvyBasInfo.c02n,
                outDivStyle: { display: 'flex', margin: '10px', width: '60%' },
                innerDivStyle: {},
                inputStyle: { width: '70%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'c02n'
              },
              {
                name: '疾病编码',
                value: this.value.somHiInvyBasInfo.c01c,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '70%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'c01c'
              }
            ]
          }
        ]
        this.beInHospitalEndList = [
          {
            style: { border: '1px black solid', borderWidth: '0 0 1px 0', display: 'flex' },
            allValue: [
              {
                name: '输血品种',
                value: this.value.somHiInvyBasInfo.c45,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'c45'
              },
              {
                name: '输血量',
                value: this.value.somHiInvyBasInfo.c46,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'c46'
              },
              {
                name: '输血计量单位',
                value: this.value.somHiInvyBasInfo.c47,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'c47'
              }
            ]
          },
          {
            style: { border: '1px black solid', borderWidth: '0 0 1px 0', display: 'flex' },
            allValue: [
              {
                name: '特殊级护理天数',
                value: this.value.somHiInvyBasInfo.b44,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b44'
              },
              {
                name: '一级护理天数',
                value: this.value.somHiInvyBasInfo.b45,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b45'
              },
              {
                name: '二级护理天数',
                value: this.value.somHiInvyBasInfo.b46,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b46'
              },
              {
                name: '三级护理天数',
                value: this.value.somHiInvyBasInfo.b47,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b47'
              }
            ]
          },
          {
            style: { border: '1px black solid', borderWidth: '0 0 1px 0', display: 'flex' },
            allValue: [
              {
                name: '离院方式',
                value: this.value.somHiInvyBasInfo.b34c,
                outDivStyle: { display: 'flex', margin: '10px' },
                innerDivStyle: {},
                inputStyle: { height: '17px', width: '17px' },
                inputType: 'text',
                ref: 'b34c',
                explain: '1、医嘱离院 2、医嘱转院，拟接收机构名称  3、转社区、转卫生院机构，拟接收机构名称 4、非医嘱离院 5、死亡 9、其他',
                addDiv: '1'
              }
            ]
          },
          {
            style: { border: '1px black solid', borderWidth: '0 0 1px 0', display: 'flex' },
            allValue: [
              {
                name: '是否有出院31天内再住院计划',
                value: this.value.somHiInvyBasInfo.b36c,
                outDivStyle: { display: 'flex', margin: '10px', width: '30%' },
                innerDivStyle: {},
                inputStyle: { width: '50%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b36c'
              },
              {
                name: '目的',
                value: this.value.somHiInvyBasInfo.b37,
                outDivStyle: { display: 'flex', margin: '10px', width: '40%' },
                innerDivStyle: {},
                inputStyle: { width: '70%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b37'
              }
            ]
          },
          {
            style: { border: '1px black solid', borderWidth: '0 0 1px 0', display: 'flex' },
            allValue: [
              {
                name: '主治医师姓名',
                value: this.value.somHiInvyBasInfo.b52n,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '40%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b52n'
              },
              {
                name: '主治医师代码',
                value: this.value.somHiInvyBasInfo.b51c,
                outDivStyle: { display: 'flex', margin: '10px', width: '20%' },
                innerDivStyle: {},
                inputStyle: { width: '40%', border: 'none', borderBottom: '1px black solid' },
                inputType: 'text',
                ref: 'b51c'
              }
            ]
          }
        ]
        this.refs = [
          { ref: 'b38' }, { ref: 'b11c' }, { ref: 'b39' }, { ref: 'ssjs' }, { ref: 'c37' }, { ref: 'c39n' },
          { ref: 'c39c' }, { ref: 'c44n' }, { ref: 'c44c' }, { ref: 'c42' }, { ref: 'c43' }, { ref: 'c44' },
          { ref: 'c28' }, { ref: 'c29' }, { ref: 'c30' }, { ref: 'c31' }, { ref: 'c32' }, { ref: 'c33' },
          { ref: 'd35' }, { ref: 'd38' }, { ref: 'd39' }, { ref: 'd36' }, { ref: 'd37' }, { ref: 'd58' },
          { ref: 'd59' }, { ref: 'd60' }
        ]
        this.refs0 = [
          { ref: 'a02' }, { ref: 'a50' }, { ref: 'a51' }, { ref: 'a48' }, { ref: 'b51c' },
          { ref: 'a52' }, { ref: 'a11' }, { ref: 'a12c' }, { ref: 'a13' }, { ref: 'a14' }, { ref: 'a15c' },
          { ref: 'a16' }, { ref: 'a19c' }, { ref: 'a53' }, { ref: 'a20' }, { ref: 'a38c' }, { ref: 'a26' },
          { ref: 'a29n' }, { ref: 'a29' }, { ref: 'a30' }, { ref: 'a31c' }, { ref: 'a32' }, { ref: 'a33c' },
          { ref: 'a34' }, { ref: 'a35' }, { ref: 'a54' }, { ref: 'a55' }, { ref: 'a56' }, { ref: 'a57' },
          { ref: 'a18' }, { ref: 'a17' }, { ref: 'b12' }, { ref: 'b13n' }, { ref: 'b14n' }, { ref: 'b21c' },
          { ref: 'b15' }, { ref: 'b16n' }, { ref: 'b17n' }, { ref: 'b20' }, { ref: 'c36n' }, { ref: 'c35c' },
          { ref: 'c01c' }, { ref: 'c45' }, { ref: 'c46' }, { ref: 'b44' }, { ref: 'b45' }, { ref: 'c02n' },
          { ref: 'b46' }, { ref: 'b47' }, { ref: 'b34c' }, { ref: 'b36c' }, { ref: 'b37' }, { ref: 'b52n' }
        ]
        this.getRawData()
      }
    },
    // 查询所有手术
    queryOperateList (queryString) {
      const param = {
        likeQueryString: queryString,
        icd_type: 'ICD-9', // 只查询手术信息
        icd_codg_ver: '5' // 只查询医保版本编码
      }
      queryLikeIcdsByPram(param).then(response => {
        this.operateList = response.data
      })
    },
    // 查询所有疾病代码
    queryDiagnosisList (queryString) {
      const param = {
        likeQueryString: queryString,
        icd_type: 'ICD-10', // 只查询疾病信息
        icd_codg_ver: '5' // 只查询医保版本编码
      }
      queryLikeIcdsByPram(param).then(response => {
        this.diagnosisList = response.data
      })
    },
    // 获取行的index
    tableRowClassName ({ row, rowIndex }) {
      row.index = rowIndex
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      // 表列合并
      if (columnIndex === 0) {
        if (rowIndex % 4 === 0) {
          return {
            rowspan: 5,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    objectSpanMethod2 ({ row, column, rowIndex, columnIndex }) {
      // 表列合并
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return {
            rowspan: 9,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    // 上移动
    moveUp (index, row, tableData) {
      if (index > 0 && this.value.somHiInvyBasInfo.uploadFlag != '1') {
        const upDate = tableData[index - 1]
        tableData.splice(index - 1, 1)
        tableData.splice(index, 0, upDate)
        // if(row.c06c1){
        //   this.modifyContent.push(
        //     {
        //       zd:'疾病代码'+row.c06c1,
        //       old:"原本是第"+(index+1)+"行",
        //       new:'现在已上移'
        //     }
        //   )
        // }else if(row.c35c){
        //   this.modifyContent.push(
        //     {
        //       zd:'手术及操作代码'+row.c35c,
        //       old:"原本是第"+(index+1)+"行",
        //       new:'现在已上移'
        //     }
        //   )
        // }
        this.$emit('showChange-Message', this.modifyContent)
      } else {
        this.$message({ message: '已经是第一条，不可上移', type: 'warning' })
      }
    },
    // 下移动
    moveDown (index, row, tableData) {
      if (index + 1 === tableData.length || this.value.somHiInvyBasInfo.uploadFlag == '1') {
        if (index + 1 === tableData.length) {
          this.$message({ message: '已经是最后一条，不可下移', type: 'warning' })
        }
      } else {
        const downDate = tableData[index + 1]
        tableData.splice(index + 1, 1)
        tableData.splice(index, 0, downDate)
        // if(row.c06c1){
        //   this.modifyContent.push(
        //     {
        //       zd:'疾病代码'+row.c06c1,
        //       old:"原本是第"+(index+1)+"行",
        //       new:'现在已下移'
        //     }
        //   )
        // }else if(row.c35c){
        //   this.modifyContent.push(
        //     {
        //       zd:'手术及操作代码'+row.c35c,
        //       old:"原本是第"+(index+1)+"行",
        //       new:'现在已下移'
        //     }
        //   )
        // }
        this.$emit('showChange-Message', this.modifyContent)
      }
    },
    checkData (type) {
      // if(value != '' && value != undefined || value ===0){
      // if(type==1){
      return 'black'
      // }else {
      //   return 'black'
      // }
    },
    // 备份数据
    getRawData () {
      this.$nextTick(() => {
        this.refs.forEach(items => {
          if (this.$refs[items.ref] != undefined) {
            let value = this.$refs[items.ref].value
            let ref = items.ref
            this.historicalData.push(
              {
                zd: ref,
                value: value
              }
            )
          }
        })
        this.refs0.forEach(items0 => {
          if (this.$refs[items0.ref] != undefined) {
            let value = this.$refs[items0.ref][0].value
            let ref = items0.ref
            this.historicalData.push(
              {
                zd: ref,
                value: value
              }
            )
          }
        })
      })
    },
    changeRed () {
      this.$nextTick(() => {
        if (this.busSettleLErrorList) {
          this.busSettleLErrorList.forEach(itemMax => {
            itemMax.forEach(item => {
              if (this.$refs[item.fld] != undefined && this.$refs[item.fld][0] != undefined) {
                // this.$refs[item.fld][0].focus()
                this.$refs[item.fld][0].style.borderBottomColor = 'red'
              }
              if (this.$refs[item.fld] != undefined && this.$refs[item.fld][0] == undefined) {
                // this.$refs[item.fld].focus()
                this.$refs[item.fld].style.borderBottomColor = 'red'
              }
            })
          })
        }
      })
    },
    // 改变颜色
    checkGb (val) {
      this.$nextTick(() => {
        if (this.$refs[val] != undefined) {
          this.$refs[val][0].focus()
          this.$refs[val][0].style.borderBottomColor = 'red'
        }
      })
    },
    // 手术改变记录
    operationListChange () {
      for (let i = 0; i < this.storeOperationList.length; i++) {
        // for (let j = 0; j < this.value.busOperateDiagnosisListBa; j++) {
        // 手术编码
        if (this.storeOperationList[i].c35c != this.changeOperationList[i].c35c) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '手术') {
                item.after = this.changeOperationList[i].c35c
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '手术',
                old: this.storeOperationList[i].c35c,
                after: this.changeOperationList[i].c35c,
                name: '第' + (i + 1) + '手术'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '手术',
              old: this.storeOperationList[i].c35c,
              after: this.changeOperationList[i].c35c,
              name: '第' + (i + 1) + '手术'
            }
          )
          // }
        }
        // 手术及操作名称
        if (this.storeOperationList[i].c36n != this.changeOperationList[i].c36n) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '手术名称') {
                item.after = this.changeOperationList[i].c36n
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '手术名称',
                old: this.storeOperationList[i].c36n,
                after: this.changeOperationList[i].c36n,
                name: '第' + (i + 1) + '手术名称'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '手术名称',
              old: this.storeOperationList[i].c36n,
              after: this.changeOperationList[i].c36n,
              name: '第' + (i + 1) + '手术名称'
            }
          )
          // }
        }
        // 手术及操作日期
        if (this.storeOperationList[i].oprn_oprt_date != this.changeOperationList[i].oprn_oprt_date) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '手术操作日期') {
                item.after = this.changeOperationList[i].oprn_oprt_date
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '手术操作日期',
                old: this.storeOperationList[i].oprn_oprt_date,
                after: this.changeOperationList[i].oprn_oprt_date,
                name: '第' + (i + 1) + '手术操作日期'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '手术操作日期',
              old: this.storeOperationList[i].oprn_oprt_date,
              after: this.changeOperationList[i].oprn_oprt_date,
              name: '第' + (i + 1) + '手术操作日期'
            }
          )
          // }
        }
        // 麻醉方式
        if (this.storeOperationList[i].c43 != this.changeOperationList[i].c43) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '麻醉方式') {
                item.after = this.changeOperationList[i].c43
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '麻醉方式',
                old: this.storeOperationList[i].c43,
                after: this.changeOperationList[i].c43,
                name: '第' + (i + 1) + '麻醉方式'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '麻醉方式',
              old: this.storeOperationList[i].c43,
              after: this.changeOperationList[i].c43,
              name: '第' + (i + 1) + '麻醉方式'
            }
          )
          // }
        }
        // 术者医师姓名
        if (this.storeOperationList[i].oprn_oprt_oper_name != this.changeOperationList[i].oprn_oprt_oper_name) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '术者医师姓名') {
                item.after = this.changeOperationList[i].oprn_oprt_oper_name
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '术者医师姓名',
                old: this.storeOperationList[i].oprn_oprt_oper_name,
                after: this.changeOperationList[i].oprn_oprt_oper_name,
                name: '第' + (i + 1) + '术者医师姓名'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '术者医师姓名',
              old: this.storeOperationList[i].oprn_oprt_oper_name,
              after: this.changeOperationList[i].oprn_oprt_oper_name,
              name: '第' + (i + 1) + '术者医师姓名'
            }
          )
          // }
        }
        // 术者医师代码
        if (this.storeOperationList[i].c39c != this.changeOperationList[i].c39c) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '术者医师代码') {
                item.after = this.changeOperationList[i].c39c
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '术者医师代码',
                old: this.storeOperationList[i].c39c,
                after: this.changeOperationList[i].c39c,
                name: '第' + (i + 1) + '术者医师代码'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '术者医师代码',
              old: this.storeOperationList[i].c39c,
              after: this.changeOperationList[i].c39c,
              name: '第' + (i + 1) + '术者医师代码'
            }
          )
          // }
        }
        // 麻醉医师姓名
        if (this.storeOperationList[i].oprn_oprt_anst_dr_name != this.changeOperationList[i].oprn_oprt_anst_dr_name) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '麻醉医师姓名') {
                item.after = this.changeOperationList[i].oprn_oprt_anst_dr_name
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '麻醉医师姓名',
                old: this.storeOperationList[i].oprn_oprt_anst_dr_name,
                after: this.changeOperationList[i].oprn_oprt_anst_dr_name,
                name: '第' + (i + 1) + '麻醉医师姓名'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '麻醉医师姓名',
              old: this.storeOperationList[i].oprn_oprt_anst_dr_name,
              after: this.changeOperationList[i].oprn_oprt_anst_dr_name,
              name: '第' + (i + 1) + '麻醉医师姓名'
            }
          )
          // }
        }
        // 麻醉医师代码
        if (this.storeOperationList[i].oprn_oprt_anst_dr_code != this.changeOperationList[i].oprn_oprt_anst_dr_code) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '麻醉医师代码') {
                item.after = this.changeOperationList[i].oprn_oprt_anst_dr_code
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '麻醉医师代码',
                old: this.storeOperationList[i].oprn_oprt_anst_dr_code,
                after: this.changeOperationList[i].oprn_oprt_anst_dr_code,
                name: '第' + (i + 1) + '麻醉医师代码'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '麻醉医师代码',
              old: this.storeOperationList[i].oprn_oprt_anst_dr_code,
              after: this.changeOperationList[i].oprn_oprt_anst_dr_code,
              name: '第' + (i + 1) + '麻醉医师代码'
            }
          )
          // }
        }
      }
      this.$emit('showChange-Message', this.modifyContent)
    },
    // 西医诊断改变记录
    xyDiseaseListChange () {
      for (let i = 0; i < this.storeXyDiseaseList.length; i++) {
        // for (let j = 0; j < this.value.busOperateDiagnosisListBa; j++) {
        // 西医诊断编码
        if (this.storeXyDiseaseList[i].c06c1 != this.changeXyDiseaseList[i].c06c1) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '西医诊断代码') {
                item.after = this.changeXyDiseaseList[i].c06c1
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '西医诊断代码',
                old: this.storeXyDiseaseList[i].c06c1,
                after: this.changeXyDiseaseList[i].c06c1,
                name: '第' + (i + 1) + '西医诊断代码'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '西医诊断代码',
              old: this.storeXyDiseaseList[i].c06c1,
              after: this.changeXyDiseaseList[i].c06c1,
              name: '第' + (i + 1) + '西医诊断代码'
            }
          )
          // }
        }
        // 西医诊断名称
        if (this.storeXyDiseaseList[i].c07n1 != this.changeXyDiseaseList[i].c07n1) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '西医诊断名称') {
                item.after = this.changeXyDiseaseList[i].c07n1
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '西医诊断名称',
                old: this.storeXyDiseaseList[i].c07n1,
                after: this.changeXyDiseaseList[i].c07n1,
                name: '第' + (i + 1) + '西医诊断名称'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '西医诊断名称',
              old: this.storeXyDiseaseList[i].c07n1,
              after: this.changeXyDiseaseList[i].c07n1,
              name: '第' + (i + 1) + '西医诊断名称'
            }
          )
          // }
        }
        // 西医入院病情
        if (this.storeXyDiseaseList[i].c08c1 != this.changeXyDiseaseList[i].c08c1) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '西医入院病情') {
                item.after = this.changeXyDiseaseList[i].c08c1
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '西医入院病情',
                old: this.storeXyDiseaseList[i].c08c1,
                after: this.changeXyDiseaseList[i].c08c1,
                name: '第' + (i + 1) + '西医入院病情'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '西医入院病情',
              old: this.storeXyDiseaseList[i].c08c1,
              after: this.changeXyDiseaseList[i].c08c1,
              name: '第' + (i + 1) + '西医入院病情'
            }
          )
          // }
        }
      }
      this.$emit('showChange-Message', this.modifyContent)
    },
    // 中医诊断改变记录
    zyDiseaseListChange () {
      // 中医诊断编码
      for (let i = 0; i < this.storeXyDiseaseList.length; i++) {
        // for (let j = 0; j < this.value.busOperateDiagnosisListBa; j++) {
        if (this.storeZyDiseaseList[i].c06c2 != this.changeZyDiseaseList[i].c06c2) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '中医诊断编码') {
                item.after = this.changeZyDiseaseList[i].c06c2
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '中医诊断编码',
                old: this.storeZyDiseaseList[i].c06c2,
                after: this.changeZyDiseaseList[i].c06c2,
                name: '第' + (i + 1) + '中医诊断编码'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '中医诊断编码',
              old: this.storeZyDiseaseList[i].c06c2,
              after: this.changeZyDiseaseList[i].c06c2,
              name: '第' + (i + 1) + '中医诊断编码'
            }
          )
          // }
        }
        // 中医诊断名称
        if (this.storeZyDiseaseList[i].c07n2 != this.changeZyDiseaseList[i].c07n2) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '中医诊断名称') {
                item.after = this.changeZyDiseaseList[i].c07n2
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '中医诊断名称',
                old: this.storeZyDiseaseList[i].c07n2,
                after: this.changeZyDiseaseList[i].c07n2,
                name: '第' + (i + 1) + '中医诊断名称'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '中医诊断名称',
              old: this.storeZyDiseaseList[i].c07n2,
              after: this.changeZyDiseaseList[i].c07n2,
              name: '第' + (i + 1) + '中医诊断名称'
            }
          )
          // }
        }
        // 中医诊断入院病情
        if (this.storeZyDiseaseList[i].c08c2 != this.changeZyDiseaseList[i].c08c2) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '中医入院病情') {
                item.after = this.changeZyDiseaseList[i].c08c2
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '中医入院病情',
                old: this.storeZyDiseaseList[i].c08c2,
                after: this.changeZyDiseaseList[i].c08c2,
                name: '第' + (i + 1) + '中医入院病情'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '中医入院病情',
              old: this.storeZyDiseaseList[i].c08c2,
              after: this.changeZyDiseaseList[i].c08c2,
              name: '第' + (i + 1) + '中医入院病情'
            }
          )
          // }
        }
      }
      this.$emit('showChange-Message', this.modifyContent)
    },
    // 门慢门特改变记录
    OutpatientListChange () {
      for (let i = 0; i < this.storeOutpatientList.length; i++) {
        // 诊断名称
        if (this.storeOutpatientList[i].diagName != this.changeOutpatientList[i].diagName) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '门诊慢特第' + (i + 1) + '诊断名称') {
                item.after = this.changeOutpatientList[i].diagName
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '门诊慢特第' + (i + 1) + '诊断名称',
                old: this.storeOutpatientList[i].diagName,
                after: this.changeOutpatientList[i].diagName,
                name: '门诊慢特第' + (i + 1) + '诊断名称'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '门诊慢特第' + (i + 1) + '诊断名称',
              old: this.storeOutpatientList[i].diagName,
              after: this.changeOutpatientList[i].diagName,
              name: '门诊慢特第' + (i + 1) + '诊断名称'
            }
          )
        }
        // 诊断代码
        if (this.storeOutpatientList[i].diagCode != this.changeOutpatientList[i].diagCode) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '门诊慢特第' + (i + 1) + '诊断代码') {
                item.after = this.changeOutpatientList[i].diagCode
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '门诊慢特第' + (i + 1) + '诊断代码',
                old: this.storeOutpatientList[i].diagCode,
                after: this.changeOutpatientList[i].diagCode,
                name: '门诊慢特第' + (i + 1) + '诊断代码'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '门诊慢特第' + (i + 1) + '诊断代码',
              old: this.storeOutpatientList[i].diagCode,
              after: this.changeOutpatientList[i].diagCode,
              name: '门诊慢特第' + (i + 1) + '诊断代码'
            }
          )
        }
        // 手术名称
        if (this.storeOutpatientList[i].oprnOprtName != this.changeOutpatientList[i].oprnOprtName) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '门诊慢特第' + (i + 1) + '手术及操作名称') {
                item.after = this.changeOutpatientList[i].oprnOprtName
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '门诊慢特第' + (i + 1) + '手术及操作名称',
                old: this.storeOutpatientList[i].oprnOprtName,
                after: this.changeOutpatientList[i].oprnOprtName,
                name: '门诊慢特第' + (i + 1) + '手术及操作名称'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '门诊慢特第' + (i + 1) + '手术及操作名称',
              old: this.storeOutpatientList[i].oprnOprtName,
              after: this.changeOutpatientList[i].oprnOprtName,
              name: '门诊慢特第' + (i + 1) + '手术及操作名称'
            }
          )
        }
        // 手术代码
        if (this.storeOutpatientList[i].oprnOprtCode != this.changeOutpatientList[i].oprnOprtCode) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '门诊慢特第' + (i + 1) + '手术及操作代码') {
                item.after = this.changeOutpatientList[i].oprnOprtCode
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '门诊慢特第' + (i + 1) + '手术及操作代码',
                old: this.storeOutpatientList[i].oprnOprtCode,
                after: this.changeOutpatientList[i].oprnOprtCode,
                name: '门诊慢特第' + (i + 1) + '手术及操作代码'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '门诊慢特第' + (i + 1) + '手术及操作代码',
              old: this.storeOutpatientList[i].oprnOprtCode,
              after: this.changeOutpatientList[i].oprnOprtCode,
              name: '门诊慢特第' + (i + 1) + '手术及操作代码'
            }
          )
        }
      }
      this.$emit('showChange-Message', this.modifyContent)
    },
    // ICU改变
    BusIcuListChange () {
      for (let i = 0; i < this.storeBusIcuList.length; i++) {
        // 重症类型
        if (this.storeBusIcuList[i].b40 != this.changeBusIcuList[i].b40) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '重症监护病房类型') {
                item.after = this.changeBusIcuList[i].b40
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '重症监护病房类型',
                old: this.storeBusIcuList[i].b40,
                after: this.changeBusIcuList[i].b40,
                name: '第' + (i + 1) + '重症监护病房类型'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '重症监护病房类型',
              old: this.storeBusIcuList[i].b40,
              after: this.changeBusIcuList[i].b40,
              name: '第' + (i + 1) + '重症监护病房类型'
            }
          )
        }
        // 进重症监护室时间
        if (this.storeBusIcuList[i].b41 != this.changeBusIcuList[i].b41) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '进重症监护室时间') {
                item.after = this.changeBusIcuList[i].b41
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '进重症监护室时间',
                old: this.storeBusIcuList[i].b41,
                after: this.changeBusIcuList[i].b41,
                name: '第' + (i + 1) + '进重症监护室时间'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '进重症监护室时间',
              old: this.storeBusIcuList[i].b41,
              after: this.changeBusIcuList[i].b41,
              name: '第' + (i + 1) + '进重症监护室时间'
            }
          )
        }
        // 出重症监护室时间
        if (this.storeBusIcuList[i].b42 != this.changeBusIcuList[i].b42) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '出重症监护室时间') {
                item.after = this.changeBusIcuList[i].b42
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '出重症监护室时间',
                old: this.storeBusIcuList[i].b42,
                after: this.changeBusIcuList[i].b42,
                name: '第' + (i + 1) + '出重症监护室时间'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '出重症监护室时间',
              old: this.storeBusIcuList[i].b42,
              after: this.changeBusIcuList[i].b42,
              name: '第' + (i + 1) + '出重症监护室时间'
            }
          )
        }
        // 合计
        if (this.storeBusIcuList[i].b43 != this.changeBusIcuList[i].b43) {
          if (this.modifyContent) {
            this.modifyContent.forEach(item => {
              if (item.zd == '第' + (i + 1) + '合计（时间）') {
                item.after = this.changeBusIcuList[i].b43
              }
            })
          } else {
            this.modifyContent.push(
              {
                zd: '第' + (i + 1) + '合计（时间）',
                old: this.storeBusIcuList[i].b43,
                after: this.changeBusIcuList[i].b43,
                name: '第' + (i + 1) + '合计（时间）'
              }
            )
          }
          this.modifyContent.push(
            {
              zd: '第' + (i + 1) + '合计（时间）',
              old: this.storeBusIcuList[i].b43,
              after: this.changeBusIcuList[i].b43,
              name: '第' + (i + 1) + '合计（时间）'
            }
          )
        }
      }
      this.$emit('showChange-Message', this.modifyContent)
    },
    // input内容改变记录
    inputChange (value, ref, name) {
      if (value && ref) {
        if (this.modifyContent) {
          this.modifyContent.forEach(item => {
            if (item.zd == ref) {
              item.after = value
            }
          })
          this.modifyContent.push(
            { zd: ref, old: '-', after: value, name: name }
          )
        } else {
          this.modifyContent.push(
            { zd: ref, old: '-', after: value, name: name }
          )
        }
      }
      this.modifyContent.forEach(item => {
        this.historicalData.forEach(itemt => {
          if (item.zd == itemt.zd) {
            item.old = itemt.value
          }
        })
      })
      this.$emit('showChange-Message', this.modifyContent)
    },
    // 重跑预分组和校验
    async pre (type) {
      let preDto = {}
      // 基础数据
      // preDto.basylx=this.value.somHiInvyBasInfo
      preDto.yljgdm = this.value.somHiInvyBasInfo.a01
      preDto.cblx = this.value.somHiInvyBasInfo.a54
      preDto.username = this.value.somHiInvyBasInfo.a02
      preDto.ylfkfs = this.value.somHiInvyBasInfo.a46c
      preDto.jkkh = this.value.somHiInvyBasInfo.a47
      preDto.zycs = this.value.somHiInvyBasInfo.a49
      preDto.bah = this.value.somHiInvyBasInfo.a48
      preDto.xm = this.value.somHiInvyBasInfo.a11
      preDto.xb = this.value.somHiInvyBasInfo.a12c
      preDto.csrq = this.value.somHiInvyBasInfo.a13
      preDto.nl = this.value.somHiInvyBasInfo.a14
      preDto.gj = this.value.somHiInvyBasInfo.a15c
      preDto.bzyzsnl = this.value.somHiInvyBasInfo.a16
      preDto.xsecstz = this.value.somHiInvyBasInfo.a18
      preDto.xserytz = this.value.somHiInvyBasInfo.a17
      preDto.csd = this.value.somHiInvyBasInfo.a22
      preDto.gg = this.value.somHiInvyBasInfo.a23c
      preDto.mz = this.value.somHiInvyBasInfo.a19c
      preDto.sfzh = this.value.somHiInvyBasInfo.a20
      preDto.zy = this.value.somHiInvyBasInfo.a38c
      preDto.hy = this.value.somHiInvyBasInfo.a21c
      preDto.xzz = this.value.somHiInvyBasInfo.a26
      preDto.dh = this.value.somHiInvyBasInfo.a27
      preDto.yb1 = this.value.somHiInvyBasInfo.a28c
      preDto.hkdz = this.value.somHiInvyBasInfo.a24
      preDto.yb2 = this.value.somHiInvyBasInfo.a25c
      preDto.gzdwjdz = this.value.somHiInvyBasInfo.a29
      preDto.gzdwmc = this.value.somHiInvyBasInfo.a29n
      preDto.dwdh = this.value.somHiInvyBasInfo.a30
      preDto.yb3 = this.value.somHiInvyBasInfo.a31c
      preDto.lxrxm = this.value.somHiInvyBasInfo.a32
      preDto.gx = this.value.somHiInvyBasInfo.a33c
      preDto.dz = this.value.somHiInvyBasInfo.a34
      preDto.dh2 = this.value.somHiInvyBasInfo.a35
      preDto.rytj = this.value.somHiInvyBasInfo.b11c
      preDto.rysj = this.value.somHiInvyBasInfo.b12
      preDto.rysjs = this.value.somHiInvyBasInfo.b12s
      preDto.rykbbm = this.value.somHiInvyBasInfo.b13n
      preDto.rybf = this.value.somHiInvyBasInfo.b14n
      preDto.zkkbbm = this.value.somHiInvyBasInfo.b21c
      preDto.cysj = this.value.somHiInvyBasInfo.b15
      preDto.cysjs = this.value.somHiInvyBasInfo.b15s
      preDto.cykbbm = this.value.somHiInvyBasInfo.b16n
      preDto.cybf = this.value.somHiInvyBasInfo.b17n
      preDto.zyts = this.value.somHiInvyBasInfo.b20
      preDto.wbyy = this.value.somHiInvyBasInfo.c13n
      preDto.blh = this.value.somHiInvyBasInfo.c11
      preDto.ywgm = this.value.somHiInvyBasInfo.c25
      // preDto.gmyw =this.value.somHiInvyBasInfo.?
      preDto.swhzsj = this.value.somHiInvyBasInfo.c34c
      preDto.xx = this.value.somHiInvyBasInfo.c26c
      preDto.rh = this.value.somHiInvyBasInfo.c27c
      preDto.kzr = this.value.somHiInvyBasInfo.b22n
      preDto.zrys = this.value.somHiInvyBasInfo.b23c
      preDto.zzys = this.value.somHiInvyBasInfo.b24c
      preDto.zyys = this.value.somHiInvyBasInfo.b25c
      preDto.zrhs = this.value.somHiInvyBasInfo.b26n
      preDto.jxys = this.value.somHiInvyBasInfo.b27n
      preDto.sxys = this.value.somHiInvyBasInfo.b28
      preDto.bmy = this.value.somHiInvyBasInfo.b29n
      preDto.bazl = this.value.somHiInvyBasInfo.b30c
      preDto.zkys = this.value.somHiInvyBasInfo.b31n
      preDto.zkhs = this.value.somHiInvyBasInfo.b32n
      preDto.zkrq = this.value.somHiInvyBasInfo.b33
      preDto.lyfs = this.value.somHiInvyBasInfo.b34c
      preDto.yzzy_yljg = this.value.somHiInvyBasInfo.b49
      // preDto.fzlx  =this.value.somHiInvyBasInfo.？
      // preDto.hosp_lv  =this.value.somHiInvyBasInfo.？
      preDto.sjhlts = this.value.somHiInvyBasInfo.b47
      preDto.ejhlts = this.value.somHiInvyBasInfo.b46
      preDto.yjhlts = this.value.busSettleListb45
      preDto.tjhlts = this.value.somHiInvyBasInfo.b44
      // preDto.qj=this.value.somHiInvyBasInfo.？
      // preDto.qj=this.value.somHiInvyBasInfo.？
      // preDto.czzjhssj3 =this.value.somHiInvyBasInfo.？
      // preDto.czzjhssj2=this.value.somHiInvyBasInfo.？
      // preDto.czzjhssj1 =this.value.somHiInvyBasInfo.？
      // preDto.jzzjhssj1=this.value.somHiInvyBasInfo.？
      // preDto.jzzjhssj2=this.value.somHiInvyBasInfo.？
      // preDto.jzzjhssj3=this.value.somHiInvyBasInfo.？
      preDto.hxjsysj = this.value.somHiInvyBasInfo.c43
      // preDto.oth_fee   =this.value.somHiInvyBasInfo. busMedicalCostList.costItemName=“其他费”
      preDto.ycxyyclf = this.value.somHiInvyBasInfo.d33
      preDto.yyclf = this.value.somHiInvyBasInfo.d32
      preDto.hcyyclf = this.value.somHiInvyBasInfo.d31
      preDto.xbyzlzpf = this.value.somHiInvyBasInfo.d30
      preDto.nxyzlzpf = this.value.somHiInvyBasInfo.d29
      preDto.qdblzpf = this.value.somHiInvyBasInfo.d28
      preDto.bdblzpf = this.value.somHiInvyBasInfo.d27
      preDto.blo_fee = this.value.somHiInvyBasInfo.d26
      preDto.tcmherb = this.value.somHiInvyBasInfo.d25
      preDto.tcmpat_fee = this.value.somHiInvyBasInfo.d24
      preDto.kjywf = this.value.somHiInvyBasInfo.d23x01
      preDto.west_fee = this.value.somHiInvyBasInfo.d23
      preDto.tcm_treat_fee = this.value.somHiInvyBasInfo.d22
      preDto.rhab_fee = this.value.somHiInvyBasInfo.d21
      preDto.ssf = this.value.somHiInvyBasInfo.d20x02
      preDto.maf = this.value.somHiInvyBasInfo.d20x01
      preDto.oprn_treat_fee = this.value.somHiInvyBasInfo.d20
      preDto.wlzlf = this.value.somHiInvyBasInfo.d19x01
      preDto.nsrgtrt_item_fee = this.value.somHiInvyBasInfo.d19
      preDto.clnc_diag_item_fee = this.value.somHiInvyBasInfo.d18
      preDto.rdhy_diag_fee = this.value.somHiInvyBasInfo.d17
      preDto.lab_diag_fee = this.value.somHiInvyBasInfo.d16
      preDto.cas_diag_fee = this.value.somHiInvyBasInfo.d15
      preDto.oth_fee_com = this.value.somHiInvyBasInfo.d14
      preDto.nursfee = this.value.somHiInvyBasInfo.d13
      preDto.zlczf = this.value.somHiInvyBasInfo.d12
      preDto.ylfuf = this.value.somHiInvyBasInfo.d11
      preDto.zfje = this.value.somHiInvyBasInfo.d09
      preDto.jcfyzb = 0
      preDto.jyfyzb = 0
      preDto.hcfyzb = 0
      preDto.ypfyzb = 0
      preDto.zyzfy = this.value.somHiInvyBasInfo.d01
      preDto.ryh_f = this.value.somHiInvyBasInfo.c33
      preDto.ryh_xs = this.value.somHiInvyBasInfo.c32
      preDto.ryh_t = this.value.somHiInvyBasInfo.c31
      preDto.ryq_f = this.value.somHiInvyBasInfo.c30
      preDto.ryq_xs = this.value.somHiInvyBasInfo.c29
      preDto.ryq_t = this.value.somHiInvyBasInfo.c28
      preDto.md = this.value.somHiInvyBasInfo.b37
      preDto.sfzzyjh = this.value.somHiInvyBasInfo.b36c
      preDto.wsy_yljg = this.value.somHiInvyBasInfo.b49
      preDto.yzzy_yljg = this.value.somHiInvyBasInfo.b49
      preDto.lyfs = this.value.somHiInvyBasInfo.b34c
      // 诊断
      preDto.jbdm = this.value.xyDiseaseList[0].c06c1
      preDto.zyzd = this.value.xyDiseaseList[0].c07n1
      preDto.jbdm1 = this.value.xyDiseaseList[1].c06c1
      preDto.jbdm2 = this.value.xyDiseaseList[2].c06c1
      preDto.jbdm3 = this.value.xyDiseaseList[3].c06c1
      preDto.jbdm4 = this.value.xyDiseaseList[4].c06c1
      preDto.jbdm5 = this.value.xyDiseaseList[5].c06c1
      preDto.jbdm6 = this.value.xyDiseaseList[6].c06c1
      preDto.jbdm7 = this.value.xyDiseaseList[7].c06c1
      preDto.jbdm8 = this.value.xyDiseaseList[8].c06c1
      // preDto.jbdm9=this.value.xyDiseaseList[9].c06c1
      // preDto.jbdm10=this.value.busDiseaseDiagnosisTrimList[9].c06c1
      // preDto.jbdm11=this.value.busDiseaseDiagnosisTrimList[9].c06c1
      // preDto.jbdm12=this.value.busDiseaseDiagnosisTrimList[9].c06c1
      // preDto.jbdm13=this.value.busDiseaseDiagnosisTrimList[9].c06c1
      // preDto.jbdm14=this.value.busDiseaseDiagnosisTrimList[9].c06c1
      // preDto.jbdm15=this.value.busDiseaseDiagnosisTrimList[9].c06c1
      // 手术编码
      preDto.ssjczbm1 = this.value.busOperateDiagnosisListBa[0].c35c
      preDto.ssjczbm2 = this.value.busOperateDiagnosisListBa[1].c35c
      preDto.ssjczbm3 = this.value.busOperateDiagnosisListBa[2].c35c
      preDto.ssjczbm4 = this.value.busOperateDiagnosisListBa[3].c35c
      preDto.ssjczbm5 = this.value.busOperateDiagnosisListBa[4].c35c
      preDto.ssjczbm6 = this.value.busOperateDiagnosisListBa[5].c35c
      preDto.ssjczbm7 = this.value.busOperateDiagnosisListBa[6].c35c
      preDto.ssjczbm8 = this.value.busOperateDiagnosisListBa[7].c35c
      preDto.ssjczbm9 = this.value.busOperateDiagnosisListBa[8].c35c
      preDto.ssjczbm10 = this.value.busOperateDiagnosisListBa[9].c35c
      // 手术名称
      preDto.ssjczmc1 = this.value.busOperateDiagnosisListBa[0].c36n
      preDto.ssjczmc2 = this.value.busOperateDiagnosisListBa[1].c36n
      preDto.ssjczmc3 = this.value.busOperateDiagnosisListBa[2].c36n
      preDto.ssjczmc4 = this.value.busOperateDiagnosisListBa[3].c36n
      preDto.ssjczmc5 = this.value.busOperateDiagnosisListBa[4].c36n
      preDto.ssjczmc6 = this.value.busOperateDiagnosisListBa[5].c36n
      preDto.ssjczmc7 = this.value.busOperateDiagnosisListBa[6].c36n
      preDto.ssjczmc8 = this.value.busOperateDiagnosisListBa[7].c36n
      preDto.ssjczmc9 = this.value.busOperateDiagnosisListBa[8].c36n
      preDto.ssjczmc10 = this.value.busOperateDiagnosisListBa[9].c36n
      // preDto.hospital_id=this.$store.getters.
      preDto.fzlx = 'DRG'
      preDto.encryptKey = '57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E'
      // let preJson=JSON.stringify(preDto);
      await preGroup(preDto).then(res => {
        this.restart.url = res.url
      })
      // this.$emit('selectPre-Result',this.url)
      // return this.url
      if (type == '1') {
        await restartCheck(this.value).then(res => {
          if (res.code == 200) {
            this.restart.error = res.data
          }
        })
      }
      return this.restart
    },
    // 如果诊断内容发生改变重新组合诊断
    mergeDisease () {
      if (this.value.xyDiseaseList) {
        this.value.busDiseaseDiagnosisTrimList = []
        for (let i = 0; i < this.value.xyDiseaseList.length; i++) {
          this.value.busDiseaseDiagnosisTrimList.push(
            {
              c06c1: this.value.xyDiseaseList[i].c06c1,
              c07n1: this.value.xyDiseaseList[i].c07n1,
              c08c1: this.value.xyDiseaseList[i].c08c1,
              seq: this.value.xyDiseaseList[i].index,
              c06c2: this.value.zyDiseaseList[i].c06c2,
              c07n2: this.value.zyDiseaseList[i].c07n2,
              c08c2: this.value.zyDiseaseList[i].c08c2
            }
          )
        }
        const busDiseaseDiagnosisTrimsList = { busDiseaseDiagnosisTrimsList: this.value.busDiseaseDiagnosisTrimList }
        Object.assign(this.value, busDiseaseDiagnosisTrimsList)
      }
    },
    handleSelectionChange (selection) {
      if (selection.length > 1) {
        this.$refs.xyzdbListTable.clearSelection()
        this.$refs.xyzdbListTable.toggleRowSelection(selection.pop())
      }
      this.checkedGh = selection[0].index
    },
    checkGbValue (value) {
      // if(this.checkedGh && this.checkedGh==value){
      //   console.log(this.checkedGh)
      //   console.log(value)
      //   return true
      // }else {
      return false
      // }
    },
    // 更新是否可改
    upldStas () {
      if (this.value.somHiInvyBasInfo.uploadFlag == '1') {
        return 'readonly'
      }
    },
    // 更新是否可改
    elUploadState () {
      if (this.value.somHiInvyBasInfo.uploadFlag == '1') {
        return true
      }
    }
  },
  watch: {
    'data.zd': {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.checkGb(val)
      }
    },
    'value.busOperateDiagnosisListBa': {
      handler: function (val) {
        this.changeOperationList = val
        // console.log(this.changeOperationList)
        this.$emit('update-showChange-Message', this.value)
        this.$emit('showChange-Message', this.modifyContent)
      },
      deep: true,
      immediate: true
    },
    'value.xyDiseaseList': {
      handler: function (val) {
        this.changeXyDiseaseList = val
        this.mergeDisease()
        this.$emit('update-showChange-Message', this.value)
        this.$emit('showChange-Message', this.modifyContent)
      },
      deep: true,
      immediate: true
    },
    'value.zyDiseaseList': {
      handler: function (val) {
        this.changeZyDiseaseList = val
        this.mergeDisease()
        this.$emit('update-showChange-Message', this.value)
        this.$emit('showChange-Message', this.modifyContent)
      },
      deep: true,
      immediate: true
    },
    'value.busOutpatientListBa': {
      handler: function (val) {
        this.changeOutpatientList = val
        this.mergeDisease()
        this.$emit('update-showChange-Message', this.value)
        this.$emit('showChange-Message', this.modifyContent)
      },
      deep: true,
      immediate: true
    },
    'value.busIcuListBa': {
      handler: function (val) {
        this.changeBusIcuList = val
        this.mergeDisease()
        this.$emit('update-showChange-Message', this.value)
        this.$emit('showChange-Message', this.modifyContent)
      },
      deep: true,
      immediate: true
    },
    value: {
      handler: function (value) {
        if (value) {
          this.setSettleListData(value)
        }
      },
      immediate: true,
      deep: true
    },
    deleteWatchData: {
      handler: function (value) {
        if (value == '2') {
          this.modifyContent = []
        }
      }
    }
  }
}
</script>
<style scoped>
.inputStyle {
  border: none;
  border-bottom: 1px black solid
}

.numInputStyle {
  height: 17px;
  width: 17px
}

.fontWeight {

}

.divMargin {
  display: flex;
  margin: 10px
}

/deep/ .el-input.is-disabled .el-input__inner {
  color: black;
}

.subtitleStyle {
  text-align: center;
  border: 1px black solid;
  border-width: 0 0 1px 0;
  line-height: 40px;
  font-weight: bold;
  font-size: 20px;
  background-color: #228fdda1;
}

.subtitleStyle2 {
  text-align: center;
  border: 1px #000000 solid;
  border-width: 1px 0 1px 0;
  line-height: 40px;
  font-weight: bold;
  font-size: 20px;
  background-color: #228fdda1;
}

</style>
