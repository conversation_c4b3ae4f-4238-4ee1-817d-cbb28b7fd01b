<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             headerTitle="查询条件"
             contentTitle="参数"
             @query="queryTempData"
             @reset="resetData">

      <template slot="extendFormItems">
        <el-form-item label="键" class="som-form-item">
<!--          <el-input v-model="queryForm.key" placeholder="配置键" />-->
          <el-select v-model="queryForm.key" clearable placeholder="请选择" class="som-form-item">
            <el-option v-for="item in keySelect"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item label="值" class="som-form-item">-->
<!--          <el-input v-model="queryForm.value" placeholder="配置值" />-->
<!--        </el-form-item>-->
      </template>

      <template slot="containerContent">
        <el-table :data="tempTableData"
                  v-loading="loading"
                  :header-cell-style = "{'text-align' : 'center'}"
                  height="100%"
                  border>
          <el-table-column label="序号" type="index" align="center" />
          <el-table-column label="键" prop="key" />
          <el-table-column label="值" prop="value" />
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" icon="el-icon-edit" circle @click="editItem(scope.row)"></el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog title="编辑" :visible.sync="showDialog" width="30%">
          <el-form :model="editForm" size="mini" :rules="editFormRules" ref="editForm">
            <el-form-item label="配置键" class="som-form-item" prop="configKey">
              <el-input disabled v-model="editForm.configKey"></el-input>
            </el-form-item>
            <el-form-item label="配置值" class="som-form-item" prop="configValue" required>
              <el-input v-model="editForm.configValue"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="cancelItem">取 消</el-button>
            <el-button type="primary" @click="submitItem('editForm')">确 定</el-button>
          </div>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { querySettleListData, updateSysSettleListConfig } from '@/api/dataConfig/commonConfig'

export default {
  name: 'settleListConfigManage',
  data () {
    return {
      queryForm: {},
      tableData: [],
      editForm: {
        configKey: '',
        configValue: ''
      },
      loading: false,
      showDialog: false,
      editFormRules: {
        configValue: [
          { required: true, message: '请输入需要修改的值', trigger: 'blur' }
        ]
      },
      keySelect: [
        { value: 'MSGID', label: '发送方报文ID' },
        { value: 'MDTRTAREA_ADMVS', label: '就医地医保区划' },
        { value: 'INSUPLC_ADMINFNODVS', label: '参保地医保区划' },
        { value: 'RECER_SYS_CODE', label: '接收方系统代码' },
        { value: 'DEV_NO', label: '设备编号' },
        { value: 'DEV_SAFE_INFO', label: '设备安全信息' },
        { value: 'CAINFO', label: '数字签名信息' },
        { value: 'SIGNTYPE', label: '签名类型' },
        { value: 'INFVER', label: '接口版本号' },
        { value: 'OPTER_TYPE', label: '经办人类别' },
        { value: 'OPTER', label: '经办人' },
        { value: 'OPTER_NAME', label: '经办人姓名' },
        { value: 'FIXMEDINS_CODE', label: '定点医药机构编号' },
        { value: 'FIXMEDINS_NAME', label: '定点医药机构名称' },
        { value: 'URL', label: 'URL' }
      ],
      tempTableData: [],
      tempSaveData: []
    }
  },
  mounted () {
    this.queryData()
  },
  methods: {
    queryData () {
      this.tableData = []
      this.loading = true
      querySettleListData().then(res => {
        this.tableData = res.data
        if (res.data) {
          this.mappingFields(this.tableData)
        }
        this.loading = false
      })
    },
    editItem (row) {
      this.editForm.configKey = row.key
      this.editForm.configValue = row.value
      this.showDialog = true
    },
    cancelItem () {
      this.$confirm('取消后所做修改将不会保存，是否取消？')
        .then(_ => {
          this.showDialog = false
        })
        .catch(_ => {})
    },
    submitItem (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirm('是否确认修改？')
            .then(_ => {
              this.returnFields(this.editForm)
              this.showDialog = false
              updateSysSettleListConfig(this.editForm).then(res => {
                if (res.code == 200) {
                  this.$message({
                    message: '修改成功！',
                    type: 'success'
                  })
                  this.queryData()
                }
              })
            })
            .catch(_ => {})
        } else {
          this.$message({
            message: '请输入需要修改的值！',
            type: 'warning'
          })
        }
      })
    },
    queryTempData () {
      if (this.queryForm.key && this.queryForm.key != '') {
        this.tempSaveData = this.tempTableData
        this.tempTableData = []
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.queryForm.key == this.tableData[i].key) {
            let item = []
            item.push(this.tableData[i])
            this.mappingFields(item)
            break
          }
        }
      }
    },
    mappingFields (item) {
      this.tempTableData = []
      for (let i = 0; i < item.length; i++) {
        for (let j = 0; j < this.keySelect.length; j++) {
          if (item[i].key == this.keySelect[j].value) {
            this.tempTableData.push({ key: this.keySelect[j].label, value: item[i].value })
          }
        }
      }
    },
    returnFields (item) {
      for (let i = 0; i < this.keySelect.length; i++) {
        if (item.configKey == this.keySelect[i].label) {
          item.configKey = this.keySelect[i].value
        }
      }
    },
    resetData () {
      this.queryForm = {}
      this.tempTableData = []
      this.queryData()
    }
  }
}

</script>

<style scoped>

</style>
