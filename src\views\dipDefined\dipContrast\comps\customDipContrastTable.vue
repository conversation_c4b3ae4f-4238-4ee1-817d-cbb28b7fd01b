<template>
  <el-table border
            :key="id"
            :header-cell-style="{'text-align':'center'}"
            height="100%"
            :id="id"
            :ref="name"
            v-loading="tableLoading"
            :data="tableData">
    <el-table-column label="序号" type="index" align="center" fixed="left"/>
    <el-table-column label="DIP编码" prop="dipCodg" width="100px" align="left" show-overflow-tooltip fixed="left"/>
    <el-table-column label="DIP名称" prop="dipName" width="100px" align="left" show-overflow-tooltip fixed="left"/>
    <drg-table-column label="是否使用辅助目录" prop="usedAsstList" width="80px" dicType="AD"/>
    <el-table-column label="年龄辅助目录" prop="asstListAgeGrp" align="left" show-overflow-tooltip/>
    <el-table-column label="严重程度辅助目录" prop="asstListDiseSevDeg" align="left" show-overflow-tooltip/>
    <el-table-column label="肿瘤辅助目录" prop="asstListTmorSevDeg" align="left" show-overflow-tooltip/>
    <el-table-column label="平均住院费用" prop="dipStandardCost" width="100px" align="right"/>
    <el-table-column label="平均住院天数" prop="dipStandardDaysLevel" width="100px" align="right"/>
    <el-table-column label="药占比" prop="dipDrugRate" width="100px" align="right">
      <template slot-scope="scope">
        {{ scope.row.dipDrugRate + '%' }}
      </template>
    </el-table-column>
    <el-table-column label="耗占比" prop="dipComsumableRate" width="100px" align="right">
      <template slot-scope="scope">
        {{ scope.row.dipComsumableRate + '%' }}
      </template>
    </el-table-column>
  </el-table>
</template>
<script>
export default {
  name: 'customDipContrastTable',
  props: {
    tableData: {
      type: Array
    },
    id: {
      type: String
    },
    name: {
      type: String
    },
    tableLoading: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({})
}
</script>
<style scoped>
</style>
