-- 方法1: 使用临时表分步查询（推荐用于复杂查询）
-- 第一步：先获取符合时间条件的患者ID
CREATE TEMPORARY TABLE temp_patient_ids AS
SELECT DISTINCT hisid 
FROM hcm_settle_zy_b 
WHERE discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59';

-- 第二步：基于患者ID查询验证结果
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_valid_result_inhosp a
  INNER JOIN temp_patient_ids t ON a.unique_id = t.hisid
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
WHERE
  a.rule_scen_type = '1'
LIMIT 200;

-- 清理临时表
DROP TEMPORARY TABLE temp_patient_ids;

-- 方法2: 使用窗口函数优化（如果需要排序）
SELECT * FROM (
  SELECT
    a.rule_valid_result_id AS id,
    a.unique_id AS uniqueId,
    a.rule_scen_type AS ruleScenType,
    a.rule_detl_codg AS ruleDetlCodg,
    a.rule_data_meta AS ruleDataMeta,
    a.error_desc AS errorDesc,
    a.error_detail_codg AS errorDetailCodg,
    a.violation_amount AS violationAmount,
    a.med_list_codg AS medListCodg,
    b.data_name AS dataName,
    d.rule_grp_name AS ruleGrpName,
    a.cnt,
    a.pric,
    a.vola_deg AS volaDeg,
    ROW_NUMBER() OVER (ORDER BY a.rule_valid_result_id) as rn
  FROM
    hcm_valid_result_inhosp a
    INNER JOIN hcm_settle_zy_b e ON a.unique_id = e.hisid
    INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
      AND LEFT(a.oprn_date, 4) = b.rule_year
      AND b.data_grp_code = a.rule_data_meta
    INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
      AND d.rule_year = LEFT(a.oprn_date, 4)
  WHERE
    a.rule_scen_type = '1'
    AND e.discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
) ranked_results
WHERE rn <= 200;

-- 方法3: 使用UNION ALL分批查询（适用于大数据量）
(SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_valid_result_inhosp a
  INNER JOIN hcm_settle_zy_b e ON a.unique_id = e.hisid
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
WHERE
  a.rule_scen_type = '1'
  AND e.discharge_date BETWEEN '2024-07-01' AND '2024-09-30'
LIMIT 100)
UNION ALL
(SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_valid_result_inhosp a
  INNER JOIN hcm_settle_zy_b e ON a.unique_id = e.hisid
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
WHERE
  a.rule_scen_type = '1'
  AND e.discharge_date BETWEEN '2024-10-01' AND '2025-08-23'
LIMIT 100);

-- 方法4: 使用强制索引提示（需要先创建索引）
SELECT /*+ USE_INDEX(a, idx_hcm_valid_result_inhosp_rule_scen_type) 
           USE_INDEX(e, idx_hcm_settle_zy_b_discharge_date) */
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_valid_result_inhosp a FORCE INDEX (idx_hcm_valid_result_inhosp_rule_scen_type)
  INNER JOIN hcm_settle_zy_b e FORCE INDEX (idx_hcm_settle_zy_b_discharge_date)
    ON a.unique_id = e.hisid
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
WHERE
  a.rule_scen_type = '1'
  AND e.discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
LIMIT 200;
