<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-issue
             :container="true"
             headerTitle="查询条件"
             contentTitle="科室倍率区间分析列表"
             label-width="80px"
             @query="queryData">
      <!-- 内容 -->
      <template slot="containerContent">

      </template>
    </drg-form>
  </div>
</template>
<script>
export default {
  name: 'backContrast',
  data: () => ({
    queryForm: {

    }
  }),
  methods: {
    queryData () {

    }
  }
}
</script>
