<template>
  <div class="app-container">
    <el-card class="hos-analysis-wrapper">
      <drg-title-line title="全院分析" class="first-page-title">
        <template slot="rightSide">
          <div style="display: flex">
            <div>
              <el-radio-group v-model="dateType" @change="radioChange" style="margin-right: 20px;">
                <el-radio-button :label="1">出院</el-radio-button>
                <el-radio-button :label="2">结算</el-radio-button>
              </el-radio-group>
              <el-date-picker
                v-model="queryDate"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="dateChange"
                :clearable="false"
                :picker-options="pickerOptions">
              </el-date-picker>
            </div>
          </div>
        </template>
      </drg-title-line>
      <!-- 汇总信息 -->
      <div class="hos-analysis-head">
        <div class="hos-analysis-head-item"
             :style="{ backgroundColor: item.bgColor, border: '1px solid ' + item.bColor }"
             v-for="(item, index) in headList"
             :key="index">
          <div class="hos-analysis-head-item-icon" :style="{ backgroundColor: item.bColor }">
            <i :class="[ item.icon ]"></i>
          </div>
          <div class="hos-analysis-head-item-value">
            <span class="title" v-html="item.value"></span>
            <span class="subhead"> {{ item.name }} </span>
          </div>
        </div>
      </div>

      <!-- 病案错误 -->
      <div class="hos-analysis-content">
        <drg-echarts :options="gerErrorOption(i)" @chartClick="pieChartClick" v-for="i in 2" :key="i"/>
      </div>

      <!-- 亏损/盈利排名 -->
      <div class="hos-analysis-footer">
        <drg-echarts :options="getLossOrProfitOrderOption(i)"
                    @chartClick="click"  v-for="i in 4" :key="i"/>

        <!-- 图标 -->
        <div class="hos-analysis-footer-icon">
          <!-- profit -->
          <i class="som-icon-profit "
             title="盈利"
             v-if="isLoss"
             @click="switchLossOrProfit(false)"
             style="height: 1.2rem;width: 1.2rem" />

          <!-- loss -->
          <i class="som-icon-loss "
             title="亏损"
             v-if="!isLoss"
             @click="switchLossOrProfit(true)"
             style="height: 1.2rem;width: 1.2rem" />
        </div>

        <!-- 排名 -->
        <div class="hos-analysis-footer-order">
          <el-select v-model="topVal" placeholder="请选择" @change="topChange">
            <el-option
              v-for="item in topOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>

        <!-- 排序 -->
        <div class="hos-analysis-footer-sort">
          <i class="som-icon-sort "
             :title="sort ? '倒序' : '正序'"
             @click="sortChane"
             style="height: 1.2rem;width: 1.2rem" />
        </div>

        <!-- 定时 -->
        <div class="hos-analysis-footer-timer">
          <i class="som-icon-time "
             title="关闭定时"
             v-if="timer"
             @click="changeTimer"
             style="height: 1.2rem;width: 1.2rem" />

          <i class="som-icon-close-time "
             title="开启定时"
             v-else
             @click="changeTimer"
             style="height: 1.2rem;width: 1.2rem" />
        </div>

      </div>
    </el-card>
  </div>
</template>

<script>
import { querySummaryData, queryErrorData, queryOrderData } from '@/api/newDrgBusiness/newDrgBusinessHos'
import { formatCost } from '@/utils/common'
import moment from 'moment'
import { updateSwitchState } from '@/api/newBusiness/newBusinessCommon'
export default {
  name: 'hospAnalysis',
  data: () => ({
    feeStas: '0',
    headList: [
      { name: '病案总数', value: 0, type: 1, bgColor: '#fedd9b', bColor: '#fdbd3e', icon: 'som-icon-high-white' },
      { name: '入组病案', value: 0, type: 2, bgColor: '#d1effe', bColor: '#6eccfc', icon: 'som-icon-low-white' },
      { name: '未入组病案', value: 0, type: 3, bgColor: '#d1effe', bColor: '#6eccfc', icon: 'som-icon-low-white' },
      { name: '总费用', value: 0, type: 4, bgColor: '#fde1d9', bColor: '#f79478', icon: 'som-icon-total-cost' },
      { name: '预测费用', value: 0, type: 5, bgColor: '#e2e1fe', bColor: '#7f7cfb', icon: 'som-icon-money-forecast' },
      { name: '预测费用差异', value: 0, type: 6, bgColor: '#fedd9b', bColor: '#fdbd3e', icon: 'som-icon-money-balance' }
    ],
    queryDate: '',
    pickerOptions: {
      shortcuts: [{
        text: '当月',
        onClick (picker) {
          const end = new Date()
          const start = new Date()
          start.setDate(1)
          picker.$emit('pick', [start, end])
        }
      }, {
        text: '上月',
        onClick (picker) {
          let end = new Date()
          let start = new Date()
          let lastMonth = start.getMonth() - 1
          start = new Date(start.getFullYear(), lastMonth, 1)
          end = new Date(start.getFullYear(), lastMonth, new Date(start.getFullYear(), end.getMonth(), 0).getDate())
          picker.$emit('pick', [start, end])
        }
      }, {
        text: '当年',
        onClick (picker) {
          const end = new Date()
          const start = new Date()
          start.setDate(1)
          start.setMonth(0)
          end.setFullYear(end.getFullYear() + 1)
          end.setDate(0)
          end.setMonth(-1)
          picker.$emit('pick', [start, end])
        }
      }]
    },
    begnDate: '',
    expiDate: '',
    // 分组失败
    errorOptions1: {},
    // 逻辑性完整性
    errorOptions2: {},

    // 科室
    lossOrProfitOrderOptions1: {},
    // 医生
    lossOrProfitOrderOptions2: {},
    // 病组
    lossOrProfitOrderOptions3: {},
    // 患者
    lossOrProfitOrderOptions4: {},

    // 颜色
    colors: ['#6eccfc', '#f79478', '#7f7cfb', '#fdbd3e'],
    // 是否是亏损排名
    isLoss: true,
    // 排名
    topVal: 10,
    topOptions: [
      { value: 10, label: 'TPP10' },
      { value: 20, label: 'TPP20' }
    ],
    // 排序 true：正序 false：倒序
    sort: true,
    // 定时器
    timer: '',
    // 当前进度
    value: '0',
    // 最大进度
    maxvalue: '100',

    dateType: 2
  }),
  created () {
    if (this.queryDate.length == 0) {
      this.begnDate = moment(this.$somms.getYearMonthStartTime()).format('YYYY-MM-DD')
      this.expiDate = moment(this.$somms.getYearMonthEndTime()).format('YYYY-MM-DD')
      this.queryDate = [this.begnDate, this.expiDate]
    }
  },
  mounted () {
    this.feeStas = String(this.$store.getters.feeStas)
    this.changeName()
    this.init()
  },
  computed: {
    prefix () {
      return this.isLoss ? '亏损' : '盈利'
    }
  },
  methods: {
    changeSwitch (val) {
      this.$store.commit('SET_SWITCHSTATE', val)
      this.feeStas = this.$store.getters.feeStas
      let params = {}
      this.changeName()
      params.feeStas = this.feeStas
      params.username = this.$store.getters.name
      updateSwitchState(params).then((res) => {
        if (res.code == 200) {
          this.init()
        }
      })
    },
    changeName () {
      this.headList.forEach(item => {
        if (item.name == '预测费用' && this.feeStas == '1') {
          item.name = '反馈费用'
        } else if (item.name == '反馈费用' && this.feeStas == '0') {
          item.name = '预测费用'
        } else if (item.name == '预测费用差异' && this.feeStas == '1') {
          item.name = '反馈费用差异'
        } else if (item.name == '反馈费用差异' && this.feeStas == '0') {
          item.name = '预测费用差异'
        }
      })
    },
    pieChartClick (params) {
      if (params) {
        this.$router.push({
          path: params.data.url,
          query: {
            begnDate: moment(this.begnDate).format('YYYY-MM-DD'),
            expiDate: moment(this.expiDate).format('YYYY-MM-DD'),
            resultType: 1,
            isInGroup: 0,
            notGroupReason: [params.data.errorReason],
            feeStas: this.feeStas
          }
        })
      }
    },
    click (params) {
      this.$router.push({
        path: params.data.url,
        query: {
          ym: this.queryDate,
          code: params.data.code,
          begnDate: moment(this.begnDate).format('YYYY-MM-DD'),
          expiDate: moment(this.expiDate).format('YYYY-MM-DD'),
          radioMode: '3',
          isLoss: this.isLoss ? 1 : 0,
          feeStas: this.feeStas
        }
      })
    },
    // 初始化
    init () {
      this.getHeadData()
      this.getErrorData()
      this.getOrderData()
      // this.enableTimer()
    },
    dateChange (val) {
      if (val) {
        this.begnDate = val[0]
        this.expiDate = val[1]
      } else {
        this.begnDate = null
        this.expiDate = null
      }
      this.init()
    },
    // 获取头部信息
    getHeadData () {
      querySummaryData(this.getParams()).then(res => {
        if (res.data) {
          this.headList[0].value = res.data.totalNum
          this.headList[1].value = res.data.drgInGroupMedcasVal
          this.headList[2].value = res.data.notInGroupNum
          this.headList[3].value = formatCost(res.data.sumfee, true)
          this.headList[4].value = formatCost(res.data.forecastAmount, true)
          this.headList[5].value = formatCost(res.data.forecastAmountDiff, true)
        }
      })
    },
    // 获取错误信息
    getErrorData () {
      queryErrorData(this.getParams()).then(res => {
        this.generateErrorOptions(res.data)
      })
    },
    // 生成错误图
    generateErrorOptions (data) {
      let tempData = []
      for (let i = 0; i < 7; i++) {
        let errNum = data['error' + (i + 1)]
        let name = this.getErrorOptionsErrorName((i + 1))
        if (errNum && errNum > 0) {
          tempData.push({
            value: errNum,
            name: name,
            url: '/hosDrgAnalysisNew/pattAnalysis',
            errorReason: 'error' + (i + 1)
          })
        }
      }

      let options = {
        color: [...this.colors],
        title: {
          text: '分组校验情况',
          right: '10%'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '入组错误病例',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '12',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: tempData
          }
        ]
      }

      // 环形进度条
      this.value = data.inGroupRate
      let tempData1 = []
      if (data.inGroupRate && data.inGroupRate > 0) {
        tempData1.push({
          value: data.inGroupRate,
          name: '入组率',
          url: '/hosDrgAnalysisNew/deptDrgAnalysis'
        })
        tempData1.push({
          value: this.maxvalue - this.value
        })
      }
      let option1 = {
        color: [...this.colors],
        title: {
          text: '入组率', right: '10%'
        },
        // 饼图中间显示文字
        graphic: {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: this.value + '%', // 文字内容
            // fill: "#fff",//文字颜色
            fontSize: 30 // 文字字号
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [{
          // 第一张圆环
          type: 'pie',
          radius: ['50%', '70%'],
          center: ['50%', '50%'],
          // 隐藏指示线
          labelLine: { show: false },
          data: tempData1
        }]
      }

      this.errorOptions2 = options
      // let options1 = JSON.parse(JSON.stringify(options))
      // let tempData1 = []
      // if(data.compeleteErrorNum && data.compeleteErrorNum > 0){
      //   tempData1.push({
      //     value: data.compeleteErrorNum, name: '完整性错误',
      //     url:'/caseQual/compVali',
      //   })
      // }
      // if(data.logicErrorNum && data.logicErrorNum > 0){
      //   tempData1.push({
      //     value: data.logicErrorNum, name: '逻辑性错误',
      //     url:'/caseQual/logiVali',
      //   })
      // }
      // options1.series[0].name = '逻辑性完整性错误'
      // options1.series[0].data = tempData1
      // options1.profttl.text = '病例校验情况'
      this.errorOptions1 = option1
    },
    // 获取显示名称
    getErrorOptionsErrorName (index) {
      if (index == 1) {
        return this.$somms.getDictValueByType('1', 'GROUP_ERROR_MSG_DRG')
      }
      if (index == 2) {
        return this.$somms.getDictValueByType('2', 'GROUP_ERROR_MSG_DRG')
      }
      if (index == 3) {
        return this.$somms.getDictValueByType('3', 'GROUP_ERROR_MSG_DRG')
      }
      if (index == 4) {
        return this.$somms.getDictValueByType('4', 'GROUP_ERROR_MSG_DRG')
      }
      if (index == 5) {
        return this.$somms.getDictValueByType('5', 'GROUP_ERROR_MSG_DRG')
      }
      if (index == 6) {
        return this.$somms.getDictValueByType('6', 'GROUP_ERROR_MSG_DRG')
      }
      if (index == 7) {
        return this.$somms.getDictValueByType('7', 'GROUP_ERROR_MSG_DRG')
      }
    },
    // 获取排序信息
    getOrderData () {
      // 清除数据
      this.generateOrderOptions([])
      queryOrderData(this.getParams()).then(res => {
        if (res.data) {
          this.generateOrderOptions(res.data)
        }
      })
    },
    // 生成排序图
    generateOrderOptions (data) {
      let option = {
        title: {
          text: '科室亏损排名',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            interval: 0,
            rotate: 40,
            formatter: params => {
              return this.formatCost(params, 0)
            }
          },
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          axisLabel: {
            formatter: params => {
              return params.length > 8 ? params.substr(0, 8) + '...' : params
            }
          },
          data: []
        },
        series: [
          {
            name: '科室',
            type: 'bar',
            data: [
              {
                code: '',
                url: ''
              }
            ]

          }
        ]
      }

      let names = ['科室', '医生', '病组', '患者']
      let dataList = [
        this.generateOrderData(data.deptList, 'deptName'),
        this.generateOrderData(data.doctorList, 'doctorName'),
        this.generateOrderData(data.disList, 'drgName'),
        this.generateOrderData(data.medList, 'name')
      ]
      for (let i = 0; i < names.length; i++) {
        let tempOption = this.$somms.cloneObj(option)
        tempOption.yAxis.data = dataList[i].yAxisData
        tempOption.series[0].data = dataList[i].seriesData
        tempOption.series[0].name = names[i]
        tempOption.profttl.text = names[i] + this.prefix + '排名'
        tempOption.color = this.colors[i]
        this['lossOrProfitOrderOptions' + (i + 1)] = tempOption
      }
    },
    // 生成排序数据
    generateOrderData (data, fld) {
      let res = {}
      res.yAxisData = []
      res.seriesData = []
      if (data) {
        data.forEach(item => {
          res.yAxisData.unshift((item[fld] && item[fld] != undefined) ? item[fld] : '-')
          if (fld == 'deptName') {
            res.seriesData.unshift({
              value: item.diff,
              code: item.deptCode,
              url: '/hosDrgAnalysisNew/deptDrgAnalysis'
            })
          } else if (fld == 'doctorName') {
            res.seriesData.unshift({
              value: item.diff,
              code: item.drCodg,
              url: '/hosDrgAnalysisNew/doctAnalysis'
            })
          } else if (fld == 'drgName') {
            res.seriesData.unshift({
              value: item.diff,
              code: item.drgCodg,
              url: '/hosDrgAnalysisNew/gropAnalysis'
            })
          } else {
            res.seriesData.unshift({
              value: item.diff,
              code: item.patientId,
              url: '/hosDrgAnalysis/predictPay'
            })
          }
        })
      }
      return res
    },
    // 格式化
    formatCost (val, fixed = 2) {
      let prefix = ''
      let resVal = val
      if (String(val).includes('-')) {
        prefix = '-'
        val = Math.abs(val)
        resVal = val
      }
      if (Math.abs((parseFloat(val) / 10000)) > 1) {
        resVal = (parseFloat(val) / 10000).toFixed(fixed) + '/万'
      } else {
        resVal = val
      }
      return prefix + resVal
    },
    // 获取错误option
    gerErrorOption (index) {
      return this['errorOptions' + index]
    },
    // 获取排名option
    getLossOrProfitOrderOption (index) {
      return this['lossOrProfitOrderOptions' + index]
    },
    // 选择亏损还是盈利排名
    switchLossOrProfit (isLoss) {
      this.clearTimer()
      this.isLoss = isLoss
      this.getOrderData()
    },
    // 排名个数改变
    topChange () {
      this.clearTimer()
      this.getOrderData()
    },
    // 排序改变
    sortChane () {
      this.clearTimer()
      this.sort = !this.sort
      this.getOrderData()
    },
    // 定时改变
    changeTimer () {
      if (this.timer) {
        this.clearTimer()
      } else {
        this.enableTimer()
      }
    },
    // 开启定时
    enableTimer () {
      this.timer = setInterval(() => {
        if ((Math.random() * 10).toFixed(0) % 2 == 0) {
          this.isLoss = !this.isLoss
        } else {
          this.sort = !this.sort
        }
        this.getOrderData()
      }, 10000)
    },
    // 清除定时
    clearTimer () {
      clearInterval(this.timer)
      this.timer = ''
    },
    // 参数
    getParams () {
      let params = {}
      // let firstDay = new Date();
      // firstDay.setDate(1);
      // firstDay.setMonth(0);
      // let lastDay = new Date();
      // lastDay.setFullYear(lastDay.getFullYear()+1);
      // lastDay.setDate(0);
      // lastDay.setMonth(-1);
      // firstDay = moment(firstDay).format("YYYY-MM-DD");
      // lastDay = moment(lastDay).format("YYYY-MM-DD");
      // params.begnDate = firstDay
      // params.expiDate = lastDay
      // params.begnDate = '2021-05-01'
      // params.expiDate = '2021-05-31'
      params.begnDate = moment(this.begnDate).format('YYYY-MM-DD')
      params.expiDate = moment(this.expiDate).format('YYYY-MM-DD')
      params.auth = true
      params.isLoss = this.isLoss
      params.limit = this.topVal
      params.sort = this.sort
      params.feeStas = this.feeStas
      params.dateType = this.dateType

      params.url = 'hospAnalysis'
      return params
    },

    radioChange () {
      this.init()
    }
  }
}
</script>

<style lang="scss" scoped>
@mixin bgColor{
  border: 1px solid green;
}

@mixin layout{
  display: flex;
  justify-content: space-between;
  position: relative;
}
.first-page-title {
  padding-bottom: 1.8rem!important;
}

.hos-analysis{

  &-wrapper{
    width: 100%;
    height: 100%;
    overflow: auto;
  }

  &-wrapper::-webkit-scrollbar{
    display: none;
  }

  &-head{
    width: 100%;
    height: 12%;
    padding-top: 0.1%;
    @include layout;

    &-item{
      width: 19%;
      height: 96%;
      padding: 2%;
      border-radius: 30px 0;
      display: flex;
      align-items: center;
      position: relative;
      margin-left: 1%;

      &-icon{
        width: 3rem;
        height: 3rem;
        border-radius: 10px;
        position: relative;

        &>i{
          position: absolute;
          left: 25%;
          top: 25%;
          width: 1.5rem;
          height: 1.5rem;
        }
      }

      &-value{
        display: flex;
        flex-direction: column;
        text-align: center;
        position: absolute;
        right: 10%;

        .title{
          font-size: var(--biggerSmallTitleSize);
        }

        .subhead{
          font-size: var(--biggerSmallSize);
        }
      }
    }
  }

  &-content{
    width: 100%;
    height: 40%;
    padding-top: 1%;
    @include layout;
  }

  &-footer{
    width: 100%;
    height: 60%;
    padding-top: 3%;
    @include layout;

    @mixin cursor{
      cursor: pointer;
    }
    &-icon{
      position: absolute;
      top: 2%;
      right: 0;
      @include cursor;
    }

    &-order{
      width: 10%;
      position: absolute;
      top: 0;
      right: calc(6% + 1.2rem);
    }

    &-timer{
      position: absolute;
      top: 1%;
      right: calc(3% + 1.2rem);
      @include cursor;
    }

    &-sort{
      position: absolute;
      top: 1%;
      right: calc(1% + 1.2rem);
      @include cursor;
    }
  }
}
</style>
