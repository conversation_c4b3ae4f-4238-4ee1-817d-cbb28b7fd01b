<template>
  <div class="echart-container" v-som-resize="this.resizeChart">
    <div :id="id" :style="style"></div>
  </div>
</template>
<script>
import echarts from 'echarts'
export default {
  name: 'echarts',
  data: () => ({
    myChart: null,
    id: null
  }),
  props: {
    height: {
      type: String,
      default: '100%'
    },
    width: {
      type: String,
      default: '100%'
    },
    options: {
      type: Object,
      required: true,
      default: () => {}
    }
  },
  computed: {
    style: function () {
      return {
        height: this.height,
        width: this.width
      }
    }
  },
  created () {
    this.id = new Date().getTime() + Math.random()
  },
  methods: {
    initChart () {
      let _this = this
      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(document.getElementById(this.id))

      // 使用刚指定的配置项和数据显示图表。
      this.myChart.setOption(this.options)
      this.myChart.off('click')
      this.myChart.on('click', function (params) {
        _this.$emit('chartClick', params)
      })
    },
    resizeChart () {
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    getInstance () {
      return this.myChart
    }
  },
  watch: {
    options (option) {
      if (option) {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    }
  }
}
</script>

<style scoped>
.echart-container {
  height: 100%;
  width: 100%;
}
</style>
