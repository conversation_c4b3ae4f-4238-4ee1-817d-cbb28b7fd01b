import request from '@/utils/request'

/**
 * 查询患者基本信息数据
 * @param params
 * @returns {*}
 */
export function queryPatientBasicInfoData (params) {
  return request({
    url: '/newDipBusinessPatientAnalysisController/queryPatientBasicInfoData',
    method: 'post',
    data: params
  })
}

export function queryPatientCostInfoData (params) {
  return request({
    url: '/newDipBusinessPatientAnalysisController/queryPatientCostInfoData',
    method: 'post',
    data: params
  })
}

export function queryPatientForecastData (params) {
  return request({
    url: '/newDipBusinessPatientAnalysisController/queryPatientForecastData',
    method: 'post',
    data: params
  })
}

export function queryDrgPatientBasicInfoData (params) {
  return request({
    url: '/newDrgBusinessPatientAnalysisController/queryDrgPatientBasicInfoData',
    method: 'post',
    data: params
  })
}

export function queryDrgPatientCostInfoData (params) {
  return request({
    url: '/newDrgBusinessPatientAnalysisController/queryDrgPatientCostInfoData',
    method: 'post',
    data: params
  })
}

export function queryDrgPatientForecastData (params) {
  return request({
    url: '/newDrgBusinessPatientAnalysisController/queryDrgPatientForecastData',
    method: 'post',
    data: params
  })
}

export function queryDrgPatientBasicSortInfo (params) {
  return request({
    url: '/newDrgBusinessPatientAnalysisController/queryDrgPatientBasicSortInfo',
    method: 'post',
    data: params
  })
}
