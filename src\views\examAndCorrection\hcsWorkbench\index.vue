<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
              show-date-range
              :show-in-date-range="false"
              :show-se-date-range="false"
              show-hos-dept
              showPagination
              :totalNum="total"
              :container="true"
              :initTimeValueNotQuery="false"
              @query="handleSearchList" @reset="handleResetSearch" ref="listQuery">

      <template slot="buttons">
        <el-popconfirm
          confirm-button-text='确定'
          cancel-button-text='导出全部'
          icon="el-icon-info"
          icon-color="red"
          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>

      <template slot="extendFormItems">
        <el-form-item label="场景类型" prop="ruleScenType">
          <drg-dict-select dicType="RULE_SCEN_TYPE" placeholder="请选择场景类型" v-model="listQuery.ruleScenType"
                           @change="getDataIsuue"/>
        </el-form-item>
      </template>

      <template slot="containerContent">
        <div style="height: 300px; width: 100%">
          <el-row :gutter="10" style="height: 100%" class="custem-row" ref="custemRowContent">
            <el-col :span="8" style="height: 100%">
              <el-card style="height: 100%">
                <drg-title-line title="违规病例占比"></drg-title-line>
                <drg-echarts :options="violationMedAnysOptions" :height="'80%'"/>
              </el-card>
            </el-col>
            <el-col :span="8" style="height: 100%">
              <el-card style="height: 100%">
                <drg-title-line title="违规类型统计"></drg-title-line>
                <drg-echarts :options="violationRuleTypeAnysOptions" :height="'80%'"/>
              </el-card>
            </el-col>
            <el-col :span="8" style="height:100%">
              <el-card style="height: 100%" :height="'80%'">
                <drg-title-line title="审核金额统计"></drg-title-line>
                <el-table ref="violationAmountTable"
                          id="violationAmountTable"
                          size="mini"
                          height="100%"
                          :data="violationAmountList"
                          v-loading="listLoading"
                          border>
                  <el-table-column label="错误类型" align="left" width="80">
                    <template slot-scope="scope">{{ scope.row.errorType | formatIsEmpty }}</template>
                  </el-table-column>
                  <el-table-column label="错误名称" align="left">
                    <template slot-scope="scope">{{ scope.row.errorDesc | formatIsEmpty }}</template>
                  </el-table-column>
                  <el-table-column label="违规病案数" align="center" width="100">
                    <template slot-scope="scope">
                      <div v-if="Number(scope.row.violationSize)>0" class='skip'
                           @click="queryTotalMedicalNum(scope.row)">
                        {{ scope.row.violationSize | formatIsEmpty }}
                      </div>
                      <div v-if="Number(scope.row.violationSize)==0" style="color:#000000">
                        {{ scope.row.violationSize | formatIsEmpty }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="违规金额" align="center" width="80">
                    <template slot-scope="scope">{{ scope.row.violationAmount | formatIsEmpty }}</template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="width: 100%">
          <el-table ref="inGroupListTable"
                    id="inGroupTable"
                    highlight-current-row
                    size="mini"
                    :header-cell-style="{'text-align':'center'}"
                    :data="list"
                    :height="tableHeight"
                    v-loading="listLoading"
                    @sort-change='sort_change'
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column
              label="序号"
              align="center"
              type="index"
              width="50">
            </el-table-column>
            <el-table-column label="违规规则编码" prop="ruleDetlCodg" width="100" align="left"
                             :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.ruleDetlCodg | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="违规类型名称" prop="ruleTypeName" width="120">
              <template slot-scope="scope">{{ scope.row.ruleTypeName | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="违规规则名称" align="left" prop="ruleGrpName" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.ruleGrpName | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="规则数据分组" align="center" width="194" prop="dataGrp" sortable='custom'>
              <template slot-scope="scope">
                {{ $somms.getDictValueByType(scope.row.dataGrp, 'DATA_TYPE_CODE') }}
              </template>
            </el-table-column>

            <el-table-column label="违规病案数" prop="violationMedSize" align="right" width="70"
                             :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.violationMedSize)>0" class='skip' @click="queryTotalMedicalNum(scope.row)">
                  {{ scope.row.violationMedSize | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.violationMedSize)==0" style="color:#000000">
                  {{ scope.row.violationMedSize | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="违规金额" prop="violationAmount" align="right" width="70"
                             :show-overflow-tooltip="true">
              <template slot-scope="scope">
                {{ scope.row.violationAmount | formatIsEmpty }}
              </template>
            </el-table-column>
            <el-table-column label="违规金额占比" prop="itemViolationAmountRatio" align="right" width="75"
                             :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.itemViolationAmountRatio)>0">
                  {{ (scope.row.itemViolationAmountRatio * 100).toFixed(2) + "%"  | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.itemViolationAmountRatio)==0" style="color:#000000">
                  {{ scope.row.itemViolationAmountRatio | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import {queryDataIsuue, querySelectTreeAndSelectList, queryLikeDipGroupByPram} from '@/api/common/drgCommon'
import {queryAnalysisGraphic, queryAnalysisDataList} from '@/api/examCorrection/hcsWorkBench'
import {formatDate} from '@/utils/date'
import {elExportExcel} from '@/utils/exportExcel'
import echarts from 'echarts'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  ruleScenType: '1',
  deptCode:'',
  begnDate: '',
  expiDate: '',
  seStartTime: '',
  seEndTime: '',
  inHosFlag: ''
}
export default {
  name: 'hcsWorkbench',
  computed: {},
  components: {},
  data() {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      list: null,
      tempList: [],
      total: null,
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      b16c: null,
      medicalRecordNum: 0,
      tableHeight: 0,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      violationAmountTable: null,
      violationMedAnysOptions: {},
      violationRuleTypeAnysOptions: {},
      violationMedAnalysisVo: {},
      violationRuleTypeAnalysisVo: {},
      medDataList: [],
      medRatioList: [],
      violationAmountList: [],
      medViolationRatio: 0,
      medSumSize: 0,
      medNormalSize: 0,
      medViolationSize: 0,
      ruleTypes: [],
      ruleViolationMedSize: [],
      ruleViolationMedRatioSize: []
    }
  },
  created() {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty(value) {
      if (value) {
        return value
      } else {
        return '0'
      }
    },
    formatType(value) {
      if (value == '1') {
        return '分组器结果'
      } else if (value == '2') {
        return '排除病案'
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.inGroupListTable.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - 180 - (this.$refs.inGroupListTable.$el.offsetTop - 35)
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.inGroupListTable.$el.offsetTop - 35
      }
      if (Object.keys(this.$route.query).length > 0) {
        if (this.$route.query.inHosFlag) {
          this.listQuery.inHosFlag = this.$route.query.inHosFlag
        }
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.listQuery.jumpTimeChange('out', this.$route.query, this.listQuery)
        }
        if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
          this.$refs.listQuery.jumpTimeChange('in', this.$route.query, this.listQuery)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.listQuery.jumpTimeChange('se', this.$route.query, this.listQuery)
        }
      }
      this.handleSearchList()
      this.generatorMedCirclePie()
      this.generatorVioltionPie()
    })
  },
  methods: {
    handleSelectionChange(val) {
      if (val.length == 0) {
        this.tempList = []
      } else {
        this.tempList = this.list.filter(data => {
          for (const v of val) {
            if (data.priOutHosDeptCode == v.priOutHosDeptCode) {
              return true
            }
          }
          return false
        })
      }
    },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    my_desc_sort(a, b) {
      if (Number(a.inGroupRate) > Number(b.inGroupRate)) {
        return -1
      } else if (Number(a.inGroupRate) < Number(b.inGroupRate)) {
        return 1
      } else {
        return 0
      }
    },
    my_asc_sort(a, b) {
      if (Number(a.inGroupRate) < Number(b.inGroupRate)) {
        return -1
      } else if (Number(a.inGroupRate) > Number(b.inGroupRate)) {
        return 1
      } else {
        return 0
      }
    },
    sort_change(column) {
      // this.current_page = 1
      if (column.prop === 'groupRate') {
        if (column.order === 'descending') {
          this.list = this.list.sort(this.my_desc_sort)
        } else if (column.order === 'ascending') {
          this.list = this.list.sort(this.my_asc_sort)
        }
      }
      // this.list = this.filtered_data.slice(0, 200) // show only one page
    },
    getDataIsuue() {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
        this.getTopCount()
        // this.getNoGroupResonCount()
      })
    },
    getList() {
      this.listLoading = true
      this.getParamByBaseQuery(this.submitListQuery, this.listQuery)
      queryAnalysisDataList(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    allExcel() {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.inGroupListTable.$children, document.getElementById('inGroupTable').children[1].children[0].children[1].children[0].childNodes, queryAnalysisDataList, '违规明细')
    },
    getTopCount() {
      this.listLoading = true
      this.getParamByBaseQuery(this.submitListQuery, this.listQuery)
      queryAnalysisGraphic(this.submitListQuery).then(response => {
        this.listLoading = false
        this.medViolationRatio = response.data.violationMedAnalysisVo.medViolationRatio
        this.medSumSize = response.data.violationMedAnalysisVo.medSumSize
        this.medViolationSize = response.data.violationMedAnalysisVo.medViolationSize
        this.medNormalSize = this.medSumSize - this.medViolationSize
        this.ruleTypes = response.data.ruleTypes
        this.ruleViolationMedSize = response.data.ruleViolationMedSize
        this.ruleViolationMedRatioSize = response.data.ruleViolationMedRatioSize
        this.violationAmountList = response.data.violationAmountAnalysisVo
        this.generatorMedCirclePie()
        this.generatorVioltionPie()
      })
    },
    dateChangeCysj(val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.listQuery.cysj = [this.$somms.getYearMonthStartTime(), this.$somms.getYearMonthEndTime()]
        this.cy_start_date = this.$somms.getYearMonthStartTime()
        this.cy_end_date = this.$somms.getYearMonthEndTime()
        this.seStartTime = this.$somms.getYearMonthStartTime()
        this.seEndTime = this.$somms.getYearMonthEndTime()
      }
      this.getList()
      this.getTopCount()
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList() {
      this.getTopCount()
      this.getList()

    },
    handleResetSearch() {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.tempList = []
      this.getDataIsuue()
    },
    customColorMethod(percentage) {
      if (Number(percentage) < 80) {
        return '#FF0000'
      } else if (Number(percentage) < 85) {
        return '#FA8072'
      } else if (Number(percentage) < 90) {
        return '#FFA500'
      } else {
        return '#67c23a'
      }
    },
    // 下转详情点击
    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      // 第一列合并
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return {
            rowspan: 7,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
        // else if (rowIndex === 3) {
        // return {
        //   rowspan: 4,
        //   colspan: 1
        // };
        // }
      }
    },
    exportExcel() {
      let tableId = 'violationAmountTable'
      let fileName = '违规明细信息'
      elExportExcel(tableId, fileName)
    },
    getParamByBaseQuery(targetParam, sourceParam) {
      if (targetParam) {
        const keys = Object.keys(targetParam)
        // 更新参数值
        keys.forEach(key => {
          targetParam[key] = sourceParam[key]
        })
      }
    },
    // 环形图 violationAmountTable: {},violationMedAnysOptions: {}
    generatorMedCirclePie() {
      this.violationMedAnysOptions = {
        title: {
          text: (this.medViolationRatio ? (this.medViolationRatio * 100).toFixed(2) : 0) + '%',
          left: 'center',
          top: 'center',
          textStyle: {
            color: '#000',
            fontSize: 20
          }
        },
        tooltip: { // 提示框，可以在全局也可以在
          trigger: 'item', // 提示框的样式
          formatter: '{a} <br/>{b}: {c} ({d}%)',
          backgroundColor: 'rgba(0,0,0,0.8)',
          color: '#fff', // 提示框的背景色
          textStyle: { // 提示的字体样式
            color: 'white'
          }
        },
        legend: { // 图例
          orient: 'vertical', // 图例的布局，竖直    horizontal为水平
          x: 'left', // 图例显示在右边
          data: ['正常病案数', '违规病案数'],
          textStyle: { // 图例文字的样式
            color: '#333', // 文字颜色
            fontSize: 12 // 文字大小
          }
        },
        series: [{
          name: '',
          type: 'pie', // 环形图的type和饼图相同
          radius: ['50%', '70%'], // 饼图的半径，第一个为内半径，第二个为外半径
          avoidLabelOverlap: false,
          color: ['#409EFF', '#fa8000'],
          emphasis: { // 使用emphasis
            disable: false,
            scale: false, // 不缩放
            scaleSize: 0 // 为了防止失效直接设置未0
          },
          label: {
            normal: { // 正常的样式
              show: true,
              position: 'left'
            },
            emphasis: { // 选中时候的样式
              show: true,
              textStyle: {
                fontSize: '20',
                fontWeight: 'bold'
              }
            }
          },
          labelLine: {
            normal: {
              show: false
            }
          },
          data: [
            {value: this.medNormalSize, name: '正常病案数'},
            {value: this.medViolationSize, name: '违规病案数'}
          ]
        }]
      }
    },
    // 下钻查询违规病案详情
    queryTotalMedicalNum(row) {
      let transformBaseParam = Object.assign({}, defaultListQuery)
      this.getParamByBaseQuery(transformBaseParam, this.listQuery)

      // 根据点击的行数据设置查询参数
      if(row.errorType){
        transformBaseParam.errorType = row.errorType
      }
      if (row.ruleDetlCodg) {
        transformBaseParam.ruleDetlCodg = row.ruleDetlCodg
      }
      if (row.ruleType) {
        transformBaseParam.ruleType = row.ruleType
      }

      this.$router.push({
        path: '/examAndCorrection/vialationPatientDetails',
        query: transformBaseParam
      })
    },
    // 柱状图+折线图
    generatorVioltionPie() {
      // 基于准备好的dom，初始化echarts实例
      let option = {
        color: ['#458cf6', '#83ced0'],
        title: {
          text: '',
          left: '5%',
          textStyle: {
            fontSize: Number(getComputedStyle(document.documentElement).getPropertyValue('--biggerSize').replace('px', ' '))
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        grid: {
          bottom: '10%'
        },
        legend: {
          right: '1%',
          textStyle: {
            fontSize: Number(getComputedStyle(document.documentElement).getPropertyValue('--smallSize').replace('px', ' '))
          },
          data: ['病例数', '病例占比']
        },
        xAxis: [
          {
            type: 'category',
            splitLine: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            data: this.ruleTypes,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位/万元',
            min: 0,
            axisTick: {
              show: false
            },
            axisLine: { // y轴
              show: false
            },
            splitLine: {
              show: false
            }
          },
          {
            type: 'value',
            // min: 0,
            // interval: 1,
            axisTick: {
              show: false
            },
            axisLine: { // y轴
              show: false
            },
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: '病例数',
            type: 'bar',
            barWidth: 20,
            data: this.ruleViolationMedSize
          },
          {
            name: '病例占比',
            type: 'line',
            yAxisIndex: 1,
            data: this.ruleViolationMedRatioSize
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      this.violationRuleTypeAnysOptions = option
    }
  }
}
</script>
<style scoped>
.number {
  margin-top: 2px;
  font-size: 28px;
  font-weight: bold;
  font-family: Microsoft YaHei
}

.fen {
  font-size: 10px;
}

.compare1 {
  font-weight: normal;
  font-size: 10px;
  color: grey;
  width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

.compare2 {
  font-weight: normal;
  font-size: 10px;
  color: grey;
  margin-top: 3px;
  width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

.compareNum {
  font-weight: bold;
  margin-left: 5px;
}

.compareRateIncrease {
  font-weight: bold;
  color: #409EFF;
  margin-left: 5px;
}

.compareRateEqual {
  font-weight: bold;
  color: #000000;
  margin-left: 5px;
}

.compareRateDecrease {
  font-weight: bold;
  color: red;
  margin-left: 5px;
}

.custem-row /deep/ .el-col {
  border-radius: 4px;
  margin-bottom: 0px;
}
</style>
