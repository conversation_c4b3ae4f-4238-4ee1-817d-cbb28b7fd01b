<template>
  <div class="site-wrapper site-page--login">
    <div class="site-content__wrapper">
      <div class="site-content">
        <div class="brand-info">
          <h2 class="brand-info__text">付费辅助决策管理平台</h2>
          <p class="brand-info__intro">DRG+DIP Paid Fine Management Platform</p>
        </div>
        <div class="login-main">
          <h3 class="login-title" style="font-size: 25px;font-family: 微软雅黑">登录</h3>
          <el-form :model="loginForm" :rules="loginRules" ref="loginForm" @keyup.enter.native="handleLogin" status-icon>
            <el-form-item prop="username" size="big">
              <el-input class="input-style" name="username"
                        type="text"
                        v-model="loginForm.username"
                        autoComplete="on"
                        @change="changeUsername"
                        placeholder="请输入用户名"
              >
              </el-input>
            </el-form-item>
            <el-form-item prop="password" size="big">
              <el-input class="input-style" name="password"
                        :type="pwdType"
                        @keyup.enter.native="handleLogin"
                        v-model="loginForm.password"
                        autoComplete="on"
                        placeholder="请输入密码">
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button class="login-btn-submit" type="primary" :loading="loading" @click.native.prevent="handleLogin">
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="login-footer">
    </div>
  </div>
</template>

<script>
import { isvalidUsername } from '@/utils/validate'
import { getCookie, setCookie, setSupport } from '@/utils/support'
import login_center_bg from '@/assets/images/login_center_bg.png'
import { querySysOutOfDate } from '@/api/login'

export default {
  name: 'login',
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!isvalidUsername(value)) {
        callback(new Error('请输入正确的用户名'))
      } else {
        callback()
      }
    }
    const validatePass = (rule, value, callback) => {
      if (value.length < 3) {
        callback(new Error('密码不能小于3位'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          { required: true, message: '帐号不能为空', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' }
        ]
      },
      loading: false,
      pwdType: 'password',
      login_center_bg,
      dialogVisible: false,
      supportDialogVisible: false,
      expireWarnInfoHidden: false,
      expireErrorInfoHidden: false,
      expireWarnInfo: '',
      expireErrorInfo: '',
      editVisible: false
    }
  },
  created () {
    // 查询系统是否过期
    this.sysOutOfDate()
    this.loginForm.username = getCookie('username')
    this.loginForm.password = getCookie('password')
    if (this.loginForm.username === undefined || this.loginForm.username == null || this.loginForm.username === '') {
      this.loginForm.username = ''
    }
    if (this.loginForm.password === undefined || this.loginForm.password == null) {
      this.loginForm.password = ''
    }
  },
  methods: {
    open () {
      this.editVisible = true
    },
    showPwd () {
      if (this.pwdType === 'password') {
        this.pwdType = ''
      } else {
        this.pwdType = 'password'
      }
    },
    handleLogin () {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$store.dispatch('Login', this.loginForm).then(() => {
            this.loading = false
            setCookie('username', this.loginForm.username, 15)
            // setCookie("password",this.loginForm.password,15);
            this.$router.push({ path: '/' })
          }).catch(() => {
            this.loading = false
          })
        } else {
          console.log('参数验证不合法！')
          return false
        }
      })
    },
    sysOutOfDate () {
      querySysOutOfDate().then(response => {
        let data = response.data
        if (data) {
          if (data.errorInfo) {
            this.expireErrorInfoHidden = true
            this.expireErrorInfo = data.errorInfo
          } else if (data.warningInfo) {
            this.expireWarnInfoHidden = true
            this.expireWarnInfo = data.warningInfo
          }
        }
      }).catch(() => {
        this.loading = false
      })
    },
    changeUsername () {
      if (this.loginForm.username == 'developer') {
        this.expireErrorInfoHidden = false
        this.expireWarnInfoHidden = false
      } else {
        this.sysOutOfDate()
      }
    },
    handleTry () {
      this.dialogVisible = true
    },
    dialogConfirm () {
      this.dialogVisible = false
      setSupport(true)
    },
    dialogCancel () {
      this.dialogVisible = false
      setSupport(false)
    }
  }
}
</script>

<style lang="scss">
.site-wrapper.site-page--login {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  //background-color: rgba(38, 50, 56, .6);
  overflow: hidden;

  &:before {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    content: "";
    background-image: url(~@/assets/images/login_center_bg.png);
    background-size: cover;
  }

  .site-content__wrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: transparent;
  }

  .site-content {
    min-height: 100%;
    padding: 30px 500px 30px 30px;
  }

  .brand-info {
    margin: 220px 100px 0 90px;
    color: #fff;
  }

  .brand-info__text {
    margin: 0 0 22px 0;
    font-size: 48px;
    font-weight: 400;
    text-transform: uppercase;
  }

  .brand-info__intro {
    margin: 10px 0;
    font-size: 25px;
    line-height: 1.58;
    opacity: .6;
  }

  .login-main {
    position: absolute;
    top: 0;
    right: 0;
    padding: 180px 60px 180px;
    width: 525px;
    min-height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
  }

  .login-title {
    font-size: 16px;
  }

  .login-captcha {
    overflow: hidden;

    > img {
      width: 100%;
      cursor: pointer;
    }
  }

  .login-btn-submit {
    width: 100%;
    margin-top: 38px;
    height: 50px;
    //border-radius: 25px;
    font-size: var(--biggerSize);
    background-color: #0BA2B3;
  }

  .login-btn-submit:hover {
    opacity: 0.8;
  }

  .input-style {
    font-size: var(--textSize);
  }
}

.login-footer {
  position: absolute;
  right: 39%;
  bottom: 10%;
  color: white;
  font-size: 16px;
}
</style>
