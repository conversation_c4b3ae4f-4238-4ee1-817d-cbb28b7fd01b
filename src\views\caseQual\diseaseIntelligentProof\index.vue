<template>
  <div class="app-container">
    <el-card class="filter-container" shadow="never">
      <div>
        <i class="el-icon-search"></i>
        <span style="font-size:14px;">查询条件</span>
        <el-button
          style="float: right;margin-top:27px;"
          @click="handleSearchList()"
          type="primary"
          size="mini">
          查询结果
        </el-button>
        <el-button
          style="float: right;margin-right: 15px;margin-top:27px;"
          @click="handleResetSearch()"
          size="mini">
          重置
        </el-button>
      </div>
      <div style="margin-top: 10px">
        <el-form :inline="true" :model="listQuery" size="mini" label-width="100px">
          <el-form-item label="日志号：">
            <el-input style="width: 150px" v-model="listQuery.id" placeholder="日志号"></el-input>
          </el-form-item>
          <el-form-item label="上传方式：">
            <el-select v-model="listQuery.type" placeholder="全部" clearable style="width: 150px">
              <el-option
                v-for="item in uploadType"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上传时间：">
            <el-date-picker style="width: 150px"
              v-model="listQuery.data_upld_time"
              value-format="yyyy-MM-dd"
              type="updt_date"
              placeholder="选择日期"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="处理结果：">
            <el-select v-model="listQuery.result" placeholder="全部" clearable style="width: 150px">
              <el-option
                v-for="item in uploadResult"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <div class="table-container">
      <el-table ref="dataHandleLogTable"
                :data="list"
                style="width: 100%"
                v-loading="listLoading"
                border>
        <el-table-column label="日志号"  align="center">
          <template slot-scope="scope">{{scope.row.id}}</template>
        </el-table-column>
        <el-table-column label="上传方式"  align="center">
          <template slot-scope="scope">{{scope.row.type | formatType}}</template>
        </el-table-column>
        <el-table-column label="病案总数"  align="center">
          <template slot-scope="scope">{{scope.row.medcas_val}}</template>
        </el-table-column>
        <el-table-column label="完整性校验通过数"  align="center">
          <template slot-scope="scope">{{scope.row.integrity_chk_pass_val}}</template>
        </el-table-column>
        <el-table-column label="逻辑性校验通过数"  align="center">
          <template slot-scope="scope">{{scope.row.logic_chk_pass_val}}</template>
        </el-table-column>
        <el-table-column label="数据处理结果"  align="center">
          <template slot-scope="scope">{{scope.row.result | formatResult}}</template>
        </el-table-column>
        <el-table-column label="数据上传时间"  align="center">
          <template slot-scope="scope">{{scope.row.data_upld_time | formatTime}}</template>
        </el-table-column>
        <el-table-column label="数据处理时长"  align="center">
          <template slot-scope="scope">{{scope.row.datapros_dura}}</template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        layout="total, sizes,prev, pager, next,jumper"
        :page-size="listQuery.pageSize"
        :page-sizes="[50,100,200]"
        :current-page.sync="listQuery.pageNum"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { fetchList } from '@/api/dataHandle/dataHandleLog'
import { formatDate } from '@/utils/date'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 5,
  id: null,
  type: null,
  data_upld_time: null,
  result: null
}
export default {
  name: 'diseaseIntelligentProof',
  data () {
    return {
      uploadType: [{
        value: '1',
        label: '文件上传'
      }, {
        value: '2',
        label: '接口上传'
      }],
      uploadResult: [{
        value: '1',
        label: '处理成功'
      }, {
        value: '0',
        label: '存在异常'
      }],
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick (picker) {
            picker.$emit('pick', new Date())
          }
        }, {
          text: '昨天',
          onClick (picker) {
            const updt_date = new Date()
            updt_date.setTime(updt_date.getTime() - 3600 * 1000 * 24)
            picker.$emit('pick', updt_date)
          }
        }, {
          text: '一周前',
          onClick (picker) {
            const updt_date = new Date()
            updt_date.setTime(updt_date.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', updt_date)
          }
        }]
      },
      listLoading: true,
      list: null,
      total: null,
      listQuery: Object.assign({}, defaultListQuery)
    }
  },
  created () {
    this.getList()
  },
  filters: {
    formatTime (time) {
      let updt_date = new Date(time)
      return formatDate(updt_date, 'yyyy-MM-dd hh:mm:ss')
    },
    formatType (value) {
      if (value === 1) {
        return '文件上传'
      } else if (value === 2) {
        return '接口上传'
      }
    },
    formatResult (value) {
      if (value === 1) {
        return '处理成功'
      } else if (value === 0) {
        return '存在异常'
      }
    }
  },
  methods: {
    getList () {
      this.listLoading = true
      fetchList(this.listQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.listQuery.pageNum = 1
      this.getList()
    },
    handleResetSearch () {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.getList()
    }
  }
}
</script>
<style></style>
