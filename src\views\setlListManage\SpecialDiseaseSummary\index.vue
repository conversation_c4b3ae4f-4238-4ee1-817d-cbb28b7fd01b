<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
              show-date-range
              show-in-date-range
              show-se-date-range
              show-patient-num
              :show-hos-dept="{ show: this.$somms.hasHosRole()}"
              showPagination
              :totalNum="total"
              headerTitle="查询条件"
              contentTitle="结算清单详情"
              :container="true"
              :exportExcel="{ 'tableId': tableId, exportName: '结算清单'}"
              :exportExcelFun="queryPageData"
              :exportExcelHasChild="false"
              :initTimeValueNotQuery="false"
              ref="somForm"
              @query="handleSearchList">

      <template slot="extendFormItems">

        <el-form-item label="重症及多学科诊治" prop="scsMDT">
          <drg-dict-select dicType="BOOLEAN" placeholder="是否重症及多学科诊治病例" v-model="listQuery.scsMDT" @change="getDataIsuue"/>
        </el-form-item>
        <el-form-item label="超长住院病例" prop="isUltraLong">
          <drg-dict-select dicType="BOOLEAN" placeholder="是否超长住院病例" v-model="listQuery.isUltraLong" @change="getDataIsuue"/>
        </el-form-item>
        <el-form-item label="超高费用病例" prop="isUltraHigh">
          <drg-dict-select dicType="BOOLEAN" placeholder="是否超高费用病例" v-model="listQuery.isUltraHigh" @change="getDataIsuue"/>
        </el-form-item>
      </template>


      <!-- 内容 -->
      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  :id="tableId"
                  size="mini"
                  stripe
                  height="100%"
                  :data="list"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>
          <el-table-column label="序号" type="index" align="right">
          </el-table-column>

          <el-table-column label="病案号" prop="a48" align="left"  width="100px">
            <template slot-scope="scope">{{scope.row.a48 | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="结算id" prop="settleMentID" align="left"  width="200px">
            <template slot-scope="scope">{{scope.row.settleMentID | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="姓名" prop="a11" align="left"  width="90" >
            <template slot-scope="scope">{{scope.row.a11 | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column prop="diagCodeAndName" label="诊断名称编码"  align="left"  width="200px" :show-overflow-tooltip="true" sortable>
          </el-table-column>
          <el-table-column prop="oprnCodeAndName" label="手术名称编码"  align="left" width="200px" :show-overflow-tooltip="true"  sortable>
          </el-table-column>
          <el-table-column label="DRGs编码和名称" prop="drgsCodeAndName" align="left" width="200px" :show-overflow-tooltip="true" v-if="this.$somms.getGroupType() === '3'">
            <template slot-scope="scope">{{scope.row.drgsCodeAndName | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="DIP编码和名称" prop="dipCodeAndName" align="left"  width="200px" :show-overflow-tooltip="true" v-if="this.$somms.getGroupType() === '1'">
            <template slot-scope="scope">{{scope.row.dipCodeAndName | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column prop="type" label="特病单议类型"  align="left" width="160px" :show-overflow-tooltip="true" sortable>
          </el-table-column>
          <el-table-column prop="desc" label="特病单议归类原因"  align="left" width="300px":show-overflow-tooltip="true"  sortable>
          </el-table-column>
          <el-table-column prop="sumfee" label="住院费"  align="right"  width="100px" sortable>
          </el-table-column>
          <el-table-column prop="refeFee" label="参考费用"  align="right" width="100px"  sortable>
          </el-table-column>
          <el-table-column prop="b20" label="住院天数"  align="right"  width="120px" sortable>
          </el-table-column>
          <el-table-column prop="avgDays" label="平均住院天数"  align="right" width="150px"  sortable>
          </el-table-column>
          <el-table-column prop="totalDays" label="重症病房使用天数"  align="right" width="180px"  sortable>
          </el-table-column>
          <el-table-column prop="deptName" label="科室名称"  align="left" width="120px"  sortable>
          </el-table-column>
          <el-table-column prop="doctorName" label="医生姓名"  align="left" width="100px"  sortable>
          </el-table-column>
        </el-table>


      </template>
    </drg-form>
  </div>
</template>
<script>
import { querySelectTreeAndSelectList, queryLikeDipGroupByPram, queryLikeDrgsByPram, queryDataIsuue } from '@/api/common/drgCommon'
import { querySpecialDisease as queryPageData, deleteDataById, getHisDate } from '@/api/medicalQuality/settleList'
import { formatDate } from '@/utils/date'
import { elExportExcel } from '@/utils/exportExcel'
import { medicalDeleteDataUpload } from '@/api/medicalQuality/medicalDeleteDataUpload'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  a48: null,
  a11: null,
  b16c: null,
  b34c: null,

  settle_start_date: null,
  settle_end_date: null,
  ry_start_date: null,
  ry_end_date: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date,
  queryDipGroup: '',
  queryDrg: '',
  tableHeight: 0,
  scsMDT: null,
  isUltraLong: null,
  isUltraHigh: null
}
export default {
  name: 'SpecialDiseaseSummary',
  components: { },
  inject: ['reload'],
  data () {
    return {
      showFlag: false,
      dialogVisible: false,
      tableId: 'dataTable',
      listLoading: true,
      list: null,
      total: null,
      settle_start_date: null,
      settle_end_date: null,
      ry_start_date: null,
      ry_end_date: null,
      cy_start_date: null,
      cy_end_date: null,
      dictVoList: {},
      grpType: this.$somms.getGroupType(),
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      depts: [],
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created () {

  },
  watch: {
    list: function () {
      this.$nextTick(() => {
        if (this.$refs.settleListTable) {
          this.$refs.settleListTable.doLayout()
        }
      })
    }
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime (time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },
    formatA12c (value) {
      if (value == '1') {
        return '男'
      } else if (value == '2') {
        return '女'
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.findSelectTreeAndSelectList()
      if (Object.keys(this.$route.query).length > 0) {
        if (this.$route.query.lookOver) {
          this.listQuery.lookOver = this.$route.query.lookOver
        }
        // 时间需要在最下面设置
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.listQuery)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.listQuery)
        }
      }
      this.getList()
    })
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'B34C')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]

        this.getList()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.a48 = this.listQuery.medcasCodg
      this.submitListQuery.a11 = this.listQuery.a11
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.b34c = this.listQuery.b34c
      this.submitListQuery.settle_start_date = this.settle_start_date
      this.submitListQuery.settle_end_date = this.settle_end_date
      this.submitListQuery.ry_start_date = this.listQuery.inStartTime
      this.submitListQuery.ry_end_date = this.listQuery.inEndTime
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.lookOver = this.listQuery.lookOver
      this.submitListQuery.isAdjustable = this.listQuery.isAdjustable
      this.submitListQuery.isNullPreHosCost = this.listQuery.isNullPreHosCost
      this.submitListQuery.listSerialNumFlag = this.listQuery.listSerialNumFlag
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.scsMDT = this.listQuery.scsMDT
      this.submitListQuery.isUltraLong = this.listQuery.isUltraLong
      this.submitListQuery.isUltraHigh = this.listQuery.isUltraHigh
      this.submitListQuery.groupType = this.listQuery.groupType
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.settleListTable.$children, document.getElementById('slTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病案数据')
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSelect (item) {
      this.listQuery.dipCodg = item.dipCodg
    },
    querySearchAsyncForDrg (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocompleteForDrg.handleFocus()
      })
    },
    handleSelectForDrg (item) {
      this.listQuery.drgCodg = item.drgsCode
    },
    dateChangeSettle_date (val) {
      if (val) {
        this.settle_start_date = val[0]
        this.settle_end_date = val[1]
      } else {
        this.settle_start_date = null
        this.settle_end_date = null
      }
    },
    dateChangeRysj (val) {
      if (val) {
        this.ry_start_date = val[0]
        this.ry_end_date = val[1]
      } else {
        this.ry_start_date = null
        this.ry_end_date = null
      }
      this.getList()
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.getList()
    },
    handleResetSearch () {
      this.getDataIsuue()
    },
    preview (row) {
      this.$router.push({ path: '/setlListManage/mdcsListPreview', query: { k00: row.k00, id: row.id } })
    },
    handleShowMedicalDetail (index, row) {
      this.$router.push({ path: '/setlListManage/setlListDetail', query: { k00: row.k00, id: row.id } })
    },
    newHandleShowMedicalDetail (index, row, type) {
      let obj = { path: '/setlListManage/setlListInfo2', query: { k00: row.k00, id: row.id } }
      if (type === '2') {
        obj.query.see = true
      } else {
        obj.query.see = false
      }
      this.$router.push(obj)
    },
    filterSex (value, row) {
      return row.a12c === value
    },
    exportExcel () {
      let tableId = 'slTable'
      let fileName = '病案数据'
      elExportExcel(tableId, fileName)
    },
    refresh () {
      this.reload()
    },

    upload (data) {
      let params = new FormData()
      params.append('file', data.file)
      medicalDeleteDataUpload(params).then(res => {
        if (res.code == 200) {
          this.dialogVisible = false
          this.$refs.upload.clearFiles()
          this.$message.success('上传成功')
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
      })
    },
    deleteData (index, item) {
      let params = {
        ids: []
      }
      params.ids.push(item.id)
      deleteDataById(params).then(res => {
        if (res.code == 200) {
          this.$message.success('删除成功')
          this.handleSearchList()
        }
      })
    },
    recoveryData (index, item) {
      let params = {
        ids: []
      }
      params.ids.push(item.id)
      recoveryDataById(params).then(res => {
        if (res.code == 200) {
          this.$message.success('恢复成功')
          this.handleSearchList()
        }
      })
    },
    getParams () {
      return this.submitListQuery
    },
    extractHisViueData () {
      getHisDate({
        startTime: this.getParams().cy_start_date,
        endTime: this.getParams().cy_end_date,
        medcasno: this.getParams().a48
      })
    }
  }
  // beforeRouteLeave(to, from ,next) {
  //   let list = document.getElementsByClassName("el-tooltip__popper");
  //   // console.log("🚀 ~ file: index.vue ~ line 309 ~ deactivated ~ list", list)
  //   if (list.length > 0) {
  //     list[list.length - 1].style.display = "none";
  //   }
  //   next()
  // }
}
</script>
<style scoped>
/*时间样式设置*/
/deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner{
  width: 200px;
}
.autoSelectInputWidth{
  width: 178px;
}

</style>
