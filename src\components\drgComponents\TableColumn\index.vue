<template>
  <el-table-column :prop="prop"
                :type="type"
                :width="width"
                :minWidth="minWidth"
                :header-align="headerAlign"
                :align="align"
                :sortable="sortable"
                :label="label"
                :show-overflow-tooltip="showOverflowTooltip">

    <slot slot-scope="scope">
      <template v-if="dicType">
        {{ getDictLabel(scope.row[scope.column.property]) }}
      </template>
      <template v-else>
        <span v-if="scope.column.label == '序号'">
          {{ scope.$index + 1 }}
        </span>
        <span v-else>
          {{ scope.row[scope.column.property] }}
        </span>
      </template>
    </slot>
  </el-table-column>
</template>
<script>
export default {
  name: 'jpTableColumn',
  props: {
    prop: String,

    headerAlign: String,

    align: String,

    label: String,

    showOverflowTooltip: Boolean,

    type: {
      type: String,
      default: 'default'
    },

    width: {},

    minWidth: {},

    dicType: String,

    sortable: {
      type: [<PERSON>ole<PERSON>, String],
      default: false
    },

    // 类型 1：全局配置字典 2：清单字典
    dictSelectType: {
      type: Number,
      default: 1
    }

  },
  methods: {
    getDictLabel (val) {
      let dict = this.$somms.getDictValue(this.dicType, this.dictSelectType)
      if (dict) {
        let dictObj = dict.find(item => item.value == val)
        if (dictObj) {
          return dictObj.label
        }
        return val
      }
      return val
    }
  }
}
</script>
<style scoped>
</style>
