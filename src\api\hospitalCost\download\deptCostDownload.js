import request from '@/utils/request'

/**
 * 下载科室单元成本模板
 * @param params
 * @returns {*}
 */
export function downloadtemplate (params) {
  return request({
    url: '/DeptCostDownloadController/downloadtemplate',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}
/**
 * 下载科室收入模板
 * @param params
 * @returns {*}
 */
export function downloadDeptIncome (params) {
  return request({
    url: '/DeptCostDownloadController/downloadDeptIncome',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}
/**
 * 下载科室收入明细模板
 * @param params
 * @returns {*}
 */
export function downloadDeptIncomeDetails (params) {
  return request({
    url: '/DeptCostDownloadController/downloadDeptIncomeDetails',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}
export function inDirectDownloadtemplate (params) {
  return request({
    url: '/DeptCostDownloadController/inDirectDownloadtemplate',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}

/**
 * 生成数据
 * @param params
 * @returns {*}
 */
export function resultData (params) {
  return request({
    url: '/hospitalCostCalculateCostController/calculCostDetail',
    method: 'post',
    params: params
  })
}

/**
 * 查询数据
 * @param params
 * @returns {*}
 */
export function querydeptData (params) {
  return request({
    url: '/hospitalCostCalculateCostController/querydeptData',
    method: 'post',
    params: params
  })
}

export function queryList (params) {
  return request({
    url: '/hospitalCostCalculateCostController/querydeptData',
    method: 'post',
    params: params
  })
}

/**
 * 下载科室其他费用模板
 * @param params
 * @returns {*}
 */
export function downloadDeptCostOther (params) {
  return request({
    url: '/DeptCostDownloadController/downloadDeptCostOther',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}
