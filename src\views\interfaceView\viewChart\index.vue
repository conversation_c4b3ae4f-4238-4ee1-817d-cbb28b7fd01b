<template>
  <div class="app-container">
    <drg-form headerTitle="查询条件"
             :container="true"
             v-model="queryForm"
             show-date-range
             contentTitle="视图查看"
             @query="queryData">

      <!-- 内容 -->
      <template slot="containerContent">
        <textarea v-model="text" style="width: 100%;height: 80%"/>
      </template>
    </drg-form>
  </div>
</template>

<script>
import { queryViewData } from '@/api/interfaceView/viewChart'

export default {
  name: 'viewChart',
  data: () => ({
    queryForm: {},
    text: ''
  }),
  methods: {
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    queryData () {
      queryViewData(this.getParams()).then(res => {
        this.text = JSON.stringify(JSON.parse(res.data), null, '\t')
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
