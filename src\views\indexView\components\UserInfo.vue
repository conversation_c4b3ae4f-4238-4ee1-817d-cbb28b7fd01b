<template>
  <el-dialog class="user-info"
             width="25%"
             v-som-dialog-drag title="修改密码"
             :close-on-click-modal="false"
             :show-close="closeBtn"
             :close-on-press-escape="ESCFun"
             :modal-append-to-body="false"
             z-index="1000"
             :visible="visible" @close="closeDialog">
    <el-form :model="form" :rules="rules" ref="form">
<!--      <div style="margin-bottom: 20px;color: red" v-if="this.$store.getters.weekPassWord">现密码为弱密码,请修改密码!</div>-->
      <el-form-item label="用户名">
        <el-input v-model="form.username" disabled></el-input>
      </el-form-item>
<!--      <el-form-item label="修改昵称" prop="nickname">-->
<!--        <el-input v-model="form.nickname"></el-input>-->
<!--      </el-form-item>-->
      <el-form-item label="修改密码" prop="password">
        <el-input v-model="form.password" show-password></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input v-model="form.confirmPassword" show-password></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="cancelModification" :disabled="closeDis">取 消</el-button>
      <el-button type="primary" @click="modificationInfo('form')">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { modifyInformation } from '@/api/orgManamement'

export default {
  name: 'UserInfo',
  data () {
    let validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      }
      if (value !== '') {
        if (!(/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,16}$/.test(value))) {
          callback(new Error('密码必须包含大小写字母、数字、特殊字符中至少3种且长度不小于8'))
        } else {
          if (this.form.confirmPassword !== '') {
            this.$refs.form.validateField('confirmPassword')
          }
        }
        callback()
      }
    }
    let validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.form.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      visible: false,
      form: {
        nickname: '',
        id: '',
        username: '',
        password: '',
        confirmPassword: ''
      },
      closeBtn: true,
      ESCFun: true,
      closeDis: false,
      booleanVal: false,
      rules: {
        // nickname:[{required: true, message: '昵称不能为空', trigger: 'blur'}],
        // username: [{required: true, message: '用户名不能为空', trigger: 'blur'}],
        password: [{ validator: validatePass, trigger: 'blur' }],
        confirmPassword: [{ validator: validatePass2, trigger: 'blur' }]
      }
    }
  },
  // data: () => ({
  //   visible: false,
  //   form:{
  //     // nickname: '',
  //     id: '',
  //     username: '',
  //     password: '',
  //     confirmPassword: ''
  //   },
  //   rules: {
  //     // nickname:[{required: true, message: '昵称不能为空', trigger: 'blur'}],
  //     username: [{required: true, message: '用户名不能为空', trigger: 'blur'}]
  //     password: [{required: true, message: '用户名不能为空', trigger: 'blur'}]
  //     confirmPassword: [{required: true, message: '用户名不能为空', trigger: 'blur'}]
  //   }
  // }),
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false
    }
  },
  mounted () {
    this.form.username = this.$store.getters.name
    this.form.nickname = this.$store.getters.nickname
    this.form.id = this.$store.getters.id
    this.setBtn()
    this.showWarn()
  },
  methods: {
    cancelModification () {
      this.$message({
        type: 'info',
        message: '取消修改'
      })
      this.visible = false
    },
    modificationInfo (form) {
      if (this.form.password != this.form.confirmPassword) {
        this.$message({
          type: 'error',
          message: '两次密码不一致，请重新输入'
        })
      };
      this.$refs[form].validate((valid) => {
        if (valid) {
          // if(this.form.nickname.trim() != ''){
          if (this.form.username.trim() != '') {
            modifyInformation(this.form).then(() => {
              this.$message({
                type: 'success',
                message: '修改成功'
              })
              this.visible = false
              location.reload()
            })
          }
          // }
        }
        // else{
        //   this.$message({
        //     type: 'info',
        //     message: '请输入昵称或用户名'
        //   });
        // }
      })
    },
    closeDialog () {
      this.$emit('closeDialog', '')
    },
    showWarn () {
      if (this.$store.getters.weekPassWord) {
        this.$message({ message: '密码强度弱，请修改密码', type: 'warning' })
      }
    },
    setBtn () {
      this.closeBtn = !this.$store.getters.weekPassWord
      this.ESCFun = !this.$store.getters.weekPassWord
      this.closeDis = this.$store.getters.weekPassWord
    }
  },
  watch: {
    dialogFormVisible: function (visible) {
      this.visible = visible
    }
  }
}
</script>
