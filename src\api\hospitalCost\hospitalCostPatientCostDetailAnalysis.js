import request from '@/utils/request'

export function selectPatientTotalCost (params) {
  return request({
    url: '/hospitalCostPatientCostDetailAnalysisController/selectPatientTotalCost',
    method: 'post',
    params: params
  })
}

export function selectPatientCostDetail (params) {
  return request({
    url: '/hospitalCostPatientCostDetailAnalysisController/selectPatientCostDetail',
    method: 'post',
    params: params
  })
}

export function queryPatientBasicInfo (params) {
  return request({
    url: '/hospitalCostDiseaseCompareAnalysisController/queryPatientBasicInfo',
    method: 'post',
    data: params
  })
}

export function queryPatientCostItemInfo (params) {
  return request({
    url: '/hospitalCostDiseaseCompareAnalysisController/queryPatientCostItemInfo',
    method: 'post',
    data: params
  })
}

export function querySummaryInfo (params) {
  return request({
    url: '/hospitalCostDiseaseCompareAnalysisController/queryDiseaseData',
    method: 'post',
    params: params
  })
}

export function selectPatientItemTotalCost (params) {
  return request({
    url: '/hospitalCostPatientCostDetailAnalysisController/selectPatientItemTotalCost',
    method: 'post',
    params: params
  })
}

export function selectMSICData (params) {
  return request({
    url: '/hospitalCostPatientCostDetailAnalysisController/selectMSICData',
    method: 'post',
    params: params
  })
}

export function selectMSIImputationDiseaseData (params) {
  return request({
    url: '/hospitalCostPatientCostDetailAnalysisController/selectMSIImputationDiseaseData',
    method: 'post',
    params: params
  })
}
