import { queryUserInfo } from '@/api/common/drgCommon'
import store from '../store'

export function setParams (config) {
  if (config.params) {
    // 设置科室编码
    if (config.params.dataAuth && config.params.dataAuth == true) {
      queryUserInfo().then((res) => {
        if (res.code === 200) {
          if (res.data.b16c) {
            config.params.deptCode = res.data.b16c
            store.commit('setDeptCode', res.data.b16c)
          }
          store.commit('del_roles', res.data.userRoles)
          store.commit('set_roles', res.data.userRoles)
        }
      })
    }
  }
}
