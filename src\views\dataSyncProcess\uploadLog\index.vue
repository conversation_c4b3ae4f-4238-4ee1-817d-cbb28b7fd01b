<template>
  <div class="app-container">
    <drg-container :headerPercent="10">
        <template slot="header">
          <drg-title-line title="查询条件" />
          <!--  <el-card class="filter-container" style="height:100px;overflow-y:auto;"> -->
              <div>
                <el-form :inline="true" :model="listQuery" size="mini" label-width="auto">
                 <!-- <el-row :gutter="20" type="flex" justify="space-between"> -->
                   <!-- <el-col > -->
                  <el-form-item label="上传时间：">
                    <el-date-picker
                      class="som-form-item"
                      v-model="listQuery.data_upld_time"
                      value-format="yyyy-MM-dd"
                      type="updt_date"
                      placeholder="选择日期"
                      :picker-options="pickerOptions">
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item  label="批次号：">
                        <el-input class="som-form-item" v-model="listQuery.id" placeholder="日志号"></el-input>
                  </el-form-item>
                  <el-form-item label="处理结果：" >
                    <el-select v-model="listQuery.result" placeholder="全部" clearable>
                      <el-option
                        v-for="item in uploadResult"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item class="som-el-form-item-margin-left">
                    <el-button
                      @click="handleSearchList()"
                      type="primary"
                      size="mini"
                      align="middle"
                      >
                      查询结果
                    </el-button>
                     <el-button @click="refresh">重置</el-button>
                 </el-form-item>
                <!--    </el-col> -->
                    <el-col />
                    <el-col />
                 <!-- </el-row> -->
                </el-form>
              </div>
            <!-- </el-card> -->
        </template>
    <template slot="content">
      <drg-title-line title="数据处理列表" />
      <div class="table-container" style="height:90%">
        <el-table ref="dataHandleLogTable"
                :data="list" size="mini"
                stripe
                  height="100%"
                style="width: 100%"
                v-loading="listLoading"
                :header-cell-style="{'text-align':'center'}"
                border>
          <el-table-column label="批次号"  align="right" width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.id}}</template>
          </el-table-column>
          <el-table-column label="病案总数" align="right" width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.medcasVal}}</template>
          </el-table-column>
          <el-table-column label="完整性校验通过数"  align="right" width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.integrityChkPassVal | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="逻辑性校验通过数"  align="right" width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.logicChkPassVal | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="清洗通过数"  align="right" width="90" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.dataCleanPassVal | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="DRG入组病案数"  align="right" width="90" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.drgInGroupMedcasVal | formatIsEmpty}}</template>
          </el-table-column>
          <!--<el-table-column label="中间表抽取病案数"  align="center"  width="80" :show-overflow-tooltip="true">-->
            <!--<template slot-scope="scope">{{scope.row.busKeyTabSelcMedcasVal | formatIsEmpty}}</template>-->
          <!--</el-table-column>-->
<!--          <el-table-column label="DIP入组病案数"  align="center"  width="80" :show-overflow-tooltip="true">-->
<!--            <template slot-scope="scope">{{scope.row.dipInGroupMedcasVal | formatIsEmpty}}</template>-->
<!--          </el-table-column>-->
<!--          <el-table-column label="成都入组病案数"  align="center"  width="80" :show-overflow-tooltip="true">-->
<!--            <template slot-scope="scope">{{scope.row.cdInGroupMedcasVal | formatIsEmpty}}</template>-->
<!--          </el-table-column>-->
          <el-table-column label="DIP入组病案数"  align="right"  width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.cdInGroupMedcasVal | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="数据处理结果"  align="center" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <div v-if="scope.row.result.substring(0,3)=='exp'" >
                  <el-tag size="medium" style='font-weight:bold;color: #CC0033'>{{scope.row.result | formatResult}}</el-tag>
              </div>
              <div v-if="scope.row.result.substring(0,7)=='success'">
                  <el-tag size="medium" style='font-weight:bold;color: #00CC00'>{{scope.row.result | formatResult}}</el-tag>
              </div>
              <div v-if="scope.row.result.substring(0,3)!='exp'&&scope.row.result.substring(0,7)!='success'">
                  <el-tag size="medium" style='font-weight:bold;color: #409eff'>{{scope.row.result | formatResult}}</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="数据上传时间"  align="right" width="140" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.dataUpldTime | formatTime}}</template>
          </el-table-column>
          <el-table-column label="出院时间"  align="right" width="150" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.dataDscgTimeScp | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="时长/s"  align="right" width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.dataprosDura | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="上传人"  align="left" width="80" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.oprt_psn | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="操作"  align="left" width="100" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <div class="itemPaperName">
                <el-dropdown>
                  <el-button size="mini" icon="el-icon-caret-bottom" style="border:2px solid #DCDFE6;" circle></el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-if="scope.row.result.substring(0,3)=='exp'||scope.row.result.substring(0,7)=='success'" @click.native="restartProcess(scope.row.id)" style="font-size:12px;"><i class="el-icon-refresh-right"></i> 重新执行</el-dropdown-item>
                    <el-dropdown-item v-if="scope.row.result.substring(0,3)=='exp'" @click.native="keepProcess(scope.row.id)" style="font-size:12px;"><i class="el-icon-video-play"></i> 继续执行</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          layout="total, sizes,prev, pager, next,jumper"
          :page-size="listQuery.pageSize"
          :page-sizes="[50,100,200]"
          :current-page.sync="listQuery.pageNum"
          :total="total">
        </el-pagination>
      </div>
    </template>
    </drg-container>
    </div>
    </template>
<script>
import { fetchList, queryCurrentBusSettleList, keepProcessByLogId, restartProcessByLogId } from '@/api/dataHandle/dataHandleLog'
import { format } from '@/utils/datetime'
// import { queryCurrentBusSettleList } from '@/api/dataHandle/dataHandleLog'
// import { restartProcessByLogId } from '@/api/dataHandle/dataHandleLog'
// import { keepProcessByLogId } from '@/api/dataHandle/dataHandleLog'
import { formatDate } from '@/utils/date'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 50,
  id: null,
  data_upld_time: null,
  // upload_date:formatDate(new Date(), 'yyyy-MM-dd'),
  result: null
}
export default {
  name: 'uploadLog',
  inject: ['reload'],
  data () {
    return {
      tableHeight: 0,
      uploadResult: [{
        value: 'success',
        label: '执行成功'
      }, {
        value: 'exp',
        label: '存在异常'
      },
      {
        value: 'run',
        label: '正在执行'
      }
      ],
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick (picker) {
            picker.$emit('pick', new Date())
          }
        }, {
          text: '昨天',
          onClick (picker) {
            const updt_date = new Date()
            updt_date.setTime(updt_date.getTime() - 3600 * 1000 * 24)
            picker.$emit('pick', updt_date)
          }
        }, {
          text: '一周前',
          onClick (picker) {
            const updt_date = new Date()
            updt_date.setTime(updt_date.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', updt_date)
          }
        }]
      },
      listLoading: true,
      list: null,
      total: null,
      listQuery: Object.assign({}, defaultListQuery)
    }
  },
  created () {
    this.getList()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime (time) {
      if (time) {
        return format(time)
      } else {
        return '-'
      }
    },
    formatResult (value) {
      if (value) {
        if (value.substring(0, 3) == 'exp') {
          return value.substring(4, 100)
        } else if (value.substring(0, 7) == 'success') {
          return '执行成功'
        } else if (value.substring(0, 3) != 'exp' && value.substring(0, 7) != 'success') {
          return '正在执行'
        }
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.dataHandleLogTable.$el.offsetTop：表格距离浏览器的高度
      // 50表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.dataHandleLogTable.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.dataHandleLogTable.$el.offsetTop - 35
      }
    })
  },
  methods: {
    getList () {
      this.listLoading = true
      fetchList(this.listQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.listQuery.pageNum = 1
      this.getList()
    },
    handleResetSearch () {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.getList()
    },
    refresh () {
      this.reload()
    },
    // 重新执行该批次
    restartProcess (id) {
      let params0 = new URLSearchParams()
      params0.append('logId', id)
      // 如果当前有效数据为空，没有必要重新执行和继续执行该流程，首先做判断
      queryCurrentBusSettleList(params0).then(response => {
        if (response.data > 0) {
          this.$confirm('批次号：' + id + '，有效病案数：' + response.data + ',确定重新执行该批次数据吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            restartProcessByLogId(params0).then(response => {})
          }).catch(() => {
          })
        } else {
          this.$alert('不必重新执行该批次，当前批次已不存在有效数据！', '提示', {
            confirmButtonText: '确定'
          })
        }
      })
    },
    // 继续执行该批次
    keepProcess (id) {
      let params1 = new URLSearchParams()
      params1.append('logId', id)
      queryCurrentBusSettleList(params1).then(response => {
        if (response.data > 0) {
          this.$confirm('批次号：' + id + '，有效病案数：' + response.data + ',确定继续执行该批次数据吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            keepProcessByLogId(params1).then(response => {})
          }).catch(() => {
          })
        } else {
          this.$alert('不必继续执行该批次，当前批次已不存在有效数据！', '提示', {
            confirmButtonText: '确定'
          })
        }
      })
    }
  }
}
</script>
<style scoped>
  .el-icon-caret-bottom{
    font-weight:900;
  }
</style>
