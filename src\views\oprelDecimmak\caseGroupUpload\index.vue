<template>
  <div class="app-container">
    <drg-container :headerPercent="11" :type="1">
      <template slot="header">
        <drg-title-line title="查询条件"/>
        <el-form :inline="true" :model="queryForm"  ref="queryForm" size="mini" style="height: 55%">
          <el-form-item label="上传时间">
            <el-date-picker
              v-model="queryForm.updt_date"
              :clearable="false"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item class="som-el-form-item-margin-left">
            <el-button type="primary" @click="queryData">查询</el-button>
            <el-button type="primary" @click="dialogVisible = true"><i class="el-icon-upload el-icon--left"></i>文件上传</el-button>
            <el-button type="primary" @click="downloadTemplate"><i class="el-icon-download el-icon--left"></i>模板下载</el-button>
            <el-button @click="refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="上传日志列表"/>
        <el-empty v-show="empty" style="position: absolute;top: 25%;left: 45%" description="暂无数据"></el-empty>
        <el-table :data="tableData"
                  v-loading="loading"
                  border
                  highlight-current-row
                  size="mini"
                  height="95%"
                  :header-cell-style="{'text-align':'center'}"
                  >
          <el-table-column
            prop="id"
            align="right"
            label="日志ID">
          </el-table-column>

          <el-table-column
            prop="fileName"
            align="left"
            label="文件名称">
          </el-table-column>

          <el-table-column
            prop="fileUpldCnt"
            align="right"
            label="文件上传数量">
          </el-table-column>

          <el-table-column
            prop="fileUpldTimeTime"
            align="right"
            label="文件上传时间">
          </el-table-column>

          <el-table-column
            prop="errMsg"
            align="left"
            label="错误信息">
          </el-table-column>

          <el-table-column
            prop="state"
            width="80"
            align="center"
            label="状态">
            <template slot-scope="scope">
              <i class="el-icon-success" v-if="scope.row.state == 1" style="font-size: 1.3rem;color: green"></i>
              <i class="el-icon-error" v-else style="font-size: 1.3rem;color: red"></i>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog
          title="上传文件"
          :visible.sync="dialogVisible"
          width="50%">
          <el-alert
            center
            :title="titleHide()"
            type="warning"
            v-if="selectHide(medicalDetailsIssue)"
            :closable="false">
          </el-alert>
          <el-form :inline="true" :model="queryForm" size="mini" style="height: 10%;text-align: center">
            <el-form-item class="som-form-item">
              <el-radio-group v-model="queryForm.type" @change="selectMedicalDetails()">
                <el-radio label="1">DIP</el-radio>
                <el-radio label="3">DRG</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <el-form :inline="true" :model="queryForm"  ref="queryForm" size="mini" style="height: 55%;text-align: center;margin-top: 15px">
            <el-form-item label="期号">
              <el-date-picker
                class="som-form-item"
                v-model="queryForm.ym"
                value-format="yyyyMM"
                type="month"
                :clearable="false"
                placeholder="选择月"
                @change="selectMedicalDetails">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <el-upload
            style="text-align: center"
            drag
            ref="upload"
            :limit="1"
            action="customize"
            accept=".xlsx,.xls"
            :http-request="upload">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
          </el-upload>
        </el-dialog>
      </template>
    </drg-container>
  </div>
</template>

<script>
import { fileUpload, downloadMedicalTemplate, queryMedicalUploadLog, selectDetails } from '@/api/operationalDecision/upload/upload'
import moment from 'moment'

export default {
  name: 'caseGroupUpload',
  inject: ['reload'],
  data () {
    return {
      queryForm: {
        updt_date: [],
        type: '1',
        ym: new Date()
      },
      dialogVisible: false,
      tableData: [],
      empty: false,
      loading: false,
      medicalDetailsIssue: []
    }
  },
  created () {
    this.queryForm.updt_date[0] = this.$somms.getYearMonthStartTime()
    this.queryForm.updt_date[1] = this.$somms.getYearMonthEndTime()
    this.getCurrentMonthFirst()
    this.selectMedicalDetails()
  },
  mounted () {
    this.queryData()
  },
  methods: {
    queryData () {
      let params = this.queryForm
      if (params.updt_date) {
        params.begnDate = this.queryForm.updt_date[0]
        params.expiDate = this.queryForm.updt_date[1]
      }
      queryMedicalUploadLog(params).then(res => {
        this.tableData = res.data
        if (res.data.length == 0) {
          this.empty = true
        } else {
          this.empty = false
        }
      })
    },
    upload (data) {
      let params = new FormData()
      this.dialogVisible = false
      this.loading = true
      params.append('file', data.file)
      params.append('ym', this.queryForm.ym)
      params.append('type', this.queryForm.type)
      fileUpload(params).then(res => {
        if (res.code == 200) {
          this.$refs.upload.clearFiles()
          this.$message.success('上传成功')
          this.loading = false
          this.queryData()
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
        this.loading = false
      })
    },
    selectMedicalDetails () {
      let params = this.queryForm
      selectDetails(params).then(res => {
        this.medicalDetailsIssue = res.data
      })
    },
    titleHide () {
      let a = this.queryForm.ym
      return '该期号' + '(' + a + ')' + '已上传，再次上传会覆盖之前的内容'
    },
    selectHide (medicalDetailsIssue) {
      if (medicalDetailsIssue.length !== 0) {
        return true
      } else if (medicalDetailsIssue.length == 0) {
        return false
      }
    },
    downloadTemplate () {
      downloadMedicalTemplate().then(res => {
        this.downloadExcel(res, '医院月度分组详情模板')
      })
    },
    downloadExcel (blobPart, filename) {
      const blob = new Blob([blobPart], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      // const blob = new Blob([blobPart], { type: 'application/vnd.ms-excel' })
      // 创建一个超链接，将文件流赋进去，然后实现这个超链接的单击事件
      const elink = document.createElement('a')
      elink.download = decodeURIComponent(filename)
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    },
    getCurrentMonthFirst () {
      let updt_date = new Date()
      updt_date.setDate(1)
      let month = parseInt(updt_date.getMonth() + 1)
      let day = updt_date.getDate()
      if (month < 10) month = '0' + month
      if (day < 10) day = '0' + day
      let first = updt_date.getFullYear() + '-' + month + '-' + day
      this.queryForm.updt_date[0] = first
      let lastMonth = updt_date.getFullYear() + String(month - 1)
      this.queryForm.ym = lastMonth
    },
    refresh () {
      this.reload()
    }
  }
}
</script>

<style scoped>
</style>
