<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="form"
             :show-date-range="{ show: true, clearable: true }"
             :show-se-date-range="{ show: true, clearable: true }"
             :container="true"
             :showPagination="true"
             :totalNum="total"
             :exportExcel="{ 'tableId': tableId, exportName: '医生反馈分析'}"
             :exportExcelFun="queryFeedAnalysis"
             :exportExcelHasChild="false"
             headerTitle="查询条件"
             contentTitle="数据列表"
             @query="queryData"
    >
      <!-- 扩展搜索表单 -->
      <template slot="extendFormItems">
        <el-form-item label="医生姓名">
          <el-select
            v-model="queryForm.drName" filterable placeholder="请选择" clearable>
            <el-option v-for="item in doctorValueList" :key="item.value"
                       :label="item.label" :value="item.value">
              <!--<span style="float: left">{{ item.label }}</span>-->
            </el-option>
          </el-select>
        </el-form-item>
      </template>

      <!-- 表格 -->
      <template slot="containerContent">
        <el-table size="mini" height="100%" ref="dataTable"
                  :header-cell-style="{'text-align':'center'}" stripe
                  :data="list" v-loading="loadinglist" border :id="tableId">
          <!-- 序号列（固定） -->
          <el-table-column
            fixed label="序号" type="index" align="right">
          </el-table-column>
          <!-- 医生名称列 -->
          <el-table-column
            label="医生名称" prop="drName" width="130" align="center">
          </el-table-column>
          <!-- 出院科室列 -->
          <el-table-column
            label="出院科室" prop="deptName" width="200" align="center">
          </el-table-column>
          <!-- 病案数列 -->
          <el-table-column
            label="病案数" prop="countMedical" width="130" align="center">
            <template slot-scope="scope">
              <div :class="scope.row.countMedical === 0 ? '' : 'skip'" @click="scope.row.countMedical === 0 ? '' : jumpPersonData(scope.row)">
                {{ scope.row.countMedical }}
              </div>
            </template>
          </el-table-column>
          <!-- 结算点数列 -->
          <el-table-column
            label="结算点数" prop="selPointAll" width="200" align="center" sortable>
          </el-table-column>
          <!-- 总费用列 -->
          <el-table-column
            label="总费用" prop="totalCostAll" width="200" align="center" sortable>
          </el-table-column>
          <!-- 统筹费用列 -->
          <el-table-column
            label="统筹费用" prop="overallCostAll" width="200" align="center" sortable>
          </el-table-column>
          <!-- 病组总费用列 -->
          <el-table-column
            label="病组总费用" prop="groupTotalCostAll" width="200" align="center" sortable>
          </el-table-column>
          <!-- 病组统筹费用列 -->
          <el-table-column
            label="病组统筹费用" prop="groupOverallCostAll" width="200" align="center" sortable>
          </el-table-column>
          <!-- 盈亏列 -->
          <el-table-column
            label="盈亏" prop="profitAll" width="135" align="center" sortable>
          </el-table-column>
        </el-table>
      </template>
    </drg-form>
  </div>
</template>

<script>
import { queryDropdown, queryFeedAnalysis } from '@/api/newBusiness/feebackAnalyse'
import { queryFeedbackDetail2 } from '@/api/newBusiness/newBusinessUpload'
export default {
  name: 'feedbackDoctorAnalyis',
  data: () => ({
    queryForm: {
      drName: ''
    },
    doctorValueList: [],
    list: [],
    loadinglist: true,
    tableId: 'dataTable',
    total: null
  }),
  methods: {
    queryFeedAnalysis,
    // 查询数据
    queryData () {
      queryDropdown(this.getParams()).then(res => {
        this.doctorValueList = res.data
      })
      queryFeedAnalysis(this.getParams()).then(res => {
        this.list = res.data.list
        this.total = res.data.total
        this.loadinglist = false
      })
    },
    // 获取查询参数
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    // 跳转患者数据
    jumpPersonData (rowData) {
      this.$router.push({
        path: '/feedbackAnaly/feedbackPersonAnalyis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime,
          drName: rowData.drName,
          deptName: rowData.deptName
        }
      })
    }
  }
}
</script>
