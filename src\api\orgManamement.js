import request from '@/utils/request'

// 获取组织结构树
export function queryTreeNode (params) {
  return request({
    url: '/org/somBasDept/getListTree',
    method: 'post',
    params: params
  })
}

// 新增组织
export function addOrg (data) {
  return request({
    url: '/org/somBasDept/addOrg',
    method: 'post',
    data: data
  })
}

// 删除组织
export function removeOrg (data) {
  return request({
    url: '/org/somBasDept/removeOrg',
    method: 'post',
    data: data
  })
}

// 编辑组织
export function editOrg (data) {
  return request({
    url: '/org/somBasDept/editOrg',
    method: 'post',
    data: data
  })
}

// 查询用户列表
export function queryUserByOrgId (data) {
  return request({
    url: '/user/list',
    method: 'post',
    data: data
  })
}

// 新增用户
export function registerRequest (data) {
  return request({
    url: '/user/register',
    method: 'post',
    data: data
  })
}

// 编辑用户
export function editUserRequest (userId, data) {
  return request({
    url: '/user/update/' + userId,
    method: 'post',
    data: data
    /* transformRequest:[function () {
          return JSON.stringify(params)
        }] */
  })
}

// 用户重置密码
export function resetPassword (userId, data) {
  return request({
    url: '/user/resetPassword/' + userId,
    method: 'post',
    data: data
  })
}

// 恢复系统
export function recoverSystem () {
  return request({
    url: '/user/recoverSystem',
    method: 'post'
  })
}

// 抽查科室
export function extractDept (params) {
  return request({
    url: '/org/extractDept',
    method: 'post',
    data: params
  })
}

export function modifyInformation (params) {
  return request({
    url: '/user/modifyInformation',
    method: 'post',
    params: params
  })
}

export function extractDeptAndDoctoerByView () {
  return request({
    url: '/hisView/extractDeptAndDoctoerByView',
    method: 'post'
  })
}
