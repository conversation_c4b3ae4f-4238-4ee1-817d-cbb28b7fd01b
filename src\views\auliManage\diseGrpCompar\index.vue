<template>
  <div class="app-container">
    <drg-container :headerPercent="14" :isButtonEnd="true" >
      <template slot="header">
        <drg-title-line title="查询条件" />
        <el-form :model="queryForm" size="mini" label-width="80px">
          <el-row type="flex" justify="space-between">
            <el-col>
              <el-form-item label="出院时间">
                <el-date-picker
                  v-model="queryForm.cysj"
                  type="daterange"
                  size="mini"
                  class="som-form-item"
                  start-placeholder="开始日期"
                  range-separator="-"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  :disabled="compareDisabled"
                  @change="dateChangeCysj">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col v-if="this.$somms.hasDeptRole()">
              <el-form-item label="出院科别" class="som-department-height som-form-item">
                <drg-department v-model="queryForm.deptCode" placeholder="请选择科室" :disabled="comDisabled" />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="组">
                <el-select  v-model="group"
                            placeholder="请选择组"
                            class="som-form-item"
                            :disabled="compareDisabled"
                            @change="typeConflict()">
                  <el-option
                    v-for="item in groupOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-if="showHideDrg(group)">
              <el-form-item label="DRGs组" >
                <el-autocomplete
                  class="som-form-item"
                  v-model="queryForm.drgGroup"
                  placeholder="请输入DRGs组编码或名称"
                  :fetch-suggestions="querySearchAsync"
                  @select="fnDrgGroupSelect"
                  :popper-append-to-body="true"
                  :clearable="true"
                  :trigger-on-focus="false"
                  ref="elautocomplete"
                  :disabled="compareDisabled">
                  <template slot-scope="{ item }">
                    <div class="code">{{ item.drgsCode }}</div>
                    <span class="name">{{ item.drgsName }}</span>
                  </template>
                </el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col v-if="showHideDip(group)">
              <el-form-item label="DIP组" >
                <el-autocomplete
                  class="som-form-item"
                  v-model="queryForm.dipGroup"
                  :fetch-suggestions="querySearchAsync"
                  placeholder="请输入DIP组编码或名称"
                  @select="fnDipGroupSelect"
                  :popper-append-to-body="true"
                  :clearable="true"
                  :trigger-on-focus="false"
                  ref="elautocomplete"
                  :disabled="compareDisabled">
                  <template slot-scope="{ item }">
                    <div class="code">{{ item.dipCodg }}</div>
                    <span class="name">{{ item.dipName }}</span>
                  </template>
                </el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item class="som-form-item">
                <el-radio-group v-model="queryForm.radio" :disabled="compareDisabled">
                  <el-radio label="1">左页面</el-radio>
                  <el-radio label="2">右页面</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="som-align-center">
            <el-radio-group  v-model="queryForm.queryType" size="mini" @change="fnClickQuery()"  class="som-el-form-item-margin-left">
              <el-radio-button :label="1" :disabled="compareDisabled">按科室查询</el-radio-button>
              <el-radio-button :label="3" :disabled="compareDisabled">按病组查询</el-radio-button>
            </el-radio-group>
            <el-button
              @click="fnClickQuery()"
              type="primary"
              size="mini"
              :disabled="compareDisabled"
              class="som-el-form-item-margin-left">
              查询
            </el-button>
            <el-button @click="fnCompare"
                       type="success"
                       :disabled="allChecked">
              对比
            </el-button>
            <el-button @click="refresh">重置</el-button>
          </div>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="对比分析" />
        <div :style="{height: leftPageShow && rightPageShow ? '' : '93%',
                      width: '100%',
                      display: 'flex',
                      flexDirection: 'row' }" id="content">
          <!-- 左: 选择数据后显示 -->
          <div v-if="leftPageShow"  class="content-item">
            <disease-group-analysis-description :data="leftPageData"
                                                :otherPageData="rightCostData"
                                                :otherGroupData="rightInGroupData"
                                                :prefix="group"
                                                :preType="queryForm.queryType"
                                                :costPayData="leftCostPayData"
                                                :groupData="leftInGroupData"
                                                :costData="leftCostData"
                                             @backPage="handlerBackPage(true)"/>
          </div >

          <!-- 左: 选择数据前显示 -->
          <div v-if="!leftPageShow" class="content-table content-item">
            <disease-group-analysis-table :tableData="leftTableData"
                                       :tableLoading="leftTableLoading"
                                       :queryForm="queryForm"
                                       :total="leftTotal"
                                       :rightZero="true"
                                       :prefix="group"
                                       @changeCheck="(Code) => this.leftPageCode =Code "
                                       @changeDept="(Dept) => this.leftPageDept =Dept "
                                       @checkState="fnLeftCheckState"/>
            <div class="pagination" style="margin-top: 0.2rem">
              <el-pagination
                background
                align="right"
                @size-change="leftHandleSizeChange"
                @current-change="leftHandleCurrentChange"
                layout="total, sizes,prev, pager, next,jumper"
                :page-size="queryForm.leftPageSize"
                :page-sizes="[200,1000,5000,10000]"
                :current-page.sync="queryForm.leftPageNum"
                :total="leftTotal">
              </el-pagination>
            </div>
          </div>

          <!-- 分隔线 -->
          <div style="width: 4%" :style="splitStyle">
            <div style="height: 100%;background-color: gray;width: 0.1rem;position: absolute;left: 50%">
            </div>
          </div>

          <!-- 右: 选择数据后显示 -->
          <div v-if="rightPageShow" class="content-item">
            <disease-group-analysis-description :data="rightPageData"
                                                :groupData="rightInGroupData"
                                                :otherPageData="leftCostData"
                                                :otherGroupData="leftInGroupData"
                                                :costPayData="rightCostPayData"
                                                :costData="rightCostData"
                                                @backPage="handlerBackPage(false)"
                                                :prefix="group"
                                                :preType="queryForm.queryType"
                                                :rightAdjust="true"/>
          </div>

          <!-- 右: 选择数据前显示 -->
          <div v-if="!rightPageShow" class="content-table content-item">
            <disease-group-analysis-table :tableData="rightTableData"
                                       :tableLoading="rightTableLoading"
                                       :queryForm="queryForm"
                                       :total="rightTotal"
                                       :prefix="group"
                                       @changeCheck="(Code) => this.rightPageCode =Code"
                                       @changeDept="(Dept) => this.rightPageDept =Dept"
                                       @checkState="fnRightCheckState"/>
            <div class="pagination" style="margin-top: 0.2rem">
              <el-pagination
                background
                align="right"
                @size-change="rightHandleSizeChange"
                @current-change="rightHandleCurrentChange"
                layout="total, sizes,prev, pager, next,jumper"
                :page-size="queryForm.rightPageSize"
                :page-sizes="[200,1000,5000,10000]"
                :current-page.sync="queryForm.rightPageNum"
                :total="rightTotal">
              </el-pagination>
            </div>
          </div>
        </div>
      </template>
    </drg-container>
  </div>
</template>
<script>

import { queryDataIsuue, queryLikeDrgsByPram, queryLikeDipGroupByPram } from '@/api/common/drgCommon'
import { queryEnableGroup } from '@/api/common/sysCommon'
import { getList, getInfo, getInGroup, getCostPay, getCost } from '@/api/medicalQuality/diseaseGroupAnalysis'
import diseaseGroupAnalysisTable from './components/diseaseGroupAnalysisTable'
import diseaseGroupAnalysisDescription from './components/diseaseGroupAnalysisDescription'
import moment from 'moment'
import { MessageBox } from 'element-ui'

export default {
  name: 'diseGrpCompar',
  inject: ['reload'],
  components: { diseaseGroupAnalysisTable, diseaseGroupAnalysisDescription },
  data () {
    return {
      cy_start_date: null,
      cy_end_date: null,
      queryForm: {
        pageNum: 1,
        pageSize: 200,
        leftPageNum: 1,
        leftPageSize: 200,
        rightPageNum: 1,
        rightPageSize: 200,
        dipGroup: '',
        drgGroup: '',
        cysj: null,
        deptCode: '',
        radio: '1',
        queryType: '1',
        prefix: 0,
        preType: ''
      },
      splitStyle: {
        height: '',
        position: 'relative'
      },
      group: '1',
      leftPageShow: false,
      rightPageShow: false,
      leftTableLoading: false,
      leftTableData: [],
      leftTotal: 0,
      rightTableLoading: false,
      rightTableData: [],
      rightTotal: 0,
      leftChecked: false,
      rightChecked: false,
      allChecked: true,
      compareDisabled: false,
      comDisabled: false,
      groupOptions: [],
      leftPageCode: '',
      rightPageCode: '',
      leftPageDept: '',
      rightPageDept: '',
      leftPageData: [],
      rightPageData: [],
      leftInGroupData: [],
      leftCostData: [],
      rightCostData: [],
      rightInGroupData: [],
      leftCostPayData: [],
      rightCostPayData: []
    }
  },
  created () {
    // 获取数据查询时间
    this.getDataIsuue()
    this.getEnabledGroup()
    if (this.$route.query.type) {
      this.skip()
    }
  },
  methods: {
    leftHandleSizeChange (val) {
      this.queryForm.leftPageNum = 1
      this.queryForm.leftPageSize = val
      this.getTableData(1)
    },
    leftHandleCurrentChange (val) {
      this.queryForm.leftPageNum = val
      this.getTableData(1)
    },
    rightHandleSizeChange (val) {
      this.queryForm.rightPageNum = 1
      this.queryForm.rightPageSize = val
      this.getTableData(2)
    },
    rightHandleCurrentChange (val) {
      this.queryForm.rightPageNum = val
      this.getTableData(2)
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.queryForm.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getTableData(3)
      })
    },
    getPropByType (type) {
      if (type == '1') {
        return 'dip'
      } else if (type == '3') {
        return 'drg'
      }
    },
    showHideDip (type) {
      if (type == '1') {
        return true
      } else if (type == '3') {
        return false
      }
    },
    showHideDept (queryType) {
      if (queryType == '1') {
        return true
      } else if (queryType == '3') {
        return false
      }
    },
    showHideDrg (type) {
      if (type == '3') {
        return true
      } else if (type == '1') {
        return false
      }
    },
    getEnabledGroup () {
      queryEnableGroup().then(res => {
        if (res.code == 200) {
          this.groupOptions = res.data
        }
      })
    },
    // DRG组选中
    fnDrgGroupSelect (item) {
      this.queryForm.drgGroup = item.drgsCode
    },
    // DIP组选中
    fnDipGroupSelect (item) {
      this.queryForm.dipGroup = item.dipCodg
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    getTableData (index) {
      this.resetSearch()
      let params = this.getParams()

      if (index == 3) {
        this.leftTableLoading = true
        this.rightTableLoading = true
      } else if (index == 1) {
        this.leftTableLoading = true
        params.pageSize = this.queryForm.leftPageSize
        params.pageNum = this.queryForm.leftPageNum
      } else if (index == 2) {
        this.rightTableLoading = true
        params.pageSize = this.queryForm.rightPageSize
        params.pageNum = this.queryForm.rightPageNum
      }
      if (params.queryType == 3) {
        this.leftTableLoading = true
        this.rightTableLoading = true
      }
      getList(params).then(res => {
        let data = res.data.list
        if (index == 3) {
          this.leftTableData = data
          this.leftTotal = res.data.total
          this.rightTableData = data
          this.rightTotal = res.data.total
          this.leftTableLoading = false
          this.rightTableLoading = false
        } else if (index == 1) {
          this.leftTableData = data
          this.leftTotal = res.data.total
          this.leftTableLoading = false
        } else if (index == 2) {
          this.rightTableData = data
          this.rightTotal = res.data.total
          this.rightTableLoading = false
        }
      })
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
    },
    refresh () {
      this.resetSearch()
      this.reload()
    },
    getParams () {
      let params = this.queryForm
      if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
        Object.assign(this.queryForm, { cysj: [this.$route.query.cy_start_date, this.$route.query.cy_end_date] })
        this.cy_start_date = this.$route.query.cy_start_date
        this.cy_end_date = this.$route.query.cy_end_date
      }
      if (params.cysj) {
        params.begnDate = moment(params.cysj[0]).format('YYYY-MM-DD')
        params.expiDate = moment(params.cysj[1]).format('YYYY-MM-DD')
      }
      params.dataAuth = true
      if (this.$route.query.type) {
        this.group = this.$route.query.type
      }
      params.type = this.group
      return params
    },
    typeConflict () {
      if (!this.leftPageShow && this.rightPageShow) {
        this.getTableData(this.queryForm.radio)
      }
      if (!this.rightPageShow && this.leftPageShow) {
        this.getTableData(this.queryForm.radio)
      }
    },
    fnClickQuery () {
      this.getTableData(this.queryForm.queryType)
      this.showHideDept(this.queryForm.queryType)

      if (this.queryForm.queryType == '3') {
        this.comDisabled = true
      }
    },
    fnLeftCheckState (val) {
      this.leftChecked = val
      this.judgeAllChecked()
    },
    fnRightCheckState (val) {
      this.rightChecked = val
      this.judgeAllChecked()
    },
    judgeAllChecked () {
      if (this.leftChecked && this.rightChecked) {
        this.allChecked = false
      }
    },
    fnCompare () {
      this.resetSearch()
      this.leftPageShow = true
      this.rightPageShow = true
      this.allChecked = true
      this.compareDisabled = true
      this.comDisabled = this.compareDisabled
      this.queryDescriptionsData()
      this.queryDescriptionsGroupData()
      this.queryDescriptionsCostPayData()
      this.queryDescriptionsCostData()
      this.$nextTick(() => {
        this.queryForm.deptCode = ''
      })
    },
    handlerBackPage (flag) {
      if (flag) {
        this.leftPageShow = false
        this.leftChecked = false
        this.queryForm.radio = '1'
      } else {
        this.rightPageShow = false
        this.rightChecked = false
        this.queryForm.radio = '2'
      }
      this.allChecked = false
      this.compareDisabled = false
      if (this.queryForm.queryType == '1') {
        this.comDisabled = false
      }
      // this.group = ''
      this.getDataIsuue()
    },
    queryDescriptionsData () {
      // 查询数据传入 description 组件
      let params = this.getParams()
      if (this.$route.query.id) {
        params.ids = [this.$route.query.id]
        this.leftPageCode = this.$route.query.id
      } else {
        params.ids = [this.leftPageCode, this.rightPageCode]
      }
      if (this.$route.query.deptCode) {
        params.depts = [this.$route.query.deptCode]
        this.leftPageDept = this.$route.query.deptCode
      } else {
        params.depts = [this.leftPageDept, this.rightPageDept]
      }
      if (this.$route.query.queryType) {
        this.queryForm.queryType = this.$route.query.queryType
      }
      getInfo(params).then(res => {
        if (res.code == 200) {
          res.data.map(data => {
            if (this.queryForm.queryType == '1') {
              if (this.group == '1') {
                if (data.dipCodg == this.leftPageCode && data.deptCode == this.leftPageDept) {
                  this.leftPageData = data
                } else if (data.dipCodg == this.rightPageCode && data.deptCode == this.rightPageDept) {
                  this.rightPageData = data
                }
              }
              if (this.group == '3') {
                if (data.drgCodg == this.leftPageCode && data.deptCode == this.leftPageDept) {
                  this.leftPageData = data
                } else if (data.drgCodg == this.rightPageCode && data.deptCode == this.rightPageDept) {
                  this.rightPageData = data
                }
              }
            } else if (this.queryForm.queryType == '3') {
              if (this.group == '1') {
                if (data.dipCodg == this.leftPageCode) {
                  this.leftPageData = data
                } else if (data.dipCodg == this.rightPageCode) {
                  this.rightPageData = data
                }
              }
              if (this.group == '3') {
                if (data.drgCodg == this.leftPageCode) {
                  this.leftPageData = data
                } else if (data.drgCodg == this.rightPageCode) {
                  this.rightPageData = data
                }
              }
            }
          })
        }
      })
    },
    queryDescriptionsGroupData () {
      // 查询数据传入 description 组件
      let params = this.getParams()
      if (this.$route.query.id) {
        params.ids = [this.$route.query.id]
        this.leftPageCode = this.$route.query.id
      } else {
        params.ids = [this.leftPageCode, this.rightPageCode]
      }
      if (this.$route.query.deptCode) {
        params.depts = [this.$route.query.deptCode]
        this.leftPageDept = this.$route.query.deptCode
      } else {
        params.depts = [this.leftPageDept, this.rightPageDept]
      }
      getInGroup(params).then(res => {
        if (res.code == 200) {
          res.data.map(data => {
            if (this.queryForm.queryType == '1') {
              if (this.group == '1') {
                if (data.dipCodg == this.leftPageCode && data.deptCode == this.leftPageDept) {
                  this.leftInGroupData = data
                } else if (data.dipCodg == this.rightPageCode && data.deptCode == this.rightPageDept) {
                  this.rightInGroupData = data
                }
              }
              if (this.group == '3') {
                if (data.drgCodg == this.leftPageCode && data.deptCode == this.leftPageDept) {
                  this.leftInGroupData = data
                } else if (data.drgCodg == this.rightPageCode && data.deptCode == this.rightPageDept) {
                  this.rightInGroupData = data
                }
              }
            } else if (this.queryForm.queryType == '3') {
              if (this.group == '1') {
                if (data.dipCodg == this.leftPageCode) {
                  this.leftInGroupData = data
                } else if (data.dipCodg == this.rightPageCode) {
                  this.rightInGroupData = data
                }
              }
              if (this.group == '3') {
                if (data.drgCodg == this.leftPageCode) {
                  this.leftInGroupData = data
                } else if (data.drgCodg == this.rightPageCode) {
                  this.rightInGroupData = data
                }
              }
            }
          })
        }
      })
    },
    queryDescriptionsCostPayData () {
      // 查询数据传入 description 组件
      let params = this.getParams()
      if (this.$route.query.id) {
        params.ids = [this.$route.query.id]
        this.leftPageCode = this.$route.query.id
      } else {
        params.ids = [this.leftPageCode, this.rightPageCode]
      }
      if (this.$route.query.deptCode) {
        params.depts = [this.$route.query.deptCode]
        this.leftPageDept = this.$route.query.deptCode
      } else {
        params.depts = [this.leftPageDept, this.rightPageDept]
      }
      getCostPay(params).then(res => {
        if (res.code == 200) {
          res.data.map(data => {
            if (this.queryForm.queryType == '1') {
              if (this.group == '1') {
                if (data.dipCodg == this.leftPageCode && data.deptCode == this.leftPageDept) {
                  this.leftCostPayData = data
                } else if (data.dipCodg == this.rightPageCode && data.deptCode == this.rightPageDept) {
                  this.rightCostPayData = data
                }
              }
              if (this.group == '3') {
                if (data.drgCodg == this.leftPageCode && data.deptCode == this.leftPageDept) {
                  this.leftCostPayData = data
                } else if (data.drgCodg == this.rightPageCode && data.deptCode == this.rightPageDept) {
                  this.rightCostPayData = data
                }
              }
            } else if (this.queryForm.queryType == '3') {
              if (this.group == '1') {
                if (data.dipCodg == this.leftPageCode) {
                  this.leftCostPayData = data
                } else if (data.dipCodg == this.rightPageCode) {
                  this.rightCostPayData = data
                }
              }
              if (this.group == '3') {
                if (data.drgCodg == this.leftPageCode) {
                  this.leftCostPayData = data
                } else if (data.drgCodg == this.rightPageCode) {
                  this.rightCostPayData = data
                }
              }
            }
          })
        }
      })
    },
    queryDescriptionsCostData () {
      // 查询数据传入 description 组件
      let params = this.getParams()
      if (this.$route.query.id) {
        params.ids = [this.$route.query.id]
        this.leftPageCode = this.$route.query.id
      } else {
        params.ids = [this.leftPageCode, this.rightPageCode]
      }
      if (this.$route.query.deptCode) {
        params.depts = [this.$route.query.deptCode]
        this.leftPageDept = this.$route.query.deptCode
      } else {
        params.depts = [this.leftPageDept, this.rightPageDept]
      }
      getCost(params).then(res => {
        if (res.code == 200) {
          res.data.map(data => {
            if (this.queryForm.queryType == '1') {
              if (this.group == '1') {
                if (data.dipCodg == this.leftPageCode && data.deptCode == this.leftPageDept) {
                  this.leftCostData = data
                } else if (data.dipCodg == this.rightPageCode && data.deptCode == this.rightPageDept) {
                  this.rightCostData = data
                }
              }
              if (this.group == '3') {
                if (data.drgCodg == this.leftPageCode && data.deptCode == this.leftPageDept) {
                  this.leftCostData = data
                } else if (data.drgCodg == this.rightPageCode && data.deptCode == this.rightPageDept) {
                  this.rightCostData = data
                }
              }
            } else if (this.queryForm.queryType == '3') {
              if (this.group == '1') {
                if (data.dipCodg == this.leftPageCode) {
                  this.leftCostData = data
                } else if (data.dipCodg == this.rightPageCode) {
                  this.rightCostData = data
                }
              }
              if (this.group == '3') {
                if (data.drgCodg == this.leftPageCode) {
                  this.leftCostData = data
                } else if (data.drgCodg == this.rightPageCode) {
                  this.rightCostData = data
                }
              }
            }
          })
        }
      })
    },
    skip () {
      this.leftPageShow = true
      this.allChecked = false
      this.compareDisabled = false
      this.queryDescriptionsData()
      this.queryDescriptionsGroupData()
      this.queryDescriptionsCostPayData()
      this.queryDescriptionsCostData()
      this.$nextTick(() => {
        this.queryForm.deptCode = ''
      })
    },
    // 清空下转内容
    resetSearch () {
      if (this.$route.query) {
        this.$router.push({ query: {} }).catch(() => {})
      }
    }
  },
  watch: {
    group: {
      handler: function () {
        this.leftTableData = []
        this.rightTableData = []
        this.getTableData(3)
      }
    }
  }
}

</script>
<style scoped>
.content-table{
  height: 98%;
}
.content-item {
  width: 48%;
}
</style>
