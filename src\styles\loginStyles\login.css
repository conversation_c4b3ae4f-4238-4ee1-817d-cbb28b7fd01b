.form-left form section label input {
  width: 100%;
  display: block;
  border: none;
  background: transparent;
  color: #000;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-sizing: border-box;
  font-weight: 600;
  caret-color: #000;
}

.form-left form section label .big-input {
  height: 50px;
}

/deep/ .form-left form section label .big-input input {
  height: 40px;
  padding-left: 40px;
}

.form-left form section label input:-internal-autofill-previewed,
.form-left form section label input:-internal-autofill-selected {
  -webkit-text-fill-color: #000 !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

.title-right {
  height: 100%;
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-image: url(~@/assets/images/login/login-back.jpg);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.title-right p {
  font-size: 37px;
  line-height: 46px;
  font-weight: 600;
  padding: 0 100px;
  text-align: center;
  color: #F0F0EE;
}

.custom_prefix_init > .custom_login_icon {
  font-family: 'element-icons' !important;
  width: 30px;
  height: 35px;
  font-size: 30px;
  color: #5191f5;

}

.custom_prefix_init > .custom_login_icon.pass_word_init{
  height: 35px !important;
}

.login_welcome_tips {
  color: #5191f5;
}

.custom_input_init {
  border-bottom: solid 1px #e1e1e1;
}

.custom_select_init .el-input__inner,.custom_input_init .el-input__inner {
  padding-left: 50px;
  height: 38px;
  line-height: 40px;
}
.custom_select_init .custom_prefix_init{
  display: flex;
  height: 30px;
  align-items: center;
  justify-content: center;
}
.custom_select_init .custom_line_init{
  width: 1px;
  height: 70%;
  background-color: #5191f5;
  margin-left: 6px;
}

.custom_input_init .custom_prefix_init{
  display: flex;
  height: 70%;
  align-items: center;
  justify-content: center;
}

.custom_input_init .custom_line_init{
  width: 1px;
  height: 80%;
  background-color: #5191f5;
  margin-left: 6px;
}

.custom_input_init  .el-input__inner:hover{background-color: #ecf5ff}
.custom_input_init  .el-input__inner:focus{background-color: #ecf5ff}

.custom_input_init .el-select>.el-input {height: 50px}
