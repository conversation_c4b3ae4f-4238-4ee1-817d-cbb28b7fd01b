<template>
  <div class="app-container">
    <drg-container :headerPercent="17">
      <template slot="header">
        <drg-title-line title="查询条件"/>
        <el-form :model="listQuery" size="mini" label-width="96px">
          <el-row type="flex" :gutter="10" justify="space-between" style="width: 100%;">
            <el-col :span="6">
              <el-form-item :label="timeName">
                <el-date-picker disabled
                                style="width: 100%"
                                v-model="listQuery.expiDate"
                                type="daterange"
                                unlink-panels
                                range-separator="-"
                                value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="出院科室名称">
                <el-input class="som-form-item" v-model="listQuery.deptName" disabled></el-input>
              </el-form-item>
            </el-col>


            <el-col :span="6">
              <el-form-item label="医生姓名">
                <el-input v-model="listQuery.doctorName" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="6" style="display: flex; justify-content: center">
              <el-popconfirm
                confirm-button-text='确定'
                cancel-button-text='导出全部'
                icon="el-icon-info"
                icon-color="red"
                title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel">
                <el-button slot="reference" type="success">导出Excel</el-button>
              </el-popconfirm>
            </el-col>
          </el-row>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="病案详情"/>
        <div class="table-container" style="height: 90%;width: 100%">
          <el-table ref="medicalDetail"
                    id="medicalTable"
                    size="mini"
                    height="100%"
                    :data="list"
                    style="width: 100%;"
                    v-loading="listLoading"
                    :row-style="sumStyle"
                    border>
            <!--:span-method="objectSpanMethod"-->

            <el-table-column fixed label="序号" width="50">
              <template slot-scope="scope">
                {{ (listQuery.pageNum - 1) * listQuery.pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="病案号" prop="medcasno" align="center"/>
            <el-table-column label="患者姓名" prop="name" align="center"/>
            <el-table-column label="年龄" prop="age" align="center"/>

            <el-table-column label="入院时间" prop="b12" width="150px" align="left"/>
            <el-table-column label="出院时间" prop="b15" width="150px" align="left"/>
            <el-table-column label="住院天数" prop="iptDay" width="80px" align="left"/>
            <el-table-column label="诊断信息" prop="diagCodeAndName" width="200px" show-overflow-tooltip align="left"/>
            <el-table-column label="手术信息" prop="oprnCodeAndName" width="200px" show-overflow-tooltip align="left"/>
            <el-table-column label="场景类型" prop="ruleScenType" align="left"/>
            <el-table-column label="违规规则数" prop="totalRecords" width="100px" align="left"/>
            <el-table-column label="违规元素条数" prop="tupleNum" width="100px" align="left"/>
            <el-table-column label="违规总费用" prop="totalAmount" width="100px" align="left"/>
            <el-table-column label="出院科室" prop="deptName" align="center" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.deptName | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="主诊医生" prop="doctorName" align="center" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.doctorName | formatIsEmpty }}</template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            layout="total, sizes, prev, pager, next, jumper"
            :page-size="listQuery.pageSize"
            :page-sizes="[200, 1000, 5000, 10000]"
            :current-page.sync="listQuery.pageNum"
            :total="total">
          </el-pagination>
        </div>
      </template>
    </drg-container>
  </div>
</template>

<script>
import {querySelectTreeAndSelectList} from '@/api/common/drgCommon'
import {queryVioalDetailList as queryPageData} from '@/api/examCorrection/ruleAndTuples'
import {formaterDict} from '@/utils/dict'
import {elExportExcel} from '@/utils/exportExcel'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  ruleScenType: '1',
  cysj: null,
  priOutHosDeptCode: null,
  priOutHosDeptName: null,
  doctorCode: null,
  doctorName: null,
  queryType: null,
  type: null,
  errorType: null,
  ruleDetlCodg: null,
  begnDate: '',
  expiDate: '',
  seStartTime: '',
  seEndTime: ''
}
export default {
  name: 'docerVialDetial',
  data() {
    return {
      dictVoList: {}, // 码表
      listLoading: true,
      list: [],
      total: 0,
      listQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      prefix: 0,
      timeName: '出院时间',
      isInitialized: false // 标记是否已经初始化过路由参数
    }
  },
  created() {
    
  },
  filters: {
    formatIsEmpty(value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      if (Object.keys(this.$route.query).length > 0) {
        // 先处理日期范围
        if (this.$route.query.expiDate && Array.isArray(this.$route.query.expiDate)) {
          this.listQuery.expiDate = this.$route.query.expiDate;
        } else if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.listQuery.expiDate = [this.$route.query.begnDate, this.$route.query.expiDate];
        }

        if (this.$route.query.inHosFlag) {
          this.listQuery.inHosFlag = this.$route.query.inHosFlag
          if (this.$route.query.inHosFlag == '1') {
            this.timeName = '出院时间'
            Object.assign(this.listQuery, {cysj: [this.$route.query.cy_start_date, this.$route.query.cy_end_date]})
          } else if (this.$route.query.inHosFlag == '2') {
            this.timeName = '入院时间'
            Object.assign(this.listQuery, {cysj: [this.$route.query.inStartTime, this.$route.query.inEndTime]})
          } else if (this.$route.query.inHosFlag == '3') {
            this.timeName = '结算时间'
            Object.assign(this.listQuery, {cysj: [this.$route.query.seStartTime, this.$route.query.seEndTime]})
          }
        }

        // 其他参数的处理保持不变
        if (this.$route.query.doctorCode) {
          Object.assign(this.listQuery, {doctorCode: this.$route.query.doctorCode})
        }
        if (this.$route.query.deptCode) {
          Object.assign(this.listQuery, {deptCode: this.$route.query.deptCode})
        }
        if (this.$route.query.ruleDetlCodg) {
          Object.assign(this.listQuery, {ruleDetlCodg: this.$route.query.ruleDetlCodg})
        }
        if (this.$route.query.errorType) {
          Object.assign(this.listQuery, {errorType: this.$route.query.errorType})
        }
        if (this.$route.query.doctorName) {
          Object.assign(this.listQuery, {doctorName: this.$route.query.doctorName})
        }
        if (this.$route.query.deptName) {
          Object.assign(this.listQuery, {deptName: this.$route.query.deptName})
        }
      }
      this.findSelectTreeAndSelectList()
      this.getList()
    })
  },
  methods: {
    // 获取所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      params.append('codeKeys', 'B34C')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    /**
     * 码表渲染方法
     */
    dictFormatter(col, row, dic_type) {
      return formaterDict(row[col.property], this.dictVoList[dic_type])
    },
    getList() {
      console.log('getList called with pageNum:', this.listQuery.pageNum, 'pageSize:', this.listQuery.pageSize)

      // 创建请求参数，保留当前的分页参数
      let requestParams = Object.assign({}, this.listQuery)

      // 从路由参数获取查询条件，但保留当前的分页参数
      let currentPageNum = requestParams.pageNum
      let currentPageSize = requestParams.pageSize

      this.getParamByBaseQuery(requestParams, this.$route.query)

      // 恢复分页参数
      requestParams.pageNum = currentPageNum
      requestParams.pageSize = currentPageSize

      let standardYear = this.$route.query.standardYear
      if (standardYear && standardYear.includes('年')) {
        standardYear = standardYear.substring(0, standardYear.indexOf('年'))
      }
      Object.assign(requestParams, {standardYear: standardYear})
      this.prefix = this.$somms.getCodePrefixByType(this.$route.query.type)
      this.listLoading = true

      // 使用新的请求参数，保留分页信息
      console.log('Sending request with params:', requestParams)
      queryPageData(requestParams).then(response => {
        this.listLoading = false
        if (response && response.data) {
          this.list = response.data.list || []
          this.total = response.data.total || 0
          console.log('Data loaded successfully, total:', this.total)
        } else {
          this.list = []
          this.total = 0
          console.warn('获取数据失败：响应数据格式不正确', response)
        }
      }).catch(error => {
        this.listLoading = false
        this.list = []
        this.total = 0
        console.error('获取数据失败：', error)
        this.$message.error('获取数据失败，请稍后重试')
      })
    },
    queryPageData,
    allExcel() {
      this.$somms.exportExcelAll(this.listQuery, this.total, this.$refs.medicalDetail.$children, document.getElementById('medicalTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病组详细信息')
    },
    sumStyle({row, rowIndex}) {
      if (row.patientId == '合计') {
        return {
          'background-color': '#336688',
          'font-size': '14px',
          'font-weight': 'bold',
          color: '#ffffff'
        }
      }
    },
    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      // 最后一行，合计，第一、第二、三列合并
      let lastRow = this.list.length - 1
      if (rowIndex === lastRow) {
        if (columnIndex === 0) {
          return [0, 1]
        }
        if (columnIndex === 1) {
          return [1, 2]
        } else {
          return [1, 1]
        }
      }
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    exportExcel() {
      let tableId = 'medicalTable'
      let fileName = '病案详细信息'
      elExportExcel(tableId, fileName)
    },
    getParamByBaseQuery(targetParam, sourceParam) {
      if (targetParam) {
        const keys = Object.keys(targetParam)
        // 更新参数值
        keys.forEach(key => {
          targetParam[key] = sourceParam[key]
        })
      }
    },
    setTimeToNull(type) {
      if (type != '1') {
        this.listQuery.cy_start_date = ''
        this.listQuery.cy_end_date = ''
      }
      if (type != '2') {
        this.listQuery.inStartTime = ''
        this.listQuery.inEndTime = ''
      }
      if (type != '3') {
        this.listQuery.seStartTime = ''
        this.listQuery.seEndTime = ''
      }
    }
  }
}
</script>
<style scoped>
.el-icon-caret-bottom {
  color: #FF0000;
}

.el-icon-caret-top {
  color: #00CC00;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.pagination-container .el-pagination {
  display: inline-block;
}
</style>
