<template>
  <div class="app-container">
    <!-- 一级路径 -->
    <pathway-chart :data="data" @chartClick="params => this.$emit('chartClick', params)" @showLeve2Path="showLeve2Path"
                   v-if="!level2Show"/>

    <!-- 二级路径 -->
    <div class="paths-level2-wrap" v-if="level2Show">
      <!-- 图 -->
      <pathway-chart :data="level2Data" :chart-type="2" class="paths-level2-chart" @chartClick="level2ChartClick"
                     ref="level2Chart"/>
      <!-- 表 -->
      <div class="paths-level2-table">
        <el-table height="90%"
                  stripe
                  :data="level2TableData"
                  v-loading="level2TableDataLoading"
                  style="width: 100%;"
                  border>
          <el-table-column label="目录代码" prop="catalogueCode"/>
          <el-table-column label="目录名称" prop="catalogueName"/>
          <el-table-column label="使用量" prop="useAmount"/>
          <el-table-column label="使用率">
            <template slot-scope="scope">
              {{ scope.row.useRate + '%' }}
            </template>
          </el-table-column>
          <el-table-column label="使用确认" align="center">
            <template slot-scope="scope">
              <i :class="[ 'el-icon-circle-check', 'use-confirm', scope.row.catalogueCode === '01001' ? 'use-confirm-check' : '']"></i>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { queryItems } from '@/api/clinicalPathway'
import PathwayChart from './pathwayChart'

export default {
  name: 'dipPathway',
  props: {
    data: Array
  },
  components: {
    'pathway-chart': PathwayChart
  },
  data: () => ({
    level2Show: false,
    level2TableDataLoading: false,
    level2Data: [],
    level2TableData: [],
    costItems: [
      { name: '床位费' },
      { name: '诊察费' },
      { name: '检查费' },
      { name: '化验费' },
      { name: '治疗费' },
      { name: '手术费' },
      { name: '护理费' },
      { name: '卫生材料费' },
      { name: '西药费' },
      { name: '中药饮片费' },
      { name: '中成药费' },
      { name: '一般治疗费' },
      { name: '挂号费' },
      { name: '其他费' }
    ]
  }),
  methods: {
    showLevel1 () {
      this.level2Show = false
    },
    level2ChartClick (params) {
      this.costItems.forEach(item => {
        if (item.name === params.name) {
          item.lineStyle = {
            color: 'blue'
          }
        } else {
          item.lineStyle = {}
        }
      })
      this.$refs.level2Chart.reInit()
    },
    showLeve2Path (params) {
      this.level2TableDataLoading = true
      queryItems(params).then(res => {
        this.level2TableData = res.data.itemsData
        this.level2TableDataLoading = false
      }).catch(() => {
        this.level2TableDataLoading = false
      })
      this.$emit('showLeve2Path')
      this.level2Show = true
      this.$nextTick(() => {
        this.level2Data = [{
          name: params.dipName,
          children: this.costItems.map(item => {
            item.lineStyle = {}
            return item
          })
        }]
      })
    }
  }
}
</script>
<style scoped lang="scss">
.paths-level2 {
  &-wrap {
    width: 100%;
    height: 100%;
    display: flex;
  }

  &-chart {
    width: 50%;
    height: 100%;
  }

  &-table {
    width: 50%;
    height: 100%;
  }
}

.use-confirm {
  font-size: 20px;
  cursor: pointer;

  &-check {
    color: #67c23a;
  }
}
</style>
