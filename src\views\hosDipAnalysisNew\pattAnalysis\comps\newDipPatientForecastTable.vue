<template>
  <el-table ref="elTable"
            :id="id"
            height="100%"
            stripe
            :header-cell-style="{'text-align':'center'}"
            :data="data"
            v-loading="loading"
            border>
    <el-table-column label="序号" type="index" width="50" fixed align="center"></el-table-column>
    <el-table-column label="姓名" prop="name" width="130" :fixed="include('name')"></el-table-column>
    <el-table-column label="病案号" prop="bah" width="130" :fixed="include('bah')" align="right"></el-table-column>
    <el-table-column label="出院科室" prop="deptName" width="130" :fixed="include('deptName')" align="right"></el-table-column>
    <el-table-column label="DIP编码" prop="dipCodg" width="130" :fixed="include('dipCode')" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="DIP名称" prop="dipName" width="130" :fixed="include('dipName')" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="医保类型" prop="insuType" width="130" :fixed="include('insType')">
      <template slot-scope="scope">
        {{ $somms.getDictValueByType(scope.row.insuType, "INSURANCE_TYPE") }}
      </template>
    </el-table-column>
    <el-table-column label="住院总费用" prop="inHosTotalCost" width="130" :fixed="include('inHosTotalCost')" align="right"></el-table-column>
    <el-table-column :label="this.$somms.getFCOrFBName(this.$store.getters.feeStas,0)" prop="forecastAmount" width="130" :fixed="include('forecastAmount')" align="right"></el-table-column>
    <el-table-column :label="this.$somms.getFCOrFBName(this.$store.getters.feeStas,1)" prop="forecastAmountDiff" width="130" :fixed="include('forecastAmountDiff')" align="right"></el-table-column>
    <el-table-column label="病例类型" prop="ratioRange" width="130" :fixed="include('ratioRange')">
      <template slot-scope="scope">
        {{ $somms.getDictValueByType(scope.row.ratioRange, "CASE_TYPE") }}
      </template>
    </el-table-column>
    <el-table-column label="O/E值" prop="oeVal" width="130" :fixed="include('oeVal')" align="right"></el-table-column>
    <el-table-column label="总分值" prop="totlSco" width="130" :fixed="include('totalScore')" align="right"></el-table-column>
    <el-table-column label="单价" prop="price" width="130" :fixed="include('price')" align="right"></el-table-column>
    <el-table-column label="辅助目录" prop="isUsedAsstList" :fixed="include('auxiliaryCatalogue')" width="130" align="center">
      <template slot-scope="scope">
        {{ $somms.getDictValueByType(scope.row.isUsedAsstList, "AD") }}
      </template>
    </el-table-column>
    <el-table-column label="年龄段" prop="asstListAgeGrp" width="130" :fixed="include('auxiliaryAge')" align="center"></el-table-column>
    <el-table-column label="疾病严重程度" prop="asstListDiseSevDeg" width="130" :fixed="include('auxiliaryIllness')" align="center"></el-table-column>
    <el-table-column label="肿瘤严重程度" prop="asstListTmorSevDeg" width="130" :fixed="include('auxiliaryTumour')" align="center"></el-table-column>
    <el-table-column label="悬浮"  align="center" >
      <template slot-scope="scope">
        <el-button size="mini" type="primary" @click="showSuspension(scope.row)">悬浮</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'newDipPatientForecastTable',
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    },
    // 表格id
    id: {
      type: String
    },
    columnOptions: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    include (column) {
      return this.fixedColumns.includes(column)
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    // 显示悬浮
    showSuspension (scope) {
      this.scopeData = scope
      let scopeList = []
      for (let i in this.scopeData) {
        let o = {}
        o.key = i
        o.value = this.scopeData[i]
        scopeList.push(o)
      }
      let res = []
      if (scopeList.length) {
        for (let i = 0; i < scopeList.length; i++) {
          for (let j = 0; j < this.columnOptions.length; j++) {
            if (scopeList[i].key == this.columnOptions[j].value) {
              res.push({
                key: scopeList[i].key,
                label: this.columnOptions[j].label,
                value: scopeList[i].value,
                type: 2,
                show: true
              })
            }
          }
        }
      }
      if (scope) {
        this.$emit('showSuspension', res)
      }
    }
  }
}
</script>
