<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="somForm"
             show-date-range
             show-in-date-range
             show-se-date-range
             :show-hos-dept="{ show: this.$somms.hasHosRole()}"
             show-pagination
             :show-dip="{ show: showDip}"
             :show-drg="{ show: showDrg }"
             :show-cd="{ show: showCd }"
             :totalNum="total"
             :container="true"
              :showCoustemContentTitle="true"
             headerTitle="查询条件"
             :notClearableProps="['group']"
             :exportExcel="{ 'tableId': tableId, exportName: '运营决策病种分析' + '(' + this.queryForm.begnDate + '-' + this.queryForm.expiDate + ')' }"
             :exportExcelFun="queryPageData"
             :exportExcelHasChild="true"
             :initTimeValueNotQuery="false"
             @query="queryData"
             @reset="clearRouteQuery" >
      <template slot="extendFormItems" >
<!--        <el-form-item label="组" prop="group">-->
<!--          <el-select  v-model="queryForm.group"-->
<!--                      placeholder="请选择组"-->
<!--                      class="som-form-item"-->
<!--                      @change="typeConflict()">-->
<!--            <el-option-->
<!--              v-for="item in groupOptions"-->
<!--              :key="item.value"-->
<!--              :label="item.label"-->
<!--              :value="item.value">-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
      </template>
      <template slot="buttonsPrefix">
        <el-radio-group  v-model="queryForm.queryType" size="mini" @change="fnClickQuery()" class="som-button-margin-right" v-if=this.$somms.hasHosRole()>
          <el-radio-button :label="1" >按科室查询</el-radio-button>
          <el-radio-button :label="3" >按病组查询</el-radio-button>
        </el-radio-group>
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <!-- 固定列 -->
        <div class="fixed-column">
          <el-select v-model="columnVal"
                     multiple
                     collapse-tags
                     :multiple-limit="3"
                     placeholder="请选择固定列">
            <el-option
              v-for="item in columnOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div  v-if="!showSplashes" style="height: 100%" >
            <query-Disease-Data-Table
                              ref="dataTable"
                              :table-data="tableData"
                              :grper-type="queryForm.group"
                              :queryType="queryForm.queryType"
                              :table-loading="tableLoading"
                              :query-form="queryForm"
                              :id="tableId"
                              :columnOptions="columnOptions"
                              :fixed-columns="columnVal"
                              @setRefObj="(obj) => this.tableObj = obj"/>
        </div>
        <!-- 散点图 -->
        <div id="splashes" v-else style="height: 100%;width: 100%">
          <drg-echarts :options="splashesOptions" ref="splashesChart"/>
        </div>
      </template>

      <!-- 内容 profttl -->
      <template slot="contentTitle">
        <drg-title-line title="病组分析列表">
          <template slot="rightSide">

            <!-- splashes -->
            <i class="som-icon-splashes som-iconTool"
               title="散点图"
               v-if="!showSplashes"
               @click="changeSplashesOrTable(1)"
               style="height: 1.2rem;width: 1.2rem">
            </i>

            <!-- table -->
            <i class="som-icon-table som-iconTool"
               title="表格"
               v-else
               @click="changeSplashesOrTable(2)"
               style="height: 1.2rem;width: 1.2rem"></i>
          </template>
        </drg-title-line>
      </template>
    </drg-form>
  </div>
</template>
<script>
import queryDiseaseDataTable from './/comps/queryDiseaseDataTable'
import { queryEnableGroup } from '@/api/common/sysCommon'
import { queryData as queryPageData } from '@/api/operationalDecision/analysis/operationalDecisionDiseaseAnalysis'
import { cacheMixin } from '@/utils/mixin'
export default {
  name: 'diseExtrAnalysis',
  components: {
    'query-Disease-Data-Table': queryDiseaseDataTable
  },
  // mixins: [cacheMixin],
  data: () => ({
    queryForm: {
      group: '1',
      queryType: '1',
      cdCodg: ''
    },
    tableData: [],
    tableLoading: false,
    total: 0,
    showSplashes: false,
    splashesOptions: {},
    curCheckedTag: 'dipData',
    showDept: true,
    showDip: true,
    showDrg: false,
    showCd: false,
    groupOptions: [1],
    tableId: 'diseaseTable',
    tableObj: {},
    columnVal: [],
    columnOptions: [],
    exportAllObj: {
      children: [],
      tableNode: []
    }
  }),
  created () {
    this.queryForm.group = this.$somms.getGroupType()
    if (!this.$route.query) {
      this.typeConflict()
    }
  },
  mounted () {
    if (!this.$somms.hasHosRole()) {
      this.queryForm.queryType = 3
    }
    this.$nextTick(() => {
      this.$refs.somForm.created = true
      if (this.$route.query.begnDate && this.$route.query.expiDate) {
        if (this.$route.query.begnDate.indexOf('1899') === -1 && this.$route.query.begnDate.indexOf('Invalid date') === -1) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.queryForm)
        }
      }
      if (this.$route.query.group) {
        this.queryForm.group = this.$route.query.group
      }

      if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
        if (this.$route.query.inStartTime.indexOf('1899') === -1 && this.$route.query.inStartTime.indexOf('Invalid date') === -1) {
          this.$refs.somForm.jumpTimeChange('in', this.$route.query, this.queryForm)
        }
      }

      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        if (this.$route.query.seStartTime.indexOf('1899') === -1 && this.$route.query.seStartTime.indexOf('Invalid date') === -1) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.queryForm)
        }
      }
      if (this.$route.query.deptCode) {
        this.queryForm.deptCode = this.$route.query.deptCode
      }
      /*  if(this.$route.query.queryType){
        this.queryForm.queryType=this.$route.query.queryType
      } */
      if (this.$route.query.priOutHosDeptCode) {
        this.queryForm.priOutHosDeptCode = this.$route.query.priOutHosDeptCode
      }
      if (this.$route.query.priOutHosDeptName) {
        this.queryForm.priOutHosDeptName = this.$route.query.priOutHosDeptName
      }
      if (this.$route.query.type) {
        this.queryForm.type = this.$route.query.type
      }
      this.init()
    })
    this.getEnabledGroup()
    if (this.$route.query.deptCode != '' && this.$route.query.deptCode != null) {
      this.queryForm.group = this.$route.query.group
      this.typeConflict()
      this.queryForm.deptCode = this.$route.query.deptCode
      this.queryForm.begnDate = this.$route.query.begnDate
      this.queryForm.expiDate = this.$route.query.expiDate
      this.queryForm.dateRange = this.$route.query.dateRange
      this.queryForm.inStartTime = this.$route.query.inStartTime
      this.queryForm.inEndTime = this.$route.query.inEndTime

      if (this.$route.query.inHosFlag) {
        if (this.$route.query.inHosFlag == 1) {
          this.$nextTick(() => {
            this.$refs.somForm.changeCheckBoxTime(['出院'])
            this.$refs.somForm.setButtonCheckbox('出院')
          })
        }
        if (this.$route.query.inHosFlag == 2) {
          this.$nextTick(() => {
            this.$refs.somForm.changeCheckBoxTime(['入院'])
            this.$refs.somForm.setButtonCheckbox('入院')
          })
        }
        if (this.$route.query.inHosFlag == 3) {
          this.$nextTick(() => {
            this.$refs.somForm.changeCheckBoxTime(['结算'])
            this.$refs.somForm.setButtonCheckbox('结算')
          })
        }
      }
      this.queryForm.inHosFlag = this.$route.query.inHosFlag
      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.queryForm)
      }
    }
    this.init()
  },
  methods: {
    queryPageData,
    init () {
      this.queryData()
    },
    // 生成固定列
    generateFixedColumns () {
      this.columnOptions = []
      let children = this.$refs.dataTable.$children[0].$children
      if (children) {
        children.forEach(item => {
          if (item.$options.propsData.label && item.$options.propsData.label != '序号') {
            this.columnOptions.push({
              value: item.$options.propsData.prop,
              label: item.$options.propsData.label
            })
          }
        })
      }
    },
    queryData () {
      this.tableLoading = true
      queryPageData(this.getParams()).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.total
          this.tableLoading = false
          this.createSplashes()
          this.generateFixedColumns()
        }
      })
    },
    clearRouteQuery () {
      if (this.$route.query) {
        this.$router.push({ query: {
        } }).catch(() => {})
      }
    },
    createSplashes () {
      let highData = []
      let lowData = []
      let deptNames = []
      if (this.tableData.length > 0) {
        this.tableData.map(data => {
          if (this.queryForm.queryType == 1) {
            deptNames.push(data.deptName)
          }
          if (this.queryForm.group == 1) {
            highData.push([data.medcasVal, data.ultrahighRate, data.dipCodg, data.dipName])
            lowData.push([data.medcasVal, data.ultraLowRate, data.dipCodg, data.dipName])
          } else if (this.queryForm.group == 3) {
            highData.push([data.medcasVal, data.ultrahighRate, data.drgCodg, data.drgName])
            lowData.push([data.medcasVal, data.ultraLowRate, data.drgCodg, data.drgName])
          } else if (this.queryForm.group == 2) {
            highData.push([data.medcasVal, data.ultrahighRate, data.cdCodg, data.cdName])
            lowData.push([data.medcasVal, data.ultraLowRate, data.cdCodg, data.cdName])
          }
        })
      }
      this.splashesOptions = {
        title: {
          text: '病种超高超低分析',
          left: 'center',
          top: 0
        },
        tooltip: {
          // trigger: 'axis',
          showDelay: 0,
          formatter: function (params) {
            if (deptNames[params.dataIndex] == undefined) {
              return '编码:' + params.data[2] + '<br/>' +
               '名称:' + params.data[3] + '<br/>' +
               '病案数:' + params.data[0] + '<br/>' +
               (params.seriesIndex == 0 ? '  超高率:' : '   超低率') + params.data[1] + '%'
            } else {
              return deptNames[params.dataIndex] + '<br/>' +
                '编码:' + params.data[2] + '<br/>' +
               '名称:' + params.data[3] + '<br/>' +
               '病案数:' + params.data[0] + '<br/>' +
               (params.seriesIndex == 0 ? '  超高率:' : '   超低率') + params.data[1] + '%'
            }
          },
          axisPointer: {
            show: true,
            type: 'cross',
            lineStyle: {
              type: 'dashed',
              width: 1
            }
          }
        },
        color: ['#778dd1', '#a7d692'],
        legend: {
          data: ['高倍率', '低倍率'],
          right: 50,
          top: 50,
          width: 100
        },
        xAxis: {
          name: '病案数',
          splitLine: { show: false },
          axisLabel: {
            formatter: '{value}'
          }
        },
        yAxis: {
          name: '超高超低率',
          scale: true,
          splitLine: { show: false },
          axisLabel: {
            formatter: '{value} %'
          }
        },
        series: [
          // 高倍率点
          {
            name: '高倍率',
            type: 'scatter',
            emphasis: {
              focus: 'series'
            },
            symbolSize: 15,
            data: highData
          },
          // 低倍率点
          {
            name: '低倍率',
            type: 'scatter',
            emphasis: {
              focus: 'series'
            },
            symbolSize: 15,
            data: lowData
          }
        ]
      }
      if (this.showSplashes) {
        this.$nextTick(() => {
          this.$refs.splashesChart.initChart()
        })
      }
    },
    getEnabledGroup () {
      queryEnableGroup().then(res => {
        if (res.code == 200) {
          this.groupOptions = res.data
        }
      })
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.ym = params.ym.substring(0, 4) + '-' + params.ym.substring(4, 7)
      params.year = params.ym.substring(0, 4)
      params.dataAuth = true
      params.cdCodg = this.queryForm.cdCodg
      return params
    },
    updated: function () {
      this.$nextTick(() => {
        this.$refs['dataTable'].doLayout()
      })
    },
    typeConflict () {
      if (this.queryForm.group == 1) {
        this.showDip = true
        this.showDrg = false
        this.showCd = false
        this.queryForm.drgCodg = ''
        this.queryForm.cdCodg = ''
        this.tableData = []
        this.queryData()
      } else if (this.queryForm.group == 3) {
        this.showDip = false
        this.showDrg = true
        this.showCd = false
        this.queryForm.dipCodg = ''
        this.queryForm.cdCodg = ''
        // this.tableData=''
        this.queryData()
      } else if (this.queryForm.group == 2) {
        this.showDip = false
        this.showDrg = false
        this.showCd = true
        this.queryForm.dipCodg = ''
        this.queryForm.drgCodg = ''
        this.queryData()
      }
    },
    fnClickQuery () {
      if (this.queryForm.queryType == 3) {
        this.showDept = false
        this.queryData()
      } else {
        this.showDept = true
        this.queryData()
      }
    },
    changeSplashesOrTable (index) {
      if (index == 1) {
        this.showSplashes = true
      } else {
        this.showSplashes = false
      }
      this.init()
    },
    defaultRadio () {

    }
  }
}
</script>

<style scoped>
/*固定列*/
.fixed-column {
  position: absolute;
  left: 10%;
  top: 1%;
}
/deep/ .el-tab-pane{
  height: 99%;
}
/deep/ .el-tabs__content{
  height: 92%;
}
/deep/ .el-progress__text{
  font-size:10px;
}
/deep/ .el-progress-bar{
  width: 99%;
}
/deep/ .el-card__header{
  padding:5px 5px;
  font-size: 13px;
  background-color: #eef1f6;
}
.el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
  margin-bottom:20px;
  margin-right: 10px;
}
/deep/ .el-card__body{
  height:90%;
  padding-top: 0px;
  padding-right: 10px;
  padding-bottom: 0px;
  overflow-y:auto;
}
.note {
  font-size:12px;
}
.time{
  font-size:13px;
  font-weight: bold;
}
.scope{
  margin-top:0px;
}
/deep/ .is-process{
  color: #e6a23c;
  border-color: #e6a23c;
}

</style>
