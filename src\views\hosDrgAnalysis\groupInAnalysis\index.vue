<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
              show-date-range
              show-in-date-range
              show-se-date-range
              show-hos-dept
              showPagination
              :totalNum="total"
              headerTitle="查询条件"
              contentTitle="DRGs入组情况"
              :container="true"
              :initTimeValueNotQuery="false"
              @query="handleSearchList" ref="somForm">

      <!--      <template slot="buttons">-->
      <!--        <el-popconfirm confirm-button-text='确定'-->
      <!--                       cancel-button-text='导出全部'-->
      <!--                       icon="el-icon-info"-->
      <!--                       icon-color="red"-->
      <!--                       title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">-->
      <!--          <el-button slot="reference" type="success">导出Excel</el-button>-->
      <!--        </el-popconfirm>-->
      <!--      </template>-->

      <template slot="buttons">
        <el-button type="success" @click="exportExcel" style="margin-right: 15px">导出Excel</el-button>
      </template>

      <template slot="containerContent">
        <div style="height:4rem">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="6" style="height: 100%">
              <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                <div style="height:30%;width:100%;padding-left:5px;margin-top:5px;font-size: 12px;font-weight: bold;">
                  全院病案总数
                </div>
                <div style="height:70%;width:100%;padding-left:10px;font-size: 12px;font-weight: bold;display: flex;">
                  <div style="width:40%;height:100%;display: flex;">
                    <div v-if="medicalRecordNum>=10000" class="number">
                      {{ (medicalRecordNum / 10000).toFixed(2) }}万
                    </div>
                    <div v-else class="number">
                      {{ medicalRecordNum }}
                      <span class="fen">例</span>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同期:
                        <span class="compareNum">{{ lastYearMedicalRecordNum }}</span>
                      </div>
                      <div class="compare2">
                        上期:
                        <span class="compareNum">{{ lastMonthMedicalRecordNum }}</span>
                      </div>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同比:
                        <span class="compareRateIncrease" v-if="medicalRecordNumYOY>0">
                      {{ (medicalRecordNumYOY) }}%↑
                    </span>
                        <span class="compareRateEqual" v-if="medicalRecordNumYOY==0">
                      0
                    </span>
                        <span class="compareRateDecrease" v-if="medicalRecordNumYOY<0">
                      {{ Math.abs(medicalRecordNumYOY) }}%↓
                    </span>
                      </div>
                      <div class="compare2">
                        环比:
                        <span class="compareRateIncrease" v-if="medicalRecordNumRingRatio>0">
                      {{ (medicalRecordNumRingRatio) }}%↑
                    </span>
                        <span class="compareRateEqual" v-if="medicalRecordNumRingRatio==0">
                      0
                    </span>
                        <span class="compareRateIncrease" v-if="medicalRecordNumRingRatio<0">
                      {{ Math.abs(medicalRecordNumRingRatio) }}%↓
                    </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>

            <el-col :span="6" style="height: 100%">
              <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                <div style="height:30%;width:100%;padding-left:5px;margin-top:5px;font-size: 12px;font-weight: bold;">
                  全院DRGs组数
                </div>
                <div style="height:70%;width:100%;padding-left:10px;font-size: 12px;font-weight: bold;display: flex;">
                  <div style="width:40%;height:100%;display: flex;">
                    <div v-if="drgNum>=10000" class="number">
                      {{ (drgNum / 10000) }}万
                    </div>
                    <div v-else class="number">
                      {{ drgNum }}
                      <span class="fen">组</span>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同期:
                        <span class="compareNum">{{ lastYearDrgNum }}</span>
                      </div>
                      <div class="compare2">
                        上期:
                        <span class="compareNum">{{ lastMonthDrgNum }}</span>
                      </div>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同比:
                        <span class="compareRateIncrease" v-if="drgNumYOY>0">
                      {{ (drgNumYOY) }}%↑
                    </span>
                        <span class="compareRateEqual" v-if="drgNumYOY==0">
                      0
                    </span>
                        <span class="compareRateDecrease" v-if="drgNumYOY<0">
                      {{ Math.abs(drgNumYOY) }}%↓
                    </span>
                      </div>
                      <div class="compare2">
                        环比:
                        <span class="compareRateIncrease" v-if="drgNumRingRatio>0">
                      {{ (drgNumRingRatio) }}%↑
                    </span>
                        <span class="compareRateEqual" v-if="drgNumRingRatio==0">
                      0
                    </span>
                        <span class="compareRateIncrease" v-if="drgNumRingRatio<0">
                      {{ Math.abs(drgNumRingRatio) }}%↓
                    </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="6" style="height: 100%">
              <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                <div style="height:30%;width:100%;padding-left:5px;margin-top:5px;font-size: 12px;font-weight: bold;">
                  全院入组病案数
                </div>
                <div style="height:70%;width:100%;padding-left:10px;font-size: 12px;font-weight: bold;display: flex;">
                  <div style="width:40%;height:100%;display: flex;">
                    <div v-if="drgInGroupMedcasVal>=10000" class="number">
                      {{ (drgInGroupMedcasVal / 10000).toFixed(2) }}万
                    </div>
                    <div v-else class="number">
                      {{ drgInGroupMedcasVal }}
                      <span class="fen">例</span>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同期:
                        <span class="compareNum">{{ lastYearInGroupNum }}</span>
                      </div>
                      <div class="compare2">
                        上期:
                        <span class="compareNum">{{ lastMonthInGroupNum }}</span>
                      </div>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同比:
                        <span class="compareRateIncrease" v-if="inGroupNumYOY>0">
                      {{ (inGroupNumYOY) }}%↑
                    </span>
                        <span class="compareRateEqual" v-if="inGroupNumYOY==0">
                      0
                    </span>
                        <span class="compareRateDecrease" v-if="inGroupNumYOY<0">
                      {{ Math.abs(inGroupNumYOY) }}%↓
                    </span>
                      </div>
                      <div class="compare2">
                        环比:
                        <span class="compareRateIncrease" v-if="inGroupNumRatio>0">
                      {{ (inGroupNumRatio) }}%↑
                    </span>
                        <span class="compareRateEqual" v-if="inGroupNumRatio==0">
                      0
                    </span>
                        <span class="compareRateIncrease" v-if="inGroupNumRatio<0">
                      {{ Math.abs(inGroupNumRatio) }}%↓
                    </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="6" style="height: 100%">
              <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                <div style="height:30%;width:100%;padding-left:5px;margin-top:5px;font-size: 12px;font-weight: bold;">
                  全院未入组病案数
                </div>
                <div style="height:70%;width:100%;padding-left:10px;font-size: 12px;font-weight: bold;display: flex;">
                  <div style="width:40%;height:100%;display: flex;">
                    <div v-if="notInGroupNum>=10000" class="number">
                      {{ (notInGroupNum / 10000) }}万
                    </div>
                    <div v-else class="number">
                      {{ notInGroupNum }}
                      <span class="fen">例</span>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同期:
                        <span class="compareNum">{{ lastYearNotInGroupNum }}</span>
                      </div>
                      <div class="compare2">
                        上期:
                        <span class="compareNum">{{ lastMonthNotInGroupNum }}</span>
                      </div>
                    </div>
                  </div>
                  <div style="width:30%;display: flex;flex-direction: column">
                    <div style="margin:auto">
                      <div class="compare1">
                        同比:
                        <span class="compareRateIncrease" v-if="notInGroupNumYOY>0">
                      {{ notInGroupNumYOY }}%↑
                    </span>
                        <span class="compareRateEqual" v-if="notInGroupNumYOY==0">
                      0
                    </span>
                        <span class="compareRateDecrease" v-if="notInGroupNumYOY<0">
                      {{ Math.abs(notInGroupNumYOY) }}%↓
                    </span>
                      </div>
                      <div class="compare2">
                        环比:
                        <span class="compareRateIncrease" v-if="notInGroupNumRatio>0">
                      {{ (notInGroupNumRatio) }}%↑
                    </span>
                        <span class="compareRateEqual" v-if="notInGroupNumRatio==0">
                      0
                    </span>
                        <span class="compareRateIncrease" v-if="notInGroupNumRatio<0">
                      {{ Math.abs(notInGroupNumRatio) }}%↓
                    </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div style="height:31%;">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="12" style="height: 100%">
              <div id="notIngroupCount" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
            <el-col :span="12" style="height:100%">
              <el-table ref="notInGroupTable"
                        size="mini"
                        :header-cell-style="{'text-align' : 'center'}"
                        stripe
                        height="100%"
                        :data="notInGroupList"
                        :span-method="objectSpanMethod"
                        v-loading="listLoading"
                        border>
                <el-table-column label="分类" align="left" width="95">
                  <template slot-scope="scope">{{ scope.row.notInGroupName | formatIsEmpty }}</template>
                </el-table-column>
                <el-table-column label="未入组 / 歧义原因" align="left">
                  <template slot-scope="scope">{{ scope.row.notInGroupReason | formatIsEmpty }}</template>
                </el-table-column>
                <el-table-column label="病案数" align="right" width="120">
                  <template slot-scope="scope">
                    <div v-if="Number(scope.row.medcasVal)>0" class='skip'
                         @click="queryNoGroupCountMedicalNum(scope.row)">
                      {{ scope.row.medcasVal | formatIsEmpty }}
                    </div>
                    <div v-if="Number(scope.row.medcasVal)==0" style="color:#000000">
                      {{ scope.row.medcasVal | formatIsEmpty }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="未入组占比" align="right" width="110">
                  <template slot-scope="scope">{{ scope.row.notInGroupRate | formatIsEmpty }}</template>
                </el-table-column>
                <el-table-column label="总体占比" align="right" width="110">
                  <template slot-scope="scope">{{ scope.row.allRate | formatIsEmpty }}</template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="height: calc(69% - 20px - 4rem)">
          <el-table ref="inGroupListTable"
                    id="inGroupTable"
                    :header-cell-style="{'text-align' : 'center'}"
                    highlight-current-row
                    size="mini"
                    height="100%"
                    :data="list"
                    :summary-method="getSummaries"
                    show-summary
                    v-loading="listLoading"
                    @sort-change='sortChange'
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column label="序号"
                             align="center"
                             type="index"
                             width="50">
            </el-table-column>
            <el-table-column label="出院科室编码" prop="priOutHosDeptCode" v-if="false">
              <template slot-scope="scope">{{ scope.row.priOutHosDeptCode | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="出院科室名称" prop="priOutHosDeptName" align="center" width="150" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.priOutHosDeptName | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="入组率" align="center" prop="inGrpupRate" width="150" sortable='custom'>
              <template slot-scope="scope">
                <el-progress :text-inside="true" :stroke-width="16" :percentage="Number(scope.row.inGrpupRate)"
                             :color="customColorMethod">
                </el-progress>
              </template>
            </el-table-column>
            <el-table-column label="DRGs组数" prop="drgsNum" align="center" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.drgsNum)>0" class='skip' @click="queryDrgsNum(scope.row)">
                  {{ scope.row.drgsNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.drgsNum)==0" style="color:#000000">
                  {{ scope.row.drgsNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="病案总数" prop="medicalTotalNum" align="center" width="100"
                             :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.medicalTotalNum)>0" class='skip' @click="queryMedicalTotalNum(scope.row)">
                  {{ scope.row.medicalTotalNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.medicalTotalNum)==0" style="color:#000000">
                  {{ scope.row.medicalTotalNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="入组病案数" prop="groupNum" align="center" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.groupNum)>0" class='skip' @click="queryGroupNum(scope.row)">
                  {{ scope.row.groupNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.groupNum)==0" style="color:#000000">
                  {{ scope.row.groupNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="未入组病案数" prop="noGroupNum" align="center" width="125"
                             :show-overflow-tooltip="true" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.noGroupNum)>0" class='skip' @click="queryNoGroupNum(scope.row)">
                  {{ scope.row.noGroupNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.noGroupNum)==0" style="color:#000000">
                  {{ scope.row.noGroupNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="排除病案数" prop="eliminateNum" align="center" width="110"
                             :show-overflow-tooltip="true" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.eliminateNum)>0" class='skip' @click="queryEliminateNum(scope.row)">
                  {{ scope.row.eliminateNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.eliminateNum)==0" style="color:#000000">
                  {{ scope.row.eliminateNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="主要诊断不在分组方案中" prop="mainCodeErrorNum" align="center" width="190"
                             :show-overflow-tooltip="true" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.mainCodeErrorNum)>0" class='skip' @click="queryMainCodeErrorNum(scope.row)">
                  {{ scope.row.mainCodeErrorNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.mainCodeErrorNum)==0" style="color:#000000">
                  {{ scope.row.mainCodeErrorNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="新生儿编码入组,年龄不为0" prop="noGroupPlanNum" align="center" width="200"
                             :show-overflow-tooltip="true" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.noGroupPlanNum)>0" class='skip' @click="queryNoGroupPlanNum(scope.row)">
                  {{ scope.row.noGroupPlanNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.noGroupPlanNum)==0" style="color:#000000">
                  {{ scope.row.noGroupPlanNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="当前诊断可以入MDCP但是次要诊断或者手术没有找到相应的ADRG或者填写为空"
                             prop="mainCodeAndOperateErrorNum" align="center" width="320" :show-overflow-tooltip="true"
                             sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.mainCodeAndOperateErrorNum)>0" class='skip'
                     @click="queryMainCodeAndOperateErrorNum(scope.row)">
                  {{ scope.row.mainCodeAndOperateErrorNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.mainCodeAndOperateErrorNum)==0" style="color:#000000">
                  {{ scope.row.mainCodeAndOperateErrorNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="主要诊断不可为null" prop="codeAndSexErrorNum" align="center" width="160"
                             :show-overflow-tooltip="true" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.codeAndSexErrorNum)>0" class='skip'
                     @click="queryCodeAndSexErrorNum(scope.row)">
                  {{ scope.row.codeAndSexErrorNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.codeAndSexErrorNum)==0" style="color:#000000">
                  {{ scope.row.codeAndSexErrorNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="年龄参数异常" prop="ageErrorNum" align="center" width="160"
                             :show-overflow-tooltip="true" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.ageErrorNum)>0" class='skip' @click="queryAgeErrorNum(scope.row)">
                  {{ scope.row.ageErrorNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.ageErrorNum)==0" style="color:#000000">
                  {{ scope.row.ageErrorNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="新生儿体重参数异常" prop="xseWeightErrorNum" align="center" width="170"
                             :show-overflow-tooltip="true" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.xseWeightErrorNum)>0" class='skip'
                     @click="queryXseWeightErrorNum(scope.row)">
                  {{ scope.row.xseWeightErrorNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.xseWeightErrorNum)==0" style="color:#000000">
                  {{ scope.row.xseWeightErrorNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="新生儿年龄参数异常" prop="xseAgeErrorNum" align="center" width="170"
                             :show-overflow-tooltip="true" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.xseAgeErrorNum)>0" class='skip' @click="queryXseAgeErrorNum(scope.row)">
                  {{ scope.row.xseAgeErrorNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.xseAgeErrorNum)==0" style="color:#000000">
                  {{ scope.row.xseAgeErrorNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="分组失败" prop="groupFailNum" align="center" width="120"
                             :show-overflow-tooltip="true" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.groupFailNum)>0" class='skip' @click="queryGroupFailNum(scope.row)">
                  {{ scope.row.groupFailNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.groupFailNum)==0" style="color:#000000">
                  {{ scope.row.groupFailNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, querySelectTreeAndSelectList } from '@/api/common/drgCommon'
import {
  fetchList as queryPageData,
  getNoGroupResonCountInfo,
  getTopCountInfo
} from '@/api/hospitalAnalysis/inGroupAnalysis'
import { elExportExcel } from '@/utils/exportExcel'
import echarts from 'echarts'
import { sortChange } from '@/utils/common'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  b16c: null,
  cysj: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date
}
export default {
  name: 'groupInAnalysis',
  components: {},
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      tempList: [],
      listLoading: true,
      list: [],
      total: null,
      cy_start_date: '2019-06-01',
      cy_end_date: '2019-06-30',
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      b16c: null,
      medicalRecordNum: 0,
      lastYearMedicalRecordNum: 0,
      lastMonthMedicalRecordNum: 0,
      drgNum: 0,
      lastYearDrgNum: 0,
      lastMonthDrgNum: 0,
      drgInGroupMedcasVal: 0,
      lastYearInGroupNum: 0,
      lastMonthInGroupNum: 0,
      medicalRecordNumYOY: 0,
      medicalRecordNumRingRatio: 0,
      drgNumYOY: 0,
      drgNumRingRatio: 0,
      inGroupNumYOY: 0,
      inGroupNumRatio: 0,
      notInGroupNumYOY: 0,
      notInGroupNumRatio: 0,
      notInGroupNum: 0,
      lastYearNotInGroupNum: 0,
      lastMonthNotInGroupNum: 0,
      tableHeight: 0,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      notInGroupList: null,
      tableId: 'inGroupTable'
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatType (value) {
      if (value == '1') {
        return '分组器结果'
      } else if (value == '2') {
        return '排除病案'
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(() => {
      if (Object.keys(this.$route.query).length > 0) {
        if (this.$route.query.inHosFlag) {
          this.listQuery.inHosFlag = this.$route.query.inHosFlag
        }
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.listQuery)
        }

        if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
          this.$refs.somForm.jumpTimeChange('in', this.$route.query, this.listQuery)
        }

        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.listQuery)
        }
      }
      // this.handleSearchList()
    })
  },
  // 在vue生命周期updated中添加方法（使用该方法要给table里加ref="table"）
  updated: function () {
    this.$nextTick(() => {
      this.$refs['inGroupListTable'].doLayout()
    })
  },

  methods: {
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length
      }
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计'
          return
        }
        const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value))
        if (index === 2) {
          sums[index] = calculations.average(values).toFixed(2) + '%'
        } else if (index === 1) {
          sums[index] = ''
        } else {
          sums[index] = calculations.sum(values)
        }
      })
      return sums
    },
    handleSelectionChange (val) {
      if (val.length == 0) {
        this.tempList = []
      } else {
        this.tempList = this.list.filter(data => {
          for (const v of val) {
            if (data.priOutHosDeptCode == v.priOutHosDeptCode) {
              return true
            }
          }
          return false
        })
      }
    },
    sortChange,
    // my_desc_sort(a, b) {
    //   if (Number(a.inGrpupRate) > Number(b.inGrpupRate)) {
    //     return -1
    //   } else if (Number(a.inGrpupRate) < Number(b.inGrpupRate)) {
    //     return 1
    //   } else {
    //     return 0
    //   }
    // },
    // my_asc_sort(a, b) {
    //   if (Number(a.inGrpupRate) < Number(b.inGrpupRate)) {
    //     return -1
    //   } else if (Number(a.inGrpupRate) > Number(b.inGrpupRate)) {
    //     return 1
    //   } else {
    //     return 0
    //   }
    // },
    // sort_change(column) {
    //   //this.current_page = 1
    //   if (column.prop === 'groupRate') {
    //     if (column.order === 'descending') {
    //       this.list = this.list.sort(this.my_desc_sort)
    //     } else if (column.order === 'ascending') {
    //       this.list = this.list.sort(this.my_asc_sort)
    //     }
    //   }
    //   //this.list = this.filtered_data.slice(0, 200) // show only one page
    // },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.begnDate = this.cy_start_date
        this.listQuery.expiDate = this.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
        this.getTopCount()
        this.getNoGroupResonCount()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.inGroupListTable.$children, document.getElementById('inGroupTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DRG入组情况')
    },
    getTopCount () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.b16c = this.listQuery.deptCode
      getTopCountInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        this.medicalRecordNum = response.data.medicalRecordNum
        this.lastYearMedicalRecordNum = response.data.lastYearMedicalRecordNum
        this.lastMonthMedicalRecordNum = response.data.lastMonthMedicalRecordNum
        this.drgNum = response.data.drgNum
        this.lastYearDrgNum = response.data.lastYearDrgNum
        this.lastMonthDrgNum = response.data.lastMonthDrgNum
        this.drgInGroupMedcasVal = response.data.drgInGroupMedcasVal
        this.lastYearInGroupNum = response.data.lastYearInGroupNum
        this.lastMonthInGroupNum = response.data.lastMonthInGroupNum
        this.notInGroupNum = response.data.notInGroupNum
        this.lastYearNotInGroupNum = response.data.lastYearNotInGroupNum
        this.lastMonthNotInGroupNum = response.data.lastMonthNotInGroupNum
        this.medicalRecordNumYOY = response.data.medicalRecordNumYOY
        this.medicalRecordNumRingRatio = response.data.medicalRecordNumRingRatio
        this.drgNumYOY = response.data.drgNumYOY
        this.drgNumRingRatio = response.data.drgNumRingRatio
        this.inGroupNumYOY = response.data.inGroupNumYOY
        this.inGroupNumRatio = response.data.inGroupNumRatio
        this.notInGroupNumYOY = response.data.notInGroupNumYOY
        this.notInGroupNumRatio = response.data.notInGroupNumRatio
      })
    },
    getNoGroupResonCount () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.b16c = this.listQuery.deptCode
      getNoGroupResonCountInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        this.notInGroupList = response.data
        this.pieCount()
      })
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.listQuery.cysj = ['2019-06-01', '2019-06-30']
        this.cy_start_date = '2019-06-01'
        this.cy_end_date = '2019-06-30'
      }
      this.handleSearchList()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.getList()
      this.getTopCount()
      this.getNoGroupResonCount()
    },
    handleResetSearch () {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.tempList = []
      this.getDataIsuue()
    },
    customColorMethod (percentage) {
      if (Number(percentage) < 80) {
        return '#FF0000'
      } else if (Number(percentage) < 85) {
        return '#FA8072'
      } else if (Number(percentage) < 90) {
        return '#FFA500'
      } else {
        return '#67c23a'
      }
    },
    // 下转详情点击
    queryDrgsNum (row) {
      this.$router.push({
        path: '/common/queryDrgDetail',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '2'
        }
      })
    },
    queryMedicalTotalNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryGroupNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'groupNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryNoGroupNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'noGroupNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryEliminateNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'eliminateNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryMainCodeErrorNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'mainCodeErrorNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryNoGroupPlanNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'noGroupPlanNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryMainCodeAndOperateErrorNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'mainCodeAndOperateErrorNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryCodeAndSexErrorNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'codeAndSexErrorNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryAgeErrorNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'ageErrorNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryXseWeightErrorNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'xseWeightErrorNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryXseAgeErrorNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'xseAgeErrorNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryGroupFailNum (row) {
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: 'groupFailNum',
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          type: '2',
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime
        }
      })
    },
    queryNoGroupCountMedicalNum (row) {
      let deptName = '全院'
      if (this.list.length == 1) {
        deptName = this.list.priOutHosDeptName
      }
      let queryTypeStr = ''
      switch (row.notInGroupReason) {
        case '主要诊断不在分组方案中':
          queryTypeStr = 'mainCodeErrorNum'
          break
        case '新生儿编码入组,年龄不为0':
          queryTypeStr = 'noGroupPlanNum'
          break
        case '当前诊断可以入MDCP但是次要诊断或者手术没有找到相应的ADRG或者填写为空':
          queryTypeStr = 'mainCodeAndOperateErrorNum'
          break
        case '主要诊断不可为null':
          queryTypeStr = 'codeAndSexErrorNum'
          break
        case '年龄参数异常':
          queryTypeStr = 'ageErrorNum'
          break
        case '新生儿体重参数异常':
          queryTypeStr = 'xseWeightErrorNum'
          break
        case '新生儿年龄参数异常':
          queryTypeStr = 'xseAgeErrorNum'
          break
        case '分组失败':
          queryTypeStr = 'groupFailNum'
          break
      }
      this.$router.push({
        path: '/common/queryMedicalDetail',
        query: {
          queryType: queryTypeStr,
          priOutHosDeptCode: this.submitListQuery.b16c,
          priOutHosDeptName: deptName,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '2'
        }
      })
    },
    queryDetails (row) {
      this.$router.push({
        path: '/auliManage/deptCompar',
        query: {
          priOutHosDeptCode: row.priOutHosDeptCode,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          inHosFlag: this.listQuery.inHosFlag,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          type: '3'
        }
      })
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      // 第一列合并
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return {
            rowspan: 8,
            colspan: 1
          }
          // } else if (rowIndex === 3) {
          //   return {
          //     rowspan: 4,
          //     colspan: 1
          //   };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    pieCount () {
      let notInGroupReasons = []
      let result = []
      let totals = 0
      for (let i = 0; i < this.notInGroupList.length; i++) {
        if (Number(this.notInGroupList[i].medcasVal) === 0) {
          continue
        }
        notInGroupReasons.push(this.notInGroupList[i].notInGroupReason)
        result.push({ value: this.notInGroupList[i].medcasVal, name: this.notInGroupList[i].notInGroupReason })
        totals = totals + Number(this.notInGroupList[i].medcasVal)
      }
      let colors = this.$somms.generateColor()
      let get = function (e) {
        var
          newStr = e.name

        let rate = ((Number(e.value) / (Number(totals) == 0 ? 1 : Number(totals))) * 100).toFixed(2) + '%'
        return newStr + '(' + rate + ')'
      }
      let option = {

        title: [
          { text: 'DRGs未入组/歧义原因', left: 10, top: 5, textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          top: 'center',
          data: notInGroupReasons
        },
        series: [
          {
            type: 'pie',
            radius: '65%', // 设置饼图大小
            center: ['65%', '54%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'outside',
                backgroundColor: '#F0F8FF',
                borderColor: '#aaa',
                borderWidth: 1,
                borderRadius: 4,
                formatter: get,
                lineHeight: 15,
                fontFamily: 'Microsoft YaHei',
                fontSize: 12,
                color: '#000000',
                padding: [5, 5]
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % result.length]
                }
              }
            },
            data: result
          }
        ]
      }

      let notIngroupCount = echarts.getInstanceByDom(document.getElementById('notIngroupCount'))
      if (notIngroupCount) {
        notIngroupCount.clear()
      } else {
        notIngroupCount = echarts.init(document.getElementById('notIngroupCount'))
      }
      notIngroupCount.setOption(option)
      return notIngroupCount
    },
    exportExcel () {
      let tableId = 'inGroupTable'
      let fileName = 'DRGs入组分析' + '(' + this.submitListQuery.cy_start_date + '-' + this.submitListQuery.cy_end_date + ')'
      elExportExcel(tableId, fileName)
    }
  }
}
</script>
<style scoped>
.number {
  margin-top: 2px;
  font-size: 28px;
  font-weight: bold;
}

.fen {
  font-size: 10px;
}

.compare1 {
  font-weight: normal;
  font-size: 10px;
  color: grey;
  width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

.compare2 {
  font-weight: normal;
  font-size: 10px;
  color: grey;
  margin-top: 3px;
  width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

.compareNum {
  font-weight: bold;
  margin-left: 5px;
}

.compareRateIncrease {
  font-weight: bold;
  color: #409EFF;
  margin-left: 5px;
}

.compareRateEqual {
  font-weight: bold;
  color: #000000;
  margin-left: 5px;
}

.compareRateDecrease {
  font-weight: bold;
  color: red;
  margin-left: 5px;
}
</style>
