import { getBaseInfo, getForecastInfo, getControlInfo } from '@/api/firstPage2'
import store from '@/store'

const cache = {
  state: {

  },
  mutations: {
    CACHE_ALL: state => {

    }
  },
  actions: {
    evictAll: ({ commit }) => {
      return new Promise((resolve, reject) => {
        getBaseInfo(getParams()).then(res => {
          getForecastInfo(getParams()).then(res => {
            getControlInfo(getParams()).then(res => {
              resolve()
            })
          })
        })
      })
    }
  }
}

function getParams (evict = false) {
  let params = {}
  params.grperType = store.getters.getFzlx
  let date = new Date()
  let year = date.getFullYear()
  let month = date.getMonth() + 1
  params.curMonth = year + '-' + (month < 10 ? '0' + month : month)
  params.cacheEvict = evict
  return params
}

export default cache
