<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
              show-date-range
              :show-in-date-range="false"
              :show-se-date-range="false"
              show-patient-num
              :show-hos-dept="{ show: this.$somms.hasHosRole()}"
              showPagination
              :totalNum="total"
              headerTitle="查询条件"
              contentTitle="结算清单详情"
              :container="true"
              :exportExcel="{ 'tableId': tableId, exportName: '结算清单'}"
              :exportExcelFun="queryPageData"
              :exportExcelHasChild="false"
              :initTimeValueNotQuery="false"
              ref="somForm"
              @query="handleSearchList">

      <template slot="extendFormItems">
        <el-form-item label="场景类型" prop="ruleScenType">
          <drg-dict-select dicType="RULE_SCEN_TYPE" placeholder="请选择场景类型" v-model="listQuery.ruleScenType"
                           @change="getDataIsuue"/>
        </el-form-item>
        <el-form-item label="患者姓名">
          <el-input v-model="listQuery.a11" placeholder="请输入患者姓名"/>
        </el-form-item>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  :id="tableId"
                  size="mini"
                  stripe
                  height="100%"
                  :data="list"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>
          <el-table-column label="序号" type="index" align="right">
          </el-table-column>
          <el-table-column label="病案号" prop="medcasno" align="center" width="120"/>
          <el-table-column label="患者姓名" prop="name" align="center" width="100"/>
          <el-table-column label="年龄" prop="age" align="center" width="80"/>
          <el-table-column label="入院日期" prop="b12" align="center" width="120">
            <template slot-scope="scope">
              {{ scope.row.b12 | formatTime }}
            </template>
          </el-table-column>
          <el-table-column label="出院日期" prop="b15" align="center" width="120">
            <template slot-scope="scope">
              {{ scope.row.b15 | formatTime }}
            </template>
          </el-table-column>
          <el-table-column label="住院天数" prop="iptDay" align="center" width="100"/>
          <el-table-column label="科室名称" prop="deptName" align="center" width="120"/>
          <el-table-column label="医师姓名" prop="doctorName" align="center" width="100"/>
          <el-table-column label="违规规则编码" prop="ruleDetlCodg" align="center" width="150">
          </el-table-column>
          <el-table-column label="违规描述" prop="errorDesc" align="center" width="200"/>
          <el-table-column label="违规金额" prop="violationAmount" align="center" width="120">
            <template slot-scope="scope">
              ¥{{ scope.row.totalAmount | formatIsEmpty }}
            </template>
          </el-table-column>
          <el-table-column label="总费用" prop="sumfee" align="center" width="120">
            <template slot-scope="scope">
              ¥{{ scope.row.sumfee | formatIsEmpty }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="viewDetail(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>

        </el-table>


      </template>
    </drg-form>
  </div>
</template>
<script>
import {
  querySelectTreeAndSelectList,
  queryLikeDipGroupByPram,
  queryLikeDrgsByPram,
  queryDataIsuue
} from '@/api/common/drgCommon'
import {deleteDataById, getHisDate} from '@/api/medicalQuality/settleList'
import {getResultListByPatient as queryPageData} from '@/api/examCorrection/ruleAndTuples'
import {formatDate} from '@/utils/date'
import {elExportExcel} from '@/utils/exportExcel'
import { error } from 'shelljs'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  ruleScenType: '1',
  cysj: null,
  a48: null,
  a11: null,
  b16c: null,
  b34c: null,
  begnDate: '',
  expiDate: '',
  seStartTime: '',
  seEndTime: '',
  errorType:'',
  ruleDetlCodg:''
}
export default {
  name: 'mdcsListManage',
  components: {},
  inject: ['reload'],
  data() {
    return {
      showFlag: false,
      dialogVisible: false,
      tableId: 'dataTable',
      listLoading: true,
      list: null,
      total: null,
      dictVoList: {},
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      depts: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created() {

  },
  watch: {
    list: function () {
      this.$nextTick(() => {
        if (this.$refs.settleListTable) {
          this.$refs.settleListTable.doLayout()
        }
      })
    }
  },
  filters: {
    formatIsEmpty(value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime(time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },
    formatA12c(value) {
      if (value == '1') {
        return '男'
      } else if (value == '2') {
        return '女'
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.findSelectTreeAndSelectList()
      if (Object.keys(this.$route.query).length > 0) {
        if (this.$route.query.errorType) {
          this.listQuery.errorType = this.$route.query.errorType
        }
        if (this.$route.query.ruleDetlCodg) {
          this.listQuery.ruleDetlCodg = this.$route.query.ruleDetlCodg
        }
        // 时间需要在最下面设置
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.listQuery)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.listQuery)
        }
      }
      this.getList()
    })
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'B34C')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue() {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        this.getList()
      })
    },
    getList() {
      this.listLoading = true
      this.getParamByBaseQuery(this.submitListQuery, this.listQuery)
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel() {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.settleListTable.$children, document.getElementById('slTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病案数据')
    },
    handleSelect(item) {
      this.listQuery.dipCodg = item.dipCodg
    },
    dateChangeCysj(val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList() {
      this.getList()
    },
    handleResetSearch() {
      this.getDataIsuue()
    },
    refresh() {
      this.reload()
    },
    getParamByBaseQuery(targetParam, sourceParam) {
      if (targetParam) {
        const keys = Object.keys(targetParam)
        // 更新参数值
        keys.forEach(key => {
          targetParam[key] = sourceParam[key]
        })
      }
    },
    getParams() {
      return this.submitListQuery
    },
    // 查看详情
    viewDetail(row) {
      console.log(row);
      
      this.$router.push({
        path: '/examAndCorrection/pationtsDetial',
        query: {
          k00: row.k00,
          id: row.id,
          ruleScenType: this.listQuery.ruleScenType
        }
      })
    }
  }
}
</script>
<style scoped>
/*时间样式设置*/
/deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner {
  width: 200px;
}

.autoSelectInputWidth {
  width: 178px;
}

</style>
