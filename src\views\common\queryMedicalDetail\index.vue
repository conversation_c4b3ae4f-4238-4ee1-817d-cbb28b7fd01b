<template>
  <div class="app-container">
    <drg-container :headerPercent="17">
      <template slot="header">
        <drg-title-line title="查询条件" />
        <el-form :model="listQuery" size="mini" label-width="96px">
          <el-row type="flex" :gutter="10" justify="space-between">
            <el-col>
              <el-form-item :label="timeName">
                <el-date-picker disabled
                                style="width: 100%"
                                v-model="listQuery.cysj"
                                type="daterange"
                                unlink-panels
                                range-separator="-"
                                value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="出院科室名称">
                <el-input class="som-form-item" v-model="listQuery.priOutHosDeptName"  disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item :label="prefix + '名称'">
                <el-input class="som-form-item" v-model="listQuery.queryDrgsName" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="疾病名称">
                <el-input class="som-form-item" v-model="listQuery.queryIcdName" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" :gutter="10" justify="space-between">
            <el-col>
              <el-form-item label="医生姓名">
                <el-input v-model="listQuery.drName"  disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="医院手术级别">
                <el-input class="som-form-item" v-model="listQuery.oprLevelName" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="标准手术级别">
                <el-input class="som-form-item" v-model="listQuery.stanOprLevelName" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-popconfirm
                confirm-button-text='确定'
                cancel-button-text='导出全部'
                icon="el-icon-info"
                icon-color="red"
                title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
                <el-button slot="reference" type="success">导出Excel</el-button>
              </el-popconfirm>
            </el-col>
          </el-row>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="病案详情" />
        <div class="table-container" style="height: 90%;width: 100%">
          <el-table ref="medicalDetail"
                    id="medicalTable"
                    size="mini"
                    height="100%"
                    :data="list"
                    style="width: 100%;"
                    v-loading="listLoading"
                    :row-style="sumStyle"
                    border>
            <!--:span-method="objectSpanMethod"-->

            <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
            <el-table-column fixed label="病案号" prop="patientId" align="left" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.patientId | formatIsEmpty}}</template>
              <!--<template slot-scope="scope">
                <div v-if="scope.row.patientId=='合计'" style="margin-left:50px">
                  {{scope.row.patientId | formatIsEmpty}}
                </div>
                <div v-if="scope.row.patientId!='合计'">
                  {{scope.row.patientId | formatIsEmpty}}
                </div>
              </template>-->
            </el-table-column>
            <el-table-column fixed label="姓名" prop="name"  align="left" width="70" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.name | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column fixed label="是否异地" prop="isRemote"  align="center" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.isRemote | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column fixed label="是否结算" prop="listSerialNumFlag"  align="center" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.listSerialNumFlag | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column  :label="prefix + '编码'" prop="drgsCode" align="left" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.drgsCode | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column :label="prefix + '名称'" prop="drgsName" align="left"  width="140" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.drgsName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="主要诊断" prop="mainDiagnoseCodeAndName" align="left" width="140" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.mainDiagnoseCodeAndName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="入院诊断" prop="admDiag" align="left" width="140"  v-if="this.drgshow" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.admDiag | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="出院诊断" prop="conditionDiagnosis" align="left" width="140"  v-if="this.drgshow" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.conditionDiagnosis | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="未入组原因"  align="center" prop="grpFaleRea" width="180" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.grpFaleRea | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column  label="入院时间" prop="inHosTime" align="center" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.inHosTime | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column  label="出院时间" prop="outHosTime" align="center" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.outHosTime | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column  label="出院科室" prop="priOutHosDeptName" align="center" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column  label="入院病情" prop="adm_diag" align="left"   v-if="this.drgshow" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.adm_diag | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column  label="出院病情" prop="conditionDiagnosis" align="left"   v-if="this.drgshow"  width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.conditionDiagnosis | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column :label="prefix + '标杆天数'"  align="center" prop="standardInHosDays" width="120">
              <template slot-scope="scope">{{scope.row.standardInHosDays | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="住院天数"  align="center" prop="inHosDays" width="80">
              <template slot-scope="scope">{{scope.row.inHosDays | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="天数差值"  align="center" prop="dayDifference" width="80">
              <template slot-scope="scope">
                <div v-if="scope.row.inHosDays==null||scope.row.inHosDays==''||scope.row.inHosDays==0||scope.row.standardInHosDays==null||scope.row.standardInHosDays==''||scope.row.standardInHosDays==0">
                  -
                </div>
                <div v-else-if="Number(scope.row.inHosDays)>Number(scope.row.standardInHosDays)">
                  <i class="el-icon-caret-bottom"></i>
                  <span style="font-size: 10px;color:#FF0000;font-weight: bold">
                {{(Number(scope.row.standardInHosDays)-Number(scope.row.inHosDays)).toFixed(2)}}
              </span>
                </div>
                <div v-else-if="Number(scope.row.inHosDays)<=Number(scope.row.standardInHosDays)">
                  <i class="el-icon-caret-top"></i>
                  <span style="font-size: 10px;color:#00CC00;font-weight: bold">
                +{{(Number(scope.row.standardInHosDays)-Number(scope.row.inHosDays)).toFixed(2)}}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="prefix + '标杆费用'" prop="standardInHosCost" align="center" width="120">
              <template slot-scope="scope">{{scope.row.standardInHosCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="住院总费用"  align="center" prop="inHosTotalCost" width="100">
              <template slot-scope="scope">{{scope.row.inHosTotalCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="总费用差值"  align="center" prop="costDifference" width="100">
              <template slot-scope="scope">
                <div v-if="scope.row.inHosTotalCost==null||scope.row.inHosTotalCost==''||scope.row.inHosTotalCost==0||scope.row.standardInHosCost==null||scope.row.standardInHosCost==''||scope.row.standardInHosCost==0">
                  -
                </div>
                <div v-else-if="Number(scope.row.inHosTotalCost)>Number(scope.row.standardInHosCost)">
                  <!--{{scope.row.inHosTotalCost | formatIsEmpty}}&emsp;-->
                  <i class="el-icon-caret-bottom"></i>
                  <span style="font-size: 10px;color:#FF0000;font-weight: bold">
                {{(Number(scope.row.standardInHosCost)-Number(scope.row.inHosTotalCost)).toFixed(2)}}
              </span>
                </div>
                <div v-else-if="Number(scope.row.inHosTotalCost)<=Number(scope.row.standardInHosCost)">
                  <!--{{scope.row.inHosTotalCost | formatIsEmpty}}&emsp;-->
                  <i class="el-icon-caret-top"></i>
                  <span style="font-size: 10px;color:#00CC00;font-weight: bold">
                +{{(Number(scope.row.standardInHosCost)-Number(scope.row.inHosTotalCost)).toFixed(2)}}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="amount370100" label="企业补充医疗保险基金" v-if="this.drgshow" align="left" width="180"/>
            <el-table-column prop="psnSelfpay" label="个人自付"   v-if="this.drgshow" align="left" width="180"/>
            <el-table-column prop="psnOwnpay" label="个人自费"  v-if="this.drgshow" align="left" width="180"/>
            <el-table-column prop="acctPay" label="个人账户"  v-if="this.drgshow" align="left" width="180"/>
            <el-table-column prop="psnCashpay" label="个人现金"  v-if="this.drgshow" align="left" width="180"/>
            <el-table-column label="药品费" prop="medicalCost" align="left" width="140">
              <template slot-scope="scope">{{scope.row.medicalCost | formatIsEmpty}} </template>
            </el-table-column>
            <el-table-column label="药占比" prop="medicalCostRate" align="center" width="140">
              <template slot-scope="scope">{{scope.row.medicalCostRate | formatIsEmpty}}%</template>
            </el-table-column>
            <el-table-column label="材料费" prop="materialCost" align="center" width="140">
              <template slot-scope="scope">{{scope.row.materialCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="耗占比" prop="materialCostRate" align="center" width="140">
              <template slot-scope="scope">{{scope.row.materialCostRate | formatIsEmpty}}%</template>
            </el-table-column>
            <!--<el-table-column label="结算清单总费用"  align="center" width="120">-->
            <!--<template slot-scope="scope">{{scope.row.totalBaseCost | formatIsEmpty}}</template>-->
            <!--</el-table-column>-->
            <el-table-column label="离院方式"  prop="dscgWay"  align="left" width="90"
                             :show-overflow-tooltip="true"
                             :formatter="(col,row)=>dictFormatter(row,col,'B34C')">
            </el-table-column>
            <el-table-column label="疾病诊断个数" prop="digsNum" align="center"  v-if="this.drgshow" width="140">
              <template slot-scope="scope">{{scope.row.digsNum | formatIsEmpty}}</template>
            </el-table-column>
<!--            <el-table-column label="次要诊断" prop="otherDiagnoseCodeAndName" align="center" width="140" :show-overflow-tooltip="true">-->
<!--              <template slot-scope="scope">{{scope.row.otherDiagnoseCodeAndName | formatIsEmpty}}</template>-->
<!--            </el-table-column>-->

            <el-table-column label="手术例数" prop="oprNum"  v-if="this.drgshow"  align="center" width="80">
              <template slot-scope="scope">{{scope.row.oprNum | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="主要手术" prop="mainOperativeCodeAndName" align="center" width="140"   v-if="this.drgshow" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.mainOperativeCodeAndName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="一级手术" prop="oneLevelOprCodeAndName" align="center" width="100"   v-if="this.drgshow" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.oneLevelOprCodeAndName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="一级标准手术" prop="oneLevelStanOprCodeAndName" align="center" width="100"   v-if="this.drgshow" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.oneLevelStanOprCodeAndName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="二级手术" prop="twoLevelOprCodeAndName" align="center" width="100"   v-if="this.drgshow" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.twoLevelOprCodeAndName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="二级标准手术" prop="twoLevelStanOprCodeAndName" align="center" width="100"  v-if="this.drgshow"  :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.twoLevelStanOprCodeAndName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="三级手术" prop="threeLevelOprCodeAndName" align="center" width="100"  v-if="this.drgshow" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.threeLevelOprCodeAndName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="三级标准手术" prop="threeLevelStanOprCodeAndName" align="center" width="100"  v-if="this.drgshow"  :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.threeLevelStanOprCodeAndName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="四级手术" prop="fourLevelOprCodeAndName" align="center" width="100"  v-if="this.drgshow" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.fourLevelOprCodeAndName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="四级标准手术" prop="fourLevelStanOprCodeAndName" align="center" width="100" v-if="this.drgshow" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.fourLevelStanOprCodeAndName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="住院医师" prop="inHosDoctorName" align="center"  width="100">
              <template slot-scope="scope">{{scope.row.inHosDoctorName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="主治医师" prop="atddrName" align="center"  v-if="this.drgshow" width="100">
              <template slot-scope="scope">{{scope.row.atddrName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="科主任" prop="deptdrtName" align="center"  v-if="this.drgshow"  width="100">
              <template slot-scope="scope">{{scope.row.deptdrtName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="主（副主）任医师" prop="chfdrName" align="center"  v-if="this.drgshow"  width="130">
              <template slot-scope="scope">{{scope.row.chfdrName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="进修医师" prop="trainDrName" align="center"  v-if="this.drgshow"  width="100">
              <template slot-scope="scope">{{scope.row.trainDrName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="质控医师" prop="qltctrlDrName" align="center"  v-if="this.drgshow"  width="100">
              <template slot-scope="scope">{{scope.row.qltctrlDrName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="实习医师" prop="intnDr" align="center"  v-if="this.drgshow"  width="100">
              <template slot-scope="scope">{{scope.row.intnDr | formatIsEmpty}}</template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            layout="total, sizes,prev, pager, next,jumper"
            :page-size="listQuery.pageSize"
            :page-sizes="[200,1000,5000,10000]"
            :current-page.sync="listQuery.pageNum"
            :total="total">
          </el-pagination>
        </div>
      </template>
    </drg-container>
  </div>
</template>

<script>
import { queryMedicalDetailList as queryPageData, querySelectTreeAndSelectList } from '@/api/common/drgCommon'
import { formaterDict } from '@/utils/dict'
import { elExportExcel } from '@/utils/exportExcel'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  priOutHosDeptCode: null,
  priOutHosDeptName: null,
  queryDrgsCode: null,
  queryDrgsName: null,
  queryIcdCode: null,
  queryIcdName: null,
  drCodg: null,
  drName: null,
  cy_start_date: null,
  cy_end_date: null,
  allOprLevelCode: null,
  oprLevelCode: null,
  oprLevelName: null,
  stanOprLevelCode: null,
  stanOprLevelName: null,
  queryType: null,
  queryDiagnosisType: null,
  type: null
}
export default {
  name:'queryMedicalDetail',
  data () {
    return {
      dictVoList: {}, // 码表
      listLoading: true,
      list: null,
      total: null,
      listQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      prefix: 0,
      drgshow:false,
      timeName: '出院时间'
    }
  },
  // created() {
  //   this.findSelectTreeAndSelectList();
  //   this.getList();
  // },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatGroupLog (value) {
      if (value == '主要诊断编码不规范') {
        return value + '（所填编码不属于国标、国临、医保版标准编码）'
      } else {
        return value
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.prefix = this.$somms.getCodePrefixByType(this.$route.query.type)
    debugger
    this.drgshow = this.prefix.toUpperCase() ==='DRG'
    this.$nextTick(function () {
      // this.$refs.medicalDetail.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      // this.tableHeight = window.innerHeight - this.$refs.medicalDetail.$el.offsetTop -35;
      // 监听窗口大小变化
      // let self = this;
      // window.onresize = function() {
      //   self.tableHeight = window.innerHeight - self.$refs.medicalDetail.$el.offsetTop -35;
      // }
      if (Object.keys(this.$route.query).length > 0) {
        if (this.$route.query.inHosFlag) {
          this.listQuery.inHosFlag = this.$route.query.inHosFlag
          if (this.$route.query.inHosFlag == '1') {
            this.timeName = '出院时间'
            Object.assign(this.listQuery, { cysj: [this.$route.query.cy_start_date, this.$route.query.cy_end_date] })
          } else if (this.$route.query.inHosFlag == '2') {
            this.timeName = '入院时间'
            Object.assign(this.listQuery, { cysj: [this.$route.query.inStartTime, this.$route.query.inEndTime] })
          } else if (this.$route.query.inHosFlag == '3') {
            this.timeName = '结算时间'
            Object.assign(this.listQuery, { cysj: [this.$route.query.seStartTime, this.$route.query.seEndTime] })
          }
        }
        if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
          Object.assign(this.listQuery, { cy_start_date: this.$route.query.cy_start_date })
          Object.assign(this.listQuery, { cy_end_date: this.$route.query.cy_end_date })
          this.setTimeToNull('1')
        }
        if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
          Object.assign(this.listQuery, { inStartTime: this.$route.query.inStartTime })
          Object.assign(this.listQuery, { inEndTime: this.$route.query.inEndTime })
          this.setTimeToNull('2')
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          Object.assign(this.listQuery, { seStartTime: this.$route.query.seStartTime })
          Object.assign(this.listQuery, { seEndTime: this.$route.query.seEndTime })
          this.setTimeToNull('3')
        }
      }
      this.findSelectTreeAndSelectList()
      this.getList()
    })
  },
  methods: {
    // 获取所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      params.append('codeKeys', 'B34C')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    /**
       * 码表渲染方法
       */
    dictFormatter (col, row, dic_type) {
      return formaterDict(row[col.property], this.dictVoList[dic_type])
    },
    getList () {
      // 回显
      Object.assign(this.listQuery, { priOutHosDeptCode: this.$route.query.priOutHosDeptCode })
      Object.assign(this.listQuery, { priOutHosDeptName: this.$route.query.priOutHosDeptName })
      Object.assign(this.listQuery, { queryDrgsCode: this.$route.query.queryDrgsCode })
      Object.assign(this.listQuery, { queryDrgsName: this.$route.query.queryDrgsName })
      Object.assign(this.listQuery, { queryIcdCode: this.$route.query.queryIcdCode })
      Object.assign(this.listQuery, { queryIcdName: this.$route.query.queryIcdName })
      Object.assign(this.listQuery, { drCodg: this.$route.query.drCodg })
      Object.assign(this.listQuery, { drName: this.$route.query.drName })
      Object.assign(this.listQuery, { allOprLevelCode: this.$route.query.allOprLevelCode })
      Object.assign(this.listQuery, { oprLevelCode: this.$route.query.oprLevelCode })
      Object.assign(this.listQuery, { oprLevelName: this.$route.query.oprLevelName })
      Object.assign(this.listQuery, { stanOprLevelCode: this.$route.query.stanOprLevelCode })
      Object.assign(this.listQuery, { stanOprLevelName: this.$route.query.stanOprLevelName })
      Object.assign(this.listQuery, { queryType: this.$route.query.queryType })
      Object.assign(this.listQuery, { queryDiagnosisType: this.$route.query.queryDiagnosisType })
      Object.assign(this.listQuery, { type: this.$route.query.type })
      Object.assign(this.listQuery, { cy_start_date: this.$route.query.cy_start_date })
      Object.assign(this.listQuery, { cy_end_date: this.$route.query.cy_end_date })
      Object.assign(this.listQuery, { inStartTime: this.$route.query.inStartTime })
      Object.assign(this.listQuery, { inEndTime: this.$route.query.inEndTime })
      Object.assign(this.listQuery, { seStartTime: this.$route.query.seStartTime })
      Object.assign(this.listQuery, { seEndTime: this.$route.query.seEndTime })
      let standardYear = this.$route.query.standardYear
      if (standardYear && standardYear.includes('年')) {
        standardYear = standardYear.substring(0, standardYear.indexOf('年'))
      }
      Object.assign(this.listQuery, { standardYear: standardYear })

      this.listLoading = true
      queryPageData(this.listQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.listQuery, this.total, this.$refs.medicalDetail.$children, document.getElementById('medicalTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病组详细信息')
    },
    sumStyle ({ row, rowIndex }) {
      if (row.patientId == '合计') {
        return {
          'background-color': '#336688',
          'font-size': '14px',
          'font-weight': 'bold',
          color: '#ffffff'
        }
      }
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      // 最后一行，合计，第一、第二、三列合并
      let lastRow = this.list.length - 1
      if (rowIndex === lastRow) {
        if (columnIndex === 0) {
          return [0, 1]
        }
        if (columnIndex === 1) {
          return [1, 2]
        } else {
          return [1, 1]
        }
      }
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    exportExcel () {
      let tableId = 'medicalTable'
      let fileName = '病案详细信息'
      elExportExcel(tableId, fileName)
    },

    setTimeToNull (type) {
      if (type != '1') {
        this.listQuery.cy_start_date = ''
        this.listQuery.cy_end_date = ''
      }
      if (type != '2') {
        this.listQuery.inStartTime = ''
        this.listQuery.inEndTime = ''
      }
      if (type != '3') {
        this.listQuery.seStartTime = ''
        this.listQuery.seEndTime = ''
      }
    }
  }
}
</script>
<style scoped>
  .el-icon-caret-bottom{
    color:#FF0000;
  }
  .el-icon-caret-top{
    color:#00CC00;
  }
</style>
