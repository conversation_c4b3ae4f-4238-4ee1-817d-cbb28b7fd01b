<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="form"
             :show-date-range="{ show: true, clearable: true }"
             :show-se-date-range="{ show: true, clearable: true }"
             :container="true"
             :showPagination="true"
             headerTitle="查询条件"
             contentTitle="数据列表"
             :totalNum="total"
             :exportExcel="{ 'tableId': tableId, exportName: '患者反馈分析'}"
             :exportExcelFun="selectFeedbackPersonData"
             :exportExcelHasChild="false"
             @query="queryData">

      <template slot="extendFormItems">
        <el-form-item label="科室名称">
          <el-select v-model="queryForm.deptName" clearable placeholder="请选择">
            <el-option
              v-for="item in deptOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="医生姓名">
          <el-select
            v-model="queryForm.drName" filterable placeholder="请选择" clearable>
            <el-option v-for="item in doctorValueList" :key="item.value"
                       :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="DIP编码">
          <el-input  v-model="queryForm.dipCodg" placeholder="请输入DIP编码" class="som-form-item" clearable />
        </el-form-item>
      </template>

      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  size="mini"
                  stripe
                  :id="tableId"
                  height="100%"
                  :data="dataList"
                  style="width: 100%;"
                  border>
          <el-table-column label="患者姓名" prop="psnName" align="left" >
          </el-table-column>
          <el-table-column label="病组名称" prop="disGpCodg" align="center" show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="出院科室" prop="deptName" align="center" >
          </el-table-column>
          <el-table-column label="医生名称" prop="drName" align="center" >
          </el-table-column>
          <el-table-column label="结算患者类型" prop="setlPatnType" align="center" >
          </el-table-column>
          <el-table-column label="盈亏" prop="profit" align="center" sortable>
          </el-table-column>
          <el-table-column label="结算点数" prop="setlPtNum" align="center" sortable>
          </el-table-column>
          <el-table-column label="每点数费用" prop="ptNumFee" align="center" sortable>
          </el-table-column>
          <el-table-column label="统筹费用" prop="poolFee" align="center" sortable>
          </el-table-column>
          <el-table-column label="病组统筹费用" prop="disGpPoolFee" align="center" sortable>
          </el-table-column>
          <el-table-column label="参保类型" prop="insuredType" align="center" >
          </el-table-column>
        </el-table>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDropdown, selectFeedbackDeptOptions, selectFeedbackPersonData } from '@/api/newBusiness/feebackAnalyse'
export default {
  name: 'feedbackPersonAnalyis',
  data: () => ({
    queryForm: {
      deptName: '',
      drName: '',
      dipCodg: ''
    },
    dataList: [],
    tableId: 'dataTable',
    total: null,
    deptOptions: [],
    doctorValueList: []
  }),
  mounted () {
    selectFeedbackDeptOptions(this.getParams()).then(res => {
      this.deptOptions = res.data
    })
    if (Object.keys(this.$route.query).length > 0) {
      if (this.$route.query.deptName) {
        this.queryForm.deptName = this.$route.query.deptName
      }
      if (this.$route.query.drName) {
        this.queryForm.drName = this.$route.query.drName
      }
      if (this.$route.query.dipCodg) {
        this.queryForm.dipCodg = this.$route.query.dipCodg
      }
      if (this.$route.query.begnDate && this.$route.query.expiDate) {
        this.$refs.form.jumpTimeChange('out', this.$route.query, this.queryForm)
      }

      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        this.$refs.form.jumpTimeChange('se', this.$route.query, this.queryForm)
      }
      this.queryData()
    }
  },
  methods: {
    selectFeedbackPersonData,
    queryData () {
      queryDropdown(this.getParams()).then(res => {
        this.doctorValueList = res.data
      })
      selectFeedbackPersonData(this.getParams()).then(res => {
        this.dataList = res.data.list
        this.total = res.data.total
      })
    },
    // 获取参数
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    }
  }
}
</script>
