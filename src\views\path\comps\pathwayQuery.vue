<template>
  <el-drawer
    title="路径筛选"
    :visible.sync="showQuery"
    :before-close="close">
    <!-- 提交 -->
    <div class="pq-header">
      <el-button type="primary" @click="filter">筛选</el-button>
    </div>
    <el-form :model="form" label-width="80px" class="pq-query">
      <div v-for="dataItem in form.disAndOpeList" :key="dataItem.type">
        <!-- add -->
        <div class="add-icon-container som-form-item" v-if="dataItem.data.length !== dataItem.max">
          <i class="el-icon-plus add-icon-color" @click="addFormItem(dataItem.type)"></i>
        </div>
        <el-form-item v-for="(item, index) in dataItem.data" :label="item.label" :prop="item.prop" :key="index">
          <el-select v-model="item.value"
                     filterable remote clearable
                     :remote-method="(str) => getCode(str, dataItem.type)"
                     :style="{ width:'80%' }">
            <el-option
              v-for="item in selectData(dataItem.type)"
              :key="item.icdCodg"
              :label="item.icdName"
              :value="item.icdCodg">
              <span class="code">{{ item.icdCodg }}</span>
              <span class="name">{{ item.icdName }}</span>
            </el-option>
          </el-select>
          <i class="el-icon-minus add-icon-color" @click="removeFormItem(index, dataItem.type)" v-if="index !== 0"></i>
        </el-form-item>
      </div>

      <!-- 性别 -->
      <el-form-item label="性别" prop="age">
        <drg-dict-select v-model="form.gend" dicType="GENDER" style="width: 89%"/>
      </el-form-item>

      <!-- 年龄 -->
      <el-form-item label="年龄" prop="age">
        <el-select v-model="form.age"
                   clearable
                   :style="{ width:'80%' }">
          <el-option
            v-for="item in ageOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 严重程度 -->
<!--      <el-form-item label="严重程度" prop="age">-->
<!--        <el-select v-model="form.severity"-->
<!--                   :style="{ width:'80%' }">-->
<!--          <el-option-->
<!--            v-for="item in severityOptions"-->
<!--            :key="item.value"-->
<!--            :label="item.label"-->
<!--            :value="item.value">-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
    </el-form>
  </el-drawer>
</template>
<script>
import { queryICDCode } from '@/api/medicalQuality/settleListDetail'
export default {
  name: 'pathwayQuery',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  computed: {

  },
  data: () => ({
    showQuery: false,
    form: {
      disAndOpeList: [
        {
          type: 'dis',
          max: 16,
          data: [
            { label: '主要诊断', prop: 'primaryDis', value: '' }
          ]
        },
        {
          type: 'ope',
          max: 7,
          data: [
            { label: '主要手术', prop: 'primaryOpe', value: '' }
          ]
        }
      ],
      gend: '',
      age: '',
      severity: ''
    },
    ageOptions: [
      { label: '小于18岁', value: '1' },
      { label: '大于等于18岁-小于65岁', value: '2' },
      { label: '大于等于65岁', value: '3' }
    ],
    severityOptions: [
      { label: '严重并发症或合并症', value: '1' },
      { label: '一般并发症或合并症', value: '3' },
      { label: '不伴并发症或合并症', value: '5' }
    ],
    disArr: [],
    opeArr: []
  }),
  methods: {
    getIcdData (data, max) {
      let res = []
      for (let i = 0; i < data.length; i++) {
        if (i === max) {
          break
        }
        res.push(data[i])
      }
      return res
    },
    selectData (type) {
      if (type === 'dis') {
        return this.disArr
      } else {
        return this.opeArr
      }
    },
    getCode (str, type) {
      queryICDCode({ ver: '10' }).then(res => {
        let max = 20
        if (type === 'dis') {
          let data = this.filterData(res.data.icd10, str)
          this.disArr = this.getIcdData(data, max)
        } else {
          let data = this.filterData(res.data.icd9, str)
          this.opeArr = this.getIcdData(data, max)
        }
      })
    },
    filterData (data, str) {
      return data.filter(item => item.icdCodg.toUpperCase().includes(str.toUpperCase()) ||
        item.icdName.toUpperCase().includes(str.toUpperCase()))
    },
    close (done) {
      this.$emit('close')
      done()
    },
    addFormItem (type) {
      let info = this.getFormItemInfoByType(type)
      this.form.disAndOpeList.forEach(item => {
        if (item.type === type) {
          let len = item.data.length
          item.data.push({
            label: info.label + len, prop: info.prop + len, value: ''
          })
        }
      })
    },
    removeFormItem (index, type) {
      let info = this.getFormItemInfoByType(type)
      this.form.disAndOpeList.forEach(item => {
        if (item.type === type) {
          item.data.splice(index, 1)
          for (let i = 1; i < item.data.length; i++) {
            item.data[i].label = info.label + i
            item.data[i].prop = info.prop + i
          }
        }
      })
    },
    getFormItemInfoByType (type) {
      if (type === 'dis') {
        return {
          label: '其他诊断', prop: 'otherDis'
        }
      } else if (type === 'ope') {
        return {
          label: '其他手术', prop: 'otherOpe'
        }
      }
      return {}
    },
    // 筛选
    filter () {
      let { age, gend, severity } = this.form
      let params = { age, gend, severity }
      this.form.disAndOpeList.forEach(item => {
        let arr = []
        for (let i = 0; i < item.data.length; i++) {
          if (item.data[i].value) {
            arr.push({
              label: (item.type === 'dis' ? 'c06c_' : 'c35c_') + i,
              value: item.data[i].value
            })
          }
        }
        params[item.type] = arr
      })
      this.$emit('query', params)
    }
  },
  watch: {
    show: {
      immediate: true,
      handler: function (val) {
        this.showQuery = val
      }
    }
  }
}
</script>
<style scoped>
.pq-query{
  height: 95%;
  overflow-y: auto;
}
.pq-header{
  padding: 1% 2%;
  height: 5%;
  display: flex;
  justify-content: left;
}
.add-icon-container{
  width: 100%;
  display: flex;
  justify-content: right;
  margin-bottom: 1rem;
  position: absolute;
  z-index: 9999;
}
.add-icon-color{
  color: #409EFF;
  font-size: 14px;
  cursor: pointer;
}
</style>
