<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             headerTitle="查询条件"
             contentTitle="清单查询结果"
             @query="queryData">

      <template slot="extendFormItems">

        <el-form-item label="人员编号" prop = "psnNo">
          <el-input v-model="queryForm.psnNo" />
        </el-form-item>

        <el-form-item label="结算ID" prop="setlId">
          <el-input v-model="queryForm.setlId" />
        </el-form-item>

      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <textarea v-model="text" style="width: 100%;height: 80%"/>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { querySettleListInfo } from '@/api/listManagement/listUpload'
export default {
  name: 'mdcsQuery',
  data: () => ({
    queryForm: {
      psnNo: '',
      setlId: ''
    },
    text: ''
  }),
  methods: {
    queryData () {
      querySettleListInfo(this.queryForm).then(res => {
        this.text = JSON.stringify(JSON.parse(res.data.info), null, '\t')
      })
    }
  }
}
</script>
