<template>
  <div class="app-container">
    <drg-container :headerPercent="17" :isButtonEnd="true">
      <template slot="header">
        <drg-title-line title="查询条件" />
        <el-form :inline="true" :model="listQuery" size="mini" style="height: 70%">

              <el-form-item label="手术编码">
                <el-input  v-model="listQuery.oprnOprtCodg" placeholder="请输入手术编码"></el-input>
              </el-form-item>

              <el-form-item label="手术名称" class="som-el-form-item-margin-left">
                <el-input  v-model="listQuery.oprnOprtName" placeholder="请输入手术名称"></el-input>
              </el-form-item>

              <el-form-item label="手术级别" class="som-el-form-item-margin-left">
                <el-select v-model="listQuery.oprnLv" placeholder="请选择手术级别">
                  <el-option
                    v-for="item in dictVoList.OPERATE_LEVEL"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="启用状态" class="som-el-form-item-margin-left">
                <el-select v-model="listQuery.enabFlag" placeholder="启用状态">
                  <el-option
                    v-for="item in dictVoList.STATUS"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>

          <el-row type="flex" justify="center">
            <el-col :span="8" align="middle" style="margin-top:10px;">
              <kt-button icon="fa fa-search" :label="$t('action.search')" auth="dataConfig:standardOperationConfig:view" type="primary" @click="handleSearchList"/>
              <kt-button icon="fa fa-plus" :label="$t('action.add')" auth="dataConfig:standardOperationConfig:add" type="primary" @click="handleAdd"/>
              <el-button class="expBtn" @click="exportExcel()" size="mini">导出Excel</el-button>
              <el-button @click="handleResetSearch()" size="mini">重置</el-button>
            </el-col>
          </el-row>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="手术配置" />
        <div style="height: 91%">
          <el-table ref="standardOperationConfigTable"
                    id="socTable"
                    :key=Math.random()
                    size="mini"
                    height="100%"
                    stripe
                    :data="list"
                    v-loading="listLoading"
                    border>
            <el-table-column fixed
                             label="序号"
                             type="index"
                             width="50">
            </el-table-column>
            <el-table-column v-if="false">
              <template slot-scope="scope">{{scope.row.id}}</template>
            </el-table-column>
            <el-table-column  label="手术编码"  align="center">
              <template slot-scope="scope">{{scope.row.oprnOprtCodg }}</template>
            </el-table-column>
            <el-table-column  label="手术名称"  align="center">
              <template slot-scope="scope">{{scope.row.oprnOprtName }}</template>
            </el-table-column>
            <el-table-column label="手术等级" prop="oprnLv" align="center" :formatter="(col,row)=>dictFormatter(row,col,'OPERATE_LEVEL')">
            </el-table-column>
            <el-table-column label="状态" prop="enabFlag" align="center" width="180" :formatter="(col,row)=>dictFormatter(row,col,'STATUS')">
            </el-table-column>
            <el-table-column fixed="right" header-align="center" align="center" width="185" label="操作">
              <template slot-scope="scope">
                <kt-button icon="fa fa-edit" :label="$t('action.edit')" auth="dataConfig:standardOperationConfig:edit" @click="handleEdit(scope.row)"/>
                <kt-button icon="fa fa-trash" :label="$t('action.delete')" auth="dataConfig:standardOperationConfig:delete" type="danger" @click="handleDelete(scope.row)"/>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- 新增修改界面 -->
        <el-dialog :title="!dataForm.id ? '新增' : '修改'" width="40%" :visible.sync="dialogVisible" :close-on-click-modal="false" v-som-dialog-drag>
          <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="submitForm()"
                   label-width="80px" size="mini" style="text-align:left;">
            <el-form-item label="手术编码" prop="oprnOprtCodg">
              <el-input v-model="dataForm.oprnOprtCodg" placeholder="手术编码"></el-input>
            </el-form-item>
            <el-form-item label="手术名称" prop="oprnOprtName">
              <el-input v-model="dataForm.oprnOprtName" placeholder="手术名称"></el-input>
            </el-form-item>

            <el-form-item label="手术级别" prop="oprnLv">
              <el-select v-model="dataForm.oprnLv" placeholder="请选择手术级别">
                <el-option
                  v-for="item in dictVoList.OPERATE_LEVEL"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="启用状态">
              <el-select v-model="dataForm.enabFlag" placeholder="启用状态">
                <el-option
                  v-for="item in dictVoList.STATUS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>

          </el-form>
          <span slot="footer" class="dialog-footer">
        <el-button size="mini"  @click="dialogVisible = false">{{$t('action.cancel')}}</el-button>
        <el-button size="mini"  type="primary" @click="submitForm()">{{$t('action.comfirm')}}</el-button>
      </span>
        </el-dialog>
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            layout="total, sizes,prev, pager, next,jumper"
            :page-size="listQuery.pageSize"
            :page-sizes="[200,1000,5000,10000]"
            :current-page.sync="listQuery.pageNum"
            :total="total">
          </el-pagination>
        </div>
      </template>
    </drg-container>
  </div>
</template>
<script>
import KtButton from '@/views/core/KtButton'
import { querySelectTreeAndSelectList, queryLikeIcdsByPram } from '@/api/common/drgCommon'
import { fetchList, saveStandardOperate, deleteStandardOperate } from '@/api/dataConfig/standardOperationConfig'
import { elExportExcel } from '@/utils/exportExcel'
import { formaterDict } from '@/utils/dict'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  oprnOprtCodg: null,
  oprnOprtName: null,
  oprnLv: null,
  enabFlag: null
}
export default {
  components: { KtButton },
  name: 'standardOperationManage',
  data () {
    return {
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      list: null,
      total: null,
      listQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      dialogVisible: false,
      dataForm: {
        id: null,
        oprnOprtCodg: null,
        oprnOprtName: null,
        oprnLv: null,
        enabFlag: null
      },
      dataRule: {
        oprnOprtCodg: [{ required: true, message: '手术编码不能为空', trigger: 'blur' }],
        oprnOprtName: [{ required: true, message: '手术名称不能为空', trigger: 'blur' }],
        oprnLv: [{ required: true, message: '手术等级不能为空', trigger: 'blur' }]
      }
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    this.getList()
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.standardOperationConfigTable.$el.offsetTop：表格距离浏览器的高度
      // 50表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.standardOperationConfigTable.$el.offsetTop - 50
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.standardOperationConfigTable.$el.offsetTop - 50
      }
    })
  },
  methods: {
    // 获取所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      params.append('codeKeys', 'OPERATE_LEVEL,STATUS')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    /**
       * 码表渲染方法
       */
    dictFormatter (col, row, dic_type) {
      return formaterDict(row[col.property], this.dictVoList[dic_type])
    },
    getList () {
      this.listLoading = true
      fetchList(this.listQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    // 显示新增界面
    handleAdd: function () {
      this.dialogVisible = true
      this.dataForm = {
        id: null,
        oprnOprtCodg: null,
        oprnOprtName: null,
        oprnLv: null,
        enabFlag: '1'
      }
    },
    // 显示编辑界面
    handleEdit: function (row) {
      this.dialogVisible = true
      Object.assign(this.dataForm, row)
    },
    // 删除
    handleDelete (row) {
      this.$confirm('确认删除选中记录吗？', '提示', {
        type: 'warning'
      }).then(() => {
        let params = new URLSearchParams()
        params.append('id', row.id)
        deleteStandardOperate(params).then(res => {
          this.getList()
          this.$message({ message: '删除成功', type: 'success' })
        })
      })
    },
    // 表单提交
    submitForm () {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$confirm('确认提交吗？', '提示', {}).then(() => {
            this.editLoading = true
            let params = Object.assign({}, this.dataForm)
            saveStandardOperate(params).then(res => {
              this.editLoading = false
              if (res.code == 200) {
                this.$message({ message: '操作成功', type: 'success' })
                this.$refs['dataForm'].resetFields()
                this.dialogVisible = false
              } else {
                this.$message({
                  message: '操作失败, ' + res.msg,
                  type: 'error'
                })
              }
              this.getList()
            })
          })
        }
      })
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.listQuery.pageNum = 1
      this.getList()
    },
    handleResetSearch () {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.getList()
    },
    exportExcel () {
      let tableId = 'socTable'
      let fileName = '标准手术信息'
      elExportExcel(tableId, fileName)
    }
  }
}
</script>
<style>
</style>
