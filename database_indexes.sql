-- 针对您的查询创建关键索引
-- 这些索引将显著提升查询性能

-- 1. 主查询条件索引
CREATE INDEX idx_hcm_valid_result_inhosp_rule_scen_type ON hcm_valid_result_inhosp(rule_scen_type);
CREATE INDEX idx_hcm_settle_zy_b_discharge_date ON hcm_settle_zy_b(discharge_date);

-- 2. JOIN条件索引
CREATE INDEX idx_hcm_valid_result_inhosp_unique_id ON hcm_valid_result_inhosp(unique_id);
CREATE INDEX idx_hcm_settle_zy_b_hisid ON hcm_settle_zy_b(hisid);

-- 3. 复合索引优化JOIN性能
CREATE INDEX idx_hcm_data_grp_cfg_join ON hcm_data_grp_cfg(data_code, rule_year, data_grp_code);
CREATE INDEX idx_hcm_rule_cfg_join ON hcm_rule_cfg(rule_detl_codg, rule_year);

-- 4. 覆盖索引（包含查询所需的所有字段，避免回表）
CREATE INDEX idx_hcm_valid_result_inhosp_cover ON hcm_valid_result_inhosp(
    rule_scen_type, 
    unique_id, 
    rule_valid_result_id, 
    rule_detl_codg, 
    rule_data_meta, 
    med_list_codg,
    oprn_date
);

-- 5. 时间范围查询优化
CREATE INDEX idx_hcm_settle_zy_b_discharge_hisid ON hcm_settle_zy_b(discharge_date, hisid);

-- 执行完索引创建后，更新表统计信息
ANALYZE TABLE hcm_valid_result_inhosp;
ANALYZE TABLE hcm_settle_zy_b;
ANALYZE TABLE hcm_data_grp_cfg;
ANALYZE TABLE hcm_rule_cfg;
