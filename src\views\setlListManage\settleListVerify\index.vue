<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
              show-issue
              show-patient-num
              :showPagination="true"
              :totalNum="total"
              :container="true"
              :exportExcel="{ tableId: tableId, exportName: '清单处理'}"
              :exportExcelFun="queryVerifyInfo"
              headerTitle="查询条件"
              @query="queryData">

      <!-- 内容标题 -->
      <template slot="contentTitle">
        <drg-title-line title="查询结果">
          <!--          <template slot="rightSide">-->
          <!--            <div style="display: flex;position: absolute;top: 10px;right: 0;width: 10rem">-->
          <!--              <el-tooltip class="item" effect="dark" content="是否开启以当前期号数据为系统数据分析数据" placement="top">-->
          <!--                <el-switch-->
          <!--                  v-model="enableAns"-->
          <!--                  active-color="#13ce66"-->
          <!--                  inactive-color="#ff4949"-->
          <!--                  active-text="开启"-->
          <!--                  inactive-text="不开启"-->
          <!--                  @change="enableSeAnsChange">-->
          <!--                </el-switch>-->
          <!--              </el-tooltip>-->
          <!--            </div>-->
          <!--          </template>-->
        </drg-title-line>
      </template>

      <!-- 按钮 -->
      <template slot="buttons">
        <el-button type="primary" class="som-button-margin-right" @click="dialogVisible = true">上传医保结算数据
        </el-button>
        <el-popconfirm
            class="som-button-margin-right"
            confirm-button-text='标识'
            cancel-button-text='取消标识'
            icon="el-icon-info"
            icon-color="red"
            title="清单标识" @confirm="mark(true, false)" @cancel="mark(false, false)">

          <el-button slot="reference" type="success">修改</el-button>
        </el-popconfirm>

        <el-popconfirm
            class="som-button-margin-right"
            confirm-button-text='全部标识'
            cancel-button-text='全部取消标识'
            icon="el-icon-info"
            icon-color="red"
            title="清单标识" @confirm="mark(true, true)" @cancel="mark(false, true)">

          <el-button slot="reference" type="success">全部修改</el-button>
        </el-popconfirm>
      </template>

      <!-- 条件 -->
      <template slot="extendFormItems">
        <el-form-item label="数据筛选" prop="dataFilter">
          <el-select v-model="queryForm.dataFilter" clearable @change="queryData">
            <el-option label="未在系统数据" value="1"></el-option>
            <el-option label="可能多余数据" value="2"></el-option>
          </el-select>
        </el-form-item>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <el-table ref="dataTable"
                  :id="tableId"
                  :data="data"
                  v-loading="loading"
                  height="100%"
                  :header-cell-style="{'text-align':'center'}"
                  @selection-change="handleSelectionChange"
                  stripe
                  border>
          <el-table-column type="selection" align="center"/>
          <el-table-column label="序号" type="index" align="center"/>
          <el-table-column label="病案号" prop="a48" align="right"/>
          <el-table-column label="姓名" prop="a11" align="right"/>
          <el-table-column label="医生名称" prop="b25n" align="right"/>
          <el-table-column label="科室名称" prop="b16n" align="right"/>
          <el-table-column label="入院时间" prop="b12" align="left"/>
          <el-table-column label="出院时间" prop="b15" align="left"/>
          <el-table-column label="结算时间" prop="d37" align="left"/>
          <el-table-column label="标识状态" align="center" width="160" fixed="right">
            <template slot-scope="scope">
              <i class="som-icon-success som-icon-big" v-if="scope.row.mark === '1'"></i>
            </template>
          </el-table-column>
        </el-table>

        <el-dialog
            title="上传文件"
            :visible.sync="dialogVisible"
            width="50%">
          <el-upload
              style="text-align: center"
              drag
              ref="upload"
              :limit="1"
              action="customize"
              accept=".xlsx,.xls"
              :http-request="upload">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
          </el-upload>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { modifyMarkState, queryVerifyInfo, uploadSettleData } from '@/api/listManagement/settleListVerify'
import { getSysConfigByKey } from '@/api/common/drgCommon'
import { modifyConfig } from '@/api/dataConfig/commonConfig'

export default {
  name: 'settleListVerify',
  data: () => ({
    queryForm: {},
    data: [],
    selection: [],
    fileList: [],
    total: 0,
    loading: false,
    dialogVisible: false,
    enableAns: false,
    tableId: 'dataTable',
    enableSeAnsKey: 'ENABLE_SE_ANS',
    enableSeAnsType: 'SYS_CONFIG'
  }),
  mounted () {
    this.init()
  },
  methods: {
    queryVerifyInfo,
    init () {
      getSysConfigByKey({ key: this.enableSeAnsKey + '+' + this.enableSeAnsType }).then(res => {
        if (res.code === 200 && res.data.value) {
          this.enableAns = (res.data.value === '1')
        }
      })
    },
    queryData () {
      this.loading = true
      queryVerifyInfo(this.getParams()).then(res => {
        if (res.code === 200) {
          let resData = res.data
          this.data = resData.list
          this.total = resData.total
          this.loading = false
        }
      }).catch(() => {
        this.loading = false
      })
    },
    // 修改系统配置
    enableSeAnsChange () {
      modifyConfig({
        configKey: this.enableSeAnsKey,
        type: this.enableSeAnsType,
        configValue: this.enableAns ? '1' : '0'
      }).then(res => {
        if (res.code === 200) {
          this.$message.success(this.enableAns ? '开启成功' : '取消成功')
        }
      })
    },
    // 处理选择表格行
    handleSelectionChange (selection) {
      this.selection = selection
    },
    // 标记/取消标记
    mark (confirmFlag, all) {
      if (!all && this.selection.length === 0) {
        this.$message.warning('未选择需要修改数据')
        return
      }
      let msg = '标识成功'
      let params = this.getParams()
      if (confirmFlag) {
        params.finishSign = '1'
      } else {
        params.finishSign = '0'
        msg = '取消成功'
      }
      if (!all) {
        let k00s = []
        this.selection.forEach(s => k00s.push(s.k00))
        params.k00s = k00s
      }
      modifyMarkState(params).then(res => {
        if (res.code === 200) {
          this.$message.success(msg)
          this.queryData()
        }
      })
    },
    // 上传
    upload (data) {
      let params = new FormData()
      params.append('file', data.file)
      params.append('ym', this.queryForm.ym)
      uploadSettleData(params).then(res => {
        if (res.code === 200) {
          this.$message.success('上传成功')
          this.dialogVisible = false
          this.$refs.upload.clearFiles()
          this.queryData()
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
      })
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    }
  }
}
</script>
