<template>
  <div>
    <!--    <el-select v-model="value"-->
    <!--               filterable remote clearable-->
    <!--               :remote-method="(str) => getDisplayVal(str)"-->
    <!--               :style="{ width: width }"-->
    <!--               @change="change">-->
    <!-- {{ data.length }} -->
    <el-select
      v-model="value"
      filterable
      clearable
      remote
      :remote-method="getDisplayVal"
      :style="{ width: width }"
      @change="change"
    >
      <el-option
        v-for="(item, index) in selectList"
        :key="item[keyProps.key] + '_' + index"
        :label="item[actualKeyProps.label]"
        :value="item[actualKeyProps.value]"
      >
        <span class="code" v-if="showBadgeLabel">
          <el-badge :value="item.badgeLabel" class="item">
            {{ item[keyProps.value] }}
          </el-badge>
        </span>
        <span class="code" v-else> {{ item[keyProps.value] }} </span>
        <span class="name"> {{ item[keyProps.label] }}</span>
      </el-option>
    </el-select>
    <modify-icon :show="showIcon" :use-position="false" />
  </div>
</template>
<script>
import ModifyIcon from './settleListInfoModifyIcon.vue' // 导入修改图标组件
export default {
  components: {
    'modify-icon': ModifyIcon, // 注册修改图标组件
  },
  props: {
    // v-model 绑定的值 (双向绑定)
    modelVal: [String, Number],
    // 是否显示修改状态的图标
    showIcon: {
      type: Boolean,
      default: false,
    },
    // 搜索时是根据 'label' 还是 'value' 进行过滤
    labelOrValue: {
      type: String,
    },
    // 组件宽度
    width: {
      type: String,
      default: '80%',
    },
    // 原始数据列表 (下拉选项的来源)
    data: [Array],
    // 定义下拉选项对象中用作唯一键(key)、显示标签(label)和实际值(value)的属性名
    keyProps: {
      type: Object,
      default: () => {
        return {
          key: 'key', // 用于 v-for 的 key
          label: 'label', // 用于显示的标签
          value: 'value', // 实际选中的值
        }
      },
    },
    // 定义实际在下拉列表中显示时，选项对象中用作标签(label)和值(value)的属性名
    // 默认情况下与 keyProps 相同
    actualKeyProps: {
      type: Object,
      default: () => {
        return this.keyProps
      },
    },
    // 是否在选项右侧显示角标 (例如新旧标识)
    showBadgeLabel: {
      type: Boolean,
      default: false,
    },
    // 在 change 事件中，是否替换标签中的括号及其内容
    replaceBracket: {
      type: Boolean,
      default: false,
    },
  },
  model: {
    // 定义 v-model 的行为
    prop: 'modelVal', // v-model 绑定的 prop
    event: 'selected', // v-model 值改变时触发的事件
  },
  data: () => ({
    selectList: [], // 实际用于渲染下拉选项的列表 (可能是 data 的子集或过滤结果)
    value: '', // el-select 组件内部 v-model 绑定的值
  }),
  mounted() {
    // 组件挂载后，可以考虑初始化一次下拉列表，但当前逻辑由 watch:data 处理
    // this.getDisplayVal('', false) // 示例：如果需要挂载时加载
  },
  methods: {
    /**
     * @description 远程搜索方法，根据输入字符串过滤下拉选项
     * @param {string} str 用户输入的搜索字符串
     */
    getDisplayVal(str) {
      this.selectList = [] // 每次搜索前清空现有列表
      const inputData = this.data // 获取原始数据

      // 如果原始数据无效，则直接返回
      if (!inputData || !Array.isArray(inputData) || inputData.length === 0) {
        return
      }

      let count = 0 // 已匹配并添加到 selectList 的项的数量
      // 判断搜索字符串是否为空 (undefined, null, 空字符串)
      const limitItems = str === '' || str === null || typeof str === 'undefined'
      const MAX_ITEMS_INITIAL = 50 // 当搜索字符串为空时，最多显示的条目数

      for (let i = 0; i < inputData.length; i++) {
        // 如果是初始加载（无搜索词）且已达到最大显示数量，则停止遍历
        if (limitItems && count >= MAX_ITEMS_INITIAL) {
          break
        }

        const item = inputData[i] // 当前遍历到的原始数据项
        let isMatch = false // 标记当前项是否匹配搜索条件

        if (limitItems) {
          // 如果搜索字符串为空，则默认所有项都匹配（直到达到 MAX_ITEMS_INITIAL）
          isMatch = true
        } else {
          // 如果有搜索字符串，则进行不区分大小写的包含匹配
          const searchStrLower = str.toLowerCase()
          const label = item[this.keyProps.label] ? String(item[this.keyProps.label]).toLowerCase() : ''
          const valueKey = item[this.keyProps.value] ? String(item[this.keyProps.value]).toLowerCase() : ''

          if (this.labelOrValue === 'label' && label.includes(searchStrLower)) {
            isMatch = true
          } else if (this.labelOrValue === 'value' && valueKey.includes(searchStrLower)) {
            isMatch = true
          } else if (!this.labelOrValue) {
            // 如果未指定 labelOrValue，则同时在 label 和 value 中搜索
            if (label.includes(searchStrLower) || valueKey.includes(searchStrLower)) {
              isMatch = true
            }
          }
        }

        if (isMatch) {
          this.selectList.push(item) // 如果匹配，则添加到下拉显示列表
          count++
        }
      }
    },
    /**
     * @description el-select 的 change 事件回调
     * @param {*} val 当前选中的值 (来自 el-option 的 value)
     */
    change(val) {
      let data = {} // 用于 $emit('change', data) 的数据对象
      let reg = /\([^\)]*\)/g // 用于移除括号内容的正则表达式

      // 根据 labelOrValue 属性，填充 data 对象的 label 或 value
      if (this.labelOrValue === 'label') {
        data.label = this.replaceBracket ? val.replace(reg, '') : val
      } else {
        data.value = val
      }

      // 从当前显示的 selectList 中查找完整的选中项数据，以补充 data 对象
      for (let item of this.selectList) {
        if (this.labelOrValue === 'label' && item[this.keyProps.label] === val) {
          data.value = item[this.keyProps.value]
        } else if (this.labelOrValue === 'value' && item[this.keyProps.value] === val) {
          data.label = this.replaceBracket ? item[this.keyProps.label].replace(reg, '') : item[this.keyProps.label]
        }
      }

      // 如果需要，在提交前再次处理选中的值（移除括号）
      if (this.replaceBracket) {
        val = val.replace(reg, '')
      }

      this.$emit('selected', val) // 触发 selected 事件，用于 v-model 更新
      this.$emit('change', data) // 触发 change 事件，传递更详细的选中项信息
    },
  },
  watch: {
    // 监听 v-model 绑定的值 modelVal 的变化
    modelVal: {
      immediate: true, // 立即执行一次 handler
      handler: function (val) {
        this.value = val // 将外部传入的值同步到组件内部的 value
        // 当外部直接设置了 modelVal，且该选项可能不在当前渲染的 selectList 中时，
        // 尝试从完整数据 data 中找到它，并添加到 selectList 的开头以确保正确显示。
        if (val && this.data && this.data.length > 0) {
          const currentOptionInList = this.selectList.find(opt => opt[this.actualKeyProps.value] === val)
          if (!currentOptionInList) {
            const optionFromFullData = this.data.find(opt => opt[this.actualKeyProps.value] === val)
            if (optionFromFullData) {
              // 再次检查，避免因 getDisplayVal('') 也可能添加此项而造成的重复
              const alreadyInList = this.selectList.find(
                opt => opt[this.actualKeyProps.value] === optionFromFullData[this.actualKeyProps.value]
              )
              if (!alreadyInList) {
                this.selectList.unshift(optionFromFullData) // 添加到列表开头
                // 可选: 如果添加后 selectList 过长，可以进行裁剪
                // const MAX_ITEMS_INITIAL = 50; // 和 getDisplayVal 中保持一致
                // if (this.selectList.length > MAX_ITEMS_INITIAL + 5) { // 加一点缓冲
                //   this.selectList = this.selectList.slice(0, MAX_ITEMS_INITIAL + 5);
                // }
              }
            }
          }
        }
      },
    },
    // 监听原始数据 data 的变化
    data: {
      immediate: true, // 立即执行一次 handler
      handler: function (newData) {
        // 当原始数据变化时，使用空字符串调用 getDisplayVal 来重新初始化下拉列表
        // (会加载有限数量的初始选项)
        this.getDisplayVal('')
      },
    },
  },
}
</script>
<style scoped lang="scss">
.code {
  float: right;
  color: #8492a6;
  font-size: 13px;
}
.name {
  float: left;
}
/deep/.el-badge__content.is-fixed {
  top: 9px;
}
</style>
