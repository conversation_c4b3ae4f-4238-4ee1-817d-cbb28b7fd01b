<template>
  <div style="width: 30%">
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item v-for="(item, index) in curLink" :key="index" :to="{ path: '/' + item.path }">
        <i :class="item.icon"></i>
        {{ item.name }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>
<script>
export default {
  name: 'jpBreadcrumb',
  data: () => ({
    links: [],
    curLink: []
  }),
  computed: {
    routers () {
      return this.$store.state.user.routers
    }
  },
  methods: {
    setBreadcrumb (route) {
      if (this.links.length == 0) {
        this.links = this.getLink()
      }
      // 首页
      if (route.path === '/home') {
        this.curLink = [
          {
            name: '工作台',
            icon: 'el-icon-s-home',
            path: '/home'
          }
        ]
      } else {
        this.links.map(link => {
          link.map(tl => {
            let paths = []
            tl.map(l => {
              if (l.path) {
                paths.push('/' + l.path)
              }
            })
            if (paths.includes(route.path)) {
              this.curLink = tl
            }
          })
        })
      }
    },
    getLink () {
      let routers = this.routers
      let links = []
      if (routers && routers.length > 0) {
        routers.map(router => {
          let link = []
          this.setAll(link, router)
          if (link.length > 0) {
            links.push(link)
          }
        })
      }
      return links
    },
    setAll (link, router) {
      if (router.children && router.children.length > 0) {
        router.children.map(r => {
          let tl = []
          if (r.children && r.children.length > 0) {
            tl.push(this.setRouteInfo(router))
            tl.push(this.setRouteInfo(r))
            r.tl = tl
          } else {
            if (router.tl && router.tl.length > 0) {
              router.tl.push(this.setRouteInfo(r))
              link.push(router.tl)
            } else {
              tl.push(this.setRouteInfo(router))
              tl.push(this.setRouteInfo(r))
              link.push(tl)
            }
          }
          this.setAll(link, r)
        })
      }
    },
    setRouteInfo (router) {
      let { path, name, meta } = router
      return { path, name, icon: meta ? meta.icon : '' }
    }
  },
  watch: {
    $route: {
      immediate: true,
      handler: function (route) {
        this.setBreadcrumb(route)
      }
    }
  }
}
</script>
