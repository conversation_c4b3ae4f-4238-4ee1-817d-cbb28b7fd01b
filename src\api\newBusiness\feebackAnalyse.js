import request from '@/utils/request'

/**
 * 查询统计数据
 * @param params
 * @returns {*}
 */
export function selectSummary (params) {
  return request({
    url: '/feedbackAnalyseController/selectSummary',
    method: 'post',
    data: params
  })
}
/**
 * 查询当年数据变化
 * @param params
 * @returns {*}
 */
export function selectDataChange (params) {
  return request({
    url: '/feedbackAnalyseController/selectDataChange',
    method: 'post',
    data: params
  })
}
/**
 * 查询结算患者类型饼状图数据
 * @param params
 * @returns {*}
 */
export function selectParTypePie (params) {
  return request({
    url: '/feedbackAnalyseController/selectParTypePie',
    method: 'post',
    data: params
  })
}

/**
 * 查询差异费用排序数据
 * @param params
 * @returns {*}
 */
export function selectOrderData (params) {
  return request({
    url: '/feedbackAnalyseController/selectOrderData',
    method: 'post',
    data: params
  })
}

/**
 * 查询医生下拉框
 * @param params
 * @returns {*}
 */
export function queryDropdown (params) {
  return request({
    url: '/feedbackAnalyseController/queryDropdown',
    method: 'post',
    data: params
  })
}

/**
 * 查询科室下拉框
 * @param params
 * @returns {*}
 */
export function queryDeptDropdown (params) {
  return request({
    url: '/feedbackAnalyseController/queryDeptDropdown',
    method: 'post',
    data: params
  })
}

export function queryFeedAnalysis (params) {
  return request({
    url: '/feedbackAnalyseController/queryFeedAnalysis',
    method: 'post',
    data: params
  })
}

/**
 * 根据科室查询反馈信息
 */
export function selectFeedbackDeptData (params) {
  return request({
    url: '/feedbackAnalyseController/selectFeedbackDeptData',
    method: 'post',
    data: params
  })
}

/**
 * 查询科室下拉选
 */
export function selectFeedbackDeptOptions (params) {
  return request({
    url: '/feedbackAnalyseController/selectFeedbackDeptOptions',
    method: 'post',
    data: params
  })
}

/**
 * 根据患者查询反馈信息
 */
export function selectFeedbackPersonData (params) {
  return request({
    url: '/feedbackAnalyseController/selectFeedbackPersonData',
    method: 'post',
    data: params
  })
}

/**
 * 根据病组查询反馈信息
 */
export function selectFeedbackDiseaseData (params) {
  return request({
    url: '/feedbackAnalyseController/selectFeedbackDiseaseData',
    method: 'post',
    data: params
  })
}

/**
 * 查询病组下拉选
 */
export function selectFeedbackDiseaseOptions (params) {
  return request({
    url: '/feedbackAnalyseController/selectFeedbackDiseaseOptions',
    method: 'post',
    data: params
  })
}

export function selectFeedbackDataRight (params) {
  return request({
    url: '/feedbackAnalyseController/selectFeedbackDataRight',
    method: 'post',
    data: params
  })
}

export function selectFeedbackDoctorRight (params) {
  return request({
    url: '/feedbackAnalyseController/selectFeedbackDoctorRight',
    method: 'post',
    data: params
  })
}
