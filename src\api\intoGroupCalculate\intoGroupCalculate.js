import request from '@/utils/request'
export function groupCalculate (params) {
  return request({
    url: '/intoGroupCalculateController/groupCalculate',
    method: 'post',
    data: params
  })
}

/**
 * 查询入组测算
 * @param params
 * @returns {*}
 */
export function queryIntoGroupCalculate (params) {
  return request({
    url: '/intoGroupCalculateController/queryIntoGroupCalculate',
    method: 'post',
    data: params
  })
}

/**
 * 测算
 * @param params
 * @returns {*}
 */
export function calCulate (params) {
  return request({
    url: '/intoGroupCalculateController/calCulate',
    method: 'post',
    data: params
  })
}

/**
 * 根据id 查询主要诊断和手术
 * @param params
 * @returns {*}
 */
export function queryDiagnoseAndOperation (params) {
  return request({
    url: '/intoGroupCalculateController/queryDiagnoseAndOperation',
    method: 'post',
    data: params
  })
}

/**
 * 查询费用详情
 * @param params
 * @returns {*}
 */
export function queryCostDetails (params) {
  return request({
    url: '/intoGroupCalculateController/queryCostDetails',
    method: 'post',
    data: params
  })
}
