<template>
  <div class="app-container">
    <el-table :data="data"
              border
              height="100%"
              v-loading="loading"
              :header-cell-style="{'text-align':'center'}"
              @selection-change="selectNode"
              ref="tableRef"
              stripe>
      <el-table-column type="selection" align="center" v-if="showOperate"/>
      <el-table-column label="序号" type="index" align="center"  />
      <el-table-column label="唯一ID" prop="uniqueId" align="right" :key="9"/>
      <el-table-column label="病案号" prop="medcasCodg" align="right"  :key="1"/>
      <el-table-column label="姓名" prop="name"  :key="2"/>
      <drg-table-column label="性别" prop="gend"  dicType="GENDER" :key="3"/>
      <el-table-column label="入院时间" prop="inHosTime" align="right"  :key="7"/>
      <el-table-column label="出院时间" prop="outHosTime" align="right" :key="11"/>
      <el-table-column label="出院科室" prop="deptName" align="center" :key="6"/>
      <el-table-column label="住院医师" prop="resident" align="center" :key="8"/>
      <el-table-column label="操作" prop="operate" align="center" :key="10" v-if="showOperate">
        <template slot-scope="scope">
          <el-button type="primary" @click="sync([scope.row.uniqueId])">同步</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { syncData } from '@/api/dataHandle/dataSynchronization'

export default {
  name: 'listTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    showOperate: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    mdtrtId: []
  }),
  methods: {
    selectNode (selection) {
      this.mdtrtId = selection
      this.$emit('selectNode', selection)
    },
    sync (uniqueIdArr) {
      this.$confirm('是否同步？', '同步提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        syncData({ uniqueIdArr }).then(res => {
          if (res.code === 200) {
            this.$message.success('同步成功')
          }
          this.$refs.tableRef.clearSelection()
          this.$emit('success')
        })
      })
    }
  }
}
</script>

<style></style>
