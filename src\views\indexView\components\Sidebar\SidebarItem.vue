<template>
  <div style="position: relative">
    <template v-for="item in routes">
      <template v-if="!item.hidden&&item.children">
        <router-link v-if="hasOneShowingChildren(item.children) && !item.children[0].children&&!item.alwaysShow"
                     :to="item.path+'/'+item.children[0].path"
                     :key="item.children[0].name">
          <el-menu-item :index="item.path+'/'+item.children[0].path" :class="{'submenu-title-noDropdown':!isNest}">
            <!--<svg-icon v-if="item.children[0].meta&&item.children[0].meta.icon" :icon-class="item.children[0].meta.icon"></svg-icon>-->
            <i v-if="item.children[0].meta&&item.children[0].meta.icon" :class="item.children[0].meta.icon"></i>
            <span v-if="item.children[0].meta&&item.children[0].meta.profttl"
                  slot="profttl">{{ item.children[0].meta.profttl }}</span>
          </el-menu-item>
        </router-link>

        <el-submenu v-else :index="item.name||item.path" :key="item.name">
          <template slot="profttl">
            <!--<svg-icon v-if="item.meta&&item.meta.icon" :icon-class="item.meta.icon"></svg-icon>-->
            <i v-if="item.meta&&item.meta.icon" :class="item.meta.icon"></i>
            <span v-if="item.meta&&item.meta.profttl" slot="profttl">{{ item.meta.profttl }}</span>
          </template>

          <template v-for="child in item.children">
            <template v-if="!child.is_hide">
              <sidebar-item :is-nest="true" class="nest-menu" v-if="child.children&&child.children.length>0"
                            :routes="[child]" :key="child.path"></sidebar-item>

              <router-link v-else :to="item.path+'/'+child.path" :key="child.name">
                <el-menu-item :index="item.path+'/'+child.path">
                  <!--<svg-icon v-if="child.meta&&child.meta.icon" :icon-class="child.meta.icon"></svg-icon>-->
                  <i v-if="child.meta&&child.meta.icon" :class="child.meta.icon"></i>
                  <span v-if="child.meta&&child.meta.profttl" slot="profttl">{{ child.meta.profttl }}</span>
                </el-menu-item>
              </router-link>
            </template>
          </template>
        </el-submenu>

      </template>
    </template>
  </div>
</template>

<script>
export default {
  name: 'SidebarItem',
  // data() {
  //   return {
  //     routes:this.$router.options.routes,
  //     isNest:false,
  //   }
  // },
  props: {
    routes: {
      type: Array
    },
    isNest: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    hasOneShowingChildren (children) {
      const showingChildren = children.filter(item => {
        return !item.is_hide
      })
      if (showingChildren.length === 1) {
        return true
      }
      return false
    }
  }
}
</script>
