import request from '@/utils/request'

/**
 * 查询标准科室单元数据
 * @param params
 * @returns {*}
 */
export function queryStandardDeptUnitData (params) {
  return request({
    url: '/costStandardDeptUnitController/queryStandardDeptUnitData',
    method: 'post',
    params: params
  })
}

/**
 * 改变有效标志
 * @param params
 * @returns {*}
 */
export function modifyDeptStatus (params) {
  return request({
    url: '/costStandardDeptUnitController/modifyDeptStatus',
    method: 'post',
    params: params
  })
}

/**
 * 修改科室分摊方式
 * @param data
 * @returns {*}
 */
export function modifyDeptApportionSchema (data) {
  return request({
    url: '/costStandardDeptUnitController/modifyDeptApportionSchema',
    method: 'post',
    data: data
  })
}

/**
 * 批量修改科室状态
 * @param params
 * @returns {*}
 */
export function batchModifyDeptStatus (data) {
  return request({
    url: '/costStandardDeptUnitController/batchModifyDeptStatus',
    method: 'post',
    data: data
  })
}

/**
 * 科室单元基础数据查询
 * @param params
 * @returns {*}
 */
export function deptUnitDataQuery (params) {
  return request({
    url: '/costDataUploadController/deptUnitDataQuery',
    method: 'post',
    params: params
  })
}

/**
 * 科室单元基础数据删除
 * @param params
 * @returns {*}
 */
export function deleteDeptUnitBasicData (params) {
  return request({
    url: '/costDataUploadController/deleteDeptUnitBasicData',
    method: 'post',
    params: params
  })
}

/**
 * 新增科室单元基础数据
 * @param params
 * @returns {*}
 */
export function addDeptUnitBasicData (params) {
  return request({
    url: '/costDataUploadController/addDeptUnitBasicData',
    method: 'post',
    params: params
  })
}

/**
 * 修改数据
 * @param params
 * @returns {*}
 */
export function modifyDeptUnitData (params) {
  return request({
    url: '/costDataUploadController/modifyDeptUnitData',
    method: 'post',
    params: params
  })
}

/**
 * 查询编码是否存在
 * @param params
 * @returns {*}
 */
export function deptUnitCodeExists (params) {
  return request({
    url: '/costDataUploadController/deptUnitCodeExists',
    method: 'post',
    params: params
  })
}

/**
 * 查询启用科室
 * @param params
 * @returns {*}
 */
export function deptUnitQuery (params) {
  return request({
    url: '/costDataUploadController/deptUnitQuery',
    method: 'post',
    params: params
  })
}
