<template>
  <div class="app-container" style="background-color: #000000;">

      <div class="head">
        <!--<div class="fs"><el-button class="fsbtn" @click="toggle()" v-if="!fullscreen" size="mini">全屏展示</el-button></div>-->
        <div class="queryDate" v-if="!fullscreen"><span>数据期号：{{ym}}</span></div>
        <!--<div class="queryDate1" v-if="fullscreen"><span>数据期号：{{this.cy_start_date.substring(0,7).replace("-","")}}</span></div>-->
        <div class="title">DRGs+DIP数据可视化平台</div>
        <div class="time"><span>{{dateYear}} {{dateWeek}} {{dateDay}}</span></div>
      </div>
      <div style="height:8%;">
        <el-row :gutter="10" style="height: 100%">
          <el-col :span="6" style="height: 100%">
            <div class="el-card is-always-shadow" style="height: 100%;width: 100%;background-color: #333399">
              <div style="height:30%;width:100%;padding-left:5px;margin-top:5px;font-size: 13px;font-weight: bold;color: #ffffff">全院入院病案数</div>
              <div style="height:70%;width:100%;padding-left:10px;font-size: 12px;font-weight: bold;display: flex;">
                <div style="width:40%;height:100%;display: flex;">
                  <div v-if="inHosMedicalRecordNum>=10000" class="number">
                    {{(inHosMedicalRecordNum/10000).toFixed(1)}}<span class="money">万</span>
                  </div>
                  <div v-else class="number">
                    {{inHosMedicalRecordNum}}
                    <span class="fen">例</span>
                  </div>
                </div>
                <div style="width:30%;display: flex;flex-direction: column">
                  <div style="margin:auto">
                    <div class="compare1">
                      同期:
                      <span class="compareNum">{{lastYearInHosMedicalRecordNum}}</span>
                    </div>
                    <div class="compare2">
                      上期:
                      <span class="compareNum">{{lastMonthInHosMedicalRecordNum}}</span>
                    </div>
                  </div>
                </div>
                <div style="width:30%;display: flex;flex-direction: column">
                  <div style="margin:auto">
                    <div class="compare1">
                      同比:
                      <span class="compareRateIncrease" v-if="((inHosMedicalRecordNum-lastYearInHosMedicalRecordNum)/(lastYearInHosMedicalRecordNum==0?1:lastYearInHosMedicalRecordNum)*100)>0">
                        {{((inHosMedicalRecordNum-lastYearInHosMedicalRecordNum)/(lastYearInHosMedicalRecordNum==0?1:lastYearInHosMedicalRecordNum)*100).toFixed(1)}}%↑
                      </span>
                      <span class="compareRateEqual" v-if="((inHosMedicalRecordNum-lastYearInHosMedicalRecordNum)/(lastYearInHosMedicalRecordNum==0?1:lastYearInHosMedicalRecordNum)*100)==0">
                        持平
                      </span>
                      <span class="compareRateDecrease" v-if="((inHosMedicalRecordNum-lastYearInHosMedicalRecordNum)/(lastYearInHosMedicalRecordNum==0?1:lastYearInHosMedicalRecordNum)*100)<0">
                        {{Math.abs((inHosMedicalRecordNum-lastYearInHosMedicalRecordNum)/(lastYearInHosMedicalRecordNum==0?1:lastYearInHosMedicalRecordNum)*100).toFixed(1)}}%↓
                      </span>
                    </div>
                    <div class="compare2">
                      环比:
                      <span class="compareRateIncrease" v-if="((inHosMedicalRecordNum-lastMonthInHosMedicalRecordNum)/(lastMonthInHosMedicalRecordNum==0?1:lastMonthInHosMedicalRecordNum)*100)>0">
                        {{((inHosMedicalRecordNum-lastMonthInHosMedicalRecordNum)/(lastMonthInHosMedicalRecordNum==0?1:lastMonthInHosMedicalRecordNum)*100).toFixed(1)}}%↑
                      </span>
                      <span class="compareRateEqual" v-if="((inHosMedicalRecordNum-lastMonthInHosMedicalRecordNum)/(lastMonthInHosMedicalRecordNum==0?1:lastMonthInHosMedicalRecordNum)*100)==0">
                        持平
                      </span>
                      <span class="compareRateIncrease" v-if="((inHosMedicalRecordNum-lastMonthInHosMedicalRecordNum)/(lastMonthInHosMedicalRecordNum==0?1:lastMonthInHosMedicalRecordNum)*100)<0">
                        {{Math.abs((inHosMedicalRecordNum-lastMonthInHosMedicalRecordNum)/(lastMonthInHosMedicalRecordNum==0?1:lastMonthInHosMedicalRecordNum)*100).toFixed(1)}}%↓
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>

          <el-col :span="6" style="height: 100%">
            <div class="el-card is-always-shadow" style="height: 100%;width: 100%;background-color: #333399">
              <div style="height:30%;width:100%;padding-left:5px;margin-top:5px;font-size: 13px;font-weight: bold;color: #ffffff">全院出院病案数</div>
              <div style="height:70%;width:100%;padding-left:10px;font-size: 12px;font-weight: bold;display: flex;">
                <div style="width:40%;height:100%;display: flex;">
                  <div v-if="outHosMedicalRecordNum>=10000" class="number">
                    {{(outHosMedicalRecordNum/10000).toFixed(1)}}<span class="wan">万</span>
                  </div>
                  <div v-else class="number">
                    {{outHosMedicalRecordNum}}
                    <span class="fen">例</span>
                  </div>
                </div>
                <div style="width:30%;display: flex;flex-direction: column">
                  <div style="margin:auto">
                    <div class="compare1">
                      同期:
                      <span class="compareNum">{{lastYearOutHosMedicalRecordNum}}</span>
                    </div>
                    <div class="compare2">
                      上期:
                      <span class="compareNum">{{lastMonthOutHosMedicalRecordNum}}</span>
                    </div>
                  </div>
                </div>
                <div style="width:30%;display: flex;flex-direction: column">
                  <div style="margin:auto">
                    <div class="compare1">
                      同比:
                      <span class="compareRateIncrease" v-if="((outHosMedicalRecordNum-lastYearOutHosMedicalRecordNum)/(lastYearOutHosMedicalRecordNum==0?1:lastYearOutHosMedicalRecordNum)*100)>0">
                        {{((outHosMedicalRecordNum-lastYearOutHosMedicalRecordNum)/(lastYearOutHosMedicalRecordNum==0?1:lastYearOutHosMedicalRecordNum)*100).toFixed(1)}}%↑
                      </span>
                      <span class="compareRateEqual" v-if="((outHosMedicalRecordNum-lastYearOutHosMedicalRecordNum)/(lastYearOutHosMedicalRecordNum==0?1:lastYearOutHosMedicalRecordNum)*100)==0">
                        持平
                      </span>
                      <span class="compareRateDecrease" v-if="((outHosMedicalRecordNum-lastYearOutHosMedicalRecordNum)/(lastYearOutHosMedicalRecordNum==0?1:lastYearOutHosMedicalRecordNum)*100)<0">
                        {{Math.abs((outHosMedicalRecordNum-lastYearOutHosMedicalRecordNum)/(lastYearOutHosMedicalRecordNum==0?1:lastYearOutHosMedicalRecordNum)*100).toFixed(1)}}%↓
                      </span>
                    </div>
                    <div class="compare2">
                      环比:
                      <span class="compareRateIncrease" v-if="((outHosMedicalRecordNum-lastMonthOutHosMedicalRecordNum)/(lastMonthOutHosMedicalRecordNum==0?1:lastMonthOutHosMedicalRecordNum)*100)>0">
                        {{((outHosMedicalRecordNum-lastMonthOutHosMedicalRecordNum)/(lastMonthOutHosMedicalRecordNum==0?1:lastMonthOutHosMedicalRecordNum)*100).toFixed(1)}}%↑
                      </span>
                      <span class="compareRateEqual" v-if="((outHosMedicalRecordNum-lastMonthOutHosMedicalRecordNum)/(lastMonthOutHosMedicalRecordNum==0?1:lastMonthOutHosMedicalRecordNum)*100)==0">
                        持平
                      </span>
                      <span class="compareRateIncrease" v-if="((outHosMedicalRecordNum-lastMonthOutHosMedicalRecordNum)/(lastMonthOutHosMedicalRecordNum==0?1:lastMonthOutHosMedicalRecordNum)*100)<0">
                        {{Math.abs((outHosMedicalRecordNum-lastMonthOutHosMedicalRecordNum)/(lastMonthOutHosMedicalRecordNum==0?1:lastMonthOutHosMedicalRecordNum)*100).toFixed(1)}}%↓
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="6" style="height: 100%">
            <div class="el-card is-always-shadow" style="height: 100%;width: 100%;background-color: #333399">
              <div style="height:30%;width:100%;padding-left:5px;margin-top:5px;font-size: 13px;font-weight: bold;color: #ffffff">全院入组病案数</div>
              <div style="height:70%;width:100%;padding-left:10px;font-size: 12px;font-weight: bold;display: flex;">
                <div style="width:40%;height:100%;display: flex;">
                  <div v-if="drgInGroupMedcasVal>=10000" class="number">
                    {{(drgInGroupMedcasVal/10000).toFixed(1)}}<span class="wan">万</span>
                  </div>
                  <div v-else class="number">
                    {{drgInGroupMedcasVal}}
                    <span class="fen">例</span>
                  </div>
                </div>
                <div style="width:30%;display: flex;flex-direction: column">
                  <div style="margin:auto">
                    <div class="compare1">
                      同期:
                      <span class="compareNum">{{lastYearInGroupNum}}</span>
                    </div>
                    <div class="compare2">
                      上期:
                      <span class="compareNum">{{lastMonthInGroupNum}}</span>
                    </div>
                  </div>
                </div>
                <div style="width:30%;display: flex;flex-direction: column">
                  <div style="margin:auto">
                    <div class="compare1">
                      同比:
                      <span class="compareRateIncrease" v-if="((inGroupNum-lastYearInGroupNum)/(lastYearInGroupNum==0?1:lastYearInGroupNum)*100)>0">
                        {{((inGroupNum-lastYearInGroupNum)/(lastYearInGroupNum==0?1:lastYearInGroupNum)*100).toFixed(1)}}%↑
                      </span>
                      <span class="compareRateEqual" v-if="((inGroupNum-lastYearInGroupNum)/(lastYearInGroupNum==0?1:lastYearInGroupNum)*100)==0">
                        持平
                      </span>
                      <span class="compareRateDecrease" v-if="((inGroupNum-lastYearInGroupNum)/(lastYearInGroupNum==0?1:lastYearInGroupNum)*100)<0">
                        {{Math.abs((inGroupNum-lastYearInGroupNum)/(lastYearInGroupNum==0?1:lastYearInGroupNum)*100).toFixed(1)}}%↓
                      </span>
                    </div>
                    <div class="compare2">
                      环比:
                      <span class="compareRateIncrease" v-if="((inGroupNum-lastMonthInGroupNum)/(lastMonthInGroupNum==0?1:lastMonthInGroupNum)*100)>0">
                        {{((inGroupNum-lastMonthInGroupNum)/(lastMonthInGroupNum==0?1:lastMonthInGroupNum)*100).toFixed(1)}}%↑
                      </span>
                      <span class="compareRateEqual" v-if="((inGroupNum-lastMonthInGroupNum)/(lastMonthInGroupNum==0?1:lastMonthInGroupNum)*100)==0">
                        持平
                      </span>
                      <span class="compareRateIncrease" v-if="((inGroupNum-lastMonthInGroupNum)/(lastMonthInGroupNum==0?1:lastMonthInGroupNum)*100)<0">
                        {{Math.abs((inGroupNum-lastMonthInGroupNum)/(lastMonthInGroupNum==0?1:lastMonthInGroupNum)*100).toFixed(1)}}%↓
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="6" style="height: 100%">
            <div class="el-card is-always-shadow" style="height: 100%;width: 100%;background-color: #333399">
              <div style="height:30%;width:100%;padding-left:5px;margin-top:5px;font-size: 13px;font-weight: bold;color: #ffffff">全院住院费用</div>
              <div style="height:70%;width:100%;padding-left:10px;font-size: 12px;font-weight: bold;display: flex;">
                <div style="width:40%;height:100%;display: flex;">
                  <div v-if="inHosCost>=10000" class="number">
                    {{(inHosCost/10000).toFixed(1)}}<span class="wan">万</span>
                  </div>
                  <div v-else class="number">
                    {{inHosCost}}
                    <span class="fen">元</span>
                  </div>
                </div>
                <div style="width:30%;display: flex;flex-direction: column">
                  <div style="margin:auto">
                    <div class="compare1">
                      同期:
                      <span v-if="lastYearInHosCost>=10000" class="compareNum">{{(lastYearInHosCost/10000).toFixed(1)}}万</span>
                      <span v-else class="compareNum">{{lastYearInHosCost}}</span>
                    </div>
                    <div class="compare2">
                      上期:
                      <span v-if="lastMonthInHosCost>=10000" class="compareNum">{{(lastMonthInHosCost/10000).toFixed(1)}}万</span>
                      <span v-else class="compareNum">{{lastMonthInHosCost}}</span>
                    </div>
                  </div>
                </div>
                <div style="width:30%;display: flex;flex-direction: column">
                  <div style="margin:auto">
                    <div class="compare1">
                      同比:
                      <span class="compareRateIncrease" v-if="((inHosCost-lastYearInHosCost)/(lastYearInHosCost==0?1:lastYearInHosCost)*100)>0">
                        {{((inHosCost-lastYearInHosCost)/(lastYearInHosCost==0?1:lastYearInHosCost)*100).toFixed(1)}}%↑
                      </span>
                      <span class="compareRateEqual" v-if="((inHosCost-lastYearInHosCost)/(lastYearInHosCost==0?1:lastYearInHosCost)*100)==0">
                        持平
                      </span>
                      <span class="compareRateDecrease" v-if="((inHosCost-lastYearInHosCost)/(lastYearInHosCost==0?1:lastYearInHosCost)*100)<0">
                        {{Math.abs((inHosCost-lastYearInHosCost)/(lastYearInHosCost==0?1:lastYearInHosCost)*100).toFixed(1)}}%↓
                      </span>
                    </div>
                    <div class="compare2">
                      环比:
                      <span class="compareRateIncrease" v-if="((inHosCost-lastMonthInHosCost)/(lastMonthInHosCost==0?1:lastMonthInHosCost)*100)>0">
                        {{((inHosCost-lastMonthInHosCost)/(lastMonthInHosCost==0?1:lastMonthInHosCost)*100).toFixed(1)}}%↑
                      </span>
                      <span class="compareRateEqual" v-if="((inHosCost-lastMonthInHosCost)/(lastMonthInHosCost==0?1:lastMonthInHosCost)*100)==0">
                        持平
                      </span>
                      <span class="compareRateIncrease" v-if="((inHosCost-lastMonthInHosCost)/(lastMonthInHosCost==0?1:lastMonthInHosCost)*100)<0">
                        {{Math.abs((inHosCost-lastMonthInHosCost)/(lastMonthInHosCost==0?1:lastMonthInHosCost)*100).toFixed(1)}}%↓
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div style="height:29%;" class="space" >
        <el-row :gutter="5" style="height: 100%">
          <el-col :span="7" style="height: 100%">
            <el-row :gutter="2" style="height: 100%;margin-bottom:2px;">
              <el-col :span="11" style="height: 100%">
                <div id="scoreEchartId" style="background-color:#1f2d3d;height: 100%;width: 100%"></div>
              </el-col>
              <el-col :span="13" style="height: 100%">
                <div style="height: 100%;width: 100%;color:#ffffff">
                  <el-row style="background-color:#1f2d3d;height: 14%;margin-bottom:1px;font-size:12px;">
                    <div style="margin-top:3%;margin-left:3%">
                      <i class="el-icon-star-off"></i>
                      完整性错误病例数：{{this.completeErrorNum}}
                    </div>
                  </el-row>
                  <el-row style="background-color:#1f2d3d;height: 14%;margin-bottom:1px;font-size:12px;">
                    <div style="margin-top:3%;margin-left:3%">
                      <i class="el-icon-paperclip"></i>
                      逻辑性错误病例数：{{this.logicErrorNum}}
                    </div>
                  </el-row>
                  <el-row style="background-color:#0066CC;height: 70%;margin-bottom:1px;font-size:12px;">
                      <div style="height:15%;margin-top:3%;text-align:center;font-size:14px;">
                        <i class="el-icon-warning-outline"></i>
                        质控评估
                      </div>
                      <div style="height:60%;padding:10px;font-size:13px;">{{qualityResult}}</div>
                      <div style="height:25%;font-size:12px;text-align:center;">
                        <i class="el-icon-date"></i>
                        数据期号：{{ym}}
                      </div>
                  </el-row>
                </div>
              </el-col>
            </el-row>

          </el-col>
          <el-col :span="10" style="height: 100%">
            <el-row style="height: 100%;width:100%;">
              <div id="drgIndex" style="height: 100%;width: 100%;background-color:#1f2d3d;"></div>
            </el-row>
          </el-col>
          <el-col :span="7" style="height: 100%">
            <el-row :gutter="2" style="height: 100%;margin-bottom:2px;">
              <el-col :span="11" style="height: 100%">
                <div id="inGroupRateEchartId"  style="background-color:#1f2d3d;height: 100%;width: 100%"></div>
              </el-col>
              <el-col :span="13" style="height: 100%">
                <div style="height: 100%;width: 100%;color:#ffffff">
                  <el-row style="background-color:#1f2d3d;height: 14%;margin-bottom:1px;font-size:12px;">
                    <div style="margin-top:4%;margin-left:4%">
                      <i class="el-icon-folder-checked"></i>
                      DRGs组数：{{this.drgNum}}
                    </div>
                  </el-row>
                  <el-row style="background-color:#1f2d3d;height: 14%;margin-bottom:1px;font-size:12px;">
                    <div style="margin-top:4%;margin-left:4%">
                      <i class="el-icon-coin"></i>
                      平均住院费用：{{this.avgCost}}
                    </div>
                  </el-row>
                  <el-row style="background-color:#1f2d3d;height: 14%;margin-bottom:1px;font-size:12px;">
                    <div style="margin-top:4%;margin-left:4%">
                      <i class="el-icon-menu"></i>
                      平均住院天数：{{this.avgDays}}
                    </div>
                  </el-row>
                  <el-row style="background-color:#1f2d3d;height: 14%;margin-bottom:1px;font-size:12px;">
                    <div style="margin-top:4%;margin-left:4%">
                      <i class="el-icon-umbrella"></i>
                      总权重：{{this.totalWeight}}
                    </div>
                  </el-row>
                  <el-row style="background-color:#1f2d3d;height: 14%;margin-bottom:1px;font-size:12px;">
                    <div style="margin-top:4%;margin-left:4%">
                      <i class="el-icon-scissors"></i>
                      CMI指数：{{this.cmi}}
                    </div>
                  </el-row>
                  <el-row style="background-color:#1f2d3d;height: 14%;margin-bottom:1px;font-size:12px;">
                    <div style="margin-top:4%;margin-left:4%">
                      <i class="el-icon-odometer"></i>
                      时间消耗指数：{{this.timeIndex}}
                    </div>
                  </el-row>
                  <el-row style="background-color:#1f2d3d;height: 14%;margin-bottom:1px;font-size:12px;">
                    <div style="margin-top:4%;margin-left:4%">
                      <i class="el-icon-stopwatch"></i>
                      费用消耗指数：{{this.costIndex}}
                    </div>
                  </el-row>
                </div>
              </el-col>
            </el-row>

          </el-col>
        </el-row>
      </div>
      <div style="height:29%;" class="space">
        <el-row :gutter="5" style="height: 100%">
          <el-col :span="7" style="height: 100%">
            <el-row style="height: 100%;width:100%;">
              <div id="disease" style="height: 100%;width: 100%;background-color:#1f2d3d;"></div>
            </el-row>
          </el-col>
          <el-col :span="10" style="height: 100%">
            <el-row style="height: 100%;width:100%;">
              <!--<div id="operativeInfo" style="height: 100%;width: 100%;background-color:#1f2d3d;"></div>-->
              <div id="doctorInfo" style="height: 100%;width: 100%;background-color:#1f2d3d;"></div>
            </el-row>
          </el-col>

          <el-col :span="7" style="height: 100%">
            <el-row style="height: 100%;width:100%;">
              <div style="position:absolute;z-index:1000;margin:auto">
                <el-radio-group v-model="dataType" size="mini" @change="changeSelectDataType">
                  <el-radio-button :label="2">病案首页</el-radio-button>
                  <el-radio-button :label="1">结算清单</el-radio-button>
                </el-radio-group>
              </div>
              <div id="medicalTreatmentCost" style="height: 100%;width: 100%;background-color:#1f2d3d;"></div>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <div style="height:30%;" class="space" >
        <el-row :gutter="5" style="height: 100%">
          <el-col :span="7" style="height: 100%">
            <el-row style="height: 100%;width:100%;">
              <div id="dipGroupNumTop10" style="height: 100%;width: 100%;background-color:#1f2d3d;"></div>
            </el-row>
          </el-col>
          <el-col :span="10" style="height: 100%">
            <div style="flex:5px;height: 100%">
              <el-table ref="diseaseCostTable"
                        size="mini"
                        stripe
                        height="100%"
                        :data="list"
                        style="width: 100%;font-size:9px"
                        :cell-style="cellHeadClass"
                        border>
                <el-table-column label="姓名"  align="left" width="45" :show-overflow-tooltip="true" >
                  <template slot-scope="scope">{{scope.row.name | formatIsEmpty}}</template>
                </el-table-column>
                <el-table-column label="DIP编码"  align="left" width="50" :show-overflow-tooltip="true" >
                  <template slot-scope="scope">{{scope.row.dipCodg | formatIsEmpty}}</template>
                </el-table-column>
                <el-table-column label="DIP名称"  align="left" :show-overflow-tooltip="true" >
                  <template slot-scope="scope">{{scope.row.dipName | formatIsEmpty}}</template>
                </el-table-column>
                <el-table-column label="住院总费用"  align="center" width="65" :show-overflow-tooltip="true">
                  <template slot-scope="scope">{{scope.row.inHosTotalCost | formatIsEmpty}}</template>
                </el-table-column>
                <el-table-column label="住院日"  align="center" width="50" :show-overflow-tooltip="true">
                  <template slot-scope="scope">{{scope.row.inHosDays | formatIsEmpty}}</template>
                </el-table-column>
                <el-table-column label="控费预警"  align="left" :show-overflow-tooltip="true" width="65">
                  <template slot-scope="scope">
                    <div v-if="scope.row.costWarn==null||scope.row.costWarn==''||scope.row.costWarn=='无法预警'||scope.row.costWarn=='暂无参考标准'">
                      {{scope.row.costWarn | formatIsEmpty}}
                    </div>
                    <div v-else-if="scope.row.costWarn=='费用偏低'">
                    <span class="lowCost">
                       {{scope.row.costWarn | formatIsEmpty}}
                    </span>
                    </div>
                    <div v-else-if="scope.row.costWarn=='平稳区间'">
                    <span class="stableCost">
                       {{scope.row.costWarn | formatIsEmpty}}
                    </span>
                    </div>
                    <div v-else-if="scope.row.costWarn=='费用偏高'">
                    <span class="highCost">
                       {{scope.row.costWarn | formatIsEmpty}}
                    </span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="平均住院费用"  align="center" width="80" :show-overflow-tooltip="true">
                  <template slot-scope="scope">{{scope.row.inHosAvgCost | formatIsEmpty}}</template>
                </el-table-column>
                <el-table-column label="平均住院日"  align="center" width="70" :show-overflow-tooltip="true">
                  <template slot-scope="scope">{{scope.row.inHosAvgDays | formatIsEmpty}}</template>
                </el-table-column>

              </el-table>
            </div>
          </el-col>
          <el-col :span="7" style="height: 100%">
            <el-row style="height: 100%;width:100%;">
              <div id="dipGroupCostTop10" style="height: 100%;width: 100%;background-color:#1f2d3d;"></div>
            </el-row>
          </el-col>
        </el-row>
      </div>

  </div>

</template>
<script>
import { queryDataIsuue } from '@/api/common/drgCommon'
import { formatDate } from '@/utils/date'
import { getDrgIndexInfo, getCountInfo, getMedicalTreatmentCostInfo, getDoctorDrgIndexInfo, getOperativeInfo, getDiseaseInfo, getIndexTop10DipInfo, getDipCostInfo } from '@/api/firstPage'
import echarts from 'echarts'

export default {
  name: 'firstPage',
  data () {
    return {
      fullscreen: false,
      dateDay: null,
      dateYear: null,
      dateWeek: null,
      weekday: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
      cy_start_date: null,
      cy_end_date: null,
      ym: '',
      dataType: '2', // 默认病案首页
      timer1: '',
      timer2: '',
      timer3: '',
      timer4: '',
      submitListQuery: {},
      inHosMedicalRecordNum: 0,
      lastYearInHosMedicalRecordNum: 0,
      lastMonthInHosMedicalRecordNum: 0,
      outHosMedicalRecordNum: 0,
      lastYearOutHosMedicalRecordNum: 0,
      lastMonthOutHosMedicalRecordNum: 0,
      drgInGroupMedcasVal: 0,
      lastYearInGroupNum: 0,
      lastMonthInGroupNum: 0,
      inHosCost: 0,
      lastYearInHosCost: 0,
      lastMonthInHosCost: 0,
      completeErrorNum: 0,
      logicErrorNum: 0,
      refer_sco: 0,
      drgNum: 0,
      inGroupRate: 0,
      avgCost: 0,
      avgDays: 0,
      totalWeight: 0,
      cmi: 0,
      timeIndex: 0,
      costIndex: 0,
      tableHeight: 0,
      list: null,
      qualityResult: null
    }
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  created () {
    // 获取数据查询时间
    this.getDataIsuue()
    this.$somms.generateUserInfo()
  },
  // 动态调整表格高度
  mounted: function () {
    // 获取时间
    this.timeFn()
    // 表格高度动态调整
    // this.$nextTick(function () {
    //   //this.$refs.diseaseCostTable.$el.offsetTop：表格距离浏览器的高度
    //   //35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
    //   this.tableHeight = window.innerHeight*0.3 - this.$refs.diseaseCostTable.$el.offsetTop-20;
    //   // 监听窗口大小变化
    //   let self = this;
    //   window.onresize = function() {
    //     self.tableHeight = window.innerHeight*0.3 - self.$refs.diseaseCostTable.$el.offsetTop-20;
    //   }
    // });
    // 表格自动滚动
    // 拿到表格挂载后的真实DOM
    const table = this.$refs.diseaseCostTable
    // 拿到表格中承载数据的div元素
    const divData = table.bodyWrapper
    // 拿到元素后，对元素进行定时增加距离顶部距离，实现滚动效果(此配置为每100毫秒移动1像素)
    this.timer4 = setInterval(() => {
      // 元素自增距离顶部2像素
      divData.scrollTop += 2
      // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)
      if (divData.clientHeight + divData.scrollTop == divData.scrollHeight) {
        // 重置table距离顶部距离
        divData.scrollTop = 0
      }
    }, 200)
  },
  beforeDestroy () {
    clearInterval(this.timer1)
    clearInterval(this.timer2)
    clearInterval(this.timer3)
    clearInterval(this.timer4)
  },
  methods: {
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.ym = response.data.cy_start_date.substring(0, 7)
        // 查询数据
        this.getCount()
        this.getDisease()
        this.getIndexTop10Dip()
        this.getDipCost()
        let selected = [
          { DRGs组数: true, CMI指数: false, 总权重: false, 时间消耗指数: false, 费用消耗指数: false },
          { DRGs组数: false, CMI指数: true, 总权重: false, 时间消耗指数: false, 费用消耗指数: false },
          { DRGs组数: false, CMI指数: false, 总权重: true, 时间消耗指数: false, 费用消耗指数: false },
          { DRGs组数: false, CMI指数: false, 总权重: false, 时间消耗指数: true, 费用消耗指数: false },
          { DRGs组数: false, CMI指数: false, 总权重: false, 时间消耗指数: false, 费用消耗指数: true }
        ]
        this.getDrgIndex(selected[0])
        this.getMedicalTreatmentCost()
        let i = 0
        this.timer2 = setInterval(() => {
          this.getDrgIndex(selected[i % 5])
          if ((i + 1) % 2 == 1) {
            this.dataType = '1'
          } else if ((i + 1) % 2 == 0) {
            this.dataType = '2'
          }
          this.getMedicalTreatmentCost()
          i++
        }, 10000)
        this.getDoctorDrgIndex(selected[0])
        let j = 0
        this.timer3 = setInterval(() => {
          this.getDoctorDrgIndex(selected[j % 5])
          j++
        }, 10000)
      })
    },
    toggle () {
      this.fullscreen = !this.fullscreen
    },
    timeFn () {
      this.timer1 = setInterval(() => {
        this.dateDay = formatDate(new Date(), 'hh:mm:ss')
        this.dateYear = formatDate(new Date(), 'yyyy-MM-dd')
        this.dateWeek = this.weekday[new Date().getDay()]
      }, 1000)
    },
    getCount () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.cy_start_date
      this.submitListQuery.cy_end_date = this.cy_end_date
      getCountInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        this.inHosMedicalRecordNum = response.data.inHosMedicalRecordNum
        this.lastYearInHosMedicalRecordNum = response.data.lastYearInHosMedicalRecordNum
        this.lastMonthInHosMedicalRecordNum = response.data.lastMonthInHosMedicalRecordNum
        this.outHosMedicalRecordNum = response.data.outHosMedicalRecordNum
        this.lastYearOutHosMedicalRecordNum = response.data.lastYearOutHosMedicalRecordNum
        this.lastMonthOutHosMedicalRecordNum = response.data.lastMonthOutHosMedicalRecordNum
        this.drgInGroupMedcasVal = response.data.drgInGroupMedcasVal
        this.lastYearInGroupNum = response.data.lastYearInGroupNum
        this.lastMonthInGroupNum = response.data.lastMonthInGroupNum
        this.inHosCost = response.data.inHosCost
        this.lastYearInHosCost = response.data.lastYearInHosCost
        this.lastMonthInHosCost = response.data.lastMonthInHosCost
        this.completeErrorNum = response.data.completeErrorNum
        this.logicErrorNum = response.data.logicErrorNum
        this.refer_sco = response.data.refer_sco
        this.drgNum = response.data.drgNum
        this.inGroupRate = response.data.inGroupRate
        this.avgDays = response.data.avgDays
        this.avgCost = response.data.avgCost
        this.totalWeight = response.data.totalWeight
        this.cmi = response.data.cmi
        this.timeIndex = response.data.timeIndex
        this.costIndex = response.data.costIndex
        // 病案质量得分
        this.ringEchart('scoreEchartId', Number(response.data.refer_sco), response.data.refer_sco + '分', '病案质量得分')
        if (Number(response.data.refer_sco) < 90) {
          this.qualityResult = '本月病案质量欠佳，为保证入组的准确性，建议进行相应调正后再重新上报！'
        } else if (Number(response.data.refer_sco) < 98) {
          this.qualityResult = '本月病案质量良好，存在部分问题需待验证和调整！'
        } else if (Number(response.data.refer_sco) < 100) {
          this.qualityResult = '本月病案质量优良，可以进行相关数据分析，排除病案错误即可上报！'
        } else if (Number(response.data.refer_sco) == 100) {
          this.qualityResult = '本月病案无质控问题，可以作为标准病案进行上报！'
        }
        // 入组率
        this.ringEchart('inGroupRateEchartId', Number(response.data.inGroupRate), response.data.inGroupRate + '%', 'DRGs病组入组率')
      })
    },
    getDrgIndex (selected) {
      this.submitListQuery.cy_start_date = this.cy_start_date
      this.submitListQuery.cy_end_date = this.cy_end_date
      getDrgIndexInfo(this.submitListQuery).then(response => {
        this.drgIndexEchart(response.data, selected)
      })
    },
    changeSelectDataType (value) {
      if (value == 1) {
        this.dataType = '1'
      } else if (value == 2) {
        this.dataType = '2'
      }
      this.getMedicalTreatmentCost()
    },
    getMedicalTreatmentCost () {
      this.submitListQuery.cy_start_date = this.cy_start_date
      this.submitListQuery.cy_end_date = this.cy_end_date
      this.submitListQuery.dataType = this.dataType
      getMedicalTreatmentCostInfo(this.submitListQuery).then(response => {
        let result = response.data
        let barData = [] // 费用数据
        let xAxisData = [] // 费用名称
        let costRate = [] // 费用占比
        let lineData = [] // 人次数据
        let patientRate = [] // 人次占比
        let sumfee = 0
        let totalPatient = 0
        if (result && result.length > 2) {
          sumfee = Number(result[result.length - 2].costValue) // 总费用
          totalPatient = Number(result[result.length - 1].patients) // 总人次
          for (let i = 0; i < result.length - 2; i++) {
            barData.push(Number(result[i].costValue))
            xAxisData.push(result[i].costName)
            lineData.push(Number(result[i].patients))
            costRate.push((Number(result[i].costValue) / sumfee * 100).toFixed(2)) // 费用占比
            patientRate.push((Number(result[i].patients) / totalPatient * 100).toFixed(2)) // 人次占比
          }
        }
        let legendData = ['费用', '人次']
        let profttl = '病案首页费用/人次'
        if (this.dataType == '1') {
          profttl = '医保结算清单费用/人次'
        }
        this.getMedicalTreatmentCostEchart(barData, profttl, xAxisData, lineData, costRate, patientRate, legendData)
      })
    },
    // 获取Drgs各个指标前10的医生Drgs指标信息
    getDoctorDrgIndex (selected) {
      this.submitListQuery.cy_start_date = this.cy_start_date
      this.submitListQuery.cy_end_date = this.cy_end_date
      getDoctorDrgIndexInfo(this.submitListQuery).then(response => {
        let result = response.data
        let titleData = '住院医生DRGs各指标前TOP' + ((result.length < 20) ? (result.length) : 20) + '排名'
        this.getDoctorIndexEchart(result, titleData, selected)
      })
    },
    getOperative () {
      this.submitListQuery.cy_start_date = this.cy_start_date
      this.submitListQuery.cy_end_date = this.cy_end_date
      getOperativeInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        let result = response.data
        let thisIssue = []
        let lastYearIssue = []
        let lastMonthIssue = []
        let standardOprData = []
        if (result) {
          for (let i = 0; i < result.length; i++) {
            thisIssue.push({ name: result[i].name, value: result[i].issueNum })
            lastYearIssue.push({ name: result[i].name, value: result[i].lastYearNum })
            lastMonthIssue.push({ name: result[i].name, value: result[i].lastMonthNum })
            standardOprData.push({ name: result[i].name, value: result[i].standardNum })
          }
        }
        this.getOperativeEchart(thisIssue, lastYearIssue, lastMonthIssue, standardOprData)
      })
    },
    getDisease () {
      this.submitListQuery.cy_start_date = this.cy_start_date
      this.submitListQuery.cy_end_date = this.cy_end_date
      getDiseaseInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        let result = response.data
        let yData = [] // 疾病名称
        let seriesData = [] // 人次
        let indexRate = [] // 人次占比
        if (result) {
          for (let i = 0; i < result.length - 1; i++) {
            yData.push(result[i].name)
            seriesData.push(result[i].value)
          }
        }
        this.getDownBarEchart('disease', seriesData, yData, '病种覆盖情况', 'disease')
      })
    },
    getDipCost () {
      this.submitListQuery.cy_start_date = this.cy_start_date
      this.submitListQuery.cy_end_date = this.cy_end_date
      getDipCostInfo(this.submitListQuery).then(response => {
        this.list = response.data
      })
    },
    getIndexTop10Dip () {
      this.submitListQuery.cy_start_date = this.cy_start_date
      this.submitListQuery.cy_end_date = this.cy_end_date
      getIndexTop10DipInfo(this.submitListQuery).then(response => {
        let result = response.data
        // 按照人次排序
        result.sort(function (o1, o2) {
          return Number(o2.outHosMedicalRecordNum) - Number(o1.outHosMedicalRecordNum)
        })
        // 截取前10
        let newRes = []
        if (result) {
          for (let i = 0; i < (result.length < 10 ? result.length : 10); i++) {
            newRes.push(result[i])
          }
        }
        // 按照人次升序
        newRes.sort(function (o1, o2) {
          return Number(o1.outHosMedicalRecordNum) - Number(o2.outHosMedicalRecordNum)
        })
        let yData = [] // DRGs编码和名称
        let seriesData = [] // 指标
        if (result) {
          for (let i = 0; i < newRes.length; i++) {
            yData.push(newRes[i].dipCodeAndName)
            seriesData.push(newRes[i].outHosMedicalRecordNum)
          }
        }
        this.getDownBarEchart('dipGroupNumTop10', seriesData, yData, 'DIP分组人次TOP10', 'dip')
        // 按照平均费用排序
        result.sort(function (o1, o2) {
          return Number(o2.avgCost) - Number(o1.avgCost)
        })
        // 截取前10
        let newRes1 = []
        if (result) {
          for (let i = 0; i < (result.length < 10 ? result.length : 10); i++) {
            newRes1.push(result[i])
          }
        }
        // 按照人次升序
        newRes1.sort(function (o1, o2) {
          return Number(o1.avgCost) - Number(o2.avgCost)
        })
        let yData1 = [] // DIP编码和名称
        let seriesData1 = [] // 指标
        if (result) {
          for (let i = 0; i < newRes1.length; i++) {
            yData1.push(newRes1[i].dipCodeAndName)
            seriesData1.push(newRes1[i].avgCost)
          }
        }
        this.getDownBarEchart('dipGroupCostTop10', seriesData1, yData1, 'DIP分组平均住院费用TOP10', 'dip')
      })
    },
    // 环状图，病案质量得分、入组率
    ringEchart (id, ringData, showData, titleData) {
      let option = {
        title: [
          { text: titleData, x: 'center', top: 10, textStyle: { fontFamily: 'Microsoft YaHei', color: '#ffffff', fontSize: 13 } }
        ],
        graphic: {
          type: 'text',
          left: 'center',
          top: '50%',
          style: {
            text: showData,
            textAlign: 'center',
            fontSize: 18,
            fill: '#ffffff',
            width: 30,
            height: 30
          }
        },
        series: [
          {
            type: 'pie',
            center: ['50%', '55%'],
            radius: ['62%', '75%'],
            labelLine: {
              show: false
            },
            data: [
              {
                value: 100 - ringData,
                itemStyle: {
                  color: '#666666'
                }
              },
              {
                value: ringData,
                itemStyle: {
                  normal: {// 颜色渐变
                    color: new echarts.graphic.LinearGradient(
                      0, 0, 0, 1,
                      [
                        { offset: 0, color: '#409EFF' },
                        { offset: 0.5, color: '#0066CC' },
                        { offset: 1, color: '#006699' }
                      ]
                    )
                  }
                }
              }
            ]
          }
        ]
      }
      let ringEct = echarts.getInstanceByDom(document.getElementById(id))
      if (ringEct) {
        ringEct.clear()
      } else {
        ringEct = echarts.init(document.getElementById(id))
      }
      ringEct.setOption(option)
      window.addEventListener('resize', () => {
        ringEct.resize()
      })
      return ringEct
    },
    drgIndexEchart (drgIndexData, selected) {
      let colors = ['#288AF2', '#4BA2FF', '#0BA2B3', '#26B9B5', '#00CC00']
      let lineData = [
        { name: 'DRGs组数', data: [] },
        { name: 'CMI指数', data: [] },
        { name: '总权重', data: [] },
        { name: '时间消耗指数', data: [] },
        { name: '费用消耗指数', data: [] }]
      let xAxis = []
      for (let i = 0; i < (drgIndexData.length < 12 ? drgIndexData.length : 12); i++) {
        xAxis.push(drgIndexData[i].month)
        lineData[0].data.push(Number(drgIndexData[i].drgNum))
        lineData[1].data.push(Number(drgIndexData[i].cmi))
        lineData[2].data.push(Number(drgIndexData[i].totalWeight))
        lineData[3].data.push(Number(drgIndexData[i].timeIndex))
        lineData[4].data.push(Number(drgIndexData[i].costIndex))
      }
      let yAxis = [// 两个y轴
        {
          type: 'value',
          splitLine: { lineStyle: { color: '#F1F2F7' } },
          axisLabel: {
            fontSize: 9,
            color: '#ffffff'
          }
        }
        // {type : "value",position:"right",name:"权重",
        //   nameTextStyle : {
        //     fontSize: 9,
        //   },
        //   axisLabel: {
        //     fontSize:9,
        //     color:"#ffffff",
        //     formatter:function(value){
        //       if(value>=10000){
        //         return (Number(value)/10000).toFixed(1)+"万";
        //       }else{
        //         return Number(value).toFixed(1);
        //       }
        //     }
        //   }
        // },
      ]
      let seriesData = []
      for (let i = 0; i < lineData.length; i++) {
        let seris = {
          type: 'bar',
          name: lineData[i].name,
          data: lineData[i].data,
          symbol: 'circle',
          symbolSize: '6',
          label: {
            show: true,
            fontSize: 10,
            position: 'top',
            formatter: function (param) {
              if (param.value >= 10000) {
                return (Number(param.value) / 10000).toFixed(1) + '万'
              } else {
                return Number(param.value).toFixed(2)
              }
            }
          },
          lineStyle: {
            color: colors[i],
            width: '2'
          },
          itemStyle: {
            color: colors[i],
            emphasis: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [
                  { offset: 0, color: '#71C8B1' }, // 柱图高亮渐变色
                  { offset: 0.7, color: '#44C0C1' }, // 柱图高亮渐变色
                  { offset: 1, color: '#06B5D7' } // 柱图高亮渐变色
                ]
              )
            }
          }
        }
        // if(lineData[i].name == "总权重"){
        //   seris["yAxisIndex"] = 1;
        // }else{
        seris['yAxisIndex'] = 0
        // }
        seriesData.push(seris)
      }
      let option = {
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let str = []
            str.push(params[0].name)
            params.forEach(function (item) {
              str.push(item.marker + ' ' + item.seriesName + ' : ' + item.value)
            })
            return str.join('</br>')
          }
        },
        legend: {
          left: '10',
          top: '0',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 9, color: '#ffffff' },
          selected: selected,
          selectedMode: 'single' // 设置单选多选模式
        },
        xAxis: {
          type: 'category',
          data: xAxis,
          axisLabel: {
            interval: 0,
            fontSize: 9,
            color: '#ffffff',
            fontFamily: 'Microsoft YaHei'
          },
          axisLine: { lineStyle: { color: '#E9E9E9', width: 1 } },
          axisTick: { show: false }
        },
        yAxis: yAxis,
        series: seriesData,
        grid: {
          left: '30',
          top: '30',
          bottom: '20',
          right: '10'
        }
      }
      let drgIndex = echarts.getInstanceByDom(document.getElementById('drgIndex'))
      if (drgIndex) {
        drgIndex.clear()
      } else {
        drgIndex = echarts.init(document.getElementById('drgIndex'))
      }
      drgIndex.setOption(option)
      window.addEventListener('resize', () => {
        drgIndex.resize()
      })
      return drgIndex
    },
    getMedicalTreatmentCostEchart (barData, titleData, xAxisData, lineData, costRate, patientRate, legendData) {
      let option = {
        title: {
          text: titleData,
          x: 'center',
          textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 11, color: '#ffffff' }
        },
        tooltip: {
          trigger: 'item',
          formatter: function (param) {
            return '费用名称：' + xAxisData[param.dataIndex] + '</br>' +
                '费用额度：' + '：' + barData[param.dataIndex] + '元' + '</br>' +
                '费用占比：' + costRate[param.dataIndex] + '%' + '</br>' +
                '人次：' + '：' + lineData[param.dataIndex] + '</br>' +
                '人次占比：' + patientRate[param.dataIndex] + '%'
          }
        },
        legend: [{
          data: legendData,
          top: '0',
          right: '0',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 9, color: '#ffffff' }
        }],
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            interval: 0,
            fontSize: 8,
            color: '#ffffff',
            rotate: 20,
            formatter: function (value) {
              return (value.length >= 4 ? (value.slice(0, 4) + '..') : value)
            }
          }
        },

        yAxis: [
          { type: 'value',
            position: 'left',
            name: '单位：元',
            nameTextStyle: {
              fontSize: 9
            },
            axisLabel: {
              fontSize: 9,
              color: '#ffffff',
              formatter: function (value) {
                if (value > 10000) {
                  return (Number(value) / 10000).toFixed(1) + '万'
                } else {
                  return Number(value).toFixed(1)
                }
              }
            }
          },
          { type: 'value',
            position: 'right',
            name: '单位：人',
            splitLine: { show: false },
            nameTextStyle: {
              fontSize: 9
            },
            axisLabel: {
              fontSize: 9,
              color: '#ffffff'
            }
          }
        ],
        series: [{
          name: legendData[0],
          data: barData,
          type: 'bar',
          yAxisIndex: 0,
          label: {
            color: '#06B5D7',
            show: true,
            fontSize: 7,
            position: 'top',
            formatter: function (param) {
              if (param.value >= 10000) {
                return (Number(param.value) / 10000).toFixed(1) + '万'
              } else {
                return Number(param.value).toFixed(2)
              }
            }
          },
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [
                  { offset: 0, color: '#06B5D7' }, // 柱图渐变色
                  { offset: 0.5, color: '#44C0C1' }, // 柱图渐变色
                  { offset: 1, color: '#71C8B1' } // 柱图渐变色
                ]
              )
            }
          }
        },
        {
          name: legendData[1],
          data: lineData,
          type: 'line',
          symbol: 'circle',
          color: '#ffffff',
          yAxisIndex: 1,
          label: {
            show: true,
            position: 'top',
            fontSize: 10,
            formatter: function (param) {
              return Number(param.value)
            }
          }
        }],
        grid: {
          top: '35',
          bottom: '25',
          left: '45',
          right: '30'
        }
      }
      let medicalTreatmentCostChart = echarts.getInstanceByDom(document.getElementById('medicalTreatmentCost'))
      if (medicalTreatmentCostChart) {
        medicalTreatmentCostChart.clear()
      } else {
        medicalTreatmentCostChart = echarts.init(document.getElementById('medicalTreatmentCost'))
      }
      medicalTreatmentCostChart.setOption(option)
      window.addEventListener('resize', () => {
        medicalTreatmentCostChart.resize()
      })
      return medicalTreatmentCostChart
    },
    getDoctorIndexEchart (doctorDrgIndexData, titleData, selected) {
      let colors = ['#288AF2', '#4BA2FF', '#0BA2B3', '#26B9B5', '#00CC00']
      // 先按照指标排序
      if (selected.DRGs组数 == true) {
        doctorDrgIndexData.sort(function (o1, o2) {
          return Number(o2.drgNum) - Number(o1.drgNum)
        })
      } else if (selected.CMI指数 == true) {
        doctorDrgIndexData.sort(function (o1, o2) {
          return Number(o2.cmi) - Number(o1.cmi)
        })
      } else if (selected.总权重 == true) {
        doctorDrgIndexData.sort(function (o1, o2) {
          return Number(o2.totalWeight) - Number(o1.totalWeight)
        })
      } else if (selected.时间消耗指数 == true) {
        doctorDrgIndexData.sort(function (o1, o2) {
          return Number(o2.timeIndex) - Number(o1.timeIndex)
        })
      } else if (selected.费用消耗指数 == true) {
        doctorDrgIndexData.sort(function (o1, o2) {
          return Number(o2.costIndex) - Number(o1.costIndex)
        })
      }
      let lineData = [
        { name: 'DRGs组数', data: [] },
        { name: 'CMI指数', data: [] },
        { name: '总权重', data: [] },
        { name: '时间消耗指数', data: [] },
        { name: '费用消耗指数', data: [] }]
      let xAxis = []
      for (let i = 0; i < (doctorDrgIndexData.length < 20 ? doctorDrgIndexData.length : 20); i++) {
        xAxis.push(doctorDrgIndexData[i].drName)
        lineData[0].data.push(Number(doctorDrgIndexData[i].drgNum))
        lineData[1].data.push(Number(doctorDrgIndexData[i].cmi))
        lineData[2].data.push(Number(doctorDrgIndexData[i].totalWeight))
        lineData[3].data.push(Number(doctorDrgIndexData[i].timeIndex))
        lineData[4].data.push(Number(doctorDrgIndexData[i].costIndex))
      }
      let yAxis = [// 两个y轴
        {
          type: 'value',
          splitLine: { lineStyle: { color: '#F1F2F7' } },
          axisLabel: {
            fontSize: 9,
            color: '#ffffff'
          }
        }
        // {type : "value",position:"right",name:"权重",
        //   nameTextStyle : {
        //     fontSize: 9,
        //   },
        //   axisLabel: {
        //     fontSize:9,
        //     color:"#ffffff",
        //     formatter:function(value){
        //       if(value>=10000){
        //         return (Number(value)/10000).toFixed(1)+"万";
        //       }else{
        //         return Number(value).toFixed(1);
        //       }
        //     }
        //   }
        // },
      ]
      let seriesData = []
      for (let i = 0; i < lineData.length; i++) {
        let seris = {
          type: 'bar',
          name: lineData[i].name,
          data: lineData[i].data,
          symbol: 'circle',
          symbolSize: '6',
          label: {
            show: true,
            fontSize: 8,
            position: 'top',
            formatter: function (param) {
              if (param.value >= 10000) {
                return (Number(param.value) / 10000).toFixed(1) + '万'
              } else {
                return Number(param.value).toFixed(2)
              }
            }
          },
          lineStyle: {
            color: colors[i],
            width: '2'
          },
          itemStyle: {
            color: colors[i]
          }
        }
        // if(lineData[i].name == "总权重"){
        //   seris["yAxisIndex"] = 1;
        // }else{
        seris['yAxisIndex'] = 0
        // }
        seriesData.push(seris)
      }
      let option = {
        title: {
          text: titleData,
          x: 'center',
          textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 11, color: '#ffffff' }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let str = []
            str.push(params[0].name)
            params.forEach(function (item) {
              str.push(item.marker + ' ' + item.seriesName + ' : ' + item.value)
            })
            return str.join('</br>')
          }
        },
        legend: {
          left: '0',
          top: '14',
          itemWidth: 8,
          itemHeight: 8,
          textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 8, color: '#ffffff' },
          formatter: function (value) {
            return (value.length >= 4 ? (value.slice(0, 4) + '..') : value)
          },
          selected: selected,
          selectedMode: 'single' // 设置单选多选模式
        },
        xAxis: {
          type: 'category',
          data: xAxis,
          axisLabel: {
            interval: 0,
            fontSize: 8.5,
            rotate: 17,
            color: '#ffffff',
            fontFamily: 'Microsoft YaHei'
          },
          axisLine: { lineStyle: { color: '#E9E9E9', width: 1 } },
          axisTick: { show: false }
        },
        yAxis: yAxis,
        series: seriesData,
        grid: {
          left: '30',
          top: '35',
          bottom: '23',
          right: '10'
        }
      }
      let doctorInfo = echarts.getInstanceByDom(document.getElementById('doctorInfo'))
      if (doctorInfo) {
        doctorInfo.clear()
      } else {
        doctorInfo = echarts.init(document.getElementById('doctorInfo'))
      }
      doctorInfo.setOption(option)
      window.addEventListener('resize', () => {
        doctorInfo.resize()
      })
      return doctorInfo
    },
    getOperativeEchart (thisIssue, lastYearIssue, lastMonthIssue, standardOprData) {
      let colors = ['#288AF2', '#4BA2FF', '#0BA2B3', '#26B9B5', '#00CC66']
      let option = {
        title: [
          { text: '手术例数占比', left: 'left', top: 0, textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 11, color: '#ffffff' } },
          { text: '本期数据', left: '10%', top: '11%', textStyle: { fontSize: 10, color: '#ffffff' } },
          { text: '一级手术：' + thisIssue[0].value, left: '9%', top: '70%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '二级手术：' + thisIssue[1].value, left: '9%', top: '75%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '三级手术：' + thisIssue[2].value, left: '9%', top: '80%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '四级手术：' + thisIssue[3].value, left: '9%', top: '85%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '其他手术：' + thisIssue[4].value, left: '9%', top: '90%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '同期数据', left: '33%', top: '11%', textStyle: { fontSize: 10, color: '#ffffff' } },
          { text: '一级手术：' + lastYearIssue[0].value, left: '32%', top: '70%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '二级手术：' + lastYearIssue[1].value, left: '32%', top: '75%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '三级手术：' + lastYearIssue[2].value, left: '32%', top: '80%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '四级手术：' + lastYearIssue[3].value, left: '32%', top: '85%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '其他手术：' + lastYearIssue[4].value, left: '32%', top: '90%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '上期数据', left: '56%', top: '11%', textStyle: { fontSize: 10, color: '#ffffff' } },
          { text: '一级手术：' + lastMonthIssue[0].value, left: '55%', top: '70%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '二级手术：' + lastMonthIssue[1].value, left: '55%', top: '75%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '三级手术：' + lastMonthIssue[2].value, left: '55%', top: '80%', textStyle: { fontSize: 10, color: '#ffffff' } },
          { text: '四级手术：' + lastMonthIssue[3].value, left: '55%', top: '85%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '其他手术：' + lastMonthIssue[4].value, left: '55%', top: '90%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '标准手术', left: '79%', top: '11%', textStyle: { fontSize: 10, color: '#ffffff' } },
          { text: '一级手术：' + standardOprData[0].value, left: '78%', top: '70%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '二级手术：' + standardOprData[1].value, left: '78%', top: '75%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '三级手术：' + standardOprData[2].value, left: '78%', top: '80%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '四级手术：' + standardOprData[3].value, left: '78%', top: '85%', textStyle: { fontSize: 9, color: '#ffffff' } },
          { text: '其他手术：' + standardOprData[4].value, left: '78%', top: '90%', textStyle: { fontSize: 9, color: '#ffffff' } }
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          icon: 'circle',
          top: '0',
          right: '0',
          itemWidth: 9,
          itemHeight: 9,
          textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 9, color: '#ffffff' }

        },
        series: [
          {
            type: 'pie',
            radius: '50%', // 设置饼图大小
            center: ['15%', '46%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'inside',
                formatter: '{b} \n {d}%',
                fontFamily: 'Microsoft YaHei',
                fontSize: 9
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % 5]
                }
              }
            },
            data: thisIssue
          },
          {
            type: 'pie',
            radius: '50%', // 设置饼图大小
            center: ['38%', '46%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'inside',
                formatter: '{b} \n {d}%',
                fontFamily: 'Microsoft YaHei',
                fontSize: 10
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % 5]
                }
              }
            },
            data: lastYearIssue
          },
          {
            type: 'pie',
            radius: '50%', // 设置饼图大小
            center: ['61%', '46%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'inside',
                formatter: '{b} \n {d}%',
                fontFamily: 'Microsoft YaHei',
                fontSize: 10
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % 5]
                }
              }
            },
            data: lastMonthIssue
          },
          {
            type: 'pie',
            radius: '50%', // 设置饼图大小
            center: ['84%', '46%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'inside',
                formatter: '{b} \n {d}%',
                fontFamily: 'Microsoft YaHei',
                fontSize: 10
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % 5]
                }
              }
            },
            data: standardOprData
          }
        ]
      }

      let operativeInfo = echarts.getInstanceByDom(document.getElementById('operativeInfo'))
      if (operativeInfo) {
        operativeInfo.clear()
      } else {
        operativeInfo = echarts.init(document.getElementById('operativeInfo'))
      }
      operativeInfo.setOption(option)
      window.addEventListener('resize', () => {
        operativeInfo.resize()
      })
      return operativeInfo
    },
    getDownBarEchart (echartId, seriesData, yData, titleData, flag) {
      let option = {
        title: {
          text: titleData,
          x: 'center',
          textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 11, color: '#ffffff' }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: yData,
          axisLabel: {
            fontSize: 9,
            color: '#ffffff',
            fontFamily: 'Microsoft YaHei',
            formatter: function (value) {
              return (value.length > 17 ? (value.slice(0, 17) + '..') : value)
            }
          }
        },
        series: [
          {
            type: 'bar',
            data: seriesData,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                  offset: 0,
                  color: '#409EFF' // 0% 处的颜色
                }, {
                  offset: 0.5,
                  color: '#0066CC' // 60% 处的颜色
                }, {
                  offset: 1,
                  color: '#006699' // 100% 处的颜色
                }], false),
                label: {
                  show: true,
                  position: 'right',
                  textStyle: {
                    color: '#ffffff',
                    fontSize: 9
                  }
                }
              }
            }
          }
        ],
        grid: {
          left: '40%',
          top: '20',
          bottom: '5',
          right: '25'
        }
      }
      let echartRes = echarts.getInstanceByDom(document.getElementById(echartId))
      if (echartRes) {
        echartRes.clear()
      } else {
        echartRes = echarts.init(document.getElementById(echartId))
      }
      echartRes.setOption(option)
      window.addEventListener('resize', () => {
        echartRes.resize()
      })
      return echartRes
    },
    tableHeadClasstableHeadClass ({ row, column, rowIndex, columnIndex }) {
      return 'background:#333366;color:#fff;'
    },
    cellHeadClass ({ row, column, rowIndex, columnIndex }) {
      return 'background:#1f2d3d;color:#fff;'
    }
  }
}
</script>
<style scoped>
  .number{
    margin-top:4px;
    font-size:23px;
    font-weight: bold;
    font-family:Microsoft YaHei;
    color:#ffffff
  }
  .fen{
    font-size: 8px;
  }
  .wan{
    font-size: 8px;
  }
  .compare1{
    font-weight: normal;
    font-size: 8px;
    color:#ffffff;
    width:100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
  }
  .compare2{
    font-weight: normal;
    font-size: 8px;
    color:#ffffff;
    margin-top:3px;
    width:100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
  }
  .compareNum{
    font-size: 8px;
    font-weight: bold;
    margin-left:4px;
  }
  .compareRateIncrease{
    font-size: 8px;
    font-weight: bold;
    color:#ffffff;
    margin-left:5px;
  }
  .compareRateEqual{
    font-size: 8px;
    font-weight: bold;
    color:#ffffff;
    margin-left:5px;
  }
  .compareRateDecrease{
    font-size: 8px;
    font-weight: bold;
    color:#ffffff;
    margin-left:5px;
  }
  .el-card{
    border:1px solid #000000;
  }
  /deep/ .el-table{
    font-size: 5px;
  }
  /deep/ .el-table th{
    font-size: 5px;
    background: #000000;
    border-color:#000000;
    color: #ffffff;
    font-weight: 600;
  }
  /deep/ .el-table th>.cell{
    padding-left:1px;
    padding-right:0px;
  }
  /deep/ .el-table .cell{
    padding-left:1px;
    padding-right:0px;
    border-color:#000000;
  }

   /deep/ .el-table td,
   /deep/ .el-table th.is-leaf,
   /deep/ .el-table--border,
   /deep/ .el-table--group {
     border-color: #333366;
   }
  /deep/ .el-table--border::after,
  /deep/ .el-table--group::after,
  /deep/ .el-table::before {
    background-color: #333366;
  }
  /*滚动条的宽度*/
  /deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: #000000;/* 滚动条的颜色 */
    border-radius: 7px;/* 滚动条的宽 */
  }
  /deep/ .el-table__body tr:hover>td {
    background-color:#000000!important;
  }

  /*!**隐藏滚动条*!*/
  /*::-webkit-scrollbar {*/
    /*width: 0 !important;*/
  /*}*/
  /*::-webkit-scrollbar {*/
    /*width: 0 !important;height: 0;*/
  /*}*/
  /*动态高度调整，增加菜单横条的高度，app-main减去了菜单横条的高度，所以这个需要加上*/
  /*.app-container{*/
  /*  height:calc(100% + 28px);*/
  /*}*/
  .fullscreen{overflow:hidden;}/*隐藏全屏自带的滚动条*/
  /*头部样式*/
  .head{height:calc(2% + 15px);
    /*background: url(../../assets/images/screenView/head_bg.png) no-repeat center center; */
    background-size: 100% 100%; position: relative}
  .fs{position:absolute;}
  .fsbtn{background-color: #000000;color:#ffffff;font-size: 8px;}
  .el-button--mini, .el-button--mini.is-round{padding:5px 8px}
  .queryDate{position:absolute;left:10px;top:0px;}
  .queryDate span{color:rgba(255,255,255,.7); font-size: 13px;}
  .queryDate1{position:absolute;left:10px;top:0px;}
  .queryDate1 span{color:rgba(255,255,255,.7); font-size: 13px;}
  .head .title{height:100%;display:flex;align-items: center;justify-content: center;color:#fff; text-align: center; font-size: 18px;}
  .time{ position:absolute; right:10px; top:2px;}
  .time span{color:rgba(255,255,255,.7); font-size: 13px;}
  /**/
  /deep/ .el-radio-button--mini .el-radio-button__inner{padding:3px 5px;font-size: 8px;border-radius:2px;}

  /*自定义样式*/
  .el-icon-caret-bottom{
    color:#FF0000;
  }
  .el-icon-caret-top{
    color:#00CC00;
  }
  .lowCost{
    font-size: 10px;
    font-weight: bold;
    color:#A9A9A9;
  }
  .stableCost{
    font-size: 10px;
    font-weight: bold;
    color:#00CC00;
  }
  .highCost{
    font-size: 10px;
    font-weight: bold;
    color:#FF0000;
  }
  .space {
    margin-top: 5px;
  }
</style>
