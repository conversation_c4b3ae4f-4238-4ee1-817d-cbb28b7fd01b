<template>
  <div>
    <el-row type="flex" justify="space-between" v-for="(data, index) in layoutData" :key="index">
      <el-col :span="6" v-for="(item, itemIndex) in data.data" :key="itemIndex">
        <el-form-item :label="item.label"
                      :prop="item.prop"
                      v-if="item.show">
          <form-items :item="item" :form="form" @change="change(item.prop)"/>
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import formItems from './formItems'
export default {
  name: 'layout',
  props: {
    layoutData: {
      type: Array
    },
    form: {
      type: Object
    }
  },
  components: {
    'form-items': formItems
  },
  data: () => ({

  }),
  methods: {
    change (propName) {
      let flag = false
      if (['dateRange', 'deptCode'].includes(propName)) {
        flag = true
      }
      this.$emit('change', flag)
    }
  }
}
</script>

<style scoped>
/deep/ .el-form-item__content{
  width: 75%;
}
/deep/ .el-form-item{
  width: 100%;
}
</style>
