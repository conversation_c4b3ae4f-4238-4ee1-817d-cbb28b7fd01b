<template>
    <el-form :inline="true" :model="value" size="mini" label-position="right" label-width="90px" style="margin-top:-18px;">
      <el-header style="height: 30px;width:168px;line-height: 30px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 10px">
        <span style="margin-left:10px;">医疗住院相关费用</span>
      </el-header>
      <el-row :gutter="0">
        <el-col :span="6" v-show="value.showJsqd">
          <el-form-item label="业务流水号">
            <el-input  v-model="value.somHiInvyBasInfo.d35" placeholder="业务流水号" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showJsqd">
          <el-form-item label="票据代码">
            <el-input  v-model="value.somHiInvyBasInfo.d38" placeholder="票据代码" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showJsqd">
          <el-form-item label="票据号">
            <el-input  v-model="value.somHiInvyBasInfo.d39" placeholder="票据号" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showJsqd">
            <el-form-item label="结算时间">
              <el-date-picker
                v-model="value.settleDateRange"
                type="daterange"
                size="mini"
                unlink-panels
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions">
              </el-date-picker>
            </el-form-item>
        </el-col>

        <div class="table-container" style="margin-top:-10px;" v-show="value.showJsqd">
          <el-table ref="medicalCostTable"
                    size="mini"
                    stripe
                    :data="value.busMedicalCostList"
                    max-height="300"
                    style="width: 100%"
                    border>
            <el-table-column
              label="序号"
              type="index"
              width="50">
            </el-table-column>
            <el-table-column label="项目名称"  align="center">
              <template slot-scope="scope">{{scope.row.medChrgItemname | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="金额"  align="center">
              <template slot-scope="scope">{{scope.row.amt | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="甲类"  align="center" >
              <template slot-scope="scope">{{scope.row.claa | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="乙类" width="100" align="center" >
              <template slot-scope="scope">{{scope.row.clab | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="自费"  align="center" >
              <template slot-scope="scope">{{scope.row.ownpay | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="其他"  align="center" >
              <template slot-scope="scope">{{scope.row.oth | formatIsEmpty}}</template>
            </el-table-column>
          </el-table>
        </div>

        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="住院总费用">
            <el-input id="d01" v-model="value.somHiInvyBasInfo.d01" type="number" min="0" placeholder="住院总费用" class="formInput"></el-input>
          </el-form-item>
          <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.d01}}</div>
          <div v-if="value.logicErrorsMap!=null" class="errorNote">{{value.logicErrorsMap.d01}}</div>
          <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.d01}}</div>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="自付金额" >
            <el-input id="d09" v-model="value.somHiInvyBasInfo.d09" type="number" min="0" placeholder="自付金额" class="formInput"></el-input>
            <div v-if="value.completeErrorsMap!=null" class="errorNote">{{value.completeErrorsMap.d09}}</div>
            <div v-if="value.logicErrorsMap!=null" class="errorNote">{{value.logicErrorsMap.d09}}</div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.d09}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-header v-show="value.showBa" style="height: 15px;width:125px;line-height: 15px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 0px;font-size:12px;">
        <span style="margin-left:10px;">综合医疗服务费</span>
      </el-header>
      <el-row :gutter="0">
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:28px;">一般医疗服务费</span>
              <el-input v-model="value.somHiInvyBasInfo.d11"  type="number" min="0"  style="width:57%" placeholder="请输入一般医疗服务费" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:28px;">一般治疗操作费</span>
              <el-input v-model="value.somHiInvyBasInfo.d12"  type="number" min="0"  style="width:57%" placeholder="请输入一般治疗操作费" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="护理费">
            <el-input  v-model="value.somHiInvyBasInfo.d13" type="number" min="0" placeholder="请输入护理费" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:13px;">综合医疗服务类其他费用</span>
              <el-input id="d14" v-model="value.somHiInvyBasInfo.d14"  type="number" min="0"  style="width:57%" placeholder="综合医疗服务类其他费用" class="formInput"></el-input>
              <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.d14}}</div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-header v-show="value.showBa" style="height: 15px;width:125px;line-height: 15px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 0px;font-size:12px;">
        <span style="margin-left:35px;">诊断费</span>
      </el-header>
      <el-row :gutter="0">
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="病理诊断费">
            <el-input  v-model="value.somHiInvyBasInfo.d15" type="number" min="0" placeholder="病理诊断费" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="实验室诊断费">
            <el-input  v-model="value.somHiInvyBasInfo.d16" type="number" min="0" placeholder="实验室诊断费" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="影像学诊断费">
            <el-input  v-model="value.somHiInvyBasInfo.d17" type="number" min="0" placeholder="影像学诊断费" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item >
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:28px;">临床诊断项目费</span>
              <el-input  v-model="value.somHiInvyBasInfo.d18"  type="number" min="0"  style="width:57%" placeholder="临床诊断项目费" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-header v-show="value.showBa" style="height: 15px;width:125px;line-height: 15px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 0px;font-size:12px;">
        <span style="margin-left:35px;">治疗费</span>
      </el-header>
      <el-row :gutter="0">
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:28px;">非手术治疗项目费</span>
              <el-input id="d19" v-model="value.somHiInvyBasInfo.d19"  type="number" min="0"  style="width:57%" placeholder="非手术治疗项目费" class="formInput"></el-input>
              <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.d19}}</div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:14px;">其中：临床物理治疗费</span>
              <el-input id="d19x01" v-model="value.somHiInvyBasInfo.d19x01"  type="number" min="0"  style="width:57%" placeholder="临床物理治疗费" class="formInput"></el-input>
              <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.d19x01}}</div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="手术治疗费">
            <el-input id="d20" v-model="value.somHiInvyBasInfo.d20" type="number" min="0" placeholder="手术治疗费" class="formInput"></el-input>
            <div v-if="value.logicErrorsMap!=null" class="errorNote">{{value.logicErrorsMap.d20}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="其中：麻醉费">
            <el-input id="d20x01" v-model="value.somHiInvyBasInfo.d20x01" type="number" min="0" placeholder="麻醉费" class="formInput"></el-input>
            <div v-if="value.logicErrorsMap!=null" class="errorNote">{{value.logicErrorsMap.d20x01}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="其中：手术费">
            <el-input id="d20x02" v-model="value.somHiInvyBasInfo.d20x02" type="number" min="0" placeholder="手术费" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="中医治疗费">
            <el-input id="d22" v-model="value.somHiInvyBasInfo.d22" type="number" min="0" placeholder="中医治疗费" class="formInput"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-header v-show="value.showBa" style="height: 15px;width:125px;line-height: 15px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 0px;font-size:12px;">
        <span style="margin-left:35px;">药品费</span>
      </el-header>
      <el-row :gutter="0">
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="西药费">
            <el-input  v-model="value.somHiInvyBasInfo.d23" type="number" min="0" placeholder="西药费" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:28px;">其中：抗菌药物费</span>
              <el-input id="d23x01" v-model="value.somHiInvyBasInfo.d23x01"  type="number" min="0"  style="width:57%" placeholder="抗菌药物费" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="中成药费">
            <el-input  v-model="value.somHiInvyBasInfo.d24" type="number" min="0" placeholder="中成药费" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="中草药费">
            <el-input id="d25" v-model="value.somHiInvyBasInfo.d25" type="number" min="0" placeholder="中草药费" class="formInput"></el-input>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.d25}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-header v-show="value.showBa" style="height: 15px;width:125px;line-height: 15px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 0px;font-size:12px;">
        <span style="margin-left:10px;">血液血制品类费</span>
      </el-header>
      <el-row :gutter="0">
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="血费">
            <el-input id="d26" v-model="value.somHiInvyBasInfo.d26" type="number" min="0" placeholder="血费" class="formInput"></el-input>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.d26}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:28px;">白蛋白类制品费</span>
              <el-input id="d27" v-model="value.somHiInvyBasInfo.d27"  type="number" min="0"  style="width:57%" placeholder="白蛋白类制品费" class="formInput"></el-input>
            </div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.d27}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:28px;">球蛋白类制品费</span>
              <el-input v-model="value.somHiInvyBasInfo.d28"  type="number" min="0"  style="width:57%" placeholder="球蛋白类制品费" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:28px;">凝血因子类制品费</span>
              <el-input v-model="value.somHiInvyBasInfo.d29"  type="number" min="0"  style="width:57%" placeholder="凝血因子类制品费" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:28px;">细胞因子类制品费</span>
              <el-input v-model="value.somHiInvyBasInfo.d30"  type="number" min="0"  style="width:57%" placeholder="细胞因子类制品费" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-header v-show="value.showBa" style="height: 15px;width:125px;line-height: 15px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 0px;font-size:12px;">
        <span style="margin-left:35px;">耗材费</span>
      </el-header>
      <el-row :gutter="0">
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:13px;">检查用一次性医用材料费</span>
              <el-input id="d31" v-model="value.somHiInvyBasInfo.d31"  type="number" min="0"  style="width:57%" placeholder="检查用一次性医用材料费" class="formInput"></el-input>
            </div>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.d31}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:13px;">治疗用一次性医用材料费</span>
              <el-input v-model="value.somHiInvyBasInfo.d32"  type="number" min="0"  style="width:57%" placeholder="治疗用一次性医用材料费" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-show="value.showBa">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:13px;">手术用一次性医用材料费</span>
              <el-input v-model="value.somHiInvyBasInfo.d33"  type="number" min="0"  style="width:57%" placeholder="手术用一次性医用材料费" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-header v-show="value.showBa" style="height: 15px;width:125px;line-height: 15px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 0px;font-size:12px;">
        <span style="margin-left:35px;">康复费</span>
      </el-header>
      <el-row :gutter="0">
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="康复费">
            <el-input id="d21" v-model="value.somHiInvyBasInfo.d21" type="number" min="0" placeholder="康复费" class="formInput"></el-input>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.d21}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-header v-show="value.showZy&&value.showBa" style="height: 15px;width:125px;line-height: 15px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 0px;font-size:12px;">
        <span style="margin-left:35px;">中医其他</span>
      </el-header>
      <el-row :gutter="0" v-show="value.showZy">
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="中医其他">
            <el-input id="d64" v-model="value.somHiInvyBasInfo.d64" type="number" min="0" placeholder="中医其他" class="formInput"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-header v-show="value.showBa" style="height: 15px;width:125px;line-height: 15px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 0px;font-size:12px;">
        <span style="margin-left:35px;">其他费</span>
      </el-header>
      <el-row :gutter="0">
        <el-col :span="6" v-show="value.showBa">
          <el-form-item label="其他费">
            <el-input id="d34" v-model="value.somHiInvyBasInfo.d34" type="number" min="0" placeholder="请输入其他费" class="formInput"></el-input>
            <div v-if="value.scoreErrorsMap!=null" class="errorNote">{{value.scoreErrorsMap.d34}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-show="value.showZy&&value.showBa">
        <el-col :span="6">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:28px;">中医辨证论治费</span>
              <el-input v-model="value.somHiInvyBasInfo.d61"  type="number" min="0"  style="width:57%" placeholder="中医辨证论治费" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:13px;">中医辨证论治会诊费</span>
              <el-input v-model="value.somHiInvyBasInfo.d62"  type="number" min="0"  style="width:57%" placeholder="中医辨证论治会诊费" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 11px;color: #606266;line-height:13px;">中医类(中医和名族医医疗服务)中医诊断</span>
              <el-input v-model="value.somHiInvyBasInfo.d63"  type="number" min="0"  style="width:57%" placeholder="中医类(中医和名族医医疗服务)中医诊断" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="中医其他">
            <el-input  v-model="value.somHiInvyBasInfo.d64" type="number" min="0" placeholder="中医其他" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:28px;">中医特殊调配加工</span>
              <el-input v-model="value.somHiInvyBasInfo.d65"  type="number" min="0"  style="width:57%" placeholder="中医特殊调配加工" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="辨证施膳">
            <el-input  v-model="value.somHiInvyBasInfo.d66" type="number" min="0" placeholder="辨证施膳" class="formInput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <div style="display: flex;">
              <span style="width:40%;font-size: 13px;color: #606266;line-height:13px;">医疗机构中药制剂费</span>
              <el-input v-model="value.somHiInvyBasInfo.d67"  type="number" min="0"  style="width:57%" placeholder="医疗机构中药制剂费" class="formInput"></el-input>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-header style="height: 30px;width:168px;line-height: 30px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 10px" v-show="value.showJsqd">
        <span style="margin-left:10px;">医保支付相关费用</span>
      </el-header>
      <el-row v-show="value.showJsqd">
        <el-form-item label="医保支付方式">
          <el-radio-group v-model="value.somHiInvyBasInfo.d58">
            <el-radio label="1" border size="mini" >按项目</el-radio>
            <el-radio label="2" border size="mini" >单病种</el-radio>
            <el-radio label="3" border size="mini" >按病种分值</el-radio>
            <el-radio label="4" border size="mini" >疾病诊断相关分组（DRG）</el-radio>
            <el-radio label="5" border size="mini" >按床日</el-radio>
            <el-radio label="6" border size="mini" >按人头</el-radio>
            <el-radio label="9" border size="mini" >其他</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-row>
      <el-row v-show="value.showJsqd">
        <el-col :span="12">
          <div class="table-container" style="margin-top:-10px;" v-show="value.showJsqd">
            <el-table ref="fundPayTable"
                      size="mini"
                      stripe
                      :data="value.busFundPayList"
                      max-height="242"
                      style="width: 100%"
                      border>
              <el-table-column label="基金支付信息"  align="center" >
                <el-table-column label="序号" type="index" width="50">
                </el-table-column>
                <el-table-column label="基金支付类型"  align="center">
                  <template slot-scope="scope">{{scope.row.fundPayType | formatIsEmpty}}</template>
                </el-table-column>
                <el-table-column label="金额"  align="center">
                  <template slot-scope="scope">{{scope.row.fundPayAmt | formatIsEmpty}}</template>
                </el-table-column>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="table-container" style="margin-top:-10px">
            <el-table
              size="mini"
              :data="value.zftableData"
              style="width: 100%"
              :span-method="objectSpanMethod"
              border>
              <el-table-column label="个人支付信息"  align="center" >
                <el-table-column  label="个人支付" prop="col1" align="center" width="100"></el-table-column>
                <el-table-column  label="个人支付类型" prop="col2" align="center"></el-table-column>
                <el-table-column  label="金额" prop="col3" align="center"></el-table-column>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="6" v-show="false">
          <el-form-item>
            <!--隐藏框用于触发查看不到字段的隐藏和展示-->
            <el-input  v-model="value.showFlag"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
</template>

<script>
export default {
  name: 'SettleListYlfyInfo',
  props: {
    value: Object
  },
  data () {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  methods: {
    dateChangeCysj (val) {
      if (val) {
        this.d36 = val[0]
        this.d37 = val[1]
      } else {
        this.d36 = null
        this.d37 = null
      }
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      // 表列合并
      if (columnIndex === 0) {
        if (rowIndex % 5 === 0) {
          return {
            rowspan: 5,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    }
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  }
}
</script>

<style scoped>
  .formInput{
    width:170px;
  }
  .note{
    font-size: 13px;
    line-height: 28px;
    margin-left: 30px;
    font-weight: bold;
  }
  .errorNote{
    color:red;
    font-size: 10px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width:170px;
  }

  /*时间样式设置*/
  /deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner{
    width: 165px;
  }
</style>
