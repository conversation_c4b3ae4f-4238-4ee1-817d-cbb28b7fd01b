import request from '@/utils/request'
export function fetchList (params) {
  return request({
    url: '/ppsInGroupStatistic/getList',
    method: 'post',
    params: params
  })
}

export function getCount (params) {
  return request({
    url: '/ppsInGroupStatistic/getCount',
    method: 'post',
    params: params
  })
}

export function getInGroupIndex (params) {
  return request({
    url: '/ppsInGroupStatistic/getInGroupIndex',
    method: 'post',
    params: params
  })
}

export function getNotInPpsGroup (params) {
  return request({
    url: '/ppsInGroupStatistic/getNotInPpsGroup',
    method: 'post',
    params: params
  })
}
