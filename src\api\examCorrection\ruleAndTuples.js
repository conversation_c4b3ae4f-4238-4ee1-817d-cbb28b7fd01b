import request from '@/utils/request'

/**
 *
 * @param params
 * @returns {*}
 */
export function queryTupleList(params) {
  return request({
    url: '/examCorrectionController/queryTupleList',
    method: 'post',
    params: params,
  })
}

export function getRuleList(params) {
  return request({
    url: '/examCorrectionController/getRuleList',
    method: 'post',
    params: params
  })
}

export function getResultListByPatient(params) {
  return request({
    url: '/examCorrectionResultController/getResultListByPatient',
    method: 'post',
    params: params
  })
}

export function getViolationsSummary(params) {
  return request({
    url: '/examCorrectionResultController/getViolationsSummary',
    method: 'post',
    params: params
  })
}

export function getResultListByMouth(params) {
  return request({
    url: '/examCorrectionResultController/getResultListByMouth',
    method: 'post',
    params: params
  })
}

export function getResultListByDept(params) {
  return request({
    url: '/examCorrectionResultController/getResultListByDept',
    method: 'post',
    params: params
  })
}


export function getResultListByDoctor(params) {
  return request({
    url: '/examCorrectionResultController/getResultListByDoctor',
    method: 'post',
    params: params
  })
}


export function fetchViolationTupleDetails(params) {
  return request({
    url: '/examCorrectionResultController/getTupleDetailByRuleCode',
    method: 'post',
    params: params
  })
}

// 跳转明细详情页面
export function queryVioalDetailList(params) {
  return request({
    url: '/examCorrectionResultController/queryVioalDetailList',
    method: 'post',
    params: params
  })
}

// 跳转患者违规详情树状
export function fetchViolationRulesByUniqueId(params) {
  return request({
    url: '/examCorrectionResultController/fetchViolationRulesByUniqueId',
    method: 'post',
    params: params
  })
}

// 跳转患者违规详情树状
export function filteredChargeItemsByUniqueId(params) {
  return request({
    url: '/examCorrectionResultController/filteredChargeItemsByUniqueId',
    method: 'post',
    params: params
  })
}

export function fetchPatientInfoByUniqueId(params) {
  return request({
    url: '/examCorrectionResultController/fetchPatientInfoByUniqueId',
    method: 'post',
    params: params
  })
}

export function getViolationsSummaryByMouth(params) {
  return request({
    url: '/examCorrectionResultController/getViolationsSummaryByMouth',
    method: 'post',
    params: params
  })
}

export function getTupleTypeList(params) {
  return request({
    url: '/examCorrectionController/getTupleTypeList',
    method: 'post',
    params: params
  })
}

export function saveNewRule(params) {
  return request({
    url: '/examCorrectionController/saveNewRule',
    method: 'post',
    params: params
  })
}

/**
 * 获取违规患者明细
 * @param params
 * @returns {*}
 */
export function getViolationItemDetail(params) {
  return request({
    url: '/examCorrectionResultController/getViolationItemDetail',
    method: 'post',
    params: params
  })
}

/**
 * 获取违规项目统计
 * @param params
 * @returns {*}
 */
export function getViolationItemSummary(params) {
  return request({
    url: '/examCorrectionResultController/getViolationItemSummary',
    method: 'post',
    params: params
  })
}


/**
 * 获取违规明细统计
 * @param params
 * @returns {*}
 */
export function getViolationDetailSummary(params) {
  return request({
    url: '/examCorrectionResultController/getViolationDetailSummary',
    method: 'post',
    params: params
  })
}

/**
 * 获取违规明细详情
 * @param {*} params 
 * @returns 
 */
export function queryDataGroup(params) {
  return request({
    url: '/examCorrectionController/queryDataGroup',
    method: 'post',
    params: params
  })
}
