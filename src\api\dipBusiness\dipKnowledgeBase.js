import request from '@/utils/request'
export function fetchList (params) {
  return request({
    url: '/dipKnowledgeBase/getList',
    method: 'post',
    params: params
  })
}

export function getCountByCoverRate (params) {
  return request({
    url: '/dipKnowledgeBase/getCountByCoverRate',
    method: 'post',
    params: params
  })
}

export function getCountByMonth (params) {
  return request({
    url: '/dipKnowledgeBase/getCountByMonth',
    method: 'post',
    params: params
  })
}
