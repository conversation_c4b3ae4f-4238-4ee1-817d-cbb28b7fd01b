<template>
  <div class="app-container">
    <div class="container-fluid">
      <div class="free-box">
        <div style="width: 100%;margin: 10px 10px 0 0">
          <span class="demonstration">默认</span>
          <el-date-picker
            style="float: right"
            @change="issueChange"
            value-format="yyyy-MM"
            placeholder="选择期号"
            v-model="ym"
            type="monthrange"
            range-separator="-"
            start-placeholder="开始月份"
            end-placeholder="结束月份">
          </el-date-picker>
        </div>
      </div>
      <!--第一行-->
      <div class="free-box">
        <div class="col-xl-3">
          <div class="card">
            <div class="card-body">
              <div class="avatar-sm float-right">
                  <span class="avatar-title bg-soft-primary rounded-circle">
                  </span>
              </div>
              <h6 class="mt-0">总费用</h6>
              <h3 class="my-3">{{ (parseFloat(summaryData.sumfee) / 10000).toFixed(2) + '/万' }}</h3>
              <div class="card-child">
                <div>
                  <span class="badge badge-soft-primary mr-1">职工</span>
                  <span class="text-muted">{{ (parseFloat(summaryData.staffTotalCost) / 10000).toFixed(2) + '/万' }}</span>
                </div>
                <div>
                  <span class="badge badge-soft-primary mr-1">居民</span>
                  <span class="text-muted">{{ (parseFloat(summaryData.residentTotalCost) / 10000).toFixed(2) + '/万' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-xl-3">
          <div class="card">
            <div class="card-body">
              <div class="avatar-sm float-right">
                  <span class="avatar-title bg-soft-primary rounded-circle">
                      <i class="bx bx-dollar-circle m-0 h3 text-primary"></i>
                  </span>
              </div>
              <h6 class="mt-0">统筹费用</h6>
              <h3 class="my-3">{{ (parseFloat(summaryData.overallTotalCost) / 10000).toFixed(2) + '/万' }}</h3>
              <div class="card-child">
                <div>
                  <span class="badge badge-soft-primary mr-1">职工</span>
                  <span class="text-muted">{{ (parseFloat(summaryData.staffOverallTotalCost) / 10000).toFixed(2) + '/万' }}</span>
                </div>
                <div>
                  <span class="badge badge-soft-primary mr-1">居民</span>
                  <span class="text-muted">{{ (parseFloat(summaryData.residentOverallTotalCost) / 10000).toFixed(2) + '/万' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-xl-3">
          <div class="card">
            <div class="card-body">
              <div class="avatar-sm float-right">
                  <span class="avatar-title bg-soft-primary rounded-circle">
                      <i class="bx bx-analyse m-0 h3 text-primary"></i>
                  </span>
              </div>
              <h6 class="mt-0">病组统筹费用</h6>
              <h3 class="my-3">{{ (parseFloat(summaryData.groupOverallTocalCost) / 10000).toFixed(2) + '/万' }}</h3>
              <div class="card-child">
                <div>
                  <span class="badge badge-soft-primary mr-1">职工</span>
                  <span class="text-muted">{{ (parseFloat(summaryData.staffGroupOverallTotalCost) / 10000).toFixed(2) + '/万' }}</span>
                </div>
                <div>
                  <span class="badge badge-soft-primary mr-1">居民</span>
                  <span class="text-muted">{{ (parseFloat(summaryData.residentGroupOverallTotalCost) / 10000).toFixed(2) + '/万' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-xl-3">
          <div class="card">
            <div class="card-body">
              <div class="avatar-sm float-right">
                  <span class="avatar-title bg-soft-primary rounded-circle">
                      <i class="bx bx-basket m-0 h3 text-primary"></i>
                  </span>
              </div>
              <h6 class="mt-0">支付差异</h6>
              <h3 class="my-3">{{ (parseFloat(summaryData.diffTocalCost) / 10000).toFixed(2) + '/万' }}</h3>
              <div class="card-child">
                <div>
                  <span class="badge badge-soft-primary mr-1">职工</span>
                  <span class="text-muted">{{ (parseFloat(summaryData.staffDiffTotalCost) / 10000).toFixed(2) + '/万' }}</span>
                </div>
                <div>
                  <span class="badge badge-soft-primary mr-1">居民</span>
                  <span class="text-muted">{{ (parseFloat(summaryData.residentDiffTotalCost) / 10000).toFixed(2) + '/万' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--第二行-->
      <div class="free-box">
        <div class="center-box-frame">
          <div id="lineChart" class="card"></div>
          <div id="pieChart" class="card"></div>
          <div class="details-button">
            <el-button type="success" @click="moreClick">更多</el-button>
          </div>
        </div>
      </div>
      <!--第三行-->
      <div class="free-box">
        <div class="footer-box-frame">
          <div class="card footer-box-frame-left">
            <div class="sort-icon">
              <i class="som-icon-sort"
              :title="sort ? '倒序' : '正序'"
                 @click="sortClick"
              ></i>
            </div>
            <div class="histogram-echars">
              <el-select v-model="type" @change="selectChange" placeholder="请选择">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </div>
            <div id="histogramChart" style="height: 100%"></div>
          </div>
          <div class="card footer-box-frame-right">
            <el-table
              :data="tableData"
              height="250"
              border
              style="width: 100%">
              <el-table-column
                prop="psmName"
                label="姓名">
              </el-table-column>
              <el-table-column
                prop="medcasNo"
                label="病案号">
              </el-table-column>
              <el-table-column
                prop="disGpName"
                label="病组名称">
              </el-table-column>
              <el-table-column
                prop="sumfee"
                label="总费用">
              </el-table-column>
              <el-table-column
                prop="poolFee"
                label="统筹费用">
              </el-table-column>
              <el-table-column
                prop="disGpSumfee"
                label="病组总费用">
              </el-table-column>
              <el-table-column
                prop="disGpPoolFee"
                label="病组统筹费用">
              </el-table-column>
            </el-table>
            <el-pagination
              small
              layout="prev, pager, next"
              @current-change="currentChange"
              :total="total"
              :page-size="pageSize" >
            </el-pagination>
          </div>
        </div>
      </div>
      <more-dialog style="height: 100%" :moreDialogVisible="moreDialogVisible" :data="moreData" @closed="closeDialog"></more-dialog>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { selectDataChange, selectOrderData, selectParTypePie, selectSummary } from '@/api/newBusiness/feebackAnalyse'
import { queryFeedbackDetail2 } from '@/api/newBusiness/newBusinessUpload'
import MoreDialog from '@/views/feedbackAnaly/feedbackCollect/comps/moreDialog'

export default {
  name: 'feedbackCollect',
  components: { MoreDialog },
  data: () => ({
    queryForm: {
      medcasCodg: ''
    },
    total: 0,
    tableId: 'dataTable',
    tableData: [],
    tableLoading: false,
    summaryData: {}, // 统计数据
    monthChangeData: {
      overallList: [],
      groupOverallList: [],
      diffList: []
    }, // 月变化费用
    patientPieData: {
      peopleNumPieList: [],
      totalCostPieList: [],
      overallPieList: [],
      groupOverallPieList: [],
      diffPieList: []
    }, // 结算患者饼状图数据
    moreData: {
      totalCostPieList: [],
      overallPieList: [],
      groupOverallPieList: [],
      diffPieList: []
    }, // 传到 moreDialog 组件的数据
    ym: [],
    pageNum: 1,
    pageSize: 20,
    type: '1', // 1：科室，2：医生，3：病组
    orderType: '1', // 1：升序，2：降序
    orderData: {
      labelList: [], // 柱状图名称
      valueList: [] // 值
    }, // 差异费用排序数据
    histogramTitle: '', // 柱状图 profttl
    options: [{
      value: '1',
      label: '科室'
    }, {
      value: '2',
      label: '医生'
    }, {
      value: '3',
      label: '病组'
    }],
    // 排序 true：正序 false：倒序
    sort: true,
    moreDialogVisible: false
  }),
  mounted () {
    this.initData()
  },
  methods: {
    closeDialog (val) {
      this.moreDialogVisible = val
    },
    moreClick () {
      this.moreDialogVisible = true
    },
    getParams () {
      return {
        seStartTime: this.ym[0],
        seEndTime: this.ym[1]
      }
    },
    issueChange () {
      this.$nextTick(function () {
        this.initData()
      })
    },
    selectChange () {
      this.selectOrderData()
    },
    sortClick () {
      this.sort = !this.sort
      if (this.sort) {
        this.orderType = '1'
      } else {
        this.orderType = '2'
      }
      this.selectOrderData()
    },
    currentChange (val) {
      this.pageNum = val
      this.selectDetails()
    },
    initPie () {
      let pieDom = document.getElementById('pieChart')
      let myChart = echarts.init(pieDom)
      let option
      option = {
        title: {
          text: '结算患者类型(人数)',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.patientPieData.peopleNumPieList
          }
        ]
      }
      option && myChart.setOption(option)
    },
    initData () {
      // 统计数据
      selectSummary(this.getParams()).then(res => {
        this.summaryData = res.data
      })
      // 查询当年折线图数据
      selectDataChange(this.getParams()).then(res => {
        this.monthChangeData = res.data
        this.initEcharts()
      })
      // 查询结算患者类型饼状图数据
      selectParTypePie(this.getParams()).then(res => {
        this.patientPieData = res.data
        this.moreData.totalCostPieList = this.patientPieData.totalCostPieList
        this.moreData.overallPieList = this.patientPieData.overallPieList
        this.moreData.groupOverallPieList = this.patientPieData.groupOverallPieList
        this.moreData.diffPieList = this.patientPieData.diffPieList
        this.initPie()
      })
      // 详情
      this.selectDetails()
      // 查询差异费用排序数据
      this.selectOrderData()
    },
    selectOrderData () {
      let params = this.getParams()
      params.type = this.type
      params.orderType = this.orderType

      selectOrderData(params).then(res => {
        this.orderData = res.data
        this.initOrderData()
      })
    },
    initOrderData () {
      let chartDom = document.getElementById('histogramChart')
      let myChart = echarts.init(chartDom)
      let option

      option = {
        title: {
          text: '支付差异排名'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            interval: 0,
            rotate: 40,
            formatter: params => {
              return this.formatCost(params, 0)
            }
          },
          boundaryGap: [0, 0.01]
        },
        color: this.$somms.generateColor(),
        yAxis: {
          type: 'category',
          data: this.orderData.labelList,
          axisLabel: {
            formatter: params => {
              return params.length > 8 ? params.substr(0, 8) + '...' : params
            }
          }
        },
        series: [
          {
            name: this.histogramTitle,
            type: 'bar',
            data: this.orderData.valueList
          }
        ]
      }

      option && myChart.setOption(option)
    },
    selectDetails () {
      let params = this.getParams()
      params.pageNum = this.pageNum
      params.pageSize = this.pageSize
      queryFeedbackDetail2(params).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
      })
    },
    initEcharts () {
      let chartDom = document.getElementById('lineChart')
      let myChart = echarts.init(chartDom)
      let option

      option = {
        title: {
          text: '当年费用折线图'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['统筹费用', '病组统筹费用', '支付差异']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },

        xAxis: {
          type: 'category',
          data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            interval: 0,
            rotate: 40,
            formatter: params => {
              return this.formatCost(params, 0)
            }
          },
          boundaryGap: [0, 0.01]
        },
        series: [
          {
            name: '统筹费用',
            data: this.monthChangeData.overallList,
            type: 'line',
            smooth: true
          },
          {
            name: '病组统筹费用',
            data: this.monthChangeData.groupOverallList,
            type: 'line',
            smooth: true
          },
          {
            name: '支付差异',
            data: this.monthChangeData.diffList,
            type: 'line',
            smooth: true
          }
        ]
      }
      option && myChart.setOption(option)
    },
    // 格式化
    formatCost (val, fixed = 2) {
      let prefix = ''
      let resVal = val
      if (String(val).includes('-')) {
        prefix = '-'
        val = Math.abs(val)
        resVal = val
      }
      if (Math.abs((parseFloat(val) / 10000)) > 1) {
        resVal = (parseFloat(val) / 10000).toFixed(fixed) + '/万'
      } else {
        resVal = val
      }
      return prefix + resVal
    }
  }
}
</script>

<style scoped>

.free-box {
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -12px;
  margin-left: -10px;
}

.col-xl-3 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 25%;
  flex: 0 0 25%;
  max-width: 25%;
  position: relative;
  padding: 10px;
}

.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border-radius: 0.35rem;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.container-fluid {
  overflow: hidden;
  overflow-y: auto;
  width: 100%;
  padding-right: 12px;
  padding-left: 12px;
  margin-right: auto;
  margin-left: auto;
}

.card-body {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  min-height: 1px;
  padding: 1.25rem;
}

.avatar-title {
  align-items: center;
  background-color: #6d61ea;
  color: #fff;
  display: flex;
  font-weight: 700;
  height: 100%;
  justify-content: center;
  width: 100%;
}

.bg-soft-primary {
  background-color: rgba(109, 97, 234, 0.18) !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.avatar-sm {
  height: 3rem;
  width: 3rem;
}

.float-right {
  float: right !important;
}

.card-body {
  -webkit-box-flex: 1;
  flex: 1 1 auto;
  min-height: 1px;
  padding: 1.25rem;
}

.center-box-frame {
  display: flex;
  position: relative;
  width: 100%;
  padding: 10px;
  height: 300px;
}

.footer-box-frame {
  display: flex;
  position: relative;
  width: 100%;
  padding: 10px;
  height: 300px;
}

.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}

.badge-soft-primary {
  color: #6d61ea;
  background-color: rgba(109, 97, 234, 0.18);
}

.mr-1 {
  margin-right: 0.25rem !important;
}

h6 {
  display: block;
  margin-block-start: 2.33em;
  margin-block-end: 2.33em;
  margin-inline-start: 0;
  margin-inline-end: 0;
  font-weight: bold;
  margin-bottom: 0.5rem;
  line-height: 1.2;
  font-size: .825rem;
  font-family: "Montserrat", sans-serif;
}

.mt-0 {
  margin-top: 0 !important;
}

.my-3 {
  margin-top: 1rem !important;
  margin-bottom: 3rem !important;
}

.card-child {
  display: flex;
  justify-content: space-between;
}

#lineChart {
  width: 60%;
  height: 100%;
}

#pieChart {
  width: 40%;
  height: 100%;
  margin-left: 20px;
}

.footer-box-frame-left {
  width: 40%;
  height: 100%;
}

.footer-box-frame-right {
  width: 60%;
  height: 100%;
  margin-left: 20px;
}
.sort-icon {
  height: 10%;
  width:20%;
  display: flex;
  right: 25px;
  top:5px;
  position: absolute;
  z-index: 999;
  cursor:pointer;
}
.histogram-echars{
  height: 10%;
  width:20%;
  display: flex;
  right: 0;
  position: absolute;
  z-index: 999
}
.details-button {
  top: 20px;
  right: 20px;
  position: absolute;
  z-index: 999
}
</style>
