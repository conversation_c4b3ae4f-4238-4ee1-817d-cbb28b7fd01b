<template>
  <div style="height: 100%">
    <el-table border
              :header-cell-style="{'text-align' : 'center'}"
              height="96.5%"
              :key="Math.random()"
              :id="tableId"
              ref="elTable"
              v-loading="loading"
              :data="data">
      <el-table-column label="序号" type="index" align="center" width="50" fixed></el-table-column>
      <el-table-column label="病案号" prop="a48" align="right" width="110" fixed></el-table-column>
      <el-table-column label="姓名" prop="a11" align="center" width="80" fixed></el-table-column>
      <el-table-column label="出院科室" prop="b16n" align="right" width="160"></el-table-column>
      <el-table-column label="住院医师" prop="b25n" align="right" width="160"></el-table-column>
      <el-table-column label="原主要诊断" prop="initDscgMainDiagCodg" align="right" width="160"></el-table-column>
      <el-table-column label="现主要诊断" prop="mainDiagCodg" align="right" width="160"></el-table-column>
      <el-table-column label="原入组" prop="initDrgCodg" align="right" width="160" :show-overflow-tooltip="true" v-if="this.$somms.getGroupType() === '3'"></el-table-column>
      <el-table-column label="原入组" prop="initDipCodg" align="right" width="160" :show-overflow-tooltip="true" v-if="this.$somms.getGroupType() === '1'"></el-table-column>
      <el-table-column label="现入组" prop="currDrgCodg" align="right" width="160" :show-overflow-tooltip="true" v-if="this.$somms.getGroupType() === '3'"></el-table-column>
      <el-table-column label="现入组" prop="currDipCodg" align="right" width="160" :show-overflow-tooltip="true" v-if="this.$somms.getGroupType() === '1'"></el-table-column>
      <el-table-column label="原点数" prop="initSco" align="right" width="160"></el-table-column>
      <el-table-column label="现点数" prop="currSco" align="right" width="160"></el-table-column>
      <el-table-column label="原差异" prop="initDif" align="right" width="160"></el-table-column>
      <el-table-column label="现差异" prop="currDif" align="right" width="160"></el-table-column>
      <el-table-column label="差异值" prop="difVal" align="right" width="160"></el-table-column>
      <el-table-column label="修改人员" prop="modiPsn" align="right" width="160"></el-table-column>
      <el-table-column label="操作记录" align="center" width="80" fixed="right">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-search" @click="showModifyRecord(scope.row)" circle></el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-drawer :visible.sync="showDrawer" :title="profttl" size="40%">
      <!-- 操作记录 -->
      <div style="margin: 0 0 3% 3%;color: #988e19;font-size: 15px">{{ oprtRcd }}</div>
      <!-- 诊断记录 -->
      <div v-if="originalDiseaseVoList.length > 0 || nowDiseaseVoList.length > 0" style="margin: 3% 0 3% 0">
        <div style="text-align: center;font-size: 14px">诊断</div>
        <div style="display: flex;justify-content: space-around;margin: 1%">
          <!-- 原诊断 -->
          <el-table :data="originalDiseaseVoList">
            <el-table-column label="序号" type="index"></el-table-column>
            <el-table-column label="原诊断编码" prop="dscg_diag_codg" :show-overflow-tooltip="true"></el-table-column>
            <el-table-column label="原诊断名称" prop="dscg_diag_name" :show-overflow-tooltip="true"></el-table-column>
          </el-table>
          <!-- 现诊断 -->
          <el-table :data="nowDiseaseVoList">
            <el-table-column label="序号" type="index"></el-table-column>
            <el-table-column label="现诊断编码" prop="dscg_diag_codg" :show-overflow-tooltip="true"></el-table-column>
            <el-table-column label="现诊断名称" prop="dscg_diag_name" :show-overflow-tooltip="true"></el-table-column>
          </el-table>
        </div>
      </div>
      <!-- 手术记录 -->
      <div v-if="originalOperateVoList.length > 0 || nowOperateVoList.length > 0" style="margin: 3% 0 3% 0">
        <div style="text-align: center;font-size: 14px">手术</div>
        <div style="display: flex;justify-content: space-around;margin: 1%">
          <!-- 原手术 -->
          <el-table :data="originalOperateVoList">
            <el-table-column label="序号" type="index"></el-table-column>
            <el-table-column label="原手术编码" prop="c35c" :show-overflow-tooltip="true"></el-table-column>
            <el-table-column label="原手术名称" prop="c36n" :show-overflow-tooltip="true"></el-table-column>
          </el-table>
          <!-- 现手术 -->
          <el-table :data="nowOperateVoList">
            <el-table-column label="序号" type="index"></el-table-column>
            <el-table-column label="现手术编码" prop="c35c" :show-overflow-tooltip="true"></el-table-column>
            <el-table-column label="现手术名称" prop="c36n" :show-overflow-tooltip="true"></el-table-column>
          </el-table>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: 'patientModifyTable',
  props: {
    tableId: {
      type: String
    },
    loading: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    showDrawer: false,
    profttl: '',
    oprtRcd: '',
    originalDiseaseVoList: [],
    nowDiseaseVoList: [],
    originalOperateVoList: [],
    nowOperateVoList: []
  }),
  methods: {
    showModifyRecord (item) {
      this.profttl = item.a48 + '/' + item.a11
      this.oprtRcd = item.oprtRcd
      this.originalDiseaseVoList = item.originalDiseaseVoList
      this.nowDiseaseVoList = item.nowDiseaseVoList
      this.originalOperateVoList = item.originalOperateVoList
      this.nowOperateVoList = item.nowOperateVoList
      this.showDrawer = true
    },
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    }
  }
}
</script>

<style lang="scss">
/deep/ .el-drawer__title {
  color: #000000;
}
</style>
