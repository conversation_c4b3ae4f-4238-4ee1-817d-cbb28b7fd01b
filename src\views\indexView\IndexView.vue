<template>
  <div class="app-wrapper" :class="classObj">
    <sidebar class="sidebar-container" :main-menu-index="mainMenuIndex"></sidebar>
    <div class="main-container">
      <navbar @showInfo="showUserInfo" @selectMainMenu="fnSetMainMenu"></navbar>
      <tags-view></tags-view>
      <!--      <div class="app-main"></div>-->
      <app-main></app-main>
      <user-info :dialogFormVisible="dialogFormVisible" @closeDialog="closeUserInfo"/>
    </div>
  </div>
</template>

<script>
import {Navbar, Sidebar, AppMain, TagsView, UserInfo} from './components'
import ResizeMixin from './mixin/ResizeHandler'
import {removeWatermark, setWaterMark} from "../../utils/waterMark";
import store from "../../store";
import {parseTime} from "../../utils";

export default {
  name: 'indexView',
  components: {
    Navbar,
    Sidebar,
    AppMain,
    TagsView,
    UserInfo
  },
  data: () => ({
    dialogFormVisible: false,
    mainMenuIndex: null
  }),
  mixins: [ResizeMixin],
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar
    },
    device() {
      return this.$store.state.app.device
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  mounted() {
    if (this.$store.getters.weekPassWord) {
      this.$nextTick(() => {
        this.showUserInfo()
      })
    }
    if (this.$store.getters.name) {
      this.$nextTick(() => {
        let msgStr = store.state.user.hospitalName || ''
        if (msgStr) {
          msgStr += '~' + (store.state.user.nickname || '')
        } else {
          msgStr = (store.state.user.nickname || '')
        }
        setWaterMark(msgStr, parseTime(new Date()))
      })
    }
  },
  beforeDestory() {
    removeWatermark()
  },
  methods: {
    showUserInfo() {
      this.dialogFormVisible = true
    },
    closeUserInfo() {
      this.dialogFormVisible = false
    },
    fnSetMainMenu(menuIndex) {
      if (menuIndex !== 'home') {
        this.mainMenuIndex = menuIndex
      } else {
        this.$router.push('/home')
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "src/styles/mixin.scss";

.app-wrapper {
  @include clearfix;
  position: absolute;
  height: 100%;
  width: 100%;
}
</style>
