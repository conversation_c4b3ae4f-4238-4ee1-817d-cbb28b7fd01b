<template>
  <div class="med-error-wrapper">
    <!-- 左侧图 -->
    <div class="med-error-wrapper-left">
      <drg-echarts @chartClick="pieLeftChartClick" :options="groupErrorOptions"/>
    </div>

    <!-- 右侧数据 -->
    <div class="med-error-wrapper-left">
      <drg-echarts @chartClick="pieRightChartClick" :options="comAndLogicErrorOptions"/>
    </div>
  </div>
</template>
<script>
import moment from 'moment'

export default {
  name:'newDrgDeptAnalysisCompMedError',
  props: {
    // 分组错误
    options: {
      type: Object,
      default: () => {}
    },
    // 逻辑性完整性错误
    options1: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    return {
      groupErrorOptions: {},
      comAndLogicErrorOptions: {}
    }
  },
  methods: {
    pieLeftChartClick (params) {
      if (params) {
        this.$router.push({
          path: params.data.url,
          query: {
            begnDate: params.data.begnDate,
            expiDate: params.data.expiDate,
            deptCode: params.data.deptCode,
            dipCodg: params.data.dipCodg,
            isInGroup: 0,
            notGroupReason: [params.data.errorReason],
            inStartTime: this.queryForm.inStartTime,
            inEndTime: this.queryForm.inEndTime,
            inHosFlag: this.queryForm.inHosFlag
          }
        })
      }
    },
    pieRightChartClick (params) {
      if (params) {
        this.$router.push({
          path: params.data.url,
          query: {
            begnDate: params.data.begnDate,
            expiDate: params.data.expiDate,
            deptCode: params.data.deptCode,
            dipCodg: params.data.dipCodg,
            resultType: 1,
            inStartTime: this.queryForm.inStartTime,
            inEndTime: this.queryForm.inEndTime,
            inHosFlag: this.queryForm.inHosFlag
          }
        })
      }
    }
  },
  watch: {
    options: {
      immediate: true,
      deep: true,
      handler (options) {
        if (options) {
          this.$nextTick(() => {
            this.groupErrorOptions = options
          })
        }
      }
    },

    options1: {
      immediate: true,
      deep: true,
      handler (options) {
        if (options) {
          this.$nextTick(() => {
            this.comAndLogicErrorOptions = options
          })
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.med-error-wrapper{
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  overflow: hidden;

  &-left{
    width: 50%;
    height: 100%;
  }

  &-right{
    width: 50%;
    height: 100%;
  }
}
</style>
