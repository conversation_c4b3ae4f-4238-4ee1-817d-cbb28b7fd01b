// $(window).load(function(){
//             $(".loading").fadeOut()
//            })

/****/
window.onload = window.onresize = function () {
  // let whei = $(window).width()

  let html = document.querySelector('html')
  // 屏幕的宽度除以10，获取跟元素fontSize标准
  let fontSize = window.innerWidth / 20
  // 获取到的 fontSize 标准不允许超过我们定义的最大值
  fontSize = fontSize > 50 ? 50 : fontSize
  html.style.fontSize = fontSize + 'px'
}
