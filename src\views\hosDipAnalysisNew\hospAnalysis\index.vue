<template>
  <div class="app-container">
    <el-card class="hos-analysis-wrapper">
      <drg-title-line title="全院分析" class="first-page-title">
        <template slot="rightSide">
          <div class="hos-analysis-wrapper-title-right">
            <el-radio-group v-model="dateType"
                            @change="radioChange"
                            style="margin-right: 10px">
              <el-radio-button :label="1">出院</el-radio-button>
              <el-radio-button :label="2">结算</el-radio-button>
            </el-radio-group>
            <el-date-picker v-model="queryDate"
                            type="daterange"
                            align="right"
                            unlink-panels
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="dateChange"
                            :clearable="false"
                            :picker-options="pickerOptions"/>
          </div>
        </template>
      </drg-title-line>
      <!-- 上 -->
      <el-row :gutter="20" style="height: 10%;display: flex">
        <el-col :span="6" v-for="(item,index) in headList" :key="index">
          <el-card class="hos-analysis-head"
                   :style="{backgroundColor: item.bgColor, border: '1px solid' + item.bColor}">
            <div class="hos-analysis-head-icon" :style="{backgroundColor: item.bColor}">
              <i :class="[ item.icon ]"/>
            </div>
            <div class="hos-analysis-head-item">
              <span class="title" v-html="item.value"/>
              <span class="subhead">{{ item.name }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <!-- 中 -->
      <el-row :gutter="20" style="height: 40%">
        <el-col :span="18" style="height: 100%">
          <el-card style="width:100%;height: 100%">
            <div style="width: 100%;height: 100%;">
              <drg-echarts :options="trendOptions"/>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6" style="height: 100%">
          <el-card style="height: 100%">
            <div slot="header">其他指标</div>
            <div style="height: 80%;overflow: auto;padding-left: 20px" class="hos-analysis-api">
              <div style="margin-bottom: 25px" v-for="(item,index) in apiList" :key="index">
                <div style="display: flex">
                  <div class="hos-analysis-icon" :style="{ backgroundColor: item.bColor }">
                    <i :class="[ item.icon ]"></i>
                  </div>
                  <div style="padding-left: 10px">
                    <div style="font-size: var(--biggerSmallTitleSize)">{{ item.value }}</div>
                    <div style="font-size: var(--biggerSmallSize);color: gray">{{ item.name }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <!-- 下 -->
      <el-row :gutter="20" style="height: 70%">
        <el-col :span="8" style="height: 100%">
          <el-card style="height: 100%">
            <!-- 图标 -->
            <div class="hos-analysis-footer-icon">
              <!-- profit -->
              <i class="som-icon-profit "
                 title="盈利"
                 v-if="isLoss"
                 @click="switchLossOrProfit(false)"
                 style="height: 1.2rem;width: 1.2rem"/>

              <!-- loss -->
              <i class="som-icon-loss "
                 title="亏损"
                 v-if="!isLoss"
                 @click="switchLossOrProfit(true)"
                 style="height: 1.2rem;width: 1.2rem"/>
            </div>
            <!-- 排名 -->
            <div class="hos-analysis-footer-order">
              <el-select v-model="topVal" placeholder="请选择" @change="topChange">
                <el-option
                  v-for="item in topOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </div>
            <!-- 排序 -->
            <div class="hos-analysis-footer-sort">
              <i class="som-icon-sort "
                 :title="sort ? '倒序' : '正序'"
                 @click="sortChane"
                 style="height: 1.2rem;width: 1.2rem"/>
            </div>
            <!-- 定时 -->
            <div class="hos-analysis-footer-timer">
              <i class="som-icon-time "
                 title="关闭定时"
                 v-if="timer"
                 @click="changeTimer"
                 style="height: 1.2rem;width: 1.2rem"/>

              <i class="som-icon-close-time "
                 title="开启定时"
                 v-else
                 @click="changeTimer"
                 style="height: 1.2rem;width: 1.2rem"/>
            </div>
            <!-- 类型 -->
            <div class="hos-analysis-footer-type">
              <el-select v-model="typeVal" placeholder="请选择" @change="typeChange">
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </div>

            <div class="hos-analysis-charts">
              <drg-echarts :options="getLossOrProfitOrderOption(typeVal)"
                          @chartClick="click"/>
            </div>
          </el-card>
        </el-col>
        <el-col :span="16" style="height: 100%">
          <el-card style="height: 100%">
            <div class="hos-analysis-content">
              <drg-echarts :options="gerErrorOption(i)" @chartClick="pieChartClick" v-for="i in 2" :key="i"/>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <!-- 最下面 -->
        <div style="height: 95%">
          <el-tabs v-model="activeTabIndex" @tab-click="onTabClick">
            <el-tab-pane label="科室" name="0"></el-tab-pane>
            <el-tab-pane label="医生" name="1"></el-tab-pane>
          </el-tabs>
              <el-card style="height: 100%">
                <div style="width: 100%;height: 100%;">
                  <drg-echarts :options="scatterOptions" @chartClick="quadrantClick" />
                </div>
              </el-card>
        </div>
    </el-card>
  </div>
</template>

<script>
import {
  querySummaryData,
  queryErrorData,
  queryOrderData,
  queryTrendData,
  queryQuadrantDipData,
  queryQuadrantDipDoctorData
} from '@/api/newBusiness/newBusinessHos'
import { formatCost } from '@/utils/common'
import moment from 'moment'
import { updateSwitchState } from '@/api/newBusiness/newBusinessCommon'

export default {
  name: 'hospAnalysis',
  data: () => ({
    activeTabIndex: 0,
    feeStas: '0',
    headList: [
      { name: '病案数', value: 0, type: 1, bgColor: '#ecd6d9', bColor: '#e9adc9', icon: 'som-icon-high-white' },
      { name: '总费用', value: 0, type: 2, bgColor: '#fce4bf', bColor: '#ffc872', icon: 'som-icon-total-cost' },
      { name: '预测费用', value: 0, type: 3, bgColor: '#c3e5ae', bColor: '#97dbae', icon: 'som-icon-money-forecast' },
      { name: '预测差异', value: 0, type: 4, bgColor: '#b7edff', bColor: '#53d2dc', icon: 'som-icon-money-balance' },
      { name: '总分值', value: 0, type: 5, bgColor: '#b7edff', bColor: '#53d2dc', icon: 'som-icon-money-balance' }
    ],
    queryDate: '',
    pickerOptions: [
      {
        text: '当月',
        onClick (picker) {
          let start = new Date()
          let end = new Date()
          start.setDate(1)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: '上月',
        onClick (picker) {
          let start = new Date()
          let end = new Date()
          let lastMonth = start.getMonth() - 1
          start = new Date(start.getFullYear(), lastMonth, 1)
          end = new Date(start.getFullYear(), lastMonth, new Date(start.getFullYear(), end.getMonth(), 0).getDate())
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: '当年',
        onClick (picker) {
          const start = new Date()
          const end = new Date()
          start.setDate(1)
          start.setMonth(0)
          end.setFullYear(end.getFullYear() + 1)
          end.setDate(0)
          end.setMonth(-1)
          picker.$emit('pick', [start, end])
        }
      }
    ],
    begnDate: '',
    expiDate: '',
    // 分组失败
    errorOptions1: {},
    // 逻辑性完整性
    errorOptions2: {},

    // 科室
    lossOrProfitOrderOptions1: {},
    // 医生
    lossOrProfitOrderOptions2: {},
    // 病组
    lossOrProfitOrderOptions3: {},
    // 患者
    lossOrProfitOrderOptions4: {},

    // 颜色
    colors: ['#6eccfc', '#f79478', '#7f7cfb', '#fdbd3e'],
    // 是否是亏损排名
    isLoss: true,
    // 排名
    topVal: 10,
    topOptions: [
      { value: 10, label: 'TPP10' },
      { value: 20, label: 'TPP20' }
    ],
    // 排序 true：正序 false：倒序
    sort: true,
    // 定时器
    timer: '',
    // 当前进度
    value: '0',
    // 最大进度
    maxvalue: '100',
    dateType: 2,

    typeVal: 1,
    typeOptions: [
      { value: 1, label: '科室' },
      { value: 2, label: '医师' },
      { value: 3, label: '病组' },
      { value: 4, label: '患者' }
    ],

    apiList: [
      { name: '入组病例数', value: 0, type: 5, bColor: '#f77080', icon: 'som-icon-api' },
      { name: '未入组病例数', value: 0, type: 6, bColor: '#f77080', icon: 'som-icon-api' },
      { name: 'CMI', value: 0, type: 7, bColor: '#f77080', icon: 'som-icon-api' },
      { name: '高倍率病例数', value: 0, type: 8, bColor: '#f77080', icon: 'som-icon-api' },
      { name: '低倍率病例数', value: 0, type: 9, bColor: '#f77080', icon: 'som-icon-api' },
      { name: '正常倍率病例数', value: 0, type: 10, bColor: '#f77080', icon: 'som-icon-api' },
      { name: '住院过程不完整病例数', value: 0, type: 11, bColor: '#f77080', icon: 'som-icon-api' }
    ],

    trendOptions: {},
    quadrantDataList: [],
    scatterOptions: {},
    scatterDoctorChart: {},
    quadrantDoctorDataList: []
  }),
  created () {
    if (this.queryDate.length == 0) {
      this.begnDate = moment(this.$somms.getYearMonthStartTime()).format('YYYY-MM-DD')
      this.expiDate = moment(this.$somms.getYearMonthEndTime()).format('YYYY-MM-DD')
      this.queryDate = [this.begnDate, this.expiDate]
    }

  },
  mounted () {
    this.feeStas = String(this.$store.getters.feeStas)
    this.changeName()
    this.init()
  },
  computed: {
    prefix () {
      return this.isLoss ? '亏损' : '盈利'
    }
  },
  methods: {
    onTabClick () {
      if (this.activeTabIndex == 0) {
        this.scatterOptions = this.scatterChart()
        this.scatterChart()
      } else if (this.activeTabIndex == 1) {
        this.scatterOptions = this.scatterDoctorChartView()
        this.scatterDoctorChartView()
      }
    },
    quadrantData () {
      queryQuadrantDipData(this.getParams()).then(res => {
        this.quadrantDataList = res.data
        this.scatterChart()
      })
    },
    quadrantDoctorData () {
      queryQuadrantDipDoctorData(this.getParams()).then(res => {
        this.quadrantDoctorDataList = res.data
        // this.scatterDoctorChartView()
      })
    },
    changeSwitch (val) {
      this.$store.commit('SET_SWITCHSTATE', val)
      this.feeStas = this.$store.getters.feeStas
      let params = {}
      this.changeName()
      params.feeStas = this.feeStas
      params.username = this.$store.getters.name
      updateSwitchState(params).then((res) => {
        if (res.code == 200) {
          this.init()
        }
      })
    },
    changeName () {
      this.headList.forEach(item => {
        if (item.name == '预测费用' && this.feeStas == '1') {
          item.name = '反馈费用'
        } else if (item.name == '反馈费用' && this.feeStas == '0') {
          item.name = '预测费用'
        } else if (item.name == '预测差异' && this.feeStas == '1') {
          item.name = '反馈差异'
        } else if (item.name == '反馈差异' && this.feeStas == '0') {
          item.name = '预测差异'
        }
      })
    },
    pieChartClick (params) {
      if (params) {
        this.$router.push({
          path: params.data.url,
          query: {
            begnDate: moment(this.begnDate).format('YYYY-MM-DD'),
            expiDate: moment(this.expiDate).format('YYYY-MM-DD'),
            resultType: 1,
            isInGroup: 0,
            notGroupReason: [params.data.errorReason],
            feeStas: this.feeStas
          }
        })
      }
    },
    click (params) {
      this.$router.push({
        path: params.data.url,
        query: {
          ym: this.queryDate,
          code: params.data.code,
          begnDate: moment(this.begnDate).format('YYYY-MM-DD'),
          expiDate: moment(this.expiDate).format('YYYY-MM-DD'),
          radioMode: '3',
          isLoss: this.isLoss ? 1 : 0,
          feeStas: this.feeStas
        }
      })
    },
    // 初始化
    init () {
      this.getHeadData()
      this.getErrorData()
      this.getOrderData()
      this.getTrendOption()
      this.quadrantData()
      this.quadrantDoctorData()
      // this.enableTimer()
    },
    dateChange (val) {
      if (val) {
        this.begnDate = val[0]
        this.expiDate = val[1]
      } else {
        this.begnDate = null
        this.expiDate = null
      }
      this.init()
    },
    // 获取头部信息
    getHeadData () {
      querySummaryData(this.getParams()).then(res => {
        if (res.data) {
          this.headList[0].value = res.data.totalNum
          this.headList[1].value = formatCost(res.data.sumfee, true)
          this.headList[2].value = formatCost(res.data.forecastAmount, true)
          this.headList[3].value = formatCost(res.data.forecastAmountDiff, true)
          this.headList[4].value = formatCost(res.data.totalSco, true)

          this.apiList[0].value = res.data.drgInGroupMedcasVal
          this.apiList[1].value = res.data.notInGroupNum
          this.apiList[2].value = res.data.cmi
          this.apiList[3].value = res.data.ultraLowNum
          this.apiList[4].value = res.data.normalNumCount
          this.apiList[5].value = res.data.ultraIncompleteNum
        }
      })
    },
    // 获取错误信息
    getErrorData () {
      queryErrorData(this.getParams()).then(res => {
        this.generateErrorOptions(res.data)
      })
    },
    // 生成错误图
    generateErrorOptions (data) {
      let tempData = []
      for (let i = 0; i < 8; i++) {
        let errNum = data['error' + (i + 1)]
        let name = this.getErrorOptionsErrorName((i + 1))
        if (errNum && errNum > 0) {
          tempData.push({
            value: errNum,
            name: name,
            url: '/hosDipAnalysisNew/pattAnalysis',
            errorReason: 'error' + (i + 1)
          })
        }
      }

      let options = {
        color: [...this.colors],
        title: {
          text: '分组校验情况',
          right: '10%'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '入组错误病例',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '12',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: tempData
          }
        ]
      }

      // 环形进度条
      this.value = data.inGroupRate
      let tempData1 = []
      if (data.inGroupRate && data.inGroupRate > 0) {
        tempData1.push({
          value: data.inGroupRate,
          name: '入组率',
          url: '/hosDipAnalysisNew/deptAnalysisNew'
        })
        tempData1.push({
          value: this.maxvalue - this.value
        })
      }
      let option1 = {
        color: [...this.colors],
        title: {
          text: '入组率', right: '10%'
        },
        // 饼图中间显示文字
        graphic: {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: this.value + '%', // 文字内容
            // fill: "#fff",//文字颜色
            fontSize: 30 // 文字字号
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [{
          // 第一张圆环
          type: 'pie',
          radius: ['50%', '70%'],
          center: ['50%', '50%'],
          // 隐藏指示线
          labelLine: { show: false },
          data: tempData1
        }]
      }

      this.errorOptions2 = options

      // let options1 = JSON.parse(JSON.stringify(options))
      // let tempData1 = []
      // if(data.compeleteErrorNum && data.compeleteErrorNum > 0){
      //   tempData1.push({
      //     value: data.compeleteErrorNum, name: '完整性错误',
      //     url:'/caseQual/compVali',
      //   })
      // }
      // if(data.logicErrorNum && data.logicErrorNum > 0){
      //   tempData1.push({
      //     value: data.logicErrorNum, name: '逻辑性错误',
      //     url:'/caseQual/logiVali',
      //   })
      // }
      // options1.series[0].name = '逻辑性完整性错误'
      // options1.series[0].data = tempData1
      // options1.profttl.text = '病例校验情况'
      //
      this.errorOptions1 = option1
    },
    // 获取显示名称
    getErrorOptionsErrorName (index) {
      if (index == 1) {
        return this.$somms.getDictValueByType('1', 'GROUP_ERROR_MSG')
      }
      if (index == 2) {
        return this.$somms.getDictValueByType('2', 'GROUP_ERROR_MSG')
      }
      if (index == 3) {
        return this.$somms.getDictValueByType('3', 'GROUP_ERROR_MSG')
      }
      if (index == 4) {
        return this.$somms.getDictValueByType('4', 'GROUP_ERROR_MSG')
      }
      if (index == 5) {
        return this.$somms.getDictValueByType('5', 'GROUP_ERROR_MSG')
      }
      if (index == 6) {
        return this.$somms.getDictValueByType('6', 'GROUP_ERROR_MSG')
      }
      if (index == 7) {
        return this.$somms.getDictValueByType('7', 'GROUP_ERROR_MSG')
      }
      if (index == 8) {
        return this.$somms.getDictValueByType('8', 'GROUP_ERROR_MSG')
      }
    },
    // 获取排序信息
    getOrderData () {
      // 清除数据
      this.generateOrderOptions([])
      queryOrderData(this.getParams()).then(res => {
        if (res.data) {
          this.generateOrderOptions(res.data)
        }
      })
    },
    // 生成排序图
    generateOrderOptions (data) {
      let option = {
        title: {
          text: '科室亏损排名',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        profttl: { text: '' },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            interval: 0,
            rotate: 40,
            formatter: params => {
              return this.formatCost(params, 0)
            }
          },
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          axisLabel: {
            formatter: params => {
              return params.length > 8 ? params.substr(0, 8) + '...' : params
            }
          },
          data: []
        },
        series: [
          {
            name: '科室',
            type: 'bar',
            data: [
              {
                code: '',
                url: ''
              }
            ]

          }
        ]
      }
      let names = ['科室', '医生', '病组', '患者']
      let dataList = [
        this.generateOrderData(data.deptList, 'deptName'),
        this.generateOrderData(data.doctorList, 'drName'),
        this.generateOrderData(data.disList, 'dipName'),
        this.generateOrderData(data.medList, 'name')
      ]
      for (let i = 0; i < names.length; i++) {
        let tempOption = this.$somms.cloneObj(option)
        tempOption.yAxis.data = dataList[i].yAxisData
        tempOption.series[0].data = dataList[i].seriesData
        tempOption.series[0].name = names[i]
        const titleText = names[i] + this.prefix + '排名'
        tempOption.title.text = titleText
        tempOption.profttl.text = titleText
        tempOption.color = this.colors[i]
        this['lossOrProfitOrderOptions' + (i + 1)] = tempOption
      }
    },
    // 生成排序数据
    generateOrderData (data, fld) {
      let res = {}
      res.yAxisData = []
      res.seriesData = []
      if (data) {
        data.forEach(item => {
          res.yAxisData.unshift((item[fld] && item[fld] != undefined) ? item[fld] : '-')
          if (fld == 'deptName') {
            res.seriesData.unshift({
              value: item.diff,
              code: item.deptCode,
              url: '/hosDipAnalysisNew/deptAnalysisNew'
            })
          } else if (fld == 'drName') {
            res.seriesData.unshift({
              value: item.diff,
              code: item.drCodg,
              url: '/hosDipAnalysisNew/doctAnalysis'
            })
          } else if (fld == 'dipName') {
            res.seriesData.unshift({
              value: item.diff,
              code: item.dipCodg,
              url: '/hosDipAnalysisNew/disenalysis'
            })
          } else {
            res.seriesData.unshift({
              value: item.diff,
              code: item.patientId,
              url: '/hosDipAnalysis/predictPay'
            })
          }
        })
      }
      return res
    },
    // 格式化
    formatCost (val, fixed = 2) {
      let prefix = ''
      let resVal = val
      if (String(val).includes('-')) {
        prefix = '-'
        val = Math.abs(val)
        resVal = val
      }
      if (Math.abs((parseFloat(val) / 10000)) > 1) {
        resVal = (parseFloat(val) / 10000).toFixed(fixed) + '/万'
      } else {
        resVal = val
      }
      return prefix + resVal
    },
    // 获取错误option
    gerErrorOption (index) {
      return this['errorOptions' + index]
    },
    // 获取排名option
    getLossOrProfitOrderOption (index) {
      return this['lossOrProfitOrderOptions' + index]
    },
    // 选择亏损还是盈利排名
    switchLossOrProfit (isLoss) {
      this.clearTimer()
      this.isLoss = isLoss
      this.getOrderData()
    },
    // 排名个数改变
    topChange () {
      this.clearTimer()
      this.getOrderData()
    },
    // 排序改变
    sortChane () {
      this.clearTimer()
      this.sort = !this.sort
      this.getOrderData()
    },
    // 定时改变
    changeTimer () {
      if (this.timer) {
        this.clearTimer()
      } else {
        this.enableTimer()
      }
    },
    // 开启定时
    enableTimer () {
      this.timer = setInterval(() => {
        if ((Math.random() * 10).toFixed(0) % 2 == 0) {
          this.isLoss = !this.isLoss
        } else {
          this.sort = !this.sort
        }
        this.getOrderData()
      }, 10000)
    },
    // 清除定时
    clearTimer () {
      clearInterval(this.timer)
      this.timer = ''
    },
    // 参数
    getParams () {
      let params = {}
      // let firstDay = new Date();
      // firstDay.setDate(1);
      // firstDay.setMonth(0);
      // let lastDay = new Date();
      // lastDay.setFullYear(lastDay.getFullYear()+1);
      // lastDay.setDate(0);
      // lastDay.setMonth(-1);
      // firstDay = moment(firstDay).format("YYYY-MM-DD");
      // lastDay = moment(lastDay).format("YYYY-MM-DD");
      // params.begnDate = firstDay
      // params.expiDate = lastDay
      // params.begnDate = '2021-05-01'
      // params.expiDate = '2021-05-31'
      params.begnDate = moment(this.begnDate).format('YYYY-MM-DD')
      params.expiDate = moment(this.expiDate).format('YYYY-MM-DD')
      params.auth = true
      params.isLoss = this.isLoss
      params.limit = this.topVal
      params.sort = this.sort
      params.feeStas = this.feeStas
      params.dateType = this.dateType

      params.url = 'hospAnalysis'
      return params
    },
    // 时间类型改变
    radioChange () {
      this.init()
    },
    // 选择框类型改变
    typeChange () {
      this.getLossOrProfitOrderOption(this.typeVal)
    },
    // 生成趋势图
    getTrendOption () {
      queryTrendData(this.getParams()).then(res => {
        this.cyList = [0]
        this.ycList = [0]
        this.monthList = [0]
        this.totalList = [0]
        if (res.controlMap.monthList != 0) {
          this.monthList = res.controlMap.monthList
          this.totalList = res.controlMap.totalList
          this.ycList = res.controlMap.ycList
          this.cyList = res.controlMap.cyList
        }
        this.drawTendencyAnalysis()
      })
    },
    drawTendencyAnalysis () {
      // 基于准备好的dom，初始化echarts实例
      // let tendencyAnalysis = echarts.init(document.getElementById('tendencyAnalysis'));
      let option = {
        color: ['#ffc872', '#97dbae', '#53d2dc'],
        title: {
          text: '趋势分析'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['总费用', '预测费用', '预测差异']
        },
        xAxis: {
          type: 'category',
          data: this.monthList
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '总费用',
            data: this.totalList,
            type: 'line',
            smooth: true
          },
          {
            name: '预测费用',
            data: this.ycList,
            type: 'line',
            smooth: true
          },
          {
            name: '预测差异',
            data: this.cyList,
            type: 'line',
            smooth: true
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      // tendencyAnalysis.setOption(option);
      this.trendOptions = option
    },

    // 科室四象限图
    scatterChart () {
      let listTotal = []
      for (let i = 0; i < this.quadrantDataList.length; i++) {
        let quadrantData = this.quadrantDataList[i]
        listTotal.push(
          quadrantData.forecastAmountDiff,
          quadrantData.cmi,
          quadrantData.forecastAmount,
          quadrantData.medicalTotalNum,
          quadrantData.sumfee,
          quadrantData.totalWeight,
          quadrantData.deptName, quadrantData.deptCode)
      }

      // 6个一组地将listTotal中的数据推入listCostData数组中
      let listCostData = []
      for (let i = 0; i < listTotal.length; i += 8) {
        listCostData.push(listTotal.slice(i, i + 8))
      }
      // 找到listCostData中所有数据medicalTotalNum的最大值和最小值
      let max = Math.max(...listCostData.map(item => item[3]))
      let min = Math.min(...listCostData.map(item => item[3]))

      // 根据需要指定最大、最小气泡大小
      let maxSize = 50
      let minSize = 10

      let schema =
        [
          { name: 'forecastAmountDiff', index: 0, text: '预测费用差异' },
          { name: 'cmi', index: 1, text: 'cmi' },
          { name: 'forecastAmount', index: 2, text: '预测费用' },
          { name: 'medicalTotalNum', index: 3, text: '总病例数' },
          { name: 'totalCost', index: 4, text: '总费用' },
          { name: 'totalWeight', index: 5, text: '总权重' },
          { name: 'deptName', index: 6, text: '科室' },
          { name: 'deptCode', index: 7, text: '科室编码' }
        ]
      // 自定义颜色
      let color_dept = []

      const colorCount = 100 // 设置要生成的随机颜色数量

      for (let i = 0; i < colorCount; i++) {
        color_dept.push(getRandomColor())
      }

      function getRandomColor () {
        let letters = '0123456789ABCDEF'
        let color = '#'
        for (let i = 0; i < 6; i++) {
          color += letters[Math.floor(Math.random() * 16)]
        }
        return color
      }

      // 获取科室名称并赋予颜色
      let pieces = []
      for (let i = 0; i < listCostData.length; i++) {
        pieces.push({
          value: listCostData[i][6],
          label: listCostData[i][6],
          color: color_dept[i]
        })
      }

      let option = {
        title: {
          text: '科室发展分析'
        },
        grid: {
          left: '10%',
          right: '15%',
          top: '10%'
        },
        tooltip: {
          formatter: function (param) {
            let value = param.value
            let tooltip = ''
            for (let i = 0; i < schema.length; i++) {
              tooltip += schema[i].text + ': ' + value[i] + '<br/>'
            }
            return tooltip
          }

        },
        // visualMap: {
        //   right: 5,
        //   top: 'middle',
        //   min: min,
        //   max: max,
        //   type: 'piecewise',
        //   splitNumber: listCostData.length,
        //   pieces: pieces
        // },
        xAxis: {
          type: 'value',
          name: '预测费用差异',
          axisLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: 'cmi指数'
        },
        series: [
          {
            // data: listCostData,
            data: [],
            type: 'scatter',
            symbolSize:
              function (dataItem) {
                // 根据公式计算气泡大小
                let size = (dataItem[3] - min) / (max - min) * (maxSize - minSize) + minSize
                return size
              },
            // itemStyle: {
            //   color: function (params) {
            //     let deptName = params.data[6];
            //     let index = pieces.findIndex(item => item.value === deptName);
            //     return color_dept[index];
            //   }
            // },
            markLine: {
              symbol: ['none', 'none'], // ['none']表示是一条横线；['arrow', 'none']表示线的左边是箭头，右边没右箭头；['none','arrow']表示线的左边没有箭头，右边有箭头
              label: {
                position: 'start' // 将警示值放在哪个位置，三个值“start”,"middle","end" 开始 中点 结束
              },
              data: [
                {
                  label: { show: false },
                  silent: true, // 鼠标悬停事件 true没有，false有
                  lineStyle: { // 警戒线的样式 ，虚实 颜色
                    type: 'solid', // 样式  ‘solid’和'dotted'
                    color: '#000000',
                    width: 1 // 宽度
                  },
                  yAxis: 1 // 警戒线的标注值，可以有多个yAxis,多条警示线 或者采用 {type : 'average', name: '平均值'}，type值有 max min average，分为最大，最小，平均值
                }
              ]
            }
          }
        ]
      }
      // this.scatterOptions = option;
      let dataList = [
        this.generateQuadrant(listCostData, 'deptName')
      ]
      let tempOption = this.$somms.cloneObj(option)
      tempOption.series[0].data = dataList[0].seriesData
      this.scatterOptions = tempOption
    },
    generateQuadrant (data, fld) {
      let res = {}
      res.yAxisData = []
      res.seriesData = []
      if (data) {
        data.forEach(item => {
          res.yAxisData.unshift((item[fld] && item[fld] != undefined) ? item[fld] : '-')
          if (fld == 'deptName') {
            res.seriesData.unshift({
              value: item,
              code: item[7],
              url: '/hosDipAnalysisNew/deptAnalysisNew'
            })
          }
        })
      }
      return res
    },

    quadrantClick (params) {
      this.$router.push({
        path: params.data.url,
        query: {
          feeStas: this.feeStas,
          code: params.data.code,
          begnDate: moment(this.begnDate).format('YYYY-MM-DD'),
          expiDate: moment(this.expiDate).format('YYYY-MM-DD'),
          dateType: this.dateType,
          radioMode: '3'
        }
      })
    },

    // 医生四象限图
    scatterDoctorChartView () {
      let listTotal = []
      for (let i = 0; i < this.quadrantDoctorDataList.length; i++) {
        let quadrantDoctorData = this.quadrantDoctorDataList[i]
        listTotal.push(
          quadrantDoctorData.forecastAmountDiff,
          quadrantDoctorData.cmi,
          quadrantDoctorData.forecastAmount,
          quadrantDoctorData.medicalTotalNum,
          quadrantDoctorData.sumfee,
          quadrantDoctorData.totalWeight,
          quadrantDoctorData.drName)
      }

      // 6个一组地将listTotal中的数据推入listCostData数组中
      let listCostData = []
      for (let i = 0; i < listTotal.length; i += 7) {
        listCostData.push(listTotal.slice(i, i + 7))
      }

      // 找到listCostData中所有数据medicalTotalNum的最大值和最小值
      let max = Math.max(...listCostData.map(item => item[3]))
      let min = Math.min(...listCostData.map(item => item[3]))

      // 根据需要指定最大、最小气泡大小
      let maxSize = 50
      let minSize = 10

      let schema = [
        { name: 'forecastAmountDiff', index: 0, text: '预测费用差异' },
        { name: 'cmi', index: 1, text: 'cmi' },
        { name: 'forecastAmount', index: 2, text: '预测费用' },
        { name: 'medicalTotalNum', index: 3, text: '总病例数' },
        { name: 'totalCost', index: 4, text: '总费用' },
        { name: 'totalWeight', index: 5, text: '总权重' },
        { name: 'doctorName', index: 6, text: '医生' }
      ]
      // 自定义颜色
      let color_dept = []

      const colorCount = 100 // 设置要生成的随机颜色数量

      for (let i = 0; i < colorCount; i++) {
        color_dept.push(getRandomColor())
      }

      function getRandomColor () {
        let letters = '0123456789ABCDEF'
        let color = '#'
        for (let i = 0; i < 6; i++) {
          color += letters[Math.floor(Math.random() * 16)]
        }
        return color
      }
      // 获取医生名称并赋予颜色
      let pieces = []
      for (let i = 0; i < listCostData.length; i++) {
        pieces.push({
          value: listCostData[i][6],
          label: listCostData[i][6],
          color: color_dept[i]
        })
      }

      let option = {
        title: {
          text: '医生发展分析'
        },
        grid: {
          left: '10%',
          right: '15%',
          top: '10%'
        },
        tooltip: {
          formatter: function (param) {
            let value = param.value
            let tooltip = ''
            for (let i = 0; i < schema.length; i++) {
              tooltip += schema[i].text + ': ' + value[i] + '<br/>'
            }
            return tooltip
          }
        },
        // visualMap: {
        //   right: 5,
        //   top: 'middle',
        //   min: min,
        //   max: max,
        //   type: 'piecewise',
        //   splitNumber: listCostData.length,
        //   pieces: pieces
        // },
        xAxis: {
          type: 'value',
          name: '预测费用差异',
          axisLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: 'cmi指数'
        },
        series: [
          {
            data: listCostData,
            type: 'scatter',
            symbolSize:
              function (dataItem) {
                // 根据公式计算气泡大小
                let size = (dataItem[3] - min) / (max - min) * (maxSize - minSize) + minSize
                return size
              },
            /*  itemStyle: {
              color: function (params) {
                let deptName = params.data[6];
                let index = pieces.findIndex(item => item.value === deptName);
                return color_dept[index];
              }
            }, */
            markLine: {
              symbol: ['none', 'none'], // ['none']表示是一条横线；['arrow', 'none']表示线的左边是箭头，右边没右箭头；['none','arrow']表示线的左边没有箭头，右边有箭头
              label: {
                position: 'start' // 将警示值放在哪个位置，三个值“start”,"middle","end" 开始 中点 结束
              },
              data: [
                {
                  label: { show: false },
                  silent: false, // 鼠标悬停事件 true没有，false有
                  lineStyle: { // 警戒线的样式 ，虚实 颜色
                    type: 'solid', // 样式  ‘solid’和'dotted'
                    color: '#000000',
                    width: 1 // 宽度
                  },
                  yAxis: 1 // 警戒线的标注值，可以有多个yAxis,多条警示线 或者采用 {type : 'average', name: '平均值'}，type值有 max min average，分为最大，最小，平均值
                }
              ]
            }
          }
        ]
      }
      this.scatterOptions = option
    }
  }
}
</script>

<style lang="scss" scoped>
@mixin layout {
  display: flex;
  justify-content: space-between;
  position: relative;
}

.hos-analysis {

  &-wrapper::-webkit-scrollbar {
    display: none;
  }

  &-wrapper {
    width: 100%;
    height: 100%;
    overflow: auto;

    &-title {
      padding-bottom: 1.8rem !important;

      &-right {
        display: flex;
      }
    }
  }

  &-head {
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
    align-items: center;
    padding: 0 2% 3% 2%;
    border-radius: 30px 0;

    &-icon {
      width: 3rem;
      height: 3rem;
      border-radius: 10px;
      position: relative;

      & > i {
        position: absolute;
        left: 25%;
        top: 25%;
        width: 1.5rem;
        height: 1.5rem;
      }
    }

    &-item {
      display: flex;
      position: absolute;
      flex-direction: column;
      text-align: center;
      right: 10%;
      top: calc(50% - 18.4px);

      .title {
        font-size: var(--biggerSmallTitleSize);
      }

      .subhead {
        font-size: var(--biggerSmallSize);
      }
    }
  }

  &-content {
    width: 100%;
    height: 100%;
    padding-top: 1%;
    @include layout;
  }

  &-footer {
    width: 100%;
    height: 60%;
    padding-top: 3%;
    @include layout;

    @mixin cursor {
      cursor: pointer;
    }

    &-icon {
      position: absolute;
      top: 3%;
      left: 30.5%;
      @include cursor
    }

    &-order {
      width: 10%;
      position: absolute;
      top: 1.2%;
      left: calc(17% - 1.2rem);
    }

    &-sort {
      position: absolute;
      top: 2%;
      left: calc(30% - 1.2rem);
      @include cursor;
    }

    &-timer {
      position: absolute;
      top: 2%;
      left: calc(28% - 1.2rem);
      @include cursor;
    }

    &-type {
      width: 10%;
      position: absolute;
      top: 1.2%;
      left: calc(3% - 1.2rem);
    }
  }

  &-charts {
    width: 30%;
    height: 88%;
    position: absolute;
    top: 10%;
  }

  &-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 10px;
    position: relative;

    & > i {
      position: absolute;
      left: calc(50% - 12px);
      top: calc(50% - 12px);
      width: 1.5rem;
      height: 1.5rem;
    }
  }

  &-api::-webkit-scrollbar {
    display: none;
  }
}

.el-row {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.el-col {
  border-radius: 4px;
}

/deep/ .el-card__header {
  font-weight: bold;
  border-bottom: 0;
}

.first-page-title{margin: 10px}
</style>
