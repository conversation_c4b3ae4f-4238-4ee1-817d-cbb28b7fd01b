<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="form"
             :show-date-range="{ show: true, clearable: true }"
             :show-se-date-range="{ show: true, clearable: true }"
             :container="true"
             :showPagination="true"
             headerTitle="查询条件"
             contentTitle="数据列表"
             :totalNum="total"
             :exportExcel="{ 'tableId': tableId, exportName: '病组反馈分析'}"
             :exportExcelFun="selectFeedbackDiseaseData"
             :exportExcelHasChild="false"
             @query="queryData">

      <template slot="extendFormItems">
<!--        <el-form-item label="DIP编码">-->
<!--        <el-select v-model="dipCodg" clearable placeholder="请选择">-->
<!--          <el-option-->
<!--            v-for="item in options"-->
<!--            :key="item.value"-->
<!--            :label="item.label"-->
<!--            :value="item.value">-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--        </el-form-item>-->
        <el-form-item label="DIP编码">
          <el-input  v-model="dipCodg" placeholder="请输入DIP编码" class="som-form-item" clearable />
        </el-form-item>
      </template>

      <template slot="buttonsMiddle">
        <el-radio-group v-model="queryForm.queryType" size="mini" @change="changeSelectQueryType" class="som-el-form-item-margin-left">
          <el-radio-button :label="1">按病组查询</el-radio-button>
          <el-radio-button :label="2">按科室查询</el-radio-button>
        </el-radio-group>
      </template>

      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  size="mini"
                  stripe
                  :id="tableId"
                  height="100%"
                  :data="dataList"
                  style="width: 100%;"
                  border>
          <el-table-column label="出院科室" prop="deptName" align="left" v-if="deptShow(queryForm.queryType)">
          </el-table-column>
          <el-table-column label="病组编码" prop="dipCodg" align="left" show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="病案数" prop="countMedical" align="center" >
            <template slot-scope="scope">
              <div :class="scope.row.countMedical === 0 ? '' : 'skip'" @click="scope.row.countMedical === 0 ? '' : jumpPersonData(scope.row)">
                {{ scope.row.countMedical }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="盈亏" prop="profitAll" align="center" sortable>
          </el-table-column>
          <el-table-column label="结算点数" prop="selPointAll" align="center" sortable>
          </el-table-column>
          <el-table-column label="病组统筹费用" prop="groupOverallCostAll" align="center" sortable>
          </el-table-column>
          <el-table-column label="统筹费用" prop="overallCostAll" align="center" sortable>
          </el-table-column>
          <el-table-column label="例均统筹费用" prop="overallCostAllAvg" align="center" sortable>
          </el-table-column>
          <el-table-column label="总费用" prop="totalCostAll" align="center" sortable>
          </el-table-column>
          <el-table-column label="例均总费用" prop="totalCostAllAvg" align="center" sortable>
          </el-table-column>
        </el-table>
      </template>
    </drg-form>
  </div>
</template>
<script>
import {
  selectFeedbackDeptOptions,
  selectFeedbackDiseaseData,
  selectFeedbackDiseaseOptions
} from '@/api/newBusiness/feebackAnalyse'
export default {
  name: 'feedbackDiseaseAnalyis',
  data: () => ({
    queryForm: {
      dipCodg: '',
      queryType: '1'
    },
    dataList: [],
    options: [],
    tableId: 'dataTable',
    total: null,
    dipCodg: ''
  }),
  methods: {
    selectFeedbackDiseaseData,
    queryData () {
      selectFeedbackDiseaseData(this.getParams()).then(res => {
        this.dataList = res.data.list
        this.total = res.data.total
      })
    },
    // 获取参数
    getParams () {
      let params = {}
      this.queryForm.dipCodg = this.dipCodg
      Object.assign(params, this.queryForm)
      return params
    },
    // 跳转患者数据
    jumpPersonData (rowData) {
      this.$router.push({
        path: '/feedbackAnaly/feedbackPersonAnalyis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime,
          dipCodg: rowData.dipCodg,
          deptName: rowData.deptName
        }
      })
    },
    changeSelectQueryType () {
      this.queryData()
    },
    deptShow (queryType) {
      if (queryType === 2) {
        return true
      } else if (queryType === 1) {
        return false
      }
    }
  }
}
</script>
