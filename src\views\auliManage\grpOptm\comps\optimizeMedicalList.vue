<template>
  <el-table :id="id"
            :data="data"
            v-loading="loading"
            ref="elTable"
            height="100%"
            :header-cell-style="{ 'text-align' : 'center' }"
            border>
    <el-table-column label="序号" type="index"></el-table-column>
    <el-table-column label="姓名" prop="name"></el-table-column>
    <el-table-column label="病案号" prop="patientId"></el-table-column>
    <el-table-column label="DIP编码" prop="dipCodg" show-overflow-tooltip></el-table-column>
    <el-table-column label="DIP名称" prop="dipName" show-overflow-tooltip></el-table-column>
    <el-table-column label="住院总费用" prop="inHosTotalCost"></el-table-column>
    <el-table-column label="院前检查费" prop="preHospExamfee"></el-table-column>
    <el-table-column label="出院时间" prop="outHosTime"></el-table-column>
    <el-table-column label="出院科室" prop="deptName"></el-table-column>
    <el-table-column label="住院医师" prop="drName"></el-table-column>
    <el-table-column label="查看详情" align="center">
      <template slot-scope="scope">
        <el-button type="primary" size="mini" icon="el-icon-search" circle @click="showPatientDrawer(scope.row,true)"></el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'optimizeMedicalList',
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    id: {
      type: String
    }
  },
  methods: {
    showPatientDrawer (row, Boolean) {
      row.drawerTableVisible = Boolean
      this.$emit('showPatientDrawer', row)
    },
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    }
  }
}
</script>
