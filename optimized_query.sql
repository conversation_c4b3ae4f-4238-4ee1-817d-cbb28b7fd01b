-- 优化版本1：调整JOIN顺序，从最小的结果集开始
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_settle_zy_b e
  INNER JOIN hcm_valid_result_inhosp a ON a.unique_id = e.hisid 
    AND a.rule_scen_type = '1'
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
WHERE
  e.discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
LIMIT 200;

-- 优化版本2：使用子查询先过滤主要条件
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  (SELECT * FROM hcm_valid_result_inhosp WHERE rule_scen_type = '1') a
  INNER JOIN (
    SELECT hisid FROM hcm_settle_zy_b 
    WHERE discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
  ) e ON a.unique_id = e.hisid
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
LIMIT 200;

-- 优化版本3：如果上述方法仍然慢，使用EXISTS
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_valid_result_inhosp a
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
WHERE
  a.rule_scen_type = '1'
  AND EXISTS (
    SELECT 1 FROM hcm_settle_zy_b e 
    WHERE e.hisid = a.unique_id 
    AND e.discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
  )
LIMIT 200;
