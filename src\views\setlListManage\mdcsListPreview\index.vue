<template>
  <div class="app-container">
    <iframe :src="url" width="100%" height="100%"></iframe>
  </div>
</template>
<script>
import { preview } from '@/api/medicalQuality/settleList'
export default {
  name: 'mdcsListPreview',
  data: () => ({
    queryForm: {},
    url: ''
  }),
  mounted () {
    this.queryData()
  },
  methods: {
    queryData () {
      let params = {}
      params.id = this.$route.query.id
      params.k00 = this.$route.query.k00
      params.type = 'SETTLELIST'
      preview(params).then(res => {
        this.url = this.$somms.getPdfJsUrl(res.data, '清单')
      })
    }
  }
}
</script>
