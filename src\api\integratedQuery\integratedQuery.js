import request from '@/utils/request'

/**
 * 查询指标
 * @param params
 * @returns {AxiosPromise}
 */
export function queryIndex (params) {
  return request({
    url: '/integratedQuery/list',
    method: 'post',
    data: params
  })
}

/**
 * 查询维度
 * @param params
 * @returns {AxiosPromise}
 */
export function querydimensionality (params) {
  return request({
    url: '/integratedQuery/querydimensionality',
    method: 'post',
    data: params
  })
}

/**
 * 查询数据
 * @param params
 * @returns {AxiosPromise}
 */
export function queryData (params) {
  return request({
    url: '/integratedQuery/queryData',
    method: 'post',
    data: params
  })
}
