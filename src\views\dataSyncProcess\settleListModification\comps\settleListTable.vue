<template>
  <div :class="['sl-table-container', showSpacing ? 'sl-table-margin': '']">
    <div class="sl-table-name">
      {{ this.tabName }}
    </div>
    <!-- 操作按钮 -->
    <move :check-table-data="checkTableData"
          ref="move"
          :data="tableData"
          @change="oprt"
          @moveLog="log => this.moveLog = log"
          v-if="showOperation"/>

    <el-table border
              :data="tableData"
              :header-cell-style="{ textAlign: 'center' }"
              :cell-style="{ textAlign: 'center' }"
              style="width: 100%" @selection-change="handleSelectionChange" ref="table">
      <el-table-column v-if="showOperation" type="selection" width="55" align="center">
      </el-table-column>
      <el-table-column type="index" width="55" align="center">
      </el-table-column>
      <el-table-column :label="item.label" v-for="(item, index) in columns" :key="index" :width="item.width">
        <template slot-scope="scope">
          <!-- 远程搜索 -->
          <template v-if="item.type === 'select'">
            <dis-or-ope-select v-model="scope.row[item.fld]"
                               v-if="item.inputSelectType === 'icd-select'"
                               :icd-type="item.icdType"
                               :code-or-name="item.codeOrName"
                               :show-icon="scope.row[item.fld + modifyField]"
                               :diagnosis-code-arr="diagnosisCodeArr"
                               :operation-code-arr="operationCodeArr"
                               @change="selectChange($event, scope.row, item.fld, item.linkageField, item.codeOrName, 'icd')"/>

            <common-select v-model="scope.row[item.fld]"
                           v-if="item.inputSelectType === 'select'"
                           :data="getSelectData(item.selectDataField)"
                           :key-props="item.keyProps"
                           :label-or-value="item.labelOrValue"
                           :actual-key-props="item.actualKeyProps"
                           :replace-bracket="item.replace"
                           :show-icon="scope.row[item.fld + modifyField]"
                           @change="selectChange($event, scope.row, item.fld, item.linkageField, item.labelOrValue, 'common')"/>
          </template>

          <!-- 下拉选 -->
          <template v-if="item.type === 'dictSelect'">
            <drg-dict-select :dicType="item.dicType"
                            v-model="scope.row[item.fld]"
                            :type="item.dictSelectType"
                            :useClass="false"
                            :selectStyle="{width:'80%',height:'50%'}"
                            @change="selectChange($event, scope.row, item.fld)">
              <modify-icon :show="scope.row[item.fld + modifyField]" slot="suffix" :usePosition="false"/>
            </drg-dict-select>
          </template>

          <!-- 输入框 -->
          <template v-if="item.type === 'input'">
            <el-input v-model="scope.row[item.fld]" @input="updateState(scope.row, item.fld)" style="width: 80%"/>
            <modify-icon :show="scope.row[item.fld + modifyField]" slot="suffix" :usePosition="false"/>
          </template>

          <!--不可输入框-->
          <template v-if="item.type === 'disInput'">
            <el-input v-model="scope.row[item.fld]"  :disabled="true" style="width: 80%"/>
            <modify-icon :show="scope.row[item.fld + modifyField]" slot="suffix" :usePosition="false"/>
          </template>

          <!-- 普通展示 -->
          <template v-if="item.type === 'normal'">
            {{ nullif(scope.row[item.fld]) }}
          </template>

          <!--日期展示-->
          <template v-if="item.type === 'datetime'">
            <el-date-picker
                v-model="scope.row[item.fld]"
                type="datetime"
                size="mini"
                unlink-panels
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="updateState(scope.row, item.fld)"
                style="width: 98%">
            </el-date-picker>
          </template>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import DisOrOpeSelect from './settleListInfoICDSelect.vue'
import CommonSelect from './settleListInfoSelect.vue'
import Move from './settleListInfoMove.vue'
import ModifyIcon from './settleListInfoModifyIcon.vue'

export default {
  components: {
    'dis-or-ope-select': DisOrOpeSelect,
    'modify-icon': ModifyIcon,
    'common-select': CommonSelect,
    Move
  },
  props: {
    // v-model 绑定值
    modelVal: [Array],
    // 诊断编码
    diagnosisCodeArr: [Array],
    // 手术编码
    operationCodeArr: [Array],
    // 修改字段
    modifyField: [String],
    // 复制前缀
    copyPrefix: [String],
    // 表格列
    columns: [Array],
    // 收费显示操作
    showOperation: {
      type: Boolean,
      default: true
    },
    // 表名
    tabName: {
      type: String
    },
    // 是否现在间距
    showSpacing: {
      type: Boolean,
      default: true
    }
  },
  model: {
    prop: 'modelVal',
    event: 'selected'
  },
  data: () => ({
    tableData: [],
    checkTableData: [],
    moveLog: []
  }),
  methods: {
    // 诊断和手术改变
    selectChange (val, item, fld, linkageField, type, selectType) {
      this.updateState(item, fld)
      if (linkageField) {
        item[linkageField] = type === (selectType === 'icd' ? 'name' : 'label') ? val.value : val.label
        this.updateState(item, linkageField)
      }
      this.emitData()
    },
    // 更改修改状态
    updateState (item, fld) {
      if (item[this.copyPrefix + fld] !== item[fld]) {
        item[fld + this.modifyField] = true
      } else {
        item[fld + this.modifyField] = false
      }
    },
    // 选择改变
    handleSelectionChange (val) {
      this.checkTableData = val
    },
    // 操作
    oprt (params) {
      this.tableData = params.data
      this.emitData()
      if (params.modifyData) {
        this.$nextTick(() => {
          if (params.modifyData) {
            params.modifyData.forEach(row => {
              this.$refs.table.toggleRowSelection(row)
            })
          }
        })
      }
    },
    emitData () {
      this.$emit('selected', this.tableData)
    },
    // 清除操作日志
    clearLog () {
      if (this.$refs.move) {
        this.$refs.move.clearLog()
      }
    },
    // 如果未空值转换为 -
    nullif (val, text = '-') {
      // if(!val && val === ''){
      //   return text
      // }
      return val
    },
    // 获取下拉选数据
    getSelectData (fld) {
      return this.$parent.$parent[fld]
    }
  },
  watch: {
    modelVal: {
      immediate: true,
      handler: function (val) {
        this.tableData = val
      }
    }
  }
}
</script>
<style scoped lang="scss">
$color: #8c939d;
$border: 1px solid $color;

.sl-table-container{
  position: relative;
}
.sl-table-margin {
  margin-bottom: 2rem;
}
.sl-table-name{
  width: 100%;
  height: 2rem;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/deep/.el-table--border, .el-table--group {
  border: $border!important;
}
/**
改变表格内竖线颜色
 */
/deep/.el-table--border  td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
  border-right: $border!important;
}
/**
改变表格内行线颜色
 */
/deep/.el-table  td, .el-table th.is-leaf  {
  border-bottom: $border!important;
}

/deep/.el-table thead tr th {
  border-color: $color;
}

/deep/ .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf{
  border-bottom: $border!important;
}
</style>
