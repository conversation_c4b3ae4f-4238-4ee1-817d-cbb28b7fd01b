<template>
  <div class="app-container">
    <el-table :id="id"
              ref="elTable"
              :data="data"
              v-loading="loading"
              height="100%"
              :header-cell-style="{'text-align':'center'}"
              @selection-change="handleSelectionChange"
              stripe
              border>
      <el-table-column type="selection"  align="center" width="40" :selectable="selectable"/>
      <el-table-column label="序号" type="index" align="center" width="50"/>
      <el-table-column label="是否结算" width="80px" align="center">
        <template slot-scope="scope">
          <i class="som-icon-error-waring som-icon-big" v-if="!scope.row.listSerialNumFlag"></i>
          <i class="som-icon-success som-icon-big" v-else></i>
        </template>
      </el-table-column>
      <el-table-column label="是否异地" prop="isRemote" align="center" width="90" v-if="condition3" />
      <el-table-column label="病案号" prop="medcasCodg" align="right" width="110" v-if="condition3" :key="1"/>
      <el-table-column label="医生名称" prop="drName" align="left" width="80" v-if="condition3" :key="21"/>
      <el-table-column label="姓名" prop="name" width="80" v-if="condition3" :key="2"/>
      <el-table-column label="性别" prop="gend" :formatter="judgeSex" width="50" v-if="condition3" :key="3"/>
      <el-table-column label="年龄" prop="age" align="right" width="50" v-if="condition3" :key="4"/>
      <el-table-column :label="disGpName + '编码'" prop="dipCodg" show-overflow-tooltip v-if="condition1 && isDip"
                       :key="5"/>
      <el-table-column :label="disGpName + '名称'" prop="dipName" show-overflow-tooltip v-if="condition1 && isDip"
                       :key="6"/>
      <el-table-column :label="disGpName + '编码'" prop="drgCodg" show-overflow-tooltip v-if="condition1 && isDrg"
                       :key="5"/>
      <el-table-column :label="disGpName + '名称'" prop="drgName" show-overflow-tooltip v-if="condition1 && isDrg"
                       :key="6"/>
      <el-table-column label="主要诊断" prop="mainDiseaseName" show-overflow-tooltip v-if="condition1" :key="7"/>
      <el-table-column label="主要手术" prop="mainOperatorName" show-overflow-tooltip v-if="condition1" :key="8"/>
      <el-table-column label="住院总费用" prop="inHosTotalCost" align="right" width="100" v-if="condition1" :key="9"/>
      <el-table-column label="出院科室" prop="deptName" width="200" show-overflow-tooltip v-if="condition3" :key="10"/>
      <el-table-column label="出院时间" prop="outHosTime" align="right" width="135" show-overflow-tooltip
                       v-if="condition3" :key="11"/>
      <!--      <el-table-column label="请求参数" prop="reqtPara" align="right" show-overflow-tooltip v-if="condition2" :key="12"/>-->
      <el-table-column label="错误信息" prop="errMsg" align="right" width="500" v-if="condition2" :key="13"/>
      <el-table-column label="上传时间" prop="upldTime" align="right" show-overflow-tooltip
                       v-if="condition2 || condition4 || condition5" :key="14"/>
      <el-table-column label="标识状态" align="center" show-overflow-tooltip v-if="condition1" :key="16" fixed="right">
        <template slot-scope="scope">
          <i class="som-icon-error-waring som-icon-big" v-if="scope.row.lookOver === '0'"></i>
          <i class="som-icon-success som-icon-big" v-else></i>
        </template>
      </el-table-column>
<!--      <el-table-column label="错误信息" prop="errMsg"width="160" show-overflow-tooltip v-if="upldStas == 0" :key="21"/>-->
      <el-table-column label="提交状态" align="center" width="160" show-overflow-tooltip v-if="condition5" :key="20"
                       fixed="right">
        <template slot-scope="scope">
          <div class="som-flex-center" v-if="scope.row.stasType === '0'"><i
              class="som-icon-waring2 mgr-l-02"></i>未提交
          </div>
          <div class="som-flex-center" v-else><i class="som-icon-success mgr-l-02"></i>已提交</div>
        </template>
      </el-table-column>

      <el-table-column label="清单校验状态" align="center" width="160" show-overflow-tooltip v-if="condition1" :key="17"
                       fixed="right">
        <template slot-scope="scope">
          <i class="som-icon-error-waring som-icon-big" v-if="scope.row.chkStas === '0'"></i>
          <i class="som-icon-success som-icon-big" v-else></i>
        </template>
      </el-table-column>
      <!--      <el-table-column label="标识状态记录查看" align="center" width="160" :key="18" fixed="right">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button type="primary" size="mini" icon="el-icon-search" @click="showDialog(scope.row)" circle />-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="复制请求参数" align="center" width="160" :key="19" fixed="right"
                       v-if="condition2 || condition4">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-document-copy" @click="seeRequestParams(scope.row)"
                     circle/>
        </template>
      </el-table-column>
      <el-table-column label="查看详情" align="center" width="100" :key="15" fixed="right">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-search" @click="jumpDetails(scope.row)" circle/>
        </template>
      </el-table-column>
      <!--    <el-table-column label="上传时间" prop="upldTime" align="right" width="135" show-overflow-tooltip v-if="updateStatus == '撤销'" />-->
      <!--    <el-table-column label="撤销时间" prop="revokeTime" align="right" width="135" show-overflow-tooltip v-if="updateStatus == '未上传'" />-->
      <el-table-column label="编辑清单" align="center" width="80" fixed="right" >
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-search" @click="editDetails(scope.row)" circle/>
        </template>
      </el-table-column>
      <el-table-column label="清单操作" align="center" width="80" fixed="right" v-if = "condition5">
        <template slot-scope="scope">
          <el-button type="success" class="som-button-margin-right" size="mini" v-if="scope.row.stasType === '0'"
                     @click="uploadPatient(scope.row )">提交
          </el-button>
          <el-button type="danger" class="som-button-margin-right" size="mini"  v-else @click="quash(scope.row)">撤销
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
        :title="profttl"
        :visible.sync="dialogVisible"
        width="50%">
      <el-table
          :data="form"
          style="width: 100%">
        <el-table-column
            prop="userName"
            label="用户名"
            width="180">
        </el-table-column>
        <el-table-column
            prop="nknm"
            label="昵称"
            width="180">
        </el-table-column>
        <el-table-column
            prop="crteTime"
            label="时间">
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <span>{{ scope.row.oprt == 0 ? '修改标识为未完成' : '修改标识为完成' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {querySettleListOpeLog} from '@/api/medicalQuality/settleListDetail'
import {   modifySettleListType} from '@/api/listManagement/listUpload'
export default {
  name: 'listTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    updateStatus: {
      type: String,
      default: '未上传'
    },
    tabName: {
      type: String,
      default: 'dataTable'
    },
    id: {
      type: String
    },
    // 是否通过校验才能上传
    passValidateUpload: {
      type: Boolean,
      default: true
    }
  },
  data: () => ({
    dialogVisible: false,
    profttl: '标识状态记录查看',
    form: {},
    queryForm: {},
    quashflag: false,
    ids: [],
    k00s: [],
    selectedRows: [], // 存储选中的行
    firstSelectedStasType: null, // 存储第一行选中的 stasType

  }),
  updated() {
    this.updateTable()
  },
  computed: {
    // 未上传
    condition1() {
      return this.updateStatus === '未上传'
    },
    // 上传失败
    condition2() {
      return this.updateStatus === '上传失败'
    },
    // 上传成功
    condition4() {
      return this.updateStatus === '上传成功'
    },
    // 状态修改
    condition5() {
      return this.updateStatus === '状态修改'
    },
    // 所有
    condition3() {
      return this.condition1 || this.condition2 || this.condition4 || this.condition5
    },
    // 病种分组类型名称
    disGpName() {
      return this.$somms.getGroupTypeName()
    },
    // 是否是DIP
    isDip() {
      return this.$somms.isDIP()
    },
    // 是否是DRG
    isDrg() {
      return this.$somms.isDRG()
    }
  },
  methods: {
    obtainParams(id , k00) {
      let params = {}
      Object.assign(params, this.queryForm)
      if(this.quashflag){
        params.uploadFlag = '1'
        params.isSuccess = '1'
      } else {
        params.uploadFlag = '0'
        params.isSuccess = '0'
      }
      this.ids = []
      this.k00s = []
      this.ids.push(id)
      this.k00s.push(k00)
      params.name = this.$store.getters.name
      params.nknm = this.$store.getters.nickname
      params.ids = this.ids
      params.k00s = this.k00s
      params.grperType = this.$somms.getGroupType()
      return params
    },
    quash (item) {
      let  profttl = '清单状态修改提示'
      let msg = '是否撤销病案号为【 '+item.medcasCodg+' 】的已上传数据？'
      this.quashflag = true
      let params = this.obtainParams(item.id,item.k00)
      params.uploadType = '2'
      params.upldStas = '1'

      this.loading = true

      this.$confirm(msg, profttl, {
        type: 'warning'
      }).then(() => {
        modifySettleListType(params).then(res => {
          if (res.code == 200) {
            this.$refs[formName].resetFields()
            this.$message({message: '撤销成功', type: 'success'})
            this.revokeData = []
            this.callParentQueryData()
          }
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      }).catch(() => {
        this.loading = false
      })
    },

    callParentQueryData() {
      // 触发事件并传递数据给父组件
      this.$emit('callQueryData');
    },

    uploadPatient (item) {
      this.quashflag = false
      let  profttl = '清单状态修改提示'
      let msg = '是否修改 病案号为【 '+item.medcasCodg+' 】的清单状态（stas_type），修改后清单将无法再重传？'
      let params = this.obtainParams(item.id,item.k00)
      params.uploadType = '2'
      params.upldStas = '1'
      this.$confirm(msg, profttl, {
        type: 'warning'
      }).then(() => {
        this.loading = true
        modifySettleListType(params).then(res => {
          if (res.code === 200) {
            this.$message({message: '修改成功', type: 'success'})
            this.loading = false
            this.callParentQueryData()
          }
        }).catch(() => {
          this.loading = false
        })
      }).catch(() => {
        this.loading = false
      })
    },
    updateTable() {
      this.$nextTick(() => {
        if (this.$refs.elTable) {
          setTimeout(() => {
            this.$refs.elTable.doLayout()
          }, 400)
        }
      })
    },
    jumpDetails(row) {
      this.$router.push({
        path: '/setlListManage/setlListInfo2',
        query: {id: row.id, k00: row.k00, see: true}
      })
    },
    judgeSex(row) {
      switch (row.gend) {
        case '1':
          return '男'
        case '2':
          return '女'
      }
    },
    handleSelectionChange(val) {

      this.$emit('selectData', val)
      if (Array.isArray(val) && val.length > 1) {
        const lastVal = val[val.length - 1];
        const firstVal = val[0];

        // 比较第一个和最后一个元素的 stasType
        if (firstVal.stasType !== lastVal.stasType) {
          this.$message.error(
            `当前的病案号为【 ${lastVal.medcasCodg} 】 的提交状态与第一个选择的病案号为 【 ${firstVal.medcasCodg} 】的提交状态不同`
          );
        }
      }

    },
    seeRequestParams(row) {
      const textarea = document.createElement('textarea')
      textarea.setAttribute('readonly', 'readonly')
      textarea.value = row.reqtPara
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      this.$message.success('复制成功')
    },
    selectable(row) {
      if (this.condition5) {
        // if (row.stasType === '1') {
        //   return false
        // }
      } else {
        if ((row.chkStas === '0' || row.lookOver === '0') && this.passValidateUpload) {
          return false
        }
      }
      return true
    },
    showDialog(row) {
      let params = {k00: ''}
      params.k00 = row.k00
      querySettleListOpeLog(params).then(res => {
        this.form = res.data
        this.dialogVisible = true
      })
    },
    setTableObj() {
      this.$emit('setRefObj', this.$refs.elTable)
    },

    editDetails(item) {
      this.$router.push({
        path: '/setlListManage/setlListInfo2',
        query: {id: item.id, k00: item.k00, see: false}
      })
    }
  }
}
</script>

<style scoped>
/deep/ .el-tooltip__popper, .el-tooltip__popper.is-dark {
  max-height: 90%;
}

.mgr-l-02{
  margin-right: 0.2rem;
}
</style>
