import request from '@/utils/request'

/**
 * 查询头部汇总数据
 * @param params
 * @returns {*}
 */
export function querySummaryData (params) {
  return request({
    url: '/newDrgBusinessHosAnalysisController/querySummaryData',
    method: 'post',
    params: params
  })
}

/**
 * 查询错误数据
 * @param params
 * @returns {*}
 */
export function queryErrorData (params) {
  return request({
    url: '/newDrgBusinessHosAnalysisController/queryErrorData',
    method: 'post',
    params: params
  })
}

/**
 * 查询排序数据
 * @param params
 * @returns {*}
 */
export function queryOrderData (params) {
  return request({
    url: '/newDrgBusinessHosAnalysisController/queryOrderData',
    method: 'post',
    params: params
  })
}

export function queryTrendData (params) {
  return request({
    url: '/newDrgBusinessHosAnalysisController/queryTrendData',
    method: 'post',
    params: params
  })
}
// 查询象限图数据
export function queryQuadrantData (params) {
  return request({
    url: '/newDrgBusinessHosAnalysisController/queryQuadrantData',
    method: 'post',
    params: params
  })
}

// 查询象限图医生数据
export function queryQuadrantDrgDoctorData (params) {
  return request({
    url: '/newDrgBusinessHosAnalysisController/queryQuadrantDrgDoctorData',
    method: 'post',
    params: params
  })
}
// 查询cmi 时耗费耗
export function queryEntireAssessment (params) {
  return request({
    url: '/newDrgBusinessHosAnalysisController/queryEntireAssessment',
    method: 'post',
    params: params
  })
}
