<template>
  <div class="card-wrapper">
    <!-- head -->
    <div class="card-wrapper-head">
      <!-- profttl -->
      <div class="card-wrapper-head-title">
        <div style="width: 40%;">
          <span class="font-style">{{ headCode }}</span>
          <span class="font-style">{{ headTitle }}</span>
        </div>

        <div class="card-wrapper-head-title-info" v-if="headTitle != ''">
          <div class="info-style">
            <div class="info-style-head">标杆住院费用</div>
            <div class="info-style-body">{{ standardFee }}</div>
          </div>
          <div class="info-style">
            <div class="info-style-head">标杆住院天数</div>
            <div class="info-style-body">{{ standardDays }}</div>
          </div>
          <div class="info-style">
            <div class="info-style-head">标杆药占比</div>
            <div class="info-style-body">{{ standardDrugRatio }}</div>
          </div>
          <div class="info-style">
            <div class="info-style-head">标杆耗占比</div>
            <div class="info-style-body">{{ standardConsumeRatio }}</div>
          </div>
        </div>

        <div class="card-wrapper-head-title-dropdown">
          <el-select v-model="diseaseCode"
                     placeholder="请选择"
                     @change="diseaseChange">
            <el-option v-for="(item, index) in tempDropdownData"
                       :key="index"
                       :label="item.label"
                       :value="item.value" :style="{ 'max-width' : '400px' }">
              <span style="float: left" class="option-span-left">
                {{ item.label }}
              </span>
              <span style="float: right; color: #8492a6; font-size: 13px" class="option-span-right">
                {{ item.value }}
              </span>
            </el-option>
          </el-select>
        </div>
      </div>
    </div>

    <div class="card-wrapper-body">
      <div style="height: 100%;width: 100%;overflow-y: auto">
        <drg-loading :loading="loading" style="position: absolute;top: 50%;left: 45%;"/>
        <el-empty v-show="data.length == 0 ? true : false" style="position: absolute;top: 25%;left: 45%" description="暂无数据"></el-empty>
        <div style="height: 100%;width: 100%;background-color: white;position: absolute" v-show="loading" />
        <el-card shadow="hover"
                 :body-style="{ 'padding' : '0px!important' }"
                 class="dcew-content-box"
                 v-for="(item, index) in data"
                 :key="index"
                 :class="[(index + 1) % 4 != 1 ? 'dcew-content-box-left' : '',colorSelect(item)]">
          <div class="dcew-content-box-title">

            <div style="width: 100%;height: 50%" class="dcew-content-box-title-head">
              {{ item.name }}
            </div>
            <div style="width: 100%;height: 50%;position: relative;padding: 2% 0 0 2%">
              <div style="width: 50%;height: 100%;position: absolute;color: gray;font-size: var(--biggerSmallSize)">
                <div>病案号：{{ item.bah }}</div>
              </div>
              <div style="width: 50%;height: 100%;position: absolute;color: gray;font-size: var(--biggerSmallSize);left: 50%">
                <div>住院医师：{{ item.drName }}</div>
              </div>
            </div>
          </div>
          <div class="dcew-content-box-item">
            <div class="dcew-content-box-item-title">住院总费用</div>
            <div class="dcew-content-box-item-val">{{ item.sumfee }}</div>
          </div>
          <div class="dcew-content-box-item">
            <div class="dcew-content-box-item-title">费用比</div>
            <div class="dcew-content-box-item-val">{{ item.costIndex }}</div>
          </div>
          <div class="dcew-content-box-item">
            <div class="dcew-content-box-item-title">住院天数</div>
            <div class="dcew-content-box-item-val">{{ item.inHosDays }}</div>
          </div>
          <div style="width: 100%;height: 30%">
            <div class="dcew-content-box-item-end">
              <div class="dcew-content-box-item-end-title">时间比</div>
              <div class="dcew-content-box-item-val">{{ item.timeIndex }}</div>
            </div>
            <div class="dcew-content-box-item-end">
              <div class="dcew-content-box-item-end-title">药占比</div>
              <div class="dcew-content-box-item-val">{{ item.drugRatio }}</div>
            </div>
            <div class="dcew-content-box-item-end">
              <div class="dcew-content-box-item-end-title">耗占比</div>
              <div class="dcew-content-box-item-val">{{ item.consumeRatio }}</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'newDrgPatientCardComp',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    dropdownData: {
      type: Array,
      default: () => []
    },
    dropdownVal: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    headTitle: '',
    headCode: '',
    standardFee: 0,
    standardDays: 0,
    standardDrugRatio: 0,
    standardConsumeRatio: 0,
    tempDropdownData: [],
    diseaseCode: ''
  }),
  methods: {
    diseaseChange (val, boolean = true) {
      this.dropdownData.forEach(item => {
        if (item.value == val) {
          if (boolean) {
            this.$emit('diseaseChange', item.value)
          }
          this.headTitle = item.label
          this.headCode = item.value
        }
      })
    },
    colorSelect (item) {
      if (item && item.contrastType) {
        if (item.contrastType == '1') {
          return 'surplus'
        }
        if (item.contrastType == '2') {
          return 'steady'
        }
        if (item.contrastType == '3') {
          return 'warning'
        }
        if (item.contrastType == '4') {
          return 'seriousness'
        }
      }
      // -0.0 情况
      return 'steady'
    }
  },
  watch: {
    dropdownVal: {
      immediate: true,
      deep: true,
      handler: function (val) {
        if (val) {
          this.diseaseCode = val
          this.diseaseChange(val, false)
        }
      }
    },
    dropdownData: {
      immediate: true,
      deep: true,
      handler: function (val) {
        if (val) {
          this.tempDropdownData = val
        }
      }
    },
    data: {
      immediate: true,
      deep: true,
      handler: function (val) {
        if (val) {
          val.forEach(item => {
            this.standardFee = item.standardFee
            this.standardDays = item.standardDays
            this.standardDrugRatio = item.standardDrugRatio
            this.standardConsumeRatio = item.standardConsumeRatio
          })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.card-wrapper {
  height: 100%;
  width: 100%;
  position: relative;

  &-head {
    height: 14%;
    width: 100%;

    &-title {
      width: 100%;
      height: 50%;
      font-size: var(--biggerSize);
      font-weight: 600;
      position: relative;

      &-info {
        width: 50%;
        padding: 0.5% 0 0 0;
        position: absolute;
        font-size: var(--textSize);
        font-weight: 300;
        color: gray;
      }

      &-dropdown {
        position: absolute;
        right: 35%;
        top: 30%;
      }
    }
  }

  &-body {
    width: 100%;
    height: 80%;
    position: relative;
    display: flex;
  }
}

.dcew-content-box {
  height: 40%;
  width: 24.5%;
  display: inline-block;

  &-left {
    margin-left: 0.5%;
  }

  &-title {
    width: 100%;
    height: 30%;
    padding: 0.5rem 0 0 0;
    //display: flex;
    //position: relative;

    &-head {
      padding: 1% 0 1% 2%;
      font-size: var(--biggerSize);
      font-weight: bold;
    }

    &-left {
      position: absolute;
      padding: 1% 0 1% 2%;
      font-size: var(--biggerSize);
      font-weight: bold;
      width: 100%;
      height: 50%;
      left: 0;
    }
  }

  &-item {
    margin: 3% 0 3% 2%;
    width: 29%;
    display: inline-block;
    text-align: center;

    &-title {
      padding: 5% 0 0 0;
      color: gray;
      font-size: var(--biggerSmallSize);
      margin-bottom: 0.5rem
    }

    &-val {
      font-size: var(--biggerSmallTitleSize);
    }

    &-end {
      //width: 24%;
      //height: 75%;
      //margin: 1rem 0 0 0;
      margin: 3% 0 3% 2%;
      width: 29%;
      text-align: center;
      display: inline-block;

      &-title {
        margin-bottom: 0.6rem;
        color: gray;
        font-size: var(--biggerSmallSize);
      }

      &-val {
        font-size: var(--biggerSmallTitleSize);
      }
    }
  }
}

.seriousness {
  /*<!-- rgba(246,114,114,0.5) #f67272 -->*/
  background: linear-gradient(to bottom, rgba(246,114,114,0.5),white);
}
.warning {
  /*<!-- e7a646 rgba(231,166,70,0.5) -->*/
  background: linear-gradient(to bottom, rgba(231,166,70,0.5),white);
}
.steady {
  /** rgba(77,162,255,0.5) 4da2ff*/
  background: linear-gradient(to bottom, rgba(77,162,255,0.5),white);
}
.surplus {
  /** rgba(111,196,68,0.5) 6fc444*/
  background: linear-gradient(to bottom, rgba(111,196,68,0.5),white);
}

.font-style {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.option-span-left {
  width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.option-span-right {
  width: 30%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.info-style {
  width: 18%;
  white-space: nowrap;
  display: inline-block;

  &-head {
    width: 80%;
    //text-align: center;
  }

  &-body {
    width: 80%;
    //text-align: center;
  }
}
</style>
