<template>
  <el-table ref="elTable"
            :id="id"
            height="100%"
            stripe
            :header-cell-style="{'text-align':'center'}"
            :data="data"
            v-loading="loading"
            border>
    <el-table-column label="序号" type="index" width="50" fixed align="center"></el-table-column>
    <el-table-column fixed="right" label="查看详情" align="center" width="90px" >
      <template slot-scope="scope">
        <el-button type="primary" size="mini" icon="el-icon-search" @click="newHandleShowMedicalDetail(scope.$index, scope.row)" circle>
        </el-button>
      </template>
    </el-table-column>
    <el-table-column label="姓名" prop="name" width="80" :fixed="include('name')"></el-table-column>
    <el-table-column label="病案号" prop="bah" width="130" :fixed="include('bah')" align="right"></el-table-column>
    <el-table-column label="出院科室" prop="deptName" width="130" :fixed="include('deptName')" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="DIP编码" prop="dipCodg" width="130" :fixed="include('dipCode')" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="DIP名称" prop="dipName" width="130" :fixed="include('dipName')" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="未入组原因" prop="grpFaleRea" width="130" :fixed="include('groupFailReason')" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="主要诊断" prop="mainDiag" width="130" :fixed="include('mainDiagnosis')" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="主要手术" prop="majorSurgery" width="130" :fixed="include('majorSurgery')" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="住院医师姓名" prop="drName" width="130" :fixed="include('doctorName')" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="入院时间" prop="inHosTime" width="130" :fixed="include('outHosTime')" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="出院时间" prop="outHosTime" width="130" :fixed="include('outHosTime')" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="住院天数" prop="inHosDays" width="100" :fixed="include('inHosDays')" align="right" sortable></el-table-column>
    <el-table-column label="住院天数(标杆)" prop="standardDays" width="150" :fixed="include('standardDays')" align="right" sortable></el-table-column>
    <el-table-column label="住院总费用" prop="inHosTotalCost" width="130" :fixed="include('inHosTotalCost')" align="right" sortable></el-table-column>
    <el-table-column label="住院总费用(标杆)" prop="standardInHosTotalCost" width="160" :fixed="include('standardInHosTotalCost')" align="right" sortable></el-table-column>
    <el-table-column label="药占比" prop="medicalCostRate" width="130" :fixed="include('medicalCostRate')" align="right" sortable>
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.medicalCostRate) }}
      </template>
    </el-table-column>
    <el-table-column label="药占比(标杆)" prop="drugRatio" width="130" :fixed="include('drugRate')" align="right" sortable>
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.drugRatio) }}
      </template>
    </el-table-column>
    <el-table-column label="耗占比" prop="materialCostRate" width="130" :fixed="include('materialCostRate')" align="right" sortable>
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.materialCostRate) }}
      </template>
    </el-table-column>
    <el-table-column label="耗占比(标杆)" prop="consumRatio" width="130" :fixed="include('comsumableRate')" align="right" sortable>
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.consumRatio) }}
      </template>
    </el-table-column>
    <el-table-column label="悬浮"  align="center" >
      <template slot-scope="scope">
        <el-button size="mini" type="primary" @click="showSuspension(scope.row)">悬浮</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'newDipPatientBasicInfoTable',
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    },
    // 表格id
    id: {
      type: String
    },
    columnOptions: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    scopeData: {}
  }),
  methods: {
    include (column) {
      return this.fixedColumns.includes(column)
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    // 显示悬浮
    showSuspension (scope) {
      this.scopeData = scope
      let scopeList = []
      for (let i in this.scopeData) {
        let o = {}
        o.key = i
        o.value = this.scopeData[i]
        scopeList.push(o)
      }
      let res = []
      for (let i = 0; i < scopeList.length; i++) {
        for (let j = 0; j < this.columnOptions.length; j++) {
          if (scopeList[i].key == this.columnOptions[j].value) {
            res.push({
              key: scopeList[i].key,
              label: this.columnOptions[j].label,
              value: scopeList[i].value,
              type: 1,
              show: true
            })
          }
        }
      }
      if (scope) {
        this.$emit('showSuspension', res)
      }
    },
    newHandleShowMedicalDetail (index, row) {
      this.$router.push({ path: '/setlListManage/setlListInfo2', query: { k00: row.k00, id: row.settleListId } })
    }
  }
}
</script>
