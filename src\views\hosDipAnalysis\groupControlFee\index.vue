<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             ref="somForm"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-dip
             show-patient-num
             show-hos-dept
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="DIP分组"
             :container="true"
             @query="handleSearchList" @reset="refresh">

      <template slot="extendFormItems">
        <el-form-item label="预警类型" class="som-form-extend-form-item" >
          <el-select v-model="listQuery.costWarnType"  clearable @change="getDataIsuue" style="width: 100%">
            <el-option
              v-for="item in dictVoList.COST_WARN_TYPE"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="患者姓名" class="som-form-extend-form-item">
          <el-input v-model="listQuery.a11" placeholder="请输入姓名"  style="width: 100%"></el-input>
        </el-form-item>
      </template>

      <template slot="buttons">
        <el-popconfirm
          confirm-button-text='确定'
          cancel-button-text='导出全部'
          icon="el-icon-info"
          icon-color="red"
          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>

      <template slot="containerContent">
        <div style="height:33%">
          <el-row :gutter="0" style="height: 100%">
            <el-col :span="6" style="height: 100%">
              <div id="dipGroupNumTop10" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
            <el-col :span="6" style="height: 100%">
              <div id="dipGroupCostTop10" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
            <el-col :span="12" style="height: 100%">
              <div id="medicalCostChart" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
          </el-row>
        </div>
        <div style="height: 66%">
          <el-table ref="dipGroupListTable"
                    id="dipGroupTable"
                    size="mini"
                    :header-cell-style="{'text-align':'center'}"
                    height="100%"
                    stripe
                    :data="dipGroupList"
                    v-loading="listLoading"
                    border>
            <el-table-column
              label="序号"
              type="index"
              align="right"
              width="50">
            </el-table-column>
            <el-table-column label="出院科室" prop="priOutHosDeptName" align="left" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="病案号" prop="patientId" align="left" width="80" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.patientId | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="姓名" prop="name"  align="center" width="70" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.name | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="住院医师" prop="inHosDoctorName" align="center" width="80" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.inHosDoctorName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="DIP分组编码" prop="dipCodg" align="left" width="120" :show-overflow-tooltip="true" sortable>
              <template slot-scope="scope">{{scope.row.dipCodg | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="DIP分组名称" prop="dipName" align="left" width="200" :show-overflow-tooltip="true" >
              <template slot-scope="scope">{{scope.row.dipName | formatIsEmpty}}</template>
            </el-table-column>
            <drg-table-column label="辅助目录" align="left" prop="isUsedAsstList" dicType="AD" />
            <el-table-column label="年龄段" prop="asstListAgeGrp" align="left" width="200" :show-overflow-tooltip="true" >
              <template slot-scope="scope">{{scope.row.asstListAgeGrp | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="疾病严重程度" prop="asstListDiseSevDeg" align="left" width="200" :show-overflow-tooltip="true" >
              <template slot-scope="scope">{{scope.row.asstListDiseSevDeg | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="肿瘤严重程度" prop="asstListTmorSevDeg" align="left" width="200" :show-overflow-tooltip="true" >
              <template slot-scope="scope">{{scope.row.asstListTmorSevDeg | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="住院费用（本院）" prop="inHosTotalCost" align="right" fixed="right" width="150" :show-overflow-tooltip="true" sortable>
              <template slot-scope="scope">{{scope.row.inHosTotalCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院费用（同级别）" prop="levelCityAvgCost"  align="right" fixed="right" width="200" :show-overflow-tooltip="true" sortable >
              <template slot-scope="scope">{{scope.row.levelCityAvgCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="费用差值 (级别与本院)" prop="profitAndLoss" align="right" fixed="right" width="200" :show-overflow-tooltip="true" sortable>
              <template slot-scope="scope">
                <div v-if="scope.row.inHosTotalCost==null||scope.row.inHosTotalCost==''||scope.row.inHosTotalCost==0||scope.row.levelCityAvgCost==null||scope.row.levelCityAvgCost==''||scope.row.levelCityAvgCost==0">
                  -
                </div>
                <div v-else-if="Number(scope.row.inHosTotalCost)>Number(scope.row.levelCityAvgCost)">
                  <i class="el-icon-caret-bottom"></i>
                  <span style="font-size: 10px;color:#FF0000;font-weight: bold">
                {{scope.row.profitAndLoss.toFixed(2)}}
              </span>
                </div>
                <div v-else-if="Number(scope.row.inHosTotalCost)<=Number(scope.row.levelCityAvgCost)">
                  <i class="el-icon-caret-top"></i>
                  <span style="font-size: 10px;color:#00CC00;font-weight: bold">
                +{{scope.row.profitAndLoss.toFixed(2)}}
              </span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="住院天数(本院)" prop="inHosDays" align="right" width="160" :show-overflow-tooltip="true" sortable>
              <template slot-scope="scope">{{scope.row.inHosDays | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院日（同级别）" prop="levelCityAvgDays"  align="right" width="180" :show-overflow-tooltip="true" sortable >
              <template slot-scope="scope">{{scope.row.levelCityAvgDays | formatIsEmpty}}</template>
            </el-table-column>

            <el-table-column label="天数差值 (级别与本院)" prop="inHosDays"  align="right" width="200" :show-overflow-tooltip="true" sortable >
              <template slot-scope="scope">
                <div v-if="scope.row.inHosDays==null||scope.row.inHosDays==''||scope.row.inHosDays==0||scope.row.levelCityAvgDays==null||scope.row.levelCityAvgDays==''||scope.row.levelCityAvgDays==0">
                  -
                </div>
                <div v-else-if="Number(scope.row.inHosDays)>Number(scope.row.levelCityAvgDays)">
                  <i class="el-icon-caret-bottom"></i>
                  <span style="font-size: 10px;color:#FF0000;font-weight: bold">
                {{(Number(scope.row.levelCityAvgDays)-Number(scope.row.inHosDays)).toFixed(2)}}
              </span>
                </div>
                <div v-else-if="Number(scope.row.inHosDays)<=Number(scope.row.levelCityAvgDays)">
                  <i class="el-icon-caret-top"></i>
                  <span style="font-size: 10px;color:#00CC00;font-weight: bold">
                +{{(Number(scope.row.levelCityAvgDays)-Number(scope.row.inHosDays)).toFixed(2)}}
              </span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="控费预警" prop="costWarn" align="center" :show-overflow-tooltip="true" width="130">
              <template slot-scope="scope">
                <div v-if="scope.row.costWarn==null||scope.row.costWarn==''||scope.row.costWarn=='无法预警'||scope.row.costWarn=='暂无参考标准'">
                  {{scope.row.costWarn | formatIsEmpty}}
                </div>
                <div v-else-if="scope.row.costWarn=='费用偏低'">
              <span class="lowCost">
                <i class="el-icon-bottom"></i>
                 {{scope.row.costWarn | formatIsEmpty}}
              </span>
                </div>
                <div v-else-if="scope.row.costWarn=='平稳区间'">
              <span class="stableCost">
                <i class="el-icon-circle-check"></i>
                 {{scope.row.costWarn | formatIsEmpty}}
              </span>
                </div>
                <div v-else-if="scope.row.costWarn=='费用偏高'">
              <span class="highCost">
                <i class="el-icon-top"></i>
                 {{scope.row.costWarn | formatIsEmpty}}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="病组控费区间" prop="costControlRange" align="center" width="140" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.costControlRange | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="DIP权重（区域）" prop="areaWeight"  align="right" width="150" :show-overflow-tooltip="true" sortable>
              <template slot-scope="scope">{{scope.row.areaWeight | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="DIP权重（本院）" prop="hosWeight" align="right" width="150" :show-overflow-tooltip="true" sortable >
              <template slot-scope="scope">{{scope.row.hosWeight | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="主要诊断" prop="mainCodeAndName" align="left" width="150" :show-overflow-tooltip="true" >
              <template slot-scope="scope">{{scope.row.mainCodeAndName | formatIsEmpty}}</template>
            </el-table-column>

            <el-table-column label="综合医疗服务费" prop="comMedServfee"  align="right" width="150" sortable  >
              <template slot-scope="scope">{{scope.row.comMedServfee }}</template>
            </el-table-column>
            <el-table-column label="综合医疗服务费占比" prop="serviceCostRate"  align="right" width="180"  sortable >
              <template slot-scope="scope">{{scope.row.serviceCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="康复费" prop="rhabFee" align="right" width="90"  sortable >
              <template slot-scope="scope">{{scope.row.rhabFee }}</template>
            </el-table-column>
            <el-table-column label="康复费占比" prop="recoverCostRate" align="right" width="120" sortable  >
              <template slot-scope="scope">{{scope.row.recoverCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="诊断费" prop="diagFee" align="right" width="90"  sortable >
              <template slot-scope="scope">{{scope.row.diagFee }}</template>
            </el-table-column>
            <el-table-column label="诊断费占比" prop="diagnoseCostRate" align="right" width="120"  sortable >
              <template slot-scope="scope">{{scope.row.diagnoseCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="治疗费" prop="treatFee" align="right" width="90" sortable  >
              <template slot-scope="scope">{{scope.row.treatFee }}</template>
            </el-table-column>
            <el-table-column label="治疗费占比" prop="treatmentCostRate" align="right" width="120" sortable >
              <template slot-scope="scope">{{scope.row.treatmentCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="药品费" prop="drugfee" align="right" width="90"  sortable >
              <template slot-scope="scope">{{scope.row.drugfee }}</template>
            </el-table-column>
            <el-table-column label="药品费占比" prop="medicalCostRate" align="right" width="120" sortable >
              <template slot-scope="scope">{{scope.row.medicalCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="血液和血液制品类费" prop="bloodBloPro"  align="right" width="200"  sortable >
              <template slot-scope="scope">{{scope.row.bloodBloPro }}</template>
            </el-table-column>
            <el-table-column label="血液和血液制品类费占比" prop="bloodCostRate" align="right" width="220" sortable  >
              <template slot-scope="scope">{{scope.row.bloodCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="耗材费" prop="mcsFee" align="right" width="90" sortable  >
              <template slot-scope="scope">{{scope.row.mcsFee }}</template>
            </el-table-column>
            <el-table-column label="耗材费占比" prop="materialCostRate" align="right" width="120"   >
              <template slot-scope="scope">{{scope.row.materialCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="中医其他" prop="tcmOth" align="right" width="90"  v-if="showZy" >
              <template slot-scope="scope">{{scope.row.tcmOth }}</template>
            </el-table-column>
            <el-table-column label="中医其他占比" prop="chineseOtherRate" align="right" width="120"   v-if="showZy" >
              <template slot-scope="scope">{{scope.row.chineseOtherRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="其他费" prop="othFee" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.othFee }}</template>
            </el-table-column>
            <el-table-column label="其他费占比" prop="otherCostRate" align="right" width="120"   >
              <template slot-scope="scope">{{scope.row.otherCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="抗生素费" prop="abtFee" align="right" width="120"   >
              <template slot-scope="scope">{{scope.row.abtFee }}</template>
            </el-table-column>
            <el-table-column label="抗生素费占比" prop="antibioticCostRate" align="right" width="120"  >
              <template slot-scope="scope">{{scope.row.antibioticCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="检验费" prop="inspectFee" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.inspectFee }}</template>
            </el-table-column>
            <el-table-column label="检验费占比" prop="inspectionCostRate" align="right" width="110"    >
              <template slot-scope="scope">{{scope.row.inspectionCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="患者自费" prop="psnOwnpay" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.psnOwnpay }}</template>
            </el-table-column>
            <el-table-column label="自费占比" prop="psnOwnpayRate" align="right" width="110"    >
              <template slot-scope="scope">{{scope.row.psnOwnpayRate | formatIsEmpty}}</template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, querySelectTreeAndSelectList, queryLikeDipGroupByPram } from '@/api/common/drgCommon'
import { getDipGroupAndCostControl as queryPageData, getDipGroupNumTop10, getDipGroupCostTop10, getCostCountInfo } from '@/api/dipBusiness/dipCostControl'
import { elExportExcel } from '@/utils/exportExcel'
import echarts from 'echarts'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  b16c: null,
  queryDipGroup: '',
  patientId: null,
  a11: null,
  costWarnType: null,
  drCodg: null,
  drName: null,
  cysj: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date,
  grpStas: null
}
export default {
  name: 'groupControlFee',
  components: { },
  inject: ['reload'],
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      dipGroupList: null,
      total: null,
      cy_start_date: null,
      cy_end_date: null,
      showZy: false,
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      deptName: null,
      tableHeight: 0,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted () {
    this.$nextTick(() => {
      if (this.$route.query) {
        this.listQuery.inHosFlag = this.$route.query.inHosFlag
        this.listQuery.inStartTime = this.$route.query.inStartTime
        this.listQuery.inEndTime = this.$route.query.inEndTime
        this.listQuery.seDateRange = [this.$route.query.seStartTime, this.$route.query.seEndTime]
        this.listQuery.cysj = [this.$route.query.seStartTime, this.$route.query.seEndTime]
      }
    })
    if (this.$route.query.inHosFlag) {
      if (this.$route.query.inHosFlag == 1) {
        this.$nextTick(() => {
          this.$refs.somForm.changeCheckBoxTime(['出院'])
          this.$refs.somForm.setButtonCheckbox('出院')
        })
      }
      if (this.$route.query.inHosFlag == 2) {
        this.$nextTick(() => {
          this.$refs.somForm.changeCheckBoxTime(['入院'])
          this.$refs.somForm.setButtonCheckbox('入院')
        })
      }
      if (this.$route.query.inHosFlag == 3) {
        this.$nextTick(() => {
          this.$refs.somForm.changeCheckBoxTime(['结算'])
          this.$refs.somForm.setButtonCheckbox('结算')
        })
      }
    }
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'COST_WARN_TYPE')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.dipGroupListTable.$children, document.getElementById('dipGroupTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DIP分组控费')
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]

        // 查询数据
        this.getList()
        this.getGroupNumTop10Info()
        this.getGroupCostTop10Info()
        this.getMedicalCostInfo()
      })
    },
    // 表统计结果
    getList () {
      // 回显
      if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
        this.listQuery.cysj = [this.$route.query.cy_start_date, this.$route.query.cy_end_date]
        this.listQuery.dateRange = [this.$route.query.cy_start_date, this.$route.query.cy_end_date]
        this.listQuery.begnDate = this.$route.query.cy_start_date
        this.listQuery.expiDate = this.$route.query.cy_end_date
      }
      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        this.listQuery.seStartTime = this.$route.query.seStartTime
        this.listQuery.seEndTime = this.$route.query.seEndTime
        this.listQuery.seDateRange = [this.$route.query.seStartTime, this.$route.query.seEndTime]
      }
      if (this.$route.query.priOutHosDeptCode) {
        this.listQuery.deptCode = this.$route.query.priOutHosDeptCode
      }

      if (this.$route.query.drCodg) {
        this.listQuery.drCodg = this.$route.query.drCodg
      }

      if (this.$route.query.drName) {
        this.listQuery.drName = this.$route.query.drName
      }

      if (this.$route.query.queryDipGroup) {
        this.listQuery.dipCodg = this.$route.query.queryDipGroup
      }

      if (this.$route.query.grpStas) {
        this.listQuery.grpStas = this.$route.query.grpStas
      }
      if (this.$route.query.inHosFlag) {
        this.listQuery.inDateRange = [this.$route.query.inStartTime, this.$route.query.inEndTime]
        this.listQuery.inStartTime = this.$route.query.inStartTime
        this.listQuery.inEndTime = this.$route.query.inEndTime
        this.listQuery.inHosFlag = this.$route.query.inHosFlag
      }
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryDipGroup = this.listQuery.dipCodg
      this.submitListQuery.patientId = this.listQuery.medcasCodg
      this.submitListQuery.a11 = this.listQuery.a11
      this.submitListQuery.grpStas = this.listQuery.grpStas
      this.submitListQuery.drName = this.listQuery.drName
      this.submitListQuery.drCodg = this.listQuery.drCodg
      this.submitListQuery.costWarnType = this.listQuery.costWarnType
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.dipGroupList = response.data.list
        this.total = response.data.total
      })
    },
    getGroupNumTop10Info () {
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryDipGroup = this.listQuery.dipCodg
      this.submitListQuery.a11 = this.listQuery.a11
      this.submitListQuery.costWarnType = this.listQuery.costWarnType
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      getDipGroupNumTop10(this.submitListQuery).then(response => {
        let result = response.data
        // 按照人次排序
        result.sort(function (o1, o2) {
          return Number(o2.medicalRecordNum) - Number(o1.medicalRecordNum)
        })
        // 截取前10
        let newRes = []
        if (result) {
          for (let i = 0; i < (result.length < 10 ? result.length : 10); i++) {
            newRes.push(result[i])
          }
        }
        // 按照人次升序
        newRes.sort(function (o1, o2) {
          return Number(o1.medicalRecordNum) - Number(o2.medicalRecordNum)
        })
        let yData = [] // DIP分组编码和名称
        let seriesData = [] // 指标
        if (result) {
          for (let i = 0; i < newRes.length; i++) {
            yData.push(newRes[i].dipGroupCodeAndName)
            seriesData.push(newRes[i].medicalRecordNum)
          }
        }
        this.getDownBarEchart('dipGroupNumTop10', seriesData, yData, 'DIP分组人次TOP10相关组')
      })
    },
    getGroupCostTop10Info () {
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryDipGroup = this.listQuery.dipCodg
      this.submitListQuery.a11 = this.listQuery.a11
      this.submitListQuery.costWarnType = this.listQuery.costWarnType
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      getDipGroupCostTop10(this.submitListQuery).then(response => {
        let result = response.data
        // 按照费用排序
        result.sort(function (o1, o2) {
          return Number(o2.inHosCost) - Number(o1.inHosCost)
        })
        // 截取前10
        let newRes = []
        if (result) {
          for (let i = 0; i < (result.length < 10 ? result.length : 10); i++) {
            newRes.push(result[i])
          }
        }
        // 按照人次升序
        newRes.sort(function (o1, o2) {
          return Number(o1.inHosCost) - Number(o2.inHosCost)
        })
        let yData = [] // DIP分组编码和名称
        let seriesData = [] // 指标
        if (result) {
          for (let i = 0; i < newRes.length; i++) {
            yData.push(newRes[i].dipGroupCodeAndName)
            seriesData.push(newRes[i].inHosCost)
          }
        }
        this.getDownBarEchart('dipGroupCostTop10', seriesData, yData, 'DIP分组住院费用TOP10相关组')
      })
    },
    getDownBarEchart (echartId, seriesData, yData, titleData) {
      let option = {
        title: {
          text: titleData,
          x: 'center',
          textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 14, color: '#000000' }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: yData,
          axisLabel: {
            fontSize: 9.7,
            color: '#000000',
            fontFamily: 'Microsoft YaHei',
            formatter: function (value) {
              return (value.length > 17 ? (value.slice(0, 17) + '..') : value)
            }
          }
        },
        series: [
          {
            type: 'bar',
            data: seriesData,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                  offset: 0,
                  color: '#409EFF' // 0% 处的颜色
                }, {
                  offset: 0.5,
                  color: '#0066CC' // 60% 处的颜色
                }, {
                  offset: 1,
                  color: '#006699' // 100% 处的颜色
                }], false),
                label: {
                  show: true,
                  position: 'right',
                  textStyle: {
                    color: '#000000',
                    fontSize: 9.7
                  }
                }
              }
            }
          }
        ],
        grid: {
          left: '45%',
          top: '20',
          bottom: '5',
          right: '20'
        }
      }
      let echartRes = echarts.getInstanceByDom(document.getElementById(echartId))
      if (echartRes) {
        echartRes.clear()
      } else {
        echartRes = echarts.init(document.getElementById(echartId))
      }
      echartRes.setOption(option)
      window.addEventListener('resize', () => {
        echartRes.resize()
      })
      return echartRes
    },
    getMedicalCostInfo () {
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      getCostCountInfo(this.submitListQuery).then(response => {
        let result = response.data
        let barData = [] // 费用数据
        let xAxisData = [] // 费用名称
        let costRate = [] // 费用占比
        let lineData = [] // 累计占比

        if (result) {
          let sumfee = Number(result[result.length - 1].value) // 总费用
          let accumulativeCost = 0 // 累计费用
          for (let i = 0; i < result.length - 2; i++) { // 基本医疗费用
            if (result[i].name != '抗生素费' || result[i].name != '检验费') {
              accumulativeCost += Number(result[i].value)
            }
            barData.push(Number(result[i].value))
            xAxisData.push(result[i].name)
            costRate.push((Number(result[i].value) / sumfee * 100).toFixed(2)) // 费用占比
            lineData.push((accumulativeCost / sumfee * 100).toFixed(2)) // 累计费用占比
          }
        }
        let legendData = ['费用', '累计占比']
        let profttl = 'DIP病种费用分布柏拉图'
        this.rightChart(barData, xAxisData, lineData, costRate, legendData, profttl)
      })
    },
    rightChart (barData, xAxisData, lineData, costRate, legendData, profttl) {
      let option = {
        title: [{ text: profttl, left: 'left', top: '1', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }],
        tooltip: {
          trigger: 'item',
          formatter: function (param) {
            return '费用名称：' + xAxisData[param.dataIndex] + '</br>' +
                '费用额度：' + '：' + barData[param.dataIndex] + '元' + '</br>' +
                '费用占比：' + costRate[param.dataIndex] + '%' + '</br>' +
                '累计占比：' + lineData[param.dataIndex] + '%'
          }
        },
        legend: [{
          data: legendData,
          top: '1',
          left: 'right',
          selectedMode: false,
          formatter: function (name) {
            if (name == '费用') {
              return name + '（绿柱：排名前5）'
            } else if (name == '累计占比') {
              return name + '（红点：累计前80%）'
            }
          }
        }],
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            interval: 0,
            fontSize: 10,
            rotate: 40,
            formatter: function (value) {
              return (value.length > 4 ? (value.slice(0, 4) + '..') : value)
            }
          }
        },
        yAxis: [
          { type: 'value',
            position: 'left',
            name: '单位：元',
            axisLabel: {
              formatter: function (value) {
                if (value > 10000) {
                  return (Number(value) / 10000).toFixed(1) + '万'
                } else {
                  return Number(value).toFixed(1)
                }
              }
            }
          },
          { type: 'value', position: 'right', name: '单位：%', splitLine: { show: false } }
        ],
        series: [{
          name: legendData[0],
          data: barData,
          type: 'bar',
          color: 'red',
          yAxisIndex: 0,
          label: {
            show: true,
            fontSize: 9,
            position: 'top',
            formatter: function (param) {
              if (param.value >= 10000) {
                return (Number(param.value) / 10000).toFixed(1) + '万'
              } else {
                return Number(param.value).toFixed(2)
              }
            }
          },
          itemStyle: {
            color: function (param) {
              if (param.dataIndex < 5) {
                return 'rgba(36,185,179,0.7)'
              } else {
                return 'rgba(40,138,242,0.7)'
              }
            }
          }
        },
        {
          name: legendData[1],
          data: lineData,
          type: 'line',
          symbol: 'circle',
          yAxisIndex: 1,
          label: {
            show: true,
            position: 'top',
            fontSize: 10,
            formatter: function (param) {
              return Number(param.value).toFixed(2)
            }
          },
          itemStyle: {
            color: function (param) {
              if (param.value < 80) {
                return '#FD5E51'
              } else {
                return '#516FFF'
              }
            }

          }
        }],
        grid: {
          top: '55',
          bottom: '40',
          left: '60',
          right: '30'
        }
      }
      let medicalCostChart = echarts.getInstanceByDom(document.getElementById('medicalCostChart'))
      if (medicalCostChart) {
        medicalCostChart.clear()
      } else {
        medicalCostChart = echarts.init(document.getElementById('medicalCostChart'))
      }
      medicalCostChart.setOption(option)
      window.addEventListener('resize', () => {
        medicalCostChart.resize()
      })
      return medicalCostChart
    },
    queryDeptNum (row) {
      this.$router.push({ path: '/common/queryDipDeptDetail',
        query: {
          queryDipGroupCode: row.dipCodg,
          queryDipGroupName: row.dipName,
          cy_start_date: this.cy_start_date,
          cy_end_date: this.cy_end_date
        }
      })
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSelect (item) {
      this.listQuery.queryDipGroup = item.dipCodg
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.listQuery.pageNum = 1
      if (this.listQuery.b16c) {
        this.deptName = document.getElementById('b13c').getElementsByTagName('input')[0].value
      } else {
        this.deptName = null
      }
      // this.clearRouteQuery()
      this.getList()
      this.getGroupNumTop10Info()
      this.getGroupCostTop10Info()
      this.getMedicalCostInfo()
    },
    clearRouteQuery () {
      if (this.$route.query) {
        this.$router.push({ query: {} }).catch(() => {})
      }
    },
    // 下转
    queryDetails (row) {
      this.$router.push({
        path: '/auliManage/caseCompar',
        query: {
          id: row.id,
          cy_start_date: this.cy_start_date,
          cy_end_date: this.cy_end_date,
          dipGroup: row.dipCodg,
          type: '1'
        }
      })
    },
    // 重置页面
    handleResetSearch () {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.getDataIsuue()
    },
    exportExcel () {
      let tableId = 'dipGroupTable'
      let fileName = 'DIP分组控费'
      elExportExcel(tableId, fileName)
    },
    refresh () {
      this.reload()
      this.clearRouteQuery()
    }
  }
}
</script>
<style scoped>
  /deep/ .el-table__body td {
    padding: 0;
    height: 32px;
  }
  /deep/ .el-scrollbar__wrap {
    max-height: 450px;
  }
  /deep/ .el-autocomplete-suggestion li {
    line-height: 27px;
  }
  .code {
    font-size: 12px;
    color: #000000;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .name {
    font-size: 10px;
    color: #9b9b9b;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  /deep/ .el-form-item__label{
    font-size: 12px;
  }
  /deep/.el-form--inline .el-form-item__label{
    float:left;
  }
  /deep/.el-input__prefix{
    left:0px;
  }
  /deep/.el-input--prefix .el-input__inner{
    padding-right: 0px;
  }
  /deep/.el-input__inner{
    font-size: 10px;
  }
  /deep/.el-input {
    width: 80px;
  }
  /deep/ .el-checkbox{
    margin-right:15px;
  }
  /deep/ .el-checkbox__label{
    font-size: 12px;
    padding-left:5px;
  }
  /*时间样式设置*/
  /deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner{
    width: 200px;
  }
  .selectInputWidth{
    width: 100px;
  }
  .autoSelectInputWidth{
    width: 130px;
  }

  /*自定义样式*/
  .el-icon-caret-bottom{
    color:#FF0000;
  }
  .el-icon-caret-top{
    color:#00CC00;
  }
  .lowCost{
    font-size: 13px;
    font-weight: bold;
    color:#A9A9A9;
  }
  .stableCost{
    font-size: 13px;
    font-weight: bold;
    color:#00CC00;
  }
  .highCost{
    font-size: 13px;
    font-weight: bold;
    color:#FF0000;
  }
</style>
