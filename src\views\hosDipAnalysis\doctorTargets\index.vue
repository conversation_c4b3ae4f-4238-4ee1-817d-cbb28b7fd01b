<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-dept
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="医生指标"
             :container="true"
             @query="handleSearchList" @reset="refresh">
      <template slot="extendFormItems">
        <el-form-item label="医生姓名" class="som-el-form-item-margin-left" v-if="!this.$somms.hasDoctorRole()">
          <el-select v-model="listQuery.drCodg" placeholder="请选择医生姓名"  @change="changeSelectDoctor" clearable>
            <el-option
              v-for="(item, index) in doctorNameList"
              :key="index"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </template>

      <template slot="buttons">
        <el-popconfirm
          confirm-button-text='确定'
          cancel-button-text='导出全部'
          icon="el-icon-info"
          icon-color="red"
          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>

      <template slot="buttonsMiddle">
        <el-radio-group v-model="listQuery.queryType" size="mini" @change="changeSelectQueryType">
          <el-radio-button :label="1" v-if="this.$somms.hasHosRole()">按科室查询</el-radio-button>
          <el-radio-button :label="2" v-if="!this.$somms.hasDoctorRole()">按医生查询</el-radio-button>
        </el-radio-group>
      </template>

      <template slot="containerContent">
        <div style="height:33%">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="24" style="position:relative;height: 100%">
              <div  class="rankSelect">
                <span style="font-size: 12px;">排名指标：</span>
                <el-select v-model="rankSelect" placeholder="请选择排名指标" size="mini" @change="changeSelectDoctorRank">
                  <el-option
                    v-for="item in rankList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </div>
              <div id="doctorChart" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="height: 66%">
          <el-table ref="dipDoctorIndexTable"
                    id="doctorTable"
                    size="mini"
                    height="100%"
                    :header-cell-style="{'text-align':'center'}"
                    stripe
                    :data="list"
                    v-loading="listLoading"
                    border>
            <el-table-column fixed
                             label="序号"
                             type="index"
                             align="right"
            >
            </el-table-column>
            <el-table-column  label="出院科室编码" prop="priOutHosDeptCode" v-if="false">
              <template slot-scope="scope">{{scope.row.priOutHosDeptCode | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column fixed label="出院科室名称" prop="priOutHosDeptName" align="left" :show-overflow-tooltip="true" width="130" v-if="showDept">
              <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column  label="医生所属科室" prop="priOutHosDeptName" align="left"  width="130" :show-overflow-tooltip="true" v-if="showDoctorDepts">
              <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column  label="医生编码" prop="drCodg" align="left">
              <template slot-scope="scope">{{scope.row.drCodg | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column fixed label="医生姓名" prop="drName" align="left" :show-overflow-tooltip="true" width="100">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.medicalRecordNum)>0" class='skip' @click="queryMedicalTotalNum(scope.row)">
                  {{scope.row.drName | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.medicalRecordNum)==0" style="color:#000000">
                  {{scope.row.drName | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="病案数" prop="medicalRecordNum" align="right" width="70" >
              <template slot-scope="scope">
                <div v-if="Number(scope.row.medicalRecordNum)>0" class='skip' @click="queryMedicalTotalNum(scope.row)">
                  {{scope.row.medicalRecordNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.medicalRecordNum)==0" style="color:#000000">
                  {{scope.row.medicalRecordNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="入组病案数" prop="groupNum"  align="right" width="100" >
              <template slot-scope="scope">
                <div v-if="Number(scope.row.groupNum)>0" class='skip' @click="queryGroupNum(scope.row)">
                  {{scope.row.groupNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.groupNum)==0" style="color:#000000">
                  {{scope.row.groupNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="未入组病案数" prop="noGroupNum"  align="right" width="100" >
              <template slot-scope="scope">
                <div v-if="Number(scope.row.noGroupNum)>0" class='skip' @click="queryNoGroupNum(scope.row)">
                  {{scope.row.noGroupNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.noGroupNum)==0" style="color:#000000">
                  {{scope.row.noGroupNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="DIP分组组数" prop="dipGroupNum" align="right" width="100" >
              <template slot-scope="scope">
                <div v-if="Number(scope.row.dipGroupNum)>0" class='skip' @click="queryDipGroupNum(scope.row)">
                  {{scope.row.dipGroupNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.dipGroupNum)==0" style="color:#000000">
                  {{scope.row.dipGroupNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="DIP总权重（本院）" prop="totalAreaWeight"  align="right" width="135"  >
              <template slot-scope="scope">{{scope.row.totalAreaWeight | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="DIP总权重（区域）" prop="totalHosWeight" align="right" width="135" >
              <template slot-scope="scope">{{scope.row.totalHosWeight | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院日" prop="avgDays" align="right" width="100" >
              <template slot-scope="scope">{{scope.row.avgDays | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院费用" prop="avgCost" align="right" fixed="right" width="120" >
              <template slot-scope="scope">{{scope.row.avgCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="时间消耗指数" prop="timeIndex" align="right" width="100" >
              <template slot-scope="scope">{{scope.row.timeIndex | formatIsEmpty}}</template>no
            </el-table-column>
            <el-table-column label="费用消耗指数" prop="costIndex" align="right" width="100" >
              <template slot-scope="scope">{{scope.row.costIndex | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="药品费占比" prop="medicalCostRate" align="right" width="110"   >
              <template slot-scope="scope">{{scope.row.medicalCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="耗材费占比" prop="materialCostRate" align="right" width="115"  >
              <template slot-scope="scope">{{scope.row.materialCostRate | formatIsEmpty}}</template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, querySelectTreeAndSelectList, queryMedicalDoctorSelectInput } from '@/api/common/drgCommon'
import { fetchList as queryPageData, getCountInfo } from '@/api/dipBusiness/dipDoctorIndex'
import { elExportExcel } from '@/utils/exportExcel'
import echarts from 'echarts'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  doctorType: null,
  drCodg: null,
  b16c: null,
  queryType: '1',
  cysj: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date
}
export default {
  name: 'doctorTargets',
  components: { },
  inject: ['reload'],
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      doctorNameList: null, // 医生姓名下拉
      listLoading: true,
      list: null,
      total: null,
      cy_start_date: null,
      cy_end_date: null,
      listQuery: { ...Object.assign({}, defaultListQuery), queryType: '1' },
      submitListQuery: Object.assign({}, defaultListQuery),
      queryType: '1',
      showDoctorDepts: false,
      showDept: true,
      rankSelect: '3', // 默认DIP组数排名
      deptName: null,
      tableHeight: 0,
      b16c: null,
      rankList: [
        { value: '0', label: '总结算病案数排名' },
        { value: '1', label: '入组病案数排名' },
        { value: '2', label: '未组病案数' },
        { value: '3', label: 'DIP分组组数排名' },
        // { value: '4',label: '总权重排名'},
        { value: '5', label: '总平均住院日排名' },
        { value: '6', label: '入组平均住院日排名' },
        { value: '7', label: '总平均住院费用排名' },
        { value: '8', label: '入组平均住院费用排名' },
        { value: '9', label: '时间消耗指数排名' },
        { value: '10', label: '费用消耗指数排名' },
        { value: '11', label: '药品费排名' },
        { value: '12', label: '耗材费排名' }
      ],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  watch: {
    b16c: function () {
      if (this.listQuery.b16c) {
        this.deptName = document.getElementById('b13c').getElementsByTagName('input')[0].value
      } else {
        this.deptName = null
      }
      this.getList(this.listQuery.b16c)
      this.getCount()
      this.getDoctor()
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatDocotrNameIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '未填写'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    if (!this.$somms.hasHosRole()) {
      this.listQuery.queryType = '2'
      this.changeSelectQueryType(2)
    }
    this.$nextTick(() => {
      // this.$refs.dipDoctorIndexTable.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.dipDoctorIndexTable.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.dipDoctorIndexTable.$el.offsetTop - 35
      }
    })
    this.switchType()
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'DOCTORTYPE')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    switchType () {
      if (!this.$somms.hasDoctorRole()) {
        this.queryType = 1
      } else {
        this.queryType = 2
      }
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
        this.getCount()
        this.getDoctor()
      })
    },
    getDoctor () {
      const params = {
        b16c: this.listQuery.deptCode,
        doctorType: this.listQuery.doctorType,
        type: 1,
        begnDate: this.listQuery.begnDate,
        expiDate: this.listQuery.expiDate
      }
      queryMedicalDoctorSelectInput(params).then((response) => {
        this.doctorNameList = response.data
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.doctorType = this.listQuery.doctorType
      this.submitListQuery.drCodg = this.listQuery.drCodg
      this.submitListQuery.queryType = this.listQuery.queryType
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.dipDoctorIndexTable.$children, document.getElementById('doctorTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DIP医生指标')
    },
    getCount () {
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.doctorType = this.listQuery.doctorType
      this.submitListQuery.drCodg = this.listQuery.drCodg
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.queryType = this.listQuery.queryType
      getCountInfo(this.submitListQuery).then(response => {
        let result = response.data
        if (result.length > 0) {
          switch (this.rankSelect) {
            case '0':this.getMedicalNum(result); break
            case '1':this.getInGroupNum(result); break
            case '2':this.getNotGroupNum(result); break
            case '3':this.getDipGroupNum(result); break
              // case "4":this.getDipGroupWeight(result); break;
            case '5':this.getAvgDays(result); break
            case '6':this.getInGroupAvgDays(result); break
            case '7':this.getAvgCost(result); break
            case '8':this.getIngroupAvgCost(result); break
            case '9':this.getTimeIndex(result); break
            case '10':this.getCostIndex(result); break
            case '11':this.getMedicalCost(result); break
            case '12':this.getMaterialCost(result); break
            default:this.getDipGroupNum(result)
          }
        }
      })
    },
    getMedicalNum (result) {
      result.sort(function (o1, o2) {
        return Number(o2.totalMedicalRecordNum) - Number(o1.totalMedicalRecordNum)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生名称
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].totalMedicalRecordNum)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].totalMedicalRecordNumRate) // 占比或者标杆值
        }
      }
      let legendData = ['结算病案总数', '相对全院结算病案总数占比']
      let profttl = '医生结算病案总数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = '总病案数'
      let tool2 = '总病案数占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getInGroupNum (result) {
      result.sort(function (o1, o2) {
        return Number(o2.inGroupMedicalNum) - Number(o1.inGroupMedicalNum)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生名称
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].inGroupMedicalNum)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].inGroupMedicalNumRate) // 占比或者标杆值
        }
      }
      let legendData = ['入组病案数', '相对全院入组病案数占比']
      let profttl = '医生入组病案数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = '入组病案数'
      let tool2 = '入组病案数占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getNotGroupNum (result) {
      result.sort(function (o1, o2) {
        return Number(o2.notGroupMedicalNum) - Number(o1.notGroupMedicalNum)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生名称
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].notGroupMedicalNum)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].notGroupMedicalNumRate) // 占比或者标杆值
        }
      }
      let legendData = ['未组病案数', '相对全院未组病案数占比']
      let profttl = '医生未组病案数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = '未组病案数'
      let tool2 = '未组病案数占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getDipGroupNum (result) {
      result.sort(function (o1, o2) {
        return Number(o2.dipGroupNum) - Number(o1.dipGroupNum)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].dipGroupNum)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].dipGroupNumRate) // 占比或者标杆值
        }
      }
      let legendData = ['DIP分组组数', '相对全院DIP分组组数占比']
      let profttl = '医生DIP分组组数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = 'DIP分组组数'
      let tool2 = 'DIP分组组数占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getDipGroupWeight (result) {
      result.sort(function (o1, o2) {
        return Number(o2.totalDipGroupWeight) - Number(o1.totalDipGroupWeight)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].totalDipGroupWeight)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].totalDipGroupWeightRate) // 占比或者标杆值
        }
      }
      let legendData = ['医生权重', '相对全院总权重占比']
      let profttl = '医生权重排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = '权重'
      let tool2 = '权重占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getAvgDays (result) {
      result.sort(function (o1, o2) {
        return Number(o2.avgDays) - Number(o1.avgDays)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].avgDays)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosAvgDays) // 占比或者标杆值
        }
      }
      let legendData = ['平均住院日', '全院平均住院日']
      let profttl = '医生接纳病人平均住院日排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '平均住院日'
      let tool2 = '全院平均住院日'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getInGroupAvgDays (result) {
      result.sort(function (o1, o2) {
        return Number(o2.inGroupAvgDays) - Number(o1.inGroupAvgDays)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].inGroupAvgDays)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosInGroupAvgDays) // 占比或者标杆值
        }
      }
      let legendData = ['入组平均住院日', '全院入组平均住院日']
      let profttl = '医生接纳病人入组平均住院日排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '入组平均住院日'
      let tool2 = '全院入组平均住院日'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getAvgCost (result) {
      result.sort(function (o1, o2) {
        return Number(o2.avgCost) - Number(o1.avgCost)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].avgCost)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosAvgCost) // 占比或者标杆值
        }
      }
      let legendData = ['平均住院费用', '全院平均住院费用']
      let profttl = '医生接纳病人平均住院费用排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '平均住院费用'
      let tool2 = '全院平均住院费用'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getIngroupAvgCost (result) {
      result.sort(function (o1, o2) {
        return Number(o2.inGroupAvgCost) - Number(o1.inGroupAvgCost)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].inGroupAvgCost)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosInGroupAvgCost) // 占比或者标杆值
        }
      }
      let legendData = ['入组平均住院费用', '全院入组平均住院费用']
      let profttl = '医生接纳病人入组平均住院费用排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '入组平均住院费用'
      let tool2 = '全院入组平均住院费用'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getTimeIndex (result) {
      result.sort(function (o1, o2) {
        return Number(o2.timeIndex) - Number(o1.timeIndex)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].timeIndex)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosTimeIndex) // 占比或者标杆值
        }
      }
      let legendData = ['时间消耗指数', '全院时间消耗指数']
      let profttl = '医生接纳病人时间消耗指数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '时间消耗指数'
      let tool2 = '全院时间消耗指数'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getCostIndex (result) {
      result.sort(function (o1, o2) {
        return Number(o2.costIndex) - Number(o1.costIndex)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].costIndex)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosCostIndex) // 占比或者标杆值
        }
      }
      let legendData = ['费用消耗指数', '全院费用消耗指数']
      let profttl = '医生接纳病人费用消耗指数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '费用消耗指数'
      let tool2 = '全院费用消耗指数'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getMedicalCost (result) {
      result.sort(function (o1, o2) {
        return Number(o2.avgDrugFee) - Number(o1.avgDrugFee)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].avgDrugFee)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosAvgMedicalCost) // 占比或者标杆值
        }
      }
      let legendData = ['平均药品费', '全院平均药品费']
      let profttl = '医生接纳病人平均药品费用排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '平均药品费用'
      let tool2 = '全院平均药品费用'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getMaterialCost (result) {
      result.sort(function (o1, o2) {
        return Number(o2.avgMcsFee) - Number(o1.avgMcsFee)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].avgMcsFee)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosAvgMaterialCost) // 占比或者标杆值
        }
      }
      let legendData = ['平均耗材费', '全院平均耗材费用']
      let profttl = '医生接纳病人平均耗材费用排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '平均耗材费用'
      let tool2 = '全院平均耗材费用'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    // 下转详情点击
    queryDipGroupNum (row) {
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({
          path: '/common/queryDrgDetail',
          query: {
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            drCodg: row.drCodg,
            drName: row.drName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({
          path: '/hosDipAnalysis/groupControlFee',
          query: {
            priOutHosDeptCode: this.submitListQuery.b16c,
            drCodg: row.drCodg,
            drName: row.drName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag
          }
        })
      }
    },
    queryMedicalTotalNum (row) {
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({
          path: '/hosDipAnalysis/groupControlFee',
          query: {
            priOutHosDeptCode: row.priOutHosDeptCode,
            drCodg: row.drCodg,
            drName: row.drName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({
          path: '/hosDipAnalysis/groupControlFee',
          query: {
            priOutHosDeptCode: this.submitListQuery.b16c,
            drCodg: row.drCodg,
            drName: row.drName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag
          }
        })
      }
    },
    queryGroupNum (row) {
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({
          path: '/hosDipAnalysis/groupControlFee',
          query: {
            priOutHosDeptCode: row.priOutHosDeptCode,
            drCodg: row.drCodg,
            drName: row.drName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            grpStas: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({
          path: '/hosDipAnalysis/groupControlFee',
          query: {
            priOutHosDeptCode: this.submitListQuery.b16c,
            drCodg: row.drCodg,
            drName: row.drName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            grpStas: '1'
          }
        })
      }
    },
    queryNoGroupNum (row) {
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({
          path: '/hosDipAnalysis/groupControlFee',
          query: {
            priOutHosDeptCode: row.priOutHosDeptCode,
            drCodg: row.drCodg,
            drName: row.drName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            grpStas: '0'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({
          path: '/hosDipAnalysis/groupControlFee',
          query: {
            priOutHosDeptCode: this.submitListQuery.b16c,
            drCodg: row.drCodg,
            drName: row.drName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            grpStas: '0'
          }
        })
      }
    },
    changeSelectDoctorRank (value) {
      this.rankSelect = value
      let doctorChart = echarts.getInstanceByDom(document.getElementById('doctorChart'))
      doctorChart.clear()
      this.getCount()
    },
    changeSelectQueryType (value) {
      if (value == 1) {
        this.showDoctorDepts = false
        this.showDept = true
      } else if (value == 2) {
        this.showDoctorDepts = true
        this.showDept = false
      }
      this.handleSearchList()
    },
    changeSelectDoctorType () {
      this.getDoctor()
      this.getList()
      this.getCount()
    },
    changeSelectDoctor () {
      this.getList()
      this.getCount()
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
      this.getCount()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.listQuery.pageNum = 1
      if (this.listQuery.b16c) {
        this.deptName = document.getElementById('b13c').getElementsByTagName('input')[0].value
      } else {
        this.deptName = null
      }
      this.getList()
      this.getCount()
    },
    getChart (barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, flag) {
      let option = {
        title: [{ text: profttl, left: '20', top: '5', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }],
        tooltip: {
          trigger: 'item',
          formatter: function (param) {
            let str = '医生姓名：' + xAxisData[param.dataIndex] + '</br>' +
                tool1 + '：' + barData[param.dataIndex] + yLeftUnit + '</br>'
            if (flag == 1) {
              str = str + tool2 + '：' + lineData[param.dataIndex] + '%'
            }
            if (flag == 0) {
              str = str + tool2 + '：' + lineData[param.dataIndex]
            }
            return str
          }
        },
        legend: [{
          data: legendData,
          top: '5',
          left: 'center'
        }],
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            interval: 0,
            fontSize: 10,
            rotate: 40,
            formatter: function (value) {
              return (value.length > 4 ? (value.slice(0, 4) + '..') : value)
            }
          }
        },
        yAxis: [
          { type: 'value',
            position: 'left',
            name: yLeftUnit,
            axisLabel: {
              formatter: function (value) {
                if (value > 10000) {
                  return (Number(value) / 10000).toFixed(1) + '万'
                } else {
                  return Number(value).toFixed(1)
                }
              }
            }
          },
          { type: 'value', position: 'right', name: '单位：' + yRightUnit, max: '100', splitLine: { show: false } }
        ],
        series: [{
          name: legendData[0],
          data: barData,
          type: 'bar',
          color: 'red',
          yAxisIndex: 0,
          label: {
            show: true,
            fontSize: 9,
            position: 'top',
            formatter: function (param) {
              if (param.value > 10000) {
                return (Number(param.value) / 10000).toFixed(1) + '万'
              } else {
                return Number(param.value).toFixed(2)
              }
            }
          },
          itemStyle: {
            color: function (param) {
              if (param.dataIndex < 10) {
                return 'rgba(36,185,179,0.7)'
              } else {
                return 'rgba(40,138,242,0.7)'
              }
            }
          }
        },
        {
          name: legendData[1],
          data: lineData,
          type: 'line',
          symbol: 'circle',
          yAxisIndex: flag,
          label: {
            show: true,
            position: 'top',
            fontSize: 10,
            formatter: function (param) {
              return Number(param.value).toFixed(2)
            }
          },
          itemStyle: {
            color: function (param) {
              if (param.value < 80) {
                return 'rgb(253,94,81)'
              } else {
                return 'rgb(55,88,255)'
              }
            }

          }
        }],
        grid: {
          top: '55',
          bottom: '40',
          left: '60',
          right: '30'
        }
      }
      let doctorChart = echarts.getInstanceByDom(document.getElementById('doctorChart'))
      if (doctorChart) {
        doctorChart.clear()
      } else {
        doctorChart = echarts.init(document.getElementById('doctorChart'))
      }
      doctorChart.setOption(option)
      window.addEventListener('resize', () => {
        doctorChart.resize()
      })
      return doctorChart
    },
    exportExcel () {
      let tableId = 'doctorTable'
      let fileName = 'DIP医生指标）'
      elExportExcel(tableId, fileName)
    },
    refresh () {
      this.reload()
    }
  }
}
</script>
<style scoped>
  /deep/ .el-table__body td {
    padding: 0;
    height: 32px;
  }
  /deep/ .el-scrollbar__wrap {
    max-height: 450px;
  }
  /deep/ .el-autocomplete-suggestion li {
    line-height: 27px;
  }
  .code {
    font-size: 12px;
    color: #000000;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .name {
    font-size: 10px;
    color: #9b9b9b;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  /deep/ .el-form-item__label{
    font-size: 12px;
  }
  /deep/.el-form--inline .el-form-item__label{
    float:left;
  }
  /deep/.el-input__prefix{
    left:0px;
  }
  /deep/.el-input--prefix .el-input__inner{
    padding-right: 0px;
  }
  /deep/.el-input__inner{
    font-size: 10px;
  }
  /deep/.el-input {
    width: 80px;
  }
  /deep/ .el-checkbox{
    margin-right:15px;
  }
  /deep/ .el-checkbox__label{
    font-size: 12px;
    padding-left:5px;
  }
  /*时间样式设置*/
  /deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner{
    width: 200px;
  }
  /*自定义样式*/
  .rankSelect{
    position:absolute;
    z-index:1000;
    right:5px;
  }
</style>
