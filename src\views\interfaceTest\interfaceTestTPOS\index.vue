<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             headerTitle="查询条件"
             contentTitle="接口测试列表"
             @query="queryData">

      <template #extendFormItems>
        <el-form-item label="筛选条件" prop="condition">
          <el-input v-model="queryForm.condition" placeholder="请输入唯一ID"/>
        </el-form-item>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <el-tabs v-model="activeTab" @tab-click="tabClick" style="overflow: hidden;height: 100%">
          <el-tab-pane v-for="(item, idx) in tabs" :key="idx" :label="item.label" :name="item.name" style="height: 100%">
            <div class="int-test-wrap">
              <div class="int-test-wrap-left">
                <el-table
                  :data="patientTableData"
                  height="100%"
                  highlight-current-row
                  :row-style="{ cursor: 'pointer' }"
                  @current-change="patientRowChange">
                  <el-table-column v-for="(item,idx) in patientTableColumns"
                                   :key="idx"
                                   :prop="item.prop"
                                   :label="item.label"
                                   :width="item.width"
                                   :show-overflow-tooltip="true" >
                  </el-table-column>
                </el-table>
              </div>
              <div class="int-test-wrap-right">
                <el-tabs v-model="activeDetailTab" style="height: 100%">
                  <el-tab-pane v-for="(item, idx) in detailTabs" :key="idx" :label="item.label" :name="item.name" style="height: 100%;overflow: auto">
                    <div v-for="(child, childIdx) in item.data" :key="'a' + childIdx" style="font-size: 14px">
                      {{ child.errAbnInfo }}
                      <el-divider />
                    </div>
<!--                    <el-collapse v-model="activeNames">-->
<!--                      <el-collapse-item v-for="(child, childIdx) in item.data" :key="'a' + childIdx" :title="child.errAbnInfo" :name="child.name">-->
<!--                      </el-collapse-item>-->
<!--                    </el-collapse>-->
                  </el-tab-pane>
                </el-tabs>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryValLog } from '@/api/interfaceTest/interfaceTestRule'

const patientTableColumns = [
  { label: '交易流水号', prop: 'trnsSn', width: '200' },
  { label: '错误信息', prop: 'errAbnInfo' },
  { label: '校验时间', prop: 'crteTime' }
]
export default {
  name: 'interfaceTestTPOS',
  data: () => ({
    queryForm: {
      condition: ''
    },
    activeTab: '',
    tabs: [],
    patientTableData: [],
    patientTableColumns,
    activeDetailTab: '',
    detailTabs: [],
    activeNames: []
  }),
  mounted () {
    let iftts = this.$somms.getDictValue('ITFT_NO')
    iftts.forEach(itft => {
      if (!itft.value.includes('-')) {
        this.tabs.push({
          name: itft.value, label: itft.label
        })
      }
    })
    if (this.tabs.length > 0) {
      this.activeTab = this.tabs[0].name
    }
  },
  methods: {
    queryData () {
      queryValLog({ ownIft: this.activeTab }).then(res => {
        this.patientTableData = res.data
      })
    },
    tabClick () {
      this.queryData()
    },
    patientRowChange (currentRow, oldCurrentRow) {
      this.detailTabs = []
      this.activeNames = []
      let details = currentRow.details
      let iftts = this.$somms.getDictValue('ITFT_NO')
      let detailKeys = Object.keys(details)
      iftts.forEach(itft => {
        if (detailKeys.includes(itft.value)) {
          this.detailTabs.push({ name: itft.value, label: itft.label, data: details[itft.value] })
          this.activeNames.push(itft.value)
        }
      })
      if (this.detailTabs.length > 0) {
        this.activeDetailTab = this.detailTabs[0].name
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.int-test-wrap{
  display: flex;
  width: 100%;
  height: 100%;

  &-left{
    width: 60%;
    height: 100%;
  }

  &-right{
    width: 40%;
    height: 100%;
  }
}

/deep/ .el-tabs__content{
  height: 90%;
}
</style>
