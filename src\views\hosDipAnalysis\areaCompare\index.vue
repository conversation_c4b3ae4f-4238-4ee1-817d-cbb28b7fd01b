<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-hos-dept
             show-dip
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="DIP知识库"
             :container="true"
             :extendFormIndex="[1]"
             @query="handleSearchList" @reset="refresh">
      <template slot="extendFormItems">
        <el-form-item label="年份">
          <el-date-picker
            v-model="listQuery.year"
            align="left"
            style="width: 100%"
            type="year"
            value-format="yyyy"
            @change="queryData"
            :clearable="false"
            placeholder="选择年份">
          </el-date-picker>
        </el-form-item>
      </template>
      <template slot="buttons">
        <el-popconfirm
          confirm-button-text='确定'
          cancel-button-text='导出全部'
          icon="el-icon-info"
          icon-color="red"
          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>
      <template slot="containerContent">
        <div style="height:25%">
          <el-row :gutter="2" style="height: 100%">
            <el-col :span="8" style="height: 100%">
              <div id="dipGroupCoverRageChart" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
            <el-col :span="16" style="height: 100%">
              <div id="groupMonthChart" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="height: 74%">
          <el-table ref="dipKnowledgeBaseTable"
                    id="dkbTable"
                    size="mini"
                    height="100%"
                    :data="list"
                    v-loading="listLoading"
                    border>
            <el-table-column fixed
                             label="序号"
                             type="index"
                             align="right">
            </el-table-column>
            <el-table-column label="DIP分组编码" prop="dipCodg" fixed align="left" width="120" class='skip' @click="queryMedicalTotalNum(scope.row)" :show-overflow-tooltip="true" >
              <template slot-scope="scope">{{scope.row.dipCodg | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="DIP分组名称" prop="dipName"  align="left" width="200" class='skip' @click="queryMedicalTotalNum(scope.row)" :show-overflow-tooltip="true" >
              <template slot-scope="scope">{{scope.row.dipName | formatIsEmpty}}</template>
            </el-table-column>
            <drg-table-column label="辅助目录" align="left" prop="isUsedAsstList" dicType="AD" />
            <el-table-column label="年龄段" prop="asstListAgeGrp" align="left" width="50" :show-overflow-tooltip="true" >
              <template slot-scope="scope">{{scope.row.asstListAgeGrp | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="疾病严重程度" prop="asstListDiseSevDeg" align="left" width="50" :show-overflow-tooltip="true" >
              <template slot-scope="scope">{{scope.row.asstListDiseSevDeg | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="肿瘤严重程度" prop="asstListTmorSevDeg" align="left" width="50" :show-overflow-tooltip="true" >
              <template slot-scope="scope">{{scope.row.asstListTmorSevDeg | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="病案数" prop="medcasVal" align="right" width="70">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.medcasVal)>0" class='skip' @click="queryMedicalTotalNum(scope.row)">
                  {{scope.row.medcasVal | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.medcasVal)==0" style="color:#000000">
                  {{scope.row.medcasVal | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="科室数" prop="deptNum" align="right" width="65">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.deptNum)>0" class='skip' @click="queryMedicalTotalNum(scope.row)">
                  {{scope.row.deptNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.deptNum)==0" style="color:#000000">
                  {{scope.row.deptNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="DIP权重（区域）" prop="areaWeight" align="right" width="80"  >
              <template slot-scope="scope">{{scope.row.areaWeight | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="DIP权重（本院）" prop="hosWeight"  align="right" width="80" >
              <template slot-scope="scope">{{scope.row.hosWeight | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院费用（区域）" prop="cityAvgCost" align="right" fixed="right" width="110" >
              <template slot-scope="scope">{{scope.row.cityAvgCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院费用（本院）" prop="hosAvgCost" align="right" fixed="right" width="110" >
              <template slot-scope="scope">{{scope.row.hosAvgCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院费用（差值）" prop="costDifference" align="right" fixed="right" width="110" >
              <template slot-scope="scope">
                <div v-if="scope.row.hosAvgCost==null||scope.row.hosAvgCost==''||scope.row.hosAvgCost==0||scope.row.cityAvgCost==null||scope.row.cityAvgCost==''||scope.row.cityAvgCost==0">
                  -
                </div>
                <div v-else-if="Number(scope.row.hosAvgCost)>Number(scope.row.cityAvgCost)">
                  <!--{{scope.row.hosAvgCost | formatIsEmpty}}&emsp;-->
                  <i class="el-icon-caret-bottom"></i>
                  <span style="font-size: 10px;color:#FF0000;font-weight: bold">
                {{(Number(scope.row.cityAvgCost)-Number(scope.row.hosAvgCost)).toFixed(2)}}
              </span>
                </div>
                <div v-else-if="Number(scope.row.hosAvgCost)<=Number(scope.row.cityAvgCost)">
                  <!--{{scope.row.hosAvgCost | formatIsEmpty}}&emsp;-->
                  <i class="el-icon-caret-top"></i>
                  <span style="font-size: 10px;color:#00CC00;font-weight: bold">
                +{{(Number(scope.row.cityAvgCost)-Number(scope.row.hosAvgCost)).toFixed(2)}}
              </span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="平均住院日（区域）" prop="cityAvgDays" align="right" width="100" >
              <template slot-scope="scope">{{scope.row.cityAvgDays | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院日（本院）" prop="hosAvgDays" align="right" width="100" >
              <template slot-scope="scope">{{scope.row.hosAvgDays | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院日（差值）" prop="dayDifference" align="right" width="100" >
              <template slot-scope="scope">
                <div v-if="scope.row.hosAvgDays==null||scope.row.hosAvgDays==''||scope.row.hosAvgDays==0||scope.row.cityAvgDays==null||scope.row.cityAvgDays==''||scope.row.cityAvgDays==0">
                  -
                </div>
                <div v-else-if="Number(scope.row.hosAvgDays)>Number(scope.row.cityAvgDays)">
                  <!--{{scope.row.hosAvgDays | formatIsEmpty}}&emsp;-->
                  <i class="el-icon-caret-bottom"></i>
                  <span style="font-size: 10px;color:#FF0000;font-weight: bold">
                {{(Number(scope.row.cityAvgDays)-Number(scope.row.hosAvgDays)).toFixed(2)}}
              </span>
                </div>
                <div v-else-if="Number(scope.row.hosAvgDays)<=Number(scope.row.cityAvgDays)">
                  <!--{{scope.row.hosAvgDays | formatIsEmpty}}&emsp;-->
                  <i class="el-icon-caret-top"></i>
                  <span style="font-size: 10px;color:#00CC00;font-weight: bold">
                +{{(Number(scope.row.cityAvgDays)-Number(scope.row.hosAvgDays)).toFixed(2)}}
              </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="病组控费区间" prop="costControlRange" align="right" width="150" >
              <template slot-scope="scope">{{scope.row.costControlRange | formatIsEmpty}}</template>
            </el-table-column>

            <el-table-column label="住院总费用" prop="inHosTotalCost" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.inHosTotalCost }}</template>
            </el-table-column>
            <el-table-column label="综合医疗服务费" prop="comMedServfee" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.comMedServfee }}</template>
            </el-table-column>
            <el-table-column label="综合医疗服务费占比" prop="serviceCostRate" align="right" width="120"  >
              <template slot-scope="scope">{{scope.row.serviceCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="康复费" prop="rhabFee" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.rhabFee }}</template>
            </el-table-column>
            <el-table-column label="康复费占比" prop="recoverCostRate" align="right" width="120"   >
              <template slot-scope="scope">{{scope.row.recoverCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="诊断费" prop="diagFee" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.diagFee }}</template>
            </el-table-column>
            <el-table-column label="诊断费占比" prop="diagnoseCostRate" align="right" width="120"   >
              <template slot-scope="scope">{{scope.row.diagnoseCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="治疗费" prop="treatFee" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.treatFee }}</template>
            </el-table-column>
            <el-table-column label="治疗费占比" prop="treatmentCostRate" align="right" width="120"  >
              <template slot-scope="scope">{{scope.row.treatmentCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="药品费" prop="drugfee" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.drugfee }}</template>
            </el-table-column>
            <el-table-column label="药品费占比" prop="medicalCostRate" align="right" width="120"   >
              <template slot-scope="scope">{{scope.row.medicalCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="血液和血液制品类费" prop="bloodBloPro" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.bloodBloPro }}</template>
            </el-table-column>
            <el-table-column label="血液和血液制品类费占比" prop="bloodCostRate" align="right" width="120"   >
              <template slot-scope="scope">{{scope.row.bloodCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="耗材费" prop="mcsFee" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.mcsFee }}</template>
            </el-table-column>
            <el-table-column label="耗材费占比" prop="materialCostRate" align="right" width="120"    >
              <template slot-scope="scope">{{scope.row.materialCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="中医其他" prop="tcmOth" align="right" width="90"  v-if="showZy" >
              <template slot-scope="scope">{{scope.row.tcmOth }}</template>
            </el-table-column>
            <el-table-column label="中医其他占比" prop="chineseOtherRate" align="right" width="120"   v-if="showZy" >
              <template slot-scope="scope">{{scope.row.chineseOtherRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="其他费" prop="othFee" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.othFee }}</template>
            </el-table-column>
            <el-table-column label="其他费占比" prop="otherCostRate" align="right" width="120"  >
              <template slot-scope="scope">{{scope.row.otherCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="抗生素费" prop="abtFee" align="right" width="120"   >
              <template slot-scope="scope">{{scope.row.abtFee }}</template>
            </el-table-column>
            <el-table-column label="抗生素费占比" prop="antibioticCostRate" align="right" width="120"  >
              <template slot-scope="scope">{{scope.row.antibioticCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="检验费" prop="inspectFee" align="right" width="90"   >
              <template slot-scope="scope">{{scope.row.inspectFee }}</template>
            </el-table-column>
            <el-table-column label="检验费占比" prop="inspectionCostRate" align="right" width="110"   >
              <template slot-scope="scope">{{scope.row.inspectionCostRate | formatIsEmpty}}</template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { querySelectTreeAndSelectList, queryLikeDipGroupByPram, queryDataIsuue } from '@/api/common/drgCommon'
import { fetchList as queryPageData, getCountByCoverRate, getCountByMonth } from '@/api/dipBusiness/dipKnowledgeBase'
import { elExportExcel } from '@/utils/exportExcel'
import { getDefultYear } from '@/utils/date'
import echarts from 'echarts'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  year: null,
  b16c: null,
  queryDipGroup: null
}
export default {
  name: 'areaCompare',
  components: { },
  inject: ['reload'],
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      list: null,
      tempList: [],
      total: 0,
      year: null,
      queryDipGroup: null,
      showZy: false,
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0
    }
  },
  created () {
    // this.listQuery.year=getDefultYear();
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.dipKnowledgeBaseTable.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.dipKnowledgeBaseTable.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.dipKnowledgeBaseTable.$el.offsetTop - 35
      }
    })
  },
  methods: {
    handleSelectionChange (val) {
      if (val.length == 0) {
        this.tempList = []
      } else {
        this.tempList = this.list.filter(data => {
          for (const v of val) {
            if (data.dipCodg == v.dipCodg) {
              return true
            }
          }
          return false
        })
      }
    },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        if (response.data.cy_start_date) {
          this.listQuery.year = response.data.cy_start_date.toString().substring(0, 4)
        } else {
          this.listQuery.year = getDefultYear()
        };
        this.queryData()
      })
    },
    queryData () {
      // 查询数据
      this.findSelectTreeAndSelectList()
      this.getList()
      this.getCountByCover()
      this.getCountByMonthInfo()
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.year = this.listQuery.year
      this.submitListQuery.queryDipGroup = this.listQuery.dipCodg
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      queryPageData(this.submitListQuery).then(response => {
        this.list = response.data.list
        this.tempList = this.list
        this.total = response.data.total
        this.listLoading = false
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.dipKnowledgeBaseTable.$children, document.getElementById('dkbTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DIP年度详情')
    },
    getCountByCover () {
      this.listLoading = true
      this.submitListQuery.year = this.listQuery.year
      this.submitListQuery.queryDipGroup = this.listQuery.dipCodg
      this.submitListQuery.b16c = this.listQuery.deptCode
      getCountByCoverRate(this.submitListQuery).then(response => {
        this.listLoading = false
        let result = response.data
        let title1 = this.submitListQuery.year + '年区域（全市）DIP病组覆盖情况'
        let dipCoverItem = ['DIP分组覆盖', 'DIP分组未覆盖']
        let dipCoverData = [
          { value: result.dipNotCoverNum, name: 'DIP分组未覆盖' },
          { value: result.dipCoverNum, name: 'DIP分组覆盖' }
        ]
        this.leftChart('dipGroupCoverRageChart', title1, dipCoverItem, dipCoverData)
      })
    },
    getCountByMonthInfo () {
      this.listLoading = true
      this.submitListQuery.year = this.listQuery.year
      this.submitListQuery.queryDipGroup = this.listQuery.dipCodg
      this.submitListQuery.b16c = this.listQuery.deptCode
      getCountByMonth(this.submitListQuery).then(response => {
        this.listLoading = false
        let result = response.data
        if (result) {
          let month = []
          let thisYearData = []
          let lastYearData = []
          if (result) {
            for (let i = 0; i < result.length; i++) {
              month.push(result[i].month)
              thisYearData.push(result[i].thisYearDipNum)
              lastYearData.push(result[i].lastYearDipNum)
            }
          }
          this.rightChart(month, thisYearData, lastYearData)
        }
      })
    },
    // 下转详情点击
    queryMedicalTotalNum (row) {
      this.$router.push({
        path: '/hosDipAnalysis/groupControlFee',
        query: {
          priOutHosDeptCode: this.submitListQuery.b16c,
          queryDipGroup: row.dipCodg,
          cy_start_date: this.submitListQuery.year + '-01-01',
          cy_end_date: this.submitListQuery.year + '-12-31'
        }
      })
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSelect (item) {
      this.listQuery.queryDipGroup = item.dipCodg
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      if (this.listQuery.year == null) {
        // this.$message({
        //   message: '请选择年份！',
        //   type: 'warning'
        // })
      } else {
        this.listQuery.pageNum = 1
        this.getList()
        this.getCountByCover()
        this.getCountByMonthInfo()
      }
    },
    leftChart (id, profttl, dipCoverItem, dipCoverData) {
      let colors = ['rgba(38,185,181,0.7)', 'rgba(40,138,242,0.7)']
      let option = {
        title: [
          { text: profttl, left: '10', top: 1, textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b}:{c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: '10',
          // top:"15",
          top: 'center',
          itemWidth: 10,
          itemHeight: 10,
          data: dipCoverItem
        },
        series: [
          {
            type: 'pie',
            radius: '55%', // 设置饼图大小
            center: ['58%', '55%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'outside',
                formatter: '{b}\n{c}({d}%)',
                fontFamily: 'Microsoft YaHei',
                fontSize: 12,
                color: '#000000'
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % dipCoverData.length]
                }
              }
            },
            data: dipCoverData
          }
        ]
      }

      let leftChart = echarts.getInstanceByDom(document.getElementById(id))
      if (leftChart) {
        leftChart.clear()
      } else {
        leftChart = echarts.init(document.getElementById(id))
      }
      leftChart.setOption(option)
      return leftChart
    },
    rightChart (month, thisYearData, lastYearData) {
      let colors = ['rgba(38,185,181,0.7)', 'rgba(40,138,242,0.7)', 'rgba(250,93,93,0.7)', 'rgba(145,204,117,0.7)']
      let option = {
        title: [
          { text: this.submitListQuery.year + '年本期/同期DIP组数情况', left: 'left', top: 3, textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        legend: {
          itemWidth: 12,
          itemHeight: 12,
          data: ['本期', '同期']
        },
        xAxis: [
          {
            type: 'category',
            data: month,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '组数',
            axisLabel: {
              formatter: function (value) {
                if (value > 10000) {
                  return (Number(value) / 10000).toFixed(0)
                } else {
                  return Number(value).toFixed(0)
                }
              }
            }
          }
        ],
        series: [
          {
            name: '本期',
            type: 'bar',
            data: thisYearData,
            itemStyle: {
              normal: {
                color: colors[1]
              }
            }
          },
          {
            name: '同期',
            type: 'bar',
            data: lastYearData,
            itemStyle: {
              normal: {
                color: colors[2]
              }
            }
          }
        ],
        grid: {
          top: '55',
          bottom: '30',
          left: '60',
          right: '30'
        }
      }
      let groupMonthChart = echarts.getInstanceByDom(document.getElementById('groupMonthChart'))
      if (groupMonthChart) {
        groupMonthChart.clear()
      } else {
        groupMonthChart = echarts.init(document.getElementById('groupMonthChart'))
      }
      groupMonthChart.setOption(option)
      window.addEventListener('resize', () => {
        groupMonthChart.resize()
      })
      return groupMonthChart
    },
    exportExcel () {
      let tableId = 'dkbTable'
      let fileName = 'DIP年度详情'
      elExportExcel(tableId, fileName)
    },
    refresh () {
      this.reload()
    }
  }
}
</script>
<style scoped>
  /deep/ .el-table__body td {
    padding: 0;
    height: 32px;
  }
  /deep/ .el-scrollbar__wrap {
    max-height: 450px;
  }
  /deep/ .el-autocomplete-suggestion li {
    line-height: 27px;
  }
  .code {
    font-size: 12px;
    color: #000000;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .name {
    font-size: 10px;
    color: #9b9b9b;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  /deep/ .el-form-item__label{
    font-size: 12px;
  }
  /deep/.el-form--inline .el-form-item__label{
    float:left;
  }
  /deep/.el-input__prefix{
    left:0px;
  }
  /deep/.el-input--prefix .el-input__inner{
    padding-right: 0px;
  }
  /deep/.el-input__inner{
    font-size: 10px;
  }
  /deep/.el-input {
    width: 80px;
  }
  /deep/ .el-checkbox{
    margin-right:15px;
  }
  /deep/ .el-checkbox__label{
    font-size: 12px;
    padding-left:5px;
  }
  /*时间样式设置*/
  /deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner{
    width: 200px;
  }
  .selectInputWidth{
    width: 100px;
  }
  .autoSelectInputWidth{
    width: 120px;
  }

  /*自定义样式*/
  .code {
    font-size: 12px;
    color: #000000;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .name {
    font-size: 10px;
    color: #9b9b9b;
    text-overflow: ellipsis;
    overflow: hidden;
  }
</style>
