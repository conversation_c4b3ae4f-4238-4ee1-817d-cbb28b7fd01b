import request from '@/utils/request'

export function queryDiseaseKpiData (params) {
  return request({
    url: '/newDipBusinessCustomDiseaseAnalysisController/queryDiseaseKpiData',
    method: 'post',
    params: params
  })
}

export function queryDiseaseForecastData (params) {
  return request({
    url: '/newDipBusinessCustomDiseaseAnalysisController/queryDiseaseForecastData',
    method: 'post',
    params: params
  })
}

export function queryDrgDiseaseKpiData (params) {
  return request({
    url: '/newDrgBusinesCustomsDiseaseAnalysisController/queryDrgDiseaseKpiData',
    method: 'post',
    params: params
  })
}

export function queryDrgDiseaseForecastData (params) {
  return request({
    url: '/newDrgBusinesCustomsDiseaseAnalysisController/queryDrgDiseaseForecastData',
    method: 'post',
    params: params
  })
}
