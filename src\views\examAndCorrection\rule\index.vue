<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
              :show-hos-dept="{ show: this.$somms.hasHosRole()}"
              showPagination
              :totalNum="total"
              headerTitle="查询条件"
              contentTitle="元素"
              :container="true"
              :initTimeValueNotQuery="false"
              ref="somForm"
              @query="handleSearchList">

      <template slot="extendFormItems">

        <el-form-item label="场景类型" prop="ruleScenType">
          <drg-dict-select dicType="RULE_SCEN_TYPE" placeholder="请选择场景类型" v-model="listQuery.ruleScenType"
                           @change="getDataIsuue"/>
        </el-form-item>

        <el-form-item label="违规内容" prop="ruleGrpName">
          <el-input v-model="listQuery.ruleGrpName" placeholder="请输入违规内容" @change="getDataIsuue"/>
        </el-form-item>
        <el-form-item label="数据元组ID" prop="ruleDataMeta">
          <el-input v-model="listQuery.ruleDataMeta" placeholder="请输入数据元组ID" @change="getDataIsuue"/>
        </el-form-item>

        <el-form-item label="规则详细编码" prop="ruleDetlCodg">
          <el-input v-model="listQuery.ruleDetlCodg" placeholder="请输入细分组编码" @change="getDataIsuue"/>
        </el-form-item>

        <el-form-item label="规则算子" prop="opraType">
           <drg-dict-select v-model="listQuery.opraType" placeholder="请选择" dicType="OPRA_TYPE_CODE" :type="2">
              <!-- 可添加更多选项 -->
            </drg-dict-select>
        </el-form-item>
           <el-form-item label="周期约束" prop="dataGrp">
           <drg-dict-select v-model="listQuery.dataGrp" placeholder="请选择" dicType="RULE_DATA_GROUP" :type="2">
              <!-- 可添加更多选项 -->
            </drg-dict-select>
        </el-form-item>
        <el-form-item label="规则年度" prop="ruleYear">
          <el-date-picker
              v-model="listQuery.ruleYear"
              type="year"
              placeholder="请输入规则年度"
              format="yyyy"
              @change="getDataIsuue"
              value-format="yyyy">
            </el-date-picker>
          <!-- <el-input v-model="listQuery.ruleYear" placeholder="请输入规则年度" @change="getDataIsuue"/> -->
        </el-form-item>
        
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  :id="tableId"
                  size="mini"
                  stripe
                  height="100%"
                  :data="list"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>
          <el-table-column label="序号" type="index" align="right">
          </el-table-column>
          <el-table-column label="规则年度" prop="ruleYear" align="center"/>
          <el-table-column label="规则场景" prop="ruleScenType" align="center"/>
          <el-table-column label="规则类型" prop="ruleTypeName" align="center"/>
          <el-table-column label="规则详细编码" prop="ruleDetlCodg" width="120" align="center"/>
          <el-table-column label="违规内容" prop="ruleGrpName" width="300" show-overflow-tooltip align="left"/>
           <el-table-column label="规则来源" prop="ruleSouc"  align="left" show-overflow-tooltip />
          <el-table-column label="违规程度" prop="volaDeg" align="center">
            <template slot-scope="scope">
              {{ $somms.getDictValueByType(scope.row.volaDeg, 'VOLA_DEG_CODE') }}
            </template>
          </el-table-column>
          <el-table-column label="除外类型" prop="exctType" align="center">
            <template slot-scope="scope">
              {{ $somms.getDictValueByType(scope.row.exctType, 'EXCT_CONT_CODE') }}
            </template>
          </el-table-column>
          <el-table-column label="除外内容" prop="exctCont" show-overflow-tooltip align="left"/>

          <el-table-column label="规则算子" prop="opraType" align="center"/>
         
          <!--          <el-table-column label="规则算子含义" prop="opraTypeName" align="center"/>-->
          <el-table-column label="规则数据分组" align="center" prop="dataGrp" sortable='custom'>
            <template slot-scope="scope">
              {{ $somms.getDictValueByType(scope.row.dataGrp, 'DATA_TYPE_CODE') }}
            </template>
          </el-table-column>
          <el-table-column label="数据元组ID" prop="ruleDataMeta" width="120" align="center">
            <template slot-scope="scope">
              <span @click="showTupleDetail(scope.row)"
                    style="color: red; text-decoration: underline; cursor: pointer;">
                {{ scope.row.ruleDataMeta }}
                </span>
            </template>
          </el-table-column>
        </el-table>
        <!-- 元素弹窗 -->
        <el-dialog
          :visible.sync="tupleDialogVisible"
          title="违规详细信息"
          width="50%"
        >
          <el-table :data="violationTupleList" style="width: 100%" height="500">
            <!-- 序号列 -->
            <el-table-column label="序号" width="60">
              <template slot-scope="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="ruleYear" label="规则年份" width="80"></el-table-column>
            <el-table-column prop="dataGrpCode" label="数据元组ID" width="90">
            </el-table-column>
            <el-table-column prop="dataDetailCode" label="违规编码" width="80"></el-table-column>
            <el-table-column prop="dataCode" label="违规明细编码"></el-table-column>
            <el-table-column prop="dataName" label="违规明细名称"></el-table-column>

          </el-table>
          <span slot="footer" class="dialog-footer">
      <el-button @click="tupleDialogVisible = false">关闭</el-button>
    </span>
        </el-dialog>

      </template>
    </drg-form>
  </div>
</template>
<script>
import {getRuleList as queryPageData, queryTupleList} from '@/api/examCorrection/ruleAndTuples'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  ruleScenType: '1',
  dataName: null,
  dataGrpCode: null,
  dataDetailCode: null,
  dataCode: null,
  ruleYear: null,
  ruleGrpName: null,
  ruleDataMeta: null,
  opraType: null,
  ruleDetlCodg: null,
  dataGrp: null

}
export default {
  name: 'ruleTuples',
  components: {},
  inject: ['reload'],
  data() {
    return {
      //违规详情弹窗标识符
      tupleDialogVisible: false,
      //点击违规规则存储当前行数据
      selectedRowData: {},
      //点击违规规则查询到的违规元素
      violationTupleList: [],
      //点击违规规则元素查的传参
      selectTupleParam: {},
      showFlag: false,
      dialogVisible: false,
      tableId: 'dataTable',
      listLoading: true,
      list: null,
      total: null,
      dictVoList: {},
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
    }
  },

  watch: {
    list: function () {
      this.$nextTick(() => {
        if (this.$refs.settleListTable) {
          this.$refs.settleListTable.doLayout()
        }
      })
    }
  },

  mounted() {
    this.$nextTick(() => {

      this.getList()
    })
  },
  methods: {
// 点击表格列时调用的事件
    showTupleDetail(rowData) {
      this.selectedRowData = rowData;
      this.tupleDialogVisible = true;
      this.queryTupleList(); // 打开弹窗
    },
    queryTupleList() {
      this.selectTupleParam.ruleYear = this.selectedRowData.ruleYear
      this.selectTupleParam.dataGrpCode = this.selectedRowData.ruleDataMeta
      queryTupleList(this.selectTupleParam).then(response => {
        // 请求成功后，更新违规详细信息
        if (response.data.list && Array.isArray(response.data.list)) {
          this.violationTupleList = response.data.list // 假设后端返回的数据包含违规详细列表
        }
      })
    },
    getDataIsuue() {
      this.listLoading = false
      this.getList()

    },
    getList() {
      this.listLoading = true
      this.getParamByBaseQuery(this.submitListQuery, this.listQuery)
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    handleSelect(item) {
      this.listQuery.dipCodg = item.dipCodg
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList() {
      this.getList()
    },
    refresh() {
      this.reload()
    },
    getParamByBaseQuery(targetParam, sourceParam) {
      if (targetParam) {
        const keys = Object.keys(targetParam)
        // 更新参数值
        keys.forEach(key => {
          targetParam[key] = sourceParam[key]
        })
      }
    },
    getParams() {
      return this.submitListQuery
    }
  }
}
</script>
<style scoped>
/*时间样式设置*/
/deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner {
  width: 200px;
}

.autoSelectInputWidth {
  width: 178px;
}

</style>
