<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-date-range
             show-se-date-range
             header-title="查询条件"
             content-title="监管分析"
             :container="true"
             @query="query">

      <!-- 内容 -->
      <template slot="containerContent">
        <el-tabs style="height: 99%;width: 100%">
          <el-tab-pane label="病组" style="height: 100%">
            <el-table ref="elTable" height="100%">
              <el-table-column label="序号" type="index"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="诊断"></el-tab-pane>
          <el-tab-pane label="手术"></el-tab-pane>
        </el-tabs>
      </template>

    </drg-form>
  </div>
</template>

<script>

export default {
  name: 'newDrgSuperviseAnalysis',
  data: () => ({
    queryForm: {}
  }),
  methods: {
    // 查询
    query () {},
    // 获取参数
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    }
  },
  updated () {
    this.$refs.elTable.doLayout()
  }
}
</script>

<style scoped>
/deep/ .el-tabs__content {
  height: 89%;
}
</style>
