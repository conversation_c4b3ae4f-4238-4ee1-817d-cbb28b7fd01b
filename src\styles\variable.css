:root {
    --orange: #FA8000;
    --blue: #059BFF;
    --black: #1A1F25;
    --headerColor: #333333;
    --fontColor: #333333;
}

/* 最大屏 */

@media screen and (min-device-width: 1920px) {
    :root {
        --titleSize: 34px;
        --smallTitleSize: 32px;
        --biggerSmallTitleSize: 28px;
        --biggerSize: 18px;
        --textSize: 16px;
        --smallSize: 14px;
        --biggerSmallSize: 12px;
        --chartSize: 20px;
        --labelWidth: 70px;
        --titleLine: 1.8rem;
        --type: 1;
        --percent: 5;
        --headerPercent: 9
    }
}

@media screen and (min-device-width: 1660px) and (max-device-width: 1920px) {
    :root {
        --titleSize: 32px;
        --smallTitleSize: 28px;
        --biggerSmallTitleSize: 24px;
        --biggerSize: 16px;
        --textSize: 13px;
        --smallSize: 12px;
        --biggerSmallSize: 10px;
        --chartSize: 16px;
        --margin: 14px;
        --labelWidth: 70px;
        --titleLine: 1.8rem;
        --type: 2;
        --percent: 5;
        --headerPercent: 9
    }
    .scienceCenter {
        margin-top: 310px !important;
    }
    .footer {
        padding: 91px 100px 180px 100px !important;
    }
    .contact {
        width: 40%;
        margin-right: 20px;
    }
}

@media screen and (min-device-width: 1025px) and (max-device-width: 1660px) {
    :root {
        --titleSize: 26px;
        --smallTitleSize: 24px;
        --biggerSmallTitleSize: 20px;
        --biggerSize: 12px;
        --textSize: 10px;
        --smallSize: 10px;
        --biggerSmallSize: 8px;
        --chartSize: 10px;
        --labelWidth: 60px;
        --margin: 3px;
        --titleLine: 1.2rem;
        --type: 3;
        --percent: 4;
        --headerPercent: 12
    }
    .header {
        padding: 0 50px !important;
    }
    .headerTabs>div {
        width: 80px !important;
    }
    .aboutUs {
        height: 1000px !important;
    }
    .introduce {
        padding: 134px 100px !important;
    }
    .introduce .title {
        margin-bottom: 60px !important;
    }
    .download {
        margin-top: 50px !important;
    }
    .footer {
        padding: 30px 50px 80px 50px !important;
    }
    .contact {
        width: 30%;
        margin-right: 15px;
    }
    .callUs {
        font-size: 24px !important;
        margin-top: 20px;
    }
    .remarks {
        margin: 35px 0 20px 0 !important;
        font-size: 20px !important;
    }

    .phone {
        font-size: 36px !important;
    }
    .qrCodes>div {
        margin-right: 40px !important;
    }

    .scienceCenter {
        margin-top: 365px !important;
    }
}

@media screen and (max-device-width: 1025px) {
  :root {
    --titleSize: 24px;
    --smallTitleSize: 22px;
    --biggerSmallTitleSize: 18px;
    --biggerSize: 10px;
    --textSize: 10px;
    --smallSize: 10px;
    --biggerSmallSize: 8px;
    --chartSize: 10px;
    --labelWidth: 60px;
    --margin: 3px;
    --titleLine: 1.2rem;
    --type: 3;
    --percent: 4;
    --headerPercent: 12
  }
}
