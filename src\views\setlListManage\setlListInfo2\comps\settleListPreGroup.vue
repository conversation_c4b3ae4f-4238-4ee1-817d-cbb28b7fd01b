<template>
  <el-drawer
    title="1"
    :visible.sync="show"
    direction="rtl"
    @closed="closed"
    :with-header="false">
    <div v-loading="loading">
      <iframe :src="url" height="940px" width="720px" v-if="show"></iframe>
    </div>
  </el-drawer>
</template>
<script>
import { preGroup } from '@/api/medicalQuality/settleListDetail'

export default {
  props: {
    visible: [Boolean],
    form: [Object],
    disData: [Array],
    opeData: [Array],
    icuData: [Array]
  },
  data: () => ({
    url: '',
    show: false,
    loading: false
  }),
  methods: {
    // 执行预分组
    executePreGroup () {
      // this.$nextTick(() => {
      //   let params = {}
      //   params.id = this.form.id
      //   params.k00 = this.form.k00
      //   params.yljgdm = this.form.hospitalId
      //   params.hospital_id = this.form.hospitalId
      //   params.cblx = this.form.a46c ? this.form.a46c : '-1'
      //   params.username = this.form.a02
      //   params.ylfkfs = this.form.a46c
      //   params.jkkh = this.form.a47
      //   params.zycs = this.form.a49
      //   params.bah = this.form.a48
      //   params.xm = this.form.a11
      //   params.xb = this.form.a12c
      //   params.csrq = this.form.a13
      //   params.nl = this.form.a14
      //   params.gj = this.form.a15c
      //   params.bzyzsnl = this.form.a16
      //   params.xsecstz = this.form.a18
      //   params.xserytz = this.form.a17
      //   params.csd = this.form.a22
      //   params.gg = this.form.a23c
      //   params.mz = this.form.a19c
      //   params.sfzh = this.form.a20
      //   params.zy = this.form.a38c
      //   params.hy = this.form.a21c
      //   params.xzz = this.form.a26
      //   params.dh = this.form.a27
      //   params.yb1 = this.form.a28c
      //   params.hkdz = this.form.a24
      //   params.yb2 = this.form.a25c
      //   params.gzdwjdz = this.form.a29
      //   params.gzdwmc = this.form.a29n
      //   params.dwdh = this.form.a30
      //   params.yb3 = this.form.a31c
      //   params.lxrxm = this.form.a32
      //   params.gx = this.form.a33c
      //   params.dz = this.form.a34
      //   params.dh2 = this.form.a35
      //   params.rytj = this.form.b11c
      //   params.rysj = this.form.b12
      //   params.rysjs = this.form.b12s
      //   params.rykbbm = this.form.b13c
      //   params.rybf = this.form.b14n
      //   params.zkkbbm = this.form.b21c
      //   params.cysj = this.form.b15
      //   params.cysjs = this.form.b15s
      //   params.cykbbm = this.form.b16c
      //   params.cybf = this.form.b17n
      //   params.zyts = this.form.b20
      //   params.wbyy = this.form.c13n
      //   params.blh = this.form.c11
      //   params.ywgm = this.form.c25
      //   params.swhzsj = this.form.c34c
      //   params.xx = this.form.c26c
      //   params.rh = this.form.c27c
      //   params.kzr = this.form.b22n
      //   params.zrys = this.form.b23c
      //   params.zzys = this.form.b24c
      //   params.zyys = this.form.b25c
      //   params.zrhs = this.form.b26c
      //   params.jxys = this.form.b27n
      //   params.sxys = this.form.b28
      //   params.bmy = this.form.b29n
      //   params.bazl = this.form.b30c
      //   params.zkys = this.form.b31n
      //   params.zkhs = this.form.b32n
      //   params.zkrq = this.form.b33
      //   params.lyfs = this.form.b34c
      //   params.yzzy_yljg = this.form.b49
      //   params.sjhlts = this.form.b47
      //   params.ejhlts = this.form.b46
      //   params.yjhlts = this.formb45
      //   params.tjhlts = this.form.b44
      //   params.hxjsysj = this.form.c43
      //   params.ycxyyclf = this.form.d33
      //   params.yyclf = this.form.d32
      //   params.hcyyclf = this.form.d31
      //   params.xbyzlzpf = this.form.d30
      //   params.nxyzlzpf = this.form.d29
      //   params.qdblzpf = this.form.d28
      //   params.bdblzpf = this.form.d27
      //   params.blo_fee = this.form.d26
      //   params.tcmherb = this.form.d25
      //   params.tcmpat_fee = this.form.d24
      //   params.kjywf = this.form.d23x01
      //   params.west_fee = this.form.d23
      //   params.tcm_treat_fee = this.form.d22
      //   params.rhab_fee = this.form.d21
      //   params.ssf = this.form.d20x02
      //   params.maf = this.form.d20x01
      //   params.oprn_treat_fee = this.form.d20
      //   params.wlzlf = this.form.d19x01
      //   params.nsrgtrt_item_fee = this.form.d19
      //   params.clnc_diag_item_fee = this.form.d18
      //   params.rdhy_diag_fee = this.form.d17
      //   params.lab_diag_fee = this.form.d16
      //   params.cas_diag_fee = this.form.d15
      //   params.oth_fee_com = this.form.d14
      //   params.nursfee = this.form.d13
      //   params.zlczf = this.form.d12
      //   params.ylfuf = this.form.d11
      //   params.zfje = this.form.d09
      //   params.jcfyzb = 0
      //   params.jyfyzb = 0
      //   params.hcfyzb = 0
      //   params.ypfyzb = 0
      //   params.zyzfy = this.form.d01
      //   params.ryh_f = this.form.c33
      //   params.ryh_xs = this.form.c32
      //   params.ryh_t = this.form.c31
      //   params.ryq_f = this.form.c30
      //   params.ryq_xs = this.form.c29
      //   params.ryq_t = this.form.c28
      //   params.md = this.form.b37
      //   params.sfzzyjh = this.form.b36c
      //   params.wsy_yljg = this.form.b49
      //   params.yzzy_yljg = this.form.b49
      //   params.lyfs = this.form.b34c
      //   params.sbsj = this.form.a52
      //   params.hzzjlx = this.form.a53
      //   params.zyyllx = this.form.b38
      //   params.pjdm = this.form.d38
      //   params.pjhm = this.form.d39
      //   params.ywlsh = this.form.d35
      //   params.jskssj = this.form.d36
      //   params.jsjssj = this.form.d37
      //   params.ybzffs = this.form.d58
      //   params.ybjg = this.form.medInsOrgan
      //   params.ybjgjbr = this.form.medInsOrganOperator
      //   params.qdlsh = this.form.a58
      //   params.jdid = this.form.clinicId
      //   params.jsid = this.form.settlementId
      //   params.rybh = this.form.psnNo
      //   params.zzysdm = this.form.b51c
      //   params.yqjcf = this.form.d02
      //
      //   params.disLength = 0
      //   params.opeLength = 0
      //   params.icuLength = 0
      //
      //   console.log(this.disData)
      //   console.log('www')
      //   // 诊断编码
      //   if (this.disData && this.disData.length > 0) {
      //     let disCodeField = 'c06c1'
      //     let disNameField = 'c07n1'
      //     let disTypeField = 'type'
      //     let disBQField = 'c08c1'
      //     let needDisCodePrefix = 'jbdm'
      //     let needTypeNamePrefix = 'jbdmtype'
      //     let needInBQNamePrefix = 'rybq'
      //     for (let i = 0; i < this.disData.length; i++) {
      //       if (i === 0) {
      //         params[needDisCodePrefix] = this.disData[i][disCodeField]
      //         params['zyzd'] = this.disData[i][disNameField]
      //         params[needTypeNamePrefix] = this.disData[i][disTypeField]
      //         params[needInBQNamePrefix] = this.disData[i][disBQField]
      //       } else {
      //         params[needDisCodePrefix + i] = this.disData[i][disCodeField]
      //         params['qtzd' + i] = this.disData[i][disNameField]
      //         params[needTypeNamePrefix + i] = this.disData[i][disTypeField]
      //         params[needInBQNamePrefix + i] = this.disData[i][disBQField]
      //       }
      //     }
      //     params.disLength = this.disData.length
      //   }
      //
      //   // 手术编码
      //   if (this.opeData && this.opeData.length > 0) {
      //     for (let i = 0; i < this.opeData.length; i++) {
      //       params['ssjczbm' + (i + 1)] = this.opeData[i]['c35c']
      //       params['ssjczmc' + (i + 1)] = this.opeData[i]['c36n']
      //       params['ssjczrq' + (i + 1)] = this.opeData[i]['c37']
      //       params['ssjb' + (i + 1)] = this.opeData[i]['c38']
      //       params['sz' + (i + 1)] = this.opeData[i]['c39c']
      //       params['szmc' + (i + 1)] = this.opeData[i]['c39n']
      //       params['yz' + (i + 1)] = this.opeData[i]['c40']
      //       params['ez' + (i + 1)] = this.opeData[i]['c41']
      //       params['qkdj' + (i + 1)] = this.opeData[i]['c42']
      //       params['mzfs' + (i + 1)] = this.opeData[i]['c43']
      //       params['mzys' + (i + 1)] = this.opeData[i]['c44c']
      //       params['mzysmc' + (i + 1)] = this.opeData[i]['c44n']
      //       params['sskssj' + (i + 1)] = this.opeData[i]['oprnOprtBegntime']
      //       params['ssjssj' + (i + 1)] = this.opeData[i]['oprnOprtEndtime']
      //       params['mzkssj' + (i + 1)] = this.opeData[i]['anstBegntime']
      //       params['mzjssj' + (i + 1)] = this.opeData[i]['anstEndtime']
      //     }
      //     params.opeLength = this.opeData.length
      //   }
      //
      //   // ICU
      //   if (this.icuData && this.icuData.length > 0) {
      //     for (let i = 0; i < this.icuData.length; i++) {
      //       params['zzjhbflx' + (i + 1)] = this.icuData[i]['icuWardType']
      //       params['jzzjhssj' + (i + 1)] = this.icuData[i]['icuInTime']
      //       params['czzjhssj' + (i + 1)] = this.icuData[i]['icuExitTime']
      //       params['zzjhhjsc' + (i + 1)] = this.icuData[i]['icuTotalTime']
      //     }
      //     params.icuLength = this.icuData.length
      //   }
      //
      //   params.skippingValidation = true
      //   params.isWeb = '1'
      //   this.loading = true
      //   this.url = 'http://127.0.0.1:123/1.html'
      //   setTimeout(() => {}, 500)
      //   preGroup(params).then(res => {
      //     this.url = res.url
      //     this.loading = false
      //   })
      // })
      this.$nextTick(() => {
        // 诊断信息
        let diseinfo = []
        // 操作信息
        let oprninfo = []
        // ICU信息
        let icuinfo = []
        // 清单基层信息
        let baseinfo = {}
        // 扩展
        let extinfo = {}
        extinfo.pahca_dia_name = this.form.c10n
        extinfo.pahca_dia_code = this.form.c09C
        // 院前检查费
        extinfo.pre_hs_exam_fee = this.form.d02 ? this.form.d02 : '0'
        // 医保编号
        baseinfo.k00 = this.form.k00
        baseinfo.hi_no = this.form.a51
        baseinfo.id = this.form.id
        baseinfo.fixmedins_code = this.form.a01
        // 参保地
        baseinfo.insuplc = this.form.a56
        baseinfo.insurancePlace = this.form.insuplc
        baseinfo.medfee_paymtd_code = this.form.d58 ? this.form.d58 : '-1'
        baseinfo.hi_type = this.form.a54
        baseinfo.patn_ipt_cnt = this.form.a49
        baseinfo.hi_no = this.form.a51
        baseinfo.medcasno = this.form.a48
        baseinfo.psn_name = this.form.a11
        baseinfo.gend = this.form.a12c
        baseinfo.brdy = this.form.a13
        baseinfo.age = this.form.a14
        baseinfo.ntly = this.form.a15c
        baseinfo.nwb_age = this.form.a16
        baseinfo.nwb_bir_wt = this.form.a18
        baseinfo.nwb_adm_wt = this.form.a17
        baseinfo.birplc = this.form.a22
        baseinfo.napl = this.form.a23c
        baseinfo.naty = this.form.a15c
        baseinfo.certno = this.form.a20
        baseinfo.prfs = this.form.a38c
        baseinfo.mrg_stas = this.form.a21c
        baseinfo.curr_addr_poscode = this.form.a28c
        baseinfo.curr_addr = this.form.a26
        baseinfo.psn_tel = this.form.a35
        baseinfo.resd_addr = this.form.a24
        baseinfo.resd_addr_poscode = this.form.a25c
        baseinfo.empr_tel = this.form.a30
        baseinfo.empr_poscode = this.form.a31c
        baseinfo.empr_addr = this.form.a29
        baseinfo.coner_tel = this.form.a35
        baseinfo.coner_name = this.form.a32
        baseinfo.coner_addr = this.form.a34
        baseinfo.coner_rlts_code = this.form.a33c
        baseinfo.adm_way_code = this.form.b11c
        baseinfo.adm_date = this.form.b12
        baseinfo.adm_caty = this.form.b13c
        baseinfo.adm_ward = this.form.b14c
        baseinfo.refldept_dept = this.form.b21c
        baseinfo.dscg_date = this.form.b15
        baseinfo.dscg_caty = this.form.b16c
        baseinfo.dscg_ward = this.form.b17n
        baseinfo.ipt_days = this.form.b20
        baseinfo.otp_wm_dise = this.form.c01c
        baseinfo.wm_dise_code = this.form.c03c
        baseinfo.damg_intx_ext_rea = this.form.c13n
        baseinfo.damg_intx_ext_rea_disecode = this.form.c12c
        baseinfo.palg_no = this.form.c11
        baseinfo.drug_dicm_flag = this.form.c24c
        baseinfo.dicm_drug_name = this.form.c25
        baseinfo.die_autp_flag = this.form.c34c
        baseinfo.abo_code = this.form.c26c
        baseinfo.rh_code = this.form.c27c
        baseinfo.deptdrt_code = this.form.b22c
        baseinfo.deptdrt_code = this.form.b22c
        baseinfo.chfdr_code = this.form.b23c
        baseinfo.atddr_code = this.form.b24c
        baseinfo.atddr_code = this.form.b51c
        baseinfo.atddr_name = this.form.b52n
        baseinfo.ipt_dr_code = this.form.b25c
        // 责任护士名称
        baseinfo.resp_nurs_name = this.form.b26n
        // 责任护士姓名
        baseinfo.resp_nurs_code = this.form.b26c
        // 票据代码
        baseinfo.pjdm = this.form.d38
        // 票据号
        baseinfo.pjhm = this.form.d39
        // 业务流水号
        baseinfo.ywlsh = this.form.d35
        baseinfo.train_dr_code = this.form.b27c
        baseinfo.intn_dr_code = this.form.b28
        baseinfo.codr_code = this.form.b29c
        baseinfo.qltctrl_dr_code = this.form.b31c
        baseinfo.qltctrl_nurs_code = this.form.b32c
        baseinfo.medcas_qlt_code = ''
        baseinfo.qltctrl_date = this.form.b33
        baseinfo.dscg_way = this.form.b34c
        baseinfo.acp_medins_code = this.form.b48
        baseinfo.acp_medins_name = this.form.b49
        baseinfo.dscg_31days_rinp_flag = this.form.b36c
        baseinfo.dscg_31days_rinp_pup = this.form.b37
        baseinfo.brn_damg_bfadm_coma_dura =
          (this.form.c28 || '0') + '/' +
          (this.form.c29 || '0') + '/' +
          (this.form.c30 || '0')
        baseinfo.brn_damg_afadm_coma_dura =
          (this.form.c31 || '0') + '/' +
          (this.form.c32 || '0') + '/' +
          (this.form.c33 || '0')
        baseinfo.vent_used_dura =
          (this.form.c42 || '0') + '/' +
          (this.form.c43 || '0') + '/' +
          (this.form.c44 || '0')
        // 住院费用

        baseinfo.medfee_sumamt = this.form.d01;

        //十四项费用
        baseinfo.yb14fee = this.form.costItemAmount
        baseinfo.ordn_med_servfee = this.form.d11
        baseinfo.ordn_trt_oprt_fee = this.form.d12
        baseinfo.nurs_fee = this.form.d13
        baseinfo.com_med_serv_oth_fee = this.form.d14
        baseinfo.palg_diag_fee = this.form.d15
        baseinfo.lab_diag_fee = this.form.d16
        baseinfo.rdhy_diag_fee = this.form.d17
        baseinfo.clnc_dise_fee = this.form.d18
        baseinfo.nsrgtrt_item_fee = this.form.d19
        baseinfo.clnc_phys_trt_fee = this.form.d19x01
        baseinfo.rgtrt_trt_fee = this.form.d20
        baseinfo.anst_fee = this.form.d20x01
        baseinfo.oprn_fee = this.form.d20x02
        baseinfo.rhab_fee = this.form.d21
        baseinfo.tcm_trt_fee = this.form.d22
        baseinfo.wmfee = this.form.d23
        baseinfo.abtl_medn_fee = this.form.d23x01
        baseinfo.tcmpat_fee = this.form.d24
        baseinfo.tcmherb_fee = this.form.d25
        baseinfo.blo_fee = this.form.d26
        baseinfo.albu_fee = this.form.d27
        baseinfo.glon_fee = this.form.d28
        baseinfo.clotfac_fee = this.form.d29
        baseinfo.cyki_fee = this.form.d30
        baseinfo.exam_dspo_matl_fee = this.form.d31
        baseinfo.trt_dspo_matl_fee = this.form.d32
        baseinfo.oprn_dspo_matl_fee = this.form.d33
        baseinfo.oth_fee = this.form.d34

        baseinfo.settl_start_time = this.form.d36
        baseinfo.settl_end_time = this.form.d37

        baseinfo.resc_cnt = ''
        baseinfo.resc_succ_cnt = ''
        baseinfo.spga_nurscare_days = this.form.b44
        baseinfo.lv1_nurscare_days = this.form.b45
        baseinfo.scd_nurscare_days = this.form.b46
        baseinfo.lv3_nurscare_days = this.form.b47
        // 诊断编码
        if (this.disData && this.disData.length > 0) {
          for (let i = 0; i < this.disData.length; i++) {
            if (i === 0) {
              diseinfo.push({
                diag_code: this.disData[i]['c06c1'],
                diag_name: this.disData[i]['c07n1'],
                maindiag_flag: '1',
                adm_cond: this.disData[i]['c08c1']
              })
            } else {
              diseinfo.push({
                diag_code: this.disData[i]['c06c1'],
                diag_name: this.disData[i]['c07n1'],
                maindiag_flag: '0',
                adm_cond: this.disData[i]['c08c1']
              })
            }
          }
        }

        // 手术编码
        if (this.opeData && this.opeData.length > 0) {
          for (let i = 0; i < this.opeData.length; i++) {
            oprninfo.push({
              oprn_oprt_code: this.opeData[i]['c35c'],
              oprn_oprt_name: this.opeData[i]['c36n'],
              oprn_oprt_date: this.opeData[i]['oprn_oprt_date'],
              oprn_oprt_type: this.opeData[i]['seq'],
              oper_name: this.opeData[i]['oprn_oprt_oper_name'],
              oper_code: this.opeData[i]['c39c'],
              anst_dr_name: this.opeData[i]['oprn_oprt_anst_dr_name'],
              anst_dr_code: this.opeData[i]['oprn_oprt_anst_dr_code'],
              anst_mtd_code: this.opeData[i]['c43']
            })
          }
        }

        // ICU
        if (this.icuData && this.icuData.length > 0) {
          for (let i = 0; i < this.icuData.length; i++) {
            icuinfo.push({
              icu_code: this.icuData[i]['scsCutdWardType'],
              inpool_icu_time: this.icuData[i]['scsCutdInpoolTime'],
              out_icu_time: this.icuData[i]['scsCutdExitTime'],
              icu_time_sum_dura: this.icuData[i]['scsCutdSumDura']
            })
          }
        }
        let params = {
          baseinfo: baseinfo,
          diseinfo: diseinfo,
          oprninfo: oprninfo,
          icuinfo: icuinfo,
          extinfo: extinfo
        }

        params.skippingValidation = true
        params.isWeb = '1'
        this.loading = true
        this.url = 'http://127.0.0.1:123/1.html'
        setTimeout(() => {
        }, 500)
        preGroup(JSON.stringify(params)).then(res => {
          this.url = res.url
          this.loading = false
        })
      })
    },
    closed () {
      this.$emit('closed')
    }
  },
  watch: {
    visible (val) {
      this.show = val
    },
    show (val) {
      if (val) {
        this.executePreGroup()
      }
    }
  }
}
</script>
<style scoped lang="scss">
/deep/ .el-drawer.rtl{
  width: 740px!important;
}
</style>
