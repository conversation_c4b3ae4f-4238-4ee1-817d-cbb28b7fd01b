<template>
    <el-table
      :data="tableData"
      :header-cell-style="{'text-align':'center'}"
      v-loading="tableLoading"
      height="100%"
      border>
      <el-table-column label="序号" type="index" align="center"/>

      <!-- 基层病种 -->
      <el-table-column label="DIP编码" prop="dipCodg" align="left" v-if="type == 2" :key="1"/>
      <el-table-column label="诊断编码" prop="dipDiagCodg" align="left" v-if="type == 2" :key="2"/>
      <el-table-column label="诊断名称" prop="dipDiagName" show-overflow-tooltip align="left" v-if="type == 2" :key="3"/>
      <el-table-column label="手术编码" prop="dipOprtCodg" align="left" v-if="type == 2" :key="4"/>
      <el-table-column label="手术名称" prop="dipOprtName" align="left" v-if="type == 2" :key="5"/>
      <el-table-column label="是否使用辅助目录" prop="usedAsstList" align="left" v-if="type == 2" :key="6"/>
      <el-table-column label="是否稳定病种" prop="isStabilizeDisease" align="left" v-if="type == 2" :key="7"/>
      <drg-table-column label="病种类型" dict-type="DISEASE_TYPE" prop="diseType" align="left" v-if="type == 2" :key="8"/>

      <!-- 中医优势 -->
      <el-table-column label="中医编码" prop="chineseDiseaseCode" align="left" v-if="type == 1" :key="1"/>
      <el-table-column label="中医名称" prop="chineseDiseaseName" align="left" v-if="type == 1" :key="2"/>
      <el-table-column label="医保编码" prop="medicalCode" align="left" v-if="type == 1" :key="3"/>
      <el-table-column label="医保名称" prop="medicalName" align="left" v-if="type == 1" :key="4"/>

      <!-- 重点专科 -->
      <el-table-column label="院内科室编码" prop="inhospDeptCodg" align="left" v-if="type == 4" :key="1"/>
      <el-table-column label="院内科室名称" prop="inhospDeptName" align="left" v-if="type == 4" :key="2"/>
      <el-table-column label="重点专科名称" prop="keySpcyName" align="left" v-if="type == 4" :key="3"/>
      <drg-table-column label="重点专科类型" dict-type="PROFESSIONAL_LEVEL" prop="keySpcyType" align="left" v-if="type == 4" :key="4"/>

      <!-- 病种类型系数 -->
      <drg-table-column label="病种类型" dict-type="DISEASE_CFT_TYPE" prop="diseaseCFTType" align="left" v-if="type == 5" :key="1" />
      <drg-table-column label="医院等级" dict-type="HOSPITAL_LEVEL" prop="hospLv" align="left" v-if="type == 5" :key="2" />
      <drg-table-column label="医院评级" dict-type="HOSPITAL_RATING" prop="hospRatg" align="left" v-if="type == 5" :key="3" />
      <drg-table-column label="重点学(专)科类型" dict-type="PROFESSIONAL_LEVEL" prop="keyDisc" align="left" v-if="type == 5" :key="4" />
      <el-table-column label="病种系数" prop="diseCof" align="right" v-if="type == 5" :key="5">
        <template slot-scope="scope">{{scope.row.diseCof+'%'}}</template>
      </el-table-column>

      <el-table-column label="DIP编码" prop="dipCodg" align="left" v-if="type == 6" :key="1"></el-table-column>
      <el-table-column label="DIP名称" prop="dipName" align="left" v-if="type == 6" :key="2"></el-table-column>

      <el-table-column label="编辑" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" circle @click="editDipDiseaseType(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="删除" width="100" align="center">
        <template slot-scope="scope">
          <el-popconfirm style="cursor: pointer;display: block" title="是否删除？" @confirm="deleteDipDiseaseType(scope.row)">
            <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
</template>
<script>
import { deleteDipDiseaseType } from '@/api/dataConfig/dipDiseaseTypeConfig'

export default {
  name: 'dipDiseaseTypeConfigTable',
  props: {
    tableData: {
      type: Array,
      required: true
    },
    tableLoading: {
      type: Boolean,
      required: true,
      default: false
    },
    type: {
      type: Number
    }
  },
  methods: {
    editDipDiseaseType (row) {
      this.$emit('editDipDiseaseType', row)
    },
    deleteDipDiseaseType (row) {
      let params = {}
      params.id = row.id
      if (this.type == '1') {
        params.type = '1'
      }
      if (this.type == '2') {
        params.type = '2'
      }
      if (this.type == '4') {
        params.type = '4'
      }
      if (this.type == '5') {
        params.type = '5'
      }
      if (this.type == '6') {
        params.type = '6'
      }
      deleteDipDiseaseType(params).then(res => {
        if (res.code == 200) {
          this.$message.success('删除成功')
          this.$emit('queryData', '')
        }
      })
    }
  }
}
</script>
