import request from '@/utils/request'

export function queryPatientNumYearData (params) {
  return request({
    url: '/operationalDecisionTrendAnalysisController/queryPatientNumYearData',
    method: 'post',
    params: params
  })
}

export function queryPatientNumMonthData (params) {
  return request({
    url: '/operationalDecisionTrendAnalysisController/queryPatientNumMonthData',
    method: 'post',
    params: params
  })
}

export function queryProfitAndLossYearData (params) {
  return request({
    url: '/operationalDecisionTrendAnalysisController/queryProfitAndLossYearData',
    method: 'post',
    params: params
  })
}
