import request from '@/utils/request'
export function fetchList (params) {
  return request({
    url: '/medicalQualityScore/getList',
    method: 'post',
    params: params
  })
}

export function getScoreCountInfo (params) {
  return request({
    url: '/medicalQualityScore/getScoreCountInfo',
    method: 'post',
    params: params
  })
}

export function getScoreLowTopInfo (params) {
  return request({
    url: '/medicalQualityScore/getScoreLowTopInfo',
    method: 'post',
    params: params
  })
}

export function getScoreDetailById (id) {
  return request({
    url: '/medicalQualityScore/getScoreDetailById',
    method: 'post',
    params: id
  })
}
