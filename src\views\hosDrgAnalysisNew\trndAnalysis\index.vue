<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             headerTitle="查询条件"
             :show-hos-dept="{ show: this.$somms.hasHosRole()}"
             contentTitle="趋势分析"
             :extendFormIndex="[1]"
             @query="queryData">
      <template slot="extendFormItems">
        <el-form-item label="年份" prop="year">
          <el-date-picker class="som-form-item"
                          v-model="queryForm.year"
                          value-format="yyyy"
                          type="year"
                          placeholder="请选择年份">
          </el-date-picker>
        </el-form-item>
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <el-row :gutter="10" style="height: 50%">
          <el-col :span="12" style="height: 100%">
            <drg-echarts :options="inGroupTrendData" class="forecast-col-content" />
          </el-col>
          <el-col :span="12" style="height: 100%">
            <drg-echarts :options="medicalNumTrendData" class="forecast-col-content" />
          </el-col>
        </el-row>
        <el-row :gutter="10" style="height: 50%">
          <el-col :span="24" style="height: 100%">
            <drg-echarts :options="profitAndLossTrendData" class="forecast-col-content" />
          </el-col>
        </el-row>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDrgData } from '@/api/newBusiness/newBusinessTrend'

export default {
  name: 'trndAnalysis',
  data: () => ({
    queryForm: {
      year: ''
    },
    inGroupTrendData: {},
    medicalNumTrendData: {},
    profitAndLossTrendData: {}
  }),
  mounted () {
    this.$nextTick(() => {
      this.queryData()
    })
    this.getIssue()
  },
  methods: {
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    queryData () {
      queryDrgData(this.getParams()).then(res => {
        this.barCount(res.data)
        this.profitAndLossCount(res.data)
        this.medicalNumCount(res.data)
      })
    },
    barCount (item) {
      let monthData = []
      let inGroupData = []
      // if (item.length > 0) {
        for (let i = 0; i < item.length; i++) {
          monthData.push(item[i].month)
          inGroupData.push(item[i].inGroupRate)
        }
        let option = {
          color: this.$somms.generateColor(),
          title: {
            text: '入组率',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: monthData
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              data: inGroupData,
              type: 'bar',
              showBackground: true,
              itemStyle: {
                normal: {
                  label: {
                    show: true,
                    position: 'inside',
                    formatter: '{c}%'
                  }
                }
              }
            }
          ]
        }
        this.inGroupTrendData = option
      // }
    },
    profitAndLossCount (item) {
      let monthData = []
      let profitAndLossData = []
      let ZCProfitAndLossData = []
      let upProfitAndLossData = []
      let lowProfitAndLossData = []
      let otherProfitAndLossData = []
      // if (item.length > 0) {
        for (let i = 0; i < item.length; i++) {
          monthData.push(item[i].month)
          profitAndLossData.push(item[i].profitLoss)
          ZCProfitAndLossData.push(item[i].zcprofitLoss)
          upProfitAndLossData.push(item[i].upProfitLoss)
          lowProfitAndLossData.push(item[i].lowProfitLoss)
          otherProfitAndLossData.push(item[i].otherProfitLoss)
        }
        let option = {
          color: this.$somms.generateColor(),
          title: {
            text: '盈亏',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            top: 20,
            right: 10,
            data: ['总病例', '正常病例', '超高病例', '超低病例', '其他病例']
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              data: monthData
            }
          ],
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '总病例',
              data: profitAndLossData,
              type: 'line'
            },
            {
              name: '正常病例',
              data: ZCProfitAndLossData,
              type: 'line'
            },
            {
              name: '超高病例',
              data: upProfitAndLossData,
              type: 'line'
            },
            {
              name: '超低病例',
              data: lowProfitAndLossData,
              type: 'line'
            },
            {
              name: '其他病例',
              data: otherProfitAndLossData,
              type: 'line'
            }
          ]
        }
        this.profitAndLossTrendData = option
      // }
    },
    medicalNumCount (item) {
      let monthData = []
      let ZCMedicalNumData = []
      let upMedicalNumData = []
      let lowMedicalNumData = []
      let otherMedicalNumData = []
      let medicalNumData = []
      // if (item.length > 0) {
        for (let i = 0; i < item.length; i++) {
          monthData.push(item[i].month)
          medicalNumData.push(item[i].medcasVal)
          ZCMedicalNumData.push(item[i].zcmedicalNum)
          upMedicalNumData.push(item[i].upMedicalNum)
          lowMedicalNumData.push(item[i].lowMedicalNum)
          otherMedicalNumData.push(item[i].otherMedicalNum)
        }
        let option = {
          color: this.$somms.generateColor(),
          title: {
            text: '病案数',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            top: 20,
            right: 10,
            data: ['总病例', '正常病例', '超高病例', '超低病例', '其他病例']
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              data: monthData
            }
          ],
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '总病例',
              data: medicalNumData,
              type: 'line'
            },
            {
              name: '正常病例',
              data: ZCMedicalNumData,
              type: 'line'
            },
            {
              name: '超高病例',
              data: upMedicalNumData,
              type: 'line'
            },
            {
              name: '超低病例',
              data: lowMedicalNumData,
              type: 'line'
            },
            {
              name: '其他病例',
              data: otherMedicalNumData,
              type: 'line'
            }
          ]
        }
        this.medicalNumTrendData = option
      // }
    },
    getIssue () {
      this.queryForm.year = this.$somms.getDate('yyyy', 0, 0, 0)
    }
  }
}
</script>
