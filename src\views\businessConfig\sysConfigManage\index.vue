<template>
  <div class="app-container">
    <drg-container :headerPercent="10">
      <template slot="header">
        <drg-title-line title="查询条件" />
        <el-form :inline="true" :model="queryForm" size="mini" style="height: 50%">
          <el-row :gutter="20" type="flex" justify="space-between" style="height: 100%">
            <el-col>
              <el-form-item label="键" size="mini">
                <el-input  placeholder="配置键" v-model="queryForm.configKey"></el-input>
              </el-form-item>
            </el-col>

            <el-col>
              <el-form-item label="值" size="mini">
                <el-input  placeholder="配置值" v-model="queryForm.configValue"></el-input>
              </el-form-item>
            </el-col>
            <el-col class="som-align-center">
              <div>
                <el-button type="primary" @click="getData">查 询</el-button>
                <el-button @click="refresh">刷 新</el-button>
              </div>
            </el-col>
            <el-col />
            <el-col />
            <el-col />
          </el-row>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="参数" />
        <div class="som-table-height">
          <el-table :data="tableData"
                    ref="dragTable"
                    border
                    highlight-current-row
                    size="mini"
                    row-key="id"
                    v-loading="tableLoading"
                    height="100%">
            <el-table-column
              align="center"
              type="index"
              label="序号" />

            <el-table-column
              prop="key"
              label="键">
            </el-table-column>

            <el-table-column
              prop="value"
              label="值">
            </el-table-column>

            <el-table-column
              prop="type"
              label="类型">
            </el-table-column>

            <el-table-column
              prop="description"
              label="描述">
            </el-table-column>
            <el-table-column label="操作" width="100px" align="center">
              <template #default="scope">
                <el-button
                  size="mini"
                  icon="el-icon-edit"
                  type="primary"
                  circle
                  @click="handleEdit(scope.$index, scope.row)"></el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 编辑 -->
          <el-dialog
            v-som-dialog-drag
            title="编辑"
            ref="editForm"
            width="30%"
            :visible.sync="editVisible">
            <el-form :model="editForm" size="mini" :rules="editFormRules" ref="editForm" :inline="true">
              <el-row>
                <el-col :span="11">
                  <el-form-item
                    prop="key"
                    label="配置键">
                    <el-input disabled v-model="editForm.configKey"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    prop="configValue"
                    label="配置值">
                    <el-input v-model="editForm.configValue" placeholder="请输入需要更改的值"/>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="editCancel" size="mini" >取 消</el-button>
                <el-button type="primary" @click="saveConfig" size="mini">保 存</el-button>
              </span>
            </template>
          </el-dialog>
        </div>
      </template>
    </drg-container>
  </div>
</template>

<script>
import { queryData, modifyConfig } from '@/api/dataConfig/commonConfig'
import Sortable from 'sortablejs'
export default {
  name: 'sysConfigManage',
  inject: ['reload'],
  data: () => ({
    tableData: [],
    tableLoading: false,
    editVisible: false,
    queryForm: {
      configKey: '',
      configValue: ''
    },
    editForm: {
      id: '',
      configKey: '',
      configValue: ''
    },
    configValue: 'null',
    editFormRules: {
      configValue: [
        { required: true, message: '请输入需要修改的值', trigger: 'blur' }
      ]
    },
    sortable: null,
    newList: []
  }),
  mounted () {
    this.getData()
  },
  methods: {
    setSort () {
      const el = this.$refs.dragTable.$el.querySelectorAll(
        '.el-table__body-wrapper > table > tbody'
      )[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost',
        setData: function (dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          const targetRow = this.tableData.splice(evt.oldIndex, 1)[0]
          this.tableData.splice(evt.newIndex, 0, targetRow)
          const tempIndex = this.newList.splice(evt.oldIndex, 1)[0]
          this.newList.splice(evt.newIndex, 0, tempIndex)
        }
      })
    },

    getData () {
      this.tableLoading = true
      queryData(this.queryForm).then((result) => {
        this.tableData = result.data
        this.tableLoading = false

        this.$nextTick(() => {
          this.setSort()
        })
      })
    },
    refresh () {
      this.reload()
    },
    handleEdit (index, row) {
      this.configValue = row.value
      this.editForm.id = row.id
      this.editForm.configKey = row.key
      this.editForm.configValue = row.value
      this.editVisible = true
    },
    editCancel () {
      this.$confirm('关闭后所做修改将不会保存,是否确认关闭？')
        .then(_ => {
          this.editVisible = false
        })
        .catch(_ => {})
    },
    saveConfig () {
      this.$refs['editForm'].validate((valid) => {
        if (valid) {
          let loadingInstance = null
          this.$confirm('是否确认修改？')
            .then(_ => {
              loadingInstance = this.openLoading()
              this.editVisible = false
              modifyConfig(this.editForm).then((result) => {
                loadingInstance.close()
                this.getData()
                this.$message({
                  message: '修改成功！',
                  type: 'success'
                })
              })
            })
            .catch(_ => { loadingInstance.close() })
        } else {
          this.$message({
            message: '请输入需要修改的值！',
            type: 'warning'
          })
        }
      })
    }
  }
}
</script>
