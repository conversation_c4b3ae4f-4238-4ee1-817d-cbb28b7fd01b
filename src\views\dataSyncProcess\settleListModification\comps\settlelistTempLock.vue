<template>
  <el-dialog
    :visible.sync="show"
    width="30%">
    <el-descriptions class="margin-top" title="清单操作信息" :column="2" border>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          当前用户
        </template>
        {{ lockData.lockUsername }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-location-outline"></i>
          当前IP
        </template>
        {{ lockData.lockIp }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          锁住用户
        </template>
        {{ lockData.username }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-location-outline"></i>
          锁住IP
        </template>
        {{ lockData.ip }}
      </el-descriptions-item>
    </el-descriptions>
    <span slot="footer" class="dialog-footer">
      <el-tooltip class="item" effect="dark" content="强制编辑可能会导致多人同时编辑一份清单，请谨慎执行！" placement="top-start">
        <el-button type="danger" @click="forcedEdit">强制编辑</el-button>
      </el-tooltip>
      <el-button type="primary" @click="closeDialog">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    // 数据
    lockData: {
      type: Object,
      default: () => {}
    }
  },
  data: () => ({
    showDialog: false
  }),
  methods: {
    closeDialog () {
      this.showDialog = false
      this.$emit('closeDialog')
    },
    forcedEdit () {
      this.$emit('forcedEdit')
    }
  },
  watch: {
    show: {
      immediate: true,
      handler: function (val) {
        this.showDialog = val
      }
    }
  }
}
</script>
