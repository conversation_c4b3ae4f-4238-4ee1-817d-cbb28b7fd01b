import request from '@/utils/request'

let PREFIX = '/firstPage3/'
/**
 * 查询首页 "预测/反馈费用以及病例相关数据"
 * @param params
 * @returns {*}
 */
export function getSummaryInfo (params) {
  return request({
    url: PREFIX + 'getSummaryInfo',
    method: 'post',
    params: params
  })
}

/**
 * 查询首页清单上传汇总数据
 * @param params
 * @returns {*}
 */
export function getSettleListUploadList (params) {
  return request({
    url: PREFIX + 'getSettleListUploadList',
    method: 'post',
    params: params
  })
}

/**
 * 统计科室清单修改
 * @param params
 * @returns {*}
 */
export function countSettleListModify (params) {
  return request({
    url: PREFIX + 'countSettleListModify',
    method: 'post',
    params: params
  })
}

/**
 * 查询科室亏损排名
 * @param params
 * @returns {*}
 */
export function queryOrderData (params) {
  return request({
    url: PREFIX + 'queryOrderData',
    method: 'post',
    params: params
  })
}
