<template>
  <div class="app-container">
    <drg-container :headerPercent="14" :isButtonEnd="true" >
      <template slot="header">
        <drg-title-line title="查询条件" />
        <el-form :model="queryForm" size="mini" label-width="80px" :rules="rules" ref="formName">
          <el-row type="flex" justify="space-between">
            <el-col>
              <el-form-item :label="timeName">
                <el-date-picker
                  v-model="queryForm.cysj"
                  type="daterange"
                  size="mini"
                  class="som-form-item"
                  start-placeholder="开始日期"
                  range-separator="-"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  :disabled="compareDisabled"
                  @change="dateChangeCysj">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col v-if="this.$somms.hasDeptRole()">
              <el-form-item label="出院科别" class="som-department-height som-form-item">
                <drg-department v-model="queryForm.deptCode" placeholder="请选择科室" :disabled="compareDisabled" />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="病案号" class="som-form-item">
                <el-input v-model="queryForm.a48" placeholder="请输入病案号" :disabled="compareDisabled" />
              </el-form-item >
            </el-col>
            <el-col v-if="this.bool && this.props1 == 'dipGroup'">
              <el-form-item label="DIP组" :prop="this.props1" v-if="this.bool && this.props1 == 'dipGroup'">
                <el-autocomplete
                  class="som-form-item"
                  v-model="queryForm.dipGroup"
                  :fetch-suggestions="querySearchAsync"
                  placeholder="请输入DIP组编码或名称"
                  @select="fnDipGroupSelect"
                  :popper-append-to-body="true"
                  :clearable="true"
                  :trigger-on-focus="false"
                  :disabled="compareDisabled"
                  ref="elautocomplete">
                  <template slot-scope="{ item }">
                    <div class="code">{{ item.dipCodg }}</div>
                    <span class="name">{{ item.dipName }}</span>
                  </template>
                </el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col v-if="this.bool && this.props3 == 'queryDrg'">
              <el-form-item label="DRGs组" :prop="this.props3" v-if="this.bool && this.props3 == 'queryDrg'">
                <el-autocomplete
                  popper-class="my-autocomplete"
                  size="mini"
                  v-model="queryForm.queryDrg"
                  :fetch-suggestions="queryDRGSearchAsync"
                  placeholder="请输入DRGs编码或名称"
                  @select="handleDRGSelect"
                  :popper-append-to-body="true"
                  :clearable="true"
                  :trigger-on-focus="false"
                  :disabled="compareDisabled"
                  ref='DRGelautocomplete' class="som-form-item">
                  <template slot-scope="{ item }">
                    <div class="code">{{ item.drgsCode }}</div>
                    <span class="name">{{ item.drgsName }}</span>
                  </template>
                </el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="组" prop="group">
                <el-select  v-model="group"
                            placeholder="请选择组"
                            class="som-form-item"
                            :disabled="compareDisabled">
                  <el-option
                    v-for="item in groupOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item class="som-form-item">
                <el-radio-group v-model="queryForm.radio" :disabled="compareDisabled">
                  <el-radio label="1">左页面</el-radio>
                  <el-radio label="2">右页面</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="som-align-center">
            <el-button
              @click="fnClickQuery()"
              type="primary"
              size="mini"
              :disabled="compareDisabled"
              class="som-el-form-item-margin-left">
              查询
            </el-button>
            <el-button @click="fnCompare"
                       type="success"
                       :disabled="allChecked">
              对比
            </el-button>
            <el-button @click="refresh">重置</el-button>
          </div>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="对比分析" />
        <div :style="{height: leftPageShow && rightPageShow ? '' : '93%',
                      width: '100%',
                      display: 'flex',
                      flexDirection: 'row' }" id="content">
          <!-- 左: 选择数据后显示 -->
          <div v-if="leftPageShow"  class="content-item">
            <ppl-compar-analysis-description :data="leftPageData"
                                             :otherPageData="rightCostData"
                                             :otherGroupData="rightIngroupData"
                                             :groupData="leftIngroupData"
                                             :costData="leftCostData"
                                             :costPayData="leftCostPayData"
                                             @backPage="handlerBackPage(true)"/>
          </div >

          <!-- 左: 选择数据前显示 -->
          <div v-if="!leftPageShow" class="content-table content-item">
            <ppl-compar-analysis-table :tableData="leftTableData"
                                       :tableLoading="leftTableLoading"
                                       :queryForm="queryForm"
                                       :total="leftTotal"
                                       :rightZero="true"
                                       @changeCheck="(id) => this.leftPageId = id"
                                       @checkState="fnLeftCheckState"/>
            <div class="pagination" style="margin-top: 0.2rem">
              <el-pagination
                background
                align="right"
                @size-change="leftHandleSizeChange"
                @current-change="leftHandleCurrentChange"
                layout="total, sizes,prev, pager, next,jumper"
                :page-size="queryForm.leftPageSize"
                :page-sizes="[200,1000,5000,10000]"
                :current-page.sync="queryForm.leftPageNum"
                :total="leftTotal">
              </el-pagination>
            </div>
          </div>

          <!-- 分隔线 -->
          <div style="width: 4%" :style="splitStyle">
            <div style="height: 100%;background-color: gray;width: 0.1rem;position: absolute;left: 50%">
            </div>
          </div>

          <!-- 右: 选择数据后显示 -->
          <div v-if="rightPageShow" class="content-item">
            <ppl-compar-analysis-description :data="rightPageData"
                                             :otherPageData="leftCostData"
                                             :otherGroupData="leftIngroupData"
                                             :groupData="rightIngroupData"
                                             :costData="rightCostData"
                                             :costPayData="rightCostPayData"
                                             @backPage="handlerBackPage(false)"
                                             :rightAdjust="true"/>
          </div>

          <!-- 右: 选择数据前显示 -->
          <div v-if="!rightPageShow" class="content-table content-item">
            <ppl-compar-analysis-table :tableData="rightTableData"
                                       :tableLoading="rightTableLoading"
                                       :queryForm="queryForm"
                                       :total="rightTotal"
                                       @changeCheck="(id) => this.rightPageId = id"
                                       @checkState="fnRightCheckState"/>
            <div class="pagination" style="margin-top: 0.2rem">
              <el-pagination
                background
                align="right"
                @size-change="rightHandleSizeChange"
                @current-change="rightHandleCurrentChange"
                layout="total, sizes,prev, pager, next,jumper"
                :page-size="queryForm.rightPageSize"
                :page-sizes="[200,1000,5000,10000]"
                :current-page.sync="queryForm.rightPageNum"
                :total="rightTotal">
              </el-pagination>
            </div>
          </div>
        </div>
      </template>
    </drg-container>
  </div>
</template>
<script>

import { queryDataIsuue, queryLikeDipGroupByPram, queryLikeDrgsByPram } from '@/api/common/drgCommon'
import { queryEnableGroup } from '@/api/common/sysCommon'
import { getList, getInfo, getIngroup, getCost, getCostPay } from '@/api/medicalQuality/pplComparAnalysis'
import pplComparAnalysisTable from './components/pplComparAnalysisTable'
import pplComparAnalysisDescription from './components/pplComparAnalysisDescription'
import moment from 'moment'
import { MessageBox } from 'element-ui'

export default {
  name: 'caseCompar',
  inject: ['reload'],
  components: { pplComparAnalysisTable, pplComparAnalysisDescription },
  data () {
    return {
      cy_start_date: null,
      cy_end_date: null,
      queryForm: {
        pageNum: 1,
        pageSize: 200,
        leftPageNum: 1,
        leftPageSize: 200,
        rightPageNum: 1,
        rightPageSize: 200,
        cysj: null,
        deptCode: '',
        a48: '',
        radio: '1',
        dipGroup: '',
        queryDrg: ''
      },
      splitStyle: {
        height: '',
        position: 'relative'
      },
      rules: {
        dipGroup: [
          { required: true, message: '该处为必填项', trigger: ['blur', 'change'] }
        ],
        queryDrg: [
          { required: true, message: '该处为必填项', trigger: ['blur', 'change'] }
        ],
        group: [
          { required: true, message: '该处为必填项', trigger: 'blur' }
        ]
      },
      bool: false,
      props1: 'xx',
      props3: 'xx',
      group: [],
      leftPageShow: false,
      rightPageShow: false,
      leftTableLoading: false,
      leftTableData: [],
      leftTotal: 0,
      rightTableLoading: false,
      rightTableData: [],
      rightTotal: 0,
      leftChecked: false,
      rightChecked: false,
      allChecked: true,
      compareDisabled: false,
      groupOptions: [],
      leftPageId: '',
      rightPageId: '',
      leftPageData: [],
      rightPageData: [],
      leftIngroupData: [],
      rightIngroupData: [],
      rightCostData: [],
      leftCostData: [],
      rightCostPayData: [],
      leftCostPayData: [],
      timeName: '出院时间'
    }
  },
  created () {

  },
  mounted () {
    if (Object.keys(this.$route.query).length > 0) {
      if (this.$route.query.inHosFlag) {
        this.queryForm.inHosFlag = this.$route.query.inHosFlag
        if (this.$route.query.inHosFlag == '1') {
          this.timeName = '出院时间'
          Object.assign(this.queryForm, { cysj: [this.$route.query.cy_start_date, this.$route.query.cy_end_date] })
        } else if (this.$route.query.inHosFlag == '2') {
          this.timeName = '入院时间'
          Object.assign(this.queryForm, { cysj: [this.$route.query.inStartTime, this.$route.query.inEndTime] })
        } else if (this.$route.query.inHosFlag == '3') {
          this.timeName = '结算时间'
          Object.assign(this.queryForm, { cysj: [this.$route.query.seStartTime, this.$route.query.seEndTime] })
        }
      }
      if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
        Object.assign(this.queryForm, { cy_start_date: this.$route.query.cy_start_date })
        Object.assign(this.queryForm, { cy_end_date: this.$route.query.cy_end_date })
        this.setTimeToNull('1')
      }
      if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
        Object.assign(this.queryForm, { inStartTime: this.$route.query.inStartTime })
        Object.assign(this.queryForm, { inEndTime: this.$route.query.inEndTime })
        this.setTimeToNull('2')
      }
      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        Object.assign(this.queryForm, { seStartTime: this.$route.query.seStartTime })
        Object.assign(this.queryForm, { seEndTime: this.$route.query.seEndTime })
        this.setTimeToNull('3')
      }
    } else {
      // 获取数据查询时间
      this.getDataIsuue()
    }
    this.getEnabledGroup()
    this.groupChange()
    if (this.$route.query.id && this.$route.query.dipGroup) {
      this.skip()
    }
  },
  methods: {
    setTimeToNull (type) {
      if (type != '1') {
        this.queryForm.cy_start_date = ''
        this.queryForm.cy_end_date = ''
      }
      if (type != '2') {
        this.queryForm.inStartTime = ''
        this.queryForm.inEndTime = ''
      }
      if (type != '3') {
        this.queryForm.seStartTime = ''
        this.queryForm.seEndTime = ''
      }
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
          this.cy_start_date = response.data.cy_start_date
          this.cy_end_date = response.data.cy_end_date
          Object.assign(this.queryForm, { cysj: [this.cy_start_date, this.cy_end_date] })
        } else {
          this.cy_start_date = response.data.cy_start_date
          this.cy_end_date = response.data.cy_end_date
          this.queryForm.cysj = [this.cy_start_date, this.cy_end_date]
        }
        // 查询数据
        // this.getTableData(3);
      })
    },
    getEnabledGroup () {
      queryEnableGroup().then(res => {
        if (res.code == 200) {
          this.groupOptions = res.data
        }
      })
    },
    getTableData (index) {
      if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
        Object.assign(this.queryForm, { cysj: [this.$route.query.cy_start_date, this.$route.query.cy_end_date] })
        this.cy_start_date = this.$route.query.cy_start_date
        this.cy_end_date = this.$route.query.cy_end_date
      }
      let params = this.getParams()
      if (index == 1) {
        params.pageSize = this.queryForm.leftPageSize
        params.pageNum = this.queryForm.leftPageNum
      } else if (index == 2) {
        params.pageSize = this.queryForm.rightPageSize
        params.pageNum = this.queryForm.rightPageNum
      }
      if (this.group == 1) {
        params.queryDrg = ''
      }
      if (this.group == 3) {
        params.dipGroup = ''
      }
      if (this.group == 1 && (this.queryForm.dipGroup == '' || this.queryForm.dipGroup == null)) {
        if (index == 3) {
          this.leftTableData = []
          this.leftTotal = 0
          this.rightTableData = []
          this.rightTotal = 0
        } else if (index == 1) {
          this.leftTableData = []
          this.leftTotal = 0
        } else if (index == 2) {
          this.rightTableData = []
          this.rightTotal = 0
        }
      } else if (this.group == 3 && (this.queryForm.queryDrg == '' || this.queryForm.queryDrg == null)) {
        if (index == 3) {
          this.leftTableData = []
          this.leftTotal = 0
          this.rightTableData = []
          this.rightTotal = 0
        } else if (index == 1) {
          this.leftTableData = []
          this.leftTotal = 0
        } else if (index == 2) {
          this.rightTableData = []
          this.rightTotal = 0
        }
      } else {
        getList(params).then(res => {
          let data = res.data.list
          if (index == 3) {
            this.leftTableData = data
            this.leftTotal = res.data.total
            this.rightTableData = data
            this.rightTotal = res.data.total
          } else if (index == 1) {
            this.leftTableData = data
            this.leftTotal = res.data.total
          } else if (index == 2) {
            this.rightTableData = data
            this.rightTotal = res.data.total
          }
        })
      }
    },
    leftHandleSizeChange (val) {
      this.queryForm.leftPageNum = 1
      this.queryForm.leftPageSize = val
      this.getTableData(1)
    },
    leftHandleCurrentChange (val) {
      this.queryForm.leftPageNum = val
      this.getTableData(1)
    },
    rightHandleSizeChange (val) {
      this.queryForm.rightPageNum = 1
      this.queryForm.rightPageSize = val
      this.getTableData(2)
    },
    rightHandleCurrentChange (val) {
      this.queryForm.rightPageNum = val
      this.getTableData(2)
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
    },
    refresh () {
      this.$refs['formName'].resetFields()
      this.resetSearch()
      this.reload()
    },
    getParams () {
      if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
        Object.assign(this.queryForm, { cysj: [this.$route.query.cy_start_date, this.$route.query.cy_end_date] })
        this.cy_start_date = this.$route.query.cy_start_date
        this.cy_end_date = this.$route.query.cy_end_date
      }
      if (this.$route.query.dipGroup) {
        if (this.$route.query.dipGroup == '-') {
          this.queryForm.dipGroup = ''
        }
        this.queryForm.dipGroup = this.$route.query.dipGroup
      }
      if (this.$route.query.type) {
        this.group = this.$route.query.type
      }
      let params = this.queryForm
      if (params.cysj) {
        params.begnDate = moment(params.cysj[0]).format('YYYY-MM-DD')
        params.expiDate = moment(params.cysj[1]).format('YYYY-MM-DD')
      }

      params.group = this.group
      params.dataAuth = true
      return params
    },
    fnClickQuery () {
      this.group = this.group
      this.$refs['formName'].validate((valid) => {
        if (this.group != '') {
          valid = true
        }
        if (valid) {
          this.resetSearch()
          this.getTableData(this.queryForm.radio)
        }
      })
    },
    fnLeftCheckState (val) {
      this.leftChecked = val
      this.judgeAllChecked()
    },
    fnRightCheckState (val) {
      this.rightChecked = val
      this.judgeAllChecked()
    },
    judgeAllChecked () {
      if (this.leftChecked && this.rightChecked) {
        this.allChecked = false
      }
    },
    fnCompare () {
      this.resetSearch()
      this.leftPageShow = true
      this.rightPageShow = true
      this.allChecked = true
      this.compareDisabled = true
      this.queryDescriptionsData()
      this.queryDescriptionsGroupData()
      this.queryDescriptionsCostData()
      this.queryDescriptionsCostPayData()
      this.$nextTick(() => {
        this.queryForm.a48 = ''
        this.queryForm.deptCode = ''
      })
    },
    handlerBackPage (flag) {
      if (flag) {
        this.leftPageShow = false
        this.leftChecked = false
        this.queryForm.radio = '1'
      } else {
        this.rightPageShow = false
        this.rightChecked = false
        this.queryForm.radio = '2'
      }
      this.allChecked = false
      this.compareDisabled = false
      this.getDataIsuue()
    },
    queryDescriptionsData () {
      // 查询数据传入 description 组件
      let params = this.getParams()
      if (this.$route.query.id) {
        params.ids = [this.$route.query.id]
        this.leftPageId = this.$route.query.id
      } else {
        params.ids = [this.leftPageId, this.rightPageId]
      }
      getInfo(params).then(res => {
        if (res.code == 200) {
          res.data.map(data => {
            if (data.id == this.leftPageId) {
              this.leftPageData = data
            }
            if (data.id == this.rightPageId) {
              this.rightPageData = data
            }
          })
        }
      })
    },
    queryDescriptionsGroupData () {
      let params = this.getParams()
      if (this.$route.query.id) {
        params.ids = [this.$route.query.id]
        this.leftPageId = this.$route.query.id
      } else {
        params.ids = [this.leftPageId, this.rightPageId]
      }
      getIngroup(params).then(res => {
        if (res.code == 200) {
          res.data.map(data => {
            if (data.id == this.leftPageId) {
              this.leftIngroupData = data
              this.leftIngroupData.group = this.group
            }
            if (data.id == this.rightPageId) {
              this.rightIngroupData = data
              this.rightIngroupData.group = this.group
            }
          })
        }
      })
    },
    queryDescriptionsCostData () {
      let params = this.getParams()
      if (this.$route.query.id) {
        params.ids = [this.$route.query.id]
        this.leftPageId = this.$route.query.id
      } else {
        params.ids = [this.leftPageId, this.rightPageId]
      }
      getCost(params).then(res => {
        if (res.code == 200) {
          res.data.map(data => {
            if (data.id == this.leftPageId) {
              this.leftCostData = data
              this.leftCostData.group = this.group
            }
            if (data.id == this.rightPageId) {
              this.rightCostData = data
              this.rightCostData.group = this.group
            }
          })
        }
      })
    },
    queryDescriptionsCostPayData () {
      let params = this.getParams()
      if (this.$route.query.id) {
        params.ids = [this.$route.query.id]
        this.leftPageId = this.$route.query.id
      } else {
        params.ids = [this.leftPageId, this.rightPageId]
      }
      getCostPay(params).then(res => {
        if (res.code == 200) {
          res.data.map(data => {
            if (data.id == this.leftPageId) {
              this.leftCostPayData = data
            }
            if (data.id == this.rightPageId) {
              this.rightCostPayData = data
            }
          })
        }
      })
    },
    skip () {
      this.leftPageShow = true
      this.allChecked = false
      this.compareDisabled = false
      this.queryDescriptionsData()
      this.queryDescriptionsGroupData()
      this.queryDescriptionsCostData()
      this.queryDescriptionsCostPayData()
      this.$nextTick(() => {
        this.queryForm.a48 = ''
        this.queryForm.deptCode = ''
      })
    },
    // 清空下转内容
    resetSearch () {
      if (this.$route.query) {
        this.$router.push({ query: {} }).catch(() => {})
      }
    },

    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    fnDipGroupSelect (item) {
      this.queryForm.dipGroup = item.dipCodg
    },
    queryDRGSearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.DRGelautocomplete.handleFocus()
      })
    },
    handleDRGSelect (item) {
      this.queryForm.queryDrg = item.drgsCode
    },

    groupChange () {
      if (this.group != '' || this.group != null) {
        this.bool = true
        if (this.group == 1) {
          this.props1 = 'dipGroup'
          this.props3 = 'xx'
        }
        if (this.group == 3) {
          this.props3 = 'queryDrg'
          this.props1 = 'xx'
        }
      }
    }
  },
  watch: {
    group: function () {
      // this.queryDescriptionsGroupData();
      // this.queryDescriptionsCostData();
      this.groupChange()
    }
  }
}

</script>
<style scoped>
  .content-table{
    height: 98%;
  }
  .content-item {
    width: 48%;
  }
</style>
