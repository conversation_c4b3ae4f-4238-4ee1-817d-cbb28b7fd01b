import request from '@/utils/request'

export function calculationData (params) {
  return request({
    url: '/medicalManagementController/calculationData',
    method: 'post',
    params: params
  })
}

export function drgCalculationData (params) {
  return request({
    url: '/medicalManagementController/drgCalculationData',
    method: 'post',
    params: params
  })
}

export function queryPatientInfo (params) {
  return request({
    url: '/medicalManagementController/queryPatientInfo',
    method: 'post',
    params: params
  })
}

export function queryGroupsInfo (params) {
  return request({
    url: '/medicalManagementController/queryGroupsInfo',
    method: 'post',
    params: params
  })
}
