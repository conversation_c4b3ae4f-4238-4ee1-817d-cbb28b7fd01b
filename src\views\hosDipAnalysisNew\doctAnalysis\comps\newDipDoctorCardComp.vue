<template>
  <div class="card-wrapper">
    <!-- head -->
    <div class="card-wrapper-head">
      <!-- profttl -->
      <div class="card-wrapper-head-title">
        <span>
          {{ headTitle }}
        </span>

        <div class="card-wrapper-head-title-dropdown">
          <el-select v-model="deptCode"
                     placeholder="请选择"
                     @change="deptChange">
            <el-option v-for="(item, index) in tempDropdownData"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
              <span style="float: left">
                {{ item.label }}
              </span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ item.value }}
              </span>
            </el-option>
          </el-select>
        </div>
      </div>
    </div>

    <div class="card-wrapper-body">
      <div style="height: 100%;width: 100%;overflow-y: auto">
        <drg-loading :loading="loading" style="position: absolute;top: 50%;left: 45%;"/>
        <el-empty v-show="data.length == 0 ? true : false" style="position: absolute;top: 25%;left: 45%" description="暂无数据"></el-empty>
        <div style="height: 100%;width: 100%;background-color: white;position: absolute" v-show="loading" />
        <el-card shadow="hover"
                 :body-style="{ 'padding' : '0px!important' }"
                 class="dcew-content-box"
                 v-for="(item, index) in data"
                 :key="index"
                 :class="[(index + 1) % 4 != 1 ? 'dcew-content-box-left' : '']">
          <div class="dcew-content-box-title">
            {{ item.drName }}
          </div>
          <div class="dcew-content-box-item">
            <div class="dcew-content-box-item-title">时间消耗指数</div>
            <div class="dcew-content-box-item-val">{{ item.timeIndex }}</div>
          </div>
          <div class="dcew-content-box-item">
            <div class="dcew-content-box-item-title">费用消耗指数</div>
            <div class="dcew-content-box-item-val">{{ item.costIndex }}</div>
          </div>
          <div class="dcew-content-box-item">
            <div class="dcew-content-box-item-title">平均住院天数</div>
            <div class="dcew-content-box-item-val">{{ item.avgInHosDays }}</div>
          </div>
          <div style="width: 100%;height: 40%">
            <div class="dcew-content-box-item-end">
              <div class="dcew-content-box-item-end-title">平均住院费用</div>
              <div class="dcew-content-box-item-val">{{ item.avgInHosCost }}</div>
            </div>
            <div class="dcew-content-box-item-end">
              <div class="dcew-content-box-item-end-title">药占比</div>
              <div class="dcew-content-box-item-val">{{ item.drugRatio }}</div>
            </div>
            <div class="dcew-content-box-item-end">
              <div class="dcew-content-box-item-end-title">耗占比</div>
              <div class="dcew-content-box-item-val">{{ item.consumeRatio }}</div>
            </div>
            <div class="dcew-content-box-item-end">
              <div class="dcew-content-box-item-end-title">CMI</div>
              <div class="dcew-content-box-item-val">{{ item.cmi }}</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'newDipDoctorCardComp',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    dropdownData: {
      type: Array,
      default: () => []
    },
    dropdownVal: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    headTitle: '',
    tempDropdownData: [],
    deptCode: ''
  }),
  methods: {
    deptChange (val, boolean = true) {
      this.dropdownData.forEach(item => {
        if (item.value == val) {
          if (boolean) {
            this.$emit('deptChange', item.value)
          }
          this.headTitle = item.label
        }
      })
    }
  },
  watch: {
    dropdownVal: {
      immediate: true,
      deep: true,
      handler: function (val) {
        if (val) {
          this.deptCode = val
          this.deptChange(val, false)
        }
      }
    },
    dropdownData: {
      immediate: true,
      deep: true,
      handler: function (val) {
        if (val) {
          this.tempDropdownData = val
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.card-wrapper {
  height: 100%;
  width: 100%;
  position: relative;

  &-head {
    height: 10%;
    width: 100%;
    padding-top: 1%;

    &-title {
      width: 100%;
      height: 50%;
      font-size: var(--biggerSize);
      font-weight: 600;
      position: relative;

      &-dropdown {
        position: absolute;
        right: 35%;
        top: -5px;
      }
    }
  }

  &-body {
    width: 100%;
    height: 90%;
    position: relative;
    display: flex;
  }
}

.dcew-content-box {
  height: 34%;
  width: 24.5%;
  display: inline-block;

  &-left {
    margin-left: 0.5%;
  }

  &-title {
    width: 100%;
    height: 10%;
    padding: 0.5rem 0 0 0;
    font-size: var(--biggerSize);
    font-weight: bold;
  }

  &-item {
    margin: 3% 0 3% 2%;
    width: 29%;
    display: inline-block;
    text-align: center;

    &-title {
      padding: 5% 0 0 0;
      color: gray;
      font-size: var(--biggerSmallSize);
      margin-bottom: 0.5rem
    }

    &-val {
      font-size: var(--biggerSmallTitleSize);
    }

    &-end {
      width: 24%;
      height: 75%;
      margin: 1rem 0 0 0;
      text-align: center;
      display: inline-block;

      &-title {
        margin-bottom: 0.6rem;
        color: gray;
        font-size: var(--biggerSmallSize);
      }

      &-val {
        font-size: var(--biggerSmallTitleSize);
      }
    }
  }
}
</style>
