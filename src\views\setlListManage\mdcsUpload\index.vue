<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
              :show-date-range="{ show: true, label: timeLabel }"
              :show-in-date-range="{ show: false }"
              :showAccurate="isShow"
              :show-patient-num="{ show : isShow }"
              :show-hos-dept="{show : isShow }"
              :container="true"
              :showPagination="!showPie"
              :totalNum="total"
              :initTimeValueNotQuery="false"
              headerTitle="查询条件"
              :showCoustemContentTitle="true"
              :exportExcel="['未上传', '上传失败', '上传成功', '状态修改'].includes(tabName) ? { 'tableId': tableId, exportName: this.tabName} : undefined "
              :exportExcelFun="exportFun"
              :exportExcelHasChild="true"
              @query="queryData" ref="somForm">
      <!-- 查询条件 -->
      <template slot="extendFormItems">
        <el-form-item label="是否标识" prop="lookOver" v-if="isShow">
          <drg-dict-select dic-type="BOOLEAN" placeholder="请选择" v-model="queryForm.lookOver" @change="queryData"/>
        </el-form-item>
        <el-form-item label="通过校验" prop="validateOver" v-if="isShow">
          <drg-dict-select dic-type="BOOLEAN" placeholder="请选择" v-model="queryForm.validateOver"
                           @change="queryData"/>
        </el-form-item>
        <el-form-item label="是否结算" prop="listSerialNumFlag">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择" v-model="queryForm.listSerialNumFlag"
                           @change="queryData"/>
        </el-form-item>
        <el-form-item label="是否修改" prop="isRevise">
          <drg-dict-select dic-type="BOOLEAN" placeholder="请选择" v-model="queryForm.isRevise" @change="queryData"/>
        </el-form-item>
        <el-form-item label="是否自费" prop="isSpend">
          <drg-dict-select dic-type="BOOLEAN" placeholder="请选择" v-model="queryForm.isSpend" @change="queryData"/>
        </el-form-item>
        <el-form-item label="是否完成" prop="isSuccess"  v-if = "['状态修改'].includes(tabName)">
          <drg-dict-select dic-type="BOOLEAN" placeholder="请选择" v-model="queryForm.isSuccess" @change="queryData"/>
        </el-form-item>
        <el-form-item label="是否异地" prop="isRemote">
          <drg-dict-select v-model="queryForm.isRemote" placeholder="请选择异地区域" dicType="INSUPLCADMDVS_TYPE"
                           @change="queryData" />
        </el-form-item>

      </template>

      <!-- 按钮 -->
      <template slot="buttons">
        <el-button type="primary" @click="dialogVisible = true" class="som-button-margin-right"><i
            class="el-icon-upload el-icon--left"></i>文件上传
        </el-button>
        <el-button type="primary"
                   v-if="['未上传'].includes(tabName)"
                   @click="policyAdjustmentCfg" class="som-button-margin-right">按政策调整
        </el-button>
        <el-button type="danger"
                   v-if="['未上传'].includes(tabName)"
                   :disabled="!(this.uploadData.length > 0)"
                   @click="revokeTheidentity" class="som-button-margin-right" > 取消标识
        </el-button>
        <el-button type="danger"
                   v-if="['上传成功','上传失败'].includes(tabName)"
                   :disabled="!(this.uploadData.length > 0)"
                   @click="dataWithdrawal" class="som-button-margin-right" > 撤回数据
        </el-button>
        <!--        <el-button type="primary" @click="listWithdrawn" class="som-button-margin-right"><i-->
        <!--          class="el-icon-upload el-icon&#45;&#45;left"></i>撤销-->
        <!--        </el-button>-->
        <el-button type="success" class="som-button-margin-right" size="mini"
                   v-if="['未上传', '上传失败', '上传成功', '状态修改'].includes(tabName)"
                   :disabled="!(this.uploadData.length > 0 && this.revokeData.length == 0)" @click="uploadPatient('1')">
          {{ getUploadName }}
        </el-button>
        <el-button type="success" class="som-button-margin-right" size="mini"
                   v-if="['未上传', '上传失败', '上传成功', '状态修改'].includes(tabName)" @click="uploadPatient('2')">
          {{ getAllUploadName }}
        </el-button>
        <el-button type="danger" class="som-button-margin-right" size="mini" v-if="tabName === '状态修改'"
                   :disabled="this.revokeData.length > 0  && this.uploadData.length == 0 ?  false: true" @click="dialogShow">撤销
        </el-button>
        <el-button type="danger" class="som-button-margin-right" size="mini" v-if="tabName === '状态修改'"
                   @click="dialogShow">全部撤销
        </el-button>

      </template>
      <!-- profttl -->
      <!--      <template slot="contentTitle" v-if="!uploadFalse">-->
      <!--        <drg-title-line :title="profttl">-->
      <!--          <template slot="rightSide">-->
      <!--            <div style="display: flex;position: absolute;top: 20px;right: 0px;" v-show="this.showTab">-->
      <!--              <div>-->
      <!--                <el-button type="primary" size="mini" @click="returnBack">返回</el-button>-->
      <!--              </div>-->
      <!--            </div>-->
      <!--          </template>-->
      <!--        </drg-title-line>-->
      <!--      </template>-->
      <!-- 内容 -->
      <template slot="containerContent" v-if="!showPie">
        <el-tabs class="som-table-height" v-model="tabName" @tab-click="tabClick">
          <el-tab-pane class="som-tab-pane" label="未上传" name="未上传">
            <list-table :id="tableId"
                        :data="tableData"
                        :loading="loading"
                        :update-status="tabName"
                        @selectData="selectData"
                        ref="dataTable" @setRefObj="(obj) => this.tableObj = obj"/>
          </el-tab-pane>

          <el-tab-pane class="som-tab-pane" label="上传失败" name="上传失败">
            <list-table :id="tableId" :data="tableData" :loading="loading" :update-status="tabName"
                        @selectData="selectData"
                        ref="dataTable" @setRefObj="(obj) => this.tableObj = obj"/>
          </el-tab-pane>
          <el-tab-pane class="som-tab-pane" label="上传成功" name="上传成功">
            <list-table :id="tableId" :data="tableData" :loading="loading" :update-status="tabName"
                        @selectData="selectData"
                        ref="dataTable" @setRefObj="(obj) => this.tableObj = obj"/>
          </el-tab-pane>
          <el-tab-pane class="som-tab-pane" label="状态修改" name="状态修改">
            <list-table :id="tableId" :data="tableData" :loading="loading" :update-status="tabName"
                        @selectData="selectData" @callQueryData = "queryData"
                        ref="dataTable" @setRefObj="(obj) => this.tableObj = obj" :pass-validate-upload="false"/>
          </el-tab-pane>
          <!--          <el-tab-pane class="som-tab-pane" label="操作记录" name="记录">-->
          <!--            <record-table ref="recordTable" :data="tableData" :loading="loading" @showBack="item => this.showTab = item"-->
          <!--                          @recordData="item => this.backData = item"/>-->
          <!--            <div style="height: 80%" v-if="this.showTab">-->
          <!--              <list-table :data="detailData" :loading="detailLoading"/>-->
          <!--            </div>-->
          <!--          </el-tab-pane>-->
        </el-tabs>
        <el-dialog title="请输入撤销原因" :visible.sync="showDialog" width="25%" >
          <el-form :model="dialogForm" :rules="rules" ref="dialogForm" v-loading="showDialogLoading">
            <el-form-item label="撤销原因" prop="desc" required>
              <el-input type="textarea" placeholder="请输入撤销原因" :rows="4" v-model="dialogForm.desc"/>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="resetForm('dialogForm')">取消</el-button>
            <el-button type="primary" @click="submitForm('dialogForm')">确定</el-button>
          </div>
        </el-dialog>
        <el-dialog
            title="上传文件"
            :visible.sync="dialogVisible"
            width="50%">
          <el-upload
              style="text-align: center"
              drag
              ref="upload"
              :limit="1"
              action="customize"
              accept=".xlsx,.xls"
              :http-request="upload">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
          </el-upload>
        </el-dialog>

<!--        <el-dialog-->
<!--            title="政策调整"-->
<!--            ref="policyAdjustmentForm"-->
<!--            width="40%"-->
<!--            :visible.sync="policyAdjustmentVisible">-->
<!--          <el-form :model="policyAdjustmentForm" size="mini" label-width="auto">-->
<!--            <el-form-item label="期号" prop="ym">-->
<!--              <el-date-picker-->
<!--                  style="width:85%"-->
<!--                  v-model="policyAdjustmentForm.ym"-->
<!--                  type="month"-->
<!--                  value-format="yyyy-MM"-->
<!--                  placeholder="选择期号"-->
<!--              >{{ this.policyAdjustmentForm.ym }}-->
<!--              </el-date-picker>-->
<!--            </el-form-item>-->
<!--          </el-form>-->
<!--          <template #footer>-->
<!--              <span class="dialog-footer">-->
<!--                <el-button @click="policyAdjustmentCancel" size="mini">取 消</el-button>-->
<!--                <el-button type="primary" @click="policyAdjustmentReset" size="mini"-->
<!--                           v-model="saveEnable">确定</el-button>-->
<!--              </span>-->
<!--          </template>-->
<!--        </el-dialog>-->

      </template>

      <template slot="contentTitle" v-if="uploadFalse">
          <template slot="rightSide">
            <i class="som-icon-pie som-iconTool"
               title="饼图"
               v-if="!showPie"
               @click="changePieOrCard(1)"
               style="height: 1.2rem;width: 1.2rem"></i>
            <i class="som-icon-card som-iconTool"
               title="卡片"
               v-else
               @click="changePieOrCard(2)"
               style="height: 1.2rem;width: 1.2rem"></i>
          </template>
      </template>

      <template slot="containerContent" v-if="showPie">
        <drg-echarts :options="pieOptions1" ref="pieChart1" @chartClick="pieChartClick"/>
      </template>

    </drg-form>
  </div>
</template>
<script>
import listTable from './comps/listTable'
import recordTable from './comps/recordTable'
import {
  batchListUpload,
  modifySettleListType,
  queryData as queryPageData,
  queryDetailData,
  queryRecordData,
  queryUpdateTypeData,
  queryUploadedData,
  queryUploadFalseData,
  dataWithdrawal,
  updateData,
  cancelSettleList,
  resetPolicyAdjustment,
  revokeTheidentity as revokeidentity
} from '@/api/listManagement/listUpload'

export default {
  name: 'mdcsUpload',
  components: {
    listTable,
    // eslint-disable-next-line vue/no-unused-components
    recordTable
  },
  data: () => ({
    queryForm: {},
    profttl: '结算清单上传',
    tabName: '未上传',
    tableData: [],
    loading: false,
    showDialogLoading: false,
    total: 0,
    uploadData: [],
    revokeData: [],
    ids: [],
    k00s: [],
    policyAdjustmentForm: {
      ym: ''
    },
    showDialog: false,
    dialogVisible: false,
    dialogForm: {
      desc: ''
    },
    rules: {
      desc: [{ required: true, message: '请输入撤销原因', trigger: 'blur' }]
    },
    isShow: true,
    saveEnable: true,
    backNum: 0,
    showTab: false,
    policyAdjustmentVisible: false,
    detailData: [],
    detailLoading: false,
    backData: {},
    timeLabel: '结算时间',
    showAccurate: false,
    showPie: false,
    uploadFalse: false,
    pieOptions1: {},
    uploadFalseData: [],
    pieData: [],
    tableId: 'tableId',
    tableObj: {},
    exportFun: queryPageData
  }),
  computed: {
    getUploadName () {
      if (this.tabName === '未上传') {
        return '上传'
      }
      if (['上传失败', '上传成功'].includes(this.tabName)) {
        return '重传'
      }
      if (this.tabName === '状态修改') {
        return '提交'
      }
      return '提交'
    },
    getAllUploadName () {
      if (this.tabName === '未上传') {
        return '全部上传'
      }
      if (['上传失败', '上传成功'].includes(this.tabName)) {
        return '全部重传'
      }
      if (this.tabName === '状态修改') {
        return '全部提交'
      }
      return '提交'
    }
  },
  mounted () {
    this.$nextTick(() => {
      if (Object.keys(this.$route.query.length > 0)) {
        if (this.$route.query.tabName1) {
          this.tabName = this.$route.query.tabName1
          this.selectData(this.tabName)
        }
        // 此处写结算时间无效
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.queryForm)
        }
      }
      this.queryData()
    })
  },
  methods: {

    queryPageData,
    queryUploadedData,
    queryRecordData,
    queryUpdateTypeData,
    policyAdjustmentCfg () {
      this.setPreviousMonth()
      // this.policyAdjustmentForm.ym = this.queryForm.ym
      this.policyAdjustmentVisible = true
    },

    revokeTheidentity () {
      let profttl = '标识状态取消提示'
      let params = this.getParams()
      let msg = '是否取消标记当前选中的数据'
      // this.loading = true
      this.$confirm(msg, profttl, {
        type: 'warning'
      }).then(() => {
        if (this.tableData.length > 0) {
          this.loading = true
          revokeidentity(params).then(res => {
            if (res.code == 200) {
              this.queryData()
              this.loading = false
              this.$message({
                message: '标记撤销完成，共 【 ' + res.data + '】 条',
                type: 'success'
              })
            }
          })(() => {
            this.loading = false
          })
        } else {
          this.$message({ message: '暂无取消标记数据', type: 'warning' })
        }
      })
    },

    // 数据撤回
    dataWithdrawal () {
      let profttl = '标识状态取消提示'
      let params = this.getParams()
      if (['上传成功'].includes(this.tabName)) {
        params.upldStas = '1'
      }
      if (['上传失败'].includes(this.tabName)) {
        params.upldStas = '0'
      }
      let msg = '是否撤回当前已上传的数据数据'
      // this.loading = true
      this.$confirm(msg, profttl, {
        type: 'warning'
      }).then(() => {
        if (this.tableData.length > 0) {
          this.loading = true
          dataWithdrawal(params).then(res => {
            if (res.code == 200) {
              this.uploadData = []
              this.revokeData = []
              this.queryData()
              this.loading = false
              this.$message({
                message: '撤回完成，共 【 ' + res.data + '】 条',
                type: 'success'
              })
            }
          })(() => {
            this.loading = false
          })
        } else {
          this.$message({ message: '暂无撤回数据', type: 'warning' })
        }
      })
    },
    setPreviousMonth () {
      // 获取当前日期
      const currentDate = new Date()
      // 将月份减去1，获取上一个月
      currentDate.setMonth(currentDate.getMonth() - 1)
      // 格式化为 YYYYMM 的字符串
      const year = currentDate.getFullYear()
      const month = String(currentDate.getMonth() + 1).padStart(2, '0') // 月份从0开始，所以加1，并确保是两位数字
      this.policyAdjustmentForm.ym = `${year}-${month}`
    },
    setPieOptions1 () {
      this.pieOptions1 = {
        color: this.$somms.generateColor(),
        title: {
          text: '结算清单上传失败类型统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '失败类型',
            type: 'pie',
            radius: '50%',
            data: this.uploadFalseData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      // 生成图表
      if (this.showPie) {
        this.$nextTick(() => {
          this.$refs.pieChart1.initChart()
        })
      }
    },
    policyAdjustmentCancel () {
      this.$confirm('关闭后将不会调整费用信息,是否确认关闭？')
        .then(_ => {
          this.policyAdjustmentVisible = false
        })
        .catch(_ => {
          this.policyAdjustmentVisible = false
        })
    },
    policyAdjustmentReset () {
      if (this.saveEnable != null) {
        this.$confirm('是否确认调整' + this.policyAdjustmentForm.ym + '的病例数据？')
          .then(_ => {
            if (!this.policyAdjustmentForm) {
              this.$message({
                message: '参数不能为空，请检查输入。',
                type: 'warning'
              })
              // 终止请求
              return
            }
            resetPolicyAdjustment(this.policyAdjustmentForm).then((result) => {
              //  this.tableData = result.data
              this.feeCalculationVisible = false
              this.$message({
                message: '已完成政策调整',
                type: 'success'
              })
            })
          })
          .catch(_ => {
            this.policyAdjustmentVisible = false
          })
      }
    },
    queryUploadFalse: function () {
      // 查询数据
      let params = this.getParams()
      params.upldStas = '0'
      queryUploadFalseData(params).then(res => {
        this.uploadFalseData = res.data
        this.setPieOptions1()
      })
    },
    pieChartClick: function (params) {
      let k00Str = params.data.k00
      let k00Arr = k00Str.split(',')
      let params1 = this.getParams()
      params1.upldStas = '0'
      params1.k00s = k00Arr
      this.showPie = false
      queryUploadedData(params1).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.list
          this.total = res.data.total
          this.loading = false
        }
      })
    },
    changePieOrCard (index) {
      if (index == 1) {
        this.showPie = true
        // 查询上传失败清单
        this.queryUploadFalse()
      } else {
        this.showPie = false
      }
    },
    queryData () {
      this.loading = true
      if (this.tabName === '记录') {
        queryRecordData(this.getParams()).then(res => {
          this.tableData = res.data
          this.loading = false
          this.exportFun = queryRecordData
        })
      } else if (this.tabName === '未上传') {
        queryPageData(this.getParams()).then(res => {
          if (res.code == 200) {
            this.tableData = res.data.list
            this.total = res.data.total
            this.loading = false
            this.exportFun = queryPageData
          }
        })
      } else if (['上传失败', '上传成功'].includes(this.tabName)) {
        let params = this.getParams()
        if (this.tabName === '上传失败') {
          params.upldStas = '0'
          this.queryForm.upldStas = '0'
        } else {
          params.upldStas = '1'
          this.queryForm.upldStas = '1'
        }
        queryUploadedData(params).then(res => {
          if (res.code === 200) {
            this.tableData = res.data.list
            this.total = res.data.total
            this.loading = false
            this.exportFun = queryUploadedData
          }
        })
      } else if (this.tabName === '状态修改') {
        let params = this.getParams()
        params.upldStas = '1'
        queryUpdateTypeData(params).then(res => {
          if (res.code === 200) {
            this.tableData = res.data.list
            this.total = res.data.total
            this.loading = false
            this.exportFun = queryUpdateTypeData
          }
        })
      }
    },
    tabClick () {
      if (['未上传', '上传失败', '上传成功', '状态修改'].includes(this.tabName)) {
        this.profttl = '结算清单上传'
        this.tableData = []
        this.revokeData = []
        this.isShow = true
        this.timeLabel = '结算时间'
        this.uploadFalse = false
      } else if (this.tabName == '撤销') {
        this.profttl = '结算清单撤销'
        this.tableData = []
        this.uploadData = []
        this.isShow = true
        this.uploadFalse = false
      } else if (this.tabName == '记录') {
        this.profttl = '操作记录'
        this.tableData = []
        this.uploadData = []
        this.revokeData = []
        this.isShow = false
        this.timeLabel = '操作时间'
        this.uploadFalse = false
      }
      if (this.tabName == '上传失败') {
        this.uploadFalse = true
      }
      this.queryData()
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)

      if (['未上传', '上传失败', '上传成功', '状态修改'].includes(this.tabName)) {
        if (this.showDialog) {
          // 撤销
          params.uploadFlag = '1'
          params.isSuccess = 1
        } else {
          params.uploadFlag = '0'
          params.isSuccess = this.queryForm.isSuccess
        }
      } else if (this.tabName === '撤销') {
        params.uploadFlag = '1'
      }
      this.ids = []
      this.k00s = []
      if (this.uploadData.length > 0 && this.uploadData instanceof Array) {
        this.uploadData.forEach(item => this.ids.push(item.id))
        this.uploadData.forEach(item => this.k00s.push(item.k00))
      }
      if (this.revokeData.length > 0 && this.revokeData instanceof Array) {
        this.revokeData.forEach(item => this.ids.push(item.id))
        this.revokeData.forEach(item => this.k00s.push(item.k00))
      }

      params.name = this.$store.getters.name
      params.nknm = this.$store.getters.nickname
      params.ids = this.ids
      params.k00s = this.k00s
      params.reason = this.dialogForm.desc
      params.grperType = this.$somms.getGroupType()
      return params
    },
    selectData (item) {
      if (['未上传', '上传失败', '上传成功', '状态修改'].includes(this.tabName)) {
        this.revokeData = []
        this.uploadData = []
        item.forEach((subitem) => {
          if (subitem.stasType == '1') {
            // 已提交
            this.revokeData.push(subitem)
          } else {
            this.uploadData.push(subitem)
          }
        }
        )
      } else if (this.tabName == '撤销') {
        this.revokeData = item
      }
    },
    uploadPatient (type = '1') {
      let msg, profttl

      if (this.revokeData.length !== 0) {
        this.$message({ message: '不允许上传已提交数据', type: 'error' })
        return
      }
      if (this.uploadData.length == 0) {
        this.$message({ message: '暂无上传数据', type: 'error' })
        return
      }

      if (this.tabName === '状态修改') {
        if (type === '1') {
          msg = '是否修改清单状态（stas_type），修改后清单将无法再重传？'
        } else {
          msg = '是否修改当前时间范围内所有清单状态（stas_type），修改后清单将无法再重传？'
        }
        profttl = '清单状态修改提示'
      } else {
        if (type === '1') {
          msg = '是否确认上传清单，上传后不能再修改清单？'
        } else {
          msg = '是否上传所有省医保数据？'
        }
        profttl = '清单上传提示'
      }
      let params = this.getParams()
      if (this.tabName === '未上传') {
        params.uploadType = '1'
      } else if (this.tabName === '上传失败') {
        params.uploadType = '2'
        params.upldStas = '0'
      } else if (['上传成功', '状态修改'].includes(this.tabName)) {
        params.uploadType = '2'
        params.upldStas = '1'
      } else {
        params.uploadType = '0'
      }
      this.$confirm(msg, profttl, {
        type: 'warning'
      }).then(() => {
        if (this.tableData.length > 0) {
          this.loading = true
          if (this.tabName === '状态修改') {
            params.isSuccess = '0'
            modifySettleListType(params).then(res => {
              if (res.code === 200) {
                this.$message({ message: '修改成功', type: 'success' })
                this.uploadData = []
                this.loading = false
                this.queryData()
              }
            }).catch(() => {
              this.loading = false
            }).finally(() => {
              // 不论请求成功与否，最后都会执行
              this.loading = false
            })
          } else {
            updateData(params).then(res => {
              if (res.code === 200) {
                if (res.data.state === '1') {
                  this.$message({ message: '上传成功', type: 'success' })
                } else {
                  this.$message({ message: res.data.errMsg, type: 'warning' })
                }
                this.uploadData = []
                this.loading = false
                this.queryData()
              }
            }).catch(() => {
              this.loading = false
            })
          }
        } else {
          this.$message({ message: '暂无上传数据', type: 'warning' })
        }
      }).catch(() => {
        this.loading = false
      })
    },
    dialogShow () {
      if (this.uploadData.length !== 0) {
        this.$message({ message: '无法撤销未提交数据请检查', type: 'error' })
        return
      }
      if (this.revokeData.length == 0) {
        this.$message({ message: '暂无撤销数据', type: 'error' })
        return
      }
      if (this.tableData.length > 0) {
        this.showDialog = true
      } else {
        this.$message({ message: '暂无撤销数据', type: 'warning' })
      }
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = this.getParams()
          params.uploadType = '2'
          params.upldStas = '1'
          this.showDialog = false
          this.loading = true
          modifySettleListType(params).then(res => {
            params.isSuccess = '1'
            if (res.code == 200) {
              this.$refs[formName].resetFields()
              this.$message({ message: '撤销成功', type: 'success' })
              this.revokeData = []
              this.queryData()
            }
            this.loading = false
          }).catch(() => {
            this.loading = false
          })
        } else {
          this.$message({ message: '请输入撤销原因', type: 'warning' })
        }
      })
    },
    resetForm (formName) {
      this.$refs[formName].resetFields()
      this.showDialog = false
    },
    returnBack () {
      this.showTab = false
      this.backData = {}
      this.$refs['recordTable'].returnRow()
      this.$refs['recordTable'].doLayout()
    },
    showDetail (row) {
      if (row) {
        this.detailLoading = true

        queryDetailData(row).then(res => {
          this.detailData = res.data.list
          this.total = res.data.total
          this.detailLoading = false
        })
      }
    },
    upload (data) {
      let params = new FormData()
      params.append('file', data.file)
      params.append('group', this.queryForm.group)
      batchListUpload(params).then(res => {
        if (res.code == 200) {
          this.dialogVisible = false
          this.$refs.upload.clearFiles()
          if (res.data.state == '1') {
            this.$message({ message: '上传成功', type: 'success' })
          } else {
            this.$message({ message: res.data.errMsg, type: 'warning' })
          }
          this.queryData()
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
      })
    },
    listWithdrawn () {
      cancelSettleList(this.getParams())
    }
  },
  watch: {
    backData: {
      handler: function (row) {
        this.showDetail(row)
      }
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (vm.$refs.dataTable) {
        vm.$refs.dataTable.updateTable()
      }
    })
  }
}
</script>

<style scoped>
/deep/ .el-tabs__content {
  height: 97%;
}

</style>
