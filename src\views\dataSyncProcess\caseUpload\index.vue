<template>
  <div class="app-container">
    <drg-container :headerPercent="10">
        <template slot="header">
       <!--     <el-card style="height:120px;overflow-y:auto;"> -->
              <div>
              <!--<el-row>-->
                <!--<el-col :span="7">
                  <span class="note">数据上传方式： </span>
                  <el-select v-model="selectValue" placeholder="请选择数据上传方式" size="mini" @change="selectChange">
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>-->
               <!-- <el-col>-->
                  <el-form :inline="true" :model="listQuery" size="mini">
                    <!--<el-form-item>
                      <el-upload
                        name="file"
                        ref="upload"
                        action=""
                        accept=".xls,.xlsx,.csv,.dbf,.DBF"
                        :multiple="true"
                        :limit="5"
                        :auto-upload="false"
                        :http-request="uploadFiles"
                        :on-change="fileChange"
                        :on-exceed="handleExceed"
                        :before-upload="uploadBefore"
                        :on-progress="handleProgress"
                        :on-success="handleSuccess"
                        :on-error="handleError"
                        :on-remove="handleRemove"
                        v-if="showUpload">
                        <el-button slot="trigger" size="mini" type="primary">选取文件</el-button>
                        <el-button style="margin-left: 10px;" size="mini" type="success" @click="submitUpload">上传到服务器</el-button>
                      </el-upload>
                    </el-form-item>-->
                    <drg-title-line title="查询条件" />
                    <el-form-item label="出院时间：">
                      <el-date-picker
                        v-model="listQuery.cysj"
                        type="daterange"
                        size="mini"
                        unlink-panels
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        @change="dateChangeCysj"
                        :picker-options="pickerOptions">
                      </el-date-picker>
                    </el-form-item >
                    <el-form-item class="som-el-form-item-margin-left">
                    <el-button
                         style="margin-left:5px;"
                         @click="queryUploadData()"
                         type="primary"
                         size="mini">
                      查询
                    </el-button>
                      <el-button @click="refresh">重置</el-button>
                    </el-form-item>
                  </el-form>
               <!-- </el-col>
              </el-row>-->
              </div>
           <!-- </el-card> -->
        </template>
    <template slot="content">

      <el-card class="scope" style="height:97%">
        <div slot="header" class="clearfix"><span style="font-weight:600">数据处理进度</span><span> （仅展示近12个批次数据情况，具体详情请查看“数据处理日志”功能）</span></div>
        <div style="margin-left:30px;" v-for="(item,index) in processList" :key="index">
        <div >
          <div v-if="index==0" style="margin-top:15px">
            <span class="note"><i class="el-icon-stopwatch"></i> 出院时间：<span class="time">{{item.dataDscgTimeScp | formatIsEmpty}}；</span>上传时间：{{item.dataUpldTime}}； 耗时：{{item.dataprosDura}}s</span>
            <span class="note" v-if="item.result.substring(0,3)=='exp'"  style="float:right;margin-right:70px;margin-top:4px;"><i class="el-icon-circle-close"></i>{{item.result.substring(4,100)}}</span>
            <span class="note" v-if="item.result.substring(0,7)=='success'"  style="float:right;margin-right:70px;margin-top:4px;">流程已执行成功</span>
            <span class="note" v-if="item.result.substring(0,3)!='exp'&&item.result.substring(0,7)!='success'"  style="float:right;margin-right:70px;margin-top:4px;"><i class="el-icon-loading"></i> 正在执行流程..</span>
            <el-progress v-if="item.result.substring(0,3)=='exp'" status="exception" :stroke-width="8" :percentage=item.prcs_prgs color="#CC0000"></el-progress>
            <el-progress v-if="item.result.substring(0,7)=='success'" status="success" :stroke-width="8" :percentage=item.prcs_prgs :color=customColorMethod></el-progress>
            <el-progress v-if="item.result.substring(0,3)!='exp'&&item.result.substring(0,7)!='success'"  :stroke-width="8" :percentage=item.prcs_prgs :color=customColorMethod></el-progress>
          </div>
          <div v-if="index!=0" style="margin-top:21px">
            <span class="note"><i class="el-icon-stopwatch"></i> 出院时间：<span class="time">{{item.dataDscgTimeScp| formatIsEmpty}}；</span>上传时间：{{item.dataUpldTime}}； 耗时：{{item.dataprosDura}}s</span>
            <span class="note" v-if="item.result.substring(0,3)=='exp'"  style="float:right;margin-right:70px;margin-top:4px;"><i class="el-icon-circle-close"></i>{{item.result.substring(4,100)}}</span>
            <span class="note" v-if="item.result.substring(0,7)=='success'"  style="float:right;margin-right:70px;margin-top:4px;"> 流程已执行成功</span>
            <span class="note" v-if="item.result.substring(0,3)!='exp'&&item.result.substring(0,7)!='success'"  style="float:right;margin-right:70px;margin-top:4px;"><i class="el-icon-loading"></i> 正在执行流程..</span>
            <el-progress v-if="item.result.substring(0,3)=='exp'"  status="exception" :stroke-width="8" :percentage=item.prcs_prgs color="#CC0033"></el-progress>
            <el-progress v-if="item.result.substring(0,7)=='success'" status="success" :stroke-width="8" :percentage=item.prcs_prgs :color=customColorMethod></el-progress>
            <el-progress v-if="item.result.substring(0,3)!='exp'&&item.result.substring(0,7)!='success'"  :stroke-width="8" :percentage=item.prcs_prgs :color=customColorMethod></el-progress>
          </div>
        </div>
        </div>
      </el-card>
    </template>
        </drg-container>

    </div>
  </template>
<script>
/* import {settleList} from '@/api/fileUpload' */
import { queryMedicalData, savaMedicalData, getRunningAndExptionProcess } from '@/api/dataHandle/impUpload'
import { runPro } from '@/api/dataHandle/dataHandleProcess'
import { format } from '@/utils/datetime'

const defaultListQuery = {
  begn_date: null,
  expi_date: null
}
export default {
  name: 'caseUpload',
  inject: ['reload'],
  data () {
    return {
      /* //下拉数据
          options: [{
            value: '1',
            label: '文件上传'
          }, {
            value: '2',
            label: '接口上传'
          }],
          //下拉默认值、默认展示和隐藏,默认展示文件上传按钮，隐藏接口时间范围
          selectValue:'1',
          showUpload:true,
          showImp:false, */
      // 时间范围
      timer0: '',
      timer1: '',
      timer2: '',
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      // 表单参数
      /* impParam: {
            settleDate: ''
          },
          settleDate: '2020-01-01',
          //数据处理反馈
          showDataHandel:false,
          file:'',
          files: [], */
      listLoading: false,
      list: null,
      begn_date: null,
      expi_date: null,
      processList: null,
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery)
    }
  },
  created () {
    this.getProcess()
  },
  beforeDestroy () {
    clearInterval(this.timer0)
    clearInterval(this.timer1)
    clearInterval(this.timer2)
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime (time) {
      if (time) {
        return format(time)
      } else {
        return '-'
      }
    },
    formatResult (value) {
      if (value) {
        if (value.substring(0, 3) == 'exp') {
          return value.substring(4, 100)
        } else if (value.substring(0, 7) == 'success') {
          return '执行成功'
        } else if (value.substring(0, 3) != 'exp' && value.substring(0, 7) != 'success') {
          return '正在执行'
        }
      }
    }
  },
  methods: {
    /* selectChange(value) {
          if(value == '1'){
              this.$data.showUpload=true
              this.$data.showImp=false
          }else if(value == '2'){
              this.$data.showUpload=false
              this.$data.showImp=true
          }
        }, */
    /* handleExceed(file,fileList){
          this.$message({
            showClose: true,
            message: '一次性上传文件不能超过5个',
            type: 'warning'
          });
          return false;
        },
        uploadBefore(file){
          let hz = file.name.split(".")[1]
          if (hz != 'xlsx' && hz != 'xls' && hz != 'XLSX' && hz != 'XLS' && hz != 'csv' && hz != 'CSV'&& hz != 'dbf' && hz != 'DBF' ) {
            this.$message({
              showClose: true,
              message: '上传文件格式错误',
              type: 'error'
            });
            return false;
          }
          if(file.size>60*1024*1024){
            this.$message({
              showClose: true,
              message: '上传单个文件大小不能超过60M',
              type: 'warning'
            });
            return false;
          }
        },
        handleProgress(event, file, fileList) {
        },
        handleSuccess(response, file, fileList){
          this.$message({
            showClose: true,
            message: file.name+'上传成功！',
            type: 'success'
          });
          this.$data.showDataHandel=true
        },
        handleError(response, file, fileList){
          this.$message({
            showClose: true,
            message: file.name+'上传失败！',
            type: 'error'
          });
          this.$data.showDataHandel=false
        },
        handleRemove(file) {
          this.$refs.upload.abort(); //取消上传
          this.$message({message: '成功移除' + file.name, type: 'success'});
        },
        fileChange(file){
          this.file=file;
        },
        // 自定义文件上传
        uploadFiles(file) {
          //console.log("uploadFiles:"+file.file);
          //this.files.push(file.file)
          let formData = new FormData();
          console.log("submitUpload:"+this.file.raw);
          formData.append('file', this.file.raw, this.file.name)
          settleList(formData,file).then(response => {
            //_self.dataObj.callback = response.data.callback;
            file.onProgress({percent: 100})
            file.onSuccess("上传成功"); //上传成功(打钩的小图标)
            console.log(file.onSuccess)
            //resolve(true)
          }).catch(err => {
            console.log(err)
            //reject(false)
          })
        },
        // // 上传到服务器
        submitUpload() {
              //let formData = new FormData();
              //console.log("submitUpload:"+this.file.raw);
              //formData.append('file', this.file.raw, this.file.name)
              this.$refs.upload.submit();
              //this.fileData.append('token', getToken());
              //axios.post("http://localhost:8080/settleListUpload/fileUpload", formData,{"Content-Type": "multipart/form-data" }).then((response) => {
                // if (response.data.code === 0) {
                //   this.$message({
                //     message: "上传成功",
                //     type: 'success'
                //   });
                // } else {
                //   this.$message({
                //     type: 'error'
                //   })
                //}
              //);
          //console.log(formData);
            //settleList(formData).then(response => {
              //_self.dataObj.callback = response.data.callback;
              ///this.file.onSuccess(); //上传成功(打钩的小图标)
              //resolve(true)
            //}).catch(err => {
              //console.log(err)
              //reject(false)
            //})
        }, */
    dateChangeCysj (val) {
      if (val) {
        this.begn_date = val[0]
        this.expi_date = val[1]
      } else {
        this.begn_date = null
        this.expi_date = null
      }
    },
    queryUploadData () {
      this.getList()
    },
    getList () {
      if (this.begn_date == null || this.expi_date == null) {
        this.$message({
          message: '请输入出院时间范围！',
          type: 'warning'
        })
      } else {
        this.listLoading = true
        this.submitListQuery.begn_date = this.begn_date
        this.submitListQuery.expi_date = this.expi_date
        queryMedicalData(this.submitListQuery).then(response => {
          this.listLoading = false
          if (response.data.length > 0) {
            this.list = response.data
          } else {
            this.$message.error('存在非法操作，系统已无法使用!')
            this.$store.dispatch('LogOut').then(() => { // 退出当前登陆
              location.reload()
            })
          }
        })
      }
    },
    uploadImpData (index, row) {
      this.$confirm('确认上传该批次数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        savaMedicalData(this.submitListQuery).then(response => {
          this.listLoading = false
          let params = new URLSearchParams()
          params.append('logId', response.data) // 后台返回处理的批次号
          if (response.data) {
            // 重新查询一次流程进度信息
            getRunningAndExptionProcess().then(response => {
              if (response.data) {
                this.processList.push(response.data[0]) // 加入最新的一个流程
                for (let k = 0; k < this.processList.length; k++) {
                  this.processList[k].dataUpldTime = format(this.processList[k].dataUpldTime)
                }
                // 重新排序
                this.processList.sort(function (o1, o2) {
                  return Number(o2.id) - Number(o1.id)
                })
              }
              // 此定时器处理进度条，模拟进度条值增长
              this.timer2 = setInterval(() => {
                if (this.processList[0].result.substring(0, 3) != 'exp' && this.processList[0].prcs_prgs < 99) {
                  this.processList[0].prcs_prgs = (this.processList[0].prcs_prgs ? this.processList[0].prcs_prgs : 0) + 1
                }
                if (this.processList[0].prcs_prgs == 100) {
                  clearInterval(timer2)
                }
              }, 200)
            })
            // 调用数据处理流程
            runPro(params).then(response => {})
          } else {
            this.$alert('上传失败，请联系管理员重新上传！', '提示', {
              confirmButtonText: '确定'
            })
          }
        })
      }).catch(() => {
      })
    },
    getProcess () {
      // 进页面查一次，各流程执行情况
      getRunningAndExptionProcess().then(response => {
        this.processList = response.data
        console.log(response.data)
        // 重新排序
        this.processList.sort(function (o1, o2) {
          return Number(o2.id) - Number(o1.id)
        })
        // 此定时器处理进度条，模拟进度条值增长
        this.timer0 = setInterval(() => {
          let f0 = 0
          for (let i = 0; i < this.processList.length; i++) {
            this.processList[i].dataUpldTime = format(this.processList[i].dataUpldTime)
            if (this.processList[i].result.substring(0, 3) != 'exp' && this.processList[i].prcs_prgs < 99) {
              this.processList[i].prcs_prgs = (this.processList[i].prcs_prgs ? this.processList[i].prcs_prgs : 0) + 1
            }
            if (this.processList[i].result.substring(0, 3) != 'exp' && this.processList[i].prcs_prgs != 100) {
              f0++
            }
          }
          if (f0 == 0) {
            clearInterval(this.timer0)
          }
        }, 200)
      })
      // 此定时器查询批次信息、查询批次执行的结果和进度,此定时器不能停止，要一直使用，除非关闭当前页面，停止该定时器
      this.timer1 = setInterval(() => {
        getRunningAndExptionProcess().then(response => {
          let res = response.data
          for (let i = 0; i < res.length; i++) {
            for (let j = 0; j < this.processList.length; j++) {
              // 异常情况
              if (res[i].prcs_prgs != 100 && res[i].id == this.processList[j].id && res[i].result.substring(0, 3) == 'exp') {
                this.processList[j].prcs_prgs = res[i].prcs_prgs
                this.processList[j].dataprosDura = res[i].dataprosDura
                this.processList[j].result = res[i].result
              }
              // 执行成功情况
              if (res[i].prcs_prgs == 100 && res[i].id == this.processList[j].id) {
                this.processList[j].prcs_prgs = 100
                this.processList[j].dataprosDura = res[i].dataprosDura
                this.processList[j].result = 'success'
              }
            }
          }
        })
      }, 3000)
    },
    // 进度条颜色渐变
    customColorMethod (prcs_prgs) {
      if (prcs_prgs < 40) {
        return '#909399'
      } else if (prcs_prgs < 80) {
        return '#e6a23c'
      } else if (prcs_prgs <= 99) {
        return '#409eff'
      } else if (prcs_prgs == 100) {
        return '#00CC00'
      }
    },
    refresh () {
      this.reload()
    }
  }
}
</script>
  <style scoped>
    /deep/ .el-progress__text{
      font-size:10px;
    }
    /deep/ .el-progress-bar{
      width: 99%;
    }
    /deep/ .el-card__header{
      padding:10px 10px;
      font-size: 13px;
      background-color: #eef1f6;
    }
    .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
      margin-bottom:0px;
    }
    /deep/ .el-card__body{
      height:90%;
      padding-top: 0px;
      padding-right: 10px;
      padding-bottom: 0px;
      overflow-y:auto;
    }
   .note {
      font-size:12px;
    }
    .time{
      font-size:13px;
      font-weight: bold;
    }
    .scope{
      margin-top:10px;
    }
  </style>
