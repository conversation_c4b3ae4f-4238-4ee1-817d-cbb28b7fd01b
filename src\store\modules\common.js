
const common = {
  state: {
    dictionaries: [],
    settleListDict: [],
    departments: [],
    sysConfig_FZLX: ''
  },
  mutations: {
    setDictionaries (state, data) {
      state.dictionaries = data
    },
    setDepartments (state, data) {
      state.departments = data
    },
    setSettleListDict (state, data) {
      state.settleListDict = data
    },
    setFzlx (state, data) {
      state.sysConfig_FZLX = data
    }
  },
  getters: {
    getDictByKey: (state) => (key) => {
      return state.dictionaries[key]
    },
    getSettleListDictByKey: (state) => (key) => {
      return state.settleListDict[key]
    },
    getAllDepartments (state) {
      return state.departments
    },
    getDeptNameByCode: (state) => (deptCode) => {
      return state.departments.filter(value => value.code == deptCode)[0].name
    },
    getFzlx (state) {
      return state.sysConfig_FZLX
    }
  }
}

export default common
