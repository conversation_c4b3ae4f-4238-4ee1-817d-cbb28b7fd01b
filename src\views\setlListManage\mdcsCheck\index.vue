<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :show-date-range="{ show: true }"
             show-hos-dept
             show-se-date-range
             :show-patient-num = "{ show : isTablePage }"
             :container="true"
             :showPagination="isTablePage"
             :totalNum="total"
             headerTitle="查询条件"
              :showCoustemContentTitle="true"
             :exportExcel="{tableId : this.tableId, exportName: this.exportName}"
             :exportExcelFun="queryPageData"
             :exportExcelHasChild="true"
             :initTimeValueNotQuery="false"
             @query="queryData" ref="somForm">

<!--      <template slot="extendFormItems">-->
<!--        <el-form-item label="结算时间" prop="settlementTime">-->
<!--          <el-date-picker-->
<!--            v-model="queryForm.settlementTime"-->
<!--            type="daterange"-->
<!--            class="item-width"-->
<!--            unlink-panels-->
<!--            range-separator="-"-->
<!--            start-placeholder="开始日期"-->
<!--            end-placeholder="结束日期"-->
<!--            value-format="yyyy-MM-dd"-->
<!--            @change="changeSettlementTime">-->
<!--          </el-date-picker>-->
<!--        </el-form-item>-->
<!--      </template>-->

      <template slot="buttons">
        <el-button type="success" class="som-button-margin-right" @click="exportAllError">错误全部导出</el-button>
      </template>

      <template slot="contentTitle">
        <drg-title-line title="清单校对列表">
          <template slot="rightSide">
            <div>
              <i class="som-icon-table som-iconTool"
                 title="表格"
                 v-if="showAnalysis"
                 @click="switchChartTable"
                 style="height: 1.2rem;width: 1.2rem"></i>
              <i class="som-icon-analysis som-iconTool"
                 title="分析"
                 v-if="!showAnalysis"
                 @click="switchChartTable"
                 style="height: 1.2rem;width: 1.2rem"></i>
            </div>
          </template>
        </drg-title-line>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <div class="content-wrapper">

          <div class="som-table-height" v-if="showAnalysis">
            <el-row :gutter="12" class="som-h-one-hundred">
              <el-col :span="12" class="som-h-one-hundred">
                <el-card style="height: 100%">
                  <el-empty v-show="this.chartData.length == 0" style="position: absolute;top: 25%;left: 20%" description="暂无数据"></el-empty>
                  <drg-echarts @chartClick="showErrorDetail" :options="chartOption" />
                </el-card>
              </el-col>
              <el-col :span="12" class="som-h-one-hundred">
                <el-card style="height: 100%">
                  <drg-echarts @chartClick="showErrorDetail" :options="lineOption" />
                </el-card>
              </el-col>
            </el-row>
            <el-row :gutter="12" class="som-h-one-hundred" style="top: 6px" v-if="this.showDeptPage && !this.$store.getters.getDeptCode">
              <el-col :span="12" class="som-h-one-hundred">
                <el-card style="height: 100%">
                  <el-empty v-show="this.deptChartData.length == 0" style="position: absolute;top: 25%;left: 20%" description="暂无数据"></el-empty>
                  <drg-echarts :options="deptChartOption" />
                </el-card>
              </el-col>
              <el-col :span="12" class="som-h-one-hundred">
                <el-card style="height: 100%">
                  <div>
                    <el-select v-model="topVal" placeholder="请选择" @change="topChange">
                      <el-option
                        v-for="item in topOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                  <drg-echarts :options="deptLineOption" :height="'95.5%'" />
                </el-card>
              </el-col>
            </el-row>
          </div>

          <div class="som-table-height" v-if="!showAnalysis">
            <el-empty v-if="this.tempErrorDesc.length == 0" style="height: 100%" description="无错误病案数据"></el-empty>
            <el-tabs class="som-table-height" v-model="checkTab" @tab-click="clickTab" v-else>
              <el-tab-pane class="som-tab-pane"
                           v-for="(item, index) in tempErrorDesc"
                           :key="index"
                           :label="item.label"
                           :name="item.value" style="height: 100%;">
                <tab-table :id="item.value === checkTab ? tableId : ''" :ref="item.value === checkTab ? 'dataTable' : ''" :data="tableData" :loading="tableLoading" @setRefObj="setRefObj($event, item)" />
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import tabTable from './comps/tabTable'
import { queryErrorNum, queryErrorData as queryPageData, queryDeptErrorNum, queryErrorType } from '@/api/listManagement/checkAnalysis'
import { exportAllExcel } from '@/api/common/sysCommon'

export default {
  name: 'mdcsCheck',
  components: {
    tabTable
  },
  data: () => ({
    queryForm: {
      settlementTime: []
    },
    chartData: [],
    chartOption: {},
    lineOption: {},
    deptChartData: [],
    deptChartOption: {},
    deptLineOption: {},
    checkTab: '',
    errDscr: [],
    tempErrorDesc: [],
    tableData: [],
    tableLoading: false,
    showAnalysis: false,
    showDeptPage: true,
    topVal: 10,
    topOptions: [
      { value: 10, label: 'TPP10' },
      { value: 20, label: 'TPP20' }
    ],
    isTablePage: false,
    total: 0,
    tableObj: {},
    tableId: 'tableId',
    exportName: ''

  }),
  mounted () {
    this.showErrorDesc()
    this.$nextTick(() => {
      if (Object.keys(this.$route.query).length > 0) {
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.queryForm)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.queryForm)
        }
        this.switchChartTable()
      }
      // this.queryData()
    })
  },
  methods: {
    queryPageData,
    setRefObj (obj, item) {
      if (item.value === this.checkTab) {
        this.tableObj = obj
      }
    },
    showErrorDesc () {
      this.errDscr = this.$store.getters.getSettleListDictByKey('QDJYCWLX')
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.errType = this.checkTab
      params.limit = this.topVal
      if (this.queryForm.seStartTime && this.queryForm.seEndTime) {
        params.settlementStartTime = this.queryForm.seStartTime
        params.settlementEndTime = this.queryForm.seEndTime
      }
      return params
    },
    changeSettlementTime () {
      this.queryData()
    },
    queryData () {
      if (this.$store.getters.getDeptCode) {
        this.showDeptPage = false
      } else {
        if (this.queryForm.deptCode) {
          this.showDeptPage = false
        } else {
          this.showDeptPage = true
        }
      }
      if (this.showAnalysis) {
        this.showErrorNum()
        this.showDeptErrorNum(1)
        this.showDeptErrorNum(2)
        this.isTablePage = false
      } else {
        this.queryErrorType()
        this.isTablePage = true
      }
    },
    showErrorNum () {
      queryErrorNum(this.getParams()).then(res => {
        this.chartData = res.data
        this.initChart()
        this.initLineChart()
      })
    },
    initChart () {
      let errorData = []
      if (this.chartData.length > 0) {
        for (let i = 0; i < this.chartData.length; i++) {
          for (let j = 0; j < this.errDscr.length; j++) {
            if (this.chartData[i].errType == this.errDscr[j].value) {
              errorData.push({ value: this.chartData[i].errorNum, name: this.errDscr[j].label, type: this.chartData[i].errType })
            }
          }
        }
      }
      this.chartOption = {
        color: this.$somms.generateColor(),
        title: {
          text: '校验错误病案汇总'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 20,
          bottom: 20
        },
        series: [
          {
            type: 'pie',
            height: '90%',
            width: '90%',
            label: {
              formatter: '{b} : {c}例',
              position: 'outside'
            },
            data: errorData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
    },
    initLineChart () {
      let errorName = []
      let errorNum = []
      let colorList = []
      if (this.chartData.length > 0) {
        for (let i = 0; i < this.chartData.length; i++) {
          for (let j = 0; j < this.errDscr.length; j++) {
            if (this.chartData[i].errType == this.errDscr[j].value) {
              errorName.push(this.errDscr[j].label)
              errorNum.push({ value: this.chartData[i].errorNum, type: this.chartData[i].errType })
            }
          }
        }
      }
      this.lineOption = {
        title: {
          text: '校验错误病案数量排名'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          name: '清单校验错误类型/数量',
          data: errorName
        },
        series: [
          {
            type: 'bar',
            label: {
              show: true,
              formatter: '{c}例',
              position: 'right'
            },
            data: errorNum
          }
        ]
      }
    },
    showDeptErrorNum (item) {
      let params = {}
      params = this.getParams()
      params.chartType = item
      queryDeptErrorNum(params).then(res => {
        if (item == 1) {
          this.initDeptChart(res.data)
        }
        if (item == 2) {
          this.initDeptLineChart(res.data)
        }
      })
    },
    initDeptChart (item) {
      let errorData = []
      if (item.length > 0) {
        for (let i = 0; i < item.length; i++) {
          errorData.push({ value: item[i].errorNum, name: item[i].deptName, deptCode: item[i].deptCode })
        }
      }
      this.deptChartOption = {
        color: this.$somms.generateColor(),
        title: {
          text: '科室错误病案汇总'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 20,
          bottom: 20
        },
        series: [
          {
            type: 'pie',
            height: '90%',
            width: '90%',
            data: errorData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
    },
    initDeptLineChart (item) {
      let deptName = []
      let errorNum = []
      let colorList = []
      if (item.length > 0) {
        for (let i = item.length - 1; i >= 0; i--) {
          deptName.push(item[i].deptName)
          errorNum.push({ value: item[i].errorNum, deptCode: item[i].deptCode })
          colorList.push(this.$somms.generateColor()[i])
        }
      }
      this.deptLineOption = {
        title: {
          text: '科室错误病案数量排名'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          name: '科室/数量',
          data: deptName
        },
        series: [
          {
            type: 'bar',
            data: errorNum,
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorList[params.dataIndex]
                }
              }
            }
          }
        ]
      }
    },
    topChange () {
      this.showDeptErrorNum(2)
    },
    switchChartTable () {
      this.showAnalysis = !this.showAnalysis
      if (this.showAnalysis) {
        this.checkTab = '1'
      }
      this.queryData()
    },
    clickTab () {
      this.tableLoading = true
      if (this.tempErrorDesc.length > 0) {
        for (let i = 0; i < this.tempErrorDesc.length; i++) {
          if (this.checkTab == this.tempErrorDesc[i].value) {
            this.exportName = this.tempErrorDesc[i].label
          }
        }
      }
      queryPageData(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.tableLoading = false
      })
    },
    showErrorDetail (item) {
      if (item) {
        this.showAnalysis = !this.showAnalysis
        this.checkTab = item.data.type
        this.queryData()
      }
    },

    queryErrorType () {
      this.tempErrorDesc = []
      queryErrorType(this.getParams()).then(res => {
        if (res.data) {
          for (let i = 0; i < this.errDscr.length; i++) {
            for (let j = 0; j < res.data.length; j++) {
              if (res.data[j] == this.errDscr[i].value) {
                this.tempErrorDesc.push(this.errDscr[i])
              }
            }
          }
          if (this.tempErrorDesc.length > 0) {
            this.checkTab = this.tempErrorDesc[0].value
          }

          this.clickTab()
        }
      })
    },

    exportAllError () {
      let params = this.getParams()
      params.errType = ''
      params.pageSize = '30000'
      let childrenList = []
      this.$refs.dataTable[0].setTableObj()
      childrenList = this.tableObj.$children
      let tableNodes = document.getElementById(this.tableId).children[1].children[0].children[1].children[0].childNodes
      let columnPropList = []
      let exportTableList = []
      let exportTableColumnNameList = []

      tableNodes.forEach(child => {
        if (child.innerText != '序号') {
          if (child.innerText != '') {
            exportTableColumnNameList.push(child.innerText)
          } else {
            if (child.children[0] && child.children[0].innerHTML != '序号') {
              exportTableColumnNameList.push(child.children[0].innerHTML)
            }
          }
        }
      })

      let deleteArr = []
      let tableColumnIndex = 0
      exportTableColumnNameList.forEach(columnName => {
        for (let i = 0; i < childrenList.length; i++) {
          if (childrenList[i].label == columnName) {
            if (childrenList[i].prop == undefined) {
              deleteArr.push(tableColumnIndex)
            }
            columnPropList.push(childrenList[i].prop)
            break
          }
        }
        tableColumnIndex++
      })

      for (let i = deleteArr.length - 1; i >= 0; i--) {
        exportTableColumnNameList.splice(deleteArr[i], 1)
        columnPropList.splice(deleteArr[i], 1)
      }

      this.queryPageData(params).then(res => {
        if (res.code == 200) {
          let resDataList = res.data.list
          if (resDataList && resDataList.length > 0) {
            resDataList.forEach(data => {
              let exportTableRow = []
              columnPropList.forEach(column => {
                exportTableRow.push(data[column])
              })
              exportTableList.push(exportTableRow)
            })
          }

          if (exportTableList.length > 0 && exportTableColumnNameList.length > 0) {
            let exportParams = {}
            exportParams.columns = exportTableColumnNameList
            exportParams.data = exportTableList
            exportAllExcel(exportParams).then(res => {
              this.$somms.download(res, '错误病例_所有.xlsx', 'application/vnd.ms-excel')
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  position: relative;

  &-chart-table {
    position: absolute;
    top: -7%;
    right: 0;
  }
}
.som-table-height {
  width: 100%;
}
/deep/ .el-tabs__content {
  height: 98%;
}
</style>
