-- ========================================
-- 推荐的最佳优化查询方案
-- ========================================

-- 🔥 方案A: EXISTS优化版本（强烈推荐）
-- 优势：避免大表JOIN，使用EXISTS通常比JOIN更高效
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_valid_result_inhosp a
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
WHERE
  a.rule_scen_type = '1'
  AND EXISTS (
    SELECT 1 FROM hcm_settle_zy_b e 
    WHERE e.hisid = a.unique_id 
    AND e.discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
  )
LIMIT 200;

-- 🔥 方案B: 子查询预过滤版本（次推荐）
-- 优势：先过滤出小结果集，再进行复杂JOIN
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  (
    SELECT * FROM hcm_valid_result_inhosp 
    WHERE rule_scen_type = '1'
    AND unique_id IN (
      SELECT hisid FROM hcm_settle_zy_b 
      WHERE discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
    )
  ) a
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
LIMIT 200;

-- 🔥 方案C: 如果需要创建索引，建议的索引
-- 请在数据库中执行以下索引创建语句：

/*
-- 核心索引（必须创建）
CREATE INDEX idx_hcm_valid_result_inhosp_rule_scen ON hcm_valid_result_inhosp(rule_scen_type);
CREATE INDEX idx_hcm_valid_result_inhosp_unique_id ON hcm_valid_result_inhosp(unique_id);
CREATE INDEX idx_hcm_settle_zy_b_discharge_date ON hcm_settle_zy_b(discharge_date);
CREATE INDEX idx_hcm_settle_zy_b_hisid ON hcm_settle_zy_b(hisid);

-- JOIN优化索引
CREATE INDEX idx_hcm_data_grp_cfg_composite ON hcm_data_grp_cfg(data_code, rule_year, data_grp_code);
CREATE INDEX idx_hcm_rule_cfg_composite ON hcm_rule_cfg(rule_detl_codg, rule_year);

-- 复合索引（进一步优化）
CREATE INDEX idx_hcm_settle_zy_b_date_hisid ON hcm_settle_zy_b(discharge_date, hisid);
CREATE INDEX idx_hcm_valid_result_inhosp_scen_unique ON hcm_valid_result_inhosp(rule_scen_type, unique_id);
*/

-- 🔥 方案D: 临时表方式（适用于复杂业务场景）
-- 如果上述方案仍然慢，可以使用这种分步查询方式：

/*
-- 步骤1: 创建临时表存储符合条件的患者
CREATE TEMPORARY TABLE temp_patients AS
SELECT DISTINCT hisid 
FROM hcm_settle_zy_b 
WHERE discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59';

-- 步骤2: 基于临时表查询
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_valid_result_inhosp a
  INNER JOIN temp_patients t ON a.unique_id = t.hisid
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
WHERE
  a.rule_scen_type = '1'
LIMIT 200;

-- 步骤3: 清理临时表
DROP TEMPORARY TABLE temp_patients;
*/

-- ========================================
-- 测试和验证建议
-- ========================================

-- 1. 执行计划分析
-- EXPLAIN SELECT ... (使用上述任一优化方案)

-- 2. 性能对比测试
-- 分别测试原查询和优化后查询的执行时间

-- 3. 索引使用验证
-- SHOW INDEX FROM hcm_valid_result_inhosp;
-- SHOW INDEX FROM hcm_settle_zy_b;
-- SHOW INDEX FROM hcm_data_grp_cfg;
-- SHOW INDEX FROM hcm_rule_cfg;

-- ========================================
-- 预期优化效果
-- ========================================
-- 1. 查询时间从几十秒降低到几百毫秒
-- 2. 执行计划显示使用索引而非全表扫描
-- 3. LIMIT 200 比不加LIMIT更快（正常情况）
-- 4. 减少内存使用和CPU消耗
