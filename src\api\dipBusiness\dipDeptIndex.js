import request from '@/utils/request'
export function fetchList (params) {
  return request({
    url: '/dipDeptIndex/getList',
    method: 'post',
    params: params
  })
}

export function getCountInfo (params) {
  return request({
    url: '/dipDeptIndex/getCountInfo',
    method: 'post',
    params: params
  })
}

export function queryConsumptionIndex (params) {
  return request({
    url: '/dipDeptIndex/queryConsumptionIndex',
    method: 'post',
    params: params
  })
}
