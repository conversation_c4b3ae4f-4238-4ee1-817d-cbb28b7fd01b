#app {

  // 主体区域
  .main-container {
    height: 100%;
    transition: margin-left .28s;
    margin-left: 200px;
    display: flex;
    flex-direction: column;
  }

   // 侧边栏
  .sidebar-container {
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }
    transition: width .28s;
    width: 200px !important;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    //overflow: hidden;
    a {
      display: inline-block;
      width: 100%;
    }
    .svg-icon {
      margin-right: 8px;
    }
    .el-menu {
      border: none;
      width: 100% !important;
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 70px !important;
    }
    .main-container {
      margin-left: 70px;
    }
    .submenu-title-noDropdown {
      padding-left: 40px !important;
      position: relative;
      .el-tooltip {
        padding: 0 40px !important;
      }
    }
    .el-submenu {
      &>.el-submenu__title {
        padding-left: 5px !important;
        &>span {
          height: 0;
          width: 0;
          overflow: hidden;
          visibility: hidden;
          display: inline-block;
        }
        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }
  }

  .sidebar-container .nest-menu .el-submenu>.el-submenu__title,
  .sidebar-container .el-submenu .el-menu-item {
    min-width: 200px !important;
    background-color: $subMenuBg !important;
    &:hover {
      background-color: $menuHover !important;
    }
  }
  .el-menu--collapse .el-menu .el-submenu {
    min-width: 200px !important;
  }

  //适配移动端
  //.mobile {
  //  .main-container {
  //    margin-left: 0px;
  //  }
  //  .sidebar-container {
  //    top: 50px;
  //    transition: transform .28s;
  //    width: 180px !important;
  //  }
  //  &.hideSidebar {
  //    .sidebar-container {
  //      transition-duration: 0.3s;
  //      transform: translate3d(-180px, 0, 0);
  //    }
  //  }
  //}

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}
