<template>
  <div class="app-container">
    <drg-container :headerPercent="11" :type="1">
      <template slot="header">
        <drg-title-line title="查询条件"/>
        <el-form :inline="true" :model="queryForm"  ref="queryForm" size="mini" style="height: 55%">
          <el-form-item label="上传时间">
            <el-date-picker
              v-model="queryForm.updt_date"
              :clearable="false"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item class="som-el-form-item-margin-left">
            <el-button type="primary" @click="queryData">查询</el-button>
            <el-button type="primary" @click="dialogVisible = true"><i class="el-icon-upload el-icon--left"></i>文件上传</el-button>
            <el-button type="primary" @click="downloadTemplate"><i class="el-icon-download el-icon--left"></i>模板下载</el-button>
             <el-button @click="refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="上传日志列表"/>
        <el-table :data="tableData"
                  border
                  highlight-current-row
                  size="mini"
                  :header-cell-style="{'text-align':'center'}"
                  v-loading="tableLoading"
                  style="width: 100%; height: 95%">
          <el-table-column
            prop="id"
            label="日志ID"
            align="right">
          </el-table-column>

          <el-table-column
            prop="fileName"
            align="left"
            label="文件名称">
          </el-table-column>

          <el-table-column
            prop="fileUpldCnt"
            label="文件上传数量"
            align="right">
          </el-table-column>

          <el-table-column
            prop="fileUpldTimeTime"
            align="right"
            label="文件上传时间">
          </el-table-column>

          <el-table-column
            prop="errMsg"
            align="left"
            label="错误信息">
          </el-table-column>

          <el-table-column
            prop="state"
            width="80"
            align="center"
            label="状态">
            <template slot-scope="scope">
              <i class="el-icon-success" v-if="scope.row.state == 1" style="font-size: 1.3rem;color: green"></i>
              <i class="el-icon-error" v-else style="font-size: 1.3rem;color: red"></i>
            </template>
          </el-table-column>
        </el-table>

        <el-dialog
          title="上传文件"
          :visible.sync="dialogVisible"
          width="50%">
          <el-upload
            style="text-align: center"
            drag
            ref="upload"
            :limit="1"
            action="customize"
            accept=".xlsx,.xls"
            :http-request="upload">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
          </el-upload>
        </el-dialog>
      </template>
    </drg-container>
  </div>
</template>

<script>
import { workersUpload } from '@/api/upload/upload'
import { downloadworkersTemplate } from '@/api/download/workersdownload'
import { workersFileUploadLog } from '@/api/log/index'
import moment from 'moment'

export default {
  name: 'workersUpload',
  inject: ['reload'],
  data () {
    return {
      queryForm: {
        updt_date: []
      },
      dialogVisible: false,
      tableData: [],
      tableLoading: false
    }
  },
  created () {
    this.getCurrentMonthFirst()
    this.getCurrentMonthLast()
  },
  mounted () {
    this.queryData()
  },
  methods: {
    queryData () {
      let params = this.queryForm
      if (params.updt_date) {
        params.begnDate = this.queryForm.updt_date[0]
        params.expiDate = this.queryForm.updt_date[1]
      }
      workersFileUploadLog(params).then(res => {
        this.tableData = res.data
      })
    },
    upload (data) {
      let params = new FormData()
      params.append('file', data.file)
      workersUpload(params).then(res => {
        if (res.code == 200) {
          this.dialogVisible = false
          this.$refs.upload.clearFiles()
          this.$message.success('上传成功')
          this.queryData()
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
        this.queryData()
      })
    },
    downloadTemplate () {
      downloadworkersTemplate().then(res => {
        this.downloadExcel(res, '医护人员信息模板')
      })
    },
    downloadExcel (blobPart, filename) {
      const blob = new Blob([blobPart], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      // const blob = new Blob([blobPart], { type: 'application/vnd.ms-excel' })
      // 创建一个超链接，将文件流赋进去，然后实现这个超链接的单击事件
      const elink = document.createElement('a')
      elink.download = decodeURIComponent(filename)
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    },
    getCurrentMonthFirst () {
      let updt_date = new Date()
      updt_date.setDate(1)
      let month = parseInt(updt_date.getMonth() + 1)
      let day = updt_date.getDate()
      if (month < 10) month = '0' + month
      if (day < 10) day = '0' + day
      let first = updt_date.getFullYear() + '-' + month + '-' + day
      this.queryForm.updt_date[0] = first
    },
    getCurrentMonthLast () {
      let updt_date = new Date()
      let month = parseInt(updt_date.getMonth() + 1)
      let day = updt_date.getDate()
      if (month < 10) month = '0' + month
      if (day < 10) day = '0' + day
      let last = updt_date.getFullYear() + '-' + month + '-' + day
      this.queryForm.updt_date[1] = last
    },
    refresh () {
      this.reload()
    }
  }
}
</script>

<style scoped>
</style>
