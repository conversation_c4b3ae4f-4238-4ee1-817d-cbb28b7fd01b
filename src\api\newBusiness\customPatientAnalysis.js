import request from '@/utils/request'

/**
 * 查询患者基本信息数据
 * @param params
 * @returns {*}
 */
export function queryPatientBasicInfoData (params) {
  return request({
    url: '/newDipBusinessCustomPatientAnalysisController/queryPatientBasicInfoData',
    method: 'post',
    data: params
  })
}

export function queryPatientCostInfoData (params) {
  return request({
    url: '/newDipBusinessCustomPatientAnalysisController/queryPatientCostInfoData',
    method: 'post',
    data: params
  })
}

export function queryPatientForecastData (params) {
  return request({
    url: '/newDipBusinessCustomPatientAnalysisController/queryPatientForecastData',
    method: 'post',
    data: params
  })
}

export function queryDrgPatientBasicInfoData (params) {
  return request({
    url: '/newDrgBusinessCustomPatientAnalysisController/queryDrgPatientBasicInfoData',
    method: 'post',
    data: params
  })
}

export function queryDrgPatientCostInfoData (params) {
  return request({
    url: '/newDrgBusinessCustomPatientAnalysisController/queryDrgPatientCostInfoData',
    method: 'post',
    data: params
  })
}

export function queryDrgPatientForecastData (params) {
  return request({
    url: '/newDrgBusinessCustomPatientAnalysisController/queryDrgPatientForecastData',
    method: 'post',
    data: params
  })
}

export function queryDrgPatientBasicSortInfo (params) {
  return request({
    url: '/newDrgBusinessCustomPatientAnalysisController/queryDrgPatientBasicSortInfo',
    method: 'post',
    data: params
  })
}
