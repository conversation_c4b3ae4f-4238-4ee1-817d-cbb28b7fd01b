<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-date-range
             show-se-date-range
             showPagination
             show-patient-num
             :show-hos-dept="{ show: this.$somms.hasHosRole()}"
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="可优化病案列表"
             :exportExcel="{ 'tableId': tableId, exportName: exportTableName}"
             :exportExcelFun="queryPageData"
             :exportExcelHasChild="true"
             :container="true"
             :initTimeValueNotQuery="false"
             @query="queryData" ref="somForm">

      <template slot="buttons">
        <el-button type="success"
                   @click="showCalculationDialog"
                   class="som-button-margin-right">测算</el-button>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <div style="height: 96%">
          <optimize-medical-list :id="tableId" ref="dataTable" :data="tableData" :loading="loading" @showPatientDrawer="showPatientDrawer" @setRefObj="(obj) => this.tableObj = obj" />
        </div>
        <el-dialog title="月份选择"
                   width="25%"
                   :show-close="false"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :visible.sync="dialogFormVisible">
          <el-form :model="dialogForm">
            <el-form-item label="月份">
              <el-date-picker v-model="dialogForm.dateRange"
                              type="monthrange"
                              range-separator="-"
                              start-placeholder="开始月份"
                              end-placeholder="结束月份" value-format="yyyy-MM" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="calculationCancel">取消</el-button>
            <el-button type="success" @click="calculationStart">开始测算</el-button>
          </div>
        </el-dialog>
        <el-drawer title="优化病组"
                   :visible.sync="drawerTableVisible" direction="rtl">
          <el-card class="box-card">
            <el-descriptions class="margin-top" title="基本信息" :column="2" size="mini">
              <el-descriptions-item label="姓名">
                {{ drawerPatientData.name }}
              </el-descriptions-item>
              <el-descriptions-item label="住院总费用">
                {{ drawerPatientData.inHosTotalCost }}
              </el-descriptions-item>
              <el-descriptions-item label="DIP编码">
                {{ drawerPatientData.dipCodg }}
              </el-descriptions-item>
              <el-descriptions-item label="DIP名称">
                {{ drawerPatientData.dipName }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
          <el-table :data="drawerData">
            <el-table-column label="DIP编码" prop="dipCodg" show-overflow-tooltip></el-table-column>
            <el-table-column label="DIP名称" prop="dipName" show-overflow-tooltip></el-table-column>
            <el-table-column label="主要诊断" prop="mainDiag" show-overflow-tooltip></el-table-column>
            <el-table-column label="标杆费用" prop="standardFee"></el-table-column>
            <el-table-column label="推荐">
              <template slot-scope="scope" v-if="scope.row.i == 1">
                <img :src="require('@/assets/images/五星 (1).png')">
                <img :src="require('@/assets/images/五星 (1).png')">
                <img :src="require('@/assets/images/五星 (1).png')">
              </template>
            </el-table-column>
          </el-table>
        </el-drawer>
      </template>
    </drg-form>
  </div>
</template>

<script>
import optimizeMedicalList from '@/views/auliManage/grpOptm/comps/optimizeMedicalList'
import { calculationData, drgCalculationData,
  queryPatientInfo as queryPageData, queryGroupsInfo } from '@/api/groupManagement/medicalManagement'

export default {
  name: 'grpOptm',
  components: {
    optimizeMedicalList
  },
  data: () => ({
    queryForm: {},
    dialogFormVisible: false,
    dialogForm: {
      dateRange: [],
      begnDate: '',
      expiDate: ''
    },
    total: 0,
    tableData: [],
    loading: false,
    drawerTableVisible: false,
    patientData: {},
    drawerData: [],
    drawerPatientData: {},
    tableId: 'elTable',
    exportTableName: '可优化病案',
    tableObj: {}
  }),
  mounted () {
    this.$nextTick(() => {
      if (Object.keys(this.$route.query).length > 0) {
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.queryForm)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.queryForm)
        }
      } else {
        this.getMonthRange()
      }
      this.queryData()
    })
  },
  methods: {
    queryPageData,
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.grperType = this.$somms.getGroupType()
      return params
    },
    getMonthRange () {
      this.dialogForm.begnDate = this.$somms.getDate('yyyy-MM', 0, 0, 0)
      this.dialogForm.expiDate = this.$somms.getDate('yyyy-MM', 0, 0, 0)
      this.dialogForm.dateRange = [this.dialogForm.begnDate, this.dialogForm.expiDate]
    },
    queryData () {
      this.loading = true
      queryPageData(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
      })
    },
    showCalculationDialog () {
      this.dialogFormVisible = true
    },
    calculationStart () {
      this.dialogFormVisible = false
      this.$message({
        message: '测算开始',
        type: 'success'
      })
      this.dialogForm.begnDate = this.dialogForm.dateRange[0]
      this.dialogForm.expiDate = this.dialogForm.dateRange[1]
      this.dialogForm.grperType = this.$somms.getGroupType()
      this.loading = true
      if (this.$somms.getGroupType() == 1) {
        calculationData(this.dialogForm).then(res => {
          if (res.data) {
            this.$message({
              message: '测算完成',
              type: 'success'
            })
            this.queryData()
          }
        })
      } else if (this.$somms.getGroupType() == 3) {
        drgCalculationData(this.dialogForm).then(res => {
          if (res.data) {
            this.$message({
              message: '测算完成',
              type: 'success'
            })
            this.queryData()
          }
        })
      }
    },
    calculationCancel () {
      this.dialogFormVisible = false
      this.$message({
        message: '已取消测算'
      })
    },
    showPatientDrawer (item) {
      this.drawerTableVisible = true
      this.drawerPatientData = item
      let params = {}
      if (item) {
        params.settleListId = item.settleListId
        params.grperType = item.grperType
        queryGroupsInfo(params).then(res => {
          this.drawerData = res.data
        })
      }
    }
  }
}
</script>

<style scoped>
.box-card {
  width: 96%;
  margin: 2%;
}
</style>
