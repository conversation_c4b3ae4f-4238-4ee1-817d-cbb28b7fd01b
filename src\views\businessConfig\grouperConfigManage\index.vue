<template>
  <div class="app-container">
    <drg-container :headerPercent="10">
      <template slot="header">
        <drg-title-line title="查询条件" />
        <el-form :inline="true" :model="queryForm" size="mini">
          <el-form-item
            prop="ver"
            label="分组器版本">
            <el-input  placeholder="分组器版本" v-model="queryForm.configKey"></el-input>
          </el-form-item>
          <el-form-item label="分组器地址" size="mini">
            <el-input  placeholder="分组器地址" v-model="queryForm.configValue"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getData">查 询</el-button>
            <el-button @click="refresh">刷 新</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="参数" />
        <div style="height: 90%;width: 100%;overflow-y:auto;">
          <el-table :data="tableData"
                    border
                    highlight-current-row
                    size="mini"
                    v-loading="tableLoading"
                    style="width: 100%; height: 100%">
            <el-table-column
              prop="id"
              label="ID">
            </el-table-column>

            <el-table-column
              prop="ver"
              label="分组器版本">
            </el-table-column>

            <el-table-column
              prop="url"
              label="分组器地址">
            </el-table-column>

            <el-table-column
              prop="activeflag"
              label="有效标志">
            </el-table-column>

            <el-table-column label="操作" width="100px" align="center">
              <template #default="scope">
                <el-button
                  size="mini"
                  icon="el-icon-edit"
                  type="primary"
                  circle
                  @click="handleEdit(scope.$index, scope.row)"></el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 编辑 -->
          <el-dialog
            title="编辑"
            ref="editForm"
            width="30%"
            :visible.sync="editVisible">
            <el-form :model="editForm" size="mini" >
              <el-row>
                <el-col :span="24">
                  <el-form-item
                    prop="ver"
                    label="分组器版本">
                    <el-input disabled v-model="editForm.configKey"/>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    prop="url"
                    label="分组器地址">
                    <el-input  placeholder="分组器地址" v-model="editForm.configValue"></el-input>
                    <!--   <i class="el-icon-question" style="position: absolute; right: -25px;bottom: 6px;cursor: pointer" @click="configQuestion"></i> -->
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <template #footer>
          <span class="dialog-footer">
            <el-button @click="editCancel" size="mini" >取 消</el-button>
            <el-button type="primary" size="mini" @click="saveConfig" v-model="saveEnable">保 存</el-button>
          </span>
            </template>
          </el-dialog>
        </div>
      </template>
    </drg-container>
  </div>
</template>

<script>
import { queryData, update } from '@/api/dataConfig/bursterConfig'
export default {
  name: 'grouperConfigManage',
  inject: ['reload'],
  data: () => ({
    tableData: [],
    tableLoading: false,
    editVisible: false,
    queryForm: {
      configKey: '',
      configValue: ''
    },
    editForm: {
      id: '',
      configKey: '',
      configValue: ''
    },
    configValue: 'null',
    saveEnable: true

  }),
  mounted () {
    this.getData()
  },
  methods: {
    getData () {
      this.tableLoading = true
      queryData(this.queryForm).then((result) => {
        this.tableData = result.data
        this.tableLoading = false
      })
    },
    refresh () {
      this.reload()
    },
    handleEdit (index, row) {
      this.configValue = row.url
      this.editForm.id = row.id
      this.editForm.configKey = row.ver
      this.editForm.configValue = row.url
      this.editVisible = true
    },
    // configQuestion () {
    //     this.$notify({
    //         profttl: '提示',
    //         message: "1：DRG分组，2：DIP分组，3：成都分组",
    //         duration: 3000
    //     });
    // },
    editCancel () {
      this.$confirm('关闭后所做修改将不会保存,是否确认关闭？')
        .then(_ => {
          this.editVisible = false
        })
        .catch(_ => {
          this.editVisible = false
        })
    },
    saveConfig () {
      if (this.saveEnable != null) {
        // let loadingInstance = null
        this.$confirm('这操作将重新生成数据,是否确认修改？')
          .then(_ => {
            update(this.editForm).then((result) => {
              this.tableData = result.data
              this.editVisible = false
              this.getData()
              this.$message({
                message: '修改成功！',
                type: 'success'
              })
            })
          })
          .catch(_ => { this.editVisible = false })
      }
    }
  }
}
</script>
