<template>
  <div>
    <el-select v-model="value"
               filterable remote clearable
               :remote-method="(str) => getCode(str)"
               :style="{ width:'80%' }"
               @change="change">
      <el-option
          v-for="item in selectList"
          :key="getBindValue(item)"
          :label="getBindValue(item)"
          :value="getBindValue(item)">
        <span class="code">{{ item.icdCodg }}</span>
        <span class="name">{{ item.icdName }}</span>
      </el-option>
    </el-select>
    <modify-icon :show="showIcon" :use-position="false"/>
  </div>
</template>
<script>
import ModifyIcon from './settleListInfoModifyIcon'

export default {
  components: {
    'modify-icon': ModifyIcon
  },
  props: {
    // v-model 绑定值
    modelVal: [String, Number],
    // icd类型
    icdType: [String],
    // 编码还是名称
    codeOrName: [String],
    // 诊断编码
    diagnosisCodeArr: [Array],
    // 手术编码
    operationCodeArr: [Array],
    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: 'modelVal',
    event: 'selected'
  },
  data: () => ({
    selectList: [],
    value: ''
  }),
  beforeDestroy() {
    this.selectList = null
    //重置data数据
    Object.assign(this.$data, this.$options.data())
    // console.log('=========icd beforeDestroy=========')
  },
  destroyed() {
    // console.log('=========icd destroyed=========')
  },
  methods: {
    getCode(str) {
      let arr = []
      if (this.icdType === 'dis') {
        arr = this.diagnosisCodeArr
      } else {
        arr = this.operationCodeArr
      }
      this.selectList = []
      let count = 0
      let maxCount = 200
      for (let i = 0; i < arr.length; i++) {
        if (count === maxCount) {
          break
        }
        let dis = arr[i]
        let inFlag = false
        if (this.codeOrName === 'name' && dis.icdName.includes(str)) {
          this.selectList.push(dis)
          inFlag = true
        } else if (this.codeOrName === 'code' && dis.icdCodg.includes(str)) {
          this.selectList.push(dis)
          inFlag = true
        }
        if (inFlag) {
          count++
        }
      }
    },
    change(val) {
      let data = {}
      if (this.codeOrName === 'name') {
        data.label = val
      } else {
        data.value = val
      }
      for (let item of this.selectList) {
        if (this.codeOrName === 'name' && item.icdName === val) {
          data.value = item.icdCodg
        } else if (this.codeOrName === 'code' && item.icdCodg === val) {
          data.label = item.icdName
        }
      }
      this.$emit('selected', val)
      this.$emit('change', data)
    },
    // 获取绑定字段
    getBindValue(item) {
      if (this.codeOrName === 'name') {
        return item.icdName
      }
      return item.icdCodg
    }
  },
  watch: {
    modelVal: {
      immediate: true,
      handler: function (val) {
        this.value = val
      }
    }
  }
}
</script>
<style scoped lang="scss">
.code {
  float: right;
  color: #8492a6;
  font-size: 13px;
}

.name {
  float: left;
}
</style>
