<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :show-patient-num="{show: true, clearable: true}"
             headerTitle="查询条件"
             contentTitle="数据校验情况"
             :container="true"
             showPagination
             :totalNum="total"
             @query="queryData">
      <!-- 条件 -->
      <template slot="extendFormItems">
        <el-form-item label="出院日期" prop="updt_date">
          <el-date-picker
            style="width: 100%"
            v-model="queryForm.updt_date"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="唯一ID" prop="uniqueId">
          <el-input  v-model="queryForm.uniqueId" clearable></el-input>
        </el-form-item>
        <el-form-item label="患者姓名" prop="name">
          <el-input  v-model="queryForm.name" clearable></el-input>
        </el-form-item>
        <el-form-item label="出院科室代码" prop="dept">
          <el-input  v-model="queryForm.dept" clearable></el-input>
        </el-form-item>
        <el-form-item label="住院医师代码" prop="drCodg">
          <el-input  v-model="queryForm.doctor" clearable></el-input>
        </el-form-item>
      </template>
      <!-- 按钮 -->
      <template slot="buttons">
        <el-button class="som-button-margin-right" type="success" :disabled="syncFlag" @click="syncData">同步</el-button>
        <el-button class="som-button-margin-right" type="warning" @click="syncAllData">全部同步</el-button>
        <el-tooltip class="item" effect="dark" content="Excel中只需要一列且列名为[唯一ID]" placement="top-start">
          <el-upload
             action="customize"
             :limit="1"
             :multiple="false"
             :show-file-list="false"
            accept=".xlsx,.xls"
            :http-request="fileUpload" >
            <el-button class="som-button-margin-right" type="primary">文件上传</el-button>
          </el-upload>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="同步所选日期DIP系统没有的数据" placement="top">
          <el-button class="som-button-margin-right" type="danger" @click="diffSync">差量同步</el-button>
        </el-tooltip>
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <el-tabs v-model="activeName" @tab-click="queryData">
          <el-tab-pane label="病案数据" name="med">
            <list-table :data="tableData" :loading="loading" @selectNode="selectNode" @success="queryData" ref="listTableRef"/>
          </el-tab-pane>
          <el-tab-pane label="同步记录" name="logs">
            <log-table :data="logData"/>
          </el-tab-pane>
        </el-tabs>

        <el-dialog :visible.sync="showDialog" width="30%" title="同步数据">
          <el-table
            :data="SyncName"
            border
            :header-cell-style="{'text-align':'center'}"
            @selection-change="selectSyncName"
            stripe>
            <el-table-column type="selection" align="center" width="40"/>
            <el-table-column label="同步数据类型" prop="syncName" align="center"/>
          </el-table>
          <el-button @click="showDialog = false">取 消</el-button>
          <el-button type="primary" @click="SyncData" :disabled="isManualSync">确 定</el-button>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>

<script>
import listTable from './comps/listTable'
import LogTable from './comps/logTable.vue'
import { QueryData, QueryResident, QuerySyncName, ManualSyncData, syncAllData, uploadData, diffSyncData, querySyncData } from '@/api/dataHandle/dataSynchronization'

export default {
  name: 'dataSynchronization',
  components: {
    listTable,
    LogTable
  },
  data: () => ({
    queryForm: {},
    total: 0,
    tableData: [],
    residents: [],
    isSync: true,
    isManualSync: true,
    showDialog: false,
    loading: false,
    SyncName: [],
    // 需要同步的案例和同步类型
    patients: [],
    selectType: [],
    uniqueIdArr: [],
    logData: [],
    activeName: 'med'
  }),
  mounted () {
  },
  computed: {
    syncFlag () {
      return this.uniqueIdArr.length === 0
    }
  },
  methods: {
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    fileUpload (data) {
      let params = new FormData()
      params.append('file', data.file)
      this.loading = true
      uploadData(params).then(res => {
        this.$message.success('上传成功')
        this.loading = false
      }).catch(_ => { this.loading = false })
    },
    queryData () {
      this.loading = true
      if (this.activeName === 'med') {
        if (!this.queryForm.updt_date) {
          this.$message.warning('请选择日期')
          return
        }
        QueryData(this.getParams()).then(res => {
          this.tableData = res.data.list
          this.total = res.data.total
          this.loading = false
        }).catch(_ => { this.loading = false })
        QueryResident(this.getParams()).then(res => {
          this.residents = res.data
        })
      } else {
        querySyncData(this.getParams()).then(res => {
          this.logData = res.data.list
          this.total = res.data.total
          this.loading = false
        }).catch(_ => { this.loading = false })
      }
    },
    selectNode (items) {
      let uniqueIdArr = []
      // 获取唯一id
      if (items && items.length > 0) {
        items.forEach(item => {
          if (item.uniqueId) {
            uniqueIdArr.push(item.uniqueId.trim())
          }
        })
      }
      this.uniqueIdArr = uniqueIdArr
    },
    diffSync () {
      if (!this.queryForm.updt_date) {
        this.$message.warning('请选择日期')
        return
      }
      this.loading = true
      diffSyncData(this.getParams()).then(res => {
        this.syncSuccess(res)
      }).catch(_ => { this.loading = false })
    },
    syncData () {
      this.$refs.listTableRef.sync(this.uniqueIdArr)
    },
    syncAllData () {
      if (!this.queryForm.updt_date) {
        this.$message.warning('请选择日期')
        return
      }
      this.loading = true
      this.$confirm('是否全部同步，一次最多同步 5000 条数据？', '同步提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        syncAllData(this.getParams()).then(res => {
          this.syncSuccess(res)
        }).catch(_ => { this.loading = false })
      })
    },
    syncSuccess (res) {
      if (res.code === 200) {
        this.$message.success('同步成功')
      }
      this.loading = false
    },
    QuerySyncName () {
      QuerySyncName().then(res => {
        this.SyncName = res.data
      })
      this.showDialog = true
    },
    selectSyncName (item) {
      this.selectType = item
      if (item.length > 0) {
        this.isManualSync = false
      } else {
        this.isManualSync = true
      }
    },
    SyncData () {
      this.showDialog = false
      let params = {}
      params.patients = this.patients
      params.selectType = this.selectType
      ManualSyncData(params).then(res => {

      })
    }
  }
}
</script>

<style scoped >

/deep/ .el-tabs {
  height: calc(100% - 60px);
}

/deep/ .el-tabs__content, .el-tab-pane {
  height: 100%;
}
</style>
