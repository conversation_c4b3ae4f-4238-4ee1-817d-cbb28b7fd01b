<template>
  <div class="app-container">
    <drg-form
      v-model="queryForm"
      :totalNum="total"
      container
      showPagination
      headerTitle="查询条件"
      contentTitle="国考数据项配置"
      @query="query"
    >
      <!-- 查询条件插槽 -->
      <template slot="extendFormItems">
        <el-form-item label="项目">
          <el-input v-model="queryForm.searchText" placement="请输入项目编码/名称"></el-input>
        </el-form-item>
        <el-form-item label="数据来源">
          <drg-dict-select dicType="HOS_PERF_DS" v-model="queryForm.dataSource" />
        </el-form-item>
        <el-form-item label="说明">
          <el-input v-model="queryForm.desc"></el-input>
        </el-form-item>
      </template>
      <!-- 按钮插槽 -->
      <template slot="buttons">
        <el-button @click="processData(true)" type="primary" class="som-button-margin-right">新增</el-button>
      </template>
      <!-- 内容插槽 -->
      <template slot="containerContent">
        <el-table :data="tableData" height="100%" :row-style="{ cursor: 'pointer' }" v-loading="loading">
          <el-table-column fixed label="序号" type="index" width="50"> </el-table-column>
          <el-table-column label="项目编码" prop="dsItemCode" />
          <el-table-column label="项目名称" prop="dsItemName" />
          <drg-table-column label="数据来源" prop="dataSource" dict-type="HOS_PERF_DS" />
          <el-table-column label="查询时的字段名称" prop="queryField" />
          <el-table-column label="说明" prop="desc" show-overflow-tooltip />
          <drg-table-column label="启用标准" prop="activeFlag" dict-type="STATUS" />
          <el-table-column label="创建时间" prop="crteTime" />
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-tooltip content="修改" placement="top" effect="light">
                <i class="el-icon-edit" style="font-size: 20px" @click="processData(false, scope.row)"></i>
              </el-tooltip>
              <el-tooltip content="删除" placement="top" effect="light">
                <i class="el-icon-delete" style="font-size: 20px" @click="remove(scope.row)"></i>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>

        <!-- 新增或编辑 -->
        <el-dialog title="提示" :visible.sync="showVisible" width="30%" :before-close="beforeCloseAction">
          <el-form :model="actionData.form" :rules="actionData.rules" ref="ruleForm" label-width="100px">
            <el-form-item label="项目编码" prop="dsItemCode" required>
              <el-input v-model="actionData.form.dsItemCode" @input="changeValue"></el-input>
            </el-form-item>
            <el-form-item label="项目名称" prop="dsItemName" required>
              <el-input v-model="actionData.form.dsItemName" @input="changeValue"></el-input>
            </el-form-item>
            <el-form-item label="数据来源" prop="dataSource" required>
              <drg-dict-select dicType="HOS_PERF_DS" v-model="actionData.form.dataSource" @change="changeDataSource" />
            </el-form-item>
            <el-form-item label="字段名称" prop="queryField" v-show="actionData.form.dataSource === '2'">
              <el-input v-model="actionData.form.queryField" @input="changeValue"></el-input>
            </el-form-item>
            <el-form-item label="说明" prop="desc">
              <el-input v-model="actionData.form.desc" type="textarea" @input="changeValue"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="beforeCloseAction">取 消</el-button>
            <el-button type="primary" @click="handlerItem">{{ state ? '新 增' : '更 新' }}</el-button>
          </span>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>

<script>
import { elExportExcel } from '@/utils/exportExcel'
import moment from 'moment'
import { queryData, addDsItem, updateDsItem, removeDsItem } from '@/api/hosPerfAppraisal/dsItemConfig'

export default {
  name: 'dsItemConfig',
  data: () => ({
    showVisible: false,
    state: false, // true: 新增 false: 修改
    loading: false,
    actionData: {
      form: {},
      rules: {
        dsItemCode: [{ required: true, message: '请输入项目编码', trigger: 'change' }],
        dsItemName: [{ required: true, message: '请输入项目名称', trigger: 'change' }],
        dataSource: [{ required: true, message: '请选择数据来源', trigger: 'change' }],
        queryField: [{ required: true, message: '请输入字段名称', trigger: 'change' }]
      }
    },
    queryForm: {
      dsItemCode: '',
      dsItemName: '',
      dataSource: '',
      desc: '',
      queryField: ''
    },
    tableData: [],
    total: 0
  }),
  methods: {
    query () {
      this.loading = true
      queryData(this.queryForm).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
      })
    },
    // 删除
    remove (row) {
      removeDsItem(row).then(res => {
        this.$confirm('是否删除？').then(_ => {
          this.$message.success('移除成功')
          this.close()
        })
      })
    },
    // 点击新增/编辑按钮
    processData (flag, row) {
      if (flag) {
        this.actionData.form = {}
      } else {
        this.actionData.form = row
      }
      this.state = flag
      this.showVisible = true
    },
    // 新增或编辑
    handlerItem () {
      if (this.state) {
        addDsItem(this.actionData.form).then(res => {
          this.$message.success('新增成功')
          this.close()
        })
      } else {
        updateDsItem(this.actionData.form).then(res => {
          this.$message.success('更新成功')
          this.close()
        })
      }
    },
    // 关闭
    close () {
      this.showVisible = false
      this.query()
    },
    beforeCloseAction () {
      this.close()
    },
    // 输入没有内容
    changeValue (e) {
      this.$forceUpdate()
    },
    // 改变
    changeDataSource (val) {
      if (val === '1') {
        this.actionData.form.queryField = null
      }
      this.actionData.form.dataSource = val
    }
  }
}
</script>
<style scoped>
/deep/ .el-form-item__content {
  width: calc(100% - 100px) !important;
}
</style>
