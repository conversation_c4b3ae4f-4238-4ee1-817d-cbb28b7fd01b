import request from '@/utils/request'

export function queryProcessJudge (params) {
  return request({
    url: '/ProcessJudgeController/queryProcessJudge',
    method: 'post',
    params: params
  })
}
export function processTest (params) {
  return request({
    url: '/ProcessJudgeController/processTest',
    method: 'post',
    params: params
  })
}
export function updateStart (params) {
  return request({
    url: '/ProcessJudgeController/updateStart',
    method: 'post',
    params: params
  })
}
