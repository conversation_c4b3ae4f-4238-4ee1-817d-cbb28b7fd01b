<template>
  <el-table ref="elTable"
            :id="id"
            height="100%"
            stripe
            :header-cell-style = "{'text-align' : 'center'}"
            :data="data"
            v-loading="loading"
            border>
    <el-table-column label="序号" type="index" width="50" align="center" fixed></el-table-column>
    <el-table-column label="科室名称" prop="deptName" width="100" fixed></el-table-column>
    <el-table-column label="住院医师姓名" prop="drName" width="100" fixed></el-table-column>
    <el-table-column label="病案数" prop="medicalTotalNum" align="right" width="110" :fixed="include('medicalTotalNum')" sortable>
      <template slot-scope="scope">
        <div :class="scope.row.medicalTotalNum == 0 ? '' : 'skip'" @click="scope.row.medicalTotalNum == 0 ? '' : queryTotalPatient(scope.row)">
          {{ scope.row.medicalTotalNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="组数" prop="groupNum" width="110" align="right" sortable :fixed="include('groupNum')">
      <template slot-scope="scope">
        <div :class="scope.row.groupNum == 0 ? '' : 'skip'" @click="scope.row.groupNum == 0 ? '' : queryGroup(scope.row)">
          {{ scope.row.groupNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="入组病案数" prop="drgInGroupMedcasVal" width="110" align="right" sortable :fixed="include('inGroupNum')">
      <template slot-scope="scope">
        <div :class="scope.row.drgInGroupMedcasVal == 0 ? '' : 'skip'" @click="scope.row.drgInGroupMedcasVal == 0 ? '' : queryInGroupPatient(scope.row)">
          {{ scope.row.drgInGroupMedcasVal }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="未入组病案数" prop="notGroupNum" width="130" align="right" sortable :fixed="include('notGroupNum')">
      <template slot-scope="scope">
        <div :class="scope.row.notGroupNum == 0 ? '' : 'skip'" @click="scope.row.notGroupNum == 0 ? '' : queryNotGroupPatient(scope.row)">
          {{ scope.row.notGroupNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="入组率" prop="inGroupRate" width="195" :fixed="include('inGroupRate')">
      <template slot-scope="scope">
        <el-progress :text-inside="true" :stroke-width="16" v-if="!isNaN(parseInt(scope.row.inGroupRate))"  :percentage="Number(scope.row.inGroupRate)" :color="$somms.getPercentageColor(scope.row.inGroupRate)" />
      </template>
    </el-table-column>
    <el-table-column label="时间消耗指数" prop="timeIndex" width="130" align="right" sortable :fixed="include('timeIndex')"></el-table-column>
    <el-table-column label="费用消耗指数" prop="costIndex" width="130" align="right" sortable :fixed="include('costIndex')"></el-table-column>
    <el-table-column label="药占比" prop="drugRatio" width="110" align="right" sortable :fixed="include('drugRatio')">
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.drugRatio) }}
      </template>
    </el-table-column>
    <el-table-column label="耗占比" prop="consumeRatio" width="110"  align="right" sortable :fixed="include('consumeRatio')">
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.consumeRatio) }}
      </template>
    </el-table-column>
    <el-table-column label="CMI" prop="cmi" align="right" width="110" :fixed="include('medicalTotalNum')" sortable />
    <el-table-column label="悬浮"  align="center" >
      <template slot-scope="scope">
        <el-button size="mini" type="primary" @click="showSuspension(scope.row)">悬浮</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'newDipDoctorKpiTable',
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    },
    // 表格id
    id: {
      type: String
    },
    queryForm: {
      type: Object
    },
    columnOptions: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    scopeData: {}
  }),
  methods: {
    include (column) {
      return this.fixedColumns.includes(column)
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    // 显示悬浮
    showSuspension (scope) {
      this.scopeData = scope
      let scopeList = []
      for (let i in this.scopeData) {
        let o = {}
        o.key = i
        o.value = this.scopeData[i]
        scopeList.push(o)
      }
      let res = []
      for (let i = 0; i < scopeList.length; i++) {
        for (let j = 0; j < this.columnOptions.length; j++) {
          if (scopeList[i].key == this.columnOptions[j].value) {
            res.push({
              key: scopeList[i].key,
              label: this.columnOptions[j].label,
              value: scopeList[i].value,
              type: 1,
              show: true
            })
          }
        }
      }
      if (scope) {
        this.$emit('showSuspension', res)
      }
    },
    queryTotalPatient (item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: item.drCodg,
          deptCode: this.queryForm.deptCode,
          dipCodg: this.queryForm.dipCodg,
          feeStas: this.queryForm.feeStas,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryInGroupPatient (item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: item.drCodg,
          deptCode: this.queryForm.deptCode,
          dipCodg: this.queryForm.dipCodg,
          isInGroup: 1,
          feeStas: this.queryForm.feeStas,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryNotGroupPatient (item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: item.drCodg,
          deptCode: this.queryForm.deptCode,
          dipCodg: this.queryForm.dipCodg,
          isInGroup: 0,
          feeStas: this.queryForm.feeStas,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryGroup (item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/disenalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: item.drCodg,
          deptCode: this.queryForm.deptCode,
          feeStas: this.queryForm.feeStas,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    }
  }
}
</script>
