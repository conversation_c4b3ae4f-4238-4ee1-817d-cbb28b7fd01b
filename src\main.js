import Vue from 'vue'

import 'normalize.css/normalize.css'// A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/zh-CN' // lang i18n
import VCharts from 'v-charts'
import fullscreen from 'vue-fullscreen'
import '@/styles/index.scss' // global css
import { openLoading } from './utils/common'
import './utils/global'
import echarts from 'echarts'
import App from './App'
import i18n from './i18n'
import router from './router'
import store from './store'

import '@/icons' // icon
import '@/permission' // permission control(权限管理permission.js)

Vue.use(ElementUI, { locale, size: 'mini' })
Vue.use(VCharts)
Vue.use(fullscreen)
Vue.use(echarts)

Vue.prototype.openLoading = openLoading
Vue.config.productionTip = false

let vue = new Vue({
  el: '#app',
  i18n,
  store,
  router,
  template: '<App/>',
  components: { App }
})

Vue.use(vue)
