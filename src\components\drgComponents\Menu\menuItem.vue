<template>
  <div :class="['som-menu-item', data.active ? 'active' : '']" ref="menuItem" :id="'a' + data.meta.index">
    <div style="margin-right: .8rem">
      <slot name="icon" />
    </div>
    <div>
      <slot name="profttl"/>
    </div>
  </div>
</template>
<script>
export default {
  name: 'jpMenuItem',
  props: {
    data: {
      type: Object
    }
  },
  computed: {
    reduce () {
      return this.$store.state.tagsView.reduce
    }
  },
  watch: {
    $route: {
      handler: function (val) {
        if (val.path == ('/' + this.data.path) && this.reduce) {
          this.$nextTick(() => {
            document.getElementById('a' + this.data.meta.index).scrollIntoView({ block: 'center' })
          })
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@mixin active{
  color: white;
}
.som-menu-item{
  display: flex;
  align-items: center;
  color: rgba(255,255,255,.7);
  height: 50px;
  width: 100%;
  font-size: 14px;
  transform: translateX(10%);
  transition: all .2s linear;

  &:hover{
    transform: translateX(10%) scale(1.1);
    @include active;
    //transform: scale(1.05);
  }
}

.active {
  @include active;
}
</style>
