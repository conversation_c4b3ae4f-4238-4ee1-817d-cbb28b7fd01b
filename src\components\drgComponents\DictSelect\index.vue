<template>
  <div class="dict-container" :style="selectStyle">
    <el-select
      v-model="selectVal"
      :multiple="multiple"
      :placeholder="placeholder"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :style="{ width: widthAll ? '100%' : '' }"
      @change="selectChange"
      @clear="selectClear"
      :class="[useClass ? 'som-form-item' : '']"
    >
      <el-option v-for="(item,index) in selectData" :key="item.value+index" :label="item.label" :value="item.value"> </el-option>
    </el-select>
    <slot name="suffix" />
  </div>
</template>
<script>
import { queryDictionary } from '@/api/common/drgCommon'
export default {
  name: 'dictSelect',
  props: {
    // 字典类型
    dicType: {
      type: String,
      required: true
    },
    placeholder: String,
    // v-model 绑定值
    modelVal: [String, Number, Array],
    // 是否使用通用 form-item 样式
    useClass: {
      type: Boolean,
      default: false
    },
    // 是否可清除
    clearable: {
      type: Boolean,
      default: true
    },
    // 是否可以过滤
    filterable: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 类型 1：系统通用字典 2：清单字典
    type: {
      type: Number,
      default: 1
    },
    // 下拉选样式
    selectStyle: {
      type: Object,
      default: () => {}
    },
    // 多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 宽度是否100%
    widthAll: {
      type: Boolean,
      default: true
    }
  },
  data: () => ({
    selectData: [],
    selectVal: ''
  }),
  created () {
    if (this.modelVal) {
      this.selectVal = this.modelVal
    }
    this.queryDictionary()
  },
  methods: {
    // 获取码表数据
    queryDictionary: function () {
      // let params = new URLSearchParams();
      // params.append('codeKeys', this.dicType);
      // queryDictionary(params).then((response) => {
      //   if(response.data){
      //     this.selectData = response.data[this.dicType]
      //   }
      // })
      this.selectData = this.$somms.getDictValue(this.dicType, this.type)
    },
    selectClear () {
      this.$emit('clear', null)
      this.selectChange()
    },
    selectChange () {
      this.$emit('selected', this.selectVal)
      this.$emit('change', this.selectVal)
    }
  },
  model: {
    prop: 'modelVal',
    event: 'selected'
  },
  watch: {
    modelVal: function (newVal) {
      this.selectVal = newVal
    },
    selectVal: function (newVal) {
      this.$emit('selected', newVal)
    },
    dicType: {
      immediate: true,
      handler: function (dicType) {
        this.queryDictionary()
      }
    }
  }
}
</script>
<style scoped>
.dict-container {
  height: 100%;
  width: 100%;
  position: relative;
  display: flex;
}
</style>
