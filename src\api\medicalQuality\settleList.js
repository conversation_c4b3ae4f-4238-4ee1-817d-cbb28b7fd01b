import request from '@/utils/request'
export function fetchList (params) {
  return request({
    url: '/settleListManage/mainInfoList',
    method: 'post',
    params: params
  })
}

export function fetchListForDoctor (params) {
  return request({
    url: '/settleListManage/fetchListForDoctor',
    method: 'post',
    params: params
  })
}

export function deleteDataById (params) {
  return request({
    url: '/settleListManage/deleteDataById',
    method: 'post',
    data: params
  })
}

export function recoveryDataById (params) {
  return request({
    url: '/settleListManage/recoveryDataById',
    method: 'post',
    data: params
  })
}

/**
 * 预览
 * @param params
 * @returns {*}
 */
export function preview (params) {
  return request({
    url: '/settleListManage/preview',
    method: 'get',
    responseType: 'arraybuffer',
    params: params
  })
}

/**
 * 预览
 * @param params
 * @returns {*}
 */
export function getHisDate (params) {
  return request({
    url: '/hisView/get',
    method: 'post',
    responseType: 'arraybuffer',
    data: params
  })
}

export function getHisFeedeTail (params) {
  return request({
    url: '/hisView/getHisFeedeTail',
    method: 'post',
    responseType: 'arraybuffer',
    data: params
  })
}

/**
 * 郧阳预览在院病人
 * @param params
 * @returns {*}
 */
export function get2_2_3 (params) {
  return request({
    url: '/zhongyangApi/get2_2_3',
    method: 'post',
    data: params
  })
}

/**
 * 郧阳查看预分组
 * @param params
 * @returns {*}
 */
export function preGroupSettle (params) {
  return request({
    url: '/zhongyangApi/preGroupSettle',
    method: 'post',
    data: params
  })
}

export function querySpecialDisease (params) {
  return request({
    url: '/settleListManage/querySpecialDisease',
    method: 'post',
    params: params
  })
}

export function getDiag(params) {
  return request({
    url: '/settleListManage/getDiag',
    method: 'get',
    params: params
  })
}

export function getDiagName(params) {
  return request({
    url: '/settleListManage/getDiagName',
    method: 'get',
    params: params
  })
}
export function getOprns(params) {
  return request({
    url: '/settleListManage/getOprns',
    method: 'get',
    params: params
  })
}

export function getOprnsName(params) {
  return request({
    url: '/settleListManage/getOprnsName',
    method: 'get',
    params: params
  })
}

export function getPreGroupResult(params) {
  return request({
    url: '/settleListManage/getPreGroupResult',
    method: 'post',
    data: params
  })
}

export function getHosId() {
  return request({
    url: '/settleListManage/getHosInfo',
    method: 'get'
  })
}
