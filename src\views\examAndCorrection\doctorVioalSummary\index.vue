<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
              show-date-range
              :show-in-date-range="false"
              :show-se-date-range="false"
              :show-hos-dept="{ show: this.$somms.hasHosRole()}"
              showPagination
              :totalNum="total"
              headerTitle="查询条件"
              contentTitle="违规医生汇总"
              :container="true"
              :exportExcel="{ 'tableId': tableId, exportName: '医生违规汇总'}"
              :exportExcelFun="queryPageData"
              :exportExcelHasChild="false"
              :initTimeValueNotQuery="false"
              ref="somForm"
              @query="handleSearchList">

      <template slot="extendFormItems">
        <el-form-item label="场景类型" prop="ruleScenType">
          <drg-dict-select dicType="RULE_SCEN_TYPE" placeholder="请选择场景类型" v-model="listQuery.ruleScenType"
                           @change="getDataIsuue"/>
        </el-form-item>
        <el-form-item label="医生名称">
          <el-input v-model="listQuery.doctorName" placeholder="请输入医生名称"/>
        </el-form-item>
        <el-form-item label="筛选违规" prop="showVioal">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择是否只展示违规数据" v-model="listQuery.showVioal"
                           @change="getDataIsuue"/>
        </el-form-item>
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  :id="tableId"
                  size="mini"
                  stripe
                  height="100%"
                  :data="list"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>
          <el-table-column label="序号" type="index" align="right">
          </el-table-column>
          <el-table-column label="月份" prop="mouth" align="center"/>
          <el-table-column label="医生名称" prop="doctorName" align="center"/>
          <el-table-column label="违规规则数" prop="violationRuleNum" align="center">
            <template slot-scope="scope">
              <span @click="showRuleDetail(scope.row)"
                    style="color: red; text-decoration: underline;  cursor: pointer;">
                {{ scope.row.violationRuleNum }}
                </span>
            </template>
          </el-table-column>
          <el-table-column label="违规明细数" prop="violationTupleNum" align="center">
            <template slot-scope="scope">
              <span @click="showTupleDetail(scope.row)"
                    style="color: red; text-decoration: underline; cursor: pointer;">
                {{ scope.row.violationTupleNum }}
                </span>
            </template>
          </el-table-column>
          <el-table-column label="违规总金额" prop="totalAmount" align="center"/>
          <el-table-column label="违规病例数" prop="casesNum" align="center" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <div v-if="Number(scope.row.totalAmount)>0" class='skip' @click="queryTotalMedicalNum(scope.row,'cases')">
                {{ scope.row.casesNum | formatIsEmpty }}
              </div>
              <div v-if="Number(scope.row.totalAmount)==0" style="color:#000000">
                {{ scope.row.casesNum | formatIsEmpty }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- 规则弹窗 -->
        <el-dialog
          :visible.sync="ruleDialogVisible"
          title="违规规则详细信息"
          width="70%"
        >
          <el-table :data="violationRuleList"
                    style="width: 100%"
                    height="400"
                    v-loading="ruleDialogLoading">
            <!-- 序号列 -->
            <el-table-column label="序号" width="60">
              <template slot-scope="scope">
                {{ (ruleDialogQuery.pageNum - 1) * ruleDialogQuery.pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="ruleType" label="违规描述" width="100"></el-table-column>
            <el-table-column prop="ruleDetlCodg" label="违规类型" width="100"></el-table-column>
            <el-table-column prop="ruleGrpName" label="规则分组名称"></el-table-column>

            <el-table-column prop="violationTupleNum" label="违规条数" width="80">
              <template slot-scope="scope">
              <span @click="showTupleDetail(scope.row)"
                    style="color: red; text-decoration: underline;cursor: pointer;">
                {{ scope.row.violationTupleNum }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="totalAmount" label="违规金额" width="80"></el-table-column>
          </el-table>

          <!-- 规则弹窗分页 -->
          <div class="dialog-pagination-right">
            <el-pagination
              background
              @size-change="handleRuleDialogSizeChange"
              @current-change="handleRuleDialogCurrentChange"
              layout="total, sizes, prev, pager, next, jumper"
              :page-size="ruleDialogQuery.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :current-page.sync="ruleDialogQuery.pageNum"
              :total="ruleDialogTotal">
            </el-pagination>
          </div>
        </el-dialog>
        <!-- 元素弹窗 -->
        <el-dialog
          :visible.sync="tupleDialogVisible"
          title="违规详细信息"
          width="70%"
        >
          <el-table :data="violationTupleList"
                    style="width: 100%"
                    height="400"
                    v-loading="tupleDialogLoading">
            <!-- 序号列 -->
            <el-table-column label="序号" width="60">
              <template slot-scope="scope">
                {{ (tupleDialogQuery.pageNum - 1) * tupleDialogQuery.pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="medcasno" label="病案号" width="80"></el-table-column>
            <el-table-column prop="ruleType" label="违规描述" width="100"></el-table-column>
            <el-table-column prop="ruleDetlCodg" label="违规编码" width="80"></el-table-column>

            <el-table-column prop="dataCode" label="违规明细编码"></el-table-column>
            <el-table-column prop="dataName" label="违规明细名称"></el-table-column>
            <el-table-column prop="totalAmount" label="违规金额" width="80"></el-table-column>
          </el-table>

          <!-- 元素弹窗分页 -->
          <div class="dialog-pagination-right">
            <el-pagination
              background
              @size-change="handleTupleDialogSizeChange"
              @current-change="handleTupleDialogCurrentChange"
              layout="total, sizes, prev, pager, next, jumper"
              :page-size="tupleDialogQuery.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :current-page.sync="tupleDialogQuery.pageNum"
              :total="tupleDialogTotal">
            </el-pagination>
          </div>
        </el-dialog>
      </template>

    </drg-form>
  </div>
</template>
<script>
import {
  querySelectTreeAndSelectList,
  queryLikeDipGroupByPram,
  queryLikeDrgsByPram,
  queryDataIsuue
} from '@/api/common/drgCommon'
import {deleteDataById, getHisDate} from '@/api/medicalQuality/settleList'
import {
  getResultListByDoctor as queryPageData,
  fetchViolationTupleDetails,
  getViolationsSummaryByMouth as fetchViolationRuleDetails
} from '@/api/examCorrection/ruleAndTuples'
import {formatDate} from '@/utils/date'
import {elExportExcel} from '@/utils/exportExcel'
import {medicalDeleteDataUpload} from '@/api/medicalQuality/medicalDeleteDataUpload'


const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  ruleScenType: '1',
  doctorName: null,
  doctorCode: null,
  showVioal: null,
  begnDate: '',
  expiDate: '',
  seStartTime: '',
  seEndTime: ''
}
export default {
  name: 'doctorVioalSummary',
  components: {},
  inject: ['reload'],
  data() {
    return {
      //违规详情弹窗标识符
      tupleDialogVisible: false,
      ruleDialogVisible: false,
      //点击违规规则存储当前行数据
      selectedRowData: {},
      //点击违规规则查询到的违规元素
      violationTupleList: [],
      //点击违规规则查询到的违规元素
      violationRuleList: [],
      //点击违规规则元素查的传参
      selectTupleParam: Object.assign({}, defaultListQuery),
      //点击违规规则元素查的传参
      selectRuleParam: Object.assign({}, defaultListQuery),
      // 规则弹窗分页参数
      ruleDialogQuery: {
        pageNum: 1,
        pageSize: 20
      },
      ruleDialogTotal: 0,
      ruleDialogLoading: false,
      // 元素弹窗分页参数
      tupleDialogQuery: {
        pageNum: 1,
        pageSize: 20
      },
      tupleDialogTotal: 0,
      tupleDialogLoading: false,
      showFlag: false,
      dialogVisible: false,
      tableId: 'dataTable',
      listLoading: true,
      list: [],
      total: 0,
      dictVoList: {},
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      depts: [],
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created() {

  },
  watch: {
    list: function () {
      this.$nextTick(() => {
        if (this.$refs.settleListTable) {
          this.$refs.settleListTable.doLayout()
        }
      })
    }
  },
  filters: {
    formatIsEmpty(value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime(time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },

  },
  mounted() {
    this.$nextTick(() => {
      this.findSelectTreeAndSelectList()
      if (Object.keys(this.$route.query).length > 0) {
        // 时间需要在最下面设置
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.listQuery)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.listQuery)
        }
      }
      this.getList()
    })
  },
  methods: {
    // 点击表格列时调用的事件
    showRuleDetail(rowData) {
      this.selectedRowData = rowData;
      this.ruleDialogVisible = true;
      // 重置分页参数
      this.ruleDialogQuery.pageNum = 1;
      this.ruleDialogQuery.pageSize = 20;
      this.fetchViolationRuleDetails(); // 打开弹窗
    },
    fetchViolationRuleDetails() {
      this.ruleDialogLoading = true;
      this.getParamByBaseQuery(this.selectRuleParam, this.listQuery)
      this.selectRuleParam.ruleMouth = this.selectedRowData.mouth
      this.selectRuleParam.ruleDetlCodg = this.selectedRowData.ruleDetlCodg
      this.selectRuleParam.doctorCode = this.selectedRowData.doctorCode
      this.selectRuleParam.doctorName = this.selectedRowData.doctorName
      // 添加分页参数
      this.selectRuleParam.pageNum = this.ruleDialogQuery.pageNum
      this.selectRuleParam.pageSize = this.ruleDialogQuery.pageSize
      fetchViolationRuleDetails(this.selectRuleParam).then(response => {
        this.ruleDialogLoading = false;
        // 请求成功后，更新违规详细信息
        if (response.data) {
          if (Array.isArray(response.data)) {
            this.violationRuleList = response.data;
            this.ruleDialogTotal = response.data.length; // 如果后端没有返回total，使用数组长度
          } else if (response.data.list) {
            this.violationRuleList = response.data.list;
            this.ruleDialogTotal = response.data.total || response.data.list.length;
          }
        }
      }).catch(() => {
        this.ruleDialogLoading = false;
      })
    },
    // 点击表格列时调用的事件
    showTupleDetail(rowData) {
      this.selectedRowData = rowData
      this.tupleDialogVisible = true
      // 重置分页参数
      this.tupleDialogQuery.pageNum = 1;
      this.tupleDialogQuery.pageSize = 20;
      this.fetchViolationTupleDetails() // 打开弹窗
    },
    fetchViolationTupleDetails() {
      this.tupleDialogLoading = true;
      this.getParamByBaseQuery(this.selectTupleParam, this.listQuery)
      this.selectTupleParam.ruleMouth = this.selectedRowData.mouth
      this.selectTupleParam.ruleDetlCodg = this.selectedRowData.ruleDetlCodg
      this.selectTupleParam.doctorCode = this.selectedRowData.doctorCode
      this.selectRuleParam.doctorName = this.selectedRowData.doctorName
      // 添加分页参数
      this.selectTupleParam.pageNum = this.tupleDialogQuery.pageNum
      this.selectTupleParam.pageSize = this.tupleDialogQuery.pageSize
      fetchViolationTupleDetails(this.selectTupleParam).then(response => {
        this.tupleDialogLoading = false;
        // 请求成功后，更新违规详细信息
        if (response.data) {
          if (Array.isArray(response.data)) {
            this.violationTupleList = response.data;
            this.tupleDialogTotal = response.data.length; // 如果后端没有返回total，使用数组长度
          } else if (response.data.list) {
            this.violationTupleList = response.data.list;
            this.tupleDialogTotal = response.data.total || response.data.list.length;
          }
        }
      }).catch(() => {
        this.tupleDialogLoading = false;
      })
    },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'B34C')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue() {
      queryDataIsuue().then(response => {
        if (response && response.data) {
          this.cy_start_date = response.data.cy_start_date
          this.cy_end_date = response.data.cy_end_date
          this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        }
        this.getList()
      }).catch(error => {
        console.error('获取数据配置失败：', error)
        this.getList() // 即使获取配置失败，也尝试获取列表数据
      })
    },
    getList() {
      this.listLoading = true
      this.getParamByBaseQuery(this.submitListQuery, this.listQuery)
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        if (response && response.data) {
          this.list = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.list = []
          this.total = 0
          console.warn('获取数据失败：响应数据格式不正确', response)
        }
      }).catch(error => {
        this.listLoading = false
        this.list = []
        this.total = 0
        console.error('获取数据失败：', error)
        this.$message.error('获取数据失败，请稍后重试')
      })
    },
    queryTotalMedicalNum(row, type) {
      let deptName = null
      if (this.deptName != null) {
        deptName = this.deptName
      }

      let transformBaseParam = Object.assign({}, defaultListQuery)
      this.getParamByBaseQuery(transformBaseParam, this.listQuery)
      transformBaseParam.priOutHosDeptName = deptName
      transformBaseParam.doctorCode = row.doctorCode
      transformBaseParam.doctorName = row.doctorName

      this.$router.push({
        path: '/examAndCorrection/docerVialDetial',
        query: transformBaseParam
      })
    },
    queryPageData,
    allExcel() {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.settleListTable.$children, document.getElementById('slTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病案数据')
    },
    handleSelect(item) {
      this.listQuery.dipCodg = item.dipCodg
    },
    dateChangeCysj(val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList() {
      this.getList()
    },
    handleResetSearch() {
      this.getDataIsuue()
    },
    refresh() {
      this.reload()
    },
    getParamByBaseQuery(targetParam, sourceParam) {
      if (targetParam) {
        const keys = Object.keys(targetParam)
        // 更新参数值
        keys.forEach(key => {
          targetParam[key] = sourceParam[key]
        })
      }
    },
    getParams() {
      return this.submitListQuery
    },
    extractHisViueData() {
      getHisDate({
        startTime: this.getParams().cy_start_date,
        endTime: this.getParams().cy_end_date,
        medcasno: this.getParams().a48
      })
    },
    // 规则弹窗分页事件
    handleRuleDialogSizeChange(val) {
      this.ruleDialogQuery.pageNum = 1
      this.ruleDialogQuery.pageSize = val
      this.fetchViolationRuleDetails()
    },
    handleRuleDialogCurrentChange(val) {
      this.ruleDialogQuery.pageNum = val
      this.fetchViolationRuleDetails()
    },
    // 元素弹窗分页事件
    handleTupleDialogSizeChange(val) {
      this.tupleDialogQuery.pageNum = 1
      this.tupleDialogQuery.pageSize = val
      this.fetchViolationTupleDetails()
    },
    handleTupleDialogCurrentChange(val) {
      this.tupleDialogQuery.pageNum = val
      this.fetchViolationTupleDetails()
    }
  }
}
</script>
<style scoped>
/*时间样式设置*/
::v-deep .el-date-editor--daterange.el-input,
::v-deep .el-date-editor--daterange.el-input__inner,
::v-deep .el-date-editor--timerange.el-input,
::v-deep .el-date-editor--timerange.el-input__inner {
  width: 200px;
}

.autoSelectInputWidth {
  width: 178px;
}

/* 弹窗分页样式 */
.dialog-pagination-right {
  margin-top: 20px;
  text-align: right;
}

.dialog-pagination-right .el-pagination {
  display: inline-block;
}

</style>
