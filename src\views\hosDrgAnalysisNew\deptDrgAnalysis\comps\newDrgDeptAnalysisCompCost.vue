<template>
  <div class="cost-wrapper">
    <div class="cost-wrapper-item"
         v-for="(item, index) in items"
         :key="index">
      <div class="cost-wrapper-item-title">
        {{ item.name }}
      </div>
      <div class="cost-wrapper-item-value" v-for="(dataItem, index) in item.data" style="cursor:default" :key="index">
        <span v-if="item.contrast" class="cost-wrapper-item-value-earn" :class="[item.contrast ? 'line-feed' : '']" @click="dropPatientC(dataItem)">
          <span >{{ dataItem.name_c }}：</span>
          <span :style="{color:colorChangec(dataItem)}">{{ dataItem.value_c }}</span>
        </span>
        <span :class="[item.contrast ? 'line-feed' : '']" @click="dropPatient(dataItem)">
          <span>{{ dataItem.name }}：</span>
          <span :style="{color:colorChange(dataItem)}">{{ dataItem.value }}</span>
        </span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name:'newDrgDeptAnalysisCompCost',
  props: {
    // 数据
    items: {
      type: Array,
      default: () => []
    },
    queryForm: {
      type: Object
    }
  },
  methods: {
    dropPatient (item) {
      if (item.value && item.value > 0) {
        this.$router.push({
          path: '/hosDrgAnalysisNew/pattAnalysis',
          query: {
            begnDate: this.queryForm.begnDate,
            expiDate: this.queryForm.expiDate,
            feeStas: this.queryForm.feeStas,
            deptCode: item.deptCode,
            patientIds: item.patientId,
            inStartTime: this.queryForm.inStartTime,
            inEndTime: this.queryForm.inEndTime,
            inHosFlag: this.queryForm.inHosFlag,
            seStartTime: this.queryForm.seStartTime,
            seEndTime: this.queryForm.seEndTime
          }
        })
      }
    },
    dropPatientC (item) {
      if (item.value_c && item.value_c > 0) {
        this.$router.push({
          path: '/hosDrgAnalysisNew/pattAnalysis',
          query: {
            begnDate: this.queryForm.begnDate,
            expiDate: this.queryForm.expiDate,
            deptCode: item.deptCode,
            patientIds: item.patientId_c,
            feeStas: this.queryForm.feeStas,
            inStartTime: this.queryForm.inStartTime,
            inEndTime: this.queryForm.inEndTime,
            seStartTime: this.queryForm.seStartTime,
            seEndTime: this.queryForm.seEndTime,
            inHosFlag: this.queryForm.inHosFlag
          }
        })
      }
    },
    colorChange (dataItem) {
      if (dataItem.name == '病案数') {
        if (dataItem.value_c > 0 || dataItem.value > 0) {
          return '#409EFF'
        }
      }
    },
    colorChangec (dataItem) {
      if (dataItem.name_c == '盈利病案数') {
        if (dataItem.value_c > 0) {
          return '#409EFF'
        }
      }
    }
  }

}
</script>

<style scoped lang="scss">
$titleGray: gray;
$titleSize: 13px;
.cost-wrapper{
  height: 100%;
  width: 100%;

  &-item{
    width: 100%;
    height: 27%;
    padding-top: 2%;

    &-title{
      font-size: $titleSize;
      font-weight: 600;
    }

    &-value{
      display: flex;
      font-size: 12px;
      text-align: left;
      color: $titleGray;

      &-earn{
        width: 50%;
      }
    }
  }
}
.line-feed{
  display: flex;
  flex-direction: column;
}
</style>
