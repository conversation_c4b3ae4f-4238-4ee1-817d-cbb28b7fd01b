<template>
  <el-table ref="elTable"
            :id="id"
            height="100%"
            stripe
            :header-cell-style="{'text-align':'center'}"
            :data="data"
            :summary-method="getSummaries"
            show-summary
            v-loading="loading"
            border>
    <el-table-column label="序号" type="index" width="50" fixed="left" align="center"/>
    <el-table-column label="科室名称" prop="deptName" :fixed="include('deptName')" width="150"/>
    <el-table-column label="病案数" prop="medicalTotalNum" :fixed="include('medicalTotalNum')" width="110" align="right"
                     sortable>
      <template slot-scope="scope">
        <div :class="scope.row.medicalTotalNum == 0 ? '' : 'skip'"
             @click="scope.row.medicalTotalNum == 0 ? '' : queryTotalPatient(scope.row)">
          {{ scope.row.medicalTotalNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="组数" prop="groupNum" :fixed="include('groupNum')" width="110" align="right" sortable>
      <template slot-scope="scope">
        <div :class="scope.row.groupNum == 0 ? '' : 'skip'"
             @click="scope.row.groupNum == 0 ? '' : queryGroup(scope.row)">
          {{ scope.row.groupNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="入组病案数" prop="drgInGroupMedcasVal" :fixed="include('inGroupNum')" width="110"
                     align="right" sortable>
      <template slot-scope="scope">
        <div :class="scope.row.drgInGroupMedcasVal == 0 ? '' : 'skip'"
             @click="scope.row.drgInGroupMedcasVal == 0 ? '' : queryInGroupPatient(scope.row)">
          {{ scope.row.drgInGroupMedcasVal }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="未入组病案数" prop="nonGroupNum" :fixed="include('nonGroupNum')" width="130" align="right"
                     sortable>
      <template slot-scope="scope">
        <div :class="scope.row.nonGroupNum == 0 ? '' : 'skip'"
             @click="scope.row.nonGroupNum == 0 ? '' : queryNotGroupPatient(scope.row)">
          {{ scope.row.nonGroupNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="入组率" prop="inGroupRate" :fixed="include('inGroupRate')" width="194">
      <template slot-scope="scope">
        <el-progress :text-inside="true" :stroke-width="16" v-if="!isNaN(parseInt(scope.row.inGroupRate))"
                     :percentage="Number(scope.row.inGroupRate)"
                     :color="$somms.getPercentageColor(scope.row.inGroupRate)"/>
      </template>
    </el-table-column>
    <el-table-column label="平均住院日" prop="avgInHosDays" :fixed="include('avgInHosDays')" width="130" align="right"
                     sortable/>
    <el-table-column label="平均住院费用" prop="avgFee" :fixed="include('avgTotalCost')" width="130" align="right"
                     sortable/>
    <el-table-column label="药占比" prop="medicalCostRate" :fixed="include('medicalCostRate')" width="130" align="right"
                     sortable>
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.medicalCostRate) }}
      </template>
    </el-table-column>
    <el-table-column label="耗占比" prop="materialCostRate" :fixed="include('materialCostRate')" width="130"
                     align="right" sortable>
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.materialCostRate) }}
      </template>
    </el-table-column>
    <el-table-column label="总权重" prop="totalWeight" :fixed="include('totalWeight')" width="130" align="right"
                     sortable/>
    <el-table-column label="CMI" prop="cmi" :fixed="include('cmi')" width="130" align="right" sortable/>
    <el-table-column label="时间消耗指数" prop="timeIndex" :fixed="include('timeIndex')" width="130" align="right"
                     sortable/>
    <el-table-column label="费用消耗指数" prop="costIndex" :fixed="include('costIndex')" width="130" align="right"
                     sortable/>
    <el-table-column label="悬浮" align="center">
      <template slot-scope="scope">
        <el-button size="mini" type="primary" @click="showSuspension(scope.row)">悬浮</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>
<script>
export default {
  props: {
    // 数据
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    },
    // 表格id
    id: {
      type: String
    },
    queryForm: {
      type: Object
    },
    columnOptions: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    scopeData: {}
  }),
  methods: {
    include(column) {
      return this.fixedColumns.includes(column)
    },
    // 导出
    setTableObj() {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    // 显示悬浮
    showSuspension(scope) {
      this.scopeData = scope
      let scopeList = []
      for (let i in this.scopeData) {
        let o = {}
        o.key = i
        o.value = this.scopeData[i]
        scopeList.push(o)
      }
      let res = []
      for (let i = 0; i < scopeList.length; i++) {
        for (let j = 0; j < this.columnOptions.length; j++) {
          if (scopeList[i].key == this.columnOptions[j].value) {
            res.push({
              key: scopeList[i].key,
              label: this.columnOptions[j].label,
              value: scopeList[i].value,
              type: 1,
              show: true
            })
          }
        }
      }
      if (scope) {
        this.$emit('showSuspension', res)
      }
    },
    queryTotalPatient(item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          categories:this.queryForm.categories,
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          deptCode: item.deptCode,
          feeStas: this.queryForm.feeStas,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryGroup(item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/disenalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          deptCode: item.deptCode,
          feeStas: this.queryForm.feeStas,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryInGroupPatient(item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          deptCode: item.deptCode,
          isInGroup: 1,
          feeStas: this.queryForm.feeStas,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryNotGroupPatient(item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          deptCode: item.deptCode,
          isInGroup: 0,
          feeStas: this.queryForm.feeStas,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    getSummaries(param) {
      const {columns, data} = param;
      const sums = [];
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length
      };
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value));
        if (index === 2 || index === 3 || index === 4 || index === 5) {
          sums[index] = calculations.sum(values);
        } else if (index === 6 ||index === 10 || index === 11) {
          sums[index] = calculations.average(values).toFixed(2) + '%';
        } else if (index === 7 ||index === 8 || index === 9) {
          sums[index] = calculations.average(values).toFixed(2);
        } else {
          sums[index] = ' ';
        }
      });
      return sums;
    }
  }
}
</script>
