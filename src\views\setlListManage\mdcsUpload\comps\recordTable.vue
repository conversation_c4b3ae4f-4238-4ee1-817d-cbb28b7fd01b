<template>
  <el-table :data="tableData"
            v-loading="loading"
            :ref="tabName"
            :height="elTableHeight"
            :header-cell-style="{'text-align':'center'}"
            stripe
            border>
    <!-- 11.5 -->
    <el-table-column label="序号" type="index" align="center" width="50" />
    <el-table-column label="操作者账号" prop="userName" align="right"/>
    <el-table-column label="操作时间" prop="time" align="right" />
    <el-table-column label="上传成功数量" prop="succCnt" align="left">
      <template slot-scope="scope">
        <div :class="scope.row.succCnt > 0 ? 'skip' : ''"
             @click="scope.row.succCnt > 0 ? clickRow(scope.row,1) : ''">
          {{ scope.row.succCnt }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="上传失败数量" prop="faleCnt" align="left">
      <template slot-scope="scope">
        <div :class="scope.row.faleCnt > 0 ? 'skip' : ''"
             @click="scope.row.faleCnt > 0 ? clickRow(scope.row,0) : ''">
          {{ scope.row.faleCnt }}
        </div>
      </template>
    </el-table-column>
<!--    <el-table-column label="撤销原因" prop="reason" />-->
  </el-table>
</template>

<script>
export default {
  name: 'recordTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    tabName: {
      type: String,
      default: 'dataTable'
    }
  },
  data () {
    return {
      tableData: [],
      tempData: [],
      elTableHeight: '100%'
    }
  },
  // updated() {
  //   this.$nextTick(() => {
  //     this.$refs['elTable'].doLayout()
  //   })
  // },
  methods: {
    judgeType (row) {
      switch (row.type) {
        case '1':
          return '上传'
        case '2':
          return '撤销'
      }
    },
    clickRow (row, number) {
      row.upldStas = number
      this.tempData = this.tableData
      this.tableData = []
      this.tableData.push(row)
      this.elTableHeight = '20%'
      this.$nextTick(() => {
        this.$refs['elTable'].doLayout()
      })
      this.$emit('showBack', true)
      this.$emit('recordData', row)
    },
    // dblclickRow(row) {
    //   this.tempData = this.tableData
    //   this.tableData = []
    //   this.tableData.push(row)
    //   this.elTableHeight = '20%'
    //   this.$nextTick(() => {
    //     this.$refs['elTable'].doLayout()
    //   })
    //   this.$emit('showBack',true)
    //   this.$emit('recordData',row)
    // },
    returnRow () {
      this.tableData = []
      this.tableData = this.tempData
      this.elTableHeight = '100%'
      this.$nextTick(() => {
        this.$refs['elTable'].doLayout()
      })
    }
  },
  watch: {
    data: {
      handler: function (item) {
        this.tableData = item
      }
    }

  }
}
</script>

<style scoped>

</style>
