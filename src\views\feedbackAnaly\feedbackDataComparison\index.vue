<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="form"
             :show-date-range="{ show: true, clearable: true }"
             :show-se-date-range="{ show: true, clearable: true }"
             :container="true"
             headerTitle="查询条件"
             contentTitle="数据列表"
             @query="queryData"
             :init-time-value-not-query="false">

      <!-- 扩展搜索表单 -->
      <template slot="extendFormItems">
        <el-form-item label="出院科室" prop="deptName" v-if="queryForm.tabsCode === 'dept'">
          <el-select
            v-model="queryForm.deptName" filterable placeholder="请选择" clearable>
            <el-option v-for="item in deptValueList" :key="item.value"
                       :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="医生姓名" prop="drName" v-if="queryForm.tabsCode === 'doctor'">
          <el-select
            v-model="queryForm.drName" filterable placeholder="请选择" clearable>
            <el-option v-for="item in doctorValueList" :key="item.value"
                       :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="最低倍率" >
          <el-input-number v-model="queryForm.num1" :precision="2" :step="0.01" :max="1"></el-input-number>
        </el-form-item>

        <el-form-item label="最高倍率" >
          <el-input-number v-model="queryForm.num2" :precision="2" :step="0.01" :max="1"></el-input-number>
        </el-form-item>
      </template>

      <template slot="buttonsSuffix">
        <el-tooltip class="position" effect="dark" content="倍率区间为0-1,低为下限值,高为上限值" placement="top">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </template>

      <template slot="containerContent">
        <el-tabs v-model="queryForm.tabsCode" style="height: 100%">
          <el-tab-pane label="科室" name="dept">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-tag class="centered-text">所选择时间数据</el-tag>
                <el-empty v-show="list.length == 0" description="暂无数据"></el-empty>
                <el-card v-for="(item,index) in list" :key="index" style="margin-bottom: 1%;padding-bottom: 1%">
                  <el-descriptions :title="item.deptName" direction="vertical" :column="5" border>
                    <el-descriptions-item label="病案数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('totalCases')">{{item.totalCases}}</el-descriptions-item>
                    <el-descriptions-item label="组数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('groupNum')">{{item.groupNum}}</el-descriptions-item>
                    <el-descriptions-item label="入住病案数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('inGroup')">{{item.isInGroup}}</el-descriptions-item>
                    <el-descriptions-item label="未入住病案数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('noInGroup')">{{item.noInGroup}}</el-descriptions-item>
                    <el-descriptions-item label="超高病案" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('highCases')">{{item.highCases}}</el-descriptions-item>
                    <el-descriptions-item label="超低病案" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('lowCases')">{{item.lowCases}}</el-descriptions-item>
                    <el-descriptions-item label="正常病案" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('normalCases')">{{item.normalCases}}</el-descriptions-item>
                    <el-descriptions-item label="特殊病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('specialCases')">{{item.specialCases}}</el-descriptions-item>
                    <el-descriptions-item label="非稳定病种" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('unstableDisease')">{{item.unstableDisease}}</el-descriptions-item>
                    <el-descriptions-item label="康复病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('recoverCases')">{{item.recoverCases}}</el-descriptions-item>
                    <el-descriptions-item label="特病单议-扣费病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('specialDeductionCases')">{{item.specialDeductionCases}}</el-descriptions-item>
                    <el-descriptions-item label="入组率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('inGroupRate')">{{item.inGroupRate}}</el-descriptions-item>
                    <el-descriptions-item label="住院过程不完整病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('incomHospitalCases')">{{item.incomHospitalCases}}</el-descriptions-item>
                    <el-descriptions-item label="平均住院日" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('aveHospitalDays')">{{item.aveHospitalDays}}</el-descriptions-item>
                    <el-descriptions-item label="平均住院费用" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('aveHospitalCost')">{{item.aveHospitalCost}}</el-descriptions-item>
                    <el-descriptions-item label="药占比" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('drugProportion')">{{item.drugProportion}}</el-descriptions-item>
                    <el-descriptions-item label="耗占比" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('consumRatio')">{{item.consumRatio}}</el-descriptions-item>
                    <el-descriptions-item label="总权重" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('totalDipWeight')">{{item.totalDipWeight}}</el-descriptions-item>
                    <el-descriptions-item label="cmi" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('cmi')">{{item.cmi}}</el-descriptions-item>
                    <el-descriptions-item label="时间消耗指数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('timeIndex')">{{item.timeIndex}}</el-descriptions-item>
                    <el-descriptions-item label="费用消耗指数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('costIndex')">{{item.costIndex}}</el-descriptions-item>
                    <el-descriptions-item label="超高率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('highRate')">{{item.highRate}}</el-descriptions-item>
                    <el-descriptions-item label="超低率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('lowRate')">{{item.lowRate}}</el-descriptions-item>
                    <el-descriptions-item label="正常率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('normalRate')">{{item.normalRate}}</el-descriptions-item>
                    <el-descriptions-item label="特殊病例率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('specialRate')">{{item.specialRate}}</el-descriptions-item>
                    <el-descriptions-item label="非稳定病种率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('unstableRate')">{{item.unstableRate}}</el-descriptions-item>
                    <el-descriptions-item label="康复病例率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('recoverRate')">{{item.recoverRate}}</el-descriptions-item>
                    <el-descriptions-item label="特病单议-扣费病例率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('specialDeductionRate')">{{item.specialDeductionRate}}</el-descriptions-item>
                    <el-descriptions-item label="住院过程不完整率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('incomHospitalRate')">{{item.incomHospitalRate}}</el-descriptions-item>
                    <el-descriptions-item label="总费用" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('totalCost')">{{item.sumfee}}</el-descriptions-item>
                    <el-descriptions-item label="反馈金额" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('feedbackAmount')">{{item.feedbackAmount}}</el-descriptions-item>
                    <el-descriptions-item label="金额差异" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('amountDifference')">{{item.amountDifference}}</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-tag class="centered-text">所选择时间前一年数据</el-tag>
                <el-empty v-show="leftList.length == 0" description="暂无数据"></el-empty>
                <el-card v-for="(item,index) in leftList" :key="index" style="margin-bottom: 1%;padding-bottom: 1%">
                  <el-descriptions :title="item.deptName" direction="vertical" :column="5" border>
                    <el-descriptions-item label="病案数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.totalCases}}</el-descriptions-item>
                    <el-descriptions-item label="组数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.groupNum}}</el-descriptions-item>
                    <el-descriptions-item label="入住病案数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.isInGroup}}</el-descriptions-item>
                    <el-descriptions-item label="未入住病案数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.noInGroup}}</el-descriptions-item>
                    <el-descriptions-item label="超高病案" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.highCases}}</el-descriptions-item>
                    <el-descriptions-item label="超低病案" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.lowCases}}</el-descriptions-item>
                    <el-descriptions-item label="正常病案" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.normalCases}}</el-descriptions-item>
                    <el-descriptions-item label="特殊病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.specialCases}}</el-descriptions-item>
                    <el-descriptions-item label="非稳定病种" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.unstableDisease}}</el-descriptions-item>
                    <el-descriptions-item label="康复病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.recoverCases}}</el-descriptions-item>
                    <el-descriptions-item label="特病单议-扣费病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.specialDeductionCases}}</el-descriptions-item>
                    <el-descriptions-item label="入组率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.inGroupRate}}</el-descriptions-item>
                    <el-descriptions-item label="住院过程不完整病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.incomHospitalCases}}</el-descriptions-item>
                    <el-descriptions-item label="平均住院日" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.aveHospitalDays}}</el-descriptions-item>
                    <el-descriptions-item label="平均住院费用" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.aveHospitalCost}}</el-descriptions-item>
                    <el-descriptions-item label="药占比" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.drugProportion}}</el-descriptions-item>
                    <el-descriptions-item label="耗占比" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.consumRatio}}</el-descriptions-item>
                    <el-descriptions-item label="总权重" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.totalDipWeight}}</el-descriptions-item>
                    <el-descriptions-item label="cmi" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.cmi}}</el-descriptions-item>
                    <el-descriptions-item label="时间消耗指数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.timeIndex}}</el-descriptions-item>
                    <el-descriptions-item label="费用消耗指数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.costIndex}}</el-descriptions-item>
                    <el-descriptions-item label="超高率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.highRate}}</el-descriptions-item>
                    <el-descriptions-item label="超低率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.lowRate}}</el-descriptions-item>
                    <el-descriptions-item label="正常率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.normalRate}}</el-descriptions-item>
                    <el-descriptions-item label="特殊病例率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.specialRate}}</el-descriptions-item>
                    <el-descriptions-item label="非稳定病种率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.unstableRate}}</el-descriptions-item>
                    <el-descriptions-item label="康复病例率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.recoverRate}}</el-descriptions-item>
                    <el-descriptions-item label="特病单议-扣费病例率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.specialDeductionRate}}</el-descriptions-item>
                    <el-descriptions-item label="住院过程不完整率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.incomHospitalRate}}</el-descriptions-item>
                    <el-descriptions-item label="总费用" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.sumfee}}</el-descriptions-item>
                    <el-descriptions-item label="反馈金额" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.feedbackAmount}}</el-descriptions-item>
                    <el-descriptions-item label="金额差异" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" >{{item.amountDifference}}</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="医生" name="doctor">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-tag class="centered-text">所选择时间数据</el-tag>
                <el-empty v-show="doctorList.length == 0" description="暂无数据"></el-empty>
                <el-card v-for="(item,index) in doctorList" :key="index" style="margin-bottom: 1%;padding-bottom: 1%">
                  <el-descriptions :title="item.drName" direction="vertical" :column="5" border>
                    <el-descriptions-item label="病案数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('totalCases')">{{item.totalCases}}</el-descriptions-item>
                    <el-descriptions-item label="组数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('groupNum')">{{item.groupNum}}</el-descriptions-item>
                    <el-descriptions-item label="入住病案数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('inGroup')">{{item.isInGroup}}</el-descriptions-item>
                    <el-descriptions-item label="未入住病案数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('noInGroup')">{{item.noInGroup}}</el-descriptions-item>
                    <el-descriptions-item label="超高病案" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('highCases')">{{item.highCases}}</el-descriptions-item>
                    <el-descriptions-item label="超低病案" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('lowCases')">{{item.lowCases}}</el-descriptions-item>
                    <el-descriptions-item label="正常病案" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('normalCases')">{{item.normalCases}}</el-descriptions-item>
                    <el-descriptions-item label="特殊病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('specialCases')">{{item.specialCases}}</el-descriptions-item>
                    <el-descriptions-item label="非稳定病种" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('unstableDisease')">{{item.unstableDisease}}</el-descriptions-item>
                    <el-descriptions-item label="康复病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('recoverCases')">{{item.recoverCases}}</el-descriptions-item>
                    <el-descriptions-item label="特病单议-扣费病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('specialDeductionCases')">{{item.specialDeductionCases}}</el-descriptions-item>
                    <el-descriptions-item label="入组率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('inGroupRate')">{{item.inGroupRate}}</el-descriptions-item>
                    <el-descriptions-item label="住院过程不完整病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('incomHospitalCases')">{{item.incomHospitalCases}}</el-descriptions-item>
                    <el-descriptions-item label="平均住院日" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('aveHospitalDays')">{{item.aveHospitalDays}}</el-descriptions-item>
                    <el-descriptions-item label="平均住院费用" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('aveHospitalCost')">{{item.aveHospitalCost}}</el-descriptions-item>
                    <el-descriptions-item label="药占比" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('drugProportion')">{{item.drugProportion}}</el-descriptions-item>
                    <el-descriptions-item label="耗占比" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('consumRatio')">{{item.consumRatio}}</el-descriptions-item>
                    <el-descriptions-item label="总权重" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('totalDipWeight')">{{item.totalDipWeight}}</el-descriptions-item>
                    <el-descriptions-item label="cmi" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('cmi')">{{item.cmi}}</el-descriptions-item>
                    <el-descriptions-item label="时间消耗指数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('timeIndex')">{{item.timeIndex}}</el-descriptions-item>
                    <el-descriptions-item label="费用消耗指数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('costIndex')">{{item.costIndex}}</el-descriptions-item>
                    <el-descriptions-item label="超高率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('highRate')">{{item.highRate}}</el-descriptions-item>
                    <el-descriptions-item label="超低率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('lowRate')">{{item.lowRate}}</el-descriptions-item>
                    <el-descriptions-item label="正常率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('normalRate')">{{item.normalRate}}</el-descriptions-item>
                    <el-descriptions-item label="特殊病例率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('specialRate')">{{item.specialRate}}</el-descriptions-item>
                    <el-descriptions-item label="非稳定病种率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('unstableRate')">{{item.unstableRate}}</el-descriptions-item>
                    <el-descriptions-item label="康复病例率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('recoverRate')">{{item.recoverRate}}</el-descriptions-item>
                    <el-descriptions-item label="特病单议-扣费病例率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('specialDeductionRate')">{{item.specialDeductionRate}}</el-descriptions-item>
                    <el-descriptions-item label="住院过程不完整率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('incomHospitalRate')">{{item.incomHospitalRate}}</el-descriptions-item>
                    <el-descriptions-item label="总费用" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('totalCost')">{{item.sumfee}}</el-descriptions-item>
                    <el-descriptions-item label="反馈金额" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('feedbackAmount')">{{item.feedbackAmount}}</el-descriptions-item>
                    <el-descriptions-item label="金额差异" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}" :content-class-name="setColor('amountDifference')">{{item.amountDifference}}</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-tag class="centered-text">所选择时间前一年数据</el-tag>
                <el-empty v-show="doctorListVal.length == 0" description="暂无数据"></el-empty>
                <el-card v-for="(item,index) in doctorListVal" :key="index" style="margin-bottom: 1%;padding-bottom: 1%">
                  <el-descriptions :title="item.drName" direction="vertical" :column="5" border>
                    <el-descriptions-item label="病案数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.totalCases}}</el-descriptions-item>
                    <el-descriptions-item label="组数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.groupNum}}</el-descriptions-item>
                    <el-descriptions-item label="入住病案数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.isInGroup}}</el-descriptions-item>
                    <el-descriptions-item label="未入住病案数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.noInGroup}}</el-descriptions-item>
                    <el-descriptions-item label="超高病案" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.highCases}}</el-descriptions-item>
                    <el-descriptions-item label="超低病案" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.lowCases}}</el-descriptions-item>
                    <el-descriptions-item label="正常病案" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.normalCases}}</el-descriptions-item>
                    <el-descriptions-item label="特殊病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.specialCases}}</el-descriptions-item>
                    <el-descriptions-item label="非稳定病种" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.unstableDisease}}</el-descriptions-item>
                    <el-descriptions-item label="康复病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.recoverCases}}</el-descriptions-item>
                    <el-descriptions-item label="特病单议-扣费病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.specialDeductionCases}}</el-descriptions-item>
                    <el-descriptions-item label="入组率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.inGroupRate}}</el-descriptions-item>
                    <el-descriptions-item label="住院过程不完整病例" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.incomHospitalCases}}</el-descriptions-item>
                    <el-descriptions-item label="平均住院日" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.aveHospitalDays}}</el-descriptions-item>
                    <el-descriptions-item label="平均住院费用" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.aveHospitalCost}}</el-descriptions-item>
                    <el-descriptions-item label="药占比" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.drugProportion}}</el-descriptions-item>
                    <el-descriptions-item label="耗占比" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.consumRatio}}</el-descriptions-item>
                    <el-descriptions-item label="总权重" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.totalDipWeight}}</el-descriptions-item>
                    <el-descriptions-item label="cmi" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.cmi}}</el-descriptions-item>
                    <el-descriptions-item label="时间消耗指数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.timeIndex}}</el-descriptions-item>
                    <el-descriptions-item label="费用消耗指数" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.costIndex}}</el-descriptions-item>
                    <el-descriptions-item label="超高率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.highRate}}</el-descriptions-item>
                    <el-descriptions-item label="超低率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.lowRate}}</el-descriptions-item>
                    <el-descriptions-item label="正常率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.normalRate}}</el-descriptions-item>
                    <el-descriptions-item label="特殊病例率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.specialRate}}</el-descriptions-item>
                    <el-descriptions-item label="非稳定病种率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.unstableRate}}</el-descriptions-item>
                    <el-descriptions-item label="康复病例率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.recoverRate}}</el-descriptions-item>
                    <el-descriptions-item label="特病单议-扣费病例率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.specialDeductionRate}}</el-descriptions-item>
                    <el-descriptions-item label="住院过程不完整率" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.incomHospitalRate}}</el-descriptions-item>
                    <el-descriptions-item label="总费用" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.sumfee}}</el-descriptions-item>
                    <el-descriptions-item label="反馈金额" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.feedbackAmount}}</el-descriptions-item>
                    <el-descriptions-item label="金额差异" :label-style="{'text-align' : 'center'}" :content-style="{'text-align' : 'center'}">{{item.amountDifference}}</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </template>

    </drg-form>
  </div>
</template>
<script>
import { queryDropdown, queryDeptDropdown, selectFeedbackDataRight, selectFeedbackDoctorRight } from '@/api/newBusiness/feebackAnalyse'

export default {
  name: 'feedbackDataComparison',
  data: () => ({
    queryForm: {
      tabsCode: 'dept',
      num1: '0.2',
      num2: '1.0'
    },
    list: {},
    leftList: {},
    doctorListVal: {},
    doctorList: {},
    doctorValueList: [],
    deptValueList: [],
    redColor: 'des-content',
    greenColor: 'des-label'
  }),
  mounted () {
    this.queryDropdown()
    this.queryDeptDropdown()
  },
  methods: {
    queryData () {
      if (this.queryForm.tabsCode === 'dept') {
        this.queryDeptData()
      }
      if (this.queryForm.tabsCode === 'doctor') {
        this.queryDoctorData()
      }
    },
    queryDeptData () {
      if (this.queryForm.deptName) {
        selectFeedbackDataRight(this.getParams()).then(res => {
          this.list = res.data['200']
          this.leftList = res.data['201']
        })
      } else {
        this.$message({ message: '请选择科室' })
      }
    },
    queryDoctorData () {
      if (this.queryForm.drName) {
        selectFeedbackDoctorRight(this.getParams()).then(res => {
          this.doctorList = res.data['200']
          this.doctorListVal = res.data['201']
        })
      } else {
        this.$message({ message: '请选择医师' })
      }
    },
    // 查询医师下拉
    queryDropdown () {
      queryDropdown(this.getParams()).then(res => {
        this.doctorValueList = res.data
      })
    },
    // 查询科室下拉
    queryDeptDropdown () {
      queryDeptDropdown(this.getParams()).then(res => {
        this.deptValueList = res.data
      })
    },
    // 获取参数
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },

    setColor (item) {
      if (this.queryForm.tabsCode === 'dept') {
        if (this.list.length !== 0) {
          for (let i = 0; i < this.list.length; i++) {
            for (let j = 0; j < this.leftList.length; j++) {
              if (this.list[i].deptName === this.leftList[j].deptName) {
                if (this.list[i][item] > this.leftList[j][item]) {
                  if (this.queryForm.num1 < Math.abs(this.list[i][item] / this.leftList[j][item] - 1) < this.queryForm.num2) {
                    return this.greenColor
                  } else if (Math.abs(this.list[i][item] / this.leftList[j][item]) > this.queryForm.num2) {
                    return this.redColor
                  }
                } else if (this.list[i][item] < this.leftList[j][item]) {
                  if (this.queryForm.num1 < Math.abs(this.list[i][item] / this.leftList[j][item] - 1) < this.queryForm.num2) {
                    return this.greenColor
                  } else if (Math.abs(this.list[i][item] / this.leftList[j][item]) > this.queryForm.num2) {
                    return this.redColor
                  }
                } else {
                  return ''
                }
              }
            }
          }
        }
      }
      if (this.queryForm.tabsCode === 'doctor') {
        if (this.doctorList.length !== 0) {
          for (let i = 0; i < this.doctorList.length; i++) {
            for (let j = 0; j < this.doctorListVal.length; j++) {
              if (this.doctorList[i].drName === this.doctorListVal[j].drName) {
                if (this.doctorList[i][item] > this.doctorListVal[j][item]) {
                  if (this.queryForm.num1 < Math.abs(this.doctorList[i][item] / this.doctorListVal[j][item] - 1) < this.queryForm.num2) {
                    return this.greenColor
                  } else if (Math.abs(this.doctorList[i][item] / this.doctorListVal[j][item]) > this.queryForm.num2) {
                    return this.redColor
                  }
                } else if (this.doctorList[i][item] < this.doctorListVal[j][item]) {
                  if (this.queryForm.num1 < Math.abs(this.doctorList[i][item] / this.doctorListVal[j][item] - 1) < this.queryForm.num2) {
                    return this.greenColor
                  } else if (Math.abs(this.doctorList[i][item] / this.doctorListVal[j][item]) > this.queryForm.num2) {
                    return this.redColor
                  }
                } else {
                  return ''
                }
              }
            }
          }
        }
      }
    }
  }
}
</script>
<style>
.des-label {
  background: #e1f3d8;
}

.des-content {
  background: #FDE2E2;
}

.position {
  margin-left: 10px;
}
.centered-text {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  color: black;
  background: white;
  border: none;
}
</style>
