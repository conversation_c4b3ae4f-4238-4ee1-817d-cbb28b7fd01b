import request from '@/utils/request'

/**
 * 查询DIP数据
 * @param params
 * @returns {*}
 */
export function queryDipData (params) {
  return request({
    url: '/operationalDecisionDeptAnalysisController/queryDipData',
    method: 'post',
    params: params
  })
}

/**
 * 查询DRG数据
 * @param params
 * @returns {*}
 */
export function queryDrgData (params) {
  return request({
    url: '/operationalDecisionDeptAnalysisController/queryDrgData',
    method: 'post',
    params: params
  })
}

export function queryData (params) {
  return request({
    url: '/operationalDecisionDeptAnalysisController/queryData',
    method: 'post',
    params: params
  })
}
