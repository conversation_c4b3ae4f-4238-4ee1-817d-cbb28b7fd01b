import request from '@/utils/request'

/*
 * 角色管理模块
 */

// 保存
export function save (data) {
  return request({
    url: '/role/save',
    method: 'post',
    data
  })
}
// 删除
export function batchDelete (data) {
  return request({
    url: '/role/delete',
    method: 'post',
    data
  })
}
// 分页查询
export function findPage (data) {
  return request({
    url: '/role/findPage',
    method: 'post',
    data
  })
}
// 查询全部
export function findAll () {
  return request({
    url: '/role/findAll',
    method: 'get'
  })
}
// 查询角色菜单集合
export function findRoleMenus (params) {
  return request({
    url: '/role/findRoleMenus',
    method: 'get',
    params
  })
}
// 保存角色菜单集合
export function saveRoleMenus (data) {
  return request({
    url: '/role/saveRoleMenus',
    method: 'post',
    data
  })
}
