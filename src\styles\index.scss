@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './common.scss';
@import './variable.css';
@import './module.scss';

body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

div:focus{
  outline: none;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

//main-container全局样式
.app-main{
  height:calc(100% - 101px);
}

.app-container {
  display:flex;
  width:100%;
  height:100%;
  flex-direction:column;
  padding:3px 10px;
}

//搜索栏样式
.filter-container {

}

//操作栏样式
.operate-container {
  margin-top: 20px;
}

.operate-container .btn-add {
  float: right;
}

/******    表格栏样式   ******/
//和上方内容隔离10px;
.table-container {
  margin-top: 8px;
}
//表格表头和内容始终对齐
.el-table th.gutter{
  display: table-cell!important;
}
.el-table th{
  height: 28px;
  fontFamily:'Microsoft YaHei,verdana';
  font-size: 13px;
  background: #eef1f6;
  color: #606266;
  font-weight: 600;
}

//批量操作栏样式
.batch-operate-container {
  display: inline-block;
  margin-top: 5px;
}

//分页栏样式
.pagination-container {
  position: absolute;
  right: 1%;
  bottom: 1.5%;
}
.el-pagination button, .el-pagination span:not([class*=suffix]){
  font-size:10px;
  height:18px;
  line-height:20px;
}
.el-pagination__total{
  margin-top:2px;
}
.el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li{
  height:20px;
  font-size:10px;
  line-height: 20px;
}
.el-pagination__editor.el-input .el-input__inner{
  height:20px;
  font-size:10px;
}
.el-pagination__sizes{
  font-size:10px;
}
.el-pagination .el-select .el-input .el-input__inner{
  height:22px;
  font-size:10px;
}
/*设置xx条/页的框的颜色*/
.el-select .el-input.is-focus .el-input__inner,
.el-pagination__sizes .el-input .el-input__inner:hover,
.el-select .el-input__inner:focus {
  border-color: #18ab8f;
}
/*设置当前页码的样式，及鼠标移上其他页码时的样式,以及左右箭头鼠标移上的样式*/
.el-pager li.active,.el-pager li:hover,
.el-pagination button:hover {
  color: #18ab8f;
}
/*设置当前选中的“xx条/页”的样式，是点击以后弹出来的框里的*/
li.el-select-dropdown__item.selected {
  color: #18ab8f;
}

//添加、更新表单样式
.form-container {
  position: absolute;
  left: 0;
  right: 0;
  width: 720px;
  padding: 35px 35px 15px 35px;
  margin: 20px auto;
}

//主标题
.font-extra-large {
  font-size: 20px;
  color: #303133;
}

//标题
.font-title-large {
  font-size: 18px;
  color: #303133;
}

//小标题
.font-title-medium {
  font-size: 16px;
  color: #303133;
}

//正文
.font-medium {
  font-size: 16px;
  color: #606266;
}

//正文
.font-small {
  font-size: 14px;
  color: #606266;
}

//正文（小）
.font-extra-small {
  font-size: 13px;
  color: #606266;
}

.color-main {
  color: #409EFF;
}

.color-success {
  color: #67C23A;
}

.color-warning {
  color: #E6A23C;
}

.color-danger {
  color: #F56C6C;
}

.color-info {
  color: #909399;
}

//上传组件：文件列表样式调整
.el-upload-list__item{
  font-size: 13px;
}
.el-form-item__label{
  font-size: 0.8rem;
}
//表单组件：去掉下部空白
.el-form-item el-form-item--mini{
  margin-bottom: 0px;
}

//盒模型布局
.flex{display: flex;}
.flex-1{flex: 1 auto;}
.flex-col{flex-direction: column;}
.flex-align-start{align-items: flex-start;}
.flex-align-center{align-items: center;}
.flex-align-end{align-items: end;}
.flex-justify-start{justify-items: flex-start;}
.flex-justify-center{justify-items: center;}
.flex-justify-end{justify-items:end;}

//统一字体大小
.fs-12{font-size: 12px;}
.fs-13{font-size: 13px;}
.fs-14{font-size: 14px;}
.fs-16{font-size: 16px;}

//字体超出控制
.text-ellip{display:inline-block; overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}

//日期组件
.el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner{
  width:240px;
}

//下转跳转样式
.skip{
  white-space:nowrap;
  width: 100%;
  overflow:hidden;
  text-overflow: ellipsis;
  color:#4169E1;
  text-decoration:underline;
  cursor:pointer;
}
//导出Excel按钮样式
.expBtn{
  background-color: #67c23a;
  color:#ffffff
}
//el-drawer抽屉样式
#el-drawer__title{font-size:14px;font-weight: 600;color:#409EFF;}
:focus{outline:0;}

//下拉树选中背景色设置
.el-tree-node.is-current > .el-tree-node__content  {
  background-color: #99CCFF ;
}

.el-menu.el-menu--horizontal{
  //border-bottom:solid 1px #304156
}
.title-icon{
  padding: 0 0 0 10px;
  position: relative;
}
.title-icon:before {
  content:'';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  display: inline-block;
  width: 4px;
  border-radius: 3px;
  background-color: #1b65b9;
}
