<template>
  <div class="app-container">
    <drg-container :headerPercent="11" :type="1">
      <template slot="header">
        <drg-title-line title="查询条件"/>
        <el-form :inline="true" :model="queryForm"  ref="queryForm" size="mini" style="height: 55%">
          <el-form-item class="som-el-form-item-margin-left">
<!--            <el-button type="primary" @click="queryData">查询</el-button>-->
            <el-button type="primary" @click="dialogVisible = true"><i class="el-icon-upload el-icon--left"></i>文件上传</el-button>
<!--            <el-button type="primary" @click="downloadTemplate"><i class="el-icon-download el-icon&#45;&#45;left"></i>模板下载</el-button>-->
            <el-button @click="refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="未匹配到病案列表"/>
        <el-table :data="tableData"
                  border
                  highlight-current-row
                  size="mini"
                  :header-cell-style="{'text-align':'center'}"
                  style="width: 100%; height: 95%">
          <el-table-column
            prop="a48"
            label="病案号"
            align="left">
          </el-table-column>

          <el-table-column
            prop="b15"
            align="left"
            label="出院时间">
          </el-table-column>

          <el-table-column
            prop="setlway"
            label="结算类型"
            align="left">
          </el-table-column>

          <el-table-column
            prop="a46c"
            align="left"
            label="人员类型">
          </el-table-column>

        </el-table>

        <el-dialog
          title="上传文件"
          :visible.sync="dialogVisible"
          width="50%">
          <el-upload
            style="text-align: center"
            drag
            ref="upload"
            :limit="1"
            action="customize"
            accept=".xlsx,.xls"
            :http-request="upload">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
          </el-upload>
        </el-dialog>
      </template>
    </drg-container>
  </div>
</template>

<script>

import { medicalTypeUpload } from '@/api/upload/upload'
export default {
  name: 'medicalTypeUpload',
  inject: ['reload'],
  data () {
    return {
      queryForm: {
        updt_date: []
      },
      dialogVisible: false,
      tableData: [],
      tableLoading: false
    }
  },
  created () {
  },
  mounted () {
  },
  methods: {
    upload: function (data) {
      let params = new FormData()
      params.append('file', data.file)
      medicalTypeUpload(params).then(res => {
        if (res.code === 200) {
          this.dialogVisible = false
          this.$refs.upload.clearFiles()
          this.$message.success('上传成功')
          this.tableData = res.data
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
      })
    },
    refresh () {
      this.reload()
    }
  }
}
</script>

<style scoped>
</style>
