import request from '@/utils/request'

export function queryData (data) {
  return request({
    url: '/listUploadController/queryData',
    method: 'post',
    data: data
  })
}
export function querySybData (data) {
  return request({
    url: '/listUploadController/querySybData',
    method: 'post',
    data: data
  })
}

export function updateData (data) {
  return request({
    url: '/listUploadController/updateData',
    method: 'post',
    data: data
  })
}

export function updateSybData (data) {
  return request({
    url: '/listUploadController/updateSybData',
    method: 'post',
    data: data
  })
}

export function queryRecordData (data) {
  return request({
    url: '/listUploadController/queryRecordData',
    method: 'post',
    data: data
  })
}

export function queryRecordSybData (data) {
  return request({
    url: '/listUploadController/queryRecordSybData',
    method: 'post',
    data: data
  })
}

export function queryDetailData (data) {
  return request({
    url: '/listUploadController/queryDetailData',
    method: 'post',
    data: data
  })
}

export function resetPolicyAdjustment (data) {
  return request({
    url: '/listUploadController/policyAdjustments',
    method: 'post',
    data: data
  })
}

export function queryDetailSybData (data) {
  return request({
    url: '/listUploadController/queryDetailSybData',
    method: 'post',
    data: data
  })
}

/**
 * 查询已上传数据
 * @param data
 * @returns {*}
 */
export function queryUploadedData (data) {
  return request({
    url: '/listUploadController/queryUploadedData',
    method: 'post',
    data: data
  })
}

export function queryUploadedSybData (data) {
  return request({
    url: '/listUploadController/queryUploadedSybData',
    method: 'post',
    data: data
  })
}

/**
 * 查询4103
 * @param data
 * @returns {*}
 */
export function querySettleListInfo (data) {
  return request({
    url: '/listUploadController/querySettleListInfo',
    method: 'post',
    data: data
  })
}

/**
 * 查询上传失败清单统计
 * @param data
 * @returns {*}
 */
export function queryUploadFalseData (data) {
  return request({
    url: '/listUploadController/queryUploadFalseData',
    method: 'post',
    data: data
  })
}

/**
 * 批量上传清单
 * @param params
 * @param group
 * @returns {*}
 */
export function batchListUpload (params) {
  return request({
    url: '/listUploadController/batchListUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}

/**
 * 查询清单状态分类数据
 * @param data
 * @returns {*}
 */
export function queryUpdateTypeData (data) {
  return request({
    url: '/listUploadController/queryUpdateTypeData',
    method: 'post',
    data: data
  })
}

/**
 * 修改状态
 * @param data
 * @returns {*}
 */
export function modifySettleListType (data) {
  return request({
    url: '/listUploadController/modifySettleListType',
    method: 'post',
    data: data
  })
}

/**
 * 撤销清单
 * @param data
 * @returns {*}
 */
export function cancelSettleList (data) {
  return request({
    url: '/listUploadController/listWithdrawn',
    method: 'post',
    data: data
  })
}


/**
 * 查询对照编码
 * @param data
 * @returns {*}
 */
export function codingControls (data) {
  return request({
    url: '/listUploadController/codingControls',
    method: 'post',
    data: data
  })
}
export function revokeTheidentity (data) {
  return request({
    url: '/listUploadController/revokeTheidentity',
    method: 'post',
    data: data
  })
}
// 数据撤回
export function dataWithdrawal (data) {
  return request({
    url: '/listUploadController/dataWithdrawal',
    method: 'post',
    data: data
  })
}
