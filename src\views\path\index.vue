<template>
  <div class="app-container" style="position: relative" v-loading="loading">
    <div class="pathway-button">
      <el-button type="primary" @click="showPathwayQuery = true" >路径筛选</el-button>
      <el-button type="primary" @click="generaPaths" >路径生成</el-button>
      <el-button type="primary" @click="queryDipPathway" >重置</el-button>
      <el-button type="primary" @click="back" v-if="show2Path">返回</el-button>
    </div>
    <!-- 查询条件 -->
    <pathway-query :show="showPathwayQuery" @close="showPathwayQuery = false" @query="params => this.queryDipPathway(params)"/>

    <!-- DIP -->
    <dip-pathway :data="dipChartData" @chartClick="params => this.queryDipPathway(params)" @showLeve2Path="showLeve2Path" ref="dipPaths"/>
  </div>
</template>
<script>
import { queryDipPathway } from '@/api/clinicalPathway'
import DipPathway from './comps/dipPathway'
import PathwayQuery from './comps/pathwayQuery'
export default {
  name: 'path',
  components: {
    'dip-pathway': DipPathway,
    'pathway-query': PathwayQuery
  },
  data: () => ({
    dipChartData: [],
    loading: false,
    showPathwayQuery: false,
    show2Path: false
  }),
  mounted () {
    this.queryDipPathway()
  },
  methods: {
    // 生成路径
    generaPaths () {

    },
    // 查询DIP路径
    queryDipPathway (params = {}) {
      this.loading = true
      queryDipPathway(params).then(res => {
        this.dipChartData = [res.data.data]
        this.loading = false
      })
    },
    // 返回第一路径
    back () {
      this.show2Path = false
      this.$refs.dipPaths.showLevel1()
      this.$nextTick(() => {
        let tcd = [...this.dipChartData]
        this.dipChartData = []
        this.dipChartData = tcd
      })
    },
    // 第二路径显示
    showLeve2Path () {
      this.show2Path = true
    }
  }
}
</script>
<style scoped>
</style>
