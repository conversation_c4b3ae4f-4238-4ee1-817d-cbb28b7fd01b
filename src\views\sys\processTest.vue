<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             headerTitle="查询条件"
             contentTitle="任务流程列表"
             @reset="reset"
             @query="queryData">
      <!-- 内容 -->
      <template slot="extendFormItems">
        <el-form-item label="任务名称">
          <el-input v-model="queryForm.taskName" placeholder="请输入任务名称"  @change="queryData"/>
        </el-form-item>
        <el-form-item label="任务key">
          <el-input v-model="queryForm.taskKey" placeholder="请输入任务key"  @change="queryData"/>
        </el-form-item>
        <el-form-item label="启用标志" prop="hospLv">
          <drg-dict-select dicType="STATUS" placeholder="请选择启用标志" v-model="queryForm.enabFlag" @change="queryData"/>
        </el-form-item>
      </template>
      <template slot="buttons">
        <el-button @click="showDialog()" type="primary" size="mini">测试</el-button>
      </template>
      <template slot="containerContent">
        <el-table
          :data="tableData"
          style="width: 100%">
          <el-table-column
            prop="id"
            label="数据流程配置项ID">
          </el-table-column>
          <el-table-column
            prop="taskName"
            label="任务名称">
          </el-table-column>
          <drg-table-column
            prop="taskKey"
            label="任务KEY">
          </drg-table-column>
          <el-table-column
            prop="enabFlag"
            label="启用标志">
          </el-table-column>
          <el-table-column
            prop="exeSeq"
            label="执行顺序">
          </el-table-column>
          <el-table-column label="启用状态" align="center" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.enabFlag"
                         @change="changeStart(scope.row)"
                         active-color="#13ce66"
                         inactive-color="#ff4949"
                         active-value="1"
                         inactive-value="0">
              </el-switch>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog :title="profttl" :visible.sync="dialogVisible">
          <div>
            <el-radio-group v-model="defaultYear" @change="changeDefault">
              <el-radio-button label="年"></el-radio-button>
              <el-radio-button label="月"></el-radio-button>
              <el-radio-button label="时间范围"></el-radio-button>
            </el-radio-group>
          </div>
          <div class="container" v-if="isYear">
            <div class="block" style="margin-top: 10px">
              <span class="demonstration">年</span>
              <el-date-picker
                v-model="form.year"
                type="year"
                placeholder="选择年">
              </el-date-picker>
            </div>
          </div>
          <div class="container" v-if="isMonth">
            <div class="block" style="margin-top: 10px">
              <span class="demonstration">月</span>
              <el-date-picker
                v-model="form.month"
                type="month"
                placeholder="选择月">
              </el-date-picker>
            </div>
          </div>
          <div class="block" style="margin-top: 10px" v-if="isScope" >
            <span class="demonstration">时间范围</span>
            <el-date-picker
              v-model="form.scope"
              type="daterange"
              size="mini"
              unlink-panels
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </div>
          <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="commitData()">确 定</el-button>
        </span>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { processTest, queryProcessJudge, updateStart } from '../../api/sys/processTest'
import moment from 'moment'

export default {
  name: 'processTest',
  data: () => ({
    queryForm: {
      taskName: '',
      taskKey: '',
      enabFlag: ''
    },
    tableData: [],
    dialogVisible: false,
    form: {
      month: '',
      year: '',
      dateType: '1',
      scope: '',
      begnDate: '',
      expiDate: ''
    },
    profttl: '测试',
    defaultYear: '年',
    isYear: true,
    isMonth: false,
    isScope: false,
    isStart: '0'

  }),
  mounted () {
    this.queryData()
  },
  methods: {
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    getForm () {
      let params = {
        month: moment(this.form.month).format('YYYY-MM'),
        year: moment(this.form.year).format('yyyy'),
        dateType: this.form.dateType,
        begnDate: this.form.scope[0],
        expiDate: this.form.scope[1]
      }
      return params
    },
    queryData () {
      queryProcessJudge(this.getParams()).then(res => {
        this.tableData = res.data
      })
    },
    showDialog (row) {
      this.dialogVisible = true
    },
    commitData () {
      processTest(this.getForm()).then(res => {
        if (res.code == 200) {
          this.$message.success('测试成功')
        } else {
          this.$message.success('测试失败')
        }
      })
      this.dialogVisible = false
    },
    changeDefault () {
      if (this.defaultYear == '年') {
        this.isYear = true
        this.isMonth = false
        this.isScope = false
        this.form.dateType = '1'
      } else if (this.defaultYear == '月') {
        this.isMonth = true
        this.isYear = false
        this.isScope = false
        this.form.dateType = '2'
      } else {
        this.isScope = true
        this.isMonth = false
        this.isYear = false
        this.form.dateType = '3'
      }
    },
    changeStart (row) {
      let params = {}
      Object.assign(params, row)
      updateStart(row).then(res => {
        if (res.code == 200) {
          if (params.enabFlag == 1) {
            this.$message.success('启用成功')
          } else if (params.enabFlag == 0) {
            this.$message.success('停用成功')
          }
          this.findMedical()
        }
      })
    },
    // 重置
    reset () {
      this.queryForm.taskKey = ''
      this.queryForm.enabFlag = ''
      this.queryForm.taskName = ''
      // this.queryData();
    }
  }
}
</script>
