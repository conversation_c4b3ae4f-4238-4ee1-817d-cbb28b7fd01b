import request from '@/utils/request'

export function selectDirectCost (params) {
  return request({
    url: '/hospitalCostBasicDataController/selectDirectCost',
    method: 'post',
    params: params
  })
}
export function selectInDirectCost (params) {
  return request({
    url: '/hospitalCostBasicDataController/selectInDirectCost',
    method: 'post',
    params: params
  })
}
export function selectDeptIncome (params) {
  return request({
    url: '/hospitalCostBasicDataController/selectDeptIncome',
    method: 'post',
    params: params
  })
}
export function selectDeptIncomeDetails (params) {
  return request({
    url: '/hospitalCostBasicDataController/selectDeptIncomeDetails',
    method: 'post',
    params: params
  })
}
export function insertInDirectCost (params) {
  return request({
    url: '/hospitalCostBasicDataController/insertInDirectCost',
    method: 'post',
    params: params
  })
}
/**
 * 删除科室间接成本
 * @param params
 * @returns {*}
 */
export function deleteInDirectCost (params) {
  return request({
    url: '/hospitalCostBasicDataController/deleteInDirectCost',
    method: 'post',
    params: params
  })
}
/**
 * 更新科室间接成本
 * @param params
 * @returns {*}
 */
export function updateInDirectCost (params) {
  return request({
    url: '/hospitalCostBasicDataController/updateInDirectCost',
    method: 'post',
    params: params
  })
}
