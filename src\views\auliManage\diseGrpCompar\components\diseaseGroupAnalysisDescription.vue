<template>
  <div class="som-comps-container">
    <!-- 基本信息 -->
    <el-descriptions title="基本信息" :column="4" border>
      <el-descriptions-item v-for="(item, index) in baseInfoData"
                            :key="index"
                            :label-style="{ width: '6rem' }"
                            :label="item.label">
        <el-tooltip :content="item.value"
                    placement="top-start"
                    :class="[item.overflow ? item.label == '编码' ? 'des-item-content-code' : 'des-item-content' : '']"
                    v-if="item.overflow">
          <div>
            {{ item.value }}
          </div>
        </el-tooltip>
        <div v-else>
          {{ item.value }}
        </div>
        <span v-if="item.type == 1">例</span>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 入组分析 -->
    <el-descriptions title="入组分析"
                     :column="4"
                     :colon="false"
                     border
                     class="descriptions">
      <el-descriptions-item v-for="(item, index) in inGroupData"
                            :key="index"
                            :contentStyle="{background: getCompareColor(item)}"
                            :label="item.label">
        {{ item.value }}
        <span v-if="item.type == 1">例</span>
        <span v-if="item.type == 2">/万</span>
        <span v-if="item.type == 3">元</span>
        <span v-if="item.type == 4">天</span>
      </el-descriptions-item>
    </el-descriptions>
    <!-- 费用分析 -->
    <el-descriptions title="费用分析" :column="4" class="descriptions" border>
      <el-descriptions-item v-for="(item, index) in costDataPay"
                            :key="index"
                            :label="item.label">
        {{ item.value }}
        <span v-if="item.type == 1">例</span>
        <span v-if="item.type == 2">/万</span>
        <span v-if="item.type == 3">元</span>
        <span v-if="item.type == 4">天</span>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 支付分析 -->
    <el-descriptions title="支付分析" :column="4" class="descriptions" border>
      <el-descriptions-item v-for="(item, index) in costDataIn"
                            :key="index"
                            :contentStyle="{background: getCompareColor(item)}"
                            :label="item.label">
        {{ item.value }}
        <span v-if="item.type == 1">例</span>
        <span v-if="item.type == 2">/万</span>
        <span v-if="item.type == 3">元</span>
        <span v-if="item.type == 4">天</span>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 支撑最后一个 descriptions 和底部高度 -->
    <div style="height: 2%" />

    <div class="check-info" :style="[ rightAdjust ? {right: '0'} : {right: '-2%'} ]">
      <el-button type="primary"
                 icon="el-icon-arrow-left"
                 @click="fnBackPage">
        返回
      </el-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'diseaseGroupAnalysisDescription',
  props: {
    prefix: {
      type: String,
      default: () => ''
    },
    preType: {
      type: String,
      default: () => ''
    },
    data: [],
    otherPageData: [],
    otherGroupData: [],
    groupData: [],
    costData: [],
    costPayData: [],
    rightAdjust: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    baseInfoData: [],
    inGroupData: [],
    costDataPay: [],
    costDataIn: []
  }),
  methods: {
    fnBackPage () {
      this.$emit('backPage', '')
    },
    handlerData (data) {
      this.baseInfoData = []
      if (this.prefix == '1') {
        this.baseInfoData.push({ label: '科室编码', value: data.deptCode })
        this.baseInfoData.push({ label: '科室名称', value: data.deptName, overflow: true })
        this.baseInfoData.push({ label: 'DIP编码', value: data.dipCodg, overflow: true })
        this.baseInfoData.push({ label: 'DIP名称', value: data.dipName, overflow: true })
      } else if (this.prefix == '3') {
        this.baseInfoData.push({ label: '科室编码', value: data.deptCode })
        this.baseInfoData.push({ label: '科室名称', value: data.deptName, overflow: true })
        this.baseInfoData.push({ label: 'DRG编码', value: data.drgCodg, overflow: true })
        this.baseInfoData.push({ label: 'DRG名称', value: data.drgName, overflow: true })
      } else if (this.prefix == '3') {
        if (this.preType == '1') {
          this.baseInfoData.push({ label: '科室编码', value: data.deptCode })
          this.baseInfoData.push({ label: '科室名称', value: data.deptName, overflow: true })
        }
        this.baseInfoData.push({ label: 'DRG编码', value: data.drgCodg, overflow: true })
        this.baseInfoData.push({ label: 'DRG名称', value: data.drgName, overflow: true })
      }
    },
    handlerGroupData (data, otherData) {
      this.inGroupData = []
      // this.inGroupData.push({label: 'DIP权重', value: data.dipWt})
      // this.inGroupData.push({label: 'cmi', value: data.cmi})
      if (this.prefix == '1') {
        this.inGroupData.push({ label: '住院总费用', value: data.sumfee, type: 3 })
        this.inGroupData.push({ label: '平均住院费用', value: data.avgCost, type: 3 })
        this.inGroupData.push({ label: '标杆住院费用（区域）', value: data.dipareaStandardCost, type: 3 })
        this.inGroupData.push({ label: '标杆住院费用（级别）', value: data.diplevelStandardCost, type: 3 })
        if (otherData) {
          let style = (Math.abs(data.dipqyfycy) == Math.abs(otherData.dipqyfycy)) ? 0 : (Math.abs(data.dipqyfycy) > Math.abs(otherData.dipqyfycy) ? 1 : 2)
          let style1 = (Math.abs(data.dipjbfycy) == Math.abs(otherData.dipjbfycy)) ? 0 : (Math.abs(data.dipjbfycy) > Math.abs(otherData.dipjbfycy) ? 1 : 2)
          if (data.dipqyfycy && otherData.dipqyfycy) {
            this.inGroupData.push({ label: '费用差异（区域与本院）', value: data.dipqyfycy, type: 3, style: style })
          } else {
            this.inGroupData.push({ label: '费用差异（区域与本院）', value: data.dipqyfycy, type: 3 })
          }
          if (data.dipjbfycy && otherData.dipjbfycy) {
            this.inGroupData.push({ label: '费用差异（级别与本院）', value: data.dipjbfycy, type: 3, style: style1 })
          } else {
            this.inGroupData.push({ label: '费用差异（级别与本院）', value: data.dipjbfycy, type: 3 })
          }
        } else {
          this.inGroupData.push({ label: '费用差异（区域与本院）', value: data.dipqyfycy, type: 3 })
          this.inGroupData.push({ label: '费用差异（级别与本院）', value: data.dipjbfycy, type: 3 })
        }
        this.inGroupData.push({ label: '平均住院天数', value: data.actIpt, type: 4 })
        this.inGroupData.push({ label: '标杆住院天数（区域）', value: data.dipareaStandardIndays, type: 4 })
        this.inGroupData.push({ label: '平均住院天数（级别）', value: data.diplevelStandardIndays, type: 4 })
        if (otherData) {
          let style = (Math.abs(data.dipindayqycy) == Math.abs(otherData.dipindayqycy)) ? 0 : (Math.abs(data.dipindayqycy) > Math.abs(otherData.dipindayqycy) ? 1 : 2)
          let style1 = (Math.abs(data.dipindayjbcy) == Math.abs(otherData.dipindayjbcy)) ? 0 : (Math.abs(data.dipindayjbcy) > Math.abs(otherData.dipindayjbcy) ? 1 : 2)
          if (data.dipindayqycy && otherData.dipindayqycy) {
            this.inGroupData.push({ label: '天数差值（区域与本院）', value: data.dipindayqycy, type: 4, style: style })
          } else {
            this.inGroupData.push({ label: '天数差值（区域与本院）', value: data.dipindayqycy, type: 4 })
          }
          if (data.dipindayjbcy && otherData.dipindayjbcy) {
            this.inGroupData.push({ label: '天数差值（级别与本院）', value: data.dipindayjbcy, type: 4, style: style1 })
          } else {
            this.inGroupData.push({ label: '天数差值（级别与本院）', value: data.dipindayjbcy, type: 4 })
          }
        } else {
          this.inGroupData.push({ label: '天数差值（区域与本院）', value: data.dipindayqycy, type: 4 })
          this.inGroupData.push({ label: '天数差值（级别与本院）', value: data.dipindayjbcy, type: 4 })
        }
        this.inGroupData.push({ label: '入组病案数', value: data.inGroupNumber, type: 1 })
      } else if (this.prefix == '3') {
        this.inGroupData.push({ label: '住院总费用', value: data.sumfee, type: 3 })
        this.inGroupData.push({ label: '平均住院费用', value: data.avgCost, type: 3 })
        this.inGroupData.push({ label: '标杆住院费用', value: data.drgareaStandardCost, type: 3 })
        if (otherData) {
          let style = (Math.abs(data.drgfycy) == Math.abs(otherData.drgfycy)) ? 0 : (Math.abs(data.drgfycy) > Math.abs(otherData.drgfycy) ? 1 : 2)
          if (data.drgfycy && otherData.drgfycy) {
            this.inGroupData.push({ label: '费用差异', value: data.drgfycy, type: 3, style: style })
          } else {
            this.inGroupData.push({ label: '费用差异', value: data.drgfycy, type: 3 })
          }
        } else {
          this.inGroupData.push({ label: '费用差异', value: data.drgfycy, type: 3 })
        }
        this.inGroupData.push({ label: '平均住院天数', value: data.actIpt, type: 4 })
        this.inGroupData.push({ label: '标杆住院天数', value: data.drgindayavg, type: 4 })
        if (otherData) {
          let style = (Math.abs(data.drgindaycy) == Math.abs(otherData.drgindaycy)) ? 0 : (Math.abs(data.drgindaycy) > Math.abs(otherData.drgindaycy) ? 1 : 2)
          if (data.drgindaycy && otherData.drgindaycy) {
            this.inGroupData.push({ label: '天数差值', value: data.drgindaycy, type: 4, style: style })
          } else {
            this.inGroupData.push({ label: '天数差值', value: data.drgindaycy, type: 4 })
          }
        } else {
          this.inGroupData.push({ label: '天数差值', value: data.drgindaycy, type: 4 })
        }

        this.inGroupData.push({ label: '入组病案数', value: data.inGroupNumber, type: 1 })
      }
    },
    handlerCostPayData (data) {
      this.costDataPay = []
      this.costDataPay.push({ label: '床位费', value: data.cwf, type: 3 })
      this.costDataPay.push({ label: '诊查费', value: data.zcf, type: 3 })
      this.costDataPay.push({ label: '检查费', value: data.jcf, type: 3 })
      this.costDataPay.push({ label: '化验费', value: data.hyf, type: 3 })
      this.costDataPay.push({ label: '治疗费', value: data.treat_fee, type: 3 })
      this.costDataPay.push({ label: '手术费', value: data.ssf, type: 3 })
      this.costDataPay.push({ label: '护理费', value: data.nursfee, type: 3 })
      this.costDataPay.push({ label: '卫生材料费', value: data.wsclf, type: 3 })
      this.costDataPay.push({ label: '西药费', value: data.west_fee, type: 3 })
      this.costDataPay.push({ label: '中药饮片费', value: data.zyypf, type: 3 })
      this.costDataPay.push({ label: '中成药', value: data.zcy, type: 3 })
      this.costDataPay.push({ label: '一般治疗费', value: data.ybzlf, type: 3 })
      this.costDataPay.push({ label: '挂号费', value: data.ghf, type: 3 })
      this.costDataPay.push({ label: '其他费', value: data.qt, type: 3 })
    },
    handlerCostData (data, otherData) {
      this.costDataIn = []
      this.costDataIn.push({ label: '预测费用', value: data.areaStandardCost, type: 3 })
      this.costDataIn.push({ label: '总费用', value: data.zong, type: 3 })
      if (otherData) {
        let style = (Math.abs(data.cy) == Math.abs(otherData.cy)) ? 0 : (Math.abs(data.cy) > Math.abs(otherData.cy) ? 1 : 2)
        if (data.cy && otherData.cy) {
          this.costDataIn.push({ label: '差异', value: data.cy, type: 3, style: style })
        } else {
          this.costDataIn.push({ label: '差异', value: data.cy, type: 3 })
        }
      } else {
        this.costDataIn.push({ label: '差异', value: data.cy, type: 3 })
      }
      this.costDataIn.push({ label: '差异比例', value: data.cyb })
    },
    getCompareColor (item) {
      if (item && item.style) {
        if (item.style == 1) {
          return '#FDE2E2'
        } else if (item.style == 2) {
          return '#E1F3D8'
        }
      }
      return ''
    }
  },
  watch: {
    data: function (data) {
      this.handlerData(data)
    },
    groupData: function (data) {
      this.handlerGroupData(data)
    },
    costPayData: function (data) {
      this.handlerCostPayData(data)
    },
    costData: function (data) {
      this.handlerCostData(data)
    },
    otherPageData: function (data) {
      if (this.costData) {
        this.handlerCostData(this.costData, data)
      }
    },
    otherGroupData: function (data) {
      if (this.groupData) {
        this.handlerGroupData(this.groupData, data)
      }
    }
  }
}
</script>
<style scoped>
.check-info{
  position: absolute;
  top: -1%;
  font-size: var(--textSize);
}
.descriptions{
  margin-top: 1rem;
}
.des-item-content{
  width: 7rem;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.des-item-content-code {
  width: 5rem;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.item-up{
  background: #E1F3D8;
}
.item-down{
  background: #FDE2E2;
}

</style>
