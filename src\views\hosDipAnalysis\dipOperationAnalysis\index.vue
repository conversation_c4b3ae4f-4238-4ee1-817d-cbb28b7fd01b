<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-hos-dept
             show-dip
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="手术分析"
             :container="true"
             @query="handleSearchList" @reset="handleResetSearch">

      <template slot="buttons">
        <el-popconfirm
          confirm-button-text='确定'
          cancel-button-text='导出全部'
          icon="el-icon-info"
          icon-color="red"
          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>

      <template slot="buttonsMiddle">
        <el-radio-group v-model="listQuery.queryType" size="mini" @change="changeSelectQueryType" v-if="this.$somms.hasHosRole()">
          <el-radio-button :label="1">科室查询</el-radio-button>
          <el-radio-button :label="2">病组查询</el-radio-button>
        </el-radio-group>
        <el-checkbox  v-model="hosOpr" size="mini" @change="handleCheckHosOprChange" class="som-el-form-item-margin-left">院内手术</el-checkbox>
        <el-checkbox  v-model="stanOpr" size="mini" @change="handleCheckStanOprChange">标准手术</el-checkbox>
      </template>

      <template slot="containerContent">
        <div style="height:40%">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="14" style="height: 100%">
              <div id="operativeCountLeft" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
            <el-col :span="10" style="height: 100%">
              <div id="operativeCountRight" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
          </el-row>
        </div>
        <div style="height: 60%;">
          <el-table ref="operativeAnalysisTable"
                    id="oprTable"
                    size="mini"
                    :header-cell-style = "{'text-align' : 'center'}"
                    highlight-current-row
                    height="100%"
                    :data="list"
                    style="width:100%"
                    @sort-change='sortChange'
                    @selection-change="handleSelectionChange"
                    v-loading="listLoading"
                    border>
            <!--            <el-table-column-->
            <!--              type="selection"-->
            <!--              width="55">-->
            <!--            </el-table-column>-->
            <el-table-column
              label="序号"
              align="right"
              type="index"
              width="50">
            </el-table-column>
            <el-table-column  label="DIP病组编码" prop="dipCodg" align="center" width="200" v-if="false">
              <template slot-scope="scope">{{scope.row.dipCodg | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column  label="DIP病组名称" prop="dipName" align="center" width="200"  :show-overflow-tooltip="true" v-if="showDrgs">
              <template slot-scope="scope">{{scope.row.dipName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column  label="出院科室编码" prop="priOutHosDeptCode" v-if="false">
              <template slot-scope="scope">{{scope.row.priOutHosDeptCode | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column  label="出院科室名称" prop="priOutHosDeptName" width="200"  align="left" :show-overflow-tooltip="true" v-if="showDept">
              <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="手术总例数"  align="right" width="150" prop="totalOprs"  sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.totalOprs)>0" class='skip' @click="queryTotalOprs(scope.row)">
                  {{scope.row.totalOprs | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.totalOprs)==0" style="color:#000000">
                  {{scope.row.totalOprs | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="一级手术"  align="right" width="150"   v-if="showHospitalOperation" prop="oneLvlOprHos" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.oneLvlOprHos)>0" class='skip' @click="queryOneLvlOprHos(scope.row)">
                  {{scope.row.oneLvlOprHos | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.oneLvlOprHos)==0" style="color:#000000">
                  {{scope.row.oneLvlOprHos | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="一级手术占比" prop="oneLvlOprHosRate" align="right" width="150" v-if="showHospitalOperation">
              <template slot-scope="scope">{{scope.row.oneLvlOprHosRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="一级手术（标准）"  align="right" width="150"   v-if="showStandardOperation" prop="oneLvlOprStan" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.oneLvlOprStan)>0" class='skip' @click="queryOneLvlOprStan(scope.row)">
                  {{scope.row.oneLvlOprStan | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.oneLvlOprStan)==0" style="color:#000000">
                  {{scope.row.oneLvlOprStan | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="一级手术占比（标准）" prop="oneLvlOprStanRate" align="right" width="155" v-if="showStandardOperation">
              <template slot-scope="scope">{{scope.row.oneLvlOprStanRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="二级手术" align="right" width="150"  v-if="showHospitalOperation" prop="twoLvlOprHos" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.twoLvlOprHos)>0" class='skip' @click="queryTwoLvlOprHos(scope.row)">
                  {{scope.row.twoLvlOprHos | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.twoLvlOprHos)==0" style="color:#000000">
                  {{scope.row.twoLvlOprHos | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="二级手术占比" prop="twoLvlOprHosRate" align="right" width="150"  v-if="showHospitalOperation">
              <template slot-scope="scope">{{scope.row.twoLvlOprHosRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="二级手术（标准）" align="right" width="150"  v-if="showStandardOperation" prop="twoLvlOprStan" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.twoLvlOprStan)>0" class='skip' @click="queryTwoLvlOprStan(scope.row)">
                  {{scope.row.twoLvlOprStan | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.twoLvlOprStan)==0" style="color:#000000">
                  {{scope.row.twoLvlOprStan | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="二级手术占比（标准）" prop="twoLvlOprStanRate" align="right" width="155"  v-if="showStandardOperation">
              <template slot-scope="scope">{{scope.row.twoLvlOprStanRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="三级手术" align="right" width="150"   v-if="showHospitalOperation" prop="threeLvlOprHos" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.threeLvlOprHos)>0" class='skip' @click="queryThreeLvlOprHos(scope.row)">
                  {{scope.row.threeLvlOprHos | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.threeLvlOprHos)==0" style="color:#000000">
                  {{scope.row.threeLvlOprHos | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="三级手术占比" prop="threeLvlOprHosRate" align="right" width="115"  v-if="showHospitalOperation">
              <template slot-scope="scope">{{scope.row.threeLvlOprHosRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="三级手术（标准）"  align="right" width="150"  v-if="showStandardOperation" prop="threeLvlOprStan" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.threeLvlOprStan)>0" class='skip' @click="queryThreeLvlOprStan(scope.row)">
                  {{scope.row.threeLvlOprStan | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.threeLvlOprStan)==0" style="color:#000000">
                  {{scope.row.threeLvlOprStan | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="三级手术占比（标准）" prop="threeLvlOprStanRate" align="right" width="155"   v-if="showStandardOperation">
              <template slot-scope="scope">{{scope.row.threeLvlOprStanRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="四级手术"  align="right" width="100"  v-if="showHospitalOperation" prop="fourLvlOprHos" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.fourLvlOprHos)>0" class='skip' @click="queryFourLvlOprHos(scope.row)">
                  {{scope.row.fourLvlOprHos | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.fourLvlOprHos)==0" style="color:#000000">
                  {{scope.row.fourLvlOprHos | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="四级手术占比" prop="fourLvlOprHosRate" align="right" width="115"  v-if="showHospitalOperation">
              <template slot-scope="scope">{{scope.row.fourLvlOprHosRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="四级手术（标准）"  align="right" width="150"  v-if="showStandardOperation" prop="fourLvlOprStan" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.fourLvlOprStan)>0" class='skip' @click="queryFourLvlOprStan(scope.row)">
                  {{scope.row.fourLvlOprStan | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.fourLvlOprStan)==0" style="color:#000000">
                  {{scope.row.fourLvlOprStan | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="四级手术占比（标准）" prop="fourLvlOprStanRate" align="right" width="155" v-if="showStandardOperation">
              <template slot-scope="scope">{{scope.row.fourLvlOprStanRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="其他手术"  align="right" width="110"  v-if="showHospitalOperation" prop="otherLvlOprHos" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.otherLvlOprHos)>0" class='skip' @click="queryOtherLvlOprHos(scope.row)">
                  {{scope.row.otherLvlOprHos | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.otherLvlOprHos)==0" style="color:#000000">
                  {{scope.row.otherLvlOprHos | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="其他手术占比" prop="otherLvlOprHosRate" align="right" width="115"  v-if="showHospitalOperation">
              <template slot-scope="scope">{{scope.row.otherLvlOprHosRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="其他手术（标准）"  align="right" width="150"  v-if="showStandardOperation" prop="otherOprStan" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.otherOprStan)>0" class='skip' @click="queryOtherOprStan(scope.row)">
                  {{scope.row.otherOprStan | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.otherOprStan)==0" style="color:#000000">
                  {{scope.row.otherOprStan | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="其他手术占比（标准）" prop="otherOprStanRate" align="right" width="155" v-if="showStandardOperation">
              <template slot-scope="scope">{{scope.row.otherOprStanRate | formatIsEmpty}}</template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, querySelectTreeAndSelectList, queryLikeDrgsByPram } from '@/api/common/drgCommon'
import { dipOperativeAnalysis as queryPageData, getDipOperativeCountInfo as getCountInfo } from '@/api/dipBusiness/dipOperationAnalysis'
import { elExportExcel } from '@/utils/exportExcel'
import echarts from 'echarts'
import { sortChange } from '@/utils/common'
const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  b16c: null,
  queryDrg: null,
  queryType: '1'
}
export default {
  name: 'dipOperationAnalysis',
  components: { },
  inject: ['reload'],
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      tempList: [],
      listLoading: true,
      list: null,
      total: null,
      cy_start_date: '',
      cy_end_date: '',
      listQuery: { ...Object.assign({}, defaultListQuery), queryType: '1' },
      submitListQuery: Object.assign({}, defaultListQuery),
      deptName: null,
      queryDrg: null,
      queryType: '1',
      showDrgs: false,
      showDept: true,
      showHospitalOperation: true,
      showStandardOperation: true,
      hosOpr: true,
      stanOpr: true,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(() => {
      if (!this.$somms.hasHosRole()) {
        this.listQuery.queryType = '2'
        this.changeSelectQueryType(2)
      }
    })
  },
  methods: {
    handleSelectionChange (val) {
      if (val.length == 0) {
        this.tempList = []
      } else {
        this.tempList = this.list.filter(data => {
          for (const v of val) {
            if (data.priOutHosDeptCode == v.priOutHosDeptCode) {
              return true
            }
          }
          return false
        })
      }
    },
    sortChange,
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
        this.getCount()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryDrg = this.listQuery.dipCodg
      this.submitListQuery.queryType = this.listQuery.queryType
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      // if (this.tempList.length > 0 && this.submitListQuery.b16c==null){
      //   this.list = this.tempList
      // } else {
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.tempList = this.list
        this.total = response.data.total
      })
      // }
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.operativeAnalysisTable.$children, document.getElementById('oprTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DRG手术分析')
    },
    getCount () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.queryDrg = this.listQuery.dipCodg
      this.submitListQuery.queryType = this.listQuery.queryType
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      getCountInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        let result = response.data
        let oprName = []
        let standardOpr = []
        let thisOpr = []
        let lastMonthOpr = []
        let lastYearOpr = []
        let thisIssue = []
        let lastYearIssue = []
        let lastMonthIssue = []
        let standardOprData = []
        if (result) {
          for (let i = 0; i < result.length; i++) {
            oprName.push(result[i].name)
            thisOpr.push(result[i].issueNum)
            lastMonthOpr.push(result[i].lastMonthNum)
            lastYearOpr.push(result[i].lastYearNum)
            standardOpr.push(result[i].standardNum)
            thisIssue.push({ name: result[i].name, value: result[i].issueNum })
            lastYearIssue.push({ name: result[i].name, value: result[i].lastYearNum })
            lastMonthIssue.push({ name: result[i].name, value: result[i].lastMonthNum })
            standardOprData.push({ name: result[i].name, value: result[i].standardNum })
          }
        }
        this.leftChart(oprName, standardOpr, thisOpr, lastMonthOpr, lastYearOpr)
        this.rightChart(thisIssue, lastYearIssue, lastMonthIssue, standardOprData)
      })
    },
    // 下转
    queryTotalOprs (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {

            allOprLevelCode: '1', // 赋值任意值，不为空即可，代表查询手术总例数下转
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            allOprLevelCode: '1', // 赋值任意值，不为空即可，代表查询手术总例数下转
            queryDrgsCode: row.dipCodg,
            queryDrgsName: row.dipName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1'
          }
        })
      }
    },
    queryOneLvlOprHos (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            oprLevelCode: '1', // 院内一级手术
            oprLevelName: '一级手术',
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            oprLevelCode: '1', // 院内一级手术
            oprLevelName: '一级手术',
            queryDrgsCode: row.dipCodg,
            queryDrgsName: row.dipName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '1'
          }
        })
      }
    },
    queryOneLvlOprStan (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            stanOprLevelCode: '1', // 一级标准手术
            stanOprLevelName: '一级手术',
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            stanOprLevelCode: '1', // 一级标准手术
            stanOprLevelName: '一级手术',
            queryDrgsCode: row.dipCodg,
            queryDrgsName: row.dipName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '1'
          }
        })
      }
    },
    queryTwoLvlOprHos (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            oprLevelCode: '2', // 院内二级手术
            oprLevelName: '二级手术',
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            oprLevelCode: '2', // 院内二级手术
            oprLevelName: '二级手术',
            queryDrgsCode: row.dipCodg,
            queryDrgsName: row.dipName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1'
          }
        })
      }
    },
    queryTwoLvlOprStan (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            stanOprLevelCode: '2', // 二级标准手术
            stanOprLevelName: '二级手术',
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            stanOprLevelCode: '2', // 二级标准手术
            stanOprLevelName: '二级手术',
            queryDrgsCode: row.dipCodg,
            queryDrgsName: row.dipName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1'
          }
        })
      }
    },
    queryThreeLvlOprHos (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            oprLevelCode: '3', // 院内三级手术
            oprLevelName: '三级手术',
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            oprLevelCode: '3', // 院内三级手术
            oprLevelName: '三级手术',
            queryDrgsCode: row.dipCodg,
            queryDrgsName: row.dipName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1'
          }
        })
      }
    },
    queryThreeLvlOprStan (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            stanOprLevelCode: '3', // 三级标准手术
            stanOprLevelName: '三级手术',
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            stanOprLevelCode: '3', // 三级标准手术
            stanOprLevelName: '三级手术',
            queryDrgsCode: row.drgCodg,
            queryDrgsName: row.drgName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1'
          }
        })
      }
    },
    queryFourLvlOprHos (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            oprLevelCode: '4', // 院内四级手术
            oprLevelName: '四级手术',
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            oprLevelCode: '4', // 院内四级手术
            oprLevelName: '四级手术',
            queryDrgsCode: row.dipCodg,
            queryDrgsName: row.dipName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '1'
          }
        })
      }
    },
    queryFourLvlOprStan (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            stanOprLevelCode: '4', // 四级标准手术
            stanOprLevelName: '四级手术',
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            stanOprLevelCode: '4', // 四级标准手术
            stanOprLevelName: '四级手术',
            queryDrgsCode: row.drgCodg,
            queryDrgsName: row.drgName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '1'
          }
        })
      }
    },
    queryOtherLvlOprHos (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            oprLevelCode: 'other', // 院内其他手术
            oprLevelName: '其他手术',
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            oprLevelCode: 'other', // 院内其他手术
            oprLevelName: '其他手术',
            queryDrgsCode: row.dipCodg,
            queryDrgsName: row.dipName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '1'
          }
        })
      }
    },
    queryOtherOprStan (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            stanOprLevelCode: 'other', // 其他标准手术
            stanOprLevelName: '其他手术',
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '1'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({ path: '/common/queryMedicalDetail',
          query: {
            stanOprLevelCode: 'other', // 其他标准手术
            stanOprLevelName: '其他手术',
            queryDrgsCode: row.drgCodg,
            queryDrgsName: row.drgName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '1'
          }
        })
      }
    },
    changeSelectQueryType (value) {
      if (value == 1) {
        this.showDrgs = false
        this.showDept = true
        this.queryDrg = null
      } else if (value == 2) {
        this.showDrgs = true
        this.showDept = false
        this.listQuery.b16c = null
      }
      this.getList()
      this.getCount()
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSelect (item) {
      this.listQuery.queryDrg = item.drgsCode
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        // this.listQuery.cysj = ['2019-06-01','2019-06-30'];
        // this.cy_start_date='2019-06-01';
        // this.cy_end_date='2019-06-30';
      }
      this.handleSearchList()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.listQuery.pageNum = 1
      if (this.listQuery.b16c) {
        this.deptName = document.getElementById('b13c').getElementsByTagName('input')[0].value
      } else {
        this.deptName = null
      }
      this.getList()
      this.getCount()
    },
    handleResetSearch () {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.showDrgs = false
      this.showDept = true
      this.reload()
      // this.getDataIsuue();
    },
    handleCheckHosOprChange (val) {
      this.hosOpr = val
      this.showHospitalOperation = val
    },
    handleCheckStanOprChange (val) {
      this.stanOpr = val
      this.showStandardOperation = val
    },
    leftChart (oprName, standardOpr, thisOpr, lastMonthOpr, lastYearOpr) {
      let colors = ['rgba(81,111,255,0.7)', 'rgba(11,162,179,0.7)', 'rgba(145,204,117,0.7)', 'rgba(253,94,81,0.7)']
      let option = {
        title: [
          { text: '各级别手术例数统计', left: 'left', top: 3, textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        legend: {
          itemWidth: 12,
          itemHeight: 12,
          data: ['标准手术', '本期数据', '同期数据', '上期数据']
        },
        xAxis: [
          {
            type: 'category',
            data: oprName,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '例数',
            axisLabel: {
              formatter: function (value) {
                if (value > 10000) {
                  return (Number(value) / 10000).toFixed(1) + '例'
                } else {
                  return Number(value).toFixed(1)
                }
              }
            }
          }
        ],
        series: [
          {
            name: '标准手术',
            type: 'bar',
            data: standardOpr,
            itemStyle: {
              normal: {
                color: colors[0]
              }
            }
          },
          {
            name: '本期数据',
            type: 'bar',
            data: thisOpr,
            itemStyle: {
              normal: {
                color: colors[1]
              }
            }
          },
          {
            name: '同期数据',
            type: 'bar',
            data: lastMonthOpr,
            itemStyle: {
              normal: {
                color: colors[2]
              }
            }
          },
          {
            name: '上期数据',
            type: 'bar',
            data: lastYearOpr,
            itemStyle: {
              normal: {
                color: colors[3]
              }
            }
          }
        ],
        grid: {
          top: '55',
          bottom: '30',
          left: '60',
          right: '30'
        }
      }
      let operativeCountLeft = echarts.getInstanceByDom(document.getElementById('operativeCountLeft'))
      if (operativeCountLeft) {
        operativeCountLeft.clear()
      } else {
        operativeCountLeft = echarts.init(document.getElementById('operativeCountLeft'))
      }
      operativeCountLeft.setOption(option)
      return operativeCountLeft
    },
    rightChart (thisIssue, lastYearIssue, lastMonthIssue, standardOprData) {
      let colors = ['rgba(36,185,179,0.7)', 'rgba(40,138,242,0.7)', 'rgba(253,94,81,0.7)', 'rgba(145,204,117,0.7)', ' ']
      let option = {
        title: [
          { text: '手术例数占比统计', left: 'left', top: 5, textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } },
          { text: '本期数据', left: '17%', top: '20%', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 13 } },
          { text: '同期数据', left: '57%', top: '20%', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 13 } },
          { text: '上期数据', left: '17%', top: '72%', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 13 } },
          { text: '标准手术', left: '57%', top: '72%', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 13 } }
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: '5',
          top: 'center',
          itemWidth: 12,
          itemHeight: 12
        },
        series: [
          {
            type: 'pie',
            radius: '45%', // 设置饼图大小
            center: ['42%', '25%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'inside',
                formatter: '{b} \n {d}%',
                fontFamily: 'Microsoft YaHei',
                fontSize: 9
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % 5]
                }
              }
            },
            data: thisIssue
          },
          {
            type: 'pie',
            radius: '45%', // 设置饼图大小
            center: ['82%', '25%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'inside',
                formatter: '{b} \n {d}%',
                fontFamily: 'Microsoft YaHei',
                fontSize: 10
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % 5]
                }
              }
            },
            data: lastYearIssue
          },
          {
            type: 'pie',
            radius: '45%', // 设置饼图大小
            center: ['42%', '75%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'inside',
                formatter: '{b} \n {d}%',
                fontFamily: 'Microsoft YaHei',
                fontSize: 10
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % 5]
                }
              }
            },
            data: lastMonthIssue
          },
          {
            type: 'pie',
            radius: '45%', // 设置饼图大小
            center: ['82%', '75%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'inside',
                formatter: '{b} \n {d}%',
                fontFamily: 'Microsoft YaHei',
                fontSize: 10
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % 5]
                }
              }
            },
            data: standardOprData
          }
        ]
      }

      let operativeCountRight = echarts.getInstanceByDom(document.getElementById('operativeCountRight'))
      if (operativeCountRight) {
        operativeCountRight.clear()
      } else {
        operativeCountRight = echarts.init(document.getElementById('operativeCountRight'))
      }
      operativeCountRight.setOption(option)
      return operativeCountRight
    },
    exportExcel () {
      let tableId = 'oprTable'
      let fileName = '医院手术分析'
      elExportExcel(tableId, fileName)
    }
  }
}
</script>
<style scoped>
/deep/ .el-scrollbar__wrap {
  max-height: 450px;
}
/deep/ .el-autocomplete-suggestion li {
  line-height: 20px;
}
/deep/ .el-table__header-wrapper {
  height: 12%!important;
}
/deep/ .el-table__body-wrapper {
  height: 88%!important;
}
.code {
  font-size: 12px;
  color: #000000;
  text-overflow: ellipsis;
  overflow: hidden;
}
.name {
  font-size: 10px;
  color: #9b9b9b;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
