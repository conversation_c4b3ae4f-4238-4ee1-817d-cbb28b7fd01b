* {
    outline-width: 0;
    font-family: "Nunito" !important;
}

.loginContainer {
    height: 100vh;
    width: 100vw;
    background: url("../../assets/images/login/login_zjxrmyy_bg.jpg") center/cover fixed;
    display: flex;
    justify-content: center;
    align-items: center;
}

#formContainer {
    display: flex;
    transition: 0.2s ease;
    height: 342.5px;
    transition-delay: 0.3s;
}

#formContainer.toggle {
    height: 480px;
    transition-delay: 0s;
}

.formLeft {
    background: #fff;
    border-radius: 5px 0 0 5px;
    padding: 0 35px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.formLeft p {
    width: 100px;
}

.formLeft img {
    display: block;
    width: 72px;
    border-radius: 50%;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
}

.formRight {
    position: relative;
    overflow: hidden;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.formRight:before {
    content: "";
    position: absolute;
    top: -10px;
    left: -10px;
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    box-shadow: inset 0 0 0 1000px rgba(0, 0, 0, 0.5);
    filter: blur(5px);
}

.formRight form {
    position: relative;
    width: 350px;
    padding: 25px;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
}

.formRight form header {
    color: #fff;
    text-align: center;
    margin-bottom: 15px;
}

.formRight form header h1 {
    margin: 0;
    font-weight: 400;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.formRight form header p {
    margin: 5px 0 0;
    opacity: 0.5;
    font-size: 14px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.formRight form section label {
    display: block;
    margin-bottom: 15px;
    position: relative;
}

.formRight form section label p {
    color: #fff;
    margin: 0 0 10px 0;
    font-weight: 600;
    font-size: 12px;
    opacity: 0.5;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.formRight form section label input:focus~.border {
    transform: scale(1, 1);
}

.formRight form section label input:not(:-moz-placeholder-shown)~.border {
    transform: scale(1, 1);
}

.formRight form section label input:not(:-ms-input-placeholder)~.border {
    transform: scale(1, 1);
}

.formRight form section label input:not(:placeholder-shown)~.border {
    transform: scale(1, 1);
}

.formRight form section label:last-child {
    margin-bottom: 0;
}

.formRight form footer {
    margin-top: 15px;
    display: flex;
}

.formRight form footer button {
    background: transparent;
    padding: 0;
    border: none;
    color: #fff;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    flex: 1;
    opacity: 0.5;
}

.formRight form footer button:hover {
    opacity: 1;
}

.formRight form.otherForm {
    top: 0;
    left: 0;
    position: absolute;
    background: #fff;
    height: 100%;
    z-index: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 0;
    padding: 25px 0;
    transition: 0.2s ease;
    transition-delay: 0.2s;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
}

.formRight form.otherForm header {
    color: #000;
    opacity: 0;
    transition: 0.2s ease;
    transition-delay: 0s;
}

.formRight form.otherForm p {
    color: #000;
}

.formRight form.otherForm section {
    opacity: 0;
    transition: 0.2s ease;
    transition-delay: 0s;
}

.formRight form.otherForm footer {
    border-top-color: rgba(0, 0, 0, 0.1);
    opacity: 0;
}

.formRight form.otherForm footer button {
    color: #000;
}

.formRight form.otherForm input {
    border-color: rgba(0, 0, 0, 0.1);
    color: #000;
}

.formRight form.otherForm .border {
    background: #000;
}

.formRight form.otherForm.toggle {
    width: 100%;
    padding: 25px;
    transition-delay: 0s;
}

.formRight form.otherForm.toggle header,
.formRight form.otherForm.toggle section,
.formRight form.otherForm.toggle footer {
    opacity: 1;
    transition-delay: 0.3s;
}

/deep/ .el-button {
  position: relative;
  top: 20px;
  background: #00897B;
  border: none;
  width: 100%;
  padding: 10px 0;
  font-weight: 600;
  color: #fff;
  cursor: pointer;
}

/deep/ .el-button:hover{
  color: white;
  background: #007f72;
}

/deep/ .el-button:focus{
  color: white;
  background: #007f72;
}
