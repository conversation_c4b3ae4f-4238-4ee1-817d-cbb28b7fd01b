<template>
  <div class="page-container">
    <!--工具栏-->
    <el-row  style="width: 100%" ref="tools_content">
      <div class="toolbar" style="float:left;padding-top:10px;padding-left:15px;">
        <el-form :inline="true" :model="filters" :size="size">
          <el-form-item>
            <el-input v-model="filters.name" size="mini" placeholder="角色名"></el-input>
          </el-form-item>
          <el-form-item>
            <kt-button icon="fa fa-search" :label="$t('action.search')" auth="sys:role:view" type="primary" @click="findPage(null)"/>
          </el-form-item>
          <el-form-item>
            <kt-button icon="fa fa-plus" :label="$t('action.add')" auth="sys:role:add" type="primary" @click="handleAdd" />
          </el-form-item>
        </el-form>
      </div>
    </el-row>
    <!--表格内容栏-->
    <el-row :gutter="0">
      <el-col :span="16">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span class="role-span">角色列表</span>
          </div>
          <div>
            <kt-table permsEdit="sys:role:edit" permsDelete="sys:role:delete" :highlightCurrentRow="true" :stripe="false"
              :data="pageResult" :columns="columns" :showBatchDelete="false" @handleCurrentChange="handleRoleSelectChange"
              @findPage="findPage" @handleEdit="handleEdit" @handleDelete="handleDelete" :height="roleMenuHeight.height">
            </kt-table>
          </div>
        </el-card>
      </el-col>
      <!--角色菜单，表格树内容栏-->
      <el-col :span="8">
          <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <span class="role-span">角色菜单授权</span>
            </div>
            <div :style="menuDataTreeHeight"  class="menuDataTree">
              <el-tree :data="menuData" size="mini" show-checkbox node-key="id" :props="defaultProps"
                       style="width: 100%;pading-top:20px;" ref="menuTree" :render-content="renderContent"
                       v-loading="menuLoading" element-loading-text="拼命加载中" :check-strictly="true"
                       @check-change="handleMenuCheckChange">
              </el-tree>
            </div>
            <div style="float:left;padding-left:24px;padding-top:12px;padding-bottom:4px;">
              <el-checkbox v-model="checkAll" @change="handleCheckAll" :disabled="this.selectRole.id == null"><b>全选</b></el-checkbox>
            </div>
            <div style="float:right;padding-right:15px;padding-top:4px;padding-bottom:4px;">
              <kt-button :label="$t('action.reset')" auth="sys:role:edit" type="primary" @click="resetSelection"
                         :disabled="this.selectRole.id == null"/>
              <kt-button :label="$t('action.submit')" auth="sys:role:edit" type="primary" @click="submitAuthForm"
                         :disabled="this.selectRole.id == null" :loading="authLoading"/>
            </div>
          </el-card>
      </el-col>
    </el-row>
  <!-- </el-col> -->
  <!--新增编辑界面-->
  <el-dialog :title="oprt?'新增':'编辑'" width="40%" :visible.sync="dialogVisible" :close-on-click-modal="false" v-som-dialog-drag>
    <el-form :model="dataForm" label-width="80px" :rules="dataFormRules" ref="dataForm" :size="size">
      <el-form-item label="ID" prop="id" v-if="false">
        <el-input v-model="dataForm.id" :disabled="true" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="角色编码" prop="name">
        <el-input v-model="dataForm.name" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="角色名" prop="memo_info">
        <el-input v-model="dataForm.memo_info" auto-complete="off" type="textarea"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button :size="size" @click.native="dialogVisible = false">{{$t('action.cancel')}}</el-button>
      <el-button :size="size" type="primary" @click.native="submitForm" :loading="editLoading">{{$t('action.submit')}}</el-button>
    </div>
  </el-dialog>

  </div>
</template>

<script>
import KtTable from '@/views/core/KtTable'
import KtButton from '@/views/core/KtButton'
import { format } from '@/utils/datetime'
import { save, batchDelete, findPage, findAll, findRoleMenus, saveRoleMenus } from '@/api/role'
import { findMenuTree } from '@/api/menu'
export default {
  name:'role',
  components: {
    KtTable,
    KtButton
  },
  data () {
    return {
      size: 'small',
      filters: {
        name: ''
      },
      columns: [
        { prop: 'id', label: 'ID', minWidth: 50 },
        { prop: 'name', label: '角色编码', minWidth: 120 },
        { prop: 'memo_info', label: '角色名', minWidth: 120 },
        { prop: 'crter', label: '创建人', minWidth: 120 },
        { prop: 'crteTime', label: '创建时间', minWidth: 120, formatter: this.dateFormat }
        // {prop:"updtPsn", label:"更新人", minWidth:100},
        // {prop:"updtTime", label:"更新时间", minWidth:120, formatter:this.dateFormat}
      ],
      pageRequest: { pageNum: 1, pageSize: 10 },
      pageResult: {},

      oprt: false, // true:新增, false:编辑
      dialogVisible: false, // 新增编辑界面是否显示
      editLoading: false,
      dataFormRules: {
        name: [
          { required: true, message: '请输入角色名', trigger: 'blur' }
        ]
      },
      // 新增编辑界面数据
      dataForm: {
        id: 0,
        name: '',
        memo_info: ''
      },
      selectRole: {},
      menuData: [],
      menuSelections: [],
      menuLoading: false,
      authLoading: false,
      checkAll: false,
      currentRoleMenus: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      roleMenuHeight: {
        height: ''
      },
      menuDataTreeHeight: {
        height: ''
      }
    }
  },
  created () {
    window.addEventListener('resize', this.getHeight)
    this.getHeight()
  },
  methods: {
    // 下拉树的高度自适应
    getHeight () {
      this.menuDataTreeHeight.height = window.innerHeight - 260 + 'px'
      this.roleMenuHeight.height = window.innerHeight - 260
    },
    // 获取分页数据
    findPage: function (data) {
      if (data !== null) {
        this.pageRequest = data.pageRequest
      }
      this.pageRequest.columnFilters = { name: { name: 'name', value: this.filters.name } }
      findPage(this.pageRequest).then((res) => {
        this.pageResult = res.data
        this.findTreeData()
      }).then(data != null ? data.callback : '')
    },
    // 批量删除
    handleDelete: function (data) {
      batchDelete(data.params).then(data.callback)
    },
    // 显示新增界面
    handleAdd: function () {
      this.dialogVisible = true
      this.oprt = true
      this.dataForm = {
        id: 0,
        name: '',
        memo_info: ''
      }
    },
    // 显示编辑界面
    handleEdit: function (params) {
      this.dialogVisible = true
      this.oprt = false
      this.dataForm = Object.assign({}, params.row)
    },
    // 编辑
    submitForm: function () {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.$confirm('确认提交吗？', '提示', {}).then(() => {
            this.editLoading = true
            let params = Object.assign({}, this.dataForm)
            save(params).then((res) => {
              this.editLoading = false
              if (res.code == 200) {
                this.$message({ message: '操作成功', type: 'success' })
                this.dialogVisible = false
                this.$refs['dataForm'].resetFields()
              } else {
                this.$message({ message: '操作失败, ' + res.msg, type: 'error' })
              }
              this.findPage(null)
            })
          })
        }
      })
    },
    // 获取数据
    findTreeData: function () {
      this.menuLoading = true
      findMenuTree().then((res) => {
        this.menuData = res.data
        this.menuLoading = false
      })
    },
    // 角色选择改变监听
    handleRoleSelectChange (val) {
      if (val == null || val.val == null) {
        return
      }
      this.selectRole = val.val
      findRoleMenus({ roleId: val.val.id }).then((res) => {
        this.currentRoleMenus = res.data
        this.$refs.menuTree.setCheckedNodes(res.data)
      })
    },
    // 树节点选择监听
    handleMenuCheckChange (data, check, subCheck) {
      if (check) {
        if (data.children.length > 0) {
          let allId = this.$refs.menuTree.getCheckedKeys()
          let childrenIds = []
          data.children.forEach(element => {
            childrenIds.push(element.id)
          })
          let flag = true
          for (let curId of childrenIds) {
            if (allId.includes(curId)) {
              flag = false
              break
            }
          }
          if (flag) {
            data.children.forEach(element => {
              this.$refs.menuTree.setChecked(element.id, true, false)
            })
          }
        } else {
          // 节点选中时同步选中父节点
          let prntMenuIdLv1Menu = data.prntMenuIdLv1Menu
          this.$refs.menuTree.setChecked(prntMenuIdLv1Menu, true, false)
        }
      } else {
        // 节点取消选中时同步取消选中子节点
        if (data.children != null) {
          data.children.forEach(element => {
            this.$refs.menuTree.setChecked(element.id, false, false)
          })
        }
        if (data.prntMenuIdLv1Menu != 0) {
          let curData = this.$refs.menuTree.getNode(data.prntMenuIdLv1Menu)
          let checkedArr = []
          for (let node of curData.childNodes) {
            if (node.checked) {
              checkedArr.push(node.id)
            }
          }
          if (checkedArr.length == 0) {
            this.$refs.menuTree.setChecked(data.prntMenuIdLv1Menu, false, false)
          }
        }
      }
    },
    // 重置选择
    resetSelection () {
      this.checkAll = false
      this.$refs.menuTree.setCheckedNodes(this.currentRoleMenus)
    },
    // 全选操作
    handleCheckAll () {
      if (this.checkAll) {
        let allMenus = []
        this.checkAllMenu(this.menuData, allMenus)
        this.$refs.menuTree.setCheckedNodes(allMenus)
      } else {
        this.$refs.menuTree.setCheckedNodes([])
      }
    },
    // 递归全选
    checkAllMenu (menuData, allMenus) {
      menuData.forEach(menu => {
        allMenus.push(menu)
        if (menu.children) {
          this.checkAllMenu(menu.children, allMenus)
        }
      })
    },
    // 角色菜单授权提交
    submitAuthForm () {
      let roleId = this.selectRole.id
      if (this.selectRole.name == 'admin') {
        this.$message({ message: '超级管理员拥有所有菜单权限，不允许修改！', type: 'error' })
        return
      }
      this.authLoading = true
      let checkedNodes = this.$refs.menuTree.getCheckedNodes(false, true)
      let roleMenus = []
      for (let i = 0, len = checkedNodes.length; i < len; i++) {
        let roleMenu = { roleId: roleId, menuId: checkedNodes[i].id }
        roleMenus.push(roleMenu)
      }
      saveRoleMenus(roleMenus).then((res) => {
        if (res.code == 200) {
          this.$message({ message: '操作成功', type: 'success' })
        } else {
          this.$message({ message: '操作失败, ' + res.msg, type: 'error' })
        }
        this.authLoading = false
      })
    },
    renderContent (h, { node, data, store }) {
      return (
        <div class="column-container">
          <span style="text-algin:center;margin-right:100px;">{data.name}</span>
          <span style="text-algin:center;margin-right:100px;">
            <el-tag type={data.type === 0 ? '' : data.type === 1 ? 'success' : 'info'} size="small">
              {data.type === 0 ? '目录' : data.type === 1 ? '菜单' : '按钮'}
            </el-tag>
          </span>
        </div>)
    },
    // <span style="text-algin:center;margin-right:80px;">{data.url?data.url:'\t'}</span>
    // 时间格式化
    dateFormat: function (row, column, cellValue, index) {
      return format(row[column.property])
    }

  },
  mounted () {
  }
}
</script>
<style scoped>
  .menuDataTree{
    width:100%;
    font-size:13px;
    overflow-y: auto;
  }
  .role-span {
    font-weight: bold;
    color: #000000;
    font-size: 15px;
  }
  /deep/ .el-card__header{
    background-color: #CCCCCC;
    padding:10px 20px;
  }
</style>
