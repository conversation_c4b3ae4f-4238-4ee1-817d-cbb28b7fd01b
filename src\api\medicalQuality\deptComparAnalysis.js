import request from '@/utils/request'

export function getData (params) {
  return request({
    url: '/deptComparAnalysis/getData',
    method: 'post',
    params: params
  })
}

export function getCost (params) {
  return request({
    url: '/deptComparAnalysis/getCost',
    method: 'post',
    params: params
  })
}

export function getDrgData (params) {
  return request({
    url: '/deptComparAnalysis/getDrgData',
    method: 'post',
    params: params
  })
}

export function getPayCostData (params) {
  return request({
    url: '/deptComparAnalysis/getPayCostData',
    method: 'post',
    params: params
  })
}

export function getDrgPayCostData (params) {
  return request({
    url: '/deptComparAnalysis/getDrgPayCostData',
    method: 'post',
    params: params
  })
}
