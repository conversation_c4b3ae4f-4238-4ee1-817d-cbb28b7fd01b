<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="somForm"
             :show-hos-dept="{ show: this.$somms.hasHosRole()}"
             show-date-range
             show-in-date-range
             show-se-date-range
             :initTimeValueNotQuery="false"
             headerTitle="查询条件"
             :container="true"
              :showCoustemContentTitle="true"
             :showPagination="showTablePage"
             :totalNum="total"
             :exportExcel="{ 'tableId': tableId, exportName: exportTableName}"
             :exportExcelFun="exportExcelFun"
             :exportExcelHasChild="true"
             @query="radioChange(radioMode)"
             @reset="reset">
      <template slot="extendFormItems" >
        <el-form-item label="医生姓名" prop="drName" v-if="radioMode != 4" class="som-form-extend-form-item">
          <el-select v-model="queryForm.doctorValue"
                     filterable
                     placeholder="请选择"
                     clearable
                     @change="doctorValueChange">
            <el-option
              v-for="item in doctorValueList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参保类型">
          <drg-dict-select dicType="INSURANCE_TYPE" placeholder="请选择参保类型" v-model="queryForm.categories"
                           @change="radioChange(radioMode)"/>
        </el-form-item>
      </template>
      <!-- profttl -->
      <template slot="contentTitle">
        <drg-title-line :title="profttl" :wrapStyle="{ width: 'calc(80% - 10px)'}">
          <template slot="rightSide">
            <div style="display: flex">
              <div>
                <el-radio-group v-model="radioMode" @change="radioChange">
                  <el-radio-button :label="1">指标</el-radio-button>
                  <el-radio-button :label="2">预测</el-radio-button>
                  <el-radio-button :label="3">分析</el-radio-button>
                  <el-radio-button :label="4">对比</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>

          <template slot="titleRight">
            <!-- 固定列 -->
            <el-select v-model="columnVal"
                       multiple
                       collapse-tags
                       :multiple-limit="3"
                       placeholder="请选择固定列"
                       v-if="radioMode != 4">
              <el-option
                v-for="item in columnOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </template>
        </drg-title-line>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <div class="content-wrapper">

          <!-- 左侧 -->
          <div class="content-wrapper-left" v-if="!analysis && radioMode != 4">

            <!-- 指标table -->
            <kpi-table :data="tableData"
                       ref="dataTable"
                       :loading="loading"
                       :fixed-columns="columnVal"
                       :id="kipId"
                       :query-form="queryForm"
                       v-if="radioMode == 1"
                       :columnOptions="columnOptions"
                       @setRefObj="(obj) => this.tableObj = obj"
                       @showSuspension="showSuspension"/>

            <!-- 预测table -->
            <forecast-table :data="tableData"
                            ref="dataTable"
                            :loading="loading"
                            :fixed-columns="columnVal"
                            :id="forecastId"
                            :query-form="queryForm"
                            v-if="radioMode == 2"
                            :columnOptions="columnOptions"
                            @setRefObj="(obj) => this.tableObj = obj"
                            @showSuspension="showSuspension"/>
          </div>

          <!--悬浮框-->
          <suspension-frame
            :zbData="zbData"
            :ycData="ycData"/>

          <!-- 右侧 -->
          <div class="content-wrapper-right" v-if="!analysis && radioMode != 4">
            <div class="content-wrapper-right-top">
              <div style="display: flex;">
                <!-- 排序字段选择 -->
                <div style="width: 60%;padding-right: 2%">
                  <el-select v-model="selectVal" placeholder="请选择" @change="selectChange">
                    <el-option
                      v-for="item in option"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>

                <!-- top -->
                <div style="width: 40%">
                  <el-select v-model="topVal" placeholder="请选择" @change="generateChartData">
                    <el-option
                      v-for="item in topOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>
              </div>

              <!-- 排序 -->
              <div class="content-wrapper-right-icon" @click="sortClick">
                <i class="el-icon-sort"></i>
              </div>
            </div>
            <div style="height: 93%;width: 100%;position: absolute;top: 7%">
              <!-- 图 -->
              <drg-echarts :options="chartOption" ref="chart"/>
            </div>
          </div>

          <!-- 左侧分析 -->
          <div class="content-wrapper-analysis" v-if="analysis && radioMode != 4">
            <analysis-comp ref="dataTable"
                           :data="analysisPageData"
                           :id="analysisId"
                           :dropdown="doctorDropdownList"
                           :dropdown-check-val="dropdownVal"
                           :loading="loading"
                           v-if="showAnalysis"
                           :is-loss="isLoss"
                           :queryForm="queryForm"
                           @setRefObj="(obj) => this.tableObj = obj"
                           @dropdownChange="analysisDropdownChange"
                           @checkTypeChange="analysisTypeChange"/>

            <analysis-table :data="analysisTableData"
                            ref="dataTable"
                            :loading="loading"
                            :fixed-columns="columnVal"
                            :id="analysisId"
                            :is-loss="isLoss"
                            v-if="!showAnalysis"
                            :query-form="queryForm"
                            @showAnalysisPage="switchChartTable"
                            @setRefObj="(obj) => this.tableObj = obj"/>

          </div>
          <div v-if="radioMode == 3">
          <!-- 盈利或亏损 -->
          <div class="content-wrapper-analysis-loss-profit">
            <!-- profit -->
            <i class="som-icon-profit som-iconTool"
               title="盈利"
               v-if="!isLoss"
               @click="switchLossOrProfit(true)"
               style="height: 1.2rem;width: 1.2rem"></i>

            <!-- loss -->
            <i class="som-icon-loss som-iconTool"
               title="亏损"
               v-if="isLoss"
               @click="switchLossOrProfit(false)"
               style="height: 1.2rem;width: 1.2rem"></i>
          </div>

          <!-- 分析页面 - 切换图表按钮 -->
          <div class="content-wrapper-analysis-chart-table">
            <!-- splashes -->
            <i class="som-icon-table som-iconTool"
               title="分析"
               v-if="showAnalysis"
               @click="switchChartTable(null)"
               style="height: 1.2rem;width: 1.2rem"></i>

            <!-- table -->
            <i class="som-icon-analysis som-iconTool"
               title="表格"
               v-if="!showAnalysis"
               @click="switchChartTable(null)"
               style="height: 1.2rem;width: 1.2rem"></i>
          </div>
          </div>
          <div class="content-wrapper-analysis" v-if="radioMode == 4">
<!--            <div class="right-top">-->
<!--              <div style="display: flex">-->
<!--                <div style="width: 80%; padding-right: 6%" class="right-top-dropdown">-->
<!--                  <el-select placeholder="请选择">-->
<!--                    <el-option v-for="(item, index) in [{label:'',value:''}]"-->
<!--                               :key="index"-->
<!--                               :label="item.label"-->
<!--                               :value="item.value" />-->
<!--                  </el-select>-->
<!--                </div>-->
<!--                <div style="width: 15%;" class="right-top-icon">-->
<!--                  <i class="el-icon-sort" />-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
            <contrast :data="contrastData"
                      :dropdown-val="deptDropdownVal"
                      :dropdown-data="deptDropdown"
                      :loading="contrastLoading"
                      @deptChange="deptChange" />
          </div>
        </div>
      </template>
    </drg-form>
  </div>
</template>

<script>
import { queryDoctorKpiData, queryDoctorForecastData } from '@/api/newBusiness/newBusinessDoctor'
import { queryDiseaseLoss, queryPatientLoss,
  queryDropdown, queryAnalysisSummary,
  queryMedError, updateSwitchState,
  queryDoctorContrastData, queryDeptDropdown } from '@/api/newBusiness/newBusinessCommon'
import kpiTable from './comps/newDipDoctorKpiTable'
import forecastTable from './comps/newDipDoctorForecastTable'
import analysisComp from './comps/newDipDoctorAnalysisComp'
import analysisTable from './comps/newDipDoctorAnalysisTable'
import SuspensionFrame from '../deptAnalysisNew/comps/newSuspensionFrame'
import contrast from './comps/newDipDoctorCardComp'

let kpiOptions = [
  { value: 'drugRatio', label: '药占比' },
  { value: 'consumeRatio', label: '耗占比' },
  { value: 'timeIndex', label: '时间消耗指数' },
  { value: 'costIndex', label: '费用消耗指数' }
]

let forecastOptions = [
  { value: 'forecastAmountDiff', label: '预测金额差异' },
  { value: 'forecastAmount', label: '预测金额' },
  { value: 'oeVal', label: 'O/E值' }
]
export default {
  name: 'doctAnalysis',
  components: {
    kpiTable,
    forecastTable,
    analysisComp,
    analysisTable,
    'suspension-frame': SuspensionFrame,
    contrast
  },
  data: () => ({
    queryForm: {
      isLossType: '',
      categories:'',
      feeStas: '0'
    },
    radioMode: 1,
    chartOption: {},
    tableData: [],
    total: 0,
    loading: false,
    yAxisData: [],
    seriesData: [],
    selectVal: 'drugRatio',
    option: kpiOptions,
    topVal: 10,
    topOptions: [
      { value: 10, label: 'TOP10' },
      { value: 20, label: 'TOP20' },
      { value: 30, label: 'TOP30' }
    ],
    columnVal: [],
    columnOptions: [],
    tableObj: {},
    kipId: 'kipId',
    forecastId: 'forecastId',
    analysisId: 'analysisId',
    exportTableName: '',
    tableId: '',
    exportExcelFun: queryDoctorKpiData,
    sort: true, // true：倒序 false：正序
    profttl: '医生指标分析',
    analysis: false,
    analysisPageType: '',
    analysisPageData: [],
    tempAnalysisPageData: [],
    doctorDropdownList: [],
    dropdownVal: '',
    tempList: [],
    // 分析页面 - 错误病例
    showAnalysis: true,
    analysisTableData: [],
    isLoss: true,
    doctorValueList: [],
    doctorValueListAnnex: [],
    doctorValueListAnnex2: [],
    // 悬浮框
    zbData: [],
    ycData: [],

    contrastData: [],
    deptDropdownVal: '',
    deptDropdown: [],
    contrastLoading: false,
    temVal: this.radioMode
  }),
  mounted () {
    if (Object.keys(this.$route.query).length > 0) {
      this.queryForm.feeStas = String(this.$store.getters.feeStas)
      if (this.$route.query.dipCodg) {
        this.queryForm.dipCodg = this.$route.query.dipCodg
      }
      if (this.$route.query.icdCodg) {
        this.queryForm.icdCodg = this.$route.query.icdCodg
      }
      if (this.$route.query.deptCode) {
        this.queryForm.deptCode = this.$route.query.deptCode
      }
      if (this.$route.query.code) {
        this.queryForm.doctorValue = this.$route.query.code
      }
      if (this.$route.query.isLoss) {
        this.queryForm.isLossType = Number(this.$route.query.isLoss)
        if (this.$route.query.isLoss == 1) {
          this.isLoss = true
        }
        if (this.$route.query.isLoss == 0) {
          this.isLoss = false
        }
      }
      if (this.$route.query.radioMode) {
        this.radioMode = Number(this.$route.query.radioMode)
      }
      if (this.$route.query.feeStas) {
        this.queryForm.feeStas = this.$route.query.feeStas
      }
      if (this.$route.query.begnDate && this.$route.query.expiDate) {
        this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.queryForm)
      }

      if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
        this.$refs.somForm.jumpTimeChange('in', this.$route.query, this.queryForm)
      }

      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.queryForm)
      }
    }
    this.$nextTick(() => {
      this.init()
    })
  },
  computed: {
    showTablePage () {
      return this.analysisPageType != 'errorMed' && this.showAnalysis && this.radioMode != 4
    }
  },
  methods: {
    queryDoctorKpiData,
    queryDoctorForecastData,
    queryPatientLoss,
    init () {
      this.radioChange(this.radioMode)
    },
    changeSwitch (val) {
      this.$store.commit('SET_SWITCHSTATE', val)
      this.queryForm.feeStas = this.$store.getters.feeStas
      let params = {}
      Object.assign(params, this.queryForm)
      params.username = this.$store.getters.name
      updateSwitchState(params).then((res) => {
        if (res.code == 200) {
          this.init()
        }
      })
    },
    doctorValueChange () {
      if (this.radioMode == 1) {
        this.queryDoctorData()
      } else if (this.radioMode == 2) {
        this.queryForecastData()
      } else if (this.radioMode == 3) {
        this.queryPageAnalysisData()
      }
    },
    // 查询指标数据
    queryDoctorData () {
      this.loading = true
      queryDoctorKpiData(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
        this.generateChartData()
        this.generateFixedColumns()
      })
      this.clearRouteQuery()
    },
    // 查询预测数据
    queryForecastData () {
      this.loading = true
      queryDoctorForecastData(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
        this.generateChartData()
        this.generateFixedColumns()
      })
      this.clearRouteQuery()
    },
    // 查询分析数据
    async queryPageAnalysisData (drCodg) {
      this.loading = true
      let params = this.getParams()
      // 如果是表格则生成数据
      if (!this.showAnalysis) {
        this.analysisTableData = []
        await queryAnalysisSummary(params).then(res => {
          this.analysisTableData = res.data
        })
      } else {
        this.analysisPageData = []
        // if(this.doctorDropdownList.length == 0){
        await queryDropdown(this.getParams()).then(res => {
          this.doctorDropdownList = res.data
        })
        // }
        if (drCodg) {
          this.dropdownVal = drCodg
        } else {
          // 第一次进入页面没有选择下拉选
          if (this.doctorDropdownList.length > 0 && !this.dropdownVal) {
            this.dropdownVal = this.doctorDropdownList[0].value
          } else if (this.doctorDropdownList.length > 0 && this.dropdownVal) {
            let tempList = []
            for (let i = 0; i < this.doctorDropdownList.length; i++) {
              tempList.push(this.doctorDropdownList[i].value)
            }
            if (!tempList.includes(this.dropdownVal)) {
              this.dropdownVal = this.doctorDropdownList[0].value
            }
          }
        }

        // 子页面下拉选改变传入医生编码
        if (this.dropdownVal) {
          params.drCodg = this.dropdownVal
          this.tempAnalysisPageData = []
        }
        await this.getAnalysisPageData('dis', 0, queryDiseaseLoss, params)
        // await this.getAnalysisPageData('doctor', 1, queryDoctorLoss, params)
        await this.getAnalysisPageData('med', 1, queryPatientLoss, params)

        // 错误病例
        await this.getAnalysisPageData('errorMed', 2, queryMedError, params, true)

        if (this.analysisPageData.length > 0) {
          this.tempAnalysisPageData = [...this.analysisPageData]
        }
      }
      this.loading = false

      this.clearRouteQuery()
    },
    // 获取分析数据
    async getAnalysisPageData (type, index, queryMethod, params, noPaging = false) {
      if (this.analysisPageType == type || this.tempDataIsNull(index)) {
        await queryMethod(params).then(res => {
          if ((['dis', 'doctor', 'med'].includes(type) && res.data.list && res.data.list.length > 0) ||
            (type == 'errorMed' && res.data && res.data.length > 0)) {
            this.addAnalysisData(res, index, false, noPaging)
            if (this.analysisPageType == type && !['errorMed'].includes(type)) this.total = res.data.total
          } else {
            this.analysisPageData.push(
              {
                data: [],
                total: 0,
                pageNum: 1
              }
            )
          }
        })
      } else {
        this.addAnalysisData(null, index, true)
      }
    },
    tempDataIsNull (index) {
      if (this.tempAnalysisPageData.length > 0) {
        if (this.tempAnalysisPageData[index].data != undefined) {
          return this.tempAnalysisPageData[index].data.length == 0
        } else {
          return true
        }
      }

      return this.tempAnalysisPageData.length == 0
    },
    // 添加分析数据
    addAnalysisData (res, index, useTempData, noPaging = false) {
      if (useTempData) {
        this.analysisPageData.push(this.tempAnalysisPageData[index])
      } else {
        if (noPaging) {
          let total = 0
          if (index == 2) {
            total = res.data[0].errorSummaryNum + '-' + res.data[0].compeleteErrorNum + '-' + res.data[0].logicErrorNum
          }
          // 无分页情况
          this.analysisPageData.push({
            data: res.data,
            total: total,
            pageNum: 1
          })
        } else {
          this.analysisPageData.push({
            data: res.data.list,
            total: res.data.total,
            pageNum: this.queryForm.pageNum
          })
        }
      }
    },
    // 生成图数据
    generateChartData () {
      let params = this.getParams()
      // 图数据查询为查询所有
      Object.assign(params, {
        pageNum: 1,
        pageSize: 10 ** 4
      })
      params.selectVal = this.selectVal
      if (this.radioMode == 1) {
        queryDoctorKpiData(params).then(res => {
          this.tempList = res.data.list
          this.yAxisData = []
          this.seriesData = []
          let sortData = []
          for (let i = 0; i < this.tempList.length; i++) {
            let item = this.tempList[i]
            sortData.push({
              y: (item.drName && item.drName != undefined) ? item.drName : '-',
              value: item[this.selectVal]
            })
          }

          if (this.sort) {
            sortData = sortData.sort((a, b) => a.value - b.value)
          } else {
            sortData = sortData.sort((a, b) => b.value - a.value)
          }

          for (let i = 0; i < sortData.length; i++) {
            if (i == this.topVal) {
              break
            }
            this.yAxisData.push(sortData[i].y)
            this.seriesData.push(sortData[i].value)
          }
          this.yAxisData.reverse()
          this.seriesData.reverse()
          this.initChart()
        })
      }
      if (this.radioMode == 2) {
        queryDoctorForecastData(params).then(res => {
          this.tempList = res.data.list
          this.yAxisData = []
          this.seriesData = []
          let sortData = []
          for (let i = 0; i < this.tempList.length; i++) {
            let item = this.tempList[i]
            sortData.push({
              y: (item.drName && item.drName != undefined) ? item.drName : '-',
              value: item[this.selectVal]
            })
          }

          if (this.sort) {
            sortData = sortData.sort((a, b) => a.value - b.value)
          } else {
            sortData = sortData.sort((a, b) => b.value - a.value)
          }

          for (let i = 0; i < sortData.length; i++) {
            if (i == this.topVal) {
              break
            }
            this.yAxisData.push(sortData[i].y)
            this.seriesData.push(sortData[i].value)
          }
          this.yAxisData.reverse()
          this.seriesData.reverse()
          this.initChart()
        })
      }
    },
    // 生成固定列
    generateFixedColumns () {
      this.columnOptions = []
      let children = this.$refs['dataTable'].$children[0].$children
      if (children) {
        children.forEach(item => {
          if (item.$options.propsData.label && item.$options.propsData.label != '序号' && item.$options.propsData.label != '住院医师姓名') {
            this.columnOptions.push({
              value: item.$options.propsData.prop,
              label: item.$options.propsData.label
            })
          }
        })
      }
    },
    // 初始化图
    initChart () {
      this.chartOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            interval: 0,
            rotate: 40,
            formatter: params => {
              return this.formatCost(params, 0)
            }
          },
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: this.yAxisData
        },
        series: [
          {
            name: '数值',
            type: 'bar',
            label: {
              formatter: params => {
                return this.formatCost(params.value)
              }
            },
            data: this.seriesData.map((item, index) => {
              return {
                value: item,
                label: {
                  show: true,
                  position: item > 0 ? 'right' : 'left'
                }
              }
            })
          }
        ]
      }
    },
    // 格式化
    formatCost (val, fixed = 2) {
      let prefix = ''
      let resVal = val
      if (String(val).includes('-')) {
        prefix = '-'
        val = Math.abs(val)
        resVal = val
      }
      if (Math.abs((parseFloat(val) / 10000)) > 1) {
        resVal = (parseFloat(val) / 10000).toFixed(fixed) + '/万'
      }
      return prefix + resVal
    },
    // 选择盈利还是亏损
    switchLossOrProfit (isLoss) {
      this.isLoss = isLoss
      this.queryPageAnalysisData()
    },
    // 医生下拉选择查询
    selectDoctor () {
      queryDropdown(this.getParams()).then(res => {
        this.doctorValueList = res.data
        this.doctorValueListAnnex = res.data
      })
    },
    // 选择改变
    radioChange (val) {
      if (this.temVal != val) {
        this.selectVal = ''
        this.temVal = val
      }
      if (val == 1) {
        this.queryDoctorData()
        this.exportExcelFun = queryDoctorKpiData
        this.tableId = this.kipId
        this.exportTableName = '医生指标分析'
        this.option = kpiOptions
        if (this.selectVal == '') {
          this.selectVal = 'drugRatio'
        }
        this.profttl = '医生指标分析'
        this.analysis = false
      }

      if (val == 2) {
        this.queryForecastData()
        this.exportExcelFun = queryDoctorForecastData
        this.tableId = this.forecastId
        this.exportTableName = '医生预测分析'
        this.option = forecastOptions
        if (this.selectVal == '') {
          this.selectVal = 'forecastAmountDiff'
        }
        this.profttl = '医生预测情况'
        this.analysis = false
      }

      if (val == 3) {
        this.queryPageAnalysisData()
        this.exportExcelFun = queryPatientLoss
        this.tableId = this.analysisId
        this.exportTableName = '医生分析'
        this.profttl = '医生分析'
        this.analysis = true
      }

      if (val == 4) {
        this.queryDoctorContrastData()
        this.profttl = '医生对比'
        this.analysis = false
      }
      this.selectDoctor()
    },
    // 图排序改变
    selectChange () {
      this.generateChartData()
    },
    // 点击排序
    sortClick () {
      this.sort = !this.sort
      this.generateChartData()
    },
    // 分析页面模块点击
    analysisTypeChange (item) {
      let type = item.type
      this.queryForm.pageNum = item.pageNum
      this.tempAnalysisPageData = item.data.map(i => {
        return {
          total: i.value,
          data: i.data,
          pageNum: i.pageNum
        }
      })
      this.analysisPageType = type
      if (this.analysisPageData.length > 0) {
        if (type == 'dis') {
          this.total = this.analysisPageData[0].total
        }
        if (type == 'med') {
          this.total = this.analysisPageData[1].total
        }
      }
    },
    // 切换图表
    switchChartTable (drCodg) {
      this.showAnalysis = !this.showAnalysis
      this.queryPageAnalysisData(drCodg)
    },
    // 分析页面下拉选改变
    analysisDropdownChange (val) {
      this.dropdownVal = val
      this.queryPageAnalysisData()
    },
    selectFilter (val) {
      // 判断是否为空
      if (val) {
        // 同时筛选Lable与value的值
        this.doctorValueListAnnex2 = this.doctorValueListAnnex.filter(item => item.label.includes(val) || item.value.includes(val))
      } else {
        // 赋值还原
        this.doctorValueListAnnex2 = this.doctorValueListAnnex
      }
    },
    showSuspension (item) {
      if (item) {
        if (item[0].type == 1) {
          this.zbData = item
        } else if (item[0].type == 2) {
          this.ycData = item
        }
      }
    },
    // 获取参数
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.auth = true
      // 下拉列表类型 2:医生
      params.dropdownType = '2'
      params.queryType = '2'
      // 亏损还是盈利
      params.isLoss = this.isLoss
      params.drCodg = this.queryForm.doctorValue
      return params
    },
    // 重置
    reset () {
      this.radioChange(this.selectVal)
      this.tempAnalysisPageData = []
    },
    clearRouteQuery () {
      if (this.$route.query) {
        this.$router.push({ query: {
        } }).catch(() => {})
      }
    },

    async queryDoctorContrastData () {
      this.contrastLoading = true
      let params = this.getParams()

      if (this.deptDropdown.length == 0) {
        await queryDeptDropdown(params).then(res => {
          this.deptDropdown = res.data
        })
      }
      if (this.deptDropdown.length > 0 && !this.deptDropdownVal) {
        this.deptDropdownVal = this.deptDropdown[0].value
      }
      if (this.deptDropdownVal) {
        params.deptCode = this.deptDropdownVal
      }
      params.diseaseGroupType = '1'
      queryDoctorContrastData(params).then(res => {
        this.contrastData = res.data
        this.contrastLoading = false
      })
    },
    deptChange (val) {
      this.deptDropdownVal = val
      this.queryDoctorContrastData()
    }
  },
  watch: {
    deep: true,
    doctorValueListAnnex2: {
      handler: function (val) {
        if (val) {
          this.doctorValueList = val
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content-wrapper{
  height: 100%;
  width: 100%;
  display: flex;

  &-left{
    width: 80%;
    height: 96%;
    padding-right: 10px;
    box-sizing: border-box;
    position: relative;

    &-fixed-column{
      position: absolute;
      left: 10%;
      top: -4.5%;
    }

    &-analysis{
      width: 100%;
      height: 100%;
    }
  }

  &-right{
    width: 20%;
    height: 100%;
    position: relative;

    &-top{
      height: 10%;
      width: 100%;
      position: absolute;
      top: 0%
    }

    &-icon{
      z-index: 2;
      font-size: 18px;
      width: 20px;
      height: 40px;
      cursor: pointer;
      position: absolute;
      right: 0;
      bottom: 0;
    }
  }

  &-analysis{
    width: 100%;
    height: 100%;
    position: relative;

    &-chart-table{
      position: absolute;
      top: -5px;
      right: 5px;
    }

    &-loss-profit{
      position: absolute;
      top: -2px;
      right: 35px;
    }
  }
}

/deep/ .pagination-container{
  right: 21%;
}

/deep/ .content-wrapper-right-top>.el-select{
  width: 84px;
}

.right-top {
  position: absolute;
  top: 0;
  right: 0;

  &-dropdown {
    z-index: 2;
  }

  &-icon {
    z-index: 2;
    font-size: 18px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    position: absolute;
    right: 0;
    bottom: 3px;
  }
}
.content-wrapper-left{
  height: 100%;
}
</style>
