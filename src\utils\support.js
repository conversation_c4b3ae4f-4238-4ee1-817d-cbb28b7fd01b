import Cookies from 'js-cookie'
const SupportKey = 'supportKey'
export function getSupport () {
  return Cookies.get(SupportKey)
}

export function setSupport (isSupport) {
  return Cookies.set(SupportKey, isSupport, { expires: 3 })
}

export function setCookie (key, value, expires) {
  return Cookies.set(key, value, { expires: expires })
}

export function getCookie (key) {
  return Cookies.get(key)
}
export function getUrlRelativePath (url) {
  let arrUrl = url.split('//')
  let start = arrUrl[1].indexOf('/')
  let relUrl = arrUrl[1].substring(start)
  if (relUrl.indexOf('?') !== -1) {
    relUrl = relUrl.split('?')[0]
  }
  return relUrl
}
