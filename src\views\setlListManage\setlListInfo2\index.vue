<template>
  <div class="som-wd-one-hundred">
    <div v-if="!isError" class="som-wd-one-hundred sl-container" v-loading="loading">
      <!-- 头部固定 -->
      <div class="sl-header">

        <!-- 标题 -->
        <div class="sl-title">
          <div style="display: flex;align-items: center;justify-content: center;flex-direction: column">
            <span>医疗保障结算清单</span>
            <span style="font-size: 14px;color: gray">({{ this.uploadFlag ? '未上传医保' : '已上传医保' }})</span>
          </div>

          <div class="sl-header-right">
            <el-tooltip class="item" content="标记清单完成状态，标记的作用在于HIS无法同步数据，防止您修改数据被覆盖"
                        placement="top-start"
                        data-step="2" data-intro="第二步，请标记清单完成状态">
              <el-switch
                :value="switchValue"
                :disabled="!settleListValidateState || this.isSee"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="clickLookOver()"
                active-text=""
                inactive-text="">
              </el-switch>
            </el-tooltip>
            <el-button type="primary" plain @click="noviceGuide" v-if="!switchValue">提示</el-button>
          </div>

          <div class="sl-header-lookover-warning" v-if="!this.isSee">
            <span v-if="!settleListValidateState">清单质控暂未完成，无法标记</span>
            <span v-else>清单质控已完成，可以标记</span>
          </div>
          <div class="sl-header-lookover-warning" v-else>
            <span>当前为查看状态，无法操作</span>
          </div>

          <div class="sl-header-buttons">
            <!-- 开启及为上传后不能修改 -->
            <!--          <div class="sl-header-item" v-if="uploadFlag">-->
            <div class="sl-header-item" v-if="!this.isSee">
              <el-button type="primary" @click="submit" v-if="!isUpload && !switchValue">保存</el-button>
              <el-tooltip class="item" effect="dark" content="请先取消标记后保存" placement="top" v-else-if="switchValue">
                <el-button type="primary" disabled>保存</el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="已上传医保无法再修改" placement="top" v-else>
                <el-button type="primary" disabled>保存</el-button>
              </el-tooltip>
            </div>
            <div class="sl-header-item" ref="validateButton" data-step="1" data-intro="第一步，请完成质控信息">
              <el-button type="primary" @click.stop="validate">
                质控信息
                <i :class="[ validateVisible ? 'el-icon-arrow-up' : 'el-icon-arrow-down' ]"></i>
              </el-button>
            </div>
            <div class="sl-header-item" ref="validateButton">
              <el-button type="primary" @click.stop="preGroup">
                <i :class="[ preGroupVisible ? 'el-icon-right' : 'el-icon-back' ]"></i>
                预分组
              </el-button>
            </div>
            <div class="sl-header-item">
              <el-button type="warning" icon="el-icon-refresh" @click="dataSynchronization">同步</el-button>
              <el-button type="warning" icon="el-icon-refresh" @click="pageReload">重新加载</el-button>
            </div>
          </div>
        </div>
      </div>
      <!-- 提交dialog -->
      <submit :visible="modifyVisible"
              :upload-modify-data="uploadModifyData"
              :upload-params="uploadParams"
              @success="submitSuccess"
              @change="val => this.modifyVisible = val"/>

      <!-- 校验信息 -->
      <div class="sl-validate" v-show="validateVisible" ref="validate">
        <validate :params="commonParams" :show="validateVisible"
                  @focusKeyChange="key => this.focusKeyChange([key])"
                  @showAllNull="showAllNull"/>
      </div>

      <!-- 预分组 -->
      <div class="sl-preGroup">
        <pre-group :visible="preGroupVisible"
                   ref="preGroup"
                   :form="form"
                   :dis-data="disTableData"
                   :ope-data="opeTableData"
                   :icu-data="icuTableData"

                   @closed="preGroupVisible = false"/>
      </div>

      <!-- 内容 -->
      <div class="sl-content">
        <el-form :model="form" :rules="rules" ref="form">
          <!-- 清单头信息 -->
          <div class="sl-info-content">
            <info-item v-model="headerData" @change="infoItemChange" :focus-key="focusKey"
                       @closeValidate="closeValidate" ref="infoItem1"/>
          </div>

          <!-- 清单上传字段 -->
          <div class="sl-info-wrapper">
            <div class="sl-info-header som-flex-center">清单上传字段</div>
            <div class="sl-info-content">
              <info-item v-model="slData" @change="infoItemChange" :modify-field="modifyField" :focus-key="focusKey"
                         @closeValidate="closeValidate" ref="infoItem2"/>
              <settle-list-table v-model="transfusionData"
                                 :show-operation="false"
                                 table-name="输血数据"
                                 :columns="transfusionColumns"/>
            </div>
          </div>

          <!-- 基本信息 -->
          <div class="sl-info-wrapper">
            <div class="sl-info-header som-flex-center">一、基本信息</div>
            <div class="sl-info-content">
              <info-item v-model="baseData" @change="infoItemChange" :focus-key="focusKey"
                         @closeValidate="closeValidate" ref="infoItem3"/>
            </div>
          </div>

          <!-- 门诊慢特病诊疗信息 -->
          <div class="sl-info-wrapper">
            <div class="sl-info-header som-flex-center">二、门诊慢特病诊疗信息</div>
            <div class="sl-info-content">
              <info-item v-model="ocdData" @change="infoItemChange" :focus-key="focusKey" @closeValidate="closeValidate"
                         ref="infoItem4"/>
              <settle-list-table v-model="ocdTableData"
                                 table-name="门慢门特数据"
                                 :ref="tablePrefix + '2'"
                                 :columns="ocdTableColumns"
                                 :operation-code-arr="operationCodeArr"
                                 :diagnosis-code-arr="diagnosisCodeArr"
                                 :copy-prefix="copyPrefix"
                                 :modify-field="modifyField"/>
            </div>
          </div>

          <!-- 住院诊疗信息 -->
          <div class="sl-info-wrapper">
            <div class="sl-info-header som-flex-center">三、住院诊疗信息</div>
            <div class="sl-info-content">
              <info-item v-model="inHosData" @change="infoItemChange" :focus-key="focusKey"
                         @closeValidate="closeValidate" ref="infoItem5"/>
              <!-- 西医诊断 -->
              <settle-list-table v-model="disTableData"
                                 table-name="西医诊断数据"
                                 :ref="tablePrefix + '3'"
                                 :columns="disTableColumns"
                                 :operation-code-arr="operationCodeArr"
                                 :diagnosis-code-arr="diagnosisCodeArr"
                                 :copy-prefix="copyPrefix"
                                 :modify-field="modifyField"/>
              <!-- 中医诊断 -->
              <settle-list-table v-model="disChineseTableData"
                                 table-name="中医诊断数据"
                                 :ref="tablePrefix + '6'"
                                 :columns="disChineseTableColumns"
                                 :operation-code-arr="operationCodeArr"
                                 :diagnosis-code-arr="diagnosisCodeArr"
                                 :copy-prefix="copyPrefix"
                                 :modify-field="modifyField"
                                 :show-operation="false"
              />

              <info-item v-model="disCountData" @change="infoItemChange"/>

              <!-- 手术 -->
              <settle-list-table v-model="opeTableData"
                                 table-name="手术数据"
                                 :ref="tablePrefix + '4'"
                                 :columns="opeTableColumns"
                                 :operation-code-arr="operationCodeArr"
                                 :diagnosis-code-arr="diagnosisCodeArr"
                                 :copy-prefix="copyPrefix"
                                 :modify-field="modifyField"/>
              <info-item v-model="opeCountData" @change="infoItemChange"/>
              <info-item v-model="opeData" @change="infoItemChange" ref="infoItem6"/>

              <!-- ICU -->
              <settle-list-table v-model="icuTableData"
                                 :show-operation="false"
                                 table-name="ICU数据"
                                 :ref="tablePrefix + '5'"
                                 :copy-prefix="copyPrefix"
                                 :modify-field="modifyField"
                                 :columns="icuTableColumns"/>
              <info-item v-model="icuData" @change="infoItemChange" :focus-key="focusKey" @closeValidate="closeValidate"
                         ref="infoItem7"/>
            </div>
          </div>

          <!-- 医疗收费信息 -->
          <div class="sl-info-wrapper">
            <div class="sl-info-header som-flex-center">四、医疗收费信息</div>
            <div class="sl-info-content">
              <info-item v-model="costItemData" @change="infoItemChange" :focus-key="focusKey"
                         @closeValidate="closeValidate" ref="infoItem8"/>
              <settle-list-table v-model="costItemTableData"
                                 :columns="costItemColumns"
                                 :show-spacing="false"
                                 :show-operation="false"/>
              <pay-table :fund-pay-data="fundPayData" :self-pay-data="selfPayData"/>
              <info-item v-model="endData" @change="infoItemChange" :focus-key="focusKey" @closeValidate="closeValidate"
                         ref="infoItem9"/>
            </div>
          </div>
        </el-form>

        <!-- 锁住页面信息 -->
        <temp-lock :show="showLockDialog" :lock-data="lockData" @closeDialog="() => this.showLockDialog = false"
                   @forcedEdit="forcedEdit"/>
        <!-- 底部留一部分` -->
        <div class="sl-empty"></div>
      </div>
    </div>
    <div v-if="isError && errorInfo.length > 0" class="sl-container">
      <el-empty>
        <el-button type="warning" icon="el-icon-refresh" @click="dataSynchronization">同步</el-button>
        <h1 style="color:red">当前系统未查询到数据，请联系：{{ errorInfo[0].label }}-{{ errorInfo[1].label }}</h1>
        <h2 style="color:gray">请发送<span style="color: red">病案号/住院号</span>到DIP/DRG工作群</h2>
      </el-empty>
    </div>
  </div>
</template>
<script>
import {
  getSettleListAllInfo,
  queryICDCode,
  updateSettleListLookOver,
  updateLockState,
  queryLockState,
  querySkipData, dataSynchronization,
  selectBusSettleLIstError, selectBusSettleErrorLIst
} from '@/api/medicalQuality/settleListDetail'
import {queryDoctorCodeInfo, queryNurseCodeInfo} from '@/api/dataConfig/doctorCodeConfig'
import {init as globalInit} from '@/utils/globalInit'
import InfoItem from './comps/settleListInfoItem'
import SettleListTable from './comps/settleListTable'
import PayTable from './comps/settleListPayTable'
import Submit from './comps/settleListSubmit'
import Validate from './comps/settleListValidate'
import PreGroup from './comps/settleListPreGroup'
import TempLock from './comps/settlelistTempLock'
import introJs from 'intro.js'
import 'intro.js/introjs.css'

export default {
  name: 'setlListInfo2',
  components: {
    'info-item': InfoItem,
    'settle-list-table': SettleListTable,
    'pay-table': PayTable,
    'temp-lock': TempLock,
    Submit,
    Validate,
    PreGroup
  },
  inject: ['reload'],
  data: () => ({
    costItemAmount: 0,
    isError: false,
    errorInfo: [],
    form: {},
    rules: {},
    commonParams: {
      id: '',
      k00: ''
    },
    medcasno: '',
    mdtrtId: '',
    loading: false,
    switchValue: false,
    uploadFlag: true,
    modifyVisible: false,
    validateVisible: false,
    preGroupVisible: false,
    // 聚焦key
    focusKey: '',
    // 诊断编码
    diagnosisCodeArr: [],
    // 手术编码
    operationCodeArr: [],
    // 头部信息
    headerData: [],
    // 清单上传信息
    slData: [],
    // 基本信息
    baseData: [],
    // 门诊慢特病诊疗信息
    ocdData: [],
    ocdTableData: [],
    ocdCopyTableData: [],
    ocdTableColumns: [],
    disChineseTableColumns: [],
    // 住院诊疗信息
    inHosData: [],
    opeData: [],
    disTableData: [],
    disChineseTableData: [],
    disCopyTableData: [],
    disTableColumns: [],
    opeTableData: [],
    opeCopyTableData: [],
    opeTableColumns: [],
    // 诊断代码计数
    disCountData: [],
    // 手术代码计数
    opeCountData: [],
    // ICU
    icuData: [],
    icuTableData: [],
    icuCopyTableData: [],
    icuTableColumns: [],
    // 医疗收费信息
    costItemData: [],
    costItemTableData: [],
    costItemCopyTableData: [],
    costItemColumns: [],
    costItemFixedNames: [
      '床位费', '诊察费', '检查费', '化验费', '治疗费', '手术费',
      '护理费', '卫生材料费', '西药费', '中药饮片费', '中成药费',
      '一般诊疗费', '挂号费', '其他费', '金额合计'
    ],
    // 基金支付
    fundPayData: [],
    // 输血
    transfusionData: [],
    transfusionColumns: [],
    // 个人支付
    selfPayData: [],
    // 最后数据
    endData: [],
    // 复制的字段前缀
    copyPrefix: 'som-copy',
    // 修改字段
    modifyField: 'som-modify',
    // 表格前缀
    tablePrefix: 'table',
    // 类型对应列数据
    columnsMap: {},
    // 上传修改数据
    uploadModifyData: [],
    // 上传修改数据集合
    uploadParams: {},
    operateFlag: false,
    // 医生数据
    doctorArr: [],
    // 护士数据
    nurseArr: [],
    // 医师和护士数据
    doctorAndNurseArr: [],
    // 科室数据
    deptArr: [],
    // 清单校验状态 true 校验成功 false校验失败
    settleListValidateState: false,
    // 是否查看
    isSee: false,
    // 是否现在锁住提示
    showLockDialog: false,
    // 锁住的数据
    lockData: {
      username: '',
      lockUsername: '',
      ip: '',
      lockIp: ''
    },
    isPass: false,
    isUpload: false,
    clickEventProj: null,
    beforeunloadEventProj: null

  }),
  beforeRouteLeave(to, from, next) {
    if (this.allowDeleteLock) {
      this.deleteLock()
    }
    next()
  },
  watch: {
    dict: {
      immediate: true,
      deep: true,
      handler: function (val) {
        if (val) {
          this.errorInfo = this.$somms.getDictValue('ERROR_CONTACT', 1)
        }
      }
    }
  },
  mounted() {
    this.loading = true;
    globalInit()
    let params = this.getParams()
    if (params.skip) {
      querySkipData(params).then(res => {
        this.$router.push({path: '/setlListManage/setlListInfo2', query: {k00: res.data.k00, id: res.data.id}})
        // this.reload()
      }).catch(() => {
        this.isError = true
      })
    } else {
      this.isSee = this.$route.query.see !== undefined ? (this.$route.query.see === 'true') : false
      if (this.isSee) {
        this.initQuery()
        this.initMounted()
      } else {
        // 关闭进入页面后获取当前清单锁状态
        // queryLockState({ k00: this.$route.query.k00 }).then(res => {
        //   if (res.data) {
        //     this.showLockDialog = true
        //     this.lockData = res.data
        //     this.operateFlag = true
        //   } else {
        //     if (this.$route.query.k00) {
        //       updateLockState({ lockState: 1, k00: this.$route.query.k00 }).then(res => {})
        //     }
        //     this.operateFlag = false
        //     this.initQuery()
        //     this.initMounted()
        //   }
        // })
        this.initQuery()
        this.initMounted()
      }
      this.beforeunloadEventProj = window.addEventListener('beforeunload', this.beforeunloadHandler)
    }
    // 查询清单校验情况
    selectBusSettleLIstError(this.commonParams).then(res => {
      if (Object.keys(res.data).length === 0) {
        // this.settleListValidateState = true
        this.isPass = true
      } else {
        // this.settleListValidateState = false
        this.isPass = false
      }
    }).catch(() => {
      this.loading = false
    }),
      // 查询清单校验情况
      selectBusSettleErrorLIst(this.commonParams).then(res => {
        if (true == res.data) {
          this.settleListValidateState = true
          this.isPass = true
        } else {
          this.settleListValidateState = false
          this.isPass = false
        }
      }).catch(() => {
        this.loading = false
      })
  },
  beforeDestroy() {
    window.removeEventListener('click', this.validateHandler)
    window.removeEventListener('beforeunload', this.beforeunloadHandler)
    this.clearData()
    //重置data数据
    Object.assign(this.$data, this.$options.data())
    // console.log('=========beforeDestroy=========')
  },
  computed: {
    allowDeleteLock() {
      return !this.operateFlag && !this.isSee
    },
    dict() {
      return this.$store.state.common.dictionaries
    } // 等待 15 秒
  },
  methods: {
    clearData() {
        // 清理大数组数据
        this.doctorArr = null
        this.nurseArr = null
        this.doctorAndNurseArr = null
        this.deptArr = null
        this.diagnosisCodeArr = null
        this.operationCodeArr = null

        // 清理表格数据
        this.ocdTableData = null
        this.disTableData = null
        this.opeTableData = null
        this.icuTableData = null
        this.costItemTableData = null

        // 清理拷贝数据
        this.ocdCopyTableData = null
        this.disCopyTableData = null
        this.opeCopyTableData = null
        this.icuCopyTableData = null

        // ... 清理其他大数组数据
        Object.assign(this.$data, this.$options.data())
        // 强制垃圾回收
        if (window.gc) {
          window.gc()
        }
    },
    // 新手引导点击事件
    noviceGuide() {
      this.guide()
    },
    // 新手引导步骤
    guide() {
      introJs()
        .setOption('nextLabel', ' 下一步 ')
        .setOption('prevLabel', ' 上一步 ')
        .setOption('doneLabel', ' 完成 ')
        .start()
    },
    // 强制编辑
    forcedEdit() {
      updateLockState({lockState: 0, k00: this.$route.query.k00}).then(res => {
        if (res.code === 200) {
          this.reload()
        }
      })
    },
    initMounted() {
      this.$nextTick(() => {
        this.query()
        this.initTableColumn()
        this.clickEventProj = document.addEventListener('click', this.validateHandler)
      })
    },
    validateHandler(e) {
      if (this.$refs.validate && !this.$refs.validate.contains(e.target)) {
        this.validateVisible = false
      }
    },
    beforeunloadHandler() {
      if (!this.isSee) {
        this.deleteLock()
      }
    },
    async deleteLock() {
      if (this.$route.query.k00) {
        await updateLockState({lockState: 0, k00: this.$route.query.k00}).then(res => {
        })
      }
    },
    // 校验
    validate() {
      this.validateVisible = !this.validateVisible
      selectBusSettleErrorLIst(this.commonParams).then(res => {
        if (true == res.data) {
          this.settleListValidateState = true
          this.isPass = true
        } else {
          this.settleListValidateState = false
          this.isPass = false
        }
      }).catch(() => {
        this.loading = false
      })
    },
    closeValidate(key) {
      this.$refs.form.validateField(key)
      // this.validateVisible = false
    },
    // 预分组
    preGroup() {
      this.preGroupVisible = true
    },
    // 显示所有空值
    showAllNull(params) {
      if (params.type === '1') {
        this.$refs.form.validate()
      } else {
        this.$refs.form.clearValidate()
      }
      let lastExeKeys = []
      let data = params.data
      data.forEach(item => {
        if (item.value !== '1') {
          let keys = []
          item.data.forEach(it => keys.push(it.fld + '-' + item.value))
          if (item.value === params.type) {
            lastExeKeys = keys
          } else {
            this.focusKeyChange(keys, true, true)
          }
        }
      })
      if (lastExeKeys.length > 0) {
        this.focusKeyChange(lastExeKeys, false, true)
      }
      for (let i = 1; i < 999; i++) {
        let refInfoItem = this.$refs['infoItem' + i]
        if (!refInfoItem) break
        refInfoItem.refresh()
      }
      this.validateVisible = false
    },
    // 数据同步
    selectNonGroupsList() {
      queryNonGroupsList().then(res => {
        if (res.code === 200) {
          // 检查返回的对象数据是否存在
          if (res.data && res.data.length > 0) {
            // 生成弹窗，显示所有用户信息
            const content = res.data.map(userInfo => (
              <div key={userInfo.id}>
                <p>年龄: {userInfo.age}</p>
                <p>性别: {userInfo.gender}</p>
              </div>
            ))

            this.$confirm({
              title: '用户信息',
              content: <div>{content}</div>
            })
          } else {
            // 处理没有数据的情况
            this.$message.warning('没有查询到数据')
          }
        } else {
          // 处理请求失败的情况
          this.$message.error('查询失败，请重试')
        }
      }).catch(error => {
        // 处理网络错误或其他异常
        console.error(error)
        this.$message.error('请求出现错误，请检查网络或稍后再试')
      })
    },
    // 数据同步
    async dataSynchronization() {
      // 启动 loading 效果，防止操作
      if(this.switchValue){
        this.$message.error('请取消标记后同步');
        return;
      }

      this.loading = true
      try {
        // 弹出确认框，询问用户是否确认同步
        await this.$confirm('是否确认同步？', '确认', {type: 'warning'})
        this.$message.success('正在同步数据，请您耐心稍作等待');
        // 执行同步操作
        const res = await dataSynchronization(this.getParams())
        if (res.code === 200) {
          // 同步成功，显示提示
          this.$message.success('同步成功,正在重新生成数据，请您耐心稍作等待')
          await new Promise(resolve => setTimeout(resolve, 15000));  // 等待 15 秒
        }
      } catch (error) {
        // 用户取消操作时，不做任何事情
        console.log('用户取消同步')
      } finally {

        // 刷新页面或执行其他逻辑
        this.waitForReload()
        // 无论同步成功还是失败，关闭 loading 效果
        this.loading = false
      }
    },
    async waitForReload() {
      let isReloadSuccess = false;

      // 使用递归判断 reload 是否返回 200
      while (!isReloadSuccess) {
        const res = await this.reload(); // 假设 reload() 返回一个 Promise 对象

        if (res.code === 200) {
          this.$message.success('同步成功');
          this.isLoading = false;
          isReloadSuccess = true;
        } else {
          this.$message.success('正在重新生成数据，请您耐心稍作等待');
          // 如果返回不是 200，继续等待 15 秒
          await new Promise(resolve => setTimeout(resolve, 15000));  // 等待 15 秒
        }
      }
    },
    // 重新加载
    async pageReload() {
      this.$confirm('是否确认重新加载，修改内容将不保存？', '确认', {
        type: 'warning'
      }).then(async () => {
        if (this.allowDeleteLock) {
          await this.deleteLock()
        }
        this.reload()
      }).catch(() => {
      })
    },
    // 提交
    submit() {
      let modifyBaseData = []
      let allModifyData = [
        ...this.headerData, ...this.slData,
        ...this.baseData, ...this.ocdData, ...this.inHosData,
        ...this.opeData, ...this.icuData, ...this.costItemData,
        ...this.endData
      ]

      allModifyData.map(bd => {
        let td
        if (bd instanceof Array) {
          td = bd
        } else {
          td = bd.data
        }
        td.map(item => {
          if (item.modify) {
            modifyBaseData.push({
              id: this.commonParams.id,
              key: item.key,
              value: item.value,
              hisVal: item.copyValue || '', // 确保修改前的值不为空
              name: item.label,
              type: item.type,
              dicType: item.dicType
            })
          }
        })
      })

      let tableUploadInfo = []
      if (modifyBaseData.length > 0) {
        tableUploadInfo.push({
          type: '1',
          name: 'baseInfoDto',
          upload: true,
          modify: true,
          modifyData: modifyBaseData
        })
      }
      tableUploadInfo = tableUploadInfo.concat([
        {
          tableData: this.ocdTableData,
          copyTableData: this.ocdCopyTableData,
          type: '2',
          name: 'ocdInfoDto'
        },
        {
          tableData: [...this.disTableData, ...this.disChineseTableData],
          copyTableData: this.disCopyTableData,
          type: '3',
          name: 'disInfoDto'
        },
        {
          tableData: this.opeTableData,
          copyTableData: this.opeCopyTableData,
          type: '4',
          name: 'opeInfoDto'
        },
        {
          tableData: this.icuTableData,
          copyTableData: this.icuCopyTableData,
          type: '5',
          name: 'icuInfoDto'
        }
      ])

      let tData = []
      tableUploadInfo.map(item => {
        let modifyParams
        if (item.type !== '1') {
          modifyParams = this.getModifyInfo(item.tableData, item.copyTableData, item.type)
        } else {
          modifyParams = item
        }
        if (modifyParams.upload) {
          modifyParams = this.addCommonUpdateParams(modifyParams)
          if (!modifyParams.modify) {
            modifyParams.insertData = modifyParams.listData
          }
          modifyParams.type = item.type
          tData.push({
            type: item.type,
            params: modifyParams
          })
        } else {
          this.$refs[this.tablePrefix + item.type].clearLog()
        }
      })

      let uploadParams = {}
      tableUploadInfo.map(tui => {
        for (let d of tData) {
          if (d.type === tui.type) {
            uploadParams[tui.name] = d.params
            break
          }
        }
      })

      if (Object.keys(uploadParams).length > 0) {
        this.uploadParams = {}
        this.uploadModifyData = []
        let confirmModifyData = [
          {name: '基本数据修改信息', key: 'baseInfoDto'},
          {name: '门慢门特修改信息', key: 'ocdInfoDto'},
          {name: '诊断数据修改信息', key: 'disInfoDto'},
          {name: '手术数据修改信息', key: 'opeInfoDto'},
          {name: 'ICU数据修改信息', key: 'icuInfoDto'}
        ]
        Object.keys(uploadParams).map(key => {
          this.addUploadModifyData(confirmModifyData, key, uploadParams)
        })
        this.uploadParams = uploadParams
        this.modifyVisible = true
      } else {
        this.$message({
          type: 'warning',
          message: '暂无修改数据'
        })
      }
    },
    // 提交成功
    submitSuccess() {
      setTimeout(async () => {
        await this.deleteLock()
        this.reload()
      }, 300)
    },
    // 新增上传确认信息
    addUploadModifyData(confirmModifyData, key, uploadParams) {
      for (let item of confirmModifyData) {
        if (item.key === key) {
          let tempParams = uploadParams[key]
          if (tempParams.modify) {
            this.uploadModifyData.push({
              name: item.name, data: tempParams.modifyData
            })
          } else {
            this.uploadModifyData.push({
              name: item.name, data: tempParams.modifyData, log: tempParams.log
            })
          }
          break
        }
      }
    },
    // 获取修改信息
    getModifyInfo(tableData, copyTableData, type) {
      let params = {upload: false}
      // true 表示修改 false表示删除后新增
      if (copyTableData.length !== tableData.length) {
        // 新增或删除
        params.upload = true
        params.modify = false
      } else {
        let ocdMove = false
        for (let i = 0; i < copyTableData.length; i++) {
          if (copyTableData[i].id !== tableData[i].id) {
            ocdMove = true
            break
          }
        }

        if (ocdMove) {
          // 移动
          params.upload = true
          params.modify = false
        } else {
          let modify = false
          let modifyData = this.getModifyData(tableData, type)
          if (modifyData.length > 0) {
            modify = true
          }
          if (modify) {
            // 修改
            params.upload = true
            params.modify = true
            params.modifyData = modifyData
          }
        }
      }

      if (params.upload && !params.modify) {
        params.listData = tableData
        params.log = this.$refs[this.tablePrefix + type].moveLog
        params.modifyData = this.getModifyData(tableData, type)
      }

      return params
    },
    // 获取修改数据
    getModifyData(tableData, type) {
      let modifyData = []
      tableData.map(item => {
        Object.keys(item).map(key => {
          if (key.includes(this.modifyField) && item[key] === true) {
            let replaceKey = key.replace(this.modifyField, '')
            let seq = (this.getSno(item.seq, tableData))
            modifyData.push({
              id: item.id,
              value: item[replaceKey],
              key: replaceKey,
              hisVal: item[this.copyPrefix + replaceKey] || '', // 确保修改前的值不为空
              // name: this.getName(type, replaceKey) + (type === "4" ? item.seq === 0 ? 1 :sno : parseInt(seq) + 1),
              name: this.getName(type, replaceKey) + (parseInt(seq) + 1),
              type: this.getFieldType(type, replaceKey) // 获取正确的字段类型
            })
          }
        })
      })
      return modifyData
    },
    // 设置通用修改数据参数
    addCommonUpdateParams(data) {
      return {...data, ...this.commonParams}
    },
    // 获取编号
    getSno(seq, tableData) {
      if (seq || seq === 0) {
        return seq
      }
      let res = 0
      if (tableData && tableData.length > 0) {
        for (let td of tableData) {
          if (res < td.seq) {
            res = td.seq
          }
        }
        res++
      }
      return res
    },
    // 获取名称
    getName(type, key) {
      let columns = this.columnsMap[type]
      for (let item of columns) {
        if (item.fld === key) {
          return item.label
        }
      }
    },
    // 获取字段类型
    getFieldType(type, key) {
      let columns = this.columnsMap[type]
      for (let item of columns) {
        if (item.fld === key) {
          return item.type || 'input'
        }
      }
      return 'input'
    },
    // 查询数据
    query() {
      this.loading = true
      let params = this.getParams()
      getSettleListAllInfo(params).then(res => {
        if (res.data.somHiInvyBasInfo) {
          let data = res.data.somHiInvyBasInfo
          this.medcasno = res.data.somHiInvyBasInfo.a48
          this.mdtrtId = res.data.somHiInvyBasInfo.clinicId
          this.isUpload = data.uploadFlag && data.uploadFlag === '1'
          this.commonParams.id = data.id
          this.commonParams.k00 = data.k00
          this.setForm(data)
          this.initHeaderInfo(data)
          this.initSettleListUploadInfo(data)
          this.initBaseInfo(data)
          this.initOCDInfo(data)
          this.initInHosInfo(data)
          this.initOpeInfo(data)
          this.initIcuInfo(data)
          this.initCostItemInfo(data)
          this.initEndInfo(data)
          // 生成个人支付信息
          this.generateSelfPayInfo(data)
          // 设置是否查看
          this.setDefaultInfo(data)
        }

        // 门慢门特表格
        if (res.data.busOutpatientClinicDiagnosisList) {
          this.ocdTableData = this.copyValue(res.data.busOutpatientClinicDiagnosisList)
          // 深拷贝
          this.ocdCopyTableData = JSON.parse(JSON.stringify(res.data.busOutpatientClinicDiagnosisList))
        }

        // 西医诊断信息
        if (res.data.busDiseaseDiagnosisTrimList) {
          this.disTableData = this.copyValue(res.data.busDiseaseDiagnosisTrimList)
          this.disCopyTableData = JSON.parse(JSON.stringify(res.data.busDiseaseDiagnosisTrimList))
          this.initDisCountInfo({disCount: this.disTableData.length})
        }

        // 中医诊断信息
        if (res.data.busChineseDiseaseDiagnosisTrimList) {
          this.disChineseTableData = this.copyValue(res.data.busChineseDiseaseDiagnosisTrimList)
          this.disCopyTableData = JSON.parse(JSON.stringify(res.data.busChineseDiseaseDiagnosisTrimList))
          this.initDisCountInfo({disCount: this.disTableData.length + this.disChineseTableData.length})
        }

        // 手术
        if (res.data.busOperateDiagnosisList) {
          this.opeTableData = this.copyValue(res.data.busOperateDiagnosisList)
          this.opeCopyTableData = JSON.parse(JSON.stringify(res.data.busOperateDiagnosisList))
          this.initOpeCountInfo({opeCount: this.opeTableData.length})
        }

        // ICU
        if (res.data.busIcuList) {
          this.icuTableData = this.copyValue(res.data.busIcuList)
          this.icuCopyTableData = JSON.parse(JSON.stringify(res.data.busIcuList))
        }

        // 住院收费信息
        if (res.data.busMedicalCostList) {
          this.costItemTableData = this.generateCostItemTableData(res.data.busMedicalCostList)
          // this.costItemCopyTableData = JSON.parse(JSON.stringify(tempData))
        }

        // 基金支付
        if (res.data.busFundPayList) {
          this.fundPayData = res.data.busFundPayList
        }

        // 输血
        if (res.data.busTransfusionList) {
          this.transfusionData = res.data.busTransfusionList
        }

        this.loading = false
        this.$refs.preGroup.executePreGroup()

        // 查询清单校验情况
        selectBusSettleLIstError(this.commonParams).then(res => {
          if (Object.keys(res.data).length === 0) {
            // this.settleListValidateState = true
            this.isPass = true
          } else {
            // this.settleListValidateState = false
            this.isPass = false
          }
        })

        selectBusSettleErrorLIst(this.commonParams).then(res => {
          if (true == res.data) {
            this.settleListValidateState = true
            this.isPass = true
          } else {
            this.settleListValidateState = false
            this.isPass = false
          }
        })
      }).catch(() => {
        this.loading = false
      })
    },
    updateScriptStas(flag) {
      this.settleListValidateState = flag
    },
    // 初始查询
    initQuery() {
      // ICD编码查询
      queryICDCode({ver: '10'}).then(res => {
        //避免出现响应式
        this.diagnosisCodeArr = Object.freeze(res.data.icd10)
        this.operationCodeArr = Object.freeze(res.data.icd9)
      })

      // this.doctorArr = data.list.map(item => ({
      //   code: item.operDrCode,
      //   name: item.name + '(医)',
      //   id: item.id
      // }))

      // 查询医生
      queryDoctorCodeInfo({pageSize: 1, pageNum: 1}).then(res => {
        queryDoctorCodeInfo({pageSize: res.data.total, pageNum: 1}).then(tres => {
          tres.data.list.forEach(item => {
            item.code = item.operDrCode
            item.name = item.name + '(医)'
            this.doctorArr.push(item)
            this.doctorAndNurseArr.push(item)
          })
        })
      })

      // 护士编码
      queryNurseCodeInfo({pageSize: 1, pageNum: 1}).then(res => {
        queryNurseCodeInfo({pageSize: res.data.total, pageNum: 1}).then(tres => {
          tres.data.list.forEach(item => {
            item.code = item.empno
            item.name = item.name + '(护)'
            this.nurseArr.push(item)
            this.doctorAndNurseArr.push(item)
          })
        })
      })

      // 获取科室信息
      this.$store.getters.getAllDepartments.forEach(dept => {
        let {code, name} = dept
        this.deptArr.push({code, name, id: code})
      })
      // let empty = {id: -1, code: '0000', name: '医生或者护士' }
      // this.doctorArr.push(empty)
      // this.nurseArr.push(empty)
      // this.doctorAndNurseArr.push(empty)
      // this.deptArr.push(empty)
    },
    // 初始化表格列
    initTableColumn() {
      // 门慢门特
      this.ocdTableColumns = [
        {
          label: '诊断名称',
          fld: 'diagnosisName',
          icdType: 'dis',
          codeOrName: 'name',
          linkageField: 'diagnosisCode',
          type: 'select',
          inputSelectType: 'icd-select'
        },
        {
          label: '诊断代码',
          fld: 'diagnosisCode',
          icdType: 'dis',
          codeOrName: 'code',
          linkageField: 'diagnosisName',
          type: 'select',
          inputSelectType: 'icd-select'
        },
        {
          label: '手术及操作名称',
          fld: 'operationName',
          icdType: 'ope',
          codeOrName: 'name',
          linkageField: 'operationCode',
          type: 'select',
          inputSelectType: 'icd-select'
        },
        {
          label: '手术及操作代码',
          fld: 'operationCode',
          icdType: 'ope',
          codeOrName: 'code',
          linkageField: 'operationName',
          type: 'select',
          inputSelectType: 'icd-select'
        }
      ]
      // 西医诊断
      this.disTableColumns = [
        // 西医
        {
          label: '出院西医诊断',
          fld: 'c07n1', //sql中的查询dscg_diag_name AS c07n1
          icdType: 'dis', //出院
          codeOrName: 'name', //此处显示名称
          linkageField: 'c06c1', //diag_codg
          type: 'select',
          inputSelectType: 'icd-select'
        },
        {
          label: '西医疾病代码',
          fld: 'c06c1',
          icdType: 'dis',
          codeOrName: 'code',
          linkageField: 'c07n1',
          type: 'select',
          inputSelectType: 'icd-select'
        },
        {
          label: '入院病情',
          fld: 'c08c1',
          type: 'dictSelect',
          dicType: 'RYBQ',
          dictSelectType: 2
        },
        {
          label: '是否为MCC/CC',
          fld: 'ccName',
          icdType: 'dis',
          codeOrName: 'name',
          linkageField: 'c06c1',
          type: 'disInput'
        }
          // inputSelectType: 'icd-select'
        ,
        {
          label: '是否作为分组诊断',
          fld: 'diaNoCode',
          icdType: 'dis',
          codeOrName: 'name',
          linkageField: 'c06c1',
          type: 'disInput'
          // inputSelectType: 'icd-select'
        }
      ]

      // 中医诊断
      this.disChineseTableColumns = [
        // 中医
        {
          label: '出院中医诊断',
          fld: 'c07n2',
          icdType: 'dis',
          codeOrName: 'name',
          linkageField: 'c06c2',
          type: 'select',
          inputSelectType: 'icd-select'
        },
        {
          label: '中医疾病代码',
          fld: 'c06c2',
          icdType: 'dis',
          codeOrName: 'code',
          linkageField: 'c07n2',
          type: 'select',
          inputSelectType: 'icd-select'
        },
        {
          label: '入院病情',
          fld: 'c08c2',
          type: 'dictSelect',
          dicType: 'RYBQ',
          dictSelectType: 2
        }
      ]

      // 手术
      this.opeTableColumns = [
        {
          label: '手术及操作名称',
          fld: 'c36n',
          icdType: 'ope',
          codeOrName: 'name',
          linkageField: 'c35c',
          width: '200',
          type: 'select',
          inputSelectType: 'icd-select'
        },
        {
          label: '手术及操作代码',
          fld: 'c35c',
          icdType: 'ope',
          codeOrName: 'code',
          linkageField: 'c36n',
          width: '200',
          type: 'select',
          inputSelectType: 'icd-select'
        },
        {
          label: '手术级别',
          fld: 'oprnLv',
          icdType: 'ope',
          codeOrName: 'code',
          linkageField: 'c36n',
          width: '200',
          type: 'disInput'
          // inputSelectType: 'icd-select'
        },
        {
          label: '是否作为分组手术',
          fld: 'noGroupCode',
          icdType: 'ope',
          codeOrName: 'code',
          linkageField: 'c36n',
          width: '200',
          type: 'disInput'
          // inputSelectType: 'icd-select'
        },
        {
          label: '手术及操作开始时间',
          fld: 'oprnOprtBegntime',
          width: '200',
          type: 'datetime'
        },
        {
          label: '手术及操作结束时间',
          fld: 'oprnOprtEndtime',
          width: '200',
          type: 'datetime'
        },
        {
          label: '麻醉方式',
          fld: 'c43',
          width: '150',
          type: 'dictSelect',
          dicType: 'MZFS',
          dictSelectType: 2
        },
        {
          label: '麻醉开始时间',
          fld: 'anstBegntime',
          width: '200',
          type: 'datetime'
        },
        {
          label: '麻醉结束时间',
          fld: 'anstEndtime',
          width: '200',
          type: 'datetime'
        },
        {
          label: '术者医师姓名',
          fld: 'oprn_oprt_oper_name',
          type: 'select',
          labelOrValue: 'label',
          linkageField: 'c39c',
          width: '150',
          replace: true,
          keyProps: {key: 'id', label: 'name', value: 'code'},
          actualKeyProps: {label: 'name', value: 'name'},
          selectDataField: 'doctorAndNurseArr',
          inputSelectType: 'select'
        },
        {
          label: '术者医师代码',
          fld: 'c39c',
          type: 'select',
          labelOrValue: 'value',
          linkageField: 'oprn_oprt_oper_name',
          width: '150',
          replace: true,
          keyProps: {key: 'id', label: 'name', value: 'code'},
          actualKeyProps: {label: 'code', value: 'code'},
          selectDataField: 'doctorAndNurseArr',
          inputSelectType: 'select'
        },
        {
          label: '麻醉医师姓名',
          fld: 'oprn_oprt_anst_dr_name',
          type: 'select',
          labelOrValue: 'label',
          linkageField: 'oprn_oprt_anst_dr_code',
          width: '150',
          replace: true,
          keyProps: {key: 'id', label: 'name', value: 'code'},
          actualKeyProps: {label: 'name', value: 'name'},
          selectDataField: 'doctorAndNurseArr',
          inputSelectType: 'select'
        },
        {
          label: '麻醉医师代码',
          fld: 'oprn_oprt_anst_dr_code',
          type: 'select',
          labelOrValue: 'value',
          linkageField: 'oprn_oprt_anst_dr_name',
          width: '150',
          replace: true,
          keyProps: {key: 'id', label: 'name', value: 'code'},
          actualKeyProps: {label: 'code', value: 'code'},
          selectDataField: 'doctorAndNurseArr',
          inputSelectType: 'select'
        }
      ]
      // ICU
      this.icuTableColumns = [
        {
          label: '重症监护病房类型*(CCU, NICU, EICU, SICU, PICU,RICU,其他)',
          fld: 'scsCutdWardType',
          type: 'dictSelect',
          dicType: 'ZZJHBFLX',
          dictSelectType: 2

        },
        {
          label: '进重症监护室时间*(_年_月_日_时_分)',
          fld: 'scsCutdInpoolTime',
          type: 'input'
        },
        {
          label: '出重症监护室时间(_年_月_日_时_分)',
          fld: 'scsCutdExitTime',
          type: 'input'
        },
        {
          label: '合计时长(?/?/?)格式:[天/时/分]',
          fld: 'scsCutdSumDura',
          type: 'input'
        }
      ]
      // 收费项
      this.costItemColumns = [
        {
          label: '项目名称',
          fld: 'medChrgItemname',
          type: 'normal'
        },
        {
          label: '金额',
          fld: 'amt',
          type: 'normal'
        },
        {
          label: '甲类',
          fld: 'claa',
          type: 'normal'
        },
        {
          label: '乙类',
          fld: 'clab',
          type: 'normal'
        },
        {
          label: '自费',
          fld: 'ownpay',
          type: 'normal'
        },
        {
          label: '其他',
          fld: 'oth',
          type: 'normal'
        }
      ]
      // 输血
      this.transfusionColumns = [
        {
          label: '输血品种',
          fld: 'bldCat',
          type: 'normal'
        },
        {
          label: '输血量',
          fld: 'bldAmt',
          type: 'normal'
        },
        {
          label: '输血计量单位',
          fld: 'bldUnt',
          type: 'normal'
        }
      ]
      this.columnsMap['2'] = this.ocdTableColumns
      this.columnsMap['3'] = this.disTableColumns
      this.columnsMap['4'] = this.opeTableColumns
      this.columnsMap['5'] = this.icuTableColumns
    },
    // 初始化form
    setForm(data) {
      Object.keys(data).map(key => {
        this.$set(this.form, key, data[key])
      })
    },
    // 初始化头部信息
    initHeaderInfo(data) {
      let common = {width: 0.6, allWidth: '33%'}
      this.headerData = [
        {
          align: 'right',
          data: [
            {label: '清单流水号', key: 'a58', ...common}
          ]
        },
        [
          {label: '定点医疗机构名称', key: 'a02', ...common},
          {label: '定点医疗机构代码', key: 'a01', ...common},
          {label: '医保结算等级', key: 'a50', type: 'select', dicType: 'YBJSDJ', ...common}
        ],
        [
          {label: '医保编号', key: 'a51', ...common},
          {label: '病案号', key: 'a48', ...common},
          {label: '申报时间', key: 'a52', ...common}
        ]
      ]
      this.initInputValue(data, this.headerData, this.form, this.rules)
    },
    // 初始化清单上传字段
    initSettleListUploadInfo(data) {
      let common = {
        allWidth: '33%',
        width: 0.6
      }
      this.slData = [
        [
          {label: '人员编号', key: 'psnNo', ...common},
          {label: '就诊ID', key: 'clinicId', ...common},
          {label: '结算ID', key: 'settlementId', ...common}
        ],
        [
          {label: '多新生儿入院体重', key: 'mulNwbAdmWt'},
          {label: '多新生儿出生体重', key: 'mulNwbBirWt'},
          {
            label: '责任护士代码',
            key: 'b26c',
            type: 'commonSelect',
            labelOrValue: 'value',
            replace: true,
            keyProps: {key: 'id', label: 'name', value: 'code'},
            actualKeyProps: {label: 'code', value: 'code'},
            selectDataField: 'nurseArr',
            width: 0.4
          },
          {
            label: '责任护士姓名',
            key: 'b26n',
            type: 'commonSelect',
            labelOrValue: 'label',
            replace: true,
            keyProps: {key: 'id', label: 'name', value: 'code'},
            actualKeyProps: {label: 'name', value: 'name'},
            selectDataField: 'nurseArr',
            width: 0.4
          },

          {label: '状态分类', key: 'stasType', type: 'select', dicType: 'ZTFL'}
        ]
      ]
      this.initInputValue(data, this.slData, this.form, this.rules)
    },
    // 初始化基本信息
    initBaseInfo(data) {
      this.baseData = [
        [
          {label: '姓名', key: 'a11'},
          {label: '性别', key: 'a12c', width: 0.2, type: 'select', dicType: 'SEX'},
          {label: '出生日期', key: 'a13', width: 0.6},
          {label: '年龄', key: 'a14'},
          {label: '国籍', key: 'a15c', type: 'select', dicType: 'GJ', width: 0.2}
        ],
        [
          {label: '不足一周岁年龄', key: 'a16'},
          {label: '民族', key: 'a19c', type: 'select', dicType: 'MZ', width: 0.2},
          {label: '患者证件类型', key: 'a53', type: 'select', dicType: 'HZZJLB'},
          {label: '患者证件号码', key: 'a20', width: 0.6, allWidth: '30%'}
        ],
        [
          {label: '职业', key: 'a38c', type: 'select', dicType: 'ZY'},
          {label: '现住址', key: 'a26', width: 0.6, allWidth: '30%'}
        ],
        [
          {label: '工作单位名称', key: 'a29n', width: 0.6, allWidth: '30%'},
          {label: '工作单位地址', key: 'a29', width: 0.6, allWidth: '30%'},
          {label: '单位电话', key: 'a30'},
          {label: '邮编', key: 'a31c'}
        ],
        [
          {label: '联系人姓名', key: 'a32'},
          {label: '关系', key: 'a33c', type: 'select', dicType: 'JTGX'},
          {label: '地址', key: 'a34', width: 0.6, allWidth: '30%'},
          {label: '电话', key: 'a35'}
        ],
        [
          {label: '医保类型', key: 'a54', type: 'select', dicType: 'YBLX'},
          {label: '特殊人员类型', key: 'a55', type: 'select', dicType: 'TSRYLX'},
          {label: '参保地', key: 'a56', width: 0.6, allWidth: '30%'}
        ],
        [
          {label: '新生儿入院类型', key: 'a57', type: 'select', dicType: 'XSELX'},
          {label: '新生儿出生体重', key: 'a18'},
          {label: '新生儿入院体重', key: 'a17'}
        ]
      ]
      this.initInputValue(data, this.baseData, this.form, this.rules)
    },
    // 初始化门慢门特信息
    initOCDInfo(data) {
      this.ocdData = [
        {
          align: 'space-between',
          data: [
            {label: '诊断科别', key: 'mtmmImpDeptName'},
            {label: '就诊日期', key: 'mtmmInpDate'}
          ]
        }
      ]
      this.initInputValue(data, this.ocdData, this.form, this.rules)
    },
    // 初始化住院诊疗消息
    initInHosInfo(data) {
      this.inHosData = [
        {
          showDivider: true,
          data: [
            {label: '住院医疗类型', key: 'b38', type: 'select', dicType: 'ZYYLLX'}
          ]
        },
        {
          showDivider: true,
          data: [
            {label: '入院途径', key: 'b11c', type: 'select', dicType: 'RYTJ'}
          ]
        },
        {
          showDivider: true,
          data: [
            {label: '治疗类别', key: 'b39', type: 'select', dicType: 'ZLLB'}
          ]
        },
        {
          showDivider: true,
          data: [
            {label: '入院时间', key: 'b12'},
            // { label: '入院科别', key: 'b13c' },
            // { label: '转科科别', key: 'b21c' },
            {
              label: '入院科别',
              key: 'b13c',
              type: 'commonSelect',
              labelOrValue: 'value',
              replace: false,
              keyProps: {key: 'id', label: 'name', value: 'code'},
              actualKeyProps: {label: 'name', value: 'code'},
              selectDataField: 'deptArr'
            },
            {
              label: '转科科别',
              key: 'b21c',
              type: 'commonSelect',
              labelOrValue: 'value',
              replace: false,
              keyProps: {key: 'id', label: 'name', value: 'code'},
              actualKeyProps: {label: 'name', value: 'code'},
              selectDataField: 'deptArr'
            }
          ]
        },
        {
          showDivider: true,
          data: [
            {label: '出院时间', key: 'b15'},
            // { label: '出院科别', key: 'b16c' },
            {
              label: '出院科别',
              key: 'b16c',
              type: 'commonSelect',
              labelOrValue: 'value',
              replace: false,
              keyProps: {key: 'id', label: 'name', value: 'code'},
              actualKeyProps: {label: 'name', value: 'code'},
              selectDataField: 'deptArr'
            },
            {label: '实际住院天数', key: 'b20'}
          ]
        },
        [
          {label: '门（急）诊诊断（西医诊断）', key: 'c02n', allWidth: '33%'},
          {label: '诊断代码', key: 'c01c'}
        ],
        {
          showDivider: true,
          data: [
            {label: '门（急）诊诊断（中医诊断）', key: 'c36n', allWidth: '33%'},
            {label: '诊断代码', key: 'c35c'}
          ]
        }
      ]
      this.initInputValue(data, this.inHosData, this.form, this.rules)
    },
    // 初始化手术信息
    initOpeInfo(data) {
      let common = {
        allWidth: '8%',
        width: 0.6
      }
      this.opeData = [
        [
          {label: '颅脑损伤患者昏迷时间：入院前', key: 'c28', suffixText: '天', allWidth: '30%'},
          {label: '', key: 'c29', suffixText: '小时', ...common},
          {label: '', key: 'c30', suffixText: '分钟', ...common}
        ],
        {
          showDivider: true,
          data: [
            {label: '颅脑损伤患者昏迷时间：入院后', key: 'c31', suffixText: '天', allWidth: '30%'},
            {label: '', key: 'c32', suffixText: '小时', ...common},
            {label: '', key: 'c33', suffixText: '分钟', ...common}
          ]
        },
        {
          showDivider: true,
          data: [
            {label: '呼吸机使用时间(天)', key: 'c42', suffixText: '天', allWidth: '20%'},
            {label: '呼吸机使用时间(小时)', key: 'c43', suffixText: '小时', allWidth: '20%'},
            {label: '呼吸机使用时间(分钟)', key: 'c44', suffixText: '分钟', allWidth: '20%'}
          ]
        }
      ]
      this.initInputValue(data, this.opeData, this.form, this.rules)
    },
    // 初始化诊断计数信息
    initDisCountInfo(data) {
      this.disCountData = [
        [
          {label: '诊断代码计数', key: 'disCount', required: false, disabled: true}
        ]
      ]
      this.initInputValue(data, this.disCountData, this.form, this.rules)
    },
    // 初始化手术计数信息
    initOpeCountInfo(data) {
      this.opeCountData = [
        {
          showDivider: true,
          data: [
            {label: '手术代码计数', key: 'opeCount', required: false, disabled: true}
          ]
        }
      ]
      this.initInputValue(data, this.opeCountData, this.form, this.rules)
    },
    // 初始化ICU信息
    initIcuInfo(data) {
      this.icuData = [
        {
          showDivider: true,
          data: [
            {label: '输血品种', key: 'c45', type: 'select', dicType: 'SXPZ'},
            {label: '输血量', key: 'c46'},
            {label: '输血计量单位', key: 'c47'}
          ]
        },
        {
          showDivider: true,
          data: [
            {label: '特级护理天数', key: 'b44'},
            {label: '一级护理天数', key: 'b45'},
            {label: '二级护理天数', key: 'b46'},
            {label: '三级护理天数', key: 'b47'}
          ]
        },
        {
          showDivider: true,
          data: [
            {label: '离院方式', key: 'b34c', type: 'select', dicType: 'LYFS'},
            {label: '转院拟接受机构名称', key: 'b49'},
            {label: '转院拟接受机构代码', key: 'b48'}
          ]
        },
        {
          showDivider: true,
          data: [
            {
              label: '是否有出院31日内再住院计划',
              key: 'b36c',
              type: 'select',
              dicType: 'ZZYJH',
              allWidth: '30%'
            },
            {label: '目的', key: 'b37'}
          ]
        },
        {
          showDivider: true,
          data: [
            {
              label: '主诊医师姓名',
              key: 'b52n',
              type: 'commonSelect',
              labelOrValue: 'label',
              replace: true,
              keyProps: {key: 'id', label: 'name', value: 'code'},
              actualKeyProps: {label: 'name', value: 'name'},
              selectDataField: 'doctorArr',
              width: 0.4
            },
            {
              label: '主诊医师代码',
              key: 'b51c',
              type: 'commonSelect',
              labelOrValue: 'value',
              replace: true,
              keyProps: {key: 'id', label: 'name', value: 'code'},
              actualKeyProps: {label: 'code', value: 'code'},
              selectDataField: 'doctorArr',
              width: 0.4
            }
          ]
        }
      ]
      this.initInputValue(data, this.icuData, this.form, this.rules)
    },
    // 初始化住院收费信息
    initCostItemInfo(data) {
      let common = {
        allWidth: '33%'
      }
      this.costItemData = [
        [
          {label: '业务流水号', key: 'd35', ...common},
          {label: '票据代码', key: 'd38', ...common},
          {label: '票据号码', key: 'd39', ...common}
        ],
        [
          {label: '结算开始时间', key: 'd36', ...common},
          {label: '结算结束时间', key: 'd37', ...common}
        ]
      ]
      this.initInputValue(data, this.costItemData, this.form, this.rules)
    },
    // 初始化最后的数据
    initEndInfo(data) {
      this.endData = [
        {
          showDivider: true,
          data: [
            {
              label: '医保支付方式',
              key: 'd58',
              type: 'select',
              dicType: 'YBZFFS',
              allWidth: '80%',
              width: 0.7,
              suffixText: '1.按项目2.单病种3.按病种分值4.疾病诊断相关分组(DRG) 5.按床日6.按人头……'
            }
          ]
        },
        [
          {label: '医疗机构填报部门', key: 'd59'},
          {label: '医保机构', key: 'medInsOrgan'}
        ],
        [
          {label: '医疗机构填报人', key: 'd60'},
          {label: '医保机构经办人', key: 'medInsOrganOperator'}
        ]
      ]
      this.initInputValue(data, this.endData, this.form, this.rules)
    },
    // 初始化值
    initInputValue(res, data, form, rules) {
      data.map(bd => {
        if (bd instanceof Array) {
          this.setValue(res, bd, form, rules)
        } else {
          this.setValue(res, bd.data, form, rules)
        }
      })
    },
    // 设置值
    setValue(res, bd, form, rules) {
      bd.map(item => {
        let value = this.getResValue(res, item.key)
        item.modify = false
        item.value = value
        item.copyValue = value
        item.error = ''
        // 设置默认值 begin
        if (!item.allWidth) {
          item.allWidth = '20%'
        }
        if (!item.type) {
          item.type = 'input'
        } else if (item.type === 'select' && !item.dictSelectType) {
          item.dictSelectType = 2
        }
        if (!item.suffixText) {
          item.suffixText = ''
        }
        if (!item.width) {
          item.width = 0.4
        }
        if (item.required === undefined) {
          item.required = true
        }
        if (item.disabled === undefined) {
          item.disabled = false
        }
        // 设置默认值 end
        this.$set(form, item.key, value)
        this.$set(rules, item.key, [
          {required: item.required, message: '请输入' + item.label, trigger: 'blur'}
        ])
      })
    },
    // 获取res返回值
    getResValue(res, key) {
      if (res && key) {
        return res[key]
      }
      return ''
    },
    // 子组件改变值设置form
    infoItemChange(obj) {
      this.$set(this.form, obj.key, obj.value)
    },
    // 复制数据 - 优化性能，避免直接修改原数据
    copyValue(data) {
      return data.map(td => {
        const copiedItem = {...td}
        Object.keys(td).forEach(key => {
          copiedItem[this.copyPrefix + key] = td[key]
          copiedItem[key + this.modifyField] = false
        })
        return copiedItem
      })
    },
    // 生成费用项表格数据
    generateCostItemTableData(data) {
      let tempData = []
      let amount = [
        {key: 'costItemAmount', value: 0},
        {key: 'costItemAAmount', value: 0},
        {key: 'costItemBAmount', value: 0},
        {key: 'costItemSelfpayAmount', value: 0},
        {key: 'costItemOtherAmount', value: 0}
      ]

      for (let name of this.costItemFixedNames) {
        let exists = true
        for (let td of data) {
          if (td.medChrgItemname === name) {
            tempData.push(td)
            exists = false
            break
          }
        }

        if (exists) {
          tempData.push(this.addAmount(amount, name))
        }
      }
      // this.form["costItemAmount"]  = amount.find(item => item.key === 'costItemAmount').value
      this.form['costItemAmount'] = tempData[tempData.length - 1].amt
      return tempData
    },
    addAmount(amount, medChrgItemname) {
      let tempObj = {medChrgItemname: medChrgItemname}
      for (let i = 0; i < amount.length; i++) {
        tempObj[amount[i].key] = amount[i].value
      }
      return tempObj
    },
    // 生成个人支付信息
    generateSelfPayInfo(data) {
      this.selfPayData = [
        {label: '个人自付', value: data.d54},
        {label: '个人自费', value: data.d55},
        {label: '个人账户支付', value: data.d56},
        {label: '个人现金支付', value: data.d57}
      ]
    },
    // 是否查看
    setDefaultInfo(data) {
      // 是否查看
      if (data.lookOver === '1') {
        this.switchValue = true
      } else {
        this.switchValue = false
      }
      if (data.uploadFlag === '1') {
        this.uploadFlag = false
      } else {
        this.uploadFlag = true
      }
    },
    // 点击查看
    clickLookOver() {
      if (this.isPass) {
        let msg = this.switchValue ? '是否取消标记？' : '是否标记当前病案已经完成，标识完成后HIS将不能同步数据？'
        let resMsg = this.switchValue ? '取消成功' : '标记成功'
        this.$confirm(msg, '提示', {
          type: 'warning'
        }).then(() => {
          let params = this.getParams()
          params.lookOver = this.switchValue ? '0' : '1'
          params.b16c = this.form.b16c
          params.b15 = this.form.b15
          updateSettleListLookOver(params).then(res => {
            if (res.code == 200) {
              this.switchValue = !this.switchValue
              this.$message({message: resMsg, type: 'success'})
            }
          })
        })
      }
    },
    // 点击子组件校验错误
    focusKeyChange(keys, isNull = false, batch = false) {
      keys.forEach(key => {
        let splitKey = key.split('-')
        key = splitKey[0]
        let type = splitKey[1]
        if (type !== '1') {
          let error = this.$somms.getDictValueByType(type, 'QDJYCWLX', '2')
          let formItems = [
            ...this.headerData, ...this.slData,
            ...this.baseData, ...this.ocdData, ...this.inHosData,
            ...this.opeData, ...this.icuData, ...this.costItemData,
            ...this.endData
          ]
          formItems.forEach(item => {
            let tempData
            if (item instanceof Array) {
              tempData = item
            } else {
              tempData = item.data
            }
            tempData.forEach(it => {
              if (it.key === key) {
                it.error = isNull ? '' : it.label + error
              }
            })
          })
        }
        if (!batch) {
          this.focusKey = key
        }
      })
    },
    // 获取参数
    getParams() {
      let params = {}
      if (this.$route.query.id && this.$route.query.k00) {
        params = {
          id: this.$route.query.id,
          k00: this.$route.query.k00,
          medcasno: this.medcasno,
          mdtrtId: this.mdtrtId,
          skip: false
        }
      } else {
        params = {
          k00: this.$route.query.id,
          id: '',
          skip: true
        }
      }
      return params
    }
  }
}
</script>
<style scoped lang="scss">
.sl-container {
  padding: 1%;
  position: relative;
  overflow: hidden;
}

.sl-header {
  height: 10%;
  width: 100%;
  position: relative;

  &-buttons {
    position: absolute;
    right: 0;
    bottom: 1rem;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
  }

  &-reload {
    position: absolute;
    right: 0;
    top: 0;
  }

  &-item {
    margin: 0 0.5rem;
  }

  &-right {
    position: absolute;
    left: 0;
    bottom: 1rem;
  }

  &-lookover-warning {
    position: absolute;
    bottom: 0;
    left: 0;
    font-size: 12px;
    width: 20rem
  }
}

.sl-info-wrapper {
  width: 100%;
}

.sl-title {
  width: 100%;
  height: 4rem;
  font-size: var(--biggerSmallTitleSize);
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sl-empty {
  width: 100%;
  height: 40%;
}

.sl-validate {
  position: absolute;
  width: 40%;
  height: 80%;
  z-index: 3;
  right: 1%;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
  border: 1px solid #ebeef5;
}

.sl-info-header {
  height: 2rem;
  width: 100%;
  margin-bottom: 1rem;
  background: rgb(176, 206, 232);
  color: black;
  font-weight: 600;
  font-size: 18px;
}

.sl-info-content {
  padding: 0 1%;
  width: 100%;
}

.sl-content {
  width: 100%;
  height: 90%;
  border: 2px solid #8c939d;
  overflow-x: hidden;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

.sl-content::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
</style>
