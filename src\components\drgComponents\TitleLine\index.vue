<template>
  <div class="drg-title-line-warp" :style="{...styleWarp, ...wrapStyle}">
    <div class="drg-title-line" :style="styleInner">
      <div style="display: inline-block;margin-left: 0.4rem">
        <span class="title">{{titleText}}</span>
      </div>
      <div style="display: inline-block" class="util">
        <slot name="rightSide"></slot>
      </div>
      <div class="titleRight">
        <slot name="titleRight"></slot>
      </div>
    </div>
  </div>
</template>
<script>
// 自定义标题行组件
export default {
  name: 'titleLine',
  props: ['title', 'fontSize', 'text', 'align', 'wrapStyle'],
  data () {
    let styleInner = this.makeStyleInner(this.fontSize)
    let styleWarp = this.makeStyleWarp(this.fontSize)
    let titleText = '标题'
    if (typeof this.align != 'undefined') {
      styleInner += styleInner + 'text-align:' + this.align + ';'
    }
    if (typeof this.profttl != 'undefined') {
      titleText = this.profttl
    }
    if (typeof this.text != 'undefined') {
      titleText = this.text
    }
    if (typeof this.title != 'undefined') {
      titleText = this.title
    }
    return {
      styleWarp: styleWarp,
      styleInner: styleInner,
      titleText: titleText
    }
  },
  methods: {
    makeStyleWarp (val) {
      if (typeof val != 'undefined' && val != null && val != '') {
        return 'height:' + this.fontSize + ';line-height:' + this.fontSize + ';'
      } else {
        return ''
      }
    },
    makeStyleInner (val) {
      if (typeof val != 'undefined' && val != null && val != '') {
        return 'font-size:' + this.fontSize + ';'
      } else {
        return ''
      }
    }
  },
  watch: {
    profttl (val) {
      this.titleText = val
    },
    text (val) {
      this.titleText = val
    },
    fontSize (val) {
      this.styleWarp = this.makeStyleWarp(val)
      this.styleInner = this.makeStyleInner(val)
    }
  }
}
</script>
<style scoped>
  .drg-title-line-warp {
    height: 1.1rem;
    line-height: 1.1rem;
    padding-bottom: var(--titleLine);
    width: 100%;
    position: relative;
  }
  .drg-title-line{
    height: 1.1rem;
    line-height: 1.1rem;
    display: inline-block;
    width: 100%;
    color: black;
    vertical-align: middle;
    margin: 0;
    padding: 0;
  }
  .drg-title-line::before{
    content: "";
    display: inline-block;
    width: 4px;
    background-color: #1b65b9;
    position: relative;
    height: inherit;
    /*left: -0.4rem;*/
    /*top: -0.1em;*/
    vertical-align: middle;
  }
  .title {
    font-weight: 400;
    font-size: 12px;
  }

  .util {
    z-index: 2;
    position: absolute;
    right: 0;
    top: -0.25rem;
  }

  .titleRight{
    position: absolute;
    left: 10%;
    top: -0.25rem;
  }
</style>
