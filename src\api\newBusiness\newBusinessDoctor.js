import request from '@/utils/request'

/**
 * 查询医生指标数据
 * @param params
 * @returns {*}
 */
export function queryDoctorKpiData (params) {
  return request({
    url: '/newDipBusinessDoctorAnalysisController/queryDoctorKpiData',
    method: 'post',
    params: params
  })
}

/**
 * 查询医生预测数据
 * @param params
 * @returns {*}
 */
export function queryDoctorForecastData (params) {
  return request({
    url: '/newDipBusinessDoctorAnalysisController/queryDoctorForecastData',
    method: 'post',
    params: params
  })
}

export function queryDrgDoctorKpiData (params) {
  return request({
    url: '/newDrgBusinessDoctorAnalysisController/queryDrgDoctorKpiData',
    method: 'post',
    params: params
  })
}

export function queryDrgDoctorForecastData (params) {
  return request({
    url: '/newDrgBusinessDoctorAnalysisController/queryDrgDoctorForecastData',
    method: 'post',
    params: params
  })
}
