<template>
  <div class="dept-container" :style="{ width: width }">
    <select-tree v-model="dept"
                 :options="departments"
                 :props="defaultProps"
                 :placeholder="placeholder"
                 :disabled="disabled"
                 @clearTree="fnClearTree"/>
  </div>
</template>
<script>

import { querySelectTreeAndSelectList } from '@/api/common/drgCommon'
import selectTree from '@/components/SelectTree/index'
export default {
  name: 'jp_department',
  components: { selectTree },
  props: {
    // 接收绑定参数
    modelVal: String,
    deptType: String,
    placeholder: {
      type: String,
      default: '请选择出院科室'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 宽度
    width: {
      type: String,
      default: '100%'
    }
  },
  data () {
    return {
      departments: [],
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      allDepartments: [],
      dept: '',
      tempDept: ''
    }
  },
  created () {
    this.departments = this.$store.getters.getAllDepartments
    this.allDepartments = this.$store.getters.getAllDepartments
  },
  methods: {
    fnClearTree () {
      this.dept = ''
      this.$emit('selected', this.dept)
      this.$emit('changeDept', '')
    },
    findDeptTypeByDeptCode (deptCode) {
      let deptCodeTemp = this.allDepartments.find(dept => dept.code == deptCode)
      let type = deptCodeTemp ? String(deptCodeTemp.type) : ''
      return type
    }
  },
  model: {
    prop: 'modelVal',
    event: 'selected'
  },
  watch: {
    modelVal: {
      handler: function (newVal) {
        if (!this.tempDept) {
          this.tempDept = newVal
        }
        this.dept = newVal
      },
      immediate: true
    },
    dept: {
      handler: function (newVal) {
        if (newVal) {
          this.$emit('selected', newVal)
          this.$emit('changeDept', this.findDeptTypeByDeptCode(newVal))
        } else {
          this.departments = this.allDepartments
        }
      },
      immediate: true
    },
    deptType: function (type) {
      this.departments = this.allDepartments
      if (type) {
        this.departments = this.departments.filter(dept => dept.type == type)
      } else {
        this.dept = ''
      }
    }
  }
}
</script>
<style scoped>
.dept-container {
  display: inline-block;
}
</style>
