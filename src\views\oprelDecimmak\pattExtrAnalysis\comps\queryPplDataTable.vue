<template>
  <div style="height: 100%">
    <el-table
      :data="tableData"
      :summary-method="getSummaries"
      show-summary
      :header-cell-style="{'text-align':'center'}"
      height="100%"
      v-loading="tableLoading"
      :id="id"
      ref="elTable"
      border>
      <el-table-column label="序号" type="index" fixed="left" align="center"/>
      <el-table-column label="病案号" prop="patientId" width="120px" :fixed="include('patientId')"  align="center"/>
      <el-table-column label="姓名" prop="name" width="80px" :fixed="include('name')" align="center"/>
      <drg-table-column
        prop="insuType"
        width="90"
        dicType="INSURANCE_TYPE"
        label="医保类型">
      </drg-table-column>

      <el-table-column label="性别" prop="gend"  width="80px" :fixed="include('sex')" align="center"/>
      <el-table-column label="年龄" prop="age"  width="80px" :fixed="include('age')" align="center"/>
      <el-table-column label="出院科室名称" prop="deptName" :fixed="include('deptName')" width="150px"  align="left"/>
      <el-table-column label="住院医师名称" prop="drName" :fixed="include('doctorName')" width="110px"  align="center"/>
<!--      <el-table-column label="主要诊断编码" prop="mainDiagDiseCodg" :fixed="include('mainDiagnoseCode')" width="120px"  align="left"/>-->
<!--      <el-table-column label="主要诊断名称" prop="mainDiagDiseName" :fixed="include('mainDiagnoseName')" width="150px"  align="left"/>-->
      <el-table-column label="入院诊断" prop="admDiag" width="120px" :fixed="include('admDiag')" v-if="grperType == 3" :key="1" show-overflow-tooltip align="center"/>
      <el-table-column label="出院诊断" prop="conditionDiagnosis" width="200px" :fixed="include('conditionDiagnosis')" v-if="grperType == 3" :key="2" show-overflow-tooltip align="left"/>

      <el-table-column label="DIP编码" prop="dipCodg" width="120px" :fixed="include('dipCode')" v-if="grperType == 1" :key="1" show-overflow-tooltip align="left"/>
      <el-table-column label="DIP名称" prop="dipName" width="200px" :fixed="include('dipName')" v-if="grperType == 1" :key="2" show-overflow-tooltip align="left"/>
      <el-table-column label="主要诊断【名称】" prop="mainDiagCodeAndName" width="200px" :fixed="include('mainDiagCodeAndName')"  show-overflow-tooltip align="left"/>
      <el-table-column label="其他诊断编码【名称】" prop="otherDiagCodgAndName" width="200px" :fixed="include('otherDiagCodgAndName')"  show-overflow-tooltip align="left"/>

      <el-table-column label="主要操作【名称】" prop="mainOprnCodeAndName" width="200px" :fixed="include('mainOprnCodeAndName')"   show-overflow-tooltip align="left"/>
      <el-table-column label="其他操作编码【名称】" prop="otherOprnCodeAndName" width="200px" :fixed="include('otherOprnCodeAndName')" show-overflow-tooltip align="left"/>
      <el-table-column label="病例类型" prop="diseType" width="100px" :fixed="include('diseType')" v-if="grperType == 1"  show-overflow-tooltip align="left"/>

      <drg-table-column label="辅助目录" prop="isUsedAsstList" :fixed="include('auxiliaryCatalogue')" width="160px" dicType="AD" v-if="grperType == 1"  show-overflow-tooltip align="left"/>
      <el-table-column label="辅助目录-年龄" prop="asstListAgeGrp" :fixed="include('auxiliaryAge')" width="160px" v-if="grperType == 1" :key="4" show-overflow-tooltip align="left"/>
      <el-table-column label="辅助目录-疾病" prop="asstListDiseSevDeg" :fixed="include('auxiliaryIllness')" width="160px" v-if="grperType == 1"  show-overflow-tooltip align="left"/>
      <el-table-column label="辅助目录-肿瘤" prop="asstListDiseSevDeg" :fixed="include('auxiliaryIllness')" width="160px" v-if="grperType == 1"  show-overflow-tooltip align="left"/>
      <el-table-column label="辅助目录-烧伤" prop="auxiliaryBurn" :fixed="include('auxiliaryBurn')" width="160px" v-if="grperType == 1" :key="5" show-overflow-tooltip align="left"/>
      <el-table-column label="DRG编码" prop="drgCodg" width="120px" :fixed="include('drgCode')" v-if="grperType == 3" :key="7" show-overflow-tooltip align="left"/>
      <el-table-column label="DRG名称" prop="drgName" width="200px" :fixed="include('drgName')" v-if="grperType == 3" :key="8" show-overflow-tooltip align="left"/>
      <el-table-column label="成都编码" prop="cdCodg" width="120px" :fixed="include('cdCode')" v-if="grperType == 2" :key="9" show-overflow-tooltip align="left"/>
      <el-table-column label="成都名称" prop="cdName" width="200px" :fixed="include('cdName')" v-if="grperType == 2" :key="10" show-overflow-tooltip align="left"/>
      <el-table-column label="费用区间" prop="costSection" width="150px" :fixed="include('costSection')" align="right"/>
      <el-table-column label="药品费" prop="drugfee" align="right" :fixed="include('drugfee')"></el-table-column>
      <el-table-column label="药占比" prop="medicalCostRate" :fixed="include('medicalCostRate')" width="140px" align="center"></el-table-column>
      <el-table-column label="耗材费" prop="mcsFee" :fixed="include('materialCost')" align="right"></el-table-column>
      <el-table-column label="耗占比" prop="materialCostRate" :fixed="include('materialCostRate')" width="140px" align="center"></el-table-column>

      <el-table-column label="检查费" prop="checkFee" align="right" :fixed="include('drugfee')" v-if="grperType == 1"></el-table-column>
      <el-table-column label="检查费占比" prop="checkfeeRate" :fixed="include('medicalCostRate')" width="140px" v-if="grperType == 1" align="center"></el-table-column>
      <el-table-column label="检验(化验)费" prop="assayFee" :fixed="include('materialCost')"  v-if="grperType == 1" align="right"></el-table-column>
      <el-table-column label="检验费占比" prop="assayFeeRate" :fixed="include('materialCostRate')" v-if="grperType == 1" width="140px" align="center"></el-table-column>


      <el-table-column label="住院天数" prop="inHosDays" :fixed="include('inHosDays')" width="100px" align="center"></el-table-column>
      <el-table-column label="住院天数(标杆)" prop="standardInHosDays" :fixed="include('standardInHosDays')" width="140px" align="center"></el-table-column>
      <el-table-column label="例均费用(标杆)" prop="standardFee" :fixed="include('standardCost')" width="140" align="right"/>
      <el-table-column label="住院总费用" prop="sumfee" :fixed="include('totalCost')" width="140px" align="right"/>
      <el-table-column label="单价" prop="price" width="80px" :fixed="include('price')" v-if="grperType == 3" :key="11" show-overflow-tooltip align="center"/>
      <el-table-column label="总分值" prop="totalSco" width="80px" :fixed="include('totalSco')" v-if="grperType == 3" :key="12" show-overflow-tooltip align="left"/>
      <el-table-column label="院前检查费" prop="preHosExamineCost" width="100px" align="right" :fixed="include('preHosExamineCost')" v-if="grperType == 1"/>
      <el-table-column label="预测金额" prop="forecastAmount"  :fixed="include('forecastAmount')" align="right" width="140px"  fixed="right"/>
      <el-table-column label="原项目基金费用" prop="fundAmtSum" width="150px" align="right" :fixed="include('fundAmtSum')" v-if="grperType == 1"/>
      <el-table-column label="预测金额差异" prop="forecastAmountDiff" width="140px"  align="right" fixed="right">
        <template slot-scope="scope">
          <span :class="[parseFloat(scope.row.forecastAmountDiff) < 0 ? 'som-color-error' : 'som-color-success']">
            {{ scope.row.forecastAmountDiff }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="费用状态" width="100px" align="center" fixed="right">
        <template slot-scope="scope">
          <i class="som-icon-error-waring som-icon-big" v-if="parseFloat(scope.row.forecastAmountDiff) < 0"></i>
          <i class="som-icon-success som-icon-big" v-else></i>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { formatRate } from '@/utils/common'
export default {
  name: 'queryDataTable',
  props: {
    tableData: {
      type: Array
    },
    tableLoading: {
      type: Boolean,
      default: false
    },
    grperType: {
      grperType: Number
    },
    queryType: {
      queryType: Number
    },
    id: {
      type: String
    },
    // columnOptions: {
    //   type: Array,
    //   default: () => []
    // },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    }
  },
  updated () {
    this.refreshTable()
  },
  methods: {
    formatRate,
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length,
        customAverage: (values) => {
          const sum = values.reduce((prev, curr) => prev + curr, 0)
          return sum / values.length
        }
      }
      // let sum12 = 0, sum14 = 0, sum19 = 0
      // columns.forEach((column, index) => {
      //   if (index === 0) {
      //     sums[index] = '统计'
      //     return
      //   }
      //   const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value))
      //   if (index === 12 || index === 14 || index === 18 || index === 19 || index === 20 || index === 21) {
      //     sums[index] = calculations.sum(values).toFixed(2)
      //     if (index === 12) sum12 = sums[index]
      //     else if (index === 14) sum14 = sums[index]
      //     else if (index === 19) sum19 = sums[index]
      //   } else if (index === 16 || index === 17) {
      //     sums[index] = calculations.average(values).toFixed(2)
      //   } else {
      //     sums[index] = ' '
      //   }
      // })
      // sums[13] = calculatePercentage(sum12, sum19)
      // sums[15] = calculatePercentage(sum14, sum19)
      // return sums

      function calculatePercentage (numerator, denominator) {
        if (denominator !== 0) {
          return ((numerator / denominator) * 100).toFixed(2) + '%'
        }
        return '0.00%'
      }
    },
    refreshTable () {
      this.$nextTick(() => {
        this.$refs.elTable.doLayout()
      })
    },
    include (column) {
      return this.fixedColumns.includes(column)
    },
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    }
  }
}
</script>
