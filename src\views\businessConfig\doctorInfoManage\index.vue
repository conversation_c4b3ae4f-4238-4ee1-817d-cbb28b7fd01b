<template>
  <div class="app-container" style="padding: 20px;">
    <drg-form v-model="queryForm"
              :container="true"
              headerTitle="查询条件"
              :contentTitle="contentTitle"
              :totalNum="total"
              show-pagination
              label-width="100px"
              @query="queryData">

      <template slot="extendFormItems">
        <!--        //医师查询-->
        <el-form-item label="医保医师代码" prop="hiDrCode" v-if="this.tab == 1" style="margin-bottom: 20px;">
          <el-input v-model="queryForm.hiDrCode" placeholder="请输入医保医师代码" @input="queryData"/>
        </el-form-item>
        <el-form-item label="术者医师代码" prop="operDrCode" v-if="this.tab == 1" style="margin-bottom: 20px;">
          <el-input v-model="queryForm.operDrCode" placeholder="请输入术者医师代码" @input="queryData"/>
        </el-form-item>
        <el-form-item label="姓名" prop="name" v-if="this.tab == 1" style="margin-bottom: 20px;">
          <el-input v-model="queryForm.name" placeholder="请输入姓名" @input="queryData"/>
        </el-form-item>
        <!--    护士查询    -->
        <el-form-item label="医保护士代码" prop="hiNursCode" v-if="this.tab == 2">
          <el-input v-model="queryForm.hiNursCode" placeholder="请输入医保护士代码" @input="queryData"/>
        </el-form-item>
        <el-form-item label="姓名" prop="name" v-if="this.tab == 2">
          <el-input v-model="queryForm.name" placeholder="请输入姓名" @input="queryData"/>
        </el-form-item>
        <el-form-item label="工号" prop="empno" v-if="this.tab == 2">
          <el-input v-model="queryForm.empno" placeholder="请输入工号" @input="queryData"/>
        </el-form-item>
        <!--   输血查询   -->
        <el-form-item label="院内输血代码" prop="inhospBldCode" v-if="this.tab == 3">
          <el-input v-model="queryForm.inhospBldCode" placeholder="请输入院内输血代码" @input="queryData"/>
        </el-form-item>
        <el-form-item label="院内输血名称" prop="inhospBldName" v-if="this.tab == 3">
          <el-input v-model="queryForm.inhospBldName" placeholder="请输入院内输血名称" @input="queryData"/>
        </el-form-item>
        <el-form-item label="医保输血代码" prop="hiBldCode" v-if="this.tab == 3">
          <el-input v-model="queryForm.hiBldCode" placeholder="请输入院内输血代码" @input="queryData"/>
        </el-form-item>
        <el-form-item label="医保输血名称" prop="hiBldName" v-if="this.tab == 3">
          <el-input v-model="queryForm.hiBldName" placeholder="请输入医保输血名称" @input="queryData"/>
        </el-form-item>
        <!--   科室信息   -->
        <el-form-item label="院内科室代码" prop="inhospDeptCode" v-if="this.tab == 4">
          <el-input v-model="queryForm.inhospDeptCode" placeholder="请输入院内科室代码" @input="queryData"/>
        </el-form-item>
        <el-form-item label="院内科室名称" prop="inhospBldName" v-if="this.tab == 4">
          <el-input v-model="queryForm.inhospDeptName" placeholder="请输入院内科室名称" @input="queryData"/>
        </el-form-item>
        <el-form-item label="医保科室代码" prop="hiDeptCode" v-if="this.tab == 4">
          <el-input v-model="queryForm.hiDeptCode" placeholder="请输入医保科室代码" @input="queryData"/>
        </el-form-item>
        <el-form-item label="医保科室名称" prop="hiDeptName" v-if="this.tab == 4">
          <el-input v-model="queryForm.hiDeptName" placeholder="请输入医保科室名称" @input="queryData"/>
        </el-form-item>

      </template>

      <template slot="buttons">
        <el-button @click="showDialog(null,2)" type="primary" size="mini" class="som-button-margin-right">新增
        </el-button>
        <el-button type="primary" @click="dialogVisible = true" class="som-button-margin-right"><i
          class="el-icon-upload el-icon--left"></i>文件上传
        </el-button>
        <el-button type="primary" @click="downDoctorCodeTemplate" class="som-button-margin-right"><i
          class="el-icon-download el-icon--left"></i>模板下载
        </el-button>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <el-tabs v-model="tab" @tab-click="DoctorNurse">
          <el-tab-pane label="医生" name="1"></el-tab-pane>
          <el-tab-pane label="护士" name="2"></el-tab-pane>
          <el-tab-pane label="输血" name="3"></el-tab-pane>
          <el-tab-pane label="科室" name="4"></el-tab-pane>
        </el-tabs>
        <!--  1.医师  -->
        <el-table
          height="90%"
          :data="tableData"
          style="width: 100%" v-if="tab==1">
          <el-table-column
            type="index"
            width="50"
            label="序号">
          </el-table-column>
          <el-table-column
            prop="hiDrCode"
            label="医保医师代码">
          </el-table-column>
          <drg-table-column
            prop="operDrCode"
            label="术者医师代码">
          </drg-table-column>
          <el-table-column
            prop="name"
            label="姓名">
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-edit"
                type="primary"
                circle
                @click="showDialog(scope.row,1)"></el-button>
            </template>
          </el-table-column>
          <el-table-column label="删除" align="center" fixed="right">
            <template slot-scope="scope">
              <el-popconfirm style="cursor: pointer;display: block" @confirm="deleteCodeInfo(scope.row)"
                             title="是否删除？">
                <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <!--  2.护士 -->
        <el-table
          height="90%"
          :data="tableData"
          style="width: 100%" v-if="tab == 2">
          <el-table-column
            type="index"
            width="50"
            label="序号">
          </el-table-column>
          <el-table-column
            prop="hiNursCode"
            label="医保护士代码">
          </el-table-column>
          <drg-table-column
            prop="empno"
            label="工号">
          </drg-table-column>
          <el-table-column
            prop="name"
            label="姓名">
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-edit"
                type="primary"
                circle
                @click="showDialog(scope.row,1)"></el-button>
            </template>
          </el-table-column>
          <el-table-column label="删除" align="center" fixed="right">
            <template slot-scope="scope">
              <el-popconfirm style="cursor: pointer;display: block" @confirm="deleteCodeInfo(scope.row)"
                             title="是否删除？">
                <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <!-- 3.输血  -->
        <el-table
          height="90%"
          :data="tableData"
          style="width: 100%" v-if="tab == 3">
          <el-table-column
            type="index"
            width="50"
            label="序号">
          </el-table-column>
          <el-table-column
            prop="inhospBldCode"
            label="院内输血代码">
          </el-table-column>
          <el-table-column
            prop="inhospBldName"
            label="院内输血名称">
          </el-table-column>
          <drg-table-column
            prop="hiBldCode"
            label="医保输血代码">
          </drg-table-column>
          <el-table-column
            prop="hiBldName"
            label="医保输血名称">
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-edit"
                type="primary"
                circle
                @click="showDialog(scope.row,1)"></el-button>
            </template>
          </el-table-column>
          <el-table-column label="删除" align="center" fixed="right">
            <template slot-scope="scope">
              <el-popconfirm style="cursor: pointer;display: block" @confirm="deleteCodeInfo(scope.row)"
                             title="是否删除？">
                <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <!-- 4.科室 -->
        <el-table
          height="90%"
          :data="tableData"
          style="width: 100%" v-if="tab == 4">
          <el-table-column
            type="index"
            width="50"
            label="序号">
          </el-table-column>
          <el-table-column
            prop="inhospDeptCode"
            label="院内科室代码">
          </el-table-column>
          <el-table-column
            prop="inhospDeptName"
            label="院内科室名称">
          </el-table-column>
          <drg-table-column
            prop="hiDeptCode"
            label="医保科室代码">
          </drg-table-column>
          <el-table-column
            prop="hiDeptName"
            label="医保科室名称">
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-edit"
                type="primary"
                circle
                @click="showDialog(scope.row,1)"></el-button>
            </template>
          </el-table-column>
          <el-table-column label="删除" align="center" fixed="right">
            <template slot-scope="scope">
              <el-popconfirm style="cursor: pointer;display: block" @confirm="deleteCodeInfo(scope.row)"
                             title="是否删除？">
                <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <!--   医师 dialog     -->
        <el-dialog :title="profttl" :visible.sync="dialogFormVisible">
          <el-form ref="form" :model="form" label-width="90px">
            <el-form-item>
              <el-input v-model="form.id" placeholder="" v-show="false"></el-input>
            </el-form-item>
            <el-form-item label="医保医师代码">
              <el-input v-model="form.hiDrCode" placeholder="请输入医保医师代码"></el-input>
            </el-form-item>
            <el-form-item label="术者医师代码">
              <el-input v-model="form.operDrCode" placeholder="请输入术者医师代码"></el-input>
            </el-form-item>
            <el-form-item label="姓名">
              <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取 消</el-button>
            <el-button type="primary" @click="commitData()">确 定</el-button>
          </div>
        </el-dialog>
        <!--   护士dialog     -->
        <el-dialog :title="profttl" :visible.sync="dialogFormNurse">
          <el-form ref="form" :model="form" label-width="90px">
            <el-form-item>
              <el-input v-model="form.id" placeholder="" v-show="false"></el-input>
            </el-form-item>
            <el-form-item label="医保护士代码">
              <el-input v-model="form.hiNursCode" placeholder="请输入医保护士代码"></el-input>
            </el-form-item>
            <el-form-item label="工号">
              <el-input v-model="form.empno" placeholder="请输入工号"></el-input>
            </el-form-item>
            <el-form-item label="姓名">
              <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormNurse = false">取 消</el-button>
            <el-button type="primary" @click="commitData()">确 定</el-button>
          </div>
        </el-dialog>
        <!--  输血dialog      -->
        <el-dialog :title="profttl" :visible.sync="dialogFormTransfusion">
          <el-form ref="form" :model="form" label-width="90px">
            <el-form-item>
              <el-input v-model="form.id" placeholder="" v-show="false"></el-input>
            </el-form-item>
            <el-form-item label="院内输血代码">
              <el-input v-model="form.inhospBldCode" placeholder="请输入院内输血代码"></el-input>
            </el-form-item>
            <el-form-item label="院内输血名称">
              <el-input v-model="form.inhospBldName" placeholder="请输入院内输血名称"></el-input>
            </el-form-item>
            <el-form-item label="医保输血代码">
              <el-input v-model="form.hiBldCode" placeholder="请输入医保输血代码"></el-input>
            </el-form-item>
            <el-form-item label="医保输血名称">
              <el-input v-model="form.hiBldName" placeholder="请输入医保输血名称"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormTransfusion = false">取 消</el-button>
            <el-button type="primary" @click="commitData()">确 定</el-button>
          </div>
        </el-dialog>

        <!--   科室 dialog     -->
        <el-dialog :title="profttl" :visible.sync="dialogFormDept">
          <el-form ref="form" :model="form" label-width="90px">
            <el-form-item>
              <el-input v-model="form.id" placeholder="" v-show="false"></el-input>
            </el-form-item>
            <el-form-item label="院内科室代码">
              <el-input v-model="form.inhospDeptCode" placeholder="请输入院内科室代码"></el-input>
            </el-form-item>
            <el-form-item label="院内科室名称">
              <el-input v-model="form.inhospDeptName" placeholder="请输入院内科室名称"></el-input>
            </el-form-item>
            <el-form-item label="医保科室代码">
              <el-input v-model="form.hiDeptCode" placeholder="请输入医保科室代码"></el-input>
            </el-form-item>
            <el-form-item label="医保科室名称">
              <el-input v-model="form.hiDeptName" placeholder="请输入医保科室名称"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormDept = false">取 消</el-button>
            <el-button type="primary" @click="commitData()">确 定</el-button>
          </div>
        </el-dialog>

        <el-dialog
          title="上传文件"
          :visible.sync="dialogVisible"
          width="50%">
          <el-upload
            style="text-align: center"
            drag
            ref="upload"
            :limit="1"
            action="customize"
            accept=".xlsx,.xls"
            :http-request="upload">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
          </el-upload>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>

import {
  insertNurseCodeInfo,
  queryNurseCodeInfo,
  updateNurseCodeInfo,
  deleteDoctorCodeInfo,
  doctorCodeUpload,
  downDoctorCodeTemplate,
  insertDoctorCodeInfo,
  queryDoctorCodeInfo,
  updateDoctorCodeInfo
} from '@/api/dataConfig/doctorCodeConfig'
import {deleteNurseCodeInfo, downNurseCodeTemplate, nurseCodeUpload} from '../../../api/dataConfig/doctorCodeConfig'
import {
  deleteTransfusionCodeInfo, downTransfusionCodeTemplate, insertTransfusionCodeInfo,
  queryTransfusionCodeInfo, transfusionCodeUpload,
  updateTransfusionCodeInfo
} from '../../../api/dataConfig/TransFusionCodeConfig'
import {queryHospDeptCodeInfo} from "../../../api/dataConfig/HospDeptCodeConfig";

export default {
  name: 'doctorInfoManage',
  data: () => ({
    queryForm: {
      hiDrCode: '',
      operDrCode: '',
      name: '',
      hiNursCode: '',
      empno: '',
      inhospBldCode: '',
      inhospBldName: '',
      hiBldCode: '',
      hiBldName: ''
    },
    form: {
      id: '',
      hiDrCode: '',
      operDrCode: '',
      name: '',
      hiNursCode: '',
      empno: '',
      inhospDeptCode: '',
      inhospDeptName: '',
      hiDeptCode: '',
      hiDeptName: ''
    },
    tableData: [],
    dialogFormVisible: false,
    dialogFormNurse: false,
    dialogFormTransfusion: false,
    dialogFormDept: false,
    profttl: '修改',
    dialogType: 1,
    dialogVisible: false,
    total: 0,
    contentTitle: '医师编码配置列表',
    tab: '1'
  }),
  mounted() {
    this.queryData()
  },
  methods: {
    queryData() {
      if (this.tab == 1) {
        this.tableData = []
        // 医生
        queryDoctorCodeInfo(this.getParams()).then(res => {
          this.tableData = res.data.list
          this.total = res.data.total
        })
      } else if (this.tab == 2) {
        this.tableData = []
        // 护士
        queryNurseCodeInfo(this.getParams()).then(res => {
          this.tableData = res.data.list
          this.total = res.data.total
        })
      } else if (this.tab == 3) {
        this.tableData = []
        // 输血
        queryTransfusionCodeInfo(this.getParams()).then(res => {
          this.tableData = res.data.list
          this.total = res.data.total
        })
      } else if (this.tab == 4) {
        this.tableData = []
        // 科室
        queryHospDeptCodeInfo(this.getParams()).then(res => {
          this.tableData = res.data.list
          this.total = res.data.total
        })
      }
    },
    getParams() {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    getForm() {
      let params = {}
      Object.assign(params, this.form)
      return params
    },
    deleteCodeInfo(row) {
      if (this.tab == 1) {
        // 医师
        deleteDoctorCodeInfo(row).then(res => {
          this.queryData()
        })
      } else if (this.tab == 2) {
        //  护士
        deleteNurseCodeInfo(row).then(res => {
          this.queryData()
        })
      } else if (this.tab == 3) {
        //  输血
        deleteTransfusionCodeInfo(row).then(res => {
          this.queryData()
        })
      }
    },
    commitData: function () {
      if (this.tab == 1) {
        if (this.dialogType == 1) {
          updateDoctorCodeInfo(this.getForm()).then(res => {
            this.dialogFormVisible = false
          })
        } else {
          insertDoctorCodeInfo(this.getForm()).then(res => {
            if (res.code == 200) {
              this.$message.success('新增成功')
              this.dialogFormVisible = false
              this.queryData()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      } else if (this.tab == 2) {
        if (this.dialogType == 1) {
          updateNurseCodeInfo(this.getForm()).then(res => {
            this.dialogFormNurse = false
          })
        } else {
          insertNurseCodeInfo(this.getForm()).then(res => {
            if (res.code == 200) {
              this.$message.success('新增成功')
              this.dialogFormNurse = false
              this.queryData()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      } else if (this.tab == 3) {
        if (this.dialogType == 1) {
          updateTransfusionCodeInfo(this.getForm()).then(res => {
            this.dialogFormTransfusion = false
          })
        } else {
          insertTransfusionCodeInfo(this.getForm()).then(res => {
            if (res.code == 200) {
              this.$message.success('新增成功')
              this.dialogFormTransfusion = false
              this.queryData()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      }
    },
    showDialog(row, type) {
      if (this.tab == 1) {
        this.dialogFormVisible = true
      } else if (this.tab == 2) {
        this.dialogFormNurse = true
      } else if (this.tab == 3) {
        this.dialogFormTransfusion = true
      } else if (this.tab == 4) {
        this.dialogFormDept = true
      }
      if (type == 1) {
        this.profttl = '修改'
        this.form = row
        this.dialogType = 1
      } else {
        this.form = {}
        this.profttl = '新增'
        this.dialogType = 2
      }
    },
    upload(data) {
      let params = new FormData()
      params.append('file', data.file)
      if (this.tab == 1) {
        doctorCodeUpload(params).then(res => {
          if (res.code == 200) {
            this.dialogVisible = false
            this.$refs.upload.clearFiles()
            this.$message.success('上传成功')
            this.queryData()
          }
        }).catch(() => {
          this.$refs.upload.clearFiles()
        })
      } else if (this.tab == 2) {
        nurseCodeUpload(params).then(res => {
          if (res.code == 200) {
            this.dialogVisible = false
            this.$refs.upload.clearFiles()
            this.$message.success('上传成功')
            this.queryData()
          }
        }).catch(() => {
          this.$refs.upload.clearFiles()
        })
      } else if (this.tab == 3) {
        transfusionCodeUpload(params).then(res => {
          if (res.code == 200) {
            this.dialogVisible = false
            this.$refs.upload.clearFiles()
            this.$message.success('上传成功')
            this.queryData()
          }
        }).catch(() => {
          this.$refs.upload.clearFiles()
        })
      }
    },
    downDoctorCodeTemplate() {
      if (this.tab == 1) {
        downDoctorCodeTemplate().then(res => {
          this.$somms.download(res, '医师编码对照', 'application/vnd.ms-excel')
        })
      } else if (this.tab == 2) {
        downNurseCodeTemplate().then(res => {
          this.$somms.download(res, '护士编码对照', 'application/vnd.ms-excel')
        })
      } else if (this.tab == 3) {
        downTransfusionCodeTemplate().then(res => {
          this.$somms.download(res, '输血编码对照', 'application/vnd.ms-excel')
        })
      }
    },
    DoctorNurse() {
      if (this.tab == 1) {
        this.contentTitle = '医师编码配置列表'
      } else if (this.tab == 2) {
        this.contentTitle = '护士编码配置列表'
      } else if (this.tab == 3) {
        this.contentTitle = '输血编码配置列表'
      } else if (this.tab == 4) {
        this.contentTitle = '科室编码配置列表'
      }
      this.queryData()
    }
  }
}
</script>
<style scoped>
/deep/ .el-dialog {
  height: 60vh;
  overflow: auto;
}
</style>
