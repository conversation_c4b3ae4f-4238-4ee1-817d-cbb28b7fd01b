<template>
  <div class="app-container">
    <drg-form :container="true"
             headerTitle="查询条件"
             contentTitle="数据"
             @query="queryHospital"
             v-model="queryForm">
      <template slot="extendFormItems">
        <el-form-item label="医院名称" prop="medinsName">
          <el-input class="som-form-item" v-model="queryForm.medinsName"></el-input>
        </el-form-item>
        <el-form-item label="医院等级" prop="hospLvChn">
          <el-select v-model="queryForm.hospLvChn" clearable filterable placeholder="请选择">
            <el-option
              v-for="item in selectLevel"
              :key="item.hospLvChn"
              :label="item.hospLvChn"
              :value="item.hospLvChn">
            </el-option>
          </el-select>
        </el-form-item>
      </template>
      <template slot="containerContent">
        <el-table
          :data="hospitalData"
          border
          stripe
          height="100%"
          :header-cell-style="{'text-align':'center'}">
          <el-table-column label="序号" type="index" align="center" width="50" />
          <el-table-column label="医院ID" prop="hospitalId" align="center" :key="1"/>
          <el-table-column label="医院名称" prop="medinsName" align="center" :key="2"/>
          <el-table-column label="医院级别" prop="hospLvChn" align="center" :key="3"/>
          <el-table-column label="操作" align="center" :key="4">
            <template slot-scope="scope">
              <el-button type="primary" round plain @click="handleClick(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-dialog
          title="修改医院级别"
          :visible.sync="isShowDialog"
          width="30%">
          <div style="padding: 0 0 10% 5%">
            <span>医院名称：{{ rowData.medinsName}}</span>
          </div>
          <div style="padding: 0 0 10% 5%">
            <span>医院级别：{{ rowData.hospLvChn}}</span>
          </div>
          <div style="padding: 0 0 10% 5%">
            <span>选择修改级别：</span>
            <template>
              <el-select v-model="dept_lv" placeholder="请选择级别">
                <el-option
                  v-for="item in selectLevel"
                  :key="item.hospLv"
                  :label="item.hospLvChn"
                  :value="item.hospLv">
                </el-option>
              </el-select>
            </template>
          </div>
          <div style="padding: 0 0 10% 5%">
            <el-button type="primary" round plain @click="isShowDialog = false">取消</el-button>
            <el-button type="primary" round plain @click="updateLevel">确定</el-button>
          </div>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>

<script>
import { QueryHospital, SelectLevels, UpdateLevel } from '../../../api/dataConfig/hospitalLevelConfig'

export default {
  name: 'hospitalLevelConfig',
  data: () => ({
    queryForm: {},
    hospitalData: [],
    rowData: [],
    selectLevel: [],
    dept_lv: [],
    isShowDialog: false
  }),
  mounted () {
    this.selectLevels()
  },
  methods: {
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    queryHospital () {
      QueryHospital(this.getParams()).then(res => {
        this.hospitalData = res.data
      })
    },
    handleClick (row) {
      this.rowData = row
      this.isShowDialog = true
    },
    selectLevels () {
      SelectLevels().then(res => {
        this.selectLevel = res.data
      })
    },
    updateLevel () {
      let params = {}
      Object.assign(params, this.rowData)
      params.hospLv = this.dept_lv
      UpdateLevel(params).then(res => {
        this.queryHospital()
        this.isShowDialog = false
      }).catch(() => {})
    }
  }
}
</script>

<style></style>
