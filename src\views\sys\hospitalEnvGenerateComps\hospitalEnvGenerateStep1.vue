<template>
  <div style="width: 100%; height: 85%;display: flex;">
    <div class="som-wd-one-hundred" style="display: flex;position: relative">
      <div style="width: 20%;height: 100%;overflow: auto;position: relative" @contextmenu.prevent="openAddMenu">
        <el-tree
            :data="treeData"
            node-key="orgId"
            default-expand-all
            :expand-on-click-node="false"
            class="el-tree-customer"
            empty-text="无数据"
            @node-click="nodeClick"
            ref="tree">
          <span class="custom-tree-node" slot-scope="{ node, data }">
              <span class="text-ellip node-name">{{ data.orgName }}</span>
          </span>
        </el-tree>

        <ul v-show="showAddHosMenu" :style="{ top: addHosMenu.top, left: addHosMenu.left }" class="contextmenu">
          <li @click="showOrg = true">新增组织</li>
          <!--          <li @click="showHos = true">新增医院</li>-->
        </ul>

        <!-- 新增组织 -->
        <el-dialog title="新增组织机构" :visible.sync="showOrg" :z-index="1000">
          <el-form :model="orgForm" ref="orgForm" :rules="orgRules" label-position="left" label-width="120px">
            <el-form-item label="父级机构">
              <el-input v-model="orgForm.deptNamePath" autocomplete="off" size="small" @input="orgNameInput"></el-input>
              <el-tree
                  :data="treeData"
                  node-key="orgId"
                  default-expand-all
                  :expand-on-click-node="false"
                  class="el-tree-customer"
                  style="max-height: 200px;overflow: auto"
                  empty-text="加载中.."
                  @node-click="orgNodeClick"
                  ref="tree">
                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span class="text-ellip node-name">{{ data.orgName }}</span>
                </span>
              </el-tree>
            </el-form-item>
            <el-form-item label="组织名称" prop="orgName">
              <el-input v-model="orgForm.orgName" autocomplete="off" size="small"></el-input>
            </el-form-item>
            <el-form-item label="组织编码" prop="yzbm">
              <el-input v-model="orgForm.yzbm" size="small"></el-input>
            </el-form-item>
            <el-form-item label="业务组织类型" prop="biz_org_type">
              <drg-dict-select dicType="BIZ_ORG_TYPE"
                               v-model="orgForm.bizOrgType"
                               placeholder="请输入业务组织类型"
                               :useClass="false"
                               :selectStyle="{ width: '100%' }"/>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="showOrg = false">取 消</el-button>
            <el-button type="primary" @click="addOrg">确 定</el-button>
          </div>
        </el-dialog>

        <!-- 新增医院 -->
        <el-dialog title="新增医院" :visible.sync="showHos" :z-index="1000">
          <el-form :model="hosForm" ref="hosForm" :rules="hosRules" label-position="left" label-width="120px">
            <el-form-item label="医院id" prop="hospitalId">
              <el-input v-model="hosForm.hospitalId" placeholder="请输入医院id"></el-input>
            </el-form-item>
            <el-form-item label="医院名称" prop="medinsName">
              <el-input v-model="hosForm.medinsName" placeholder="请输入医院名称"></el-input>
            </el-form-item>
            <el-form-item label="医院级别" prop="hospLv">
              <drg-dict-select dicType="YYJB"
                               v-model="hosForm.hospLv"
                               placeholder="请输入医院级别中文"
                               :useClass="false"
                               :selectStyle="{ width: '100%' }"/>
            </el-form-item>
            <el-form-item label="医院系数" prop="hospCof">
              <el-input v-model="hosForm.hospCof" placeholder="请输入医院系数"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="showHos = false">取 消</el-button>
            <el-button type="primary" @click="addHos">确 定</el-button>
          </div>
        </el-dialog>
      </div>

      <div style="width: 80%;display: flex;flex-direction: column">
        <el-alert :title="text"
                  :description="desc"
                  show-icon
                  center
                  :closable="false"
                  :type="type"></el-alert>

        <div style="margin-top: 2%;width: 100%">
          <!--          <el-card  v-if="!containsHospital"> </el-card>-->
          <el-select v-model="hospitalId" style="width: 60%;display: inline-block" v-if="!containsHospital"
                     placeholder="请选择医疗机构"
                     @change="hospitalChange">
            <el-option
                v-for="item in hospitalData"
                :key="item.hospitalId"
                :label="item.medinsName"
                :value="item.hospitalId"
                :disabled="item.disabled">
              <span style="float: left">{{ item.hospitalId }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.medinsName }}</span>
            </el-option>
          </el-select>

          <div style="display: inline-block" v-if="!containsHospital">
            <el-button type="primary" @click="showHos = true">新增机构</el-button>
          </div>

          <div style="width: 40%;margin-top: 2%;" v-if="generateInfo">
            <!-- 新增用户 form -->
            <el-form :model="userForm" :rules="userFormRule" ref="userForm" label-width="80px" class="demo-ruleForm">
              <el-form-item label="用户名" prop="username">
                <el-input v-model="userForm.username"></el-input>
              </el-form-item>
              <el-form-item label="密码" prop="password">
                <el-input type="password" v-model="userForm.password"></el-input>
              </el-form-item>
              <el-form-item label="角色" prop="userRoles">
                <el-select v-model="userForm.userRoles" multiple placeholder="请选择" style="width: 100%">
                  <el-option v-for="item in roles" :key="item.id"
                             :label="item.memo_info" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>

        </div>
      </div>

      <!-- 加载 -->
      <div v-loading="loading" style="height: 100%;width: 100%;position: absolute;" v-show="loading"></div>
    </div>
    <!-- 按钮 -->
    <div style="position: absolute; bottom: 5%;right: 2%">
      <el-button type="primary" v-if="generateInfo" @click="generateHospitalInfo">生成医院信息</el-button>
      <el-button type="danger" v-if="!nextDisabled" @click="deleteInfo">删除</el-button>
      <el-button type="danger" @click="truncateAllVisible = true">清空所有环境</el-button>
      <el-button type="primary" :disabled="nextDisabled" @click="next">下一步</el-button>
    </div>

    <!-- 显示删除所有 -->
    <el-dialog
        title="提示"
        :visible.sync="truncateAllVisible"
        width="30%;"
        :z-index="1000">
      <el-input
          type="textarea"
          :rows="20"
          placeholder="请输入内容"
          v-model="truncateSql">
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="truncateAllVisible = false">取 消</el-button>
        <el-button type="primary" @click="truncateAllEnv">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>

import {addOrg,registerRequest} from '@/api/orgManamement'
import {insertHospitalInfo, queryHospitalInfo} from '@/api/dataConfig/hospitalView'
import {findAll} from '@/api/role'

export default {
  name: 'hospitalEnvGenerateStep1',
  props: {
    treeData: {
      type: Array,
      default: () => []
    },
    // 已经在组织机构的医院信息
    treeHospitalInfo: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    containsHospital: true,
    loading: false,
    type: 'warning',
    text: '暂未选择',
    desc: '无',
    hospitalId: '',
    medinsName: '',
    username: '',
    truncateSql: '',
    hospitalData: [],
    nextDisabled: true,
    generateInfo: false,
    showAddHosMenu: false,
    truncateAllVisible: false,
    curNode: null,
    addHosMenu: {
      top: 0,
      left: 0
    },
    tempHosInfo: {},
    roles: [],
    userForm: {
      username: '',
      password: '',
      userRoles: []
    },
    userFormRule: {
      username: [
        {required: true, message: '请输入用户名', trigger: 'blur'}
      ],
      password: [
        {required: true, message: '请输入密码', trigger: 'blur'}
      ],
      userRoles: [
        {required: true, message: '请选择角色', trigger: 'blur'}
      ]
    },
    orgForm: {
      deptNamePath: '',
      orgName: '',
      yzbm: '',
      bizOrgType: ''
    },
    orgRules: {
      orgName: [
        {required: true, message: '请输入机构名称', trigger: 'blur'}
      ],
      yzbm: [
        {required: true, message: '请输入机构编码', trigger: 'blur'}
      ]
    },
    hosForm: {
      hospitalId: '',
      medinsName: '',
      hospLv: '',
      hospCof: ''
    },
    hosRules: {
      hospitalId: [
        {required: true, message: '请输入医院ID', trigger: 'blur'}
      ],
      medinsName: [
        {required: true, message: '请输入医院名称', trigger: 'blur'}
      ],
      hospLv: [
        {required: true, message: '请输入医院级别', trigger: 'blur'}
      ],
      hospLvChn: [
        {required: true, message: '请选择医院级别中文', trigger: 'change'}
      ],
      medinsType: [
        {required: true, message: '请输入医院类型', trigger: 'blur'}
      ],
      hospCof: [
        {required: true, message: '请输入医院系数', trigger: 'blur'}
      ]
    },
    curOrgNode: {},
    showOrg: false,
    showHos: false
  }),
  mounted() {
    if (this.$somms.hasHosRole()) {
      this.$message({
        message: '检测到当前为医院账号，跳过步骤【选择操作医院】',
        type: 'warning'
      })
      this.queryHospital(true)
    }

    this.truncateSql = `
        <!-- 流程表 -->
        <!-- 结算清单信息表（新） -->
        TRUNCATE TABLE som_setl_intf_bas_info;
        <!-- 结算清单诊断信息表（新） -->
        TRUNCATE TABLE som_setl_intf_diag;
        <!-- 结算清单重症监护信息表（新） -->
        TRUNCATE TABLE som_setl_intf_scs_cutd;
        <!-- 结算清单费用信息表（新） -->
        TRUNCATE TABLE som_setl_intf_chrg_item;
        <!-- 结算清单手术信息表（新） -->
        TRUNCATE TABLE som_setl_intf_oprn;
        <!-- 结算清单门诊慢特病信息表（新） -->
        TRUNCATE TABLE som_setl_intf_slow_special;
        <!-- 结算清单基金支付信息表（新） -->
        TRUNCATE TABLE som_setl_intf_fund_pay;
        <!-- 病案首页信息表（新） -->
        TRUNCATE TABLE som_medcas_intf_bas_info;
        <!-- 病案首页诊断信息表（新） -->
        TRUNCATE TABLE som_medcas_intf_diag_info;
        <!-- 病案首页重症监护信息表（新） -->
        TRUNCATE TABLE som_medcas_intf_scs_cutd_info;
        <!-- 病案首页手术信息表（新） -->
        TRUNCATE TABLE som_medcas_intf_oprn_oprt_info;
        <!-- 住院结算信息表（新） -->
        TRUNCATE TABLE som_chrg_item_intf;
        <!-- 住院医嘱信息表（新） -->
        TRUNCATE TABLE som_drord_info_intf;
        <!-- 临床检查信息表（新） -->
        TRUNCATE TABLE som_clnc_examrpt_main;
        <!-- 临床检查详情信息表（新） -->
        TRUNCATE TABLE som_exam_item_info;
        <!-- 临床检查样本信息表（新） -->
        TRUNCATE TABLE som_exam_spcm_info;
        <!-- 临床检查影像信息表（新） -->
        TRUNCATE TABLE som_exam_img_info;
        <!-- 临床检验信息表（新） -->
        TRUNCATE TABLE som_clnc_test_rpot_main;
        <!-- 临床检验详情信息表（新） -->
        TRUNCATE TABLE som_test_detl_info;
        <!-- 临床检验样本信息表（新） -->
        TRUNCATE TABLE som_test_spcm_info;
        <!-- 电子病历信息表（新） -->
        TRUNCATE TABLE som_elec_medrcd_info;
        <!-- 电子病历鉴别诊断记录表（新） -->
        TRUNCATE TABLE som_elec_medrcd_codse_rcd;
        <!-- 电子病历诊断信息表（新） -->
        TRUNCATE TABLE som_elec_medrcd_diag_info;
        <!-- 电子病历死亡记录表（新） -->
        TRUNCATE TABLE som_elec_medrcd_die_rcd;
        <!-- 电子病历手术信息表（新） -->
        TRUNCATE TABLE som_elec_medrcd_oprn_rcd;
        <!-- 电子病历出院小结信息表（新） -->
        TRUNCATE TABLE som_elec_medrcd_dscg_sumy;
        <!-- 电子病历抢救信息表（新） -->
        TRUNCATE TABLE som_elec_medrcd_cond_resc_rcd;
        TRUNCATE TABLE som_otp_mdtrt_info;
        TRUNCATE TABLE som_otp_mdtrt_diag_info;
        TRUNCATE TABLE som_fee_detl_info;
        <!-- 住院费用信息表（新） -->
        TRUNCATE TABLE som_chrg_detl_intf;


          <!-- # som_setl_invy_bas_info 新接口数据-->
          <!-- # 医保结算清单-->
        TRUNCATE TABLE som_setl_invy_bas_info;
        TRUNCATE TABLE som_bld_info;
        TRUNCATE TABLE som_diag_info;
        TRUNCATE TABLE som_scs_cutd_info;
        TRUNCATE TABLE som_chrg_item_info;
        TRUNCATE TABLE som_oprn_rcd;
        TRUNCATE TABLE som_otp_crds_diag_info;
        TRUNCATE TABLE som_fund_pay_info;
        TRUNCATE TABLE som_setl_info;

        <!-- 基本信息表 -->
        <!-- 结算清单信息表 -->
        TRUNCATE TABLE som_hi_invy_bas_info;
        <!-- 结算清单诊断信息表 -->
        TRUNCATE TABLE som_diag;
        <!-- 结算清单手术表 -->
        TRUNCATE TABLE som_oprn_oprt_info;
        <!-- 结算清单门诊慢特病信息表 -->
        TRUNCATE TABLE som_otp_slow_special_trt_info;
        <!-- 结算清单重症监护信息表 -->
        TRUNCATE TABLE som_setl_invy_scs_cutd_info;
        <!-- 结算清单费用信息表 -->
        TRUNCATE TABLE som_hi_setl_invy_med_fee_info;
        <!-- 结算清单基金支付信息表 -->
        TRUNCATE TABLE som_fund_pay;
        <!-- 结算清单输血表 -->
        TRUNCATE TABLE som_setl_invy_bld_info;

        <!-- 分组产生数据表 -->
        <!-- DRG分组记录表 -->
        TRUNCATE TABLE som_drg_grp_rcd;
        <!-- DRG分组日志表 -->
        TRUNCATE TABLE som_drg_grper_intf_trns_log;
        <!-- DIP分组记录表 -->
        TRUNCATE TABLE som_dip_grp_rcd;
        <!-- DIP分组日志表 -->
        TRUNCATE TABLE som_dip_grper_intf_trns_log;
        <!-- 成都分组记录表 -->
        TRUNCATE TABLE som_grp_rcd;
        <!-- 成都分组日志表 -->
        TRUNCATE TABLE som_cd_grper_intf_trns_log;
        <!-- DRG信息表 -->
        TRUNCATE TABLE som_drg_grp_info;
        <!-- 成都信息表 -->
        TRUNCATE TABLE som_cd_grp_info;
        <!-- DIP信息表 -->
        TRUNCATE TABLE som_dip_grp_info;
        <!-- 数据处理日志表 -->
        TRUNCATE TABLE som_datapros_log;
        <!-- 编码资源消耗预表(校验) -->
        TRUNCATE TABLE som_codg_resu_adjm_rcd;
        <!-- 结算清单校验错误记录(校验,清洗) -->
        TRUNCATE TABLE som_setl_invy_chk_err_rcd;
        <!-- 结算清单质量扣分明细表(校验) -->
        TRUNCATE TABLE som_setl_invy_qlt_dedu_point_detl;
        <!-- DIP分值信息表 -->
        TRUNCATE TABLE som_dip_sco;
        <!-- DRG分值信息表 -->
        TRUNCATE TABLE som_drg_sco;
        <!-- 结算清单校验 -->
        TRUNCATE TABLE som_setl_invy_chk;
        <!-- 结算清单校验明细 -->
        TRUNCATE TABLE som_invy_chk_detl;
        <!-- 病案首页校验信息表 -->
        TRUNCATE TABLE som_init_hi_setl_invy_med_fee_info;
        <!-- 病案首页校验诊断信息表 -->
        TRUNCATE TABLE som_init_diag_info;
        <!-- 病案首页校验手术信息表 -->
        TRUNCATE TABLE som_medcas_hmpg_oprn_info;
        <!-- 可优化病案患者信息表 -->
        TRUNCATE TABLE som_can_opt_medcas_info;
        <!-- 可优化病案病组信息表 -->
        TRUNCATE TABLE som_in_group_rcd;
        <!-- 结算清单诊断修改信息表 -->
        TRUNCATE TABLE som_his_diag;
        <!-- 结算清单手术修改信息表 -->
        TRUNCATE TABLE som_his_oprn_oprt_info;
        <!-- DIP信息备份表 -->
        TRUNCATE TABLE som_dip_grp_bac;
        <!-- DRG费用范围表 -->
        TRUNCATE TABLE som_drg_standard;
        <!-- DRG标杆信息表 -->
        TRUNCATE TABLE som_drg_supe_ultra_low_bind;
        <!-- 成都标杆信息表 -->
        TRUNCATE TABLE som_cd_standard_info;
        <!-- DIP标杆表 -->
        TRUNCATE TABLE som_dip_standard;
        <!-- DIP费用范围表 -->
        TRUNCATE TABLE som_dip_supe_ultra_low_bind;
        <!-- DIP区域标杆表 -->
        TRUNCATE TABLE som_dip_regn_standard;
        <!-- DRG区域标杆表 -->
        TRUNCATE TABLE som_drg_regn_standard;
        <!-- 标杆费用表 -->
        TRUNCATE TABLE som_std_fee;
        <!-- DRG表 -->
        TRUNCATE TABLE som_drg_name;

        <!-- DIP反馈数据上传信息表 -->
        TRUNCATE TABLE som_fund_dfr_fbck;
        <!-- DIP反馈数据详情信息表 -->
        TRUNCATE TABLE som_dip_grp_fbck;
        <!-- DRG反馈数据上传信息表 -->
        TRUNCATE TABLE som_drg_pt_val_pay;
        <!-- DRG反馈数据详情信息表 -->
        TRUNCATE TABLE som_drg_grp_fbck;

        <!-- APP标签表 -->
        TRUNCATE TABLE app_cfg_label;
        <!-- APP收藏表 -->
        TRUNCATE TABLE app_collection;
        <!-- APP配置表 -->
        TRUNCATE TABLE app_config;
        <!-- APP反馈表 -->
        TRUNCATE TABLE app_feedback;
        <!-- APP信息表 -->
        TRUNCATE TABLE app_message;
        <!-- APP历史信息表 -->
        TRUNCATE TABLE app_message_history;
        <!-- APP搜索记录表 -->
        TRUNCATE TABLE app_search_history;
        <!-- APP用户头像表 -->
        TRUNCATE TABLE app_user_image;
        <!-- APP版本表 -->
        TRUNCATE TABLE app_versions;

        <!-- 组织架构表 -->
        <!-- 组织架构信息表 -->
        TRUNCATE TABLE som_bas_dept;
        <!-- 系统用户信息表 -->
        TRUNCATE TABLE som_back_user;
        <!-- 用户权限信息表 -->
        TRUNCATE TABLE som_user_role;
        <!-- 医院信息表 -->
        TRUNCATE TABLE som_hosp_info;
        <!-- 科室信息表 -->
        TRUNCATE TABLE som_dept;
        <!-- 医生信息表 -->
        TRUNCATE TABLE som_medstff_info;
        <!-- 护士编码对照 -->
        TRUNCATE TABLE som_hi_nurs_codg_crsp;
        <!-- 输血编码对照表 -->
        TRUNCATE TABLE som_bld_crsp;
        <!-- 医生编码对照 -->
        TRUNCATE TABLE som_hi_dr_crsp;
        <!-- 监管-诊断 -->
        TRUNCATE TABLE som_init_diag;
        <!-- 监管- -->
        TRUNCATE TABLE som_init_grp;
        <!-- 监管-手术 -->
        TRUNCATE TABLE som_init_oprn_oprt_info;
        <!-- 监管 -->
        TRUNCATE TABLE som_hi_invy_supn_info;
        <!-- 清单标记表 -->
        TRUNCATE TABLE som_invy_label;
    `
  },
  methods: {
    // 树节点点击
    nodeClick(node) {
      this.curNode = node
      this.text = '已选择'
      this.generateInfo = false
      this.tempHosInfo = {}
      queryHospitalInfo({hospitalId: node.orgId}).then(res => {
        if (res.data && res.data.length > 0) {
          this.type = 'success'
          this.checkHospital(node.orgName)
          this.hospitalId = node.orgId
          this.medinsName = node.orgName
          this.username = res.data[0].username
        } else {
          this.hospitalId = ''
          this.queryHospital(false, node.orgName)
        }
      })
    },
    // 选中医院
    checkHospital(name, status = true) {
      this.desc = '医院为：' + name + ',可进行下一步操作'
      this.containsHospital = status
    },
    // 选中机构
    checkOrganization(name) {
      this.desc = '机构为：' + name + ',请选择新增医院'
      this.containsHospital = false
    },
    // 查询医院
    queryHospital(isHospital = false, orgName = '') {
      this.hospitalData = []
      queryHospitalInfo().then(res => {
        if (res.data && res.data.length > 0) {
          let existsHospitalIds = []
          this.treeHospitalInfo.map(info => existsHospitalIds.push(info.orgId))
          if (existsHospitalIds.length == res.data.length) {
            // 警告全部医院已经生成
            this.messageWaring('所有医院已经生成，请直接选择左侧医院')
            this.hospitalId = ''
            this.containsHospital = true
          } else {
            res.data.map(d => {
              if (existsHospitalIds.includes(d.hospitalId)) {
                d.disabled = true
              } else {
                d.disabled = false
              }
            })
            this.hospitalData = res.data
            if (orgName) {
              this.checkOrganization(orgName)
            }
          }
          // 如果是医院则跳过
          if (isHospital && res.data && res.data.length > 0) {
            this.hospitalId = res.data[0].hospitalId
            this.medinsName = res.data[0].medinsName
            this.username = res.data[0].username
            this.success()
          }
        }
      })
    },
    // 医院改变
    hospitalChange(val) {
      let medinsName = ''
      this.hospitalData.map(data => {
        if (data.hospitalId == val) {
          medinsName = data.medinsName
        }
      })
      this.messageWaring(medinsName + '检测到未生成医院组织架构和用户信息，请点击生成')
      // 不让下一步显示，保存临时id
      this.tempHosInfo = {hospitalId: val, medinsName: medinsName}
      // this.hospitalId = ''
      this.generateInfo = true
      // 查询所有角色
      findAll().then((res) => {
        this.roles = res.data
      })
      this.$nextTick(() => {
        this.nextDisabled = true
      })
      // this.checkHospital(medinsName, false)
    },
    // 生成医院信息
    generateHospitalInfo() {
      this.$refs.userForm.validate(async (valid) => {
        if (valid) {
          let orgParams = {
            prntDeptId: this.curNode.orgId,
            orgName: this.tempHosInfo.medinsName,
            teamleadType: '1',
            bizOrgType: '2',
            argtSeq: 4,
            deptHery: 4,
            deptIdPath: this.curNode.deptIdPath,
            deptNamePath: this.curNode.deptNamePath,
            yzbm: this.tempHosInfo.hospitalId
          }

          if (Object.keys(this.curNode).length > 0) {
            orgParams.argtSeq = this.curNode.argtSeq + 1
            orgParams.deptHery = this.curNode.deptHery + 1
          }

          // 新增组织架构
          await addOrg(orgParams).then(res => {
            this.$message({
              type: 'success',
              message: '新增组织架构成功'
            })
          })

          // 新增用户
          let userParams = {
            hospitalId: this.tempHosInfo.hospitalId,
            username: this.userForm.username,
            password: this.userForm.password,
            nknm: this.tempHosInfo.medinsName,
            blngOrgOrgId: this.tempHosInfo.hospitalId,
            blngOrgOrgName: this.tempHosInfo.medinsName,
            argtSeq: 255,
            userRoles: this.userForm.userRoles
          }

          await registerRequest(userParams).then(response => {
            this.$message({
              type: 'success',
              message: '新增用户成功'
            })
          })

          this.reset()
          // 因为是监听，所以改变下值，这样会改变 nextDisabled
          this.hospitalId = this.tempHosInfo.hospitalId
          this.nextDisabled = false
          // 模拟点击，试过elementui的树点击，不生效
          this.nodeClick({orgId: this.tempHosInfo.hospitalId, orgName: this.tempHosInfo.medinsName})
          this.tempHosInfo = {}
          this.$refs.userForm.resetFields()
        } else {
          this.$message({
            type: 'warning',
            message: '请填写完整'
          })
          return false
        }
      })
    },
    // 删除
    deleteInfo() {
      this.$confirm('当前操作会将组织机构和用户所有子集一起删除，是否删除？', '提示', {
        type: 'warning'
      })
          .then(() => {
            deleteUserConstruction({hospitalId: this.hospitalId}).then(res => {
              this.$message({
                type: 'success',
                message: '删除成功'
              })
              this.reset()
            })
          })
    },
    // 机构名称输入
    orgNameInput(val) {
      if (!val) {
        this.curOrgNode = {}
      }
    },
    // 新增组织
    addOrg() {
      let params = {
        prntDeptId: '',
        orgName: this.orgForm.orgName,
        teamleadType: '1',
        argtSeq: 3,
        deptHery: 3,
        deptIdPath: '',
        deptNamePath: '',
        yzbm: this.orgForm.yzbm,
        bizOrgType: this.orgForm.bizOrgType
      }
      if (Object.keys(this.curOrgNode).length > 0) {
        params.argtSeq = this.curOrgNode.argtSeq + 1
        params.deptHery = this.curOrgNode.deptHery + 1
        params.prntDeptId = this.curOrgNode.orgId
        params.deptIdPath = this.curOrgNode.deptIdPath
        params.deptNamePath = this.curOrgNode.deptNamePath
      }

      this.$refs.orgForm.validate((valid) => {
        if (valid) {
          addOrg(params).then(res => {
            this.$message({
              type: 'success',
              message: '新增组织架构成功'
            })
            this.showOrg = false
            this.$refs.orgForm.resetFields()
            this.curOrgNode = {}
            this.reset()
          })
        }
      })
    },
    orgNodeClick(node) {
      this.orgForm.deptNamePath = node.deptNamePath
      this.curOrgNode = node
    },
    // 新增医院
    addHos() {
      this.$refs.hosForm.validate((valid) => {
        if (valid) {
          insertHospitalInfo(this.hosForm).then(res => {
            this.$message({
              type: 'success',
              message: '新增医院成功'
            })
            this.showHos = false
            this.$refs.hosForm.resetFields()
            this.reset()
          })
        }
      })
    },
    // 打开右键菜单
    openAddMenu(e) {
      // this.addHosMenu.left = e.offsetX + 'px'
      // this.addHosMenu.top = e.offsetY + 'px'
      this.addHosMenu.left = e.layerX-20 + 'px'
      this.addHosMenu.top = e.layerY-30 + 'px'
      this.showAddHosMenu = true
    },
    closeMenu() {
      this.showAddHosMenu = false
    },
    // 清空所有环境
    truncateAllEnv() {
      this.$confirm('当前操作会删除【基础数据】，【流程生成数据】，【标杆数据】，【分值数据】，【APP数据】，是否确认删除？',
          '提示', {
            type: 'warning'
          })
          .then(() => {
            this.loading = true
            truncateAllEnv({hospitalId: this.hospitalId, truncateSql: this.truncateSql}).then(res => {
              this.$message({
                type: 'success',
                message: '环境已清空'
              })
              this.loading = false
              this.reset()
            }).catch(() => {
              this.loading = false
            })
          })
    },
    // 下一步
    next() {
      this.success()
    },
    // 成功
    success() {
      let params = {
        hospitalId: this.hospitalId,
        medinsName: this.medinsName,
        username: this.username
      }
      this.$emit('success', params)
    },
    reset() {
      this.queryHospital()
      this.$emit('reset', null)
    },
    // 提示警告
    messageWaring(message) {
      this.text = '警告'
      this.type = 'warning'
      this.desc = message
    }
  },
  watch: {
    hospitalId(val) {
      if (val) {
        this.nextDisabled = false
      } else {
        this.nextDisabled = true
      }
    },
    showAddHosMenu(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.custom-tree-node {
  display: flex;
  align-items: center;
  overflow: auto;
}

.custom-tree-node .node-name {
  max-width: 150px;
}

.contextmenu {
  margin-top: -5px;
  width: 100px;
  background: #fff;
  z-index: 3000;
  position: absolute;
  list-style-type: none;
  padding: 0px 0px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);

  li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;

    &:hover {
      background: #eee;
    }
  }
}

/deep/ .el-form-item__content {
  width: 80%;
}
</style>
