import request from '@/utils/request'
export function fetchList (params) {
  return request({
    url: '/ppsGroupKnowledgeBase/getList',
    method: 'post',
    params: params
  })
}

export function getCountByCoverRate (params) {
  return request({
    url: '/ppsGroupKnowledgeBase/getCountByCoverRate',
    method: 'post',
    params: params
  })
}

export function getCountByGroupClass (params) {
  return request({
    url: '/ppsGroupKnowledgeBase/getCountByGroupClass',
    method: 'post',
    params: params
  })
}
