import request from '@/utils/request'

// 查找科室下拉菜单树、以及所有码表值
export function queryEnableGroup (params) {
  return request({
    url: '/sysCommonConfigController/queryEnableGroup',
    method: 'post',
    params: params
  })
}

// 查找科室下拉菜单树、以及所有码表值
export function queryGroupType (params) {
  return request({
    url: '/sysCommonConfigController/queryGroupType',
    method: 'post',
    data: params
  })
}

/**
 * 导出excel
 * @param data
 * @returns {*}
 */
export function exportAllExcel (data) {
  return request({
    url: '/commonExportExcelController/export',
    method: 'post',
    responseType: 'blob',
    data: data
  })
}
