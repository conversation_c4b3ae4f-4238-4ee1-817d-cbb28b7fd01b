<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
              ref="somForm"
              show-date-range
              show-in-date-range
              show-se-date-range
              show-drg
              show-patient-num
              show-hos-dept
              showPagination
              :totalNum="total"
              :showCoustemContentTitle="true"
              :initTimeValueNotQuery="false"
              headerTitle="查询条件"
              :container="true"
              @query="fnQuery"
              @reset="refresh">

      <template slot="extendFormItems">
        <el-form-item label="参保类型">
          <drg-dict-select dicType="INSURANCE_TYPE" placeholder="请选择人群类别" v-model="queryForm.categories"
                           @change="getDataIsuue" />
        </el-form-item>

        <el-form-item label="费用区间" prop="costSection">
          <drg-dict-select v-model="queryForm.costSection" placeholder="请选择费用区间" dicType="CASE_TYPE"
                           @change="getDataIsuue" />
        </el-form-item>

        <el-form-item label="费用状态" prop="isLoss" @change="getDataIsuue">
          <el-select v-model="queryForm.isLoss" clearable placeholder="请选择" @change="getDataIsuue"
                     class="som-form-extend-form-item">
            <el-option v-for="item in isBoolean" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否异地" prop="isRemote">
          <drg-dict-select v-model="queryForm.isRemote" placeholder="请选择费用区间" dicType="INSUPLCADMDVS_TYPE"
                           @change="getDataIsuue" />
        </el-form-item>

        <!--        <el-form-item label="是否异地" prop="isRemote">-->
        <!--          <drg-dict-select dicType="BOOLEAN" placeholder="请选择" v-model="queryForm.isRemote" @change="getDataIsuue"/>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="省级医保" prop="insuPlaceType" @change="getDataIsuue">-->
        <!--          <el-select v-model="queryForm.insuPlaceType" clearable placeholder="请选择" @change="getDataIsuue"-->
        <!--                     class="som-form-extend-form-item">-->
        <!--            <el-option v-for="item in insurancePlaceType"-->
        <!--                       :key="item.value"-->
        <!--                       :label="item.label"-->
        <!--                       :value="item.value"></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
      </template>

      <!--      <template slot="buttons">-->
      <!--        <el-popconfirm-->
      <!--          confirm-button-text='确定'-->
      <!--          cancel-button-text='导出全部'-->
      <!--          icon="el-icon-info"-->
      <!--          icon-color="red"-->
      <!--          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">-->
      <!--          <el-button slot="reference" type="success">导出Excel</el-button>-->
      <!--        </el-popconfirm>-->
      <!--      </template>-->

      <template slot="buttons">
        <el-button type="success" @click="exportExcel" style="margin-right: 15px">导出Excel</el-button>
      </template>

      <!--      &lt;!&ndash; 其他内容 &ndash;&gt;-->
      <!--      <template slot="buttons">-->
      <!--        <div style="display: flex; justify-content: space-between; align-items: center;">-->
      <!--          <el-switch v-model="switchValue" v-show="this.openInsuPlcaeSelection" active-text="省医保" inactive-text="市医保"-->
      <!--                     style="margin-right: 15px;" @change="getDataIsuue"></el-switch>-->
      <!--        </div>-->
      <!--      </template>-->

      <template slot="contentTitle">
        <drg-title-line title="DRGs支付预测">
          <template slot="rightSide">
            <div style="display: flex;flex-direction: row;align-items: center">
              <div class="title-side">当前月份：
                <span class="title-side-value">{{ calculateYM }}</span>
              </div>
              <div class="title-side">（市医保）城乡测算单价：
                <span class="title-side-value">{{ calculatePriceCx }}元/点</span>
              </div>
              <div class="title-side">（市医保）城职测算单价：
                <span class="title-side-value">{{ calculatePriceCz }}元/点</span>
              </div>
              <div class="title-side" v-show="!this.openInsuPlcaeSelection">（市医保）城乡测算单价：
                <span class="title-side-value">{{ calculatePrice }}元/点</span>
              </div>
              <div class="title-side" v-show="this.openInsuPlcaeSelection">（省本级）城乡测算单价：
                <span class="title-side-value">{{ provCalculatePriceCx }}元/点</span>
              </div>
              <div class="title-side" v-show="this.openInsuPlcaeSelection">（省本级）城职测算单价：
                <span class="title-side-value">{{ provCalculatePriceCz }}元/点</span>
              </div>
              <el-button class="title-side-button" @click="fnModifyPrice">修改测算单价</el-button>
            </div>
          </template>
        </drg-title-line>
      </template>

      <template slot="containerContent">
        <div style="height: 40%;width: 100%;">
          <el-row class="som-wd-one-hundred">
            <el-col :span="6" class="som-h-one-hundred">
              <div class="som-h-fifty som-w-one-hundred">
                <!-- 总点数 -->
                <div class="som-w-one-hundred ptp-left-item">
                  <div class="ptp-left-item-title som-wd-one-hundred">
                    <div class="ptp-circle"></div>
                    总点数:
                  </div>
                  <div class="ptp-left-item-value" v-html="formatCostNum(totalPoint.total, true)">
                    {{ totalPoint.total | ifNullZero }}
                  </div>
                </div>
                <!-- 占比柱状图 -->
                <div class="ptp-left-item-draw" id="totalPoint"></div>
              </div>
              <div class="som-h-fifty som-w-one-hundred">
                <!-- 结算预测金额 -->
                <div class="som-w-one-hundred ptp-left-item">
                  <div class="ptp-left-item-title som-wd-one-hundred">
                    <div class="ptp-circle"></div>
                    结算预测金额:
                  </div>
                  <div class="ptp-left-item-value" v-html="formatCost(preCost.total, true)">
                  </div>
                </div>
                <!-- 占比柱状图 -->
                <div class="ptp-left-item-draw" id="sumfee"></div>
              </div>
            </el-col>
            <el-col :span="8" class="som-h-one-hundred som-el-form-item-margin-left">
              <div class="ptp-center-header">
                <ul class="ptp-center-header-ul">
                  <li class="ptp-center-header-li" :class="drg_pre_checked.drgPre.checked ? 'checked' : 'unchecked'">
                    <span class="ptp-center-header-li-content"
                          @click="fnChangedrgChecked(drg_pre_checked.drgPre.index)">DRGs支付预测</span>
                  </li>
                  <li class="ptp-center-header-li" :class="drg_pre_checked.sumfee.checked ? 'checked' : 'unchecked'">
                    <span class="ptp-center-header-li-content"
                          @click="fnChangedrgChecked(drg_pre_checked.sumfee.index)">正常病例预测</span>
                  </li>
                  <li class="ptp-center-header-li" :class="drg_pre_checked.payCount.checked ? 'checked' : 'unchecked'">
                    <span class="ptp-center-header-li-content"
                          @click="fnChangedrgChecked(drg_pre_checked.payCount.index)">超高病例预测</span>
                  </li>
                  <li class="ptp-center-header-li" :class="drg_pre_checked.mrType.checked ? 'checked' : 'unchecked'">
                    <span class="ptp-center-header-li-content"
                          @click="fnChangedrgChecked(drg_pre_checked.mrType.index)">超低病例预测</span>
                  </li>
                </ul>
              </div>
              <div class="ptp-center-content">

                <!-- DDRGs预付 -->
                <div class="ptp-center-content-pre">
                  <div class="som-w-fifty som-h-fifty ptp-center-content-item">
                    <div class="ptp-center-content-item-circle">
                      <div class="ptp-circle"></div>
                    </div>
                    <div>
                      <div class="ptp-center-content-title-value">
                        DRGs预付：<span class="ptp-center-content-samll-title"
                                       v-html="formatCost(preCost.total, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城职：<span v-html="formatCost(preCost.cz, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城乡：<span v-html="formatCost(preCost.cx, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        其他：<span v-html="formatCost(preCost.qt, true)"></span>
                      </div>
                    </div>
                  </div>

                  <!-- 原项目 -->
                  <div class="som-w-fifty som-h-fifty ptp-center-content-item">
                    <div class="ptp-center-content-item-circle">
                      <div class="ptp-circle"></div>
                    </div>
                    <div>
                      <div class="ptp-center-content-title-value">
                        原项目：<span class="ptp-center-content-samll-title"
                                     v-html="formatCost(originalProj.total, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城职：<span v-html="formatCost(originalProj.cz, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城乡：<span v-html="formatCost(originalProj.cx, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        其他：<span v-html="formatCost(originalProj.qt, true)"></span>
                      </div>
                    </div>
                  </div>

                  <!-- 预测结算差异 -->
                  <div class="som-w-fifty som-h-fifty ptp-center-content-item">
                    <div class="ptp-center-content-item-circle">
                      <div class="ptp-circle"></div>
                    </div>
                    <div>
                      <div class="ptp-center-content-title-value">
                        预测结算差异：<span class="ptp-center-content-samll-title"
                                           v-html="formatCost(preCostBalance.total, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城职：<span v-html="formatCost(preCostBalance.cz, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城乡：<span v-html="formatCost(preCostBalance.cx, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        其他：<span v-html="formatCost(preCostBalance.qt, true)"></span>
                      </div>
                    </div>
                  </div>

                  <!-- 收入差异比例 -->
                  <div class="som-w-fifty som-h-fifty ptp-center-content-item">
                    <div class="ptp-center-content-item-circle">
                      <div class="ptp-circle"></div>
                    </div>
                    <div>
                      <div class="ptp-center-content-title-value">
                        收入差异比例：<span class="ptp-center-content-samll-title">{{ balanceRate.total }}%</span>
                      </div>
                      <div class="ptp-center-content-value">
                        城职：<span>{{ balanceRate.cz }}%</span>
                      </div>
                      <div class="ptp-center-content-value">
                        城乡：<span>{{ balanceRate.cx }}%</span>
                      </div>
                      <div class="ptp-center-content-value">
                        其他：<span>{{ balanceRate.qt }}%</span>
                      </div>
                    </div>
                  </div>
                </div>

              </div>
            </el-col>
            <el-col :span="9" class="som-h-one-hundred">
              <drg-echarts :options="tendencyAnalysisOption" />
              <div style="height: 100%;width: 100%" id="tendencyAnalysis"></div>
            </el-col>
          </el-row>
        </div>
        <div style="height: 65%;width: 100%;position: relative;">
          <div style="height: 91%;width: 100%">
            <el-table :data="tableData" border :id="tableId" highlight-current-row size="mini"
                      :summary-method="getSummaries" show-summary :header-cell-style="{ 'text-align': 'center' }" height="100%"
                      ref="dataTable" @sort-change='sortChange' v-loading="tableLoading">
              <el-table-column prop="index" type="index" align="right" label="序号">
              </el-table-column>

              <drg-table-column prop="dept" align="left" width="150" label="是否结算">
              </drg-table-column>
              <drg-table-column prop="dept" align="left" width="150" label="科室名称">
              </drg-table-column>

              <el-table-column prop="patientID" label="病案号" align="left" width="110">
              </el-table-column>

              <el-table-column prop="name" label="姓名" align="center" width="90">
              </el-table-column>
              <el-table-column prop="isRemote" label="是否异地" align="center" width="90">
              </el-table-column>
              <el-table-column prop="attendingPhysician" label="主诊医生" align="center" width="90">
              </el-table-column>

              <drg-table-column prop="insuredType" width="90" dicType="INSURANCE_TYPE" align="center" label="参保类型">
              </drg-table-column>

              <!--              <drg-table-column prop="ydjy" align="center" width="100" label="异地患者标识">-->
              <!--              </drg-table-column>-->

              <el-table-column prop="drgCodg" label="DRG编码" align="center" width="100" :show-overflow-tooltip="true">
              </el-table-column>

              <el-table-column prop="drgName" label="DRG名称" align="left" width="200" :show-overflow-tooltip="true">
              </el-table-column>

              <el-table-column prop="admDiag" label="入院诊断" align="left" width="200" :show-overflow-tooltip="true">
              </el-table-column>

              <el-table-column prop="conditionDiagnosis" label="出院诊断" align="left" width="200"
                               :show-overflow-tooltip="true">
              </el-table-column>
              <el-table-column prop="settlementTime" label="结算时间" align="center" width="180">
              </el-table-column>
              <el-table-column prop="stsbFee" width="130" align="center" label="费用区间" sortable='custom'>
                <template slot-scope="scope">
                  <span
                    :class="[['1', '2'].includes(scope.row.stsbFee) ? 'som-color-error' : scope.row.stsbFee == '4' ? 'som-color-warning' : 'som-color-success']">
                    {{ $somms.getDictValueByType(scope.row.stsbFee, 'CASE_TYPE') }}
                  </span>
                </template>
              </el-table-column>

              <drg-table-column prop="refer_sco" width="100" align="center" label="基准点数" sortable='custom'>
              </drg-table-column>

              <drg-table-column prop="adjm_cof" width="100" align="center" label="调整系数" sortable='custom'>
              </drg-table-column>

              <drg-table-column prop="calculateScore" width="100" align="center"
                                :label="queryForm.feeStas == 1 ? '结算分值' : '预测分值'" sortable='custom'>
              </drg-table-column>

              <drg-table-column prop="runPrice" width="100" align="center" label="预测单价" sortable='custom'>
              </drg-table-column>

              <drg-table-column prop="hospCof" width="100" align="center" label="医院系数"
                                sortable="custom"></drg-table-column>

              <drg-table-column prop="addScore" width="100" align="center" label="加成分值" sortable='custom'>
              </drg-table-column>

              <drg-table-column prop="totlSco" width="120" align="center"
                                :label="queryForm.feeStas == 1 ? '拨付分值' : '预测总分值'" sortable='custom'>
              </drg-table-column>

              <drg-table-column prop="preCost" width="120" align="right"
                                :label="queryForm.feeStas == 1 ? '反馈金额' : '预付金额'" sortable='custom'>
              </drg-table-column>

              <drg-table-column prop="sumfee" width="120" align="right" label="总费用(元)" sortable='custom'>
              </drg-table-column>

              <drg-table-column prop="fbTotalCost" width="120" align="right" label="总费用(反馈)" sortable='custom'
                                v-if="queryForm.feeStas == 1">
              </drg-table-column>

              <el-table-column prop="profitAndLoss" width="120" align="right" label="盈亏" sortable='custom'>
                <template slot-scope="scope">
                  <span :class="[scope.row.profitAndLoss < 0 ? 'som-color-error' : 'som-color-success']">
                    {{ scope.row.profitAndLoss }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="费用状态" width="100px" align="center">
                <template slot-scope="scope">
                  <i class="som-icon-error-waring som-icon-big"
                     v-if="parseFloat(scope.row.preCost) - parseFloat(scope.row.sumfee) < 0"></i>
                  <i class="som-icon-success som-icon-big" v-else></i>
                </template>
              </el-table-column>
              <el-table-column prop="standardInHosCost" label="DRG标杆费用" align="center" width="180"/>
              <el-table-column prop="inHosDays" label="住院天数" align="center" width="180"/>
              <el-table-column prop="standardInHosDays" label="DRG标杆天数" align="center" width="180"/>
              <el-table-column prop="dayDifference" label="天数差值" align="center" width="180">
                <template slot-scope="scope">
                  <div v-if="scope.row.inHosDays==null||scope.row.inHosDays==''||scope.row.inHosDays==0||scope.row.standardInHosDays==null||scope.row.standardInHosDays==''||scope.row.standardInHosDays==0">
                    -
                  </div>
                  <div v-else-if="Number(scope.row.inHosDays)>Number(scope.row.standardInHosDays)">
                    <i class="el-icon-caret-bottom"></i>
                    <span style="font-size: 10px;color:#FF0000;font-weight: bold">
                      {{(Number(scope.row.standardInHosDays)-Number(scope.row.inHosDays)).toFixed(2)}}
                    </span>
                  </div>
                  <div v-else-if="Number(scope.row.inHosDays)<=Number(scope.row.standardInHosDays)">
                    <i class="el-icon-caret-top"></i>
                    <span style="font-size: 10px;color:#00CC00;font-weight: bold">
                      +{{(Number(scope.row.standardInHosDays)-Number(scope.row.inHosDays)).toFixed(2)}}
                    </span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="medicalCost" label="药品费" align="center" width="180"/>
              <el-table-column prop="medicalCostRate" label="药占比" align="center" width="180"/>
              <el-table-column prop="materialCost" label="耗材费" align="center" width="180"/>
              <el-table-column prop="materialCostRate" label="耗占比" align="center" width="180"/>
              <el-table-column prop="amount370100" label="企业补充医疗保险基金" align="center" width="180"/>
              <el-table-column prop="psnSelfpay" label="个人自付" align="center" width="180"/>
              <el-table-column prop="psnOwnpay" label="个人自费" align="center" width="180"/>
              <el-table-column prop="acctPay" label="个人账户" align="center" width="180"/>
              <el-table-column prop="psnCashpay" label="个人现金" align="center" width="180"/>

              <el-table-column prop="dscgWay" label="离院方式" align="center" width="180"/>
              <el-table-column prop="digsNum" label="疾病诊断个数" align="center" width="180"/>
              <el-table-column prop="oprNum" label="手术例数" align="center" width="180"/>
              <el-table-column prop="mainOperativeCodeAndName" label="主要手术" align="center" width="180"/>
              <el-table-column prop="oneLevelOprCodeAndName" label="一级手术" align="center" width="180"/>
              <el-table-column prop="oneLevelStanOprCodeAndName" label="一级标准手术" align="center" width="180"/>
              <el-table-column prop="twoLevelOprCodeAndName" label="二级手术" align="center" width="180"/>
              <el-table-column prop="twoLevelStanOprCodeAndName" label="二级标准手术" align="center" width="180"/>
              <el-table-column prop="threeLevelOprCodeAndName" label="三级手术" align="center" width="180"/>
              <el-table-column prop="threeLevelStanOprCodeAndName" label="三级标准手术" align="center" width="180"/>
              <el-table-column prop="fourLevelOprCodeAndName" label="四级手术" align="center" width="180"/>
              <el-table-column prop="fourLevelStanOprCodeAndName" label="四级标准手术" align="center" width="180"/>


              <!--   <el-table-column label="对比" width="100" align="center">
                   <template slot-scope="scope">
                     <el-button type="text" icon="som-icon-compare" @click="queryDetails(scope.row)"></el-button>
                   </template>
                 </el-table-column>-->
            </el-table>
          </div>
        </div>
        <!--        <el-dialog-->
        <!--          title="修改测算单价"-->
        <!--          :visible.sync="priceVisible"-->
        <!--          width="20%">-->
        <!--          <div style="display: flex;flex-direction: column">-->
        <!--            <div>-->
        <!--              城乡测算单价：-->
        <!--              <el-input-number v-model="calculatePriceCx"/>-->
        <!--            </div>-->
        <!--            <div style="margin-top: 1rem">-->
        <!--              城职测算单价：-->
        <!--              <el-input-number v-model="calculatePriceCz"/>-->
        <!--            </div>-->
        <!--            <div style="margin-top: 1rem">-->
        <!--              测算单价均值：-->
        <!--              <el-input-number v-model="calculatePrice"/>-->
        <!--            </div>-->
        <!--          </div>-->
        <el-dialog title="费用计算" ref="drgFeeForm" width="30%" :visible.sync="priceVisible">
          <el-form :model="drgFeeForm" size="mini" label-width="auto">
            <!-- 下拉框选择 -->
            <el-form-item label="选择模式" prop="mode">
              <el-select v-model="drgFeeForm.mode" placeholder="选择模式" @change="onModeChange">
                <el-option label="默认" value="default"></el-option>
                <el-option label="月度" value="monthly"></el-option>
              </el-select>
            </el-form-item>
            <!--            <el-form-item label="选择医保地" prop="mode">-->
            <!--              <el-select v-model="drgFeeForm.insuPlaceType" placeholder="选择参保地信息" @change="onModeChange">-->
            <!--                <el-option label="市医保" value="0"></el-option>-->
            <!--                <el-option label="省医保" value="1"></el-option>-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <el-form-item label="期号" prop="ym">
              <el-date-picker style="width:70%" v-model="drgFeeForm.ym" type="month" value-format="yyyyMM"
                              placeholder="选择期号" :disabled="drgFeeForm.mode !== 'monthly'"
                              @change="queryDipPrice">{{ this.drgFeeForm.ym }}
              </el-date-picker>
            </el-form-item>
            <el-form-item label="选择医保地" prop="mode"   >
              <el-select v-model="drgFeeForm.insuplcAdmdvs" placeholder="选择参保地信息"  @change="queryDipPrice">
                <el-option label="市医保" value="0"></el-option>
                <el-option label="省医保" value="1" v-show="this.openInsuPlcaeSelection" ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="cxPrice" label="城乡测算单价" required style="width:70%">
              <el-input placeholder="城乡测算单价" v-model="drgFeeForm.cxPrice" :disabled="false">
                {{ this.drgFeeForm.cxPrice }}</el-input>
            </el-form-item>
            <el-form-item prop="cxPrice" label="城镇测算单价" required style="width:70%">
              <el-input placeholder="城镇测算单价" v-model="drgFeeForm.czPrice" :disabled="false">
                {{ this.drgFeeForm.czPrice }}</el-input>
            </el-form-item>
            <el-form-item prop="price" label="测算单价均值" required style="width:70%">
              <el-input placeholder="测算单价均值" v-model="drgFeeForm.price" :disabled="false">
                {{ this.drgFeeForm.price }}</el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="priceVisible = false">取 消</el-button>
            <el-button type="primary" @click="fnSure" v-model="saveEnable">确 定</el-button>
          </span>
        </el-dialog>
      </template>

    </drg-form>
  </div>
</template>

<script>
import { queryDataIsuue, queryLikeDrgsByPram, queryMedicalDoctorSelectInput } from '@/api/common/drgCommon'
import { queryDrgData, updateDipConfigBatch, queryPrice, updatePriceAndResetFee } from '@/api/dataConfig/dipConfig'
import {
  formatCost,
  formatCostNum,
  formatMoney,
  formatRate,
  handleNumber,
  handleNumberCost,
  sortChange
} from '@/utils/common'
import { getList as queryPageData, getListPoint, getMonthData } from '@/api/hospitalAnalysis/drgsPreToPredict'
import echarts from 'echarts'
import { Message } from 'element-ui'
import moment from 'moment'

// import {updateDrg} from "../../../api/businessConfig/dipPriceConfigManage";

export default {
  name: 'predictPay',
  inject: ['reload'],

  data: () => ({
    openInsuPlcaeSelection: false,
    queryForm: {
      ym: null,
      deptType: '',
      dept: '',
      drgGroup: '',
      doctorType: '',
      dockerCode: '',
      categories: '',
      bah: '',
      recordType: '',
      pageNum: 1,
      pageSize: 200,
      cysj: null,
      cy_start_date: this.cy_start_date,
      cy_end_date: this.cy_end_date,
      costSection: '',
      isLoss: '',
      isRemote: '',
      insuPlaceType: 0,
      drCodg: '',
      feeStas: '0',
      seStartTime: '',
      seEndTime: '',
      inStartTime: '',
      inEndTime: '',
      diseType: ''
    },
    drgFeeForm: {
      mode: 'default',
      insuplcAdmdvs: '0',
      ym: '',
      type: 2,
      cxPrice: '',
      czPrice: '',
      price: ''
    },
    isBoolean: [
      { value: 0, label: '盈利' },
      { value: 1, label: '亏损' }
    ],
    insurancePlaceType: [
      { value: 0, label: '市医保地' },
      { value: 1, label: '省医保地' }
    ],
    tendencyAnalysisOption: {},
    pickerOptions: {
      shortcuts: [
        {
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }
      ]
    },
    total: null,
    totalPoint: {
      total: 0,
      cx: 0,
      cx_rate: 0,
      cz: 0,
      cz_rate: 0
    },
    preCost: {
      total: 0,
      cx: 0,
      cx_rate: 0,
      cz: 0,
      cz_rate: 0
    },
    originalProj: {
      total: 0,
      cx: 0,
      cz: 0
    },
    preCostBalance: {
      total: 0,
      cx: 0,
      cz: 0
    },
    balanceRate: {
      total: 0,
      cx: 0,
      cz: 0
    },
    dockerNames: [],
    drg_pre_checked: {
      drgPre: {
        index: 1,
        checked: true
      },
      sumfee: {
        index: 2,
        checked: false
      },
      payCount: {
        index: 3,
        checked: false
      },
      mrType: {
        index: 4,
        checked: false
      }
    },
    tableData: [],
    tableLoading: false,
    calculatePriceTotal: 0,
    calculateYM: '',
    calculatePriceCz: 0,
    calculatePriceCx: 0,
    calculatePriceQT: 0,
    calculatePrice: 0,
    provCalculatePriceCz:0,
    provCalculatePriceCx:0,
    provCalculatePrice:0,
    priceVisible: false,
    tableId: 'dataTableId',
    dataList: [],
    saveEnable: true
  }),
  mounted () {
    // window.console.log("初始化");
    if (this.$route.query.diseType) {
      this.queryForm.diseType = this.$route.query.diseType
      this.queryForm.drCodg = this.$route.query.drCodg
      // window.console.log("code1"+this.queryForm.drCodg);
      this.queryForm.seStartTime = this.$route.query.seStartTime
      this.queryForm.seEndTime = this.$route.query.seEndTime
      this.queryForm.inStartTime = this.$route.query.inStartTime
      this.queryForm.inEndTime = this.$route.query.inEndTime
      this.queryForm.cy_start_date = this.$route.query.cy_start_date
      this.queryForm.cy_end_date = this.$route.query.cy_end_date
    }

    this.initOpenInsuPlcaeSelection()
    this.queryForm.feeStas = String(this.$store.getters.feeStas)
    if (Object.keys(this.$route.query).length > 0) {
      if (this.$route.query.deptCode) {
        this.queryForm.deptCode = this.$route.query.deptCode
      }
      if (this.$route.query.icdCodg) {
        this.queryForm.icdCodg = this.$route.query.icdCodg
      }
      if (this.$route.query.drgCodg) {
        this.queryForm.drgCodg = this.$route.query.drgCodg
      }
      if (this.$route.query.begnDate && this.$route.query.expiDate) {
        this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.queryForm)
      }

      if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
        this.$refs.somForm.jumpTimeChange('in', this.$route.query, this.queryForm)
      }

      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.queryForm)
      }
    }
    this.$nextTick(() => {
      this.getDataIsuue()
    })
  },
  // 在vue生命周期updated中添加方法（使用该方法要给table里加ref="table"）
  updated: function () {
    this.$nextTick(() => {
      this.$refs['dataTable'].doLayout()
    })
  },
  methods: {

    queryPrice () {
      return queryPrice
    },
    initOpenInsuPlcaeSelection () {
      if (process.env.openInsuPlcaeSelection) {
        this.openInsuPlcaeSelection = true
      } else {
        this.openInsuPlcaeSelection = false
      }
    },
    formatCostNum,
    handleNumberCost,
    formatMoney,
    formatCost,
    handleNumber,
    formatRate,
    queryPageData,
    sortChange,
    getSummaries: function (param) {
      const { columns, data } = param
      const sums = []
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length
      }
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计'
          return
        }
        const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value))
        // if (index === 19 || index === 20) {
        //   sums[index] = calculations.sum(values).toFixed(2)
        // } else if (index === 15 || index === 16) {
        //   sums[index] = calculations.average(values)
        // } else if (index === 11 || index === 12 || index === 13 || index === 14 || index === 17 || index === 18) {
        //   sums[index] = calculations.average(values).toFixed(2)
        // } else {
        //   sums[index] = ' '
        // }
        if (index === 19 || index === 20 || index === 21) {
          sums[index] = calculations.sum(values).toFixed(2)
        }
      })
      return sums
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.dataAuth = true
      return params
    },
    allExcel () {
      this.$somms.exportExcelAll(this.getParams(), this.total, this.$refs.dataTable.$children, document.getElementById(this.tableId).children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DRG支付预测')
    },
    initOperations () {
      if (!this.queryForm.flag) {
        this.fnQuery()
        this.fnQueryDockerNames()
      }
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {

        this.queryForm.cy_start_date = response.data.cy_start_date
        this.queryForm.cy_end_date = response.data.cy_end_date
        this.queryForm.cysj = [this.queryForm.cy_start_date, this.queryForm.cy_end_date]
        this.queryForm.ym = response.data.cy_start_date


        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.queryForm.cy_start_date = this.$route.query.begnDate
          this.queryForm.cy_end_date = this.$route.query.expiDate
          this.queryForm.cysj = [this.$route.query.begnDate, this.$route.query.expiDate]
          this.queryForm.ym = this.$route.query.begnDate
        }
        if (this.$route.query.drgCodg) {
          this.queryForm.drgGroup = this.$route.query.drgCodg
        }
        if (this.$route.query.deptCode) {
          this.queryForm.dept = this.$route.query.deptCode
        }
        if (this.$route.query.drCodg) {
          this.queryForm.drCodg = this.$route.query.drCodg
        }
        if (this.$route.query.isLoss) {
          this.queryForm.isLoss = Number(this.$route.query.isLoss)
        }

        if (this.$route.query.isRemote) {
          this.queryForm.isRemote = Number(this.$route.query.isRemote)
        }
        if (this.$route.query.code) {
          this.queryForm.bah = this.$route.query.code
        }

        if (this.$route.query.costSection) {
          this.queryForm.costSection = this.$route.query.costSection
        }

        if (this.$route.query.diseType) {
          this.queryForm.diseType = this.$route.query.diseType
        }
        if (this.$route.query.seStartTime) {
          this.queryForm.seStartTime = this.$route.query.seStartTime
        }
        if (this.$route.query.seEndTime) {
          this.queryForm.seEndTime = this.$route.query.seEndTime
        }
        if (this.$route.query.inStartTime) {
          this.queryForm.inStartTime = this.$route.query.inStartTime
        }
        if (this.$route.query.inEndTime) {
          this.queryForm.inEndTime = this.$route.query.inEndTime
        }
        if (this.$route.query.cy_start_date) {
          this.queryForm.cy_start_date = this.$route.query.cy_start_date
        }
        if (this.$route.query.cy_end_date) {
          this.queryForm.cy_end_date = this.$route.query.cy_end_date
        }
        // 查询数据
        this.initOperations()
        this.getData()
      })
    },
    dateChangeCysj (val) {
      if (val) {
        this.queryForm.cy_start_date = val[0]
        this.queryForm.cy_end_date = val[1]
      } else {
        this.queryForm.cy_start_date = null
        this.queryForm.cy_end_date = null
      }
      this.fnQuery()
    },
    getData () {
      let params = this.queryForm
      // 默认是当前月份
      // if(this.$query.cy_start_date){
      //   params.cy_start_date = this.queryForm.cy_start_date;
      // }else{
      //   params.cy_start_date = this.queryForm.begnDate;
      // }
      // params.cy_start_date = this.queryForm.begnDate;
      params.cy_start_date = this.queryForm.cy_start_date
      params.ym = this.queryForm.begnDate
      if (params.ym) {
        params.ym = moment(params.ym).format('yyyyMM')
      }
      // 默认当前月份
      // if(this.$query.cy_end_date){
      //   params.cy_end_date = this.queryForm.cy_end_date;
      // }else{
      //   params.cy_end_date = this.queryForm.expiDate
      // }
      // params.cy_end_date = this.queryForm.expiDate
      params.cy_end_date = this.queryForm.cy_end_date

      params.dept = this.queryForm.deptCode
      params.drgGroup = this.queryForm.drgCodg
      params.bah = this.queryForm.medcasCodg
      // params.dept = this.queryForm.isRemote

      queryDrgData(params).then(data => {
        this.dataList = data.data.list
        for (let item of this.dataList) {
          if (item.key === 'CX_PRICE' && item.insuplcAdmdvs === '市医保地') {
            this.calculatePriceCx = item.value
          }
          if (item.key === 'CZ_PRICE' && item.insuplcAdmdvs === '市医保地') {
            this.calculatePriceCz = item.value
          }
          if (item.key === 'PRICE'&& item.insuplcAdmdvs === '市医保地') {
            this.calculatePrice = item.value
          }
          if (item.key === 'CX_PRICE' && item.insuplcAdmdvs === '省医保地') {
            this.provCalculatePriceCx = item.value
          }
          if (item.key === 'CZ_PRICE' && item.insuplcAdmdvs === '省医保地') {
            this.provCalculatePriceCz = item.value
          }
          if (item.key === 'PRICE'&& item.insuplcAdmdvs === '省医保地') {
            this.provCalculatePrice = item.value
          }
          if (item.ym === '') {
            this.calculateYM = '默认'
          }
        }
      })
    },
    getNewData (val) {
      let params = this.queryForm
      // 默认是当前月份
      // if (this.$query.cy_start_date) {
      //   params.cy_start_date = this.queryForm.cy_start_date;
      // } else {
      //   params.cy_start_date = this.queryForm.begnDate;
      // }
      // params.cy_start_date = this.queryForm.begnDate
      params.cy_start_date = this.queryForm.cy_start_date
      if (val == 1) {
        params.ym = this.queryForm.begnDate
      } else if (val == 2) {
        params.ym = this.queryForm.inStartTime
      } else if (val == 3) {
        params.ym = this.queryForm.seStartTime
      }
      if (params.ym) {
        params.ym = moment(params.ym).format('yyyyMM')
        this.calculateYM = params.ym
      }
      // 默认当前月份
      // if (this.$query.cy_end_date) {
      //   params.cy_end_date = this.queryForm.cy_end_date;
      // } else {
      //   params.cy_end_date = this.queryForm.expiDate;
      // }
      // params.cy_end_date = this.queryForm.expiDate
      parms.cy_end_date = this.queryForm.cy_end_date
      params.dept = this.queryForm.deptCode
      params.drgGroup = this.queryForm.drgCodg
      params.bah = this.queryForm.medcasCodg
      params.isRemote = this.queryForm.isRemote
      if (params.flag == true) {
        params.drCodg = ''
      }
      queryDrgData(params).then(data => {
        this.dataList = data.data.list
        for (let item of this.dataList) {
          if (item.key === 'CX_PRICE') {
            this.calculatePriceCx = item.value
          }
          if (item.key === 'CZ_PRICE') {
            this.calculatePriceCz = item.value
          }
          if (item.key === 'PRICE') {
            this.calculatePrice = item.value
          }
          if (item.ym === '') {
            this.calculateYM = '默认'
          }
        }
      })
    },
    fnQuery () {
      this.drg_pre_checked.drgPre.checked = true
      this.drg_pre_checked.sumfee.checked = false
      this.drg_pre_checked.payCount.checked = false
      this.drg_pre_checked.mrType.checked = false
      let params = this.queryForm
      params.bah = this.queryForm.medcasCodg
      params.drgGroup = this.queryForm.drgCodg
      params.dataAuth = true

      if (params.ym) {
        params.ym = moment(params.ym).format('yyyy-MM')
      }
      // 默认是当前月份
      if (this.$route.query.cy_start_date) {
        params.cy_start_date = this.queryForm.cy_start_date
      } else {
        params.cy_start_date = this.queryForm.begnDate
      }
      // //默认当前月份
      if (this.$route.query.cy_end_date) {
        params.cy_end_date = this.queryForm.cy_end_date
      } else {
        params.cy_end_date = this.queryForm.expiDate
      }
      // params.cy_start_date = this.queryForm.begnDate
      // params.cy_end_date = this.queryForm.expiDate
      // params.cy_start_date = this.queryForm.cy_start_date;
      // params.cy_end_date = this.queryForm.cy_end_date;

      this.tableLoading = true
      queryPageData(params).then(res => {
        window.console.log('时间2:' + this.queryForm.cy_start_date)
        // window.console.log("出院时间"+this.queryForm.cy_start_date);
        this.tableData = res.data.list
        this.total = res.data.total
        this.tableLoading = false
        this.getNewData(this.queryForm.inHosFlag)
      }).catch(() => {
        this.tableLoading = false
      })
      this.queryPayForecast(1)
    },
    // DRG组选中
    fnDrgGroupSelect (item) {
      this.queryForm.drgGroup = item.drgsCode
    },
    fnModifyPrice () {
      if (this.calculateYM === '默认') {
        this.drgFeeForm.mode = 'default'
      }
      this.drgFeeForm.type = 2

      if (this.drgFeeForm.mode != 'default') {
        this.setPreviousMonth()
      } else {
        this.drgFeeForm.ym = ''
      }
      queryPrice(this.drgFeeForm).then((result) => {
        this.drgFeeForm.cxPrice = result.data.cxPrice
        this.drgFeeForm.czPrice = result.data.czPrice
        this.drgFeeForm.price = result.data.price
        if (result.data.ym || result.data.ym === '') {
          this.drgFeeForm.mode = 'default'
          this.drgFeeForm.ym = ''
        } else {
          this.drgFeeForm.mode = 'monthly'
        }
      })
      this.priceVisible = true
      // TODO 修改单价
    },
    setPreviousMonth () {
      // 获取当前日期
      const currentDate = new Date()
      // 将月份减去1，获取上一个月
      currentDate.setMonth(currentDate.getMonth() - 1)
      // 格式化为 YYYYMM 的字符串
      const year = currentDate.getFullYear()
      const month = String(currentDate.getMonth() + 1).padStart(2, '0') // 月份从0开始，所以加1，并确保是两位数字
      this.drgFeeForm.ym = `${year}${month}`
    },
    onModeChange (value) {
      // 可以在这里做一些额外的操作，比如清空期号等
      if (value !== 'monthly') {
        this.drgFeeForm.ym = '' // 如果不是月度模式，清空期号
      } else {
        this.setPreviousMonth()
      }
    },

    // 月份改变 对应的数据也要发生改变
    queryDipPrice (value) {
      this.drgFeeForm.type = 2
      this.drgFeeForm.ym = value

      queryPrice(this.drgFeeForm).then((result) => {
        if (result.data != null) {
          this.drgFeeForm.cxPrice = result.data.cxPrice
          this.drgFeeForm.czPrice = result.data.czPrice
          this.drgFeeForm.price = result.data.price
        } else {
          this.drgFeeForm.cxPrice = ''
          this.drgFeeForm.czPrice = ''
          this.drgFeeForm.price = ''
        }
        this.tableLoading = false
      })
    },
    fnSure () {
      let month = this.drgFeeForm.ym
      if (this.saveEnable != null) {
        if (this.drgFeeForm.mode != 'monthly') {
          month = '默认'
          this.drgFeeForm.ym = ''
        }
        // let loadingInstance = null
        this.$confirm('是否确认修改' + month + '的各项单价，并重新计算患者费用？')
          .then(_ => {
            if (!this.validatePrices()) {
              this.$message({
                message: '请检查输入的参数不为空且可转为有效数字。',
                type: 'warning'
              })
              return // 终止请求
            } updatePriceAndResetFee(this.drgFeeForm).then((result) => {
              this.tableData = result.data
              this.priceVisible = false
              this.fnQuery()
              this.$message({
                message: '已完成单价修改与费用计算',
                type: 'success'
              })
              this.resetFeeForm()
            })
          })
          .catch(_ => {
            this.priceVisible = false
          })
      }
    },
    validatePrices () {
      if (!this.drgFeeForm || !this.drgFeeForm.price ||
        !this.drgFeeForm.czPrice || !this.drgFeeForm.cxPrice || !this.drgFeeForm.type
      ) {
        return false
      }
      const prices = [this.drgFeeForm.cxPrice, this.drgFeeForm.czPrice, this.drgFeeForm.price]
      const valid = prices.every(price => {
        const num = parseFloat(price)
        return !isNaN(num) && Number.isFinite(num) && !isNaN(num.toFixed(2)) // 先确保是有效数字，然后转为两位小数
      })
      if (valid) {
        this.drgFeeForm.cxPrice = parseFloat(this.drgFeeForm.cxPrice).toFixed(2)
        this.drgFeeForm.czPrice = parseFloat(this.drgFeeForm.czPrice).toFixed(2)
        this.drgFeeForm.price = parseFloat(this.drgFeeForm.price).toFixed(2)
      }
      return valid // 返回是否有效的结果
    },
    resetFeeForm () {
      this.drgFeeForm = {
        isRemote:'',
        ym: '',
        type: 2,
        cxPrice: '',
        czPrice: '',
        price: ''
      }
    },
    fnChangedrgChecked (index) {
      Object.keys(this.drg_pre_checked).forEach(key => {
        if (this.drg_pre_checked[key].index == index) {
          this.drg_pre_checked[key].checked = true
        } else {
          this.drg_pre_checked[key].checked = false
        }
      })
      this.queryPayForecast(index)
    },
    queryPayForecast (index) {
      let params = this.getParams()
      this.getMonth()
      if (params.ym) {
        params.ym = moment(params.ym).format('yyyy-MM')
      }
      getListPoint(params).then(data => {
        // 点数及占比
        if (index == 1) {
          this.totalPoint.cz = data.cityPoint.point.toFixed(2)
          this.totalPoint.cx = data.countrysidePoint.point.toFixed(2)
          this.totalPoint.qt = data.QTPointPoint.point.toFixed(2)
          this.totalPoint.total = data.balancePoint.point.toFixed(2)
          if (data.countrysidePoint.point == 0) {
            this.totalPoint.cx_rate = Number(0).toFixed(2)
          } else {
            this.totalPoint.cx_rate = ((this.totalPoint.cx / this.totalPoint.total) * 100).toFixed(2)
          }
          if (data.cityPoint.point == 0) {
            this.totalPoint.cz_rate = Number(0).toFixed(2)
          } else {
            this.totalPoint.cz_rate = ((this.totalPoint.cz / this.totalPoint.total) * 100).toFixed(2)
          }
          if (data.QTPointPoint.point == 0) {
            this.totalPoint.qt_rate = Number(0).toFixed(2)
          } else {
            this.totalPoint.qt_rate = ((this.totalPoint.qt / this.totalPoint.total) * 100).toFixed(2)
          }
        } else {
          if (index == 2) {
            if (data.cityPoint.ncpoint == 0) {
              this.totalPoint.cz = Number(0).toFixed(2)
            } else {
              this.totalPoint.cz = parseFloat(data.cityPoint.ncpoint).toFixed(2)
            }
            if (data.countrysidePoint.ncpoint == 0) {
              this.totalPoint.cx = Number(0).toFixed(2)
            } else {
              this.totalPoint.cx = parseFloat(data.countrysidePoint.ncpoint).toFixed(2)
            }
            if (data.QTPointPoint.ncpoint == 0) {
              this.totalPoint.qt = Number(0).toFixed(2)
            } else {
              this.totalPoint.qt = parseFloat(data.QTPointPoint.ncpoint).toFixed(2)
            }
          }
          if (index == 3) {
            if (data.cityPoint.upPoint == 0) {
              this.totalPoint.cz = Number(0).toFixed(2)
            } else {
              this.totalPoint.cz = parseFloat(data.cityPoint.upPoint).toFixed(2)
            }
            if (data.countrysidePoint.upPoint == 0) {
              this.totalPoint.cx = Number(0).toFixed(2)
            } else {
              this.totalPoint.cx = parseFloat(data.countrysidePoint.upPoint).toFixed(2)
            }
            if (data.QTPointPoint.upPoint == 0) {
              this.totalPoint.qt = Number(0).toFixed(2)
            } else {
              this.totalPoint.qt = parseFloat(data.QTPointPoint.upPoint).toFixed(2)
            }
          }
          if (index == 4) {
            if (data.cityPoint.lowPoint == 0) {
              this.totalPoint.cz = Number(0).toFixed(2)
            } else {
              this.totalPoint.cz = parseFloat(data.cityPoint.lowPoint).toFixed(2)
            }
            if (data.countrysidePoint.lowPoint == 0) {
              this.totalPoint.cx = Number(0).toFixed(2)
            } else {
              this.totalPoint.cx = parseFloat(data.countrysidePoint.lowPoint).toFixed(2)
            }
            if (data.QTPointPoint.lowPoint == 0) {
              this.totalPoint.qt = Number(0).toFixed(2)
            } else {
              this.totalPoint.qt = parseFloat(data.QTPointPoint.lowPoint).toFixed(2)
            }
          }
          this.totalPoint.total = (parseFloat(this.totalPoint.cz) + parseFloat(this.totalPoint.cx) + parseFloat(this.totalPoint.qt)).toFixed(2)
          if (this.totalPoint.cx == 0) {
            this.totalPoint.cx_rate = Number(0).toFixed(2)
          } else {
            this.totalPoint.cx_rate = ((this.totalPoint.cx / this.totalPoint.total) * 100).toFixed(2)
          }
          if (this.totalPoint.cz == 0) {
            this.totalPoint.cz_rate = Number(0).toFixed(2)
          } else {
            this.totalPoint.cz_rate = ((this.totalPoint.cz / this.totalPoint.total) * 100).toFixed(2)
          }
          if (this.totalPoint.qt == 0) {
            this.totalPoint.qt_rate = Number(0).toFixed(2)
          } else {
            this.totalPoint.qt_rate = ((this.totalPoint.qt / this.totalPoint.total) * 100).toFixed(2)
          }
        }
        this.drawTotalPointBar()
        // 预测金额
        if (index == 1) {
          if (data.cityPoint.predictCost == 0) {
            this.preCost.cz = 0
          } else {
            this.preCost.cz = data.cityPoint.predictCost
          }
          if (data.countrysidePoint.predictCost == 0) {
            this.preCost.cx = 0
          } else {
            this.preCost.cx = data.countrysidePoint.predictCost
          }
          if (data.QTPointPoint.predictCost == 0) {
            this.preCost.qt = 0
          } else {
            this.preCost.qt = data.QTPointPoint.predictCost
          }
          this.preCost.total = (this.preCost.cz + this.preCost.cx + this.preCost.qt).toFixed(2)
          if (this.preCost.cz == 0) {
            this.preCost.cz_rate = 0
          } else {
            this.preCost.cz_rate = ((this.preCost.cz / this.preCost.total) * 100).toFixed(2)
          }
          if (this.preCost.cx == 0) {
            this.preCost.cx_rate = 0
          } else {
            this.preCost.cx_rate = ((this.preCost.cx / this.preCost.total) * 100).toFixed(2)
          }
          if (this.preCost.qt == 0) {
            this.preCost.qt_rate = 0
          } else {
            this.preCost.qt_rate = ((this.preCost.qt / this.preCost.total) * 100).toFixed(2)
          }
        } else {
          if (index == 2) {
            if (data.cityPoint.ncpoint == 0) {
              this.preCost.cz = 0
            } else {
              this.preCost.cz = (data.cityPoint.ncpoint * data.cityPoint.czPrice)
            }
            if (data.countrysidePoint.ncpoint == 0) {
              this.preCost.cx = 0
            } else {
              this.preCost.cx = (data.countrysidePoint.ncpoint * data.countrysidePoint.czPrice)
            }
            if (data.QTPointPoint.ncpoint == 0) {
              this.preCost.qt = 0
            } else {
              this.preCost.qt = (data.QTPointPoint.ncpoint * data.QTPointPoint.price)
            }
          }
          if (index == 3) {
            if (data.cityPoint.upPoint == 0) {
              this.preCost.cz = 0
            } else {
              this.preCost.cz = (data.cityPoint.upPoint * data.cityPoint.czPrice)
            }
            if (data.countrysidePoint.upPoint == 0) {
              this.preCost.cx = 0
            } else {
              this.preCost.cx = (data.countrysidePoint.upPoint * data.countrysidePoint.czPrice)
            }
            if (data.QTPointPoint.upPoint == 0) {
              this.preCost.qt = 0
            } else {
              this.preCost.qt = (data.QTPointPoint.upPoint * data.QTPointPoint.price)
            }
          }
          if (index == 4) {
            if (data.cityPoint.lowPoint == 0) {
              this.preCost.cz = 0
            } else {
              this.preCost.cz = (data.cityPoint.lowPoint * data.cityPoint.czPrice)
            }
            if (data.countrysidePoint.lowPoint == 0) {
              this.preCost.cx = 0
            } else {
              this.preCost.cx = (data.countrysidePoint.lowPoint * data.countrysidePoint.czPrice)
            }
            if (data.QTPointPoint.lowPoint == 0) {
              this.preCost.qt = 0
            } else {
              this.preCost.qt = (data.QTPointPoint.lowPoint * data.QTPointPoint.price)
            }
          }
          this.preCost.total = (this.preCost.cz + this.preCost.cx + this.preCost.qt).toFixed(2)
          if (this.preCost.cz == 0) {
            this.preCost.cz_rate = 0
          } else {
            this.preCost.cz_rate = ((this.preCost.cz / this.preCost.total) * 100).toFixed(2)
          }
          if (this.preCost.cx == 0) {
            this.preCost.cx_rate = 0
          } else {
            this.preCost.cx_rate = ((this.preCost.cx / this.preCost.total) * 100).toFixed(2)
          }
          if (this.preCost.qt == 0) {
            this.preCost.qt_rate = 0
          } else {
            this.preCost.qt_rate = ((this.preCost.qt / this.preCost.total) * 100).toFixed(2)
          }
        }
        // this.drawTotalCostBar()
        if (index == 1) {
          this.originalProj.cz = data.cityPoint.sumfee
          this.originalProj.cx = data.countrysidePoint.sumfee
          this.originalProj.qt = data.QTPointPoint.sumfee
          this.originalProj.total = data.balancePoint.sumfee
        } else {
          if (index == 2) {
            this.originalProj.cz = data.countrysidePoint.cityPoint.nccost
            this.originalProj.cx = data.countrysidePoint.nccost
            this.originalProj.qt = data.QTPointPoint.nccost
          }
          if (index == 3) {
            this.originalProj.cz = data.cityPoint.upCost
            this.originalProj.cx = data.countrysidePoint.upCost
            this.originalProj.qt = data.QTPointPoint.upCost
          }
          if (index == 4) {
            this.originalProj.cz = data.cityPoint.lowCost
            this.originalProj.cx = data.countrysidePoint.lowCost
            this.originalProj.qt = data.QTPointPoint.lowCost
          }
          this.originalProj.total = this.originalProj.cz + this.originalProj.cx + this.originalProj.qt
        }
        // 差值
        this.preCostBalance.cz = this.preCost.cz - this.originalProj.cz
        this.preCostBalance.cx = this.preCost.cx - this.originalProj.cx
        this.preCostBalance.qt = this.preCost.qt - this.originalProj.qt
        this.preCostBalance.total = this.preCost.total - this.originalProj.total
        if (this.preCost.cz && this.originalProj.cz) {
          if (this.preCost.cz == 0) {
            this.balanceRate.cz = -100
          } else {
            this.balanceRate.cz = (((this.preCost.cz - this.originalProj.cz) / this.preCost.cz) * 100).toFixed(2)
          }
        } else {
          this.balanceRate.cz = 0
        }
        if (this.preCost.cx && this.originalProj.cx) {
          if (this.preCost.cz == 0) {
            this.balanceRate.cx = -100
          } else {
            this.balanceRate.cx = (((this.preCost.cx - this.originalProj.cx) / this.preCost.cx) * 100).toFixed(2)
          }
        } else {
          this.balanceRate.cx = 0
        }
        if (this.preCost.qt && this.originalProj.qt) {
          if (this.preCost.qt == 0) {
            this.balanceRate.qt = -100
          } else {
            this.balanceRate.qt = (((this.preCost.qt - this.originalProj.qt) / this.preCost.qt) * 100).toFixed(2)
          }
        } else {
          this.balanceRate.qt = 0
        }
        if (this.preCost.total && this.originalProj.total) {
          if (this.preCost.cz == 0) {
            this.balanceRate.total = -100
          } else {
            this.balanceRate.total = (((this.preCost.total - this.originalProj.total) / this.preCost.total) * 100).toFixed(2)
          }
        } else {
          this.balanceRate.total = 0
        }
      })
    },
    drawTotalCostBar () {
      // 基于准备好的dom，初始化echarts实例
      let sumfee = echarts.init(document.getElementById('totalCost'))
      let _this = this
      let option = {
        // color: ['#81C1DC', '#3488AD', '#00557C'],//设置颜色
        color: ['#83ced0', '#f2855c'],
        // 顶部显示
        legend: [
          {
            left: '-1.2%',
            bottom: '0%',
            itemHeight: 11,
            itemWidth: 20,
            formatter: function (name) {
              return name + '：' + (_this.handleNumber(_this.preCost.cx) ? _this.handleNumber(_this.preCost.cx) : 0)
            },
            data: ['城乡']
          },
          {
            left: '35%',
            bottom: '0%',
            itemHeight: 11,
            itemWidth: 20,
            formatter: function (name) {
              // return name + "：" + (_this.handleNumber(_this.preCost.qt) ? _this.handleNumber(_this.preCost.qt.toFixed(2)) : 0)
              return name + '：' + (_this.handleNumber(_this.preCost.qt) ? _this.handleNumber(_this.preCost.qt) : 0)
            },
            textStyle: {
              fontSize: _this.fontSize
            },
            data: ['其他']
          },
          {
            right: '-5%',
            bottom: '0%',
            itemHeight: 11,
            itemWidth: 20,
            formatter: function (name) {
              return name + '：' + (_this.handleNumber(_this.preCost.cz) ? _this.handleNumber(_this.preCost.cz) : 0)
            },
            data: ['城职']
          }
        ],
        // 设置下方图标的位置
        grid: {
          left: '0%',
          right: '0%',
          top: '2%',
          bottom: '30%',
          containLabel: true
        },
        // 设置X轴的参数
        xAxis: [
          {
            type: 'value',
            max: 100, // 设置最大值是多少
            splitNumber: 5, // 设置分几段显示
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            show: true
          }
        ],
        // 设置Y轴的参数
        yAxis: {
          splitLine: {
            show: false
          },
          axisLine: { // y轴
            show: false
          },
          axisTick: {
            show: false
          },
          type: 'category'
        },
        // 设置每个item的参数
        series: [{
          name: '城乡',
          type: 'bar',
          barWidth: 20,
          stack: '点数',
          label: {
            show: true,
            position: 'inside', // 在左边显示
            formatter: '{c}%'// 给计算后的数值添加%
          },
          data: this.preCost.cx_rate == 0 ? [] : [this.preCost.cx_rate]// 计算对应的百分比
        },
          {
            name: '其他',
            type: 'bar',
            stack: '点数',
            label: {
              show: true,
              position: 'inside', // 在右边显示
              formatter: '{c}%'
            },
            data: this.preCost.qt_rate == 0 ? [] : [this.preCost.qt_rate]
          },
          {
            name: '城职',
            type: 'bar',
            stack: '点数',
            label: {
              show: true,
              position: 'inside', // 在右边显示
              formatter: '{c}%'
            },
            data: this.preCost.cz_rate == 0 ? [] : [this.preCost.cz_rate]
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      sumfee.setOption(option)
    },
    drawTotalPointBar () {
      // 基于准备好的dom，初始化echarts实例
      let totalPoint = echarts.init(document.getElementById('totalPoint'))
      let _this = this
      let option = {
        // color: ['#81C1DC', '#3488AD', '#00557C'],//设置颜色
        color: ['#83ced0', '#f2855c'],
        // 顶部显示
        legend: [
          {
            left: '-1.2%',
            bottom: '0%',
            itemHeight: 11,
            itemWidth: 20,
            formatter: function (name) {
              return name + '：' + (_this.handleNumberCost(_this.totalPoint.cx) ? _this.handleNumberCost(_this.totalPoint.cx) : 0)
            },
            data: ['城乡']
          },
          {
            right: '-5%',
            itemHeight: 11,
            itemWidth: 20,
            bottom: '0%',
            formatter: function (name) {
              return name + '：' + (_this.handleNumberCost(_this.totalPoint.cz) ? _this.handleNumberCost(_this.totalPoint.cz) : 0)
            },
            data: ['城职']
          },
          {
            left: '35%',
            itemHeight: 11,
            itemWidth: 20,
            bottom: '0%',
            formatter: function (name) {
              return name + '：' + (_this.handleNumberCost(_this.totalPoint.qt) ? _this.handleNumberCost(_this.totalPoint.qt) : 0)
            },
            data: ['其他']
          }
        ],
        // 设置下方图标的位置
        grid: {
          left: '0%',
          right: '0%',
          top: '2%',
          bottom: '30%',
          containLabel: true
        },
        // 设置X轴的参数
        xAxis: [
          {
            type: 'value',
            max: 100, // 设置最大值是多少
            splitNumber: 5, // 设置分几段显示
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            show: true
          }
        ],
        // 设置Y轴的参数
        yAxis: {
          splitLine: {
            show: false
          },
          axisLine: { // y轴
            show: false
          },
          axisTick: {
            show: false
          },
          type: 'category'
        },
        // 设置每个item的参数
        series: [{
          name: '城乡',
          type: 'bar',
          barWidth: 20,
          stack: '点数',
          label: {
            show: true,
            position: 'inside', // 在左边显示
            formatter: '{c}%'// 给计算后的数值添加%
          },
          data: this.totalPoint.cx_rate == 0 ? [] : [this.totalPoint.cx_rate] // 计算对应的百分比
        },
          {
            name: '其他',
            type: 'bar',
            stack: '点数',
            label: {
              show: true,
              position: 'inside',
              formatter: '{c}%'
            },
            data: this.totalPoint.qt_rate == 0 ? [] : [this.totalPoint.qt_rate]
          },
          {
            name: '城职',
            type: 'bar',
            stack: '点数',
            label: {
              show: true,
              position: 'inside', // 在右边显示
              formatter: '{c}%'
            },
            data: this.totalPoint.cz_rate == 0 ? [] : [this.totalPoint.cz_rate]
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      totalPoint.setOption(option)
    },
    drawTendencyAnalysis () {
      // 基于准备好的dom，初始化echarts实例
      // let tendencyAnalysis = echarts.init(document.getElementById('tendencyAnalysis'));
      let option = {
        color: ['#83ced0', '#f2855c'],
        title: {
          text: 'DRGs结算预测趋势分析',
          left: '5%',
          textStyle: {
            fontSize: Number(getComputedStyle(document.documentElement).getPropertyValue('--biggerSize').replace('px', ' '))
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        grid: {
          bottom: '10%'
        },
        legend: {
          right: '1%',
          textStyle: {
            fontSize: Number(getComputedStyle(document.documentElement).getPropertyValue('--smallSize').replace('px', ' '))
          },
          data: ['原项目支付金额', 'DRG支付金额', '差异比例']
        },
        xAxis: [
          {
            type: 'category',
            splitLine: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            data: this.monthList,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位/万元',
            min: 0,
            axisTick: {
              show: false
            },
            axisLine: { // y轴
              show: false
            },
            splitLine: {
              show: false
            }
          },
          {
            type: 'value',
            // min: 0,
            // interval: 1,
            axisTick: {
              show: false
            },
            axisLine: { // y轴
              show: false
            },
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: '原项目支付金额',
            type: 'bar',
            barWidth: 20,
            data: this.totalList
          },
          {
            name: 'DRG支付金额',
            type: 'bar',
            barWidth: 20,
            data: this.drgList
          },
          {
            name: '差异比例',
            type: 'line',
            yAxisIndex: 1,
            data: this.cybList
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      // tendencyAnalysis.setOption(option);
      this.tendencyAnalysisOption = option
    },
    // 查询前几月数据
    getMonth () {
      let params = Object.assign({}, this.queryForm)
      getMonthData(params).then((monthData) => {
        this.monthList = [0]
        this.totalList = [0]
        this.drgList = [0]
        this.cybList = [0]
        if (monthData.controlMap.monthList != 0) {
          this.monthList = monthData.controlMap.monthList
          this.totalList = monthData.controlMap.totalList
          this.drgList = monthData.controlMap.drgList
          this.cybList = monthData.controlMap.cybList
        }
        this.drawTendencyAnalysis()
      })
    },
    // 下转
    queryDetails (row) {
      this.$router.push({
        path: '/auliManage/caseCompar',
        query: {
          id: row.id,
          cy_start_date: this.cy_start_date,
          cy_end_date: this.cy_end_date,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime,
          inHosFlag: this.queryForm.inHosFlag
        }
      })
    },
    fnQueryDockerNames () {
      const params = {
        b16c: this.queryForm.dept,
        doctorType: this.queryForm.doctorType
      }
      queryMedicalDoctorSelectInput(params).then((response) => {
        this.queryForm.dockerCode = ''
        this.dockerNames = response.data
      })
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSizeChange (val) {
      this.queryForm.pageNum = 1
      this.queryForm.pageSize = val
      this.fnQuery()
    },
    handleCurrentChange (val) {
      this.queryForm.pageNum = val
      this.fnQuery()
    },
    refresh () {
      this.queryForm.diseType = ''
      this.queryForm.isRemote = ''
      this.queryForm.drCodg = ''
      this.queryForm.cy_start_date = ''
      this.queryForm.cy_end_date = ''
      this.queryForm.inStartTime = ''
      this.queryForm.inEndTime = ''
      this.queryForm.seStartTime = ''
      this.queryForm.seEndTime = ''
      this.$route.query = {}
      this.reload()
    },
    exportExcel () {
      let tableId = 'dataTableId'
      let fileName = '支付预测数据' + '(' + this.queryForm.cy_start_date + '-' + this.queryForm.cy_end_date + ')'
      console.log(fileName)
      this.somExportExcel(tableId, fileName)
    },
    changeDept (deptType) {
      this.queryForm.deptType = deptType
      this.getDataIsuue()
    }
  },
  watch: {
    'queryForm.doctorType' () {
      this.fnQueryDockerNames()
    }

  }
}
</script>
<style scoped>
.code {
  font-size: 12px;
  color: #000000;
  text-overflow: ellipsis;
  overflow: hidden;
}

.name {
  font-size: 10px;
  color: #9b9b9b;
  text-overflow: ellipsis;
  overflow: hidden;
}

.ptp-left-item {
  height: 33%;
  display: flex;
  align-items: center;
}

.ptp-left-item-draw {
  height: 60%;
}

.ptp-left-item-title {
  display: inline-block;
  text-align: left;
  color: gray;
  font-size: var(--biggerSize);
  display: flex;
  align-items: center;
}

.ptp-left-item-value {
  width: 100%;
  display: inline-block;
  text-align: right;
  color: #469ca5;
  font-weight: 400;
  font-size: var(--smallTitleSize);
}

.ptp-circle {
  height: 2rem;
  width: 2rem;
  background-color: #bee3e8;
  border-radius: 50%;
  display: inline-block;
  margin-right: 0.5rem;
}

.ptp-center-header {
  height: 10%;
  width: 100%;
  margin-bottom: 1rem;
}

.ptp-center-content {
  height: 85%;
  width: 100%;
}

.ptp-center-header-ul {
  padding: 0;
  margin-bottom: 0;
  height: 100%;
  list-style-type: none;
  display: flex;
  text-align: center;
  align-content: space-between;
}

.ptp-center-header-li {
  text-align: center;
  height: 100%;
  width: 25%;
}

.ptp-center-header-li-content {
  cursor: pointer;
}

.ptp-center-content-title-value {
  color: gray;
  margin-top: 0.4rem;
  height: 1.7rem;
  font-size: var(--textSize);
  align-items: center;
}

.ptp-center-content-value {
  height: 1.5rem;
  color: gray;
  font-size: var(--smallSize);
}

.ptp-center-content-item {
  display: flex;
  flex-direction: row;
}

.ptp-center-content-item-circle {
  height: 100%;
  width: 2.5rem;
}

.ptp-center-content-samll-title {
  color: #469ca5;
  font-weight: 500;
  font-size: var(--biggerSize);
}

.ptp-center-content-pre {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
}

.title-side {
  color: gray;
  font-size: var(--smallSize);
  margin-right: 1rem;
}

.title-side-value {
  color: black;
  font-size: var(--textSize);
}

.title-side-button {
  background-color: #469ca5;
  color: white;
  border-radius: 5px
}

.checked {
  border-bottom: 2px solid #469ca5;
  color: #469ca5;
}

.button-container {
  margin-left: auto;
  /* 关键属性，自动占据剩余空间，将元素挤到右边 */
}

.unchecked {
  color: black;
  border-bottom: 2px solid whitesmoke;
}
</style>
