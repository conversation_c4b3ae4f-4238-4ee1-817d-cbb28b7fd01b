import request from '@/utils/request'
export function fetchList (params) {
  return request({
    url: '/costControl/getList',
    method: 'post',
    params: params
  })
}

export function getPpsGroupAndCostControl (params) {
  return request({
    url: '/costControl/getPpsGroupAndCostControl',
    method: 'post',
    params: params
  })
}

export function getGroupNumTop10 (params) {
  return request({
    url: '/costControl/getGroupNumTop10',
    method: 'post',
    params: params
  })
}

export function getGroupCostTop10 (params) {
  return request({
    url: '/costControl/getGroupCostTop10',
    method: 'post',
    params: params
  })
}

export function getCostCountInfo (params) {
  return request({
    url: '/costControl/getCostCountInfo',
    method: 'post',
    params: params
  })
}
