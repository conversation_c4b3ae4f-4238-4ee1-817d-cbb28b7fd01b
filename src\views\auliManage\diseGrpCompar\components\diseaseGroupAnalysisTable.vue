<template>
  <div class="som-comps-container">
    <el-table :data="tableData"
              border
              id="dataTableId"
              highlight-current-row
              :header-cell-style="{'text-align':'center'}"
              size="mini"
              height="100%"
              :key=Math.random()
              @row-click="fnTableRowClick"
              v-loading="tableLoading">
      <drg-table-column
          type="index"
          align="right"
          label="序号">
      </drg-table-column>

      <el-table-column
          v-if="false"
          prop="deptCode"
          width="150"
          label="科室编码">
      </el-table-column>

      <el-table-column
          v-if="getQueryType(queryForm.queryType)"
          prop="deptName"
          width="150"
          label="科室名称">
      </el-table-column>

      <el-table-column
          :prop="getPropByType(prefix) + 'Codg'"
          :label="getCodeByType(prefix) + '编码'"
          class="des-item-content-code"
          :show-overflow-tooltip="true">
      </el-table-column>

      <el-table-column
          :prop="getPropByType(prefix) + 'Name'"
          :label="getCodeByType(prefix) + '名称'"
          class="des-item-content-code"
          :show-overflow-tooltip="true">
      </el-table-column>

      <el-table-column
          prop="inGroupNumber"
          label="入组病案数"
          width="100"
          align="right">
      </el-table-column>

      <el-table-column fixed="right" label="选择" align="center" width="100">
        <template slot-scope="scope">
          <el-button icon="el-icon-check"
                     @click="fnTableColumnCheck(scope.row)"
                     :class="[scope.row.checked ? 'el-button--success' : '']"
                     circle/>
        </template>
      </el-table-column>

    </el-table>

    <div class="check-info" v-if="checkPatientId != ''"
         :style="[ rightZero ? {right: '0'} : '' ]">
      当前选择编码号：
      <span>
        {{ checkPatientId }}
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'diseaseGroupAnalysisTable',
  props: {
    tableData: [],
    tableLoading: Boolean,
    queryForm: {},
    total: {
      type: String,
      default: () => ''
    },
    prefix: {
      type: String,
      default: () => ''
    },
    queryType: {
      type: String,
      default: () => ''
    },
    // 显示病案号，是否是靠右边
    rightZero: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    checkPatientId: ''
  }),
  methods: {
    fnTableColumnCheck (row) {
      this.fnRowClick(row)
    },
    getCodeByType (type) {
      if (type == '1') {
        return 'DIP'
      } else if (type == '3') {
        return 'DRG'
      }
    },
    getPropByType (type) {
      if (type == '1') {
        return 'dip'
      } else if (type == '3') {
        return 'drg'
      }
    },
    emitCheckState (type) {
      this.$emit('checkState', type)
    },
    getQueryType (queryType) {
      if (queryType == '1') {
        return true
      } else if (queryType == '3') {
        return false
      }
    },
    fnTableRowClick (row, column, event) {
      this.fnRowClick(row)
    },
    fnRowClick (row) {
      if (row.checked) {
        // row.checked = false
        // this.checkPatientId = ''
        // this.emitCheckState(false)
      } else {
        this.tableData.map(data => {
          data.checked = false
          return data
        })
        row.checked = true
        this.emitCheckState(true)
        if (this.prefix == '1') {
          this.checkPatientId = row.dipCodg
          this.$emit('changeCheck', row.dipCodg)
          this.$emit('changeDept', row.deptCode)
        }
        if (this.prefix == '3') {
          this.checkPatientId = row.drgCodg
          this.$emit('changeCheck', row.drgCodg)
          this.$emit('changeDept', row.deptCode)
        }
      }
    }
  }
}
</script>
<style scoped>
.pagination {
  text-align: right;
}

.check-info {
  position: absolute;
  top: -1.6rem;
  font-size: var(--textSize);
}

.des-item-content-code {
  width: 5rem;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
