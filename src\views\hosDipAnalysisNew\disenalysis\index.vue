<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="somForm"
             :show-hos-dept="{ show: this.$somms.hasHosRole()}"
             :show-dip="{ show: showDip && radioMode != 4}"
             show-date-range
             show-in-date-range
             show-se-date-range
             :initTimeValueNotQuery="false"
             headerTitle="查询条件"
             :container="true"
             :showCoustemContentTitle="true"
             :showPagination="isTablePage"
             :totalNum="total"
             :exportExcel="{ 'tableId': tableId, exportName: exportTableName}"
             :exportExcelFun="exportExcelFun"
             :exportExcelHasChild="true"
             @query="radioChange(radioMode)"
             @reset="reset">

      <template slot="extendFormItems">
        <el-form-item label="参保类型">
          <drg-dict-select dicType="INSURANCE_TYPE" placeholder="请选择人群类别" v-model="queryForm.categories"
                           @change="radioChange(radioMode)"/>
        </el-form-item>
      </template>
      <!-- profttl -->
      <template slot="contentTitle">
        <drg-title-line :title="profttl" :wrapStyle="{ width: 'calc(80% - 10px)'}" >
          <template slot="rightSide">
            <div style="display: flex">
              <div>
                <el-radio-group v-model="radioMode" @change="radioChange">
                  <el-radio-button :label="1">指标</el-radio-button>
                  <el-radio-button :label="2">预测</el-radio-button>
                  <el-radio-button :label="3">分析</el-radio-button>
                  <el-radio-button :label="4">对比</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <template slot="titleRight">
            <!-- 固定列 -->
            <el-select v-model="columnVal"
                       multiple
                       collapse-tags
                       :multiple-limit="3"
                       placeholder="请选择固定列" v-if="radioMode != 4">
              <el-option
                v-for="item in columnOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </template>
        </drg-title-line>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <div class="content-wrapper">

          <!-- 左侧 -->
          <div class="content-wrapper-left" v-if="!analysis && radioMode != 4">

            <!-- 指标table -->
            <kpi-table :data="tableData"
                       ref="dataTable"
                       :loading="loading"
                       :fixed-columns="columnVal"
                       :id="kipId"
                       :query-form="queryForm"
                       v-if="radioMode == 1"
                       :columnOptions="columnOptions"
                       @rowClick="(row) => this.rowData = row"
                       @setRefObj="(obj) => this.tableObj = obj"
                       @showSuspension="showSuspension"/>

            <!-- 预测table -->
            <forecast-table :data="tableData"
                            ref="dataTable"
                            :loading="loading"
                            :query-form="queryForm"
                            :fixed-columns="columnVal"
                            :columnOptions="columnOptions"
                            :id="forecastId"
                            v-if="radioMode == 2"
                            @setRefObj="(obj) => this.tableObj = obj"
                            @showSuspension="showSuspension"/>

          </div>

          <!--悬浮框-->
          <suspension-frame
            :zbData="zbData"
            :ycData="ycData"/>

          <!-- 左侧分析 -->
          <div class="content-wrapper-analysis" v-if="analysis && radioMode != 4">
            <analysis-comp ref="dataTable"
                           :data="analysisPageData"
                           :id="analysisId"
                           :dropdown="diseaseDropdownList"
                           :dropdown-check-val="dropdownVal"
                           :loading="loading"
                           v-if="showAnalysis"
                           :is-loss="isLoss"
                           :queryForm="queryForm"
                           @setRefObj="(obj) => this.tableObj = obj"
                           @switchLossOrProfit="switchLossOrProfit"
                           @dropdownChange="analysisDropdownChange"
                           @checkTypeChange="analysisTypeChange" />

            <analysis-table :data="analysisTableData"
                            ref="dataTable"
                            :loading="loading"
                            :id="analysisId"
                            :is-loss="isLoss"
                            v-if="!showAnalysis"
                            :query-form="queryForm"
                            @showAnalysisPage="switchChartTable"
                            @setRefObj="(obj) => this.tableObj = obj" />

          </div>
          <div v-if="radioMode == 3">
          <!-- 盈利或亏损 -->
          <div class="content-wrapper-analysis-loss-profit">
            <!-- profit -->
            <i class="som-icon-profit som-iconTool"
               title="盈利"
               v-if="!isLoss"
               @click="switchLossOrProfit(true)"
               style="height: 1.2rem;width: 1.2rem"></i>

            <!-- loss -->
            <i class="som-icon-loss som-iconTool"
               title="亏损"
               v-if="isLoss"
               @click="switchLossOrProfit(false)"
               style="height: 1.2rem;width: 1.2rem"></i>
          </div>

          <!-- 分析页面 - 切换图表按钮 -->
          <div class="content-wrapper-analysis-chart-table">
            <!-- table -->
            <i class="som-icon-table som-iconTool"
               title="表格"
               v-if="showAnalysis"
               @click="switchChartTable(null)"
               style="height: 1.2rem;width: 1.2rem"></i>

            <!-- analysis -->
            <i class="som-icon-analysis som-iconTool"
               title="分析"
               v-if="!showAnalysis"
               @click="switchChartTable(null)"
               style="height: 1.2rem;width: 1.2rem"></i>
          </div>
          </div>

          <div class="content-wrapper-analysis" v-if="radioMode == 4">
            <div class="right-top">
              <div style="display: flex">
                <div style="width: 80%; padding-right: 6%" class="right-top-dropdown">
                  <el-select v-model="contrastSelect" clearable placeholder="请选择" @change="sortChange">
                    <el-option v-for="(item, index) in patientContrastOptions"
                               :key="index"
                               :label="item.label"
                               :value="item.value" />
                  </el-select>
                </div>
                <div style="width: 15%;" class="right-top-icon" @click="sortContrast">
                  <i class="el-icon-sort" />
                </div>
              </div>
            </div>
            <contrast :data="contrastData"
                      :dropdown-val="diseaseDropdownVal"
                      :dropdown-data="diseaseDropdown"
                      :loading="contrastLoading"
                      @diseaseChange="diseaseChange" />
          </div>
        </div>
      </template>
    </drg-form>
  </div>
</template>

<script>
import kpiTable from './comps/newDipDiseaseKpiTable'
import analysisComp from './comps/newDipDiseaseAnalysisComp'
import analysisTable from './comps/newDipDiseaseAnalysisTable'
import ForecastTable from './comps/newDipDiseaseForecastTable'
import { queryPatientLoss, queryDropdown, queryMedError, queryAnalysisSummary, updateSwitchState, selectPatientContrastData, queryDiseaseDropdown } from '@/api/newBusiness/newBusinessCommon'
import { queryDiseaseKpiData, queryDiseaseForecastData } from '@/api/newBusiness/newBusinessDisease'
import SuspensionFrame from '../deptAnalysisNew/comps/newSuspensionFrame'
import contrast from '@/views/hosDipAnalysisNew/pattAnalysis/comps/newDipPatientCardComp'

let kpiOptions = [
  { value: 'diseaseWeight', label: '权重' },
  { value: 'inGroupNum', label: '入组病案数' },
  { value: 'avgInHosCost', label: '平均住院费用' },
  { value: 'avgInHosDays', label: '平均住院天数' },
  { value: 'drugRatio', label: '药占比' },
  { value: 'consumeRatio', label: '耗占比' },
  { value: 'timeIndex', label: '时间消耗指数' },
  { value: 'costIndex', label: '费用消耗指数' }
]
let forecastOptions = [
  { value: 'forecastAmountDiff', label: '预测金额差异' },
  { value: 'forecastAmount', label: '预测金额' },
  { value: 'oeVal', label: 'O/E值' }
]

let contrastOptions = [
  { value: 'totalCost', label: '住院总费用' },
  { value: 'inHosDays', label: '住院天数' },
  { value: 'drugRatio', label: '药占比' },
  { value: 'consumeRatio', label: '耗占比' }
]
export default {
  name: 'disenalysis',
  components: {
    kpiTable,
    analysisComp,
    analysisTable,
    'forecast-table': ForecastTable,
    'suspension-frame': SuspensionFrame,
    contrast
  },
  data: () => ({
    queryForm: {
      categories:'',
      feeStas: '0'
    },

    radioMode: 1,
    chartOption: {},
    tableData: [],
    total: 0,
    loading: false,
    yAxisData: [],
    seriesData: [],
    selectVal: 'diseaseWeight',
    options: kpiOptions,
    topVal: 10,
    topOptions: [
      { value: 10, label: 'TOP10' },
      { value: 20, label: 'TOP20' },
      { value: 30, label: 'TOP30' }
    ],
    columnVal: [],
    columnOptions: [],
    tableObj: {},
    kipId: 'kipId',
    forecastId: 'forecastId',
    analysisId: 'analysisId',
    exportTableName: '',
    tableId: '',
    exportExcelFun: queryDiseaseKpiData,
    sort: true, // true：倒序 false：正序
    profttl: '病组指标分析',
    analysis: false,
    analysisPageType: '',
    analysisPageData: [],
    tempAnalysisPageData: [],
    diseaseDropdownList: [],
    dropdownVal: '',
    analysisTableData: [],
    isLoss: true,

    rightAnalysisData: {},
    suggestData: [],
    rowData: {},

    // 分析页面 - 错误病例
    showAnalysis: true,

    tempList: [],
    showDip: true,
    zbData: [],
    ycData: [],

    contrastData: [],
    diseaseDropdownVal: '',
    diseaseDropdown: [],
    contrastLoading: false,
    patientContrastOptions: contrastOptions,
    contrastSelect: 'totalCost',
    booleanSort: true
  }),
  mounted () {
    if (Object.keys(this.$route.query).length > 0) {
      this.queryForm.feeStas = String(this.$store.getters.feeStas)
      if (this.$route.query.drCodg) {
        this.queryForm.drCodg = this.$route.query.drCodg
      }
      if (this.$route.query.deptCode) {
        this.queryForm.deptCode = this.$route.query.deptCode
      }
      if (this.$route.query.code) {
        this.queryForm.dipCodg = this.$route.query.code
      }
      if (this.$route.query.radioMode) {
        this.radioMode = this.$route.query.radioMode
      }
      if (this.$route.query.feeStas) {
        this.queryForm.feeStas = this.$route.query.feeStas
      }
      if (this.$route.query.categories) {
        this.queryForm.categories = this.$route.query.categories
      }
      if (this.$route.query.begnDate && this.$route.query.expiDate) {
        this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.queryForm)
      }

      if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
        this.$refs.somForm.jumpTimeChange('in', this.$route.query, this.queryForm)
      }

      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.queryForm)
      }
    }

    this.$nextTick(() => {
      this.init()
    })
  },
  computed: {
    isTablePage () {
      return this.analysisPageType != 'errorMed' && this.showAnalysis && this.radioMode != 4
    }
  },
  methods: {
    queryDiseaseKpiData,
    queryPatientLoss,
    queryDiseaseForecastData,
    init () {
      this.radioChange(this.radioMode)
    },
    changeSwitch (val) {
      this.$store.commit('SET_SWITCHSTATE', val)
      this.queryForm.feeStas = this.$store.getters.feeStas
      let params = {}
      Object.assign(params, this.queryForm)
      params.username = this.$store.getters.name
      updateSwitchState(params).then((res) => {
        if (res.code == 200) {
          this.init()
        }
      })
    },
    // 查询指标数据
    queryPageKpiData () {
      this.loading = true
      queryDiseaseKpiData(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
        this.generateChartData(res)
        this.generateFixedColumns()
      })
    },
    // 查询预测数据
    queryForecastData () {
      this.loading = true
      queryDiseaseForecastData(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
        this.generateChartData(res)
        this.generateFixedColumns()
      })
    },
    // 查询分析数据
    async queryPageAnalysisData (dipCodg) {
      this.loading = true
      let params = this.getParams()

      if (!this.showAnalysis) {
        this.analysisTableData = []
        await queryAnalysisSummary(params).then(res => {
          this.analysisTableData = res.data
        })
      } else {
        this.analysisPageData = []
        if (this.diseaseDropdownList.length == 0) {
          await queryDropdown(params).then(res => {
            this.diseaseDropdownList = res.data
          })
        }

        if (dipCodg) {
          // 跳转到分析页面
          this.dropdownVal = dipCodg
        } else {
          // 第一次进入页面没有选择下拉选
          if (this.diseaseDropdownList.length > 0 && !this.dropdownVal) {
            this.dropdownVal = this.diseaseDropdownList[0].value
          } else if (this.diseaseDropdownList.length > 0 && this.dropdownVal) {
            let tempList = []
            for (let i = 0; i < this.diseaseDropdownList.length; i++) {
              tempList.push(this.diseaseDropdownList[i].value)
            }
            if (!tempList.includes(this.dropdownVal)) {
              this.dropdownVal = this.diseaseDropdownList[0].value
            }
          }
        }

        // 子页面下拉选改变传入科室编码
        if (this.dropdownVal) {
          params.dipCodg = this.dropdownVal
          this.tempAnalysisPageData = []
        }

        await this.getAnalysisPageData('med', 0, queryPatientLoss, params)

        // 错误病例
        await this.getAnalysisPageData('errorMed', 1, queryMedError, params, true)

        if (this.analysisPageData.length > 0) {
          this.tempAnalysisPageData = [...this.analysisPageData]
        }
      }

      this.loading = false
    },

    showSuspension (item) {
      if (item) {
        if (item[0].type == 1) {
          this.zbData = item
        } else if (item[0].type == 2) {
          this.ycData = item
        }
      }
    },

    // 获取分析数据
    async getAnalysisPageData (type, index, queryMethod, params, noPaging = false) {
      if (this.analysisPageType == type || this.tempDataIsNull(index)) {
        await queryMethod(params).then(res => {
          if ((type == 'med' && res.data.list && res.data.list.length > 0) || (type == 'errorMed' && res.data && res.data.length > 0)) {
            this.addAnalysisData(res, index, false, noPaging)
            if (this.analysisPageType == type && !['errorMed'].includes(type)) this.total = res.data.total
          } else {
            this.analysisPageData.push(
              {
                data: [],
                total: 0,
                pageNum: 1
              }
            )
          }
        })
      } else {
        this.addAnalysisData(null, index, true)
      }
    },
    tempDataIsNull (index) {
      if (this.tempAnalysisPageData.length > 0) {
        if (this.tempAnalysisPageData[index].data != undefined) {
          return this.tempAnalysisPageData[index].data.length == 0
        } else {
          return true
        }
      }
      return this.tempAnalysisPageData.length == 0
    },
    // 添加分析数据
    addAnalysisData (res, index, useTempData, noPaging = false) {
      if (useTempData) {
        this.analysisPageData.push(this.tempAnalysisPageData[index])
      } else {
        if (noPaging) {
          let total = 0
          if (index == 1) {
            total = res.data[0].errorSummaryNum + '-' + res.data[0].compeleteErrorNum + '-' + res.data[0].logicErrorNum
          }
          // 无分页情况
          this.analysisPageData.push({
            data: res.data,
            total: total,
            pageNum: 1
          })
        } else {
          this.analysisPageData.push({
            data: res.data.list,
            total: res.data.total,
            pageNum: this.queryForm.pageNum
          })
        }
      }
    },
    // 生成图数据
    generateChartData (res) {
      let params = this.getParams()
      params.selectVal = this.selectVal
      if (this.radioMode == 1) {
        this.tempList = res.data.list
        this.yAxisData = []
        this.seriesData = []
        let sortData = []
        for (let i = 0; i < this.tempList.length; i++) {
          let item = this.tempList[i]
          sortData.push({
            y: item.dipName.length > 7 ? item.dipName.substring(0, 7) + '..' : item.dipName,
            value: item[this.selectVal]
          })
        }

        if (this.sort) {
          sortData = sortData.sort((a, b) => a.value - b.value)
        } else {
          sortData = sortData.sort((a, b) => b.value - a.value)
        }

        for (let i = 0; i < sortData.length; i++) {
          if (i == this.topVal) {
            break
          }
          this.yAxisData.push(sortData[i].y)
          this.seriesData.push(sortData[i].value)
        }
        this.yAxisData.reverse()
        this.seriesData.reverse()
        this.initChart()
      }
    },
    // 生成固定列
    generateFixedColumns () {
      this.columnOptions = []
      let children = this.$refs.dataTable.$children[0].$children
      if (children) {
        children.forEach(item => {
          if (item.$options.propsData.label && item.$options.propsData.label != '序号') {
            this.columnOptions.push({
              value: item.$options.propsData.prop,
              label: item.$options.propsData.label
            })
          }
        })
      }
    },
    // 初始化图
    initChart () {
      this.chartOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            interval: 0,
            rotate: 40,
            formatter: params => {
              return this.formatCost(params, 0)
            }
          },
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: this.yAxisData
        },
        series: [
          {
            name: '数值',
            type: 'bar',
            label: {
              formatter: params => {
                return this.formatCost(params.value)
              }
            },
            data: this.seriesData.map((item, index) => {
              return {
                value: item,
                label: {
                  show: true,
                  position: item > 0 ? 'right' : 'left'
                }
              }
            })
          }
        ]
      }
    },
    // 格式化
    formatCost (val, fixed = 2) {
      let prefix = ''
      let resVal = val
      if (String(val).includes('-')) {
        prefix = '-'
        val = Math.abs(val)
        resVal = val
      }
      if (Math.abs((parseFloat(val) / 10000)) > 1) {
        resVal = (parseFloat(val) / 10000).toFixed(fixed) + '/万'
      }
      return prefix + resVal
    },
    // 选择改变
    radioChange (val) {
      this.$route.query.radioMode = val
      if (val == 1) {
        this.queryPageKpiData()
        this.exportExcelFun = queryDiseaseKpiData
        this.tableId = this.kipId
        this.exportTableName = '病组指标分析'
        this.options = kpiOptions
        this.selectVal = 'diseaseWeight'
        this.profttl = '病组指标分析'
        this.analysis = false
      }
      if (val == 2) {
        this.queryForecastData()
        this.exportExcelFun = queryDiseaseForecastData
        this.tableId = this.forecastId
        this.exportTableName = '病组预测情况'
        this.options = forecastOptions
        this.selectVal = 'forecastAmountDiff'
        this.profttl = '病组预测情况'
        this.analysis = false
      }
      if (val == 3) {
        this.queryPageAnalysisData()
        this.exportExcelFun = queryPatientLoss
        this.tableId = this.analysisId
        this.exportTableName = '病组分析'
        this.profttl = '病组分析'
        this.analysis = true
      }
      if (val == 4) {
        this.queryPatientContrastData()
        this.profttl = '患者对比'
        this.analysis = false
      }
      this.selectDisease()
    },
    // 图排序改变
    selectChange () {
      this.generateChartData()
    },
    // 点击排序
    sortClick () {
      this.sort = !this.sort
      this.generateChartData()
    },
    // 分析页面模块点击
    analysisTypeChange (item) {
      let type = item.type
      this.queryForm.pageNum = item.pageNum
      this.tempAnalysisPageData = item.data.map(i => {
        return {
          total: i.value,
          data: i.data,
          pageNum: i.pageNum
        }
      })
      this.analysisPageType = type
      if (this.analysisPageData.length > 0) {
        if (type == 'med') {
          this.total = this.analysisPageData[0].total
        }
      }
    },
    // 分析页面下拉选改变
    analysisDropdownChange (val) {
      this.dropdownVal = val
      this.queryPageAnalysisData()
    },
    // 切换图表
    switchChartTable (dipCodg) {
      this.showAnalysis = !this.showAnalysis
      this.queryPageAnalysisData(dipCodg)
    },
    // 选择盈利还是亏损
    switchLossOrProfit (isLoss) {
      this.isLoss = isLoss
      this.queryPageAnalysisData()
    },
    // 病种下拉选择查询
    selectDisease () {
      queryDropdown(this.getParams()).then(res => {
        this.diseaseDropdownList = res.data
      })
    },
    // 获取参数
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.auth = true
      // 下拉列表类型 3:病组
      params.dropdownType = '3'
      // 亏损还是盈利
      params.isLoss = this.isLoss
      params.dipCodg = this.queryForm.dipCodg
      return params
    },
    // 重置
    reset () {
      this.radioChange(this.selectVal)
      this.tempAnalysisPageData = []
    },
    // 右侧分析详情下转
    jumpDetails (data) {
      if (data) {
        if (this.radioMode == 1) {
          this.$router.push({
            path: '/hosDipAnalysisNew/pattAnalysis',
            query: {
              begnDate: this.queryForm.begnDate,
              expiDate: this.queryForm.expiDate,
              dipCodg: data.profttl,
              feeStas: this.queryForm.feeStas
            }
          })
        }
        if (this.radioMode == 3) {
          this.$router.push({
            path: '/hosDipAnalysisNew/pattAnalysis',
            query: {
              begnDate: this.queryForm.begnDate,
              expiDate: this.queryForm.expiDate,
              bah: data.subhead,
              dipCodg: this.dropdownVal,
              feeStas: this.queryForm.feeStas
            }
          })
        }
      }
    },

    async queryPatientContrastData () {
      this.contrastLoading = true
      let params = this.getParams()
      params.contrastSelect = this.contrastSelect

      params.diseaseGroupType = '1'
      // if(this.diseaseDropdown.length == 0) {
      await queryDiseaseDropdown(params).then(res => {
        this.diseaseDropdown = res.data
      })
      // }
      if (this.diseaseDropdown.length > 0) {
        let number = 0
        for (let i = 0; i < this.diseaseDropdown.length; i++) {
          if (this.diseaseDropdown[i].value == this.diseaseDropdownVal) {
            number++
          }
        }
        if (number == 0) {
          this.diseaseDropdownVal = this.diseaseDropdown[0].value
        }
        if (!this.diseaseDropdownVal) {
          this.diseaseDropdownVal = this.diseaseDropdown[0].value
        }
      }
      if (this.diseaseDropdownVal) {
        params.dipCodg = this.diseaseDropdownVal
      }

      selectPatientContrastData(params).then(res => {
        this.contrastData = res.data
        let tempData = []
        for (let i = this.contrastData.length - 1; i > -1; i--) {
          tempData.push(this.contrastData[i])
        }
        if (this.booleanSort) {

        } else {
          this.contrastData = tempData
        }
        this.contrastLoading = false
      })
    },

    diseaseChange (val) {
      this.diseaseDropdownVal = val
      this.queryPatientContrastData()
    },

    sortChange () {
      this.queryPatientContrastData()
    },

    sortContrast () {
      this.booleanSort = !this.booleanSort
      this.queryPatientContrastData()
    }
  }
}
</script>

<style lang="scss" scoped>
.content-wrapper{
  height: 98%;
  width: 100%;
  display: flex;

  &-left{
    width: 80%;
    height: 96%;
    padding-right: 10px;
    box-sizing: border-box;
    position: relative;

    &-fixed-column{
      position: absolute;
      left: 5%;
      top: -4.5%;
    }

    &-analysis{
      width: 100%;
      height: 100%;
    }
  }

  &-right{
    width: 20%;
    height: 100%;
    position: relative;

    &-select{
      position: absolute;
      right: 0;
      top: -4.5%;
    }

    &-top{
      height: 10%;
      width: 100%;
      position: absolute;
      top: 0%;
    }

    &-icon{
      z-index: 2;
      font-size: 18px;
      width: 20px;
      height: 40px;
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  &-analysis{
    width: 100%;
    height: 100%;
    position: relative;

    &-chart-table{
      position: absolute;
      top: -5px;
      right: 5px;
    }

    &-loss-profit{
      position: absolute;
      top: -2px;
      right: 35px;
    }
  }
}

/deep/ .pagination-container{
  right: 21%;
}

/deep/ .content-wrapper-right-top>.el-select{
  width: 84px;
}

.analysis-wrapper{
  height: 100%;
  width: 100%;
  position: relative;

  &-left{
    width: 80%;
    height: 100%;
  }

  &-analysis{
    width: 100%;
    height: 80%;
    position: relative;
    display: flex;
  }

  &-right{
    width: 20%;
    height: 100%;
    background-color: rgba(131,175,155,.3);
    padding: 1% 1% 0 1%;
    border-radius: 1%;
  }

  &-no-table{
    width: 100%;
    height: 80%;
  }
}
.analysis-head{
  width: 80%;
  height: 20%;

  &-title{
    width: 100%;
    height: 20%;
    font-size: var(--biggerSize);
    font-weight: 600;
    position: relative;

    &-dropdown{
      position: absolute;
      right: 10px;
      top: 0;
    }
  }

  &-content{
    width: 100%;
    height: 54%;
    display: flex;

    &-item{
      width: 14%;
      height: 100%;
      cursor: pointer;
      background-color: rgb(64,158,255);
      font-weight: 600;
      margin-right: 1%;
      padding: 1%;
      display: flex;
      align-items: center;
      justify-items: center;
      flex-direction: column;
      border-radius:  16% 0 16% 0 ;

      .title{
        font-size: 14px;
        color: white;
      }

      .value{
        margin-top: 8%;
        font-size: 24px;
        color: white;
      }
    }
  }
}

.analysis-content{
  width: 100%;
  height: 94%;
  position: relative;

  &-summary{
    width: 30%;
    height: 40%;
    border: 1px solid red;
  }
}

.analysis-wrapper-right{
  width: 100%;
  height: 96%;
  position: absolute;
  right: 0;
  top: 0%;
  display: flex;
  justify-content: center;

  &-content{
    width: 100%;
    height: 100%;
  }
}

$titleGray: gray;
$titleSize: 13px;
.analysis-right{

  &-title{
    width: 100%;
    height: 4%;
    font-size: var(--biggerSize);
    font-weight: 600;
  }

  &-subhead{
    width: 100%;
    height: 4%;
    font-size: var(--biggerSmallSize);
    color: gray;
    border-bottom: 1px solid $titleGray;
  }

  &-cost{
    width: 100%;
    height: 62%;
    line-height: 20px;
    overflow-y: auto;
    border-bottom: 1px solid $titleGray;
  }

  &-suggest{
    padding-top: 4%;
    width: 100%;
    height: 30%;
    overflow-y: auto;

    &-title{
      width: 100%;
      font-weight: 600;
      height: 10%;
      font-size: $titleSize;
    }

    &-content{
      width: 100%;
      height: 70%;
      overflow-y: auto;
    }

    &-declaration{
      width: 100%;
      height: 15%;
      color: #fa5d5d;
      display: flex;
      align-items: center
    }

    &-item{
      width: 100%;
      height: 16%;
    }
  }
}

.right-top {
  position: absolute;
  top: 0;
  right: 0;

  &-dropdown {
    z-index: 2;
  }

  &-icon {
    z-index: 2;
    font-size: 18px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    position: absolute;
    right: 0;
    bottom: 3px;
  }
}
.content-wrapper-left{
  height:100%;
  width: 150%;
}
</style>
