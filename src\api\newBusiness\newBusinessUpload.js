import request from '@/utils/request'

/**
 * 上传反馈数据
 * @param params
 * @returns {*}
 */
export function feedbackUpload (params) {
  return request({
    url: '/feedbackFileUploadController/feedbackUpload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}

/**
 * 上传反费用信息
 * @param params
 * @returns {*}
 */
export function feedbackUploadFunds (params) {
  return request({
    url: '/feedbackFileUploadController/feedbackUploadFunds',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}

/**
 * 查询文件上传日志
 * @param params
 * @returns {*}
 */
export function queryFileUploadLog (params) {
  return request({
    url: '/feedbackFileUploadController/queryFileUploadLog',
    method: 'post',
    params: params
  })
}

/**
 * 查询详情
 * @param params
 * @returns {*}
 */
export function queryFeedbackDetail (params) {
  return request({
    url: '/feedbackFileUploadController/queryFeedbackDetail',
    method: 'post',
    params: params
  })
}

/**
 * 查询详情2
 * @param params
 * @returns {*}
 */
export function queryFeedbackDetail2 (params) {
  return request({
    url: '/feedbackFileUploadController/queryFeedbackDetail2',
    method: 'post',
    params: params
  })
}

/**
 * 查询汇总
 * @param params
 * @returns {*}
 */
export function queryFeedbackDetail2Summary (params) {
  return request({
    url: '/feedbackFileUploadController/queryFeedbackDetail2Summary',
    method: 'post',
    params: params
  })
}

/**
 * 抽取
 * @param params
 * @returns {*}
 */
export function feedbackExtract (params) {
  return request({
    url: '/feedbackFileUploadController/feedbackExtract',
    method: 'post',
    params: params
  })
}
