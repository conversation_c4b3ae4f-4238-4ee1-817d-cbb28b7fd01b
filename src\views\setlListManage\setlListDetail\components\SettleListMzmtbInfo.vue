<template>
    <el-form :inline="true" :model="value" size="mini" style="margin-top:-18px;">
      <el-header v-show="value.showJsqd" style="height: 30px;width:120px;line-height: 30px;margin-left:-25px;color:white;background-color: #1e6abc;margin-bottom: 10px">
        <span style="margin-left:10px;">门诊慢特病</span>
      </el-header>
      <el-row :gutter="0" v-show="value.showJsqd">
        <el-col :span="6">
          <el-form-item label="诊断科别">
            <select-tree v-model="value.deptCode" :options="depts" :props="defaultProps" placeholder="请选择诊断科别"/>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="就诊日期" >
            <el-date-picker class="formInput"
                  v-model="value.mdtrtDate" size="mini"
                  type="updt_date"
                  placeholder="请选择就诊日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="table-container" v-show="value.showJsqd">
          <el-table ref="mzmtbListTable"
                    size="mini"
                    stripe
                    :data="value.busOutpatientClinicDiagnosisList"
                    style="width: 100%"
                    border>
            <el-table-column
              label="序号"
              type="index"
              width="50">
            </el-table-column>
            <el-table-column label="疾病诊断名称"  align="center" >
              <template slot-scope="scope">
                <el-input v-model="scope.row.diagName" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="疾病诊断代码"  align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.diagCode" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="手术及操作名称"  align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.oprnOprtName" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="手术及操作代码"  align="center" >
              <template slot-scope="scope">
                <el-input v-model="scope.row.oprnOprtCode" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="danger"
                    @click="deleteMzmtList(scope.$index, scope.row)">删除
                  </el-button>
              </template>
            </el-table-column>
          </el-table>
      </div>
      <el-row v-show="value.showJsqd">
        <el-button type="success" size="mini"  icon="el-icon-plus" @click="addMzList" style="margin-top: 3px;">添加一行</el-button>
      </el-row>

      <el-row>
        <el-col :span="6" v-show="false">
          <el-form-item>
            <!--隐藏框用于触发查看不到字段的隐藏和展示-->
            <el-input  v-model="value.showFlag"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
</template>

<script>
import SelectTree from '@/components/SelectTree/index'
import { querySelectTreeAndSelectList } from '@/api/common/drgCommon'
export default {
  name: 'SettleListMzmtbInfo',
  components: { SelectTree },
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      }
    }
  },
  props: {
    value: Object
  },
  created () {
    this.findSelectTreeAndSelectList()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    addMzList () {
      const bocdl = this.value.busOutpatientClinicDiagnosisList
      bocdl.push({ hiSetlInvyId: this.$route.query.id, diagCode: null, diagName: null, oprnOprtCode: null, oprnOprtName: null, deptCode: null, deptName: null, mdtrtDate: null })
      const bocdlMap = { busOutpatientClinicDiagnosisList: bocdl }
      Object.assign(this.value, bocdlMap)
    },
    deleteMzmtList (index, row) {
      this.$confirm('是否要进行删除操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const bocdl = this.value.busOutpatientClinicDiagnosisList
        for (let i = 0; i < bocdl.length; i++) {
          if (i == index - 1) {
            bocdl.splice(index, 1)
          }
        }
      })
    }
  }
}
</script>

<style scoped>
  .formInput{
    width:170px;
  }
</style>
