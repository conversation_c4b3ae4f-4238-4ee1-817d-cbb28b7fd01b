<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
              show-date-range
              :show-in-date-range="false"
              :show-se-date-range="false"
              :show-hos-dept="{ show: this.$somms.hasHosRole()}"
              showPagination
              :totalNum="total"
              headerTitle="查询条件"
              contentTitle="执行批量流程"
              :container="true"
              :exportExcelHasChild="false"
              :initTimeValueNotQuery="false"
              ref="somForm"
              @query="handleSearchList"
    >
      <template slot="extendFormItems">
        <el-form-item label="场景类型" prop="ruleScenType">
          <drg-dict-select dicType="RULE_SCEN_TYPE" placeholder="请选择场景类型" v-model="listQuery.ruleScenType"
                           @change="getDataIsuue"/>
        </el-form-item>
      </template>

      <template slot="buttons">
        <el-button type="primary" @click="execRunProcess" class="som-button-margin-right"><i
          class="el-icon-upload el-icon&#45;&#45;left"></i>执行
        </el-button>
      </template>
      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  :id="tableId"
                  size="mini"
                  stripe
                  height="100%"
                  :data="list"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>
          <el-table-column label="序号" type="index" align="right">
          </el-table-column>
          <el-table-column label="病案总数" prop="medcas_val" align="center"/>
          <el-table-column label="执行病案数" prop="exam_val" align="center"/>
          <el-table-column label="疑点病案数" prop="doubt_val" align="center"/>
          <el-table-column label="正常病案数" prop="chk_pass_val" align="center"/>
          <el-table-column label="数据时间范围" prop="data_time_scp" align="center" width="180"/>
          <el-table-column label="执行开始时间" prop="start_time" align="center" width="140"/>
          <el-table-column label="执行结束时间" prop="end_time" align="center" width="140"/>
          <el-table-column label="数据处理时长(s)" prop="proc_dura" align="center"/>
          <el-table-column label="数据处理结果" prop="proc_result" align="center"/>
          <el-table-column label="流程进度" prop="proc_status" align="center">
            <template slot-scope="scope">
              <span :class="[scope.row.proc_status =='0' ? 'som-color-error'  : 'som-color-success']">
              {{ $somms.getDictValueByType(scope.row.proc_status, 'PROC_STATUS') }}
            </span>
            </template>
          </el-table-column>
          <el-table-column label="操作人" prop="oprt_psn" align="center"/>
          <el-table-column label="操作时间" prop="oprt_addtime" align="center" width="140"/>
          <el-table-column label="医疗机构ID" prop="hospital_id" align="center"/>
          <el-table-column label="有效标志" prop="active_flag" align="center"/>
        </el-table>
      </template>
    </drg-form>
  </div>
</template>
<script>
import {
  querySelectTreeAndSelectList,
  queryLikeDipGroupByPram,
  queryLikeDrgsByPram,
  queryDataIsuue
} from '@/api/common/drgCommon'
import {
  runProcess, queryHcsProcList
} from '@/api/examCorrection/batchProcess'
import {formatDate} from '@/utils/date'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  ruleScenType: '1',
  begnDate: '',
  expiDate: '',
  seStartTime: '',
  seEndTime: ''
}
export default {
  name: 'bacthProcess',
  components: {},
  inject: ['reload'],
  data() {
    return {
      tupleDialogVisible: false,
      ruleDialogVisible: false,
      //点击违规规则存储当前行数据
      selectedRowData: {},
      //点击违规规则查询到的违规元素
      violationTupleList: [],
      //点击违规规则查询到的违规元素
      violationRuleList: [],
      //点击违规规则元素查的传参
      selectTupleParam: {},
      //点击违规规则元素查的传参
      selectRuleParam: {},
      showFlag: false,
      dialogVisible: false,
      tableId: 'dataTable',
      listLoading: true,
      list: null,
      total: null,
      dictVoList: {},
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      depts: [],
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created() {

  },
  watch: {
    list: function () {
      this.$nextTick(() => {
        if (this.$refs.settleListTable) {
          this.$refs.settleListTable.doLayout()
        }
      })
    }
  },
  filters: {
    formatIsEmpty(value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime(time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },

  },
  mounted() {
    this.$nextTick(() => {
      this.findSelectTreeAndSelectList()
      if (Object.keys(this.$route.query).length > 0) {
        // 时间需要在最下面设置
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.listQuery)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.listQuery)
        }
      }
      this.getList()
    })
  },
  methods: {
    handleSearchList() {
      this.getList()
    },
    handleResetSearch() {
      this.getDataIsuue()
    },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'B34C')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue() {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        this.getList()
      })
    },
    getList() {
      this.listLoading = true
      this.getParamByBaseQuery(this.submitListQuery, this.listQuery)
      queryHcsProcList(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryHcsProcList,
    dateChangeCysj(val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    refresh() {
      this.reload()
    },
    getParamByBaseQuery(targetParam, sourceParam) {
      if (targetParam) {
        const keys = Object.keys(targetParam)
        // 更新参数值
        keys.forEach(key => {
          targetParam[key] = sourceParam[key]
        })
      }
    },
    getParams() {
      return this.submitListQuery
    },
    execRunProcess() {
      this.listLoading = true
      this.getParamByBaseQuery(this.submitListQuery, this.listQuery)
      runProcess(this.submitListQuery).then(response => {
        this.listLoading = false
        this.$message({
          type: 'success',
          message: '执行完成'
        })
        this.getList()
      })
    }
  }
}
</script>
<style scoped>
/*时间样式设置*/
/deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner {
  width: 200px;
}

.autoSelectInputWidth {
  width: 178px;
}

</style>
