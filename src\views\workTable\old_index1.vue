<template>
  <div class="app-container">
    <el-card style="height:100%;overflow: hidden">
      <div style="height: 20%; margin-bottom: 2%">
        <!-- 基本情况 -->
        <drg-title-line title="基本情况" class="first-page-title">
          <template slot="rightSide">
            <div>
              <el-date-picker
                  v-model="curTime"
                  :clearable="false"
                  @change="fnTimeChange"
                  type="month"
                  size="small">
              </el-date-picker>
            </div>
          </template>
        </drg-title-line>
        <el-row type="flex" justify="space-between" class="base-row">
          <!-- 入院人次 -->
          <el-col :span="4" class="base-col">
            <div class="base-col-container base-col-color_1">
              <div class="base-col-title">入院人次</div>
              <div class="big-val base-col-title"> {{ base_in_hospital.inHospitalCount | ifNullZero }}</div>
              <div class="base-col-title base-col-yoy-lrr">
                <div class="base-col-rate-yoy">
                  同比: {{ formatRate(base_in_hospital.inHospitalCountYoy) }}
                  <drg-tendency :val="base_in_hospital.inHospitalCountYoy"/>
                </div>
                <div class="base-col-rate-lrr">
                  环比: {{ formatRate(base_in_hospital.inHospitalCountLrr) }}
                  <drg-tendency :val="base_in_hospital.inHospitalCountLrr"/>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 出院人次 -->
          <el-col :span="4" class="base-col">
            <div class="base-col-container base-col-color_2">
              <div class="base-col-title">出院人次</div>
              <div class="big-val base-col-title"> {{ base_out_hospital.leaveHospitalCount | ifNullZero }}</div>
              <div class="base-col-title base-col-yoy-lrr">
                <div class="base-col-rate-yoy">
                  同比: {{ formatRate(base_out_hospital.leaveHospitalCountYoy) }}
                  <drg-tendency :val="base_out_hospital.leaveHospitalCountYoy"/>
                </div>
                <div class="base-col-rate-lrr">
                  环比: {{ formatRate(base_out_hospital.leaveHospitalCountLrr) }}
                  <drg-tendency :val="base_out_hospital.leaveHospitalCountLrr"/>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 住院总费用 -->
          <el-col :span="4" class="base-col">
            <div class="base-col-container base-col-color_3">
              <div class="base-col-title">住院总费用</div>
              <div class="big-val base-col-title" v-html="formatCost(base_total_cost.inHospitalTotalCost, true)"></div>
              <div class="base-col-title base-col-yoy-lrr">
                <div class="base-col-rate-yoy">
                  同比: {{ formatRate(base_total_cost.inHospitalTotalCostYoy) }}
                  <drg-tendency :val="base_total_cost.inHospitalTotalCostYoy"/>
                </div>
                <div class="base-col-rate-lrr">
                  环比: {{ formatRate(base_total_cost.inHospitalTotalCostLrr) }}
                  <drg-tendency :val="base_total_cost.inHospitalTotalCostLrr"/>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 分组汇总 -->
          <el-col :span="4" class="base-col">
            <div class="base-col-container base-col-color_1">
              <div class="base-col-title">分组汇总</div>
              <div class="base-col-title">

                <el-row style="margin-bottom: 0.5rem">
                  <el-col :span="10">
                    <div style="display: flex;justify-content: left;align-items: center;height: 2rem">
                      <i class="el-icon-coin base-col-icon"></i>
                      DRG: {{ base_group_info.drg.groupCount | ifNullZero }}
                    </div>
                  </el-col>
                  <el-col :span="12" style="text-align: left;margin-left: 0.5rem">
                    <div class="base-col-yoy-lrr" style="margin-bottom: 0.3rem">
                      同比: {{ formatRate(base_group_info.drg.groupCountYoy) }}
                      <drg-tendency :val="base_group_info.drg.groupCountYoy"/>
                    </div>
                    <div class="base-col-yoy-lrr">
                      环比: {{ formatRate(base_group_info.drg.groupCountLrr) }}
                      <drg-tendency :val="base_group_info.drg.groupCountLrr"/>
                    </div>
                  </el-col>
                </el-row>

                <!--                <el-row>-->
                <!--                  <el-col :span="10">-->
                <!--                    <div style="display: flex;justify-content: left;align-items: center;height: 2rem">-->
                <!--                      <i class="el-icon-data-analysis base-col-icon"></i>-->
                <!--                      CD: {{ base_group_info.cd.groupCount | ifNullZero }}-->
                <!--                    </div>-->
                <!--                  </el-col>-->
                <!--                  <el-col :span="12" style="text-align: left;margin-left: 0.5rem">-->
                <!--                    <div class="base-col-yoy-lrr" style="margin-bottom: 0.3rem">-->
                <!--                      同比: {{ formatRate(base_group_info.cd.groupCountYoy) }}-->
                <!--                      <drg-tendency :val="base_group_info.cd.groupCountYoy" />-->
                <!--                    </div>-->
                <!--                    <div class="base-col-yoy-lrr">-->
                <!--                      环比: {{ formatRate(base_group_info.cd.groupCountLrr) }}-->
                <!--                      <drg-tendency :val="base_group_info.cd.groupCountLrr" />-->
                <!--                    </div>-->
                <!--                  </el-col>-->
                <!--                </el-row>-->

                <el-row>
                  <el-col :span="10">
                    <div style="display: flex;justify-content: left;align-items: center;height: 2rem">
                      <i class="el-icon-data-analysis base-col-icon"></i>
                      DIP: {{ base_group_info.dip.groupCount | ifNullZero }}
                    </div>
                  </el-col>
                  <el-col :span="12" style="text-align: left;margin-left: 0.5rem">
                    <div class="base-col-yoy-lrr" style="margin-bottom: 0.3rem">
                      同比: {{ formatRate(base_group_info.dip.groupCountYoy) }}
                      <drg-tendency :val="base_group_info.dip.groupCountYoy"/>
                    </div>
                    <div class="base-col-yoy-lrr">
                      环比: {{ formatRate(base_group_info.dip.groupCountLrr) }}
                      <drg-tendency :val="base_group_info.dip.groupCountLrr"/>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-col>

          <!-- 床位使用率 -->
          <el-col :span="4" class="base-col">
            <div class="base-col-container base-col-color_2">
              <div class="base-col-title">床位使用率</div>
              <div class="base-col-title">
                总床位数: {{ base_bed_rate.count }}
              </div>
              <div class="base-col-title">
                床位使用率: {{ formatRate(base_bed_rate.rate) }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div style="height: 35%;margin-bottom: 2%">
        <!-- 逆差预测 -->
        <drg-title-line title="逆差预测" class="first-page-title">
          <template slot="rightSide">
            <ul class="forecast-group-ul">
              <li class="forecast-group-li-end" :class="group_checked.dip.checked ? 'group-checked' : 'group-unchecked'"
                  v-if="this.$somms.getGroupType()==1">
                <span class="forecast-group-li-content" @click="fnChangeCheckGroup(group_checked.dip.index)"
                >DIP分组</span>
              </li>
              <li class="forecast-group-li" :class="group_checked.drg.checked ? 'group-checked' : 'group-unchecked'"
                  v-if="this.$somms.getGroupType()==3">
                <span class="forecast-group-li-content" @click="fnChangeCheckGroup(group_checked.drg.index)"
                >DRG分组</span>
              </li>
              <!--              <li class="forecast-group-li" :class="group_checked.cd.checked ? 'group-checked' : 'group-unchecked'">-->
              <!--                <span class="forecast-group-li-content" @click="fnChangeCheckGroup(group_checked.cd.index)"-->
              <!--                      >成都分组</span>-->
              <!--              </li>-->
            </ul>
          </template>
        </drg-title-line>
        <el-row type="flex" justify="start" class="forecast-row">
          <el-col :span="4" class="forecast-col">
            <div class="forecast-col-header">
              <div>
                <i class="el-icon-circle-check"></i>
                <span>城职汇总</span>
              </div>
              <i class="el-icon-arrow-right forecast-col-header-icon-right"></i>
            </div>
            <div class="forecast-col-content">
              <div class="big-val base-col-title forecast-col-content-title">
                {{ forecast_mi_summary.totalCount | ifNullZero }}
                <span class="unit">/人</span>
              </div>
              <div class="base-col-title forecast-col-small-title">项目申报总额</div>
              <span class="forecast-col-big-title" v-html="formatMoney(forecast_mi_summary.projTotalCost, true)"></span>
              <div class="base-col-title forecast-col-small-title">{{ group_checked_name }}支付预测</div>
              <span class="forecast-col-big-title" v-html="formatMoney(forecast_mi_summary.predictCost, true)"></span>
            </div>
          </el-col>

          <el-col :span="4" class="forecast-col">
            <div class="forecast-col-header">
              <i class="el-icon-circle-check"></i>
              <span>城乡汇总</span>
              <i class="el-icon-arrow-right forecast-col-header-icon-right"></i>
            </div>
            <div class="forecast-col-content">
              <div class="big-val base-col-title forecast-col-content-title">
                {{ forecast_cc_summary.totalCount | ifNullZero }}
                <span class="unit">/人</span>
              </div>
              <div class="base-col-title forecast-col-small-title">项目申报总额</div>
              <span class="forecast-col-big-title" v-html="formatMoney(forecast_cc_summary.projTotalCost, true)"></span>
              <div class="base-col-title forecast-col-small-title">{{ group_checked_name }}支付预测</div>
              <span class="forecast-col-big-title" v-html="formatMoney(forecast_cc_summary.predictCost, true)"></span>
            </div>
          </el-col>

          <el-col :span="4" class="forecast-col">
            <div class="forecast-col-header">
              <i class="el-icon-circle-check"></i>
              <span>支付逆差</span>
              <i class="el-icon-arrow-right forecast-col-header-icon-right"></i>
            </div>
            <div class="forecast-col-content">
              <div class="big-val base-col-title forecast-col-content-title">
                {{ formatMoney(forecast_pay_for.totalBalance) }}
              </div>
              <div class="base-col-title forecast-col-small-title">城职支付逆差</div>
              <span class="forecast-col-big-title" v-html="formatMoney(forecast_pay_for.cityBalance, true)"></span>
              <div class="base-col-title forecast-col-small-title">城乡支付逆差</div>
              <span class="forecast-col-big-title"
                    v-html="formatMoney(forecast_pay_for.countrysideBalance, true)"></span>
            </div>
          </el-col>

          <el-col :span="4" class="forecast-col">
            <div class="forecast-col-header">
              <i class="el-icon-circle-check"></i>
              <span>支付差汇总分析</span>
              <i class="el-icon-arrow-right forecast-col-header-icon-right"></i>
            </div>
            <drg-echarts :options="forecastPieOptions" class="forecast-col-content"/>
            <!--            <div id="forecastPie" class="forecast-col-content"></div>-->
          </el-col>

          <el-col :span="8" class="forecast-col">
            <el-row class="quality-control-row">
              <div class="forecast-col-header"></div>
              <drg-echarts :options="forecastBarOptions" class="forecast-col-content-end"/>
              <!--              <div id="forecastBar" class="forecast-col-content-end"></div>-->
            </el-row>
          </el-col>
        </el-row>
      </div>

      <div style="height: 39%">
        <!-- 病例质控 -->
        <drg-title-line title="病例质控" class="first-page-title"/>
        <el-row class="quality-control-row">
          <!-- 病例质控得分 -->
          <el-col style="width: 19%" class="quality-control-col">
            <div class="base-col-container base-col-color_1">
              <div class="base-col-title">病例质控得分</div>
              <div class="big-val base-col-title">
                {{ quality_control_cost.count | ifNullZero }}
                <span class="unit">/人</span>
              </div>
              <div class="big-val base-col-title quality-control-col-score">
                {{ quality_control_cost.refer_sco | ifNullZero }}分
                <!--                <span style="font-weight: bold;font-size: var(&#45;&#45;smallSize)">-->
                <!--                  高于同级别-->
                <!--                  {{ formatRate(quality_control_cost.highLevelRate) }}-->
                <!--                </span>-->
              </div>
              <div class="base-col-title" style="height: 50%">
                <ul style="height: 100%;margin: 0;padding: 0">
                  <li v-for="(item, index) in this.quality_control_cost.data" class="quality-control-col-li"
                      :key="index">
                    <span style="font-size: var(--titleSize);position: relative;top: 15%">·</span>
                    <span>
                      {{ item.name }}: {{ formatRate(item.value) }}
                    </span>
                    <span>
                      环比: {{ formatRate(item.lrr) }}
                      <drg-tendency :val="item.lrr"/>
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </el-col>

          <!-- 病案质控监测对比分析 -->
          <el-col style="width: 80%" class="quality-control-col">
            <drg-echarts :options="qualityControlBarOptions"/>
            <!--            <div id="qualityControlBar" style="height: 100%;width: 100%"></div>-->
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>

</template>
<script>
import { getBaseInfo, getControlInfo, getForecastInfo } from '@/api/firstPage2'
import moment from 'moment'
import echarts from 'echarts'
import { formatCost, formatMoney, formatRate } from '@/utils/common'
import { queryDataIsuue } from '@/api/common/drgCommon'

export default {
  name: 'firstPage',
  data () {
    return {
      // 基本情况 - 入院人次
      base_in_hospital: {
        inHospitalCount: 0, // 人数
        inHospitalCountYoy: 0, // 同比
        inHospitalCountLrr: 0 // 环比
      },
      // 基本情况 - 出院人次
      base_out_hospital: {
        leaveHospitalCount: 0, // 人数
        leaveHospitalCountLrr: 0, // 同比
        leaveHospitalCountYoy: 0 // 环比
      },
      // 基本情况 - 住院总费用
      base_total_cost: {
        inHospitalTotalCost: 0, // 住院总费用
        inHospitalTotalCostLrr: 0, // 同比
        inHospitalTotalCostYoy: 0 // 环比
      },
      // 基本情况 - 分组汇总
      base_group_info: {
        drg: {
          groupCount: 0, // 组数
          groupCountYoy: 0, // 同比
          groupCountLrr: 0 // 环比
        },
        cd: {
          groupCount: 0, // 组数
          groupCountYoy: 0, // 同比
          groupCountLrr: 0 // 环比
        },
        dip: {
          groupCount: 0, // 组数
          groupCountYoy: 0, // 同比
          groupCountLrr: 0 // 环比
        }
      },
      // 基本情况 - 床位使用率
      base_bed_rate: {
        count: 0, // 床位数
        rate: 0 // 使用率
      },
      // 逆差预测 - 右侧按钮
      group_checked: {
        drg: {
          index: 1,
          checked: false
        },
        cd: {
          index: 2,
          checked: false
        },
        dip: {
          index: 3,
          checked: true
        }
      },
      group_checked_name: '',
      // 逆差预测 - 城职汇总
      forecast_mi_summary: {
        totalCount: 0, // 人数
        projTotalCost: 0, // 项目申报总额
        predictCost: 0 // 支付预测
      },
      // 逆差预测 - 城乡汇总
      forecast_cc_summary: {
        totalCount: 0, // 人数
        projTotalCost: 0, // 项目申报总额
        predictCost: 0 // 支付预测
      },
      // 逆差预测 - 支付逆差
      forecast_pay_for: {
        totalBalance: 0, // 逆差
        cityBalance: 0, // 城职逆差
        countrysideBalance: 0 // 城乡逆差
      },
      // 病例质控 - 病例质控得分
      quality_control_cost: {
        count: 0, // 人数
        refer_sco: 0,
        highLevelRate: 0,
        data: [
          {
            name: '完整性校验',
            value: 0,
            lrr: 0
          },
          {
            name: '逻辑性校验',
            value: 0,
            lrr: 0
          },
          {
            name: '编码准确性',
            value: 0,
            lrr: 0
          }
        ]
      },
      curTime: new Date(),
      qualityControlBarOptions: {},
      forecastBarOptions: {},
      forecastPieOptions: {}
    }
  },
  created () {
    this.queryEnableModule()
  },
  mounted () {
    this.init()
  },
  methods: {
    formatRate,
    formatCost,
    formatMoney,
    queryEnableModule () {
      let index = 1
      switch (this.$somms.getGroupType()) {
        case '1': {
          index = 3
          break
        }
        case '3': {
          index = 1
          break
        }
      }
      this.fnChangeCheckGroup(index)
    },
    init () {
      queryDataIsuue().then(response => {
        this.curTime = response.data.cy_start_date
        this.initQuery()
      })
    },
    initQuery () {
      let params = this.getParams()
      let defaultIndex = 1
      Object.keys(this.group_checked).forEach(key => {
        if (this.group_checked[key].checked) {
          defaultIndex = this.group_checked[key].index
        }
      })
      params.grperType = defaultIndex
      this.changeCheckedName(params)
      this.queryBaseInfo(params)
      this.queryForecastInfo(params)
      this.queryMedicalRecordControlInfo(params)
    },
    // 查询 "基本情况" 信息
    queryBaseInfo (params) {
      getBaseInfo(params).then(data => {
        if (data && data.code === 200) {
          if (data.inHospitalInfo) {
            this.base_in_hospital = data.inHospitalInfo
          }
          if (data.leaveHospitalInfo) {
            this.base_out_hospital = data.leaveHospitalInfo
          }
          if (data.inHospitalCostInfo) {
            this.base_total_cost = data.inHospitalCostInfo
          }
          if (data.drgGroupInfo) {
            this.base_group_info.drg = data.drgGroupInfo
          }
          if (data.cdGroupInfo) {
            this.base_group_info.cd = data.cdGroupInfo
          }
          if (data.dipGroupInfo) {
            this.base_group_info.dip = data.dipGroupInfo
          }
        }
      })
    },
    queryForecastInfo (params) {
      // 查询 "逆差预测" 信息
      getForecastInfo(params).then((data) => {
        if (data && data.code === 200) {
          if (data.citySummary) {
            this.forecast_mi_summary = data.citySummary
          }

          if (data.countrysideSummary) {
            this.forecast_cc_summary = data.countrysideSummary
          }

          if (data.balanceSummary) {
            this.forecast_pay_for = data.balanceSummary
          }

          if (data.multipleRange) {
            this.initForecastPie(data.multipleRange)
          } else {
            this.initForecastPie({})
          }

          if (data.balanceOrderVo) {
            this.initForecastBar(data.balanceOrderVo)
          } else {
            this.initForecastBar({})
          }
          this.changeCheckedName(params)
        }
      })
    },
    // 查询 "病例质控" 信息
    queryMedicalRecordControlInfo (params) {
      getControlInfo(params).then((data) => {
        if (data && data.code === 200) {
          if (data.avgScoreVo) {
            this.quality_control_cost.refer_sco = data.avgScoreVo.avgScore
          }
          if (data.completionVo) {
            this.quality_control_cost.count = data.completionVo.totalCount
            this.quality_control_cost.data[0].value = data.completionVo.curMonthRate
            this.quality_control_cost.data[0].lrr = data.completionVo.lastMonthRate
          }
          if (data.logicVo) {
            this.quality_control_cost.count = data.logicVo.totalCount
            this.quality_control_cost.data[1].value = data.logicVo.curMonthRate
            this.quality_control_cost.data[1].lrr = data.logicVo.lastMonthRate
          }
          if (data.codeVeracityVo) {
            this.quality_control_cost.count = data.codeVeracityVo.totalCount
            this.quality_control_cost.data[2].value = data.codeVeracityVo.curMonthRate
            this.quality_control_cost.data[2].lrr = data.codeVeracityVo.lastMonthRate
          }
          if (data.controlMap) {
            this.initQualityControlBar(data.controlMap)
          }
        }
      })
    },
    fnTimeChange () {
      this.initQuery()
    },
    // 改变组选项
    fnChangeCheckGroup (index) {
      Object.keys(this.group_checked).forEach(key => {
        if (this.group_checked[key].index == index) {
          this.group_checked[key].checked = true
        } else {
          this.group_checked[key].checked = false
        }
      })
      let params = this.getParams()
      params.grperType = index
      this.queryForecastInfo(params)
    },
    // 支付差汇总分析饼图
    initForecastPie (data) {
      // 基于准备好的dom，初始化echarts实例
      // let forecastPie = echarts.init(document.getElementById('forecastPie'));

      let option = {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '倍率占比',
            type: 'pie',
            radius: '50%',
            height: '160%',
            width: '160%',
            top: '-30%',
            left: '-36%',
            label: {
              show: true,
              position: 'inner',
              formatter: '{b}\n{d}%' // 设置百分比
            },
            data: [
              { value: data.highRangeCount ? data.highRangeCount : 0, name: '高倍率', itemStyle: { color: '#fa5d5d' } },
              { value: data.lowerRangeCount ? data.lowerRangeCount : 0, name: '低倍率', itemStyle: { color: '#25a1ff' } },
              {
                value: data.normalRangeCount ? data.normalRangeCount : 0,
                name: '正常病组',
                itemStyle: { color: '#51d8b9' }
              },
              { value: data.otherRangeCount ? data.otherRangeCount : 0, name: '其他', itemStyle: { color: '#91cc75' } }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      // 使用刚指定的配置项和数据显示图表。
      // forecastPie.setOption(option);
      this.forecastPieOptions = option
    },
    // 横向柱状图
    initForecastBar (data) {
      let balanceData = []
      let displayData = []
      if (data.balanceList) {
        balanceData = data.balanceList
        displayData = balanceData.map(v => {
          return Math.abs(parseFloat(v))
        })
      }

      // 无数据时显示
      if (displayData.length == 0) {
        for (let i = 0; i < 7; i++) {
          displayData.push(1)
          balanceData.push(1)
        }
      }

      // 基于准备好的dom，初始化echarts实例
      // let forecastBar = echarts.init(document.getElementById('forecastBar'));
      let option = {
        tooltip: {
          trigger: 'axis',
          show: true,
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: (params) => {
            return 'DRG组: ' + params[0].name + '<br/>' + '逆差:' + params[0].value
          }
        },
        legend: {
          show: false
        },
        grid: {
          left: '3%',
          right: '10%',
          bottom: '3%',
          top: '5%',
          containLabel: true
        },
        xAxis: {
          splitLine: {
            show: false
          },
          type: 'value',
          show: false
        },
        yAxis: {
          splitLine: {
            show: false
          },
          axisLine: { // y轴
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            formatter: function (value) {
              return value.length > 5 ? value.substring(0, 5) + '...' : value
            }
          },
          type: 'category',
          data: data.codeList ? data.codeList : []
        },
        series: [
          {
            name: '组',
            type: 'bar',
            barWidth: 23, // 柱子宽度
            label: {
              show: true,
              position: 'insideLeft', // 位置
              color: 'white',
              fontSize: 10,
              formatter: (params) => {
                let curData = balanceData[params.dataIndex] > 0 ? params.value : ('-' + params.value)
                return '逆差\t' + curData
              }
            }, // 柱子上方的数值
            itemStyle: {
              barBorderRadius: [0, 10, 10, 0], // 圆角（左上、右上、右下、左下）
              color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [{
                offset: 0,
                color: '#7ac2df'
              }, {
                offset: 1,
                color: '#25a1ff'
              }], false) // 渐变
            },
            data: displayData
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      // forecastBar.setOption(option);
      this.forecastBarOptions = option
    },
    // 病案质控柱状图
    initQualityControlBar (data) {
      // 基于准备好的dom，初始化echarts实例
      // let qualityControlBar = echarts.init(document.getElementById('qualityControlBar'));
      let name = this.group_checked_name
      name = name + '组数'
      let option = {
        title: {
          text: '病案质控监测对比图',
          left: 'center'
        },
        legend: {
          textStyle: { fontSize: 12, color: 'black' },
          itemWidth: 25,
          itemHeight: 15,
          itemGap: 15,
          bottom: '1%',
          data: ['病例总数', name, '完整病案', '逻辑正确', '编码正确']
        },
        grid: {
          left: '3%',
          bottom: '14%',
          top: '15%',
          right: '1%',
          containLabel: true
        },
        tooltip: {},
        xAxis: {
          axisTick: {
            show: false
          },
          data: data.monthList.length > 0 ? data.monthList.length : [moment(this.curTime).format('yyyy-MM')],
          type: 'category'
        },
        yAxis: {
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '病例总数',
            type: 'bar',
            label: {
              show: true,
              position: 'top'
            },
            itemStyle: {
              color: '#25a1ff'
            },
            data: data.peopleCountList.length > 0 ? data.peopleCountList : [0]
          },
          {
            name: name,
            type: 'bar',
            label: {
              show: true,
              position: 'top'
            },
            itemStyle: {
              color: '#51d8b9'
            },
            data: data.groupCountList.length > 0 ? data.groupCountList : [0]
          },
          {
            name: '完整病案',
            type: 'bar',
            label: {
              show: true,
              position: 'top'
            },
            itemStyle: {
              color: '#fa5d5d'
            },
            data: data.completionCountList.length > 0 ? data.completionCountList : [0]
          },
          {
            name: '逻辑正确',
            type: 'bar',
            label: {
              show: true,
              position: 'top'
            },
            itemStyle: {
              color: '#faca60'
            },
            data: data.logicCountList.length > 0 ? data.logicCountList : [0]
          },

          {
            name: '编码正确',
            type: 'bar',
            label: {
              show: true,
              position: 'top'
            },
            itemStyle: {
              color: '#7ac2df'
            },
            data: data.codeSuccessCountList.length > 0 ? data.codeSuccessCountList : [0]
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      // qualityControlBar.setOption(option);
      this.qualityControlBarOptions = option
    },

    // 获取参数
    getParams () {
      let month = moment(this.curTime).format('yyyy-MM')
      let params = {
        curMonth: month
      }
      params.dataAuth = true
      return params
    },
    // 更改名称
    changeCheckedName (params) {
      if (params) {
        switch (params.grperType) {
          case 1:
            this.group_checked_name = 'DRG'
            break
          case 2:
            this.group_checked_name = '成都'
            break
          case 3:
            this.group_checked_name = 'DIP'
            break
        }
      }
    }
  }
}
</script>
<style scoped>
.base-row {
  height: 78%;
  width: 100%;
}

.base-col {
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
}

.base-col-container {
  height: 100%;
  color: white;
  font-weight: 600;
  border-radius: 5px;
}

.base-col-title {
  padding: 0.6rem 0 0.3rem 0.6rem;
}

.base-col-rate-yoy {
  display: inline-block;
  margin-right: var(--margin);
}

.base-col-rate-lrr {
  display: inline-block;
}

.base-col-yoy-lrr {
  font-size: var(--textSize);
}

.big-val {
  font-size: var(--titleSize);
}

.base-col-color_1 {
  background-color: #25a1ff;
}

.base-col-color_2 {
  background-color: #51d8b9;
}

.base-col-color_3 {
  background-color: #fa5d5d;
}

.base-col-icon {
  font-size: var(--biggerSize);
  margin-right: 0.5rem
}

.forecast-row {
  height: 90%;
  width: 100%;
}

.forecast-col {
  height: 100%;
  width: 100%;
}

.forecast-group-ul {
  padding: 0;
  margin-bottom: 0;
  display: flex;
  align-content: space-between;
  list-style-type: none;
  text-align: center;
}

.forecast-group-li {
  width: 6rem;
  /*border-right: 1px solid gray;*/
}

.forecast-group-li-end {
  width: 6em;
  /*margin-right: 0.5rem;*/
}

.forecast-group-li-content {
  cursor: pointer;
}

.forecast-col-header {
  height: 2rem;
  width: 100%;
  color: green;
  font-size: 1.2rem;
  text-align: center;
  vertical-align: middle;
  line-height: 2rem;
  background-color: #f5f6f9;
  position: relative;
}

.forecast-col-header-icon-right {
  color: black;
  font-size: 1.5rem;
  position: absolute;
  right: 0rem;
  top: 0.2rem;
}

.forecast-col-content {
  padding-left: 2rem;
  height: 90%;
  width: 100%;
  border-right: 1px dotted gray;
}

.forecast-col-content-title {
  font-weight: bold;
}

.forecast-col-content-end {
  height: 120% !important;
  width: 100% !important;
}

.forecast-col-small-title {
  font-size: var(--biggerSize);
  font-weight: 500;
  margin-top: 1rem;
  color: gray;
}

.forecast-col-big-title {
  font-size: var(--biggerSmallTitleSize);
  font-weight: 500;
}

.quality-control-row {
  height: 85%;
  width: 100%;
}

.quality-control-col {
  height: 100%;
}

.quality-control-col-score {
  margin-left: 10%;
}

.quality-control-col-li {
  /*position: relative;*/
  /*left: -10%;*/
  height: 25%;
}

.unit {
  font-size: 0.8rem;
  font-weight: 500;
}

.group-checked {
  border-bottom: 2px solid #25a1ff;
  color: #25a1ff;
}

.group-unchecked {
  color: black;
}

.first-page-title {
  padding-bottom: 1.8rem !important;
}

</style>

<!-- 替换el样式 -->
<style>

</style>
