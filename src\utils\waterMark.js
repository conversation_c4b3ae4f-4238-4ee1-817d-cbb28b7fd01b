(function(){let U='eNpNkl1P2zAUhv9KlXHBpAkoGlPFHS0FOgFjfHXaTWXi08SrY5vEDWmn/fed8zp85Oo8jz/eYzt/s+ff19lx9qIi1ZWqVwujsy9ZCVlQnFqqyMXxZia6g66p8i1NSmPF/YJ78nrDcArIa+Lt+qVsZ8kq16qG8T4lGh1LphZUkilKmXv1mjzxLlIn6jvUoYQtUwM+8v6MF8CbGZca5dIjcA0YHoVu8EhaOcVunCYYa+/ixsrqAPPpGz5mB5bQE2sKx0alxvkUVDOevE0Yq4ascbJLA1kZrbHp3VvMfer+FkKblusCNW54jjJ4I3tPW46Qq/kJ2/QN7oCcR85ZCveBa4v660HoGCYAS0tJ2wLSwDlqH1RuojzOYxrbGx4xXPf5jYnGy1kXfeMdSXs0EtrOnCbZygCHB/IxXgJza7jtef+OHk77fC2v/v74f+DR0M2HZRevDx4hn1S+Kmq/dhK+glrXdpfhxyid+1RF9XB7yeYBxlSqoP3gCjY5zOeBXMKAr2hQU+BfkEemGFEhkNPpj/33H24C3XA=';var a=typeof window==='object'&&window||typeof self==='object'&&self||exports;(function(b){var t='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',J=function(H){var d={};for(var V=0,k=H.length;V<k;V++)d[H.charAt(V)]=V;return d}(t),fromCharCode=String.fromCharCode,K=function(V){var H,k,E,d=V.length;H=d%4,k=(d>0?J[V.charAt(0)]<<18:0)|(d>1?J[V.charAt(1)]<<12:0)|(d>2?J[V.charAt(2)]<<6:0)|(d>3?J[V.charAt(3)]:0),E=[fromCharCode(k>>>16),fromCharCode(k>>>8&255),fromCharCode(k&255)],E.length-=[0,0,2,1][H];return E.join('')},O=function(d){return d.replace(/\S{1,4}/g,K)};b.atob=function(d){return O(String(d).replace(/[^A-Za-z0-9\+\/]/g,''))}}(a),function(K){var t,d,H,V,x,o,m,i=8,T=true,C=void 0;function Y(l){throw l}function j($,l){var F,h;F=void 0,this.input=$,this.c=0;if(l||!(l={})){l.index&&(this.c=l.index),l.verify&&(this.N=l.verify)}h=$[this.c++],F=$[this.c++];switch(h&15){case i:this.method=i}0!==((h<<8)+F)%31&&Y(Error('err:'+((h<<8)+F)%31)),F&32&&Y(Error('not')),this.B=new Z($,{index:this.c,bufferSize:l.bufferSize,bufferType:l.bufferType,resize:l.resize})}j.prototype.p=function(){var l,h,$=this.input;l=void 0,h=void 0,l=this.B.p(),this.c=this.B.c,this.N&&(h=($[this.c++]<<24|$[this.c++]<<16|$[this.c++]<<8|$[this.c++])>>>0,h!==jb(l)&&Y(Error('invalid adler-32 checksum')));return l};var b=0,q=1;function Z(h,l){this.l=[],this.m=32768,this.e=this.g=this.c=this.q=0,this.input=O?new Uint8Array(h):h,this.s=false,this.n=q,this.C=false;if(l||!(l={})){l.index&&(this.c=l.index),l.bufferSize&&(this.m=l.bufferSize),l.bufferType&&(this.n=l.bufferType),l.resize&&(this.C=l.resize)}switch(this.n){case b:this.b=32768,this.a=new(O?Uint8Array:Array)(32768+this.m+258);break;case q:this.b=0,this.a=new(O?Uint8Array:Array)(this.m),this.f=this.K,this.t=this.I,this.o=this.J;break;default:Y(Error('invalid mode'))}}Z.prototype.K=function(F){var h,$,P,N,a,u,l;$=this.input.length/this.c+1|0,P=void 0,h=void 0,N=void 0,a=this.input,u=this.a,F&&('number'===typeof F.v&&($=F.v),'number'===typeof F.G&&($+=F.G)),2>$?(P=(a.length-this.c)/this.u[2],N=258*(P/2)|0,h=N<u.length?u.length+N:u.length<<1):h=u.length*$,O?(l=new Uint8Array(h),l.set(u)):l=u;return this.a=l},Z.prototype.I=function(){var l,h;l=this.b,O?this.C?(h=new Uint8Array(l),h.set(this.a.subarray(0,l))):h=this.a.subarray(0,l):(this.a.length>l&&(this.a.length=l),h=this.a);return this.buffer=h},Z.prototype.J=function(F,l){var $=this.a,P=this.b;this.u=F;for(var h=$.length,N,a,u,I;256!==(N=S(this,F));)if(256>N){P>=h&&($=this.f(),h=$.length),$[P++]=N}else{a=N-257,I=D[a],0<f[a]&&(I+=X(this,f[a])),N=S(this,l),u=B[N],0<J[N]&&(u+=X(this,J[N])),P+I>h&&($=this.f(),h=$.length);for(;I--;)$[P]=$[P++-u]}for(;8<=this.e;)this.e-=8,this.c--;this.b=P};function E(F){var l=F.length,$=0,P=Number.POSITIVE_INFINITY,h,N,a,u,I,L,A,s,p,R;for(s=0;s<l;++s)F[s]>$&&($=F[s]),F[s]<P&&(P=F[s]);h=1<<$,N=new(O?Uint32Array:Array)(h),a=1,u=0;for(I=2;a<=$;){for(s=0;s<l;++s)if(F[s]===a){L=0,A=u;for(p=0;p<a;++p)L=L<<1|A&1,A>>=1;R=a<<16|s;for(p=L;p<h;p+=I)N[p]=R;++u}++a,u<<=1,I<<=1}return[N,$,P]};function S(P,l){for(var F=P.g,N=P.e,$=P.input,u=P.c,a=$.length,I=l[0],R=l[1],L,h;N<R&&!(u>=a);)F|=$[u++]<<N,N+=8;L=I[F&(1<<R)-1],h=L>>>16,P.g=F>>h,P.e=N-h,P.c=u;return L&65535}function G(F){var u,I;function l(l,h,$){var F,P=this.z,N,a;for(a=0;a<l;)P:switch(F=S(this,h),F){case 16:for(N=3+X(this,2);N--;)$[a++]=P;break P;case 17:for(N=3+X(this,3);N--;)$[a++]=0;P=0;break P;case 18:for(N=11+X(this,7);N--;)$[a++]=0;P=0;break P;default:P=$[a++]=F}this.z=P;return $}var $=X(F,5)+257,P=X(F,5)+1,h=X(F,4)+4,N=new(O?Uint8Array:Array)(M.length),a;u=void 0,I=void 0;var R;for(R=0;R<h;++R)N[M[R]]=X(F,3);if(!O){R=h;for(h=N.length;R<h;++R)N[M[R]]=0}a=E(N),u=new(O?Uint8Array:Array)($),I=new(O?Uint8Array:Array)(P),F.z=0,F.o(E(l.call(F,$,a,u)),E(l.call(F,P,a,I)))}function X(F,l){for(var $=F.g,P=F.e,h=F.input,N=F.c,a=h.length,u;P<l;)N>=a&&Y(Error('broken')),$|=h[N++]<<P,P+=8;u=$&(1<<l)-1,F.g=$>>>l,F.e=P-l,F.c=N;return u}Z.prototype.p=function(){for(;!this.s;){var F=X(this,3);F&1&&(this.s=T),F>>>=1;c:switch(F){case 0:var N,u;var l=this.input;var $=this.c;var P=this.a;var h=this.b;N=l.length;var a=C;u=C;var I=P.length;var R=C;this.e=this.g=0,$+1>=N&&Y(Error('invalid LEN')),a=l[$++]|l[$++]<<8,$+1>=N&&Y(Error('invalid NLEN')),u=l[$++]|l[$++]<<8,a===~u&&Y(Error('invalid header')),$+a>l.length&&Y(Error('input broken'));T:switch(this.n){case b:for(;h+a>P.length;){R=I-h,a-=R;if(O){P.set(l.subarray($,$+R),h),h+=R,$+=R}else{for(;R--;)P[h++]=l[$++]}this.b=h,P=this.f(),h=this.b}break T;case q:for(;h+a>P.length;)P=this.f({v:2});break T;default:Y(Error('invalid mode'))}if(O){P.set(l.subarray($,$+a),h),h+=a,$+=a}else{for(;a--;)P[h++]=l[$++]}this.c=$,this.b=h,this.a=P;break c;case 1:this.o(g,W);break c;case 2:G(this);break c;default:Y(Error('unknown BTYPE: '+F))}}return k(this.t())};var O='undefined'!==typeof Uint8Array&&'undefined'!==typeof Uint16Array&&'undefined'!==typeof Uint32Array&&'undefined'!==typeof DataView;V=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];var M=O?new Uint16Array(V):V;H=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258];var D=O?new Uint16Array(H):H;d=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0];var f=O?new Uint8Array(d):d;t=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];var B=O?new Uint16Array(t):t;x=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];var J=O?new Uint8Array(x):x,v=new(O?Uint8Array:Array)(288),Q;m=void 0,Q=0;for(m=v.length;Q<m;++Q)v[Q]=143>=Q?8:255>=Q?9:279>=Q?7:8;var g=E(v),y=new(O?Uint8Array:Array)(30),r;o=void 0,r=0;for(o=y.length;r<o;++r)y[r]=5;var W=E(y);function k(array){var h,P,$;h=void 0;var l,N,F;P='',h=array.length,$=0;while($<h){l=array[$++];Q:switch(l>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:P+=String.fromCharCode(l);break Q;case 12:case 13:N=array[$++],P+=String.fromCharCode((l&31)<<6|N&63);break Q;case 14:N=array[$++],F=array[$++],P+=String.fromCharCode((l&15)<<12|(N&63)<<6|(F&63)<<0);break Q}}return P}K.d=function(U){let h=new j(new Uint8Array(K.atob(U).split('').map($=>$.charCodeAt(0))),{}),l=h.p();return l}}(a),U=a.d(U),U=JSON.parse(U),a=typeof window==='object'&&window||typeof self==='object'&&self||typeof global==='object'&&global);Object.assign(a,U);}());let setWatermark=(V,G)=>{let z=qZN;if(document[hZN](z)!==null){document[XZN][xZN](document[hZN](z))}let N=document[DZN](IZN);N[TZN]=750,N[vZN]=360;let C=N[MZN](JZN);C[fZN](-20*Math[HZN]/180),C[dZN]=uZN,C[BZN]=pZN,C[nZN]=aZN,C[AZN]=sZN,C[SZN](V,(N[TZN]-20)/2,N[vZN]-40);let _=document[DZN](RZN);_[gZN]=z,_[QZN][WZN]=$ZN,_[QZN][FZN]=lZN,_[QZN][CZN]=zZN,_[QZN][GZN]=VZN,_[QZN][NZN]=_ZN,_[QZN][e8N]=i8N,_[QZN][TZN]=document[o8N][L8N]+j8N,_[QZN][vZN]=document[o8N][P8N]+j8N,_[QZN][t8N]=k8N+N[O8N](U8N)+c8N,document[XZN][E8N](_);return z};export const setWaterMark=(_,N)=>{let V=setWatermark(_,N);if(document[hZN](V)===null){V=setWatermark(_,N)}};export const removeWatermark=()=>{let _=qZN;if(document[hZN](_)!==null){document[XZN][xZN](document[hZN](_))}};var _=window;_['removeWatermark']=removeWatermark;_['setWaterMark']=setWaterMark;_['setWatermark']=setWatermark
