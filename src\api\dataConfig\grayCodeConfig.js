import request from '@/utils/request'
/*
 * 标准手术管理模块
 */
// 查询
export function fetchGrayCodeList (params) {
  return request({
    url: '/grayCode/queryDiagGrayCodeList',
    method: 'post',
    params: params
  })
}

// 保存
export function saveGrayCode (data) {
  return request({
    url: '/grayCode/saveGrayCode',
    method: 'post',
    data
  })
}
// 删除
export function deleteGrayCode (params) {
  return request({
    url: '/grayCode/deleteGrayCode',
    method: 'post',
    params
  })
}
