<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-date-range
             show-se-date-range
             show-hos-dept
             headerTitle="查询条件"
             contentTitle="手术分析"
             :container="true"
             @query="handleSearchList" @reset="handleResetSearch">

      <template slot="buttons">
        <el-button @click="openMedicalDetail()" size="mini">病案详情</el-button>
      </template>

      <template slot="containerContent">
        <!--ECHART图-->
        <el-row :gutter="10" style="height: 100%">
          <el-col :span="16" style="height: 99%">
            <div id="hashChartContent" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
          </el-col>
          <el-col :span="8" style="height: 33%">
            <div id="pieDRGs" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
          </el-col>
          <el-col :span="8" style="height: 33%;padding-top:10px">
            <div id="piePersons" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
          </el-col>
          <el-col :span="8" style="height: 33%;padding-top:10px;">
            <div id="pieDeadPersons" class="el-card is-always-shadow" style="height: 100%;width: 100%;"></div>
          </el-col>
        </el-row>
        <!-- 病案详情界面 -->
        <el-dialog :title="dialogTitle" width="75%" style="margin-top:-4vh;" :visible.sync="dialogVisible" :close-on-click-modal="false" @close="closeDialog" v-som-dialog-drag>
          <el-form :inline="true" :model="listQuery" size="mini" >
            <el-form-item :label="timeName">
              <el-date-picker
                v-model="listQuery.cysj"
                type="daterange"
                size="mini"
                unlink-panels
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="dateChangeCysj"
                :picker-options="pickerOptions">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="出院科别" class="som-el-form-item-margin-left" v-if="this.$somms.hasDeptRole()">
              <div style="height: 30px;border-radius: 5px">
                <select-tree v-model="listQuery.b16c" :options="depts" :props="defaultProps" placeholder="请选择出院科别"/>
              </div>
            </el-form-item>
            <el-form-item label="DRGs组" class="som-el-form-item-margin-left">
              <el-autocomplete
                popper-class="my-autocomplete"
                size="mini"
                v-model="queryDrg"
                :fetch-suggestions="querySearchAsync"
                placeholder="请输入DRGs编码或者名称"
                @select="handleSelect"
                :popper-append-to-body="true"
                :clearable="true"
                :trigger-on-focus="false"
                ref='elautocomplete'>
                <template slot-scope="{ item }">
                  <div class="code">{{ item.drgsCode }}</div>
                  <span class="name">{{ item.drgsName }}</span>
                </template>
              </el-autocomplete>
            </el-form-item>
            <el-button
              @click="handleSearchListForDialog()"
              type="primary"
              size="mini">
              查询
            </el-button>
            <el-button class="expBtn" @click="exportExcel()" size="mini">导出</el-button>
            <el-button class="reset" @click="fnReset">重置</el-button>
            <el-row :gutter="0">
              <el-col align="left">
                <el-form-item label="风险等级">
                  <el-checkbox-group v-model="listQuery.riskLevel">
                    <el-checkbox label="高风险"></el-checkbox>
                    <el-checkbox label="中高风险"></el-checkbox>
                    <el-checkbox label="中低风险"></el-checkbox>
                    <el-checkbox label="低风险"></el-checkbox>
                    <el-checkbox label="无风险"></el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col align="left">
                <el-form-item label="离院方式">
                  <el-checkbox-group v-model="listQuery.lyfs">
                    <el-checkbox label="医嘱离院"></el-checkbox>
                    <el-checkbox label="医嘱转院"></el-checkbox>
                    <el-checkbox label="医嘱转社区卫生服务机构/乡镇卫生院" ></el-checkbox>
                    <el-checkbox label="非医嘱离院"></el-checkbox>
                    <el-checkbox label="死亡"></el-checkbox>
                    <el-checkbox label="其他"></el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div class="table-container" style="margin-top:1px;flex:5px; overflow-y:auto;">
            <el-table ref="drgsSafetyAndQualityTable"
                      id="saqTable"
                      size="mini"
                      stripe
                      :height="tableHeight"
                      :data="list"
                      style="width: 100%;"
                      v-loading="listLoading"
                      border>
              <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
              <el-table-column fixed label="病案号" align="left" width="100" :show-overflow-tooltip="true">
                <template slot-scope="scope">{{scope.row.patientId | formatIsEmpty}}</template>
              </el-table-column>
              <el-table-column fixed label="姓名"  align="center" width="70" :show-overflow-tooltip="true">
                <template slot-scope="scope">{{scope.row.name | formatIsEmpty}}</template>
              </el-table-column>
              <el-table-column  label="DRGs编码"  align="center" width="90" :show-overflow-tooltip="true">
                <template slot-scope="scope">{{scope.row.drgsCode | formatIsEmpty}}</template>
              </el-table-column>
              <el-table-column label="DRGs名称"  align="center"  width="140" :show-overflow-tooltip="true">
                <template slot-scope="scope">{{scope.row.drgsName | formatIsEmpty}}</template>
              </el-table-column>
              <el-table-column  label="出院科室"  align="center" width="90" :show-overflow-tooltip="true">
                <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
              </el-table-column>
              <el-table-column label="风险等级"  prop="riskLevel"  align="center" width="90" :formatter="(col,row)=>dictFormatter(row,col,'RISK_LEVEL')" :show-overflow-tooltip="true">
              </el-table-column>
              <el-table-column label="离院方式"  prop="dscgWay"  align="center" width="90" :formatter="(col,row)=>dictFormatter(row,col,'B34C')" :show-overflow-tooltip="true">
              </el-table-column>
              <el-table-column label="标杆天数"  align="center" width="80" :show-overflow-tooltip="true">
                <template slot-scope="scope">{{scope.row.standardInHosDays | formatIsEmpty}}</template>
              </el-table-column>
              <el-table-column label="住院天数"  align="center" width="80" :show-overflow-tooltip="true">
                <template slot-scope="scope">{{scope.row.inHosDays | formatIsEmpty}}</template>

              </el-table-column>
              <el-table-column label="天数差值"  align="center" width="80" :show-overflow-tooltip="true">
                <template slot-scope="scope">
                  <div v-if="scope.row.inHosDays==null||scope.row.inHosDays==''||scope.row.inHosDays==0||scope.row.standardInHosDays==null||scope.row.standardInHosDays==''||scope.row.standardInHosDays==0">
                    -
                  </div>
                  <div v-else-if="Number(scope.row.inHosDays)>Number(scope.row.standardInHosDays)">
                    <i class="el-icon-caret-bottom"></i>
                    <span style="font-size: 10px;color:#FF0000;font-weight: bold">
                {{(Number(scope.row.standardInHosDays)-Number(scope.row.inHosDays)).toFixed(1)}}
              </span>
                  </div>
                  <div v-else-if="Number(scope.row.inHosDays)<=Number(scope.row.standardInHosDays)">
                    <i class="el-icon-caret-top"></i>
                    <span style="font-size: 10px;color:#00CC00;font-weight: bold">
                +{{(Number(scope.row.standardInHosDays)-Number(scope.row.inHosDays)).toFixed(1)}}
              </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="标杆费用"  align="center" width="80" :show-overflow-tooltip="true">
                <template slot-scope="scope">{{scope.row.standardInHosCost | formatIsEmpty}}</template>
              </el-table-column>
              <el-table-column label="住院费用"  align="center" width="80" :show-overflow-tooltip="true">
                <template slot-scope="scope">{{scope.row.inHosTotalCost | formatIsEmpty}}</template>
              </el-table-column>
              <el-table-column label="费用差值"  align="center" width="80" :show-overflow-tooltip="true">
                <template slot-scope="scope">
                  <div v-if="scope.row.inHosTotalCost==null||scope.row.inHosTotalCost==''||scope.row.inHosTotalCost==0||scope.row.standardInHosCost==null||scope.row.standardInHosCost==''||scope.row.standardInHosCost==0">
                    -
                  </div>
                  <div v-else-if="Number(scope.row.inHosTotalCost)>Number(scope.row.standardInHosCost)">
                    <i class="el-icon-caret-bottom"></i>
                    <span style="font-size: 10px;color:#FF0000;font-weight: bold">
                {{(Number(scope.row.standardInHosCost)-Number(scope.row.inHosTotalCost)).toFixed(2)}}
              </span>
                  </div>
                  <div v-else-if="Number(scope.row.inHosTotalCost)<=Number(scope.row.standardInHosCost)">
                    <i class="el-icon-caret-top"></i>
                    <span style="font-size: 10px;color:#00CC00;font-weight: bold">
                +{{(Number(scope.row.standardInHosCost)-Number(scope.row.inHosTotalCost)).toFixed(2)}}
              </span>
                  </div>
                </template>
              </el-table-column>
              <!--<el-table-column label="结算清单总费用"  align="center" width="120">-->
              <!--<template slot-scope="scope">{{scope.row.totalBaseCost | formatIsEmpty}}</template>-->
              <!--</el-table-column>-->
              <el-table-column label="主要诊断"  align="center" width="140" :show-overflow-tooltip="true">
                <template slot-scope="scope">{{scope.row.mainDiagnoseCodeAndName | formatIsEmpty}}</template>
              </el-table-column>
              <el-table-column label="主要手术"  align="center" width="140" :show-overflow-tooltip="true">
                <template slot-scope="scope">{{scope.row.mainOperativeCodeAndName | formatIsEmpty}}</template>
              </el-table-column>
            </el-table>
          </div>
          <span slot="footer" class="dialog-footer">
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            layout="total, sizes,prev, pager, next,jumper"
            :page-size="listQuery.pageSize"
            :page-sizes="[200,1000,5000,10000]"
            :current-page.sync="listQuery.pageNum"
            :total="total">
          </el-pagination>
        </div>
      </span>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import SelectTree from '@/components/SelectTree/index'
import { queryDataIsuue, querySelectTreeAndSelectList, queryLikeDrgsByPram } from '@/api/common/drgCommon'
import { fetchList, getCountInfo } from '@/api/hospitalAnalysis/safetyAndQuality'
import { formaterDict } from '@/utils/dict'
import { elExportExcel } from '@/utils/exportExcel'
import echarts from 'echarts'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date,
  b16c: null,
  queryDrg: '',
  riskLevel: [],
  lyfs: []
}
export default {
  name: 'scutQuat',
  components: { SelectTree },
  inject: ['reload'],
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      list: null,
      total: null,
      cy_start_date: null,
      cy_end_date: null,
      tableHeight: '420px',
      b16c: null,
      // listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      dialogVisible: false,
      dialogTitle: 'DRGs各风险等级病案详情',
      listQuery: Object.assign({}, defaultListQuery),
      queryDrg: '',
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      riskLevel: ['无', '低', '中低', '中高', '高'],
      riskLevelColor: ['rgba(36,185,179,0.7)', 'rgba(40,138,242,0.7)', 'rgba(253,94,81,0.7)', 'rgba(145,204,117,0.7)', '#CCFF66'],
      // 各风险组数
      noneDRGs: 0,
      lowDRGs: 0,
      minLowDRGs: 0,
      minHighDRGs: 0,
      highDRGs: 0,
      // 各风险组出院人数
      nonePersons: 0,
      lowPersons: 0,
      minLowPersons: 0,
      minHigPersons: 0,
      highPersons: 0,
      // 各风险组死亡人数
      noneDeadPersons: 0,
      lowDeadPersons: 0,
      minLowDeadPersons: 0,
      minHigDeadPersons: 0,
      highDeadPersons: 0,
      allDRGs: 0,
      allPersons: 0,
      allDeadPersons: 0,
      hashArray: [],
      // 风险等级线（ln）
      riskLine: {},
      publicOption: {
        backgroundColor: '#fff',
        title: [{
          text: '',
          textStyle: {
            fontSize: 14,
            align: 'left'
          },
          left: 'left',
          padding: [5, 5, 20, 5]
        },
        {
          text: '',
          textStyle: {
            fontSize: 13
          },
          left: '31%',
          top: '50%'
        }
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          x: 'right',
          y: 'center' // 延Y轴居中
        },
        series: [
          {
            name: '',
            type: 'pie',
            center: ['36%', '55%'],
            radius: ['45%', '60%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: true
              },
              emphasis: {
                show: false,
                textStyle: {
                  fontSize: '26',
                  fontWeight: 'bold'
                }
              }
            },
            labelLine: {
              normal: {
                show: true
              }
            }
          }
        ],
        myChart_hash: null
      },
      timeName: '出院时间'
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.findSelectTreeAndSelectList()
      // 获取数据查询时间
      this.getDataIsuue()
    })
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'RISK_LEVEL,B34C')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    /**
       * 码表渲染方法
       */
    dictFormatter (col, row, dic_type) {
      return formaterDict(row[col.property], this.dictVoList[dic_type])
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getCount()
      })
    },
    resetInit () {
      this.noneDRGs = 0
      this.lowDRGs = 0
      this.minLowDRGs = 0
      this.minHighDRGs = 0
      this.highDRGs = 0
      // 各风险组出院人数
      this.nonePersons = 0
      this.lowPersons = 0
      this.minLowPersons = 0
      this.minHigPersons = 0
      this.highPersons = 0
      // 各风险组死亡人数
      this.noneDeadPersons = 0
      this.lowDeadPersons = 0
      this.minLowDeadPersons = 0
      this.minHigDeadPersons = 0
      this.highDeadPersons = 0
      this.allDRGs = 0
      this.allPersons = 0
      this.allDeadPersons = 0
      this.hashArray = []
    },
    getCount () {
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.b16c = this.listQuery.deptCode
      getCountInfo(this.submitListQuery).then(response => {
        let itemdata = response.data
        let list = itemdata.riskDRGsList
        this.riskLine = itemdata.riskLevelLine
        let len = list.length
        // if(!(len>0)){
        //   //数据为空
        //   return;
        // }
        let itemDeadLeve = 0
        // 重置查询结果统计数据
        this.resetInit()
        this.allDRGs = len
        for (let i = 0; i < len; i++) {
          itemDeadLeve = list[i].bke725
          switch (itemDeadLeve) {
            case '0':
              this.noneDRGs++
              this.nonePersons += Number(list[i].bke766)
              this.noneDeadPersons += Number(list[i].bke818)
              break
            case '1':
              this.lowDRGs++
              this.lowPersons += Number(list[i].bke766)
              this.lowDeadPersons += Number(list[i].bke818)
              break
            case '2':
              this.minLowDRGs++
              this.minLowPersons += Number(list[i].bke766)
              this.minLowDeadPersons += Number(list[i].bke818)
              break
            case '3':
              this.minHighDRGs++
              this.minHigPersons += Number(list[i].bke766)
              this.minHigDeadPersons += Number(list[i].bke818)
              break
            case '4':
              this.highDRGs++
              this.highPersons += Number(list[i].bke766)
              this.highDeadPersons += Number(list[i].bke818)
              break
            default:
              break
          }
          if (list[i].lnbke728) {
            this.hashArray.push([list[i].lnbke779, list[i].lnbke728, list[i].bke716, list[i].bke717])
          }
          this.allPersons += Number(list[i].bke766)
          this.allDeadPersons += Number(list[i].bke818)
        }
        // 执行生成所以的操作
        this.createAllChart()
      })
    },
    createAllChart () {
      let riskDRGsPieData = []
      let riskPersonsPieData = []
      let riskDeadPersonsPieData = []
      // 组装好的源数据
      let riskDRGsData = [this.noneDRGs, this.lowDRGs, this.minLowDRGs, this.minHighDRGs, this.highDRGs]
      let riskPersonsData = [this.nonePersons, this.lowPersons, this.minLowPersons, this.minHigPersons, this.highPersons]
      let riskDeadPersonsData = [this.noneDeadPersons, this.lowDeadPersons, this.minLowDeadPersons, this.minHigDeadPersons, this.highDeadPersons]
      let len = this.riskLevel.length
      for (let i = 0; i < len; i++) {
        riskDRGsPieData.push({ name: this.riskLevel[i], value: riskDRGsData[i], itemStyle: { color: this.riskLevelColor[i] } })
        riskPersonsPieData.push({ name: this.riskLevel[i], value: riskPersonsData[i], itemStyle: { color: this.riskLevelColor[i] } })
        // if(riskDeadPersonsData[i]!=0){
        riskDeadPersonsPieData.push({ name: this.riskLevel[i], value: riskDeadPersonsData[i], itemStyle: { color: this.riskLevelColor[i] } })
        // }
      }
      // 创建饼图-各风险等级组数
      this.createRiskDRGsPieChart(riskDRGsPieData)
      // 创建饼图-各风险等级出院人数
      this.createRiskPersonsPieChart(riskPersonsPieData)
      // 创建饼图-各风险等级死亡人数
      this.createRiskDeadPersonsPieChart(riskDeadPersonsPieData)
      // 创建hash 散列表
      this.createRiskHashChart(this.hashArray, this.riskLine)
    },
    // 创建各风险等级组数饼图
    createRiskDRGsPieChart (data) {
      let option = this.publicOption
      let allDRGs = this.allDRGs
      let name = '各风险等级DRGs占比'
      option.profttl[0].text = name
      option.profttl[1].text = allDRGs + '组'
      option.series[0].name = name
      option.series[0].data = data
      let _this = this
      let riskLevelData = []
      for (let i = 0; i < data.length; i++) {
        riskLevelData.push(data[i].name)
      }
      option.legend.data = riskLevelData
      option.legend.formatter = function (name) {
        let index = 0
        let clientlabels = riskLevelData
        let clientcounts = data
        clientlabels.forEach(function (value, i) {
          if (value == name) {
            index = i
          }
        })
        let returnData = (clientcounts[index].value / allDRGs * 100).toFixed(2)
        return name + '  ' + _this.$somms.ifNullZero(returnData) + '%'
      }

      let dom = document.getElementById('pieDRGs')
      let myChart = echarts.init(dom)
      if (option && typeof option === 'object') {
        myChart.setOption(option, true)
      }
      myChart.on('click', (options) => {
        this.dialogVisible = true
        this.queryDrg = ''
        this.listQuery.riskLevel = [options.name + '风险']
        this.listQuery.lyfs = []
        this.getList()
        // 取消显示最高风险病组
        this.myChart_hash = echarts.getInstanceByDom(document.getElementById('hashChartContent'))
        if (this.myChart_hash) {
          this.myChart_hash.clear()
        } else {
          this.myChart_hash = echarts.init(document.getElementById('hashChartContent'))
        }
        this.myChart_hash.dispatchAction({
          type: 'showTip',
          seriesIndex: 0, // 显示第几个series
          dataIndex: -1 // 显示第几个数据
        })
      })
    },
    // 创建各风险等级出院人数饼图
    createRiskPersonsPieChart (data) {
      let option = this.publicOption
      let name = '各风险等级出院人数占比'
      let allPersons = this.allPersons
      option.profttl[0].text = name
      option.profttl[1].text = allPersons + '人'
      option.series[0].name = name
      option.series[0].data = data
      let _this = this
      let riskLevelData = []
      for (let i = 0; i < data.length; i++) {
        riskLevelData.push(data[i].name)
      }
      option.legend.data = riskLevelData
      option.legend.formatter = function (name) {
        let index = 0
        let clientlabels = riskLevelData
        let clientcounts = data
        clientlabels.forEach(function (value, i) {
          if (value == name) {
            index = i
          }
        })
        let returnData = (clientcounts[index].value / allPersons * 100).toFixed(2)
        return name + '  ' + _this.$somms.ifNullZero(returnData) + '%'
      }
      let myChart = echarts.getInstanceByDom(document.getElementById('piePersons'))
      if (myChart) {
        myChart.clear()
      } else {
        myChart = echarts.init(document.getElementById('piePersons'))
      }
      if (option && typeof option === 'object') {
        myChart.setOption(option, true)
      }
      myChart.on('click', (options) => {
        this.dialogVisible = true
        this.queryDrg = ''
        this.listQuery.riskLevel = [options.name + '风险']
        this.listQuery.lyfs = []
        this.getList()
        // 取消显示最高风险病组
        this.myChart_hash = echarts.getInstanceByDom(document.getElementById('hashChartContent'))
        if (this.myChart_hash) {
          this.myChart_hash.clear()
        } else {
          this.myChart_hash = echarts.init(document.getElementById('hashChartContent'))
        }
        this.myChart_hash.dispatchAction({
          type: 'showTip',
          seriesIndex: 0, // 显示第几个series
          dataIndex: -1 // 显示第几个数据
        })
      })
    },
    // 创建各风险等级死亡人数饼图
    createRiskDeadPersonsPieChart (data) {
      let option = this.publicOption
      let name = '各风险等级死亡人数占比'
      let allDeadPersons = this.allDeadPersons
      option.profttl[0].text = name
      option.profttl[1].text = allDeadPersons + '人'
      option.series[0].data = data
      option.series[0].name = name
      let _this = this
      let riskLevelData = []
      for (let i = 0; i < data.length; i++) {
        riskLevelData.push(data[i].name)
      }
      option.legend.data = riskLevelData
      option.legend.formatter = function (name) {
        let index = 0
        let clientlabels = riskLevelData
        let clientcounts = data
        clientlabels.forEach(function (value, i) {
          if (value == name) {
            index = i
          }
        })
        let returnData = (clientcounts[index].value / allDeadPersons * 100).toFixed(2)
        return name + '  ' + _this.$somms.ifNullZero(returnData) + '%'
      }
      let myChart = echarts.getInstanceByDom(document.getElementById('pieDeadPersons'))
      if (myChart) {
        myChart.clear()
      } else {
        myChart = echarts.init(document.getElementById('pieDeadPersons'))
      }
      if (option && typeof option === 'object') {
        myChart.setOption(option, true)
      }
      myChart.on('click', (options) => {
        this.dialogVisible = true
        this.queryDrg = ''
        this.listQuery.riskLevel = [options.name + '风险']
        this.listQuery.lyfs = ['死亡']
        this.getList()
        // 取消显示最高风险病组
        this.myChart_hash = echarts.getInstanceByDom(document.getElementById('hashChartContent'))
        if (this.myChart_hash) {
          this.myChart_hash.clear()
        } else {
          this.myChart_hash = echarts.init(document.getElementById('hashChartContent'))
        }
        this.myChart_hash.dispatchAction({
          type: 'showTip',
          seriesIndex: 0, // 显示第几个series
          dataIndex: -1 // 显示第几个数据
        })
      })
    },
    // 创建各风险散列图
    createRiskHashChart (data, riskLineArgs) {
      if (this.myChart_hash != null && this.myChart_hash != '' && this.myChart_hash != undefined) {
        this.myChart_hash.dispose()
      }
      let maxValueIndex = 0
      let temp = []
      for (let i = 0; i < data.length; i++) {
        temp.push(data[i][1])
      }
      temp.sort(function (o1, o2) {
        return Number(o2) - Number(o1)
      })
      for (let i = 0; i < data.length; i++) {
        if (data[i][1] == temp[0]) {
          maxValueIndex = i
        }
      }
      let option = null
      option = {
        title: {
          text: 'DRGs风险等级分布图'
        },
        backgroundColor: '#fff',
        symbolSize: 10,
        grid: {
          left: '1%',
          right: '10%',
          containLabel: true
        },
        tooltip: {
          showDelay: 0,
          formatter: function (params) {
            if (params.value.length > 1) {
              return params.value[2] + ':' + params.value[3] + '<br/>' +
                  '权重' + ':' + Math.exp(params.value[0]).toFixed(2) + '<br/>' +
                  '死亡率' + ':' + (Math.exp(params.value[1]) * 100).toFixed(2) + '%'
            } else {
              return params.name + ' :<br/>' +
                  params.value
            }
          },
          axisPointer: {
            show: true,
            type: 'cross',
            lineStyle: {
              type: 'dashed',
              width: 1
            }
          }
        },
        toolbox: {
          feature: {
            dataZoom: {},
            brush: {
              type: ['rect', 'polygon', 'clear']
            }
          }
        },
        brush: {},
        xAxis: [
          {
            name: 'LN(权重)',
            type: 'value',
            scale: true,
            axisLabel: {
              formatter: '{value}'
            },
            nameLocation: 'end',
            splitLine: {
              show: true
            }
          }
        ],
        yAxis: [
          {
            name: 'LN(死亡率)',
            type: 'value',
            scale: true,
            axisLabel: {
              formatter: '{value}'
            },
            splitLine: {
              show: false
            },
            nameLocation: 'middle',
            nameRotate: -90,
            axisLine: {
              show: true,
              onZero: false
            }
          }
        ],
        series: [
          {
            name: '散列',
            type: 'scatter',
            data: data,
            markPoint: {
              data: [
                { type: 'max', name: '最大值' },
                { type: 'min', name: '最小值' }
              ],
              label: {
                formatter: function (param) {
                  return param.value.toFixed(2)
                }
              }
            },
            markLine: {
              lineStyle: {
                normal: {
                  type: 'solid'
                }
              },
              label: {
                show: true
              },
              data: [
                { yAxis: Number(riskLineArgs[0] ? riskLineArgs[0].risk_hig_line : ''), name: '正一倍标准差' },
                { yAxis: Number(riskLineArgs[0] ? riskLineArgs[0].risk_mid_line : ''), name: '均值' },
                { yAxis: Number(riskLineArgs[0] ? riskLineArgs[0].risk_low_line : ''), name: '负一倍标准差' }
              ]
            },
            markArea: {
              silent: true,
              itemStyle: {
                normal: {
                  color: 'transparent'
                }
              },
              label: {
                show: true,
                position: 'insideRight',
                fontStyle: 'italic',
                fontFamily: 'Microsoft YaHei',
                color: '#333',
                fontSize: 14
              },
              data: [
                [{
                  name: '高风险',
                  coord: [Number.MIN_VALUE, Number.MIN_VALUE]
                }, {
                  coord: [Number.MAX_VALUE, Number(riskLineArgs[0] ? riskLineArgs[0].risk_hig_line : '')]
                }],
                [{
                  name: '中高风险',
                  coord: [Number.MIN_VALUE, Number(riskLineArgs[0] ? riskLineArgs[0].risk_hig_line : '')]
                }, {
                  coord: [Number.MAX_VALUE, Number(riskLineArgs[0] ? riskLineArgs[0].risk_mid_line : '')]
                }],
                [{
                  name: '中低风险',
                  coord: [Number.MIN_VALUE, Number(riskLineArgs[0] ? riskLineArgs[0].risk_mid_line : '')]
                }, {
                  coord: [Number.MAX_VALUE, Number(riskLineArgs[0] ? riskLineArgs[0].risk_low_line : '')]
                }],
                [{
                  name: '低风险',
                  coord: [Number.MIN_VALUE, Number(riskLineArgs[0] ? riskLineArgs[0].risk_low_line : '')]
                }, {
                  coord: [Number.MAX_VALUE, -10]
                }]
              ]
            }

          }
        ],
        color: '#6699FF'
      }
      this.myChart_hash = echarts.getInstanceByDom(document.getElementById('hashChartContent'))
      if (this.myChart_hash) {
        this.myChart_hash.clear()
      } else {
        this.myChart_hash = echarts.init(document.getElementById('hashChartContent'))
      }
      if (option && typeof option === 'object') {
        this.myChart_hash.setOption(option, true)
        this.myChart_hash.dispatchAction({
          type: 'showTip',
          seriesIndex: 0, // 显示第几个series
          dataIndex: maxValueIndex // 显示第几个数据
        })
      }
      this.myChart_hash.on('click', (options) => {
        this.dialogVisible = true
        this.listQuery.riskLevel = []
        this.listQuery.lyfs = []
        this.queryDrg = options.data[2]
        this.getList()
      })
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getCount()
      this.handleSearchList()
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSelect (item) {
      this.queryDrg = item.drgsCode
    },
    openMedicalDetail () {
      // 取消显示最高风险病组
      this.myChart_hash = echarts.getInstanceByDom(document.getElementById('hashChartContent'))
      if (this.myChart_hash) {
        this.myChart_hash.clear()
      } else {
        this.myChart_hash = echarts.init(document.getElementById('hashChartContent'))
      }
      this.myChart_hash.dispatchAction({
        type: 'showTip',
        seriesIndex: 0, // 显示第几个series
        dataIndex: -1 // 显示第几个数据
      })
      this.dialogVisible = true
      if (this.dialogVisible && this.listQuery.seStartTime) {
        this.listQuery.cysj = [this.listQuery.seStartTime, this.listQuery.seEndTime]
        this.timeName = '结算时间'
      }
      if (this.dialogVisible && this.listQuery.begnDate) {
        this.listQuery.cysj = [this.listQuery.begnDate, this.listQuery.expiDate]
        this.timeName = '出院时间'
      }
      this.getList()
    },
    // 病案详情统计结果
    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      this.submitListQuery.b16c = this.listQuery.b16c
      this.submitListQuery.queryDrg = this.queryDrg
      this.submitListQuery.riskLevel = this.convertRiskLevel(this.listQuery.riskLevel).join(',')
      this.submitListQuery.lyfs = this.convertLyfs(this.listQuery.lyfs).join(',')
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      fetchList(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    convertRiskLevel (riskLevel) {
      let res = []
      for (let i = 0; i < riskLevel.length; i++) {
        if (riskLevel[i] == '高风险') {
          res.push(4)
        } else if (riskLevel[i] == '中高风险') {
          res.push(3)
        } else if (riskLevel[i] == '中低风险') {
          res.push(2)
        } else if (riskLevel[i] == '低风险') {
          res.push(1)
        } else if (riskLevel[i] == '无风险') {
          res.push(0)
        }
      }
      return res
    },
    convertLyfs (lyfs) {
      let res = []
      for (let i = 0; i < lyfs.length; i++) {
        if (lyfs[i] == '医嘱离院') {
          res.push(1)
        } else if (lyfs[i] == '医嘱转院') {
          res.push(2)
        } else if (lyfs[i] == '医嘱转社区卫生服务机构/乡镇卫生院') {
          res.push(3)
        } else if (lyfs[i] == '非医嘱离院') {
          res.push(4)
        } else if (lyfs[i] == '死亡') {
          res.push(5)
        } else if (lyfs[i] == '其他') {
          res.push(9)
        }
      }
      return res
    },
    handleSearchList () {
      this.getCount()
    },
    handleSearchListForDialog () {
      this.getList()
    },
    exportExcel () {
      let tableId = 'saqTable'
      let fileName = 'DRGs安全质量病案信息'
      elExportExcel(tableId, fileName)
    },
    handleResetSearch () {
      this.reload()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    closeDialog () {
      this.getCount()
    },
    fnReset () {
      this.listQuery.riskLevel = []
      this.listQuery.lyfs = []
      this.listQuery.b16c = ''
      this.queryDrg = ''
      this.getList()
    }
  }
}
</script>
<style scoped>
  /deep/ .el-dialog__header{
    padding:10px 20px 10px 20px;
  }
  /deep/ .el-dialog__body{
    height: 550px;
    padding:5px 20px;
  }
  /deep/ .el-dialog__footer{
    padding:10px 20px;
  }
  .el-icon-caret-bottom{
    color:#FF0000;
  }
  .el-icon-caret-top{
    color:#00CC00;
  }
  /deep/ .el-checkbox{
    font-size:11px;
    margin-right:20px;
  }
  /deep/ .el-checkbox.el-checkbox__label{
    font-size:13px;
  }
  /deep/ .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
    margin-bottom:5px;
  }
</style>
