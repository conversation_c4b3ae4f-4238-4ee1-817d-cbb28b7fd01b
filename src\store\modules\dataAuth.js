
const dataAuth = {
  state: {
    roleIds: [],
    deptCode: '',
    deptName: ''
  },
  mutations: {
    set_roles (state, roles) {
      state.roleIds = roles
    },
    del_roles (state) {
      state.roleIds = []
    },
    setDeptCode (state, deptCode) {
      state.deptCode = deptCode
    },
    setDeptName (state, deptName) {
      state.deptName = deptName
    }
  },
  getters: {
    get_roleIds (state) {
      return state.roleIds
    },
    getDeptCode (state) {
      return state.deptCode
    },
    getDeptName (state) {
      return state.deptName
    }
  }
}

export default dataAuth
