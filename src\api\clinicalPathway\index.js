import request from '@/utils/request'

/**
 * 查询DIP路径
 * @param params
 * @returns {*}
 */
export function queryDipPathway (params) {
  return request({
    url: '/dipPathsController/queryPathway',
    method: 'post',
    data: params
  })
}

/**
 * 查到DIP费用使用项目
 * @param params
 * @returns {*}
 */
export function queryItems (params) {
  return request({
    url: '/dipPathsController/queryItems',
    method: 'post',
    data: params
  })
}

/**
 * 生成路径
 * @param params
 * @returns {*}
 */
export function generaDipPaths (params) {
  return request({
    url: '/dipPathsController/generaPaths',
    method: 'post',
    data: params
  })
}
