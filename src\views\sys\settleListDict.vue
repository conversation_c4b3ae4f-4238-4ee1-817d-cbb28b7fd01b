<template>
  <div class="page-container">
    <!-- 工具栏 -->
    <div class="toolbar" style="float: left;padding-top: 10px;padding-left: 15px">
      <el-form :inline="true" :model="queryForm" :size="size">
        <el-form-item>
          <el-input v-model="queryForm.label" placeholder="名称/类型/描述" />
        </el-form-item>
        <el-form-item>
          <kt-button icon="fa fa-search" :label="$t('action.search')" auth="sys:dict:view" type="primary" @click="query(null)" />
        </el-form-item>
        <el-form-item>
          <kt-button icon="fa fa-search" :label="$t('action.add')" auth="sys:dict:add" type="primary" @click="add" />
        </el-form-item>
        <el-form-item>
          <kt-button icon="fa fa-search" :label="$t('action.refresh')" auth="sys:dict:add" type="primary" @click="refresh" />
        </el-form-item>
      </el-form>
    </div>
    <!--表格内容栏-->
    <kt-table ref="ktTable" :maxHeight="tableHeight-60"
              :height="tableHeight" :showAddButton="true"
              permsEdit="sys:dict:edit"
              permsDelete="sys:dict:delete"
              :data="result" :columns="columns"
              @findPage="query" @handleEdit="edit" @handleDelete="handleDelete" @handleAdd="handleAdd"/>
    <el-dialog :title="oprt?'新增':'编辑'" width="40%" :visible.sync="dialogVisible" :close-on-click-modal="false" v-som-dialog-drag>
      <el-form :model="form" label-width="80px" :rules="formRules" ref="form" :size="size">
        <el-form-item label="ID" prop="id"  v-if="false">
          <el-input v-model="form.id" :disabled="true" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item label="名称" prop="lablName">
          <el-input v-model="form.lablName" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item label="值" prop="dataVal">
          <el-input v-model="form.dataVal" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="codeType">
          <el-input v-model="form.codeType" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="srt">
          <el-input v-model="form.srt" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item label="描述 " prop="dscr">
          <el-input v-model="form.dscr" auto-complete="off" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="memo_info">
          <el-input v-model="form.memo_info" auto-complete="off" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :size="size" @click.native="dialogVisible = false">{{$t('action.cancel')}}</el-button>
        <el-button :size="size" type="primary" @click.native="submit" :loading="editLoading">{{$t('action.submit')}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import KtButton from '@/views/core/KtButton'
import KtTable from '@/views/core/KtTable'
import { format } from '@/utils/datetime'
import { queryPage, save, batchDelete, refreshCache } from '@/api/settleListDict'
import { Message } from 'element-ui'

export default {
  name: 'settleListDict',
  components: {
    KtTable,
    KtButton
  },
  data () {
    return {
      size: 'small',
      queryForm: {
        label: ''
      },
      columns: [
        { prop: 'id', label: 'ID', minWidth: 50 },
        { prop: 'lablName', label: '名称', minWidth: 100 },
        { prop: 'dataVal', label: '值', minWidth: 100 },
        { prop: 'codeType', label: '类型', minWidth: 80 },
        { prop: 'srt', label: '排序', minWidth: 80 },
        { prop: 'dscr', label: '描述', minWidth: 120 },
        { prop: 'memoInfo', label: '备注', minWidth: 120 },
        { prop: 'crter', label: '创建人', minWidth: 100 },
        { prop: 'crteTime', label: '创建时间', minWidth: 120, formatter: this.dateFormat }
      ],
      page: { pageNum: 1, pageSize: 10 },
      result: {},

      oprt: false, // true:新增, false:编辑
      dialogVisible: false,
      editLoading: false,
      formRules: {
        label: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
      },
      form: {
        id: 0,
        lablName: '',
        dataVal: '',
        codeType: '',
        srt: 0,
        dscr: '',
        memo_info: ''
      },
      tableHeight: 0
    }
  },
  methods: {
    query: function (data) {
      if (data !== null) {
        this.page = data.pageRequest
      }
      this.page.columnFilters = { label: { name: 'label', value: this.queryForm.label } }
      queryPage(this.page).then((res) => {
        this.result = res.data
      }).then(data != null ? data.callback : '')
    },
    handleDelete: function (data) {
      batchDelete(data.params).then(data != null ? data.callback : '')
    },
    handleAdd (data) {
      this.dialogVisible = true
      this.oprt = true
      Object.assign(this.form, data)
      this.form.id = 0
    },
    refresh (data) {
      refreshCache({}).then(data => {
        if (data.code === 200) {
          Message({
            type: 'success',
            message: '刷新缓存成功'
          })
        }
      })
    },
    // 显示新增界面
    add: function () {
      this.dialogVisible = true
      this.oprt = true
      this.form = {
        id: 0,
        lablName: '',
        dataVal: '',
        codeType: '',
        srt: 0,
        dscr: '',
        memo_info: ''
      }
    },
    // 显示编辑界面
    edit: function (params) {
      this.dialogVisible = true
      this.oprt = false
      this.form = Object.assign({}, params.row)
    },
    // 提交
    submit: function () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm('确认提交吗？', '提示', {}).then(() => {
            this.editLoading = true
            let params = Object.assign({}, this.form)
            save(params).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '操作成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败，' + res.msg, type: 'error' })
              }
              this.editLoading = false
              this.$refs['form'].resetFields()
              this.dialogVisible = false
              this.query(null)
            })
          })
        }
      })
    },
    // 时间格式化
    dateFormat: function (row, column, cellValue, index) {
      return format(row[column.property])
    }
  },
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.ktTable.$el.offsetTop：表格距离浏览器的高度
      // 50表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.ktTable.$el.offsetTop - 50
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.ktTable.$el.offsetTop - 50
      }
    })
  }
}
</script>

<style scoped>

</style>
