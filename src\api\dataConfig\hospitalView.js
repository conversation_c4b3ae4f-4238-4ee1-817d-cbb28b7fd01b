import request from '@/utils/request'

export function queryHospitalInfo (params) {
  return request({
    url: '/viewHospitalController/queryHospitalInfo',
    method: 'post',
    params: params
  })
}
export function updateHospitalInfo (params) {
  return request({
    url: '/viewHospitalController/updateHospitalInfo',
    method: 'post',
    params: params
  })
}
export function deleteHospitalInfo (params) {
  return request({
    url: '/viewHospitalController/deleteHospitalInfo',
    method: 'post',
    params: params
  })
}
export function insertHospitalInfo (params) {
  return request({
    url: '/viewHospitalController/insertHospitalInfo',
    method: 'post',
    params: params
  })
}
