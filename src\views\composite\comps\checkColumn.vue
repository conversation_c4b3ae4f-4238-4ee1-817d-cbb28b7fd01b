<template>
  <el-drawer
    title="选择指标/维度"
    :visible.sync="drawer"
    :direction="direction"
    size="70%"
    :before-close="handleClose">
    <div style="width: 100%;height: 40%">
      <div class="index-left">
        <span>选择指标</span>
        <el-card class="box-card card-height">
          <div v-for="(item,index) in indexKeys" :key="index">
            <el-row :gutter="20">
              <el-col :span="4">
                <div style="margin-top: 10px"> {{ item }}</div>
              </el-col>
              <el-col :span="16">
                <div class="tab-li"
                     :class="{ active: getCheck(i)}"
                     v-for="(i,index2) in getValue(item,1)" :key="index2"
                     @click="cutTabClick(i,1)">
                  {{ i.fieldName }}
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
      <div class="dimensionality-right">
        <span>选择维度</span>
        <el-card class="box-card card-height">
          <div v-for="(item,index) in dimensionalityKeys" :key="index">
            <el-row :gutter="20">
              <el-col :span="4">
                <div style="margin-top: 10px"> {{ item }}</div>
              </el-col>
              <el-col :span="16">
                <div class="tab-li"
                     :class="{ active: getCheck(i)}"
                     v-for="(i,index2) in getValue(item,2)" :key="index2"
                     @click="cutTabClick(i,2)">
                  {{ i.fieldName }}
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
    </div>
    <div style="width: 100%;height: 30%">
      <div class="index-left">
        <el-card class="box-card card-space">
          <span class="choose-span">已选维度（列）</span>
          <div class="choose-list" v-for="(item,index) in chooseColumnList" :key="index">
            {{ item.fieldName }}
          </div>
        </el-card>
      </div>
      <div class="dimensionality-right">
        <el-card class="box-card card-space">
          <span class="choose-span">已选维度（行）</span>
          <div class="choose-list" v-for="(item,index) in chooseColumnListDim" :key="index">
            {{ item.fieldName }}
          </div>
        </el-card>
      </div>
    </div>
    <div style="height: 20%"></div>
    <div class="my-footer">
      <div class="footer-button">
        <el-button type="primary" @click="generateColumn">生成列表</el-button>
      </div>
    </div>
  </el-drawer>

</template>

<script>
import action from '@/components/Toolbar/Action.vue'
import { querydimensionality, queryIndex } from '@/api/integratedQuery/integratedQuery'
import Dict from '@/views/sys/dict.vue'

export default {
  computed: {
    action () {
      return action
    }
  },
  props: {
    checkColumnDrawer: {
      type: Boolean,
      default: false
    }
  },
  name: 'checkColumn',
  data () {
    return {
      drawer: false,
      direction: 'rtl',
      checkColumnData: {},
      indexKeys: [],
      chooseColumnList: [],
      chooseColumnListDim: [],
      dimensionalityKeys: [],
      checkColumnDataDim: {},
      sqlType: '1',
      sqlType2Arr: ['casesAbnormalNum', 'casesAbnormalRate', 'fullAbnormalNum', 'fullAbnormalRate', 'logicAbnormalNum', 'logicAbnormalRate']
    }
  },
  mounted () {
    // 指标
    queryIndex({}).then(res => {
      if (res.code === 200) {
        this.checkColumnData = res.data
        Object.keys(this.checkColumnData).forEach(key => {
          this.indexKeys.push(key)
        })
      }
    })
    // 维度
    querydimensionality({}).then(res => {
      if (res.code === 200) {
        this.checkColumnDataDim = res.data
        Object.keys(this.checkColumnDataDim).forEach(key => {
          this.dimensionalityKeys.push(key)
        })
      }
    })
  },
  methods: {
    getParams () {
      let index = {}
      let dimensionality = {}
      this.chooseColumnList.forEach(item => {
        index[item.pageDispFld] = 1
        this.sqlType2Arr.forEach(value => {
          if (item.pageDispFld === value) {
            this.sqlType = '2'
          }
        })
      })
      if (this.chooseColumnListDim.length > 0) {
        dimensionality.isChoose = 1
        this.chooseColumnListDim.forEach(item => {
          dimensionality[item.pageDispFld] = 1
        })
      }

      return {
        indexColumn: index,
        dimensionalityColumn: dimensionality,
        sqlType: this.sqlType
      }
    },
    generateColumn () {
      // 1.掉后端的方法
      let params = this.getParams()
      let arr = this.chooseColumnList.concat(this.chooseColumnListDim)
      this.$emit('generateColumn', arr, params)
      this.closed()
    },
    closed () {
      this.drawer = false
      this.$emit('close', false)
    },
    getCheck (item) {
      return item.check
    },
    getValue (key, type) {
      if (type === 1) {
        // 指标
        return this.checkColumnData[key]
      } else {
        // 维度
        return this.checkColumnDataDim[key]
      }
    },
    cutTabClick (item, type) {
      // 指标
      if (type === 1) {
        if (item.check) {
          let index = -1
          for (let i = 0; i < this.chooseColumnList.length; i++) {
            if (item.pageDispFld === this.chooseColumnList.pageDispFld) {
              index = i
              return
            }
          }
          this.chooseColumnList.splice(index, 1)
        } else {
          this.chooseColumnList.push(item)
        }
      } else {
        // 维度
        if (item.check) {
          let index = -1
          for (let i = 0; i < this.chooseColumnListDim.length; i++) {
            if (item.pageDispFld === this.chooseColumnListDim.pageDispFld) {
              index = i
              return
            }
          }
          this.chooseColumnListDim.splice(index, 1)
        } else {
          this.chooseColumnListDim.push(item)
        }
      }
      item.check = !item.check
    },
    handleClose (done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
          this.$emit('close', false)
        })
        .catch(_ => {
        })
    }
  },
  watch: {
    checkColumnDrawer (val) {
      this.drawer = val
    }
  }
}
</script>
<style scoped>
.tab-li {
  dispaly: block;
  float: left;
  list-style: none;
  margin: 10px 0px 0px 5px;
}

.active {
  color: red !important;
}

.choose-list {
  margin-right: 10px;
  display: inline;
}

.card-space {
  margin-top: 50px;
  padding-bottom: 10px;
  overflow:auto;
}

.choose-span {
  margin-right: 30px;
}

.card-height {
  height: 100%;
  overflow:auto;
}

.index-left {
  width: 45%;
  float: left;
  margin-left: 20px;
  height: 100%;
}

.dimensionality-right {
  width: 45%;
  float: right;
  margin-right: 20px;
  height: 100%;
}

.my-footer {
  height: 10%;
}

.footer-button {
  margin: 20px 50px 0px 20px;
  float: right;
}
</style>
