// 公共js
/**
 * 将数字转换为金额显示，每三位逗号隔开
 * @method moneyFormat
 * @param {string} money 数字
 * @param {Number} decimal 小数位
 * @param {string} symbol 金额前缀，如￥或$
 */
export function moneyFormat (money, decimal, symbol) {
  if (!money) return ''
  if (money && money.search('万') != -1) {
    money = money.replace('万', '')
    let num = Number(money)
    num = String(num.toFixed(decimal || 0))
    let re = /(-?\d+)(\d{3})/
    while (re.test(num)) {
      num = num.replace(re, '$1,$2')
    }
    return symbol ? symbol + num + '万' : num + '万'
  } else if (money && money.search('千万') != -1) {
    money = money.replace('千万', '')
    let num = Number(money)
    num = String(num.toFixed(decimal || 0))
    let re = /(-?\d+)(\d{3})/
    while (re.test(num)) {
      num = num.replace(re, '$1,$2')
    }
    return symbol ? symbol + num + '千万' : num + '千万'
  } else if (money && money.search('亿') != -1) {
    money = money.replace('亿', '')
    let num = Number(money)
    num = String(num.toFixed(decimal || 0))
    let re = /(-?\d+)(\d{3})/
    while (re.test(num)) {
      num = num.replace(re, '$1,$2')
    }
    return symbol ? symbol + num + '亿' : num + '亿'
  } else {
    let num = Number(money)
    num = String(num.toFixed(decimal || 0))
    let re = /(-?\d+)(\d{3})/
    while (re.test(num)) {
      num = num.replace(re, '$1,$2')
    }
    return symbol ? symbol + num : num
  }
}

/**
 * 数字格式化，每三位逗号隔开，支持整数和浮点数
 * @param num
 * @returns {string|string}
 */

function toThousands (num) {
  num = (num || 0).toString()
  let number = 0,
    floatNum = '',
    intNum = ''
    // 判断是否有小数位，有则截取小数点后的数字
  if (num.indexOf('.') > 0) {
    number = num.indexOf('.') // 获取小数点出现的位置
    floatNum = num.substr(number) // 截取arr.substr(form, length)
    intNum = num.substring(0, number) // 截取arr.substring(start, end)
  } else {
    intNum = num
  }
  let result = [],
    counter = 0
  intNum = intNum.split('')
  // 利用3的倍数，向数组插入','
  for (let i = intNum.length - 1; i >= 0; i--) {
    counter++
    result.unshift(intNum[i])
    if (!(counter % 3) && i != 0) {
      result.unshift(',')
    }
  }
  return result.join('') + floatNum || ''
}

/**
 * 处理大数据过长问题
 */
export function handleNumber (data) {
  if (Number(data) > 10000) {
    return (Number(data) / 10000).toFixed(2) + '/万'
  } else if (Number(data) > 10000000) {
    return (Number(data) / 10000000).toFixed(2) + '/千万'
  } else if (Number(data) > 100000000) {
    return (Number(data) / 100000000).toFixed(2) + '/亿'
  } else if (Number(data) < -10000) {
    return (Number(data) / 10000).toFixed(2) + '/万'
  } else if (Number(data) > 1000) {
    return (Number(data) / 1000).toFixed(2) + '/千点'
  } else {
    return data
  }
}

export function handleNumberCost (data) {
  if (Number(data) > 10000) {
    return (Number(data) / 10000).toFixed(2) + '/万点'
  } else if (Number(data) > 10000000) {
    return (Number(data) / 10000000).toFixed(2) + '/千万点'
  } else if (Number(data) > 100000000) {
    return (Number(data) / 100000000).toFixed(2) + '/亿点'
  } else if (Number(data) < -10000) {
    return (Number(data) / 10000).toFixed(2) + '/万点'
  } else if (Number(data) > 1000) {
    return (Number(data) / 1000).toFixed(2) + '/千点'
  } else {
    return data
  }
}

/**
 * 格式化百分数
 * @param data 数据
 * @param isFormat 是否需要进行小数的格式化 默认值为 false
 * @param removePrefix 是否需要移除前缀 example: -12 => 12
 * @returns {string}
 */
export function formatRate (data, isFormat = false, removePrefix = true) {
  if (data && !isNaN(data) && typeof (data) != 'undefined') {
    if (String(data).indexOf('.') == 1 && isFormat) {
      return (parseFloat(data) * 100).toFixed(2) + '%'
    }
    if (String(data).indexOf('-') != -1 && removePrefix) {
      let index = String(data).indexOf('-')
      let val = String(data).substr(index + 1, String(data).length)
      return parseFloat(val).toFixed(2) + '%'
    }
    return parseFloat(data).toFixed(2) + '%'
  }
  return '0%'
}

/**
 * 格式化金钱
 * example：
 *  input: 12345678 mode = 'w' isFormat = true
 *  output: 123.46 /万
 *
 *  input: 12345678 mode = 'w' isFormat = false
 *  output: 12345678 /元
 * @param data 金钱
 * @param isFormat 是否格式化
 * @param pattern 模式，默认万元 w: 万元
 */
export function formatCost (data, isFormat, pattern = 'w') {
  let prefix = ''
  let resVal = '0'
  if (String(data).includes('-')) {
    prefix = '-'
    data = Math.abs(data)
  }
  if (data) {
    if (isFormat) {
      if (Math.abs((parseFloat(data) / 100000000)) > 1) {
        resVal = (parseFloat(data) / 100000000).toFixed(2) + '<span style="font-size: 8px">/亿</span>'
      } else if (Math.abs((parseFloat(data) / 10000)) > 1) {
        resVal = (parseFloat(data) / 10000).toFixed(2) + '<span style="font-size: 8px">/万</span>'
      }
    }
    if (resVal == '0') {
      resVal = parseFloat(data).toFixed(2) + '<span style="font-size: 8px">/元</span>'
    }
  }
  return prefix + resVal
}

/**
 * 格式化点数
 */
export function formatCostNum (data, isFormat, pattern = 'w') {
  let prefix = ''
  let resVal = '0'
  if (String(data).includes('-')) {
    prefix = '-'
    data = Math.abs(data)
  }
  if (data) {
    if (isFormat) {
      if (Math.abs((parseFloat(data) / 100000000)) > 1) {
        resVal = (parseFloat(data) / 100000000).toFixed(2) + '<span style="font-size: 8px">/亿点</span>'
      } else if (Math.abs((parseFloat(data) / 10000)) > 1) {
        resVal = (parseFloat(data) / 10000).toFixed(2) + '<span style="font-size: 8px">/万点</span>'
      }
    }
    if (resVal == '0') {
      resVal = parseFloat(data).toFixed(2) + '<span style="font-size: 8px">/点</span>'
    }
  }
  return prefix + resVal
}
/**
 * 格式化金钱
 * @param data  数据
 * @param isAddPrefix 是否添加前缀
 * @param prefix 前缀
 */
export function formatMoney (data, isAddPrefix, prefix = '￥') {
  prefix = '<span style="color: gray">' + prefix + '</span>'
  if (data) {
    data = String(data)
    let splitData = data.split('.')
    data = splitData[0].split('').reverse().reduce((prev, next, index) => {
      return ((index % 3) ? next : index == splitData[0].length - 1 ? next : (next + ',')) + prev
    })
    if (splitData.length > 1) {
      data = String(data) + '.' + splitData[1]
    }
    if (isAddPrefix) {
      return prefix + data
    }
    return data
  }
  if (isAddPrefix) {
    return prefix + 0
  }
  return 0
}
/**
 * 加载
 * @returns {ElLoadingComponent | undefined | *}
 */
export function openLoading () {
  const loading = this.$loading({ // 声明一个loading对象
    lock: true, // 是否锁屏
    text: '拼命读取中', // 加载动画的文字
    spinner: 'el-icon-loading', // 引入的loading图标
    target: document.querySelector('.app-container'),
    background: 'rgba(0, 0, 0, 0.7)' // 背景颜色
  })
  // setTimeout(function() { // 设定定时器，超时2S后自动关闭遮罩层，避免请求失败时，遮罩层一直存在的问题
  //   loading.close() // 关闭遮罩层
  // }, 2000)
  return loading
}

export function sortChange (column) {
  let _this = this
  let Invalid = []
  let valid = []
  let fieldName = column.prop
  let fieldOrder = column.order
  if (_this.tableData) {
    Invalid = Object.assign([], _this.tableData.filter(el => el[fieldName] === '-'))
    valid = Object.assign([], _this.tableData.filter(el => el[fieldName] !== '-'))
  }
  if (_this.list) {
    Invalid = Object.assign([], _this.list.filter(el => el[fieldName] === '-'))
    valid = Object.assign([], _this.list.filter(el => el[fieldName] !== '-'))
  }
  if (fieldOrder !== null) {
    if (fieldOrder === 'descending') {
      valid = valid.sort((a, b) => b[fieldName] - a[fieldName])
      Invalid.forEach(el => {
        valid.push(el)
      })
      if (_this.tableData) {
        _this.tableData = valid
      }
      if (_this.list) {
        _this.list = valid
      }
    }
    if (fieldOrder === 'ascending') {
      valid = valid.sort((a, b) => a[fieldName] - b[fieldName])
      valid.reverse()
      Invalid.forEach(el => {
        valid.push(el)
      })
      valid.reverse()
      if (_this.tableData) {
        _this.tableData = valid
      }
      if (_this.list) {
        _this.list = valid
      }
    }
  }
}
