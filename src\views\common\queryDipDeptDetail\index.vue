<template>
  <div class="app-container">
    <el-card class="filter-container"  style="height:45px;overflow-y:auto;">
      <div style="margin-top: -14px">
        <el-form :inline="true" :model="listQuery" size="mini">
          <el-row :gutter="0">
            <el-col :span="6" align="center">
              <el-form-item label="时间范围">
                <el-date-picker :disabled="true"
                                v-model="listQuery.cysj"
                                type="daterange"
                                size="mini"
                                unlink-panels
                                range-separator="-"
                                value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6" align="center">
              <el-form-item label="DIP组名称">
                <el-input v-model="listQuery.queryDipGroupCode" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" align="center">
              <el-button class="expBtn" @click="exportExcel()" size="mini">导出Excel</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
    <div class="table-container" style="margin-top:10px;flex:5px; overflow-y:auto;">
      <el-table ref="deptDetail"
                id="deptTable"
                :key=Math.random()
                size="mini"
                stripe
                :height="tableHeight"
                :data="list"
                style="width: 100%;"
                v-loading="listLoading"
                border>
        <el-table-column fixed
                         label="序号"
                         type="index"
                         width="50">
        </el-table-column>
        <el-table-column label="出院科室编码" v-if="false">
          <template slot-scope="scope">{{scope.row.priOutHosDeptCode | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column fixed label="出院科室名称"  align="left" width="100" :show-overflow-tooltip="true">
          <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column fixed label="DIP组编码"  align="center" width="120" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <div class='skip' @click="queryDipGroupMedicalNum(scope.row)">
              {{scope.row.dipCodg | formatIsEmpty}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="DIP组名称"  align="center"  :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <div class='skip' @click="queryDipGroupMedicalNum(scope.row)">
              {{scope.row.dipName | formatIsEmpty}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="入组病案数"  align="center" width="90">
          <template slot-scope="scope">
            <div v-if="Number(scope.row.medicalTotalNum)>0" class='skip' @click="queryDipGroupMedicalNum(scope.row)">
              {{scope.row.medicalTotalNum | formatIsEmpty}}
            </div>
            <div v-if="Number(scope.row.medicalTotalNum)==0" style="color:#000000">
              {{scope.row.medicalTotalNum | formatIsEmpty}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="入组病案数占比"  align="center" width="80" >
          <template slot-scope="scope">{{scope.row.medicalTotalNumRate | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column label="总住院费用"  align="center" width="100" >
          <template slot-scope="scope">{{scope.row.sumfee | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column label="总住院费用占比"  align="center" width="120" >
          <template slot-scope="scope">{{scope.row.totalCostRate | formatIsEmpty}}</template>
        </el-table-column>
        <!--<el-table-column label="结算清单总费用"  align="center" width="120">-->
          <!--<template slot-scope="scope">{{scope.row.totalBaseCost | formatIsEmpty}}</template>-->
        <!--</el-table-column>-->
        <!--<el-table-column label="结算清单总费用占比"  align="center" width="90" >-->
          <!--<template slot-scope="scope">{{scope.row.totalBaseCostRate | formatIsEmpty}}</template>-->
        <!--</el-table-column>-->
        <el-table-column label="平均住院费用"  align="center" width="120">
          <template slot-scope="scope">{{scope.row.avgCost | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column label="总住院天数"   align="center" width="100" >
          <template slot-scope="scope">{{scope.row.totalDays | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column label="总住院天数占比"  align="center" width="120" >
          <template slot-scope="scope">{{scope.row.totalDaysRate | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column label="平均住院日"  align="center" width="100">
          <template slot-scope="scope">{{scope.row.avgDays | formatIsEmpty}}</template>
        </el-table-column>

      </el-table>
    </div>
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        layout="total, sizes,prev, pager, next,jumper"
        :page-size="listQuery.pageSize"
        :page-sizes="[200,1000,5000,10000]"
        :current-page.sync="listQuery.pageNum"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { queryDipDeptDetailList } from '@/api/common/drgCommon'
import { elExportExcel } from '@/utils/exportExcel'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  queryDipGroupCode: null,
  cy_start_date: null,
  cy_end_date: null
}
export default {
  name: 'queryDipDeptDetail',
  data () {
    return {
      listLoading: true,
      list: null,
      total: null,
      listQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0
    }
  },
  created () {
    this.getList()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.deptDetail.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.deptDetail.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.deptDetail.$el.offsetTop - 35
      }
    })
  },
  methods: {
    getList () {
      // 回显
      if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
        Object.assign(this.listQuery, { cysj: [this.$route.query.cy_start_date, this.$route.query.cy_end_date] })
      }
      Object.assign(this.listQuery, { cy_start_date: this.$route.query.cy_start_date })
      Object.assign(this.listQuery, { cy_end_date: this.$route.query.cy_end_date })
      Object.assign(this.listQuery, { queryDipGroupCode: this.$route.query.queryDipGroupCode })
      this.listLoading = true
      queryDipDeptDetailList(this.listQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    // 下转
    queryDipGroupMedicalNum (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      this.$router.push({ path: '/common/queryMedicalDetail',
        query: {
          queryDipGroupCode: row.dipCodg,
          queryDipGroupName: row.dipName,
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: deptName,
          cy_start_date: this.cy_start_date,
          cy_end_date: this.cy_end_date
        }
      })
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    exportExcel () {
      let tableId = 'deptTable'
      let fileName = 'DIP分组科室详情信息'
      elExportExcel(tableId, fileName)
    }
  }
}
</script>
<style>
</style>
