import request from '@/utils/request'
export function fetchList (params) {
  return request({
    url: '/settleListManage/mainInfoList',
    method: 'post',
    params: params
  })
}

export function extract (params) {
  return request({
    url: '/settleListReExtract/extract',
    method: 'post',
    data: params
  })
}

/**
 * 数据同步
 * @param params
 * @returns {*}
 */
export function dataSynchronization (params) {
  return request({
    url: '/settleListManage/dataSynchronization',
    method: 'post',
    data: params
  })
}
