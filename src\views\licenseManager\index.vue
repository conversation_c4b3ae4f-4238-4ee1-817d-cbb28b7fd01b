<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
              show-date-range
              showPagination
              :totalNum="total"
              headerTitle="查询条件"
              :container="true"
              :initTimeValueNotQuery="false"
              ref="somForm"
              @query="handleSearchList">
      <template slot="extendFormItems">
        <el-form-item label="医疗机构编码">
          <el-input v-model="listQuery.medins_no" placeholder="请输入医疗机构编码"/>
        </el-form-item>

        <el-form-item label="医疗机构名称">
          <el-input v-model="listQuery.medins_name" placeholder="请输入医疗机构名称"/>
        </el-form-item>

        <el-form-item label="第三方">
          <el-input v-model="listQuery.ttp_prdr" placeholder="请输入第三方名称"/>
        </el-form-item>

        <el-form-item label="审批状态">
          <el-select v-model="listQuery.appr_stas" placeholder="请选择审批状态" clearable
                     class="som-w-one-hundred">
            <el-option
              v-for="item in dictVoList.APPR_STAS"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="有效标志">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择" v-model="listQuery.vali_flag"/>
        </el-form-item>
      </template>
      <!--新增-->
      <template slot="buttons">
        <el-button type="primary" @click="addLicense" class="som-button-margin-right"><i
          class="el-icon-plus add-icon-color el-icon--left"></i>新增
        </el-button>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  :id="tableId"
                  size="mini"
                  stripe
                  height="100%"
                  :data="list"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>
          <el-table-column label="序号" type="index" align="left"></el-table-column>
          <el-table-column label="医疗机构编号" prop="medins_no" align="left" width="120"></el-table-column>
          <el-table-column label="医疗机构名称" prop="medins_name" align="left" width="120"></el-table-column>
          <el-table-column label="cpu编号" prop="cpu_no" align="left" width="160"></el-table-column>
          <el-table-column label="开始日期" prop="begin_date" align="center" width="100" sortable></el-table-column>
          <el-table-column label="结束日期" prop="end_date" align="center" width="100" sortable></el-table-column>
          <el-table-column label="产品授权码" prop="prod_auth_code" align="left" :show-overflow-tooltip="true"
                           width="200"></el-table-column>
          <el-table-column label="第三方厂商" prop="ttp_prdr" align="left"
                           :show-overflow-tooltip="true" width="100"></el-table-column>
          <el-table-column label="申请时间" prop="oprt_time" align="center" width="150" sortable></el-table-column>
          <el-table-column label="申请人员" prop="oprt_psn_name" align="center" width="90"></el-table-column>
          <el-table-column label="审批状态" prop="appr_stas" align="center" width="90">
            <template slot-scope="scope">{{ scope.row.appr_stas | formatApprStas }}</template>
          </el-table-column>
          <el-table-column label="审批时间" prop="appr_time" align="center" width="150"></el-table-column>
          <el-table-column label="审批人员" prop="appr_psn_name" align="right" width="90"></el-table-column>
          <el-table-column label="备注" prop="remark" align="left" width="150"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="90px">
            <template slot-scope="scope">
              <el-button @click="showDetailDialog(scope.row)">查看明细</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--新增申请记录-->
        <el-dialog
          title="新增"
          width="60%"
          :visible.sync="dialogVisible"
        >
          <el-form :model="addForm" ref="addForm" size="mini">
            <el-form-item prop="medins_no" label="医疗机构编码" required>
              <el-input placeholder="医疗机构编码" v-model="addForm.medins_no"></el-input>
            </el-form-item>
            <el-form-item prop="medins_name" label="医疗机构名称" required>
              <el-input placeholder="医疗机构名称" v-model="addForm.medins_name"></el-input>
            </el-form-item>
            <el-form-item label="开始日期" prop="begin_date" required>
              <el-date-picker
                style="width:100%"
                v-model="addForm.begin_date"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束日期" prop="end_date" required>
              <el-date-picker
                style="width:100%"
                v-model="addForm.end_date"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="cpu_no" label="处理器id" required>
              <el-input placeholder="处理器id" v-model="addForm.cpu_no"></el-input>
            </el-form-item>
            <el-form-item prop="ttp_prdr" label="第三方">
              <el-input placeholder="第三方" v-model="addForm.ttp_prdr"></el-input>
            </el-form-item>

            <el-form-item prop="remark" label="备注">
              <el-input placeholder="备注" v-model="addForm.remark"></el-input>
            </el-form-item>
          </el-form>
          <template #footer>
                <span class="dialog-footer">
                  <el-button @click="addCancel" size="mini">取 消</el-button>
                  <el-button type="primary" @click="saveAddConfig" size="mini" v-model="saveEnable">保 存</el-button>
                </span>
          </template>
        </el-dialog>

        <!--申请记录详情-->
        <el-dialog
          title="详情展示"
          width="60%"
          :visible.sync="recordDialogVisible"
        >
          <el-form :model="recordForm" ref="recordForm" size="mini">
            <el-form-item prop="medins_no" label="医疗机构编码">
              <el-input placeholder="医疗机构编码" v-model="recordForm.medins_no"></el-input>
            </el-form-item>
            <el-form-item prop="medins_name" label="医疗机构名称">
              <el-input placeholder="医疗机构名称" v-model="recordForm.medins_name"></el-input>
            </el-form-item>
            <el-form-item label="开始日期" prop="begin_date">
              <el-date-picker
                style="width:100%"
                v-model="recordForm.begin_date"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束日期" prop="end_date">
              <el-date-picker
                style="width:100%"
                v-model="recordForm.end_date"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="cpu_no" label="处理器id">
              <el-input placeholder="处理器id" v-model="recordForm.cpu_no"></el-input>
            </el-form-item>
            <el-form-item prop="ttp_prdr" label="第三方">
              <el-input placeholder="第三方" v-model="recordForm.ttp_prdr"></el-input>
            </el-form-item>
            <el-form-item prop="prod_auth_code" label="授权码">
              <el-input type="textarea" placeholder="授权码" v-model="recordForm.prod_auth_code"
                        :rows="4"></el-input>
            </el-form-item>
            <el-form-item prop="remark" label="备注">
              <el-input placeholder="备注" v-model="recordForm.remark"></el-input>
            </el-form-item>
          </el-form>
          <!--          <template #footer>-->
          <!--                <span class="dialog-footer">-->
          <!--                  <el-button @click="addCancel" size="mini">取 消</el-button>-->
          <!--                  <el-button type="primary" @click="saveAddConfig" size="mini" v-model="saveEnable">保 存</el-button>-->
          <!--                </span>-->
          <!--          </template>-->
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import {queryDataIsuue} from '@/api/common/drgCommon'
import {queryData as queryPageData, addLicense} from '@/api/licenseManager/licenseManager'
import {formatDate} from '@/utils/date'
import {querySelectTreeAndSelectList} from "@/api/common/drgCommon"

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  medins_no: null,
  medins_name: null,
  cpu_no: null,
  begin_date: null,
  end_date: null,
  ttp_prdr: '',
  oprt_time: '',
  oprt_psn_codg: 0,
  appr_stas: null,
  vali_flag: '1'
}
const defaultAddForm = {
  id: '',
  medins_no: null,
  medins_name: null,
  cpu_no: null,
  begin_date: null,
  end_date: null,
  ttp_prdr: '',
  oprt_time: '',
  oprt_psn_codg: 0,
  appr_stas: '0',
  vali_flag: '1',
  remark: ''
}

const defaultRecordForm = {
  id: '',
  medins_no: null,
  medins_name: null,
  cpu_no: null,
  begin_date: null,
  end_date: null,
  ttp_prdr: '',
  prod_auth_code: '',
  oprt_time: '',
  oprt_psn_codg: 0,
  appr_stas: '0',
  vali_flag: '1',
  remark: ''
}

export default {
  name: 'licenseManager',
  components: {},
  inject: ['reload'],
  data() {
    return {
      dialogVisible: false,
      recordDialogVisible: false,
      tableId: 'dataTable',
      listLoading: true,
      loading: false,
      list: null,
      total: null,
      dictVoList: {},
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      addForm: Object.assign({}, defaultAddForm),
      recordForm: Object.assign({}, defaultRecordForm),
      tableHeight: 0,
      depts: [],
      data: {},
      saveEnable: true,
      addFormRules: {
        medins_no: [
          {required: true, message: '请输入机构编码', trigger: 'blur'}
        ],
        medins_name: [
          {required: true, message: '请输入机构名称', trigger: 'blur'}
        ]
      },
    }
  },
  created() {

  },
  watch: {
    list: function () {
      this.$nextTick(() => {
        if (this.$refs.dataTable) {
          this.$refs.dataTable.doLayout()
        }
      })
    }
  },
  filters: {
    formatIsEmpty(value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime(time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },
    formatApprStas(value) {
      if (value == '0') {
        return '待审批'
      } else if (value == '1') {
        return '通过'
      } else if (value == '2') {
        return '不通过'
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.findSelectTreeAndSelectList()
      if (Object.keys(this.$route.query).length > 0) {
        // 时间需要在最下面设置
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.listQuery)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.listQuery)
        }
      }
      this.getList()
    })
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      params.append('codeKeys', 'APPR_STAS')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue() {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        this.getList()
      })
    },
    getList() {
      this.listLoading = true
      this.submitListQuery.medins_no = this.listQuery.medins_no
      this.submitListQuery.medins_name = this.listQuery.medins_name
      this.submitListQuery.begin_date = this.listQuery.begin_date
      this.submitListQuery.end_date = this.listQuery.end_date
      this.submitListQuery.ttp_prdr = this.listQuery.ttp_prdr
      this.submitListQuery.vali_flag = this.listQuery.vali_flag
      this.submitListQuery.appr_stas = this.listQuery.appr_stas
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    dateChangeRysj(val) {
      if (val) {
        this.ry_start_date = val[0]
        this.ry_end_date = val[1]
      } else {
        this.ry_start_date = null
        this.ry_end_date = null
      }
      this.getList()
    },
    dateChangeCysj(val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList() {
      this.getList()
    },
    refresh() {
      this.reload()
    },
    getParams() {
      return this.submitListQuery
    },
    /**
     * 取消
     */
    addCancel() {
      this.$confirm('关闭后新增信息不会保存,是否确认关闭？')
        .then(_ => {
          this.dialogVisible = false
          this.resetAddForm()
        })
        .catch(_ => {
          this.dialogVisible = false
          this.resetAddForm()
        })
    },
    /**
     * 新增点击
     */
    addLicense() {
      this.dialogVisible = true
    },
    /**
     * 重置数据
     */
    resetAddForm() {
      this.addForm = {
        id: '',
        medins_no: null,
        medins_name: null,
        cpu_no: null,
        begin_date: null,
        end_date: null,
        ttp_prdr: '',
        oprt_time: '',
        oprt_psn_codg: 0,
        appr_stas: null,
        vali_flag: '1'
      }
    },
    /**
     * 新增提交
     */
    saveAddConfig() {
      // if (this.saveEnable) {}
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          this.$confirm('是否确认提交？')
            .then(_ => {
              this.saveEnable = false
              addLicense(this.addForm).then((result) => {
                this.tableData = result.data
                this.dialogVisible = false
                this.getList()
                this.$message({
                  message: '新增成功！',
                  type: 'success'
                })
                this.resetAddForm()
              })
            })
            .catch(_ => {
              this.dialogVisible = false
              this.saveEnable = true
            })
        }
      })
    },
    /**
     * 查看明细
     * @param index
     * @param row
     */
    showDetailDialog(row) {
      this.recordForm = row
      this.recordDialogVisible = true
    }
  }
}
</script>
<style scoped>
/*时间样式设置*/
/deep/ .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner {
  width: 200px;
}

.autoSelectInputWidth {
  width: 178px;
}

.init_texearea {
  height: 90px;
}

.init_texearea .el-textarea__inner {
  height: 90px
}

</style>
