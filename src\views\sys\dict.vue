<template>
  <div class="page-container">
  <!--工具栏-->
  <div class="toolbar" style="float:left;padding-top:10px;padding-left:15px;">
    <el-form :inline="true" :model="filters" :size="size">
      <el-form-item>
        <el-input v-model="filters.label" placeholder="名称/类型/描述"></el-input>
      </el-form-item>
      <el-form-item>
        <kt-button icon="fa fa-search" :label="$t('action.search')" auth="sys:dict:view" type="primary" @click="findPage(null)"/>
      </el-form-item>
      <el-form-item>
        <kt-button icon="fa fa-plus" :label="$t('action.add')" auth="sys:dict:add" type="primary" @click="handleAdd" />
      </el-form-item>
      <el-form-item>
        <kt-button icon="fa fa-plus" :label="$t('action.refresh')" auth="sys:dict:add" type="primary" @click="handlerRefresh" />
      </el-form-item>
    </el-form>
  </div>
  <!--表格内容栏-->
  <kt-table  ref="ktTable" :maxHeight="tableHeight-60" :height="tableHeight" permsEdit="sys:dict:edit" permsDelete="sys:dict:delete"
    :data="pageResult" :columns="columns" :showAddButton="true"
    @findPage="findPage" @handleEdit="handleEdit" @handleDelete="handleDelete" @handleAdd="showAdd">
  </kt-table>
  <!--新增编辑界面-->
  <el-dialog :title="oprt?'新增':'编辑'" width="40%" :visible.sync="editDialogVisible" :close-on-click-modal="false" v-som-dialog-drag>
    <el-form :model="dataForm" label-width="80px" :rules="dataFormRules" ref="dataForm" :size="size">
      <el-form-item label="ID" prop="id"  v-if="false">
        <el-input v-model="dataForm.id" :disabled="true" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="名称" prop="lablName">
        <el-input v-model="dataForm.lablName" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="值" prop="dataVal">
        <el-input v-model="dataForm.dataVal" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="类型" prop="codeType">
        <el-input v-model="dataForm.codeType" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="srt">
        <el-input v-model="dataForm.srt" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="描述 " prop="dscr">
        <el-input v-model="dataForm.dscr" auto-complete="off" type="textarea"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="memo_info">
        <el-input v-model="dataForm.memo_info" auto-complete="off" type="textarea"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button :size="size" @click.native="editDialogVisible = false">{{$t('action.cancel')}}</el-button>
      <el-button :size="size" type="primary" @click.native="submitForm" :loading="editLoading">{{$t('action.submit')}}</el-button>
    </div>
  </el-dialog>
  </div>
</template>

<script>
import KtTable from '@/views/core/KtTable'
import KtButton from '@/views/core/KtButton'
import { format } from '@/utils/datetime'
import { findPage, save, batchDelete, refreshCache } from '@/api/dict'
import { Message } from 'element-ui'

export default {
  name: 'dict',
  components: {
    KtTable,
    KtButton
  },
  data () {
    return {
      size: 'small',
      filters: {
        label: ''
      },
      columns: [
        { prop: 'id', label: 'ID', minWidth: 50 },
        { prop: 'lablName', label: '名称', minWidth: 100 },
        { prop: 'dataVal', label: '值', minWidth: 100 },
        { prop: 'codeType', label: '类型', minWidth: 80 },
        { prop: 'srt', label: '排序', minWidth: 80 },
        { prop: 'dscr', label: '描述', minWidth: 120 },
        { prop: 'memoInfo', label: '备注', minWidth: 120 },
        { prop: 'crter', label: '创建人', minWidth: 100 },
        { prop: 'crteTime', label: '创建时间', minWidth: 120, formatter: this.dateFormat }
        // {prop:"updtPsn", label:"更新人", minWidth:100},
        // {prop:"updtTime", label:"更新时间", minWidth:120, formatter:this.dateFormat}
      ],
      pageRequest: { pageNum: 1, pageSize: 10 },
      pageResult: {},

      oprt: false, // true:新增, false:编辑
      editDialogVisible: false, // 新增编辑界面是否显示
      editLoading: false,
      dataFormRules: {
        label: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
      },
      // 新增编辑界面数据
      dataForm: {
        id: 0,
        lablName: '',
        dataVal: '',
        codeType: '',
        srt: 0,
        dscr: '',
        memo_info: ''
      },
      tableHeight: 0
    }
  },
  methods: {
    // 获取分页数据
    findPage: function (data) {
      if (data !== null) {
        this.pageRequest = data.pageRequest
      }
      this.pageRequest.columnFilters = { label: { name: 'label', value: this.filters.label } }
      findPage(this.pageRequest).then((res) => {
        this.pageResult = res.data
      }).then(data != null ? data.callback : '')
    },
    // 批量删除
    handleDelete: function (data) {
      batchDelete(data.params).then(data != null ? data.callback : '')
    },
    handlerRefresh (data) {
      refreshCache({}).then(data => {
        if (data.code === 200) {
          Message({
            type: 'success',
            message: '刷新缓存成功'
          })
        }
      })
    },
    // 显示新增界面
    handleAdd: function () {
      this.editDialogVisible = true
      this.oprt = true
      this.dataForm = {
        id: 0,
        lablName: '',
        dataVal: '',
        codeType: '',
        srt: 0,
        dscr: '',
        memo_info: ''
      }
    },
    // 显示编辑界面
    handleEdit: function (params) {
      this.editDialogVisible = true
      this.oprt = false
      this.dataForm = Object.assign({}, params.row)
    },
    // 编辑
    submitForm: function () {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.$confirm('确认提交吗？', '提示', {}).then(() => {
            this.editLoading = true
            let params = Object.assign({}, this.dataForm)
            save(params).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '操作成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败, ' + res.msg, type: 'error' })
              }
              this.editLoading = false
              // this.$refs['dataForm'].resetFields()
              this.editDialogVisible = false
              this.findPage(null)
            })
          })
        }
      })
    },
    // 显示
    showAdd (data) {
      this.editDialogVisible = true
      this.oprt = true
      Object.assign(this.dataForm, data)
      this.dataForm.id = 0
    },
    // 时间格式化
    dateFormat: function (row, column, cellValue, index) {
      return format(row[column.property])
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.ktTable.$el.offsetTop：表格距离浏览器的高度
      // 50表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.ktTable.$el.offsetTop - 50
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.ktTable.$el.offsetTop - 50
      }
    })
  }
}
</script>

<style scoped>

</style>
