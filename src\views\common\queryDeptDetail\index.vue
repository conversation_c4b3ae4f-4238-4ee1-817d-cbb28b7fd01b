<template>
  <div class="app-container">
    <drg-container :headerPercent="10">
      <template slot="header">
        <drg-title-line title="查询条件" />
        <el-form :inline="true" :model="listQuery" size="mini">
              <el-form-item label="时间范围">
                <el-date-picker :disabled="true"
                                class="som-form-item"
                                v-model="listQuery.cysj"
                                type="daterange"
                                size="mini"
                                unlink-panels
                                range-separator="-"
                                value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="prefix + '名称'">
                <el-input v-model="listQuery.queryDrgsName" disabled></el-input>
              </el-form-item>
              <el-button class="expBtn" @click="exportExcel()" size="mini">导出Excel</el-button>
        </el-form>
      </template>
      <template slot="content">
        <drg-title-line title="科室详情" />
        <div class="table-container" style="height: 90%;width: 100%">
          <el-table ref="deptDetail"
                    id="deptTable"
                    :key=Math.random()
                    size="mini"
                    height="100%"
                    stripe
                    :data="list"
                    style="width: 100%;"
                    v-loading="listLoading"
                    border>
            <el-table-column fixed
                             label="序号"
                             type="index"
                             width="50">
            </el-table-column>
            <el-table-column label="出院科室编码" v-if="false" >
              <template slot-scope="scope">{{scope.row.priOutHosDeptCode | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column fixed label="出院科室名称"  align="left" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column fixed :label="prefix + '编码'"  align="center" width="85" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.medicalTotalNum)>0" class='skip' @click="queryMedicalTotalNum(scope.row)">
                  {{scope.row.drgsCode | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.medicalTotalNum)==0" style="color:#000000">
                  {{scope.row.drgsCode | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="prefix + '名称'"  align="center" width="150"  :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.medicalTotalNum)>0" class='skip' @click="queryMedicalTotalNum(scope.row)">
                  {{scope.row.drgsName | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.medicalTotalNum)==0" style="color:#000000">
                  {{scope.row.drgsName | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="病案总数"  align="center" width="90" >
              <template slot-scope="scope">
                <div v-if="Number(scope.row.medicalTotalNum)>0" class='skip' @click="queryMedicalTotalNum(scope.row)">
                  {{scope.row.medicalTotalNum | formatIsEmpty}}
                </div>
                <div v-if="Number(scope.row.medicalTotalNum)==0" style="color:#000000">
                  {{scope.row.medicalTotalNum | formatIsEmpty}}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="病案总数占比"  align="center" width="120">
              <template slot-scope="scope">{{scope.row.medicalTotalNumRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="总权重"  align="center" width="80" >
              <template slot-scope="scope">{{scope.row.totalDrgWeight | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="总住院费用"  align="center" width="100" >
              <template slot-scope="scope">{{scope.row.sumfee | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="总住院费用占比"  align="center" width="120" >
              <template slot-scope="scope">{{scope.row.totalCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="结算清单总费用"  align="center" width="120" >
              <template slot-scope="scope">{{scope.row.totalBaseCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="结算清单总费用占比"  align="center" >
              <template slot-scope="scope">{{scope.row.totalBaseCostRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院费用"  align="center" width="100">
              <template slot-scope="scope">{{scope.row.avgCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="总住院天数"   align="center" width="90" >
              <template slot-scope="scope">{{scope.row.totalDays | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="总住院天数占比"  align="center" >
              <template slot-scope="scope">{{scope.row.totalDaysRate | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="平均住院日"  align="center" width="90">
              <template slot-scope="scope">{{scope.row.avgDays | formatIsEmpty}}</template>
            </el-table-column>

          </el-table>
        </div>
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            layout="total, sizes,prev, pager, next,jumper"
            :page-size="listQuery.pageSize"
            :page-sizes="[200,1000,5000,10000]"
            :current-page.sync="listQuery.pageNum"
            :total="total">
          </el-pagination>
        </div>
      </template>
    </drg-container>
  </div>
</template>
<script>
import { queryDeptDetailList } from '@/api/common/drgCommon'
import { elExportExcel } from '@/utils/exportExcel'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  queryDrgsCode: null,
  queryDrgsName: null,
  cy_start_date: null,
  cy_end_date: null,
  type: null
}
export default {
  name: 'queryDeptDetail',
  data () {
    return {
      listLoading: true,
      list: null,
      total: null,
      listQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      prefix: ''
    }
  },
  created () {

  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.deptDetail.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.deptDetail.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.deptDetail.$el.offsetTop - 35
      }
    })
    if (Object.keys(this.$route.query).length > 0) {
      if (this.$route.query.inHosFlag) {
        this.listQuery.inHosFlag = this.$route.query.inHosFlag
        if (this.$route.query.inHosFlag == '1') {
          this.timeName = '出院时间'
          Object.assign(this.listQuery, { cysj: [this.$route.query.cy_start_date, this.$route.query.cy_end_date] })
        } else if (this.$route.query.inHosFlag == '2') {
          this.timeName = '入院时间'
          Object.assign(this.listQuery, { cysj: [this.$route.query.inStartTime, this.$route.query.inEndTime] })
        } else if (this.$route.query.inHosFlag == '3') {
          this.timeName = '结算时间'
          Object.assign(this.listQuery, { cysj: [this.$route.query.seStartTime, this.$route.query.seEndTime] })
        }
      }
      if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
        Object.assign(this.listQuery, { cy_start_date: this.$route.query.cy_start_date })
        Object.assign(this.listQuery, { cy_end_date: this.$route.query.cy_end_date })
        this.setTimeToNull('1')
      }
      if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
        Object.assign(this.listQuery, { inStartTime: this.$route.query.inStartTime })
        Object.assign(this.listQuery, { inEndTime: this.$route.query.inEndTime })
        this.setTimeToNull('2')
      }
      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        Object.assign(this.listQuery, { seStartTime: this.$route.query.seStartTime })
        Object.assign(this.listQuery, { seEndTime: this.$route.query.seEndTime })
        this.setTimeToNull('3')
      }
    }
    this.getList()
  },
  methods: {
    getList () {
      // 回显
      if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
        Object.assign(this.listQuery, { cysj: [this.$route.query.cy_start_date, this.$route.query.cy_end_date] })
      }
      Object.assign(this.listQuery, { cy_start_date: this.$route.query.cy_start_date })
      Object.assign(this.listQuery, { cy_end_date: this.$route.query.cy_end_date })
      Object.assign(this.listQuery, { queryDrgsCode: this.$route.query.queryDrgsCode })
      Object.assign(this.listQuery, { queryDrgsName: this.$route.query.queryDrgsName })
      Object.assign(this.listQuery, { type: this.$route.query.type })
      Object.assign(this.listQuery, { priOutHosDeptCode: this.$route.query.priOutHosDeptCode })
      this.prefix = this.$somms.getCodePrefixByType(this.$route.query.type)
      this.listLoading = true
      queryDeptDetailList(this.listQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    setTimeToNull (type) {
      if (type != '1') {
        this.listQuery.cy_start_date = ''
        this.listQuery.cy_end_date = ''
      }
      if (type != '2') {
        this.listQuery.inStartTime = ''
        this.listQuery.inEndTime = ''
      }
      if (type != '3') {
        this.listQuery.seStartTime = ''
        this.listQuery.seEndTime = ''
      }
    },
    // 下转
    queryMedicalTotalNum (row) {
      this.$router.push({ path: '/common/queryMedicalDetail',
        query: {
          queryDrgsCode: row.drgsCode,
          queryDrgsName: row.drgsName,
          priOutHosDeptCode: row.priOutHosDeptCode,
          priOutHosDeptName: row.priOutHosDeptName,
          cy_start_date: this.$route.query.inHosFlag == '1' ? this.listQuery.cysj[0] : '',
          cy_end_date: this.$route.query.inHosFlag == '1' ? this.listQuery.cysj[1] : '',
          inStartTime: this.$route.query.inHosFlag == '2' ? this.listQuery.cysj[0] : '',
          inEndTime: this.$route.query.inHosFlag == '2' ? this.listQuery.cysj[1] : '',
          seStartTime: this.$route.query.inHosFlag == '3' ? this.listQuery.cysj[0] : '',
          seEndTime: this.$route.query.inHosFlag == '3' ? this.listQuery.cysj[1] : '',
          inHosFlag: this.$route.query.inHosFlag,
          type: this.listQuery.type
        }
      })
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    exportExcel () {
      let tableId = 'deptTable'
      let fileName = '科室详情信息'
      elExportExcel(tableId, fileName)
    }
  }
}
</script>
<style>
</style>
