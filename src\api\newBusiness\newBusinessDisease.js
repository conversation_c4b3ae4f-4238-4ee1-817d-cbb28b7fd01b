import request from '@/utils/request'

export function queryDiseaseKpiData (params) {
  return request({
    url: '/newDipBusinessDiseaseAnalysisController/queryDiseaseKpiData',
    method: 'post',
    params: params
  })
}

export function queryDiseaseForecastData (params) {
  return request({
    url: '/newDipBusinessDiseaseAnalysisController/queryDiseaseForecastData',
    method: 'post',
    params: params
  })
}

export function queryDrgDiseaseKpiData (params) {
  return request({
    url: '/newDrgBusinessDiseaseAnalysisController/queryDrgDiseaseKpiData',
    method: 'post',
    params: params
  })
}

export function queryDrgDiseaseForecastData (params) {
  return request({
    url: '/newDrgBusinessDiseaseAnalysisController/queryDrgDiseaseForecastData',
    method: 'post',
    params: params
  })
}
