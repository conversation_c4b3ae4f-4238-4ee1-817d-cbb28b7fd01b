<template>
  <div class="all">
    <div class="top">
      <!--<div class="companylogo">
        <div class="logo-image"></div>
        <div class="logo-name">
          <div class="chineseName">数据</div>
        </div>
      </div>-->
      <div class="systemlogo">
        <div class="logo-image"></div>
        <div class="logo-name">
          <div class="chineseName">DRG+DIP控费管理平台</div>
          <div class="englishName">FanBen DRG+DIP Cost Control Management Platform</div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="loginFormContent">
        <div class="loginFormUser">
          <el-form autoComplete="on"
                   :model="loginForm"
                   :rules="loginRules"
                   ref="loginForm"
                   label-position="left">
            <el-form-item prop="username">
              <el-input name="username"
                        type="text"
                        v-model="loginForm.username"
                        autoComplete="on"
                        @change="changeUserName"
                        placeholder="请输入用户名">
                <span slot="prefix">
                  <svg-icon icon-class="user" class="color-main"></svg-icon>
                </span>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input name="password"
                        :type="pwdType"
                        @keyup.enter.native="handleLogin"
                        v-model="loginForm.password"
                        autoComplete="on"
                        placeholder="请输入密码">
                <span slot="prefix">
                  <svg-icon icon-class="password" class="color-main"></svg-icon>
                </span>
                <span slot="suffix" @click="showPwd">
                  <svg-icon icon-class="eye" class="color-main"></svg-icon>
                </span>
              </el-input>
            </el-form-item>
            <!--<el-form-item prop="code">-->
            <!--<el-input name="code"-->
            <!--:type="pwdType"-->
            <!--@keyup.enter.native="handleLogin"-->
            <!--v-model="loginForm.code"-->
            <!--autoComplete="on"-->
            <!--value="E3D2"-->
            <!--placeholder="请输入验证码">-->
            <!--<span slot="prefix">-->
            <!--<svg-icon icon-class="password" class="code"></svg-icon>-->
            <!--</span>-->
            <!--<span slot="suffix" @click="showPwd">-->
            <!--<svg-icon icon-class="code" class="code"></svg-icon>-->
            <!--</span>-->
            <!--</el-input>-->
            <!--</el-form-item>-->
            <el-form-item style="margin-bottom: 30px;text-align: center">
              <el-button style="width: 45%" type="primary" :loading="loading" @click.native.prevent="handleLogin"
                         :disabled="expireErrorInfoHidden">
                登 录
              </el-button>
            </el-form-item>
            <div class="expireWarnInfo" v-if="expireWarnInfoHidden"><i class="el-icon-info"></i> {{ expireWarnInfo }}
            </div>
            <div class="expireErrorInfo" v-if="expireErrorInfoHidden"><i class="el-icon-info"></i> {{ expireErrorInfo }}
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <!--      <div class="footer">-->

    <!--        Copyright@  数据科技有限公司  版权所有-->

    <!--      </div>-->
  </div>
</template>

<script>
import { isvalidUsername } from '@/utils/validate'
import { getCookie, setCookie, setSupport } from '@/utils/support'
import login_center_bg from '@/assets/images/login_center_bg.png'
import { querySysOutOfDate } from '@/api/login'

export default {
  name: 'login',
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!isvalidUsername(value)) {
        callback(new Error('请输入正确的用户名'))
      } else {
        callback()
      }
    }
    const validatePass = (rule, value, callback) => {
      if (value.length < 3) {
        callback(new Error('密码不能小于3位'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePass }]
      },
      loading: false,
      pwdType: 'password',
      login_center_bg,
      dialogVisible: false,
      supportDialogVisible: false,
      expireWarnInfoHidden: false,
      expireErrorInfoHidden: false,
      expireWarnInfo: '',
      expireErrorInfo: ''
    }
  },
  created () {
    // 查询系统是否过期
    this.sysOutOfDate()
    this.loginForm.username = getCookie('username')
    this.loginForm.password = getCookie('password')
    if (this.loginForm.username === undefined || this.loginForm.username == null || this.loginForm.username === '') {
      this.loginForm.username = ''
    }
    if (this.loginForm.password === undefined || this.loginForm.password == null) {
      this.loginForm.password = ''
    }
  },
  methods: {
    showPwd () {
      if (this.pwdType === 'password') {
        this.pwdType = ''
      } else {
        this.pwdType = 'password'
      }
    },
    handleLogin () {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$store.dispatch('Login', this.loginForm).then(() => {
            this.loading = false
            setCookie('username', this.loginForm.username, 15)
            // setCookie("password",this.loginForm.password,15);
            this.$router.push({ path: '/' })
          }).catch(() => {
            this.loading = false
          })
        } else {
          console.log('参数验证不合法！')
          return false
        }
      })
    },
    sysOutOfDate () {
      querySysOutOfDate().then(response => {
        let data = response.data
        if (data) {
          if (data.errorInfo) {
            this.expireErrorInfoHidden = true
            this.expireErrorInfo = data.errorInfo
          } else if (data.warningInfo) {
            this.expireWarnInfoHidden = true
            this.expireWarnInfo = data.warningInfo
          }
        }
      }).catch(() => {
        this.loading = false
      })
    },
    changeUserName () {
      if (this.loginForm.username == 'developer') {
        this.expireErrorInfoHidden = false
        this.expireWarnInfoHidden = false
      } else {
        this.sysOutOfDate()
      }
    },
    handleTry () {
      this.dialogVisible = true
    },
    dialogConfirm () {
      this.dialogVisible = false
      setSupport(true)
    },
    dialogCancel () {
      this.dialogVisible = false
      setSupport(false)
    }
  }
}
</script>

<style scoped>
.login-form-layout {
  position: absolute;
  left: 0;
  right: 0;
  width: 360px;
  margin: 13% auto;
  border-top: 10px solid #409EFF;
}

.login-title {
  text-align: center;
  margin-bottom: 20px;
}

.login-center-layout {
  background: #409EFF;
  width: 100%;
  height: 450px;
  margin: 9% auto;
  max-width: 100%;
}

.expireWarnInfo {
  text-align: center;
  font-size: 13px;
  color: #FFA500;
}

.expireErrorInfo {
  text-align: center;
  font-size: 13px;
  color: #FF3300;
}

.end {
  margin-left: 30px;
  margin-top: 10px;
  font-size: 11px;
}

.all {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.top {
  width: 100%;
  margin-top: 6%;
  display: flex;
  align-items: center;
  flex-direction: row;
  position: relative;
  justify-content: center;
}

.top .companylogo {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.companylogo .logo-image {
  width: 80px;
  height: 60px;
  /*background: url(../../assets/images/login/fanbendata.png) no-repeat center;*/
  background-size: 100% 100%;
}

.companylogo .logo-name .chineseName {
  font-size: 28px;
  font-family: STXinwei;
}

.top .systemlogo {
  margin-bottom: 10px;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.systemlogo .logo-image {
  width: 200px;
  height: 35px;
  background-size: 100% 100%;
}

.systemlogo .logo-name .chineseName {
  font-size: 30px;
  font-family: "腾祥智黑简-W3";
}

.systemlogo .logo-name .englishName {
  font-size: 16px;
  font-family: "Arial Regular";
  color: #9e9c9c;
}

.content {
  width: 100%;
  height: 380px;
  background: url(../../assets/images/login/login_center_bg.png) no-repeat center;
  background-size: 100% 100%;
}

.loginFormContent {
  flex: auto;
  width: 25%;
  height: 350px;
  margin: auto;
  margin-top: 15px;
  position: relative;
  display: flex;
  flex-direction: row;
}

.loginFormLogo {
  width: 50%;
  flex: auto;
  background: url(../../assets/images/login/login_center_bg.png) no-repeat center;
}

.loginFormUser {
  width: 30%;
  flex: auto;
  background-color: #FFFFFF;
  border-radius: 0px 3px 3px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer {
  width: 100%;
  margin-top: 40px;
  text-align: center;
  font-family: "宋体";
  line-height: 16px;
  font-size: 10px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
