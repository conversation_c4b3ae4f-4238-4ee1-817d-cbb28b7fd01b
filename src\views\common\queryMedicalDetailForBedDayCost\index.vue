<template>
  <div class="app-container">
    <el-card class="filter-container"  style="height:45px;overflow-y:auto;">
      <div style="margin-top: -14px">
        <el-form :inline="true" :model="listQuery" size="mini">
          <el-row :gutter="0">
            <el-col :span="6" align="center">
              <el-form-item label="时间范围">
                <el-date-picker disabled
                    v-model="listQuery.cysj"
                    type="daterange"
                    size="mini"
                    unlink-panels
                    range-separator="-"
                    value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6" align="center">
              <el-form-item label="出院科室名称">
                <el-input v-model="listQuery.priOutHosDeptName"  disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" align="center">
              <el-form-item label="床日付费类型">
                <el-input v-model="listQuery.bedCostTypeName" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" align="center">
              <el-button class="expBtn" @click="exportExcel()" size="mini">导出Excel</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
    <div class="table-container" style="margin-top:10px;flex:5px; overflow-y:auto;">
      <el-table ref="medicalDetailForBedDayCost"
                id="medicalTableForBedDayCost"
                size="mini"
                :height="tableHeight"
                :data="list"
                style="width: 100%;"
                v-loading="listLoading"
                border>
        <el-table-column fixed label="序号" type="index" width="50"></el-table-column>
        <el-table-column fixed label="病案号" align="left" width="100" :show-overflow-tooltip="true">
          <template slot-scope="scope">{{scope.row.patientId | formatIsEmpty}}</template>
          <template slot-scope="scope">
            <div v-if="scope.row.patientId=='合计'" style="margin-left:50px">
              {{scope.row.patientId | formatIsEmpty}}
            </div>
            <div v-if="scope.row.patientId!='合计'">
              {{scope.row.patientId | formatIsEmpty}}
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed label="姓名"  align="center" width="70">
          <template slot-scope="scope">{{scope.row.name | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column label="入院时间"  align="center" width="100">
          <template slot-scope="scope">{{scope.row.inHosTime | formatTime }}</template>
        </el-table-column>
        <el-table-column label="出院时间"  align="center" width="100">
          <template slot-scope="scope">{{scope.row.outHosTime | formatTime }}</template>
        </el-table-column>
        <el-table-column  label="DRGs编码"  align="center" width="90">
          <template slot-scope="scope">{{scope.row.drgsCode | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column label="DRGs名称"  align="center"  width="110" :show-overflow-tooltip="true">
          <template slot-scope="scope">{{scope.row.drgsName | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column label="分组信息"  align="center"  :show-overflow-tooltip="true">
          <template slot-scope="scope">{{scope.row.grperOuptLog | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column  label="出院科室"  align="center" width="80">
          <template slot-scope="scope">{{scope.row.priOutHosDeptName | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column label="标杆住院天数"  align="center" width="100">
          <template slot-scope="scope">{{scope.row.drgStandardDays | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column label="住院天数"  align="center" width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.inHosDays==null||scope.row.inHosDays==''||scope.row.inHosDays==0||scope.row.drgStandardDays==null||scope.row.drgStandardDays==''||scope.row.drgStandardDays==0">
              {{scope.row.inHosDays | formatIsEmpty}}
            </div>
            <div v-else-if="Number(scope.row.inHosDays)>Number(scope.row.drgStandardDays)">
              {{scope.row.inHosDays | formatIsEmpty}}&emsp;
              <i class="el-icon-caret-bottom"></i>
              <span style="font-size: 10px;color:#FF0000;font-weight: bold">
                {{(Number(scope.row.drgStandardDays)-Number(scope.row.inHosDays)).toFixed(1)}}
              </span>
            </div>
            <div v-else-if="Number(scope.row.inHosDays)<=Number(scope.row.drgStandardDays)">
              {{scope.row.inHosDays | formatIsEmpty}}&emsp;
              <i class="el-icon-caret-top"></i>
              <span style="font-size: 10px;color:#00CC00;font-weight: bold">
                +{{(Number(scope.row.drgStandardDays)-Number(scope.row.inHosDays)).toFixed(1)}}
              </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="标杆床日费用"  align="center" width="120">
          <template slot-scope="scope">{{scope.row.drgStandardBedCost | formatIsEmpty}}</template>
        </el-table-column>
        <el-table-column label="床日费用"  align="center" width="160">
          <template slot-scope="scope">
            <div v-if="scope.row.bedDayCost==null||scope.row.bedDayCost==''||scope.row.bedDayCost==0||scope.row.drgStandardBedCost==null||scope.row.drgStandardBedCost==''||scope.row.drgStandardBedCost==0">
              {{scope.row.bedDayCost | formatIsEmpty}}
            </div>
            <div v-else-if="Number(scope.row.bedDayCost)>Number(scope.row.drgStandardBedCost)">
              {{scope.row.bedDayCost | formatIsEmpty}}&emsp;
              <i class="el-icon-caret-bottom"></i>
              <span style="font-size: 10px;color:#FF0000;font-weight: bold">
                {{(Number(scope.row.drgStandardBedCost)-Number(scope.row.bedDayCost)).toFixed(2)}}
              </span>
            </div>
            <div v-else-if="Number(scope.row.bedDayCost)<=Number(scope.row.drgStandardBedCost)">
              {{scope.row.bedDayCost | formatIsEmpty}}&emsp;
              <i class="el-icon-caret-top"></i>
              <span style="font-size: 10px;color:#00CC00;font-weight: bold">
                +{{(Number(scope.row.drgStandardBedCost)-Number(scope.row.bedDayCost)).toFixed(2)}}
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        layout="total, sizes,prev, pager, next,jumper"
        :page-size="listQuery.pageSize"
        :page-sizes="[200,1000,5000,10000]"
        :current-page.sync="listQuery.pageNum"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { queryMedicalDetailForBedDayCostList, querySelectTreeAndSelectList } from '@/api/common/drgCommon'
import { elExportExcel } from '@/utils/exportExcel'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  priOutHosDeptCode: null,
  priOutHosDeptName: null,
  queryDrgsCode: null,
  beddayPayType: null,
  bedCostTypeName: null,
  cy_start_date: null,
  cy_end_date: null
}
export default {
  name: 'queryMedicalDetailForBedDayCost',
  data () {
    return {
      listLoading: true,
      list: null,
      total: null,
      listQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0
    }
  },
  created () {
    this.getList()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.medicalDetailForBedDayCost.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.medicalDetailForBedDayCost.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.medicalDetailForBedDayCost.$el.offsetTop - 35
      }
    })
  },
  methods: {
    getList () {
      // 回显
      if (this.$route.query.cy_start_date && this.$route.query.cy_end_date) {
        Object.assign(this.listQuery, { cysj: [this.$route.query.cy_start_date, this.$route.query.cy_end_date] })
      }
      Object.assign(this.listQuery, { cy_start_date: this.$route.query.cy_start_date })
      Object.assign(this.listQuery, { cy_end_date: this.$route.query.cy_end_date })
      Object.assign(this.listQuery, { beddayPayType: this.$route.query.beddayPayType })
      Object.assign(this.listQuery, { bedCostTypeName: this.$route.query.bedCostTypeName })
      Object.assign(this.listQuery, { priOutHosDeptCode: this.$route.query.priOutHosDeptCode })
      Object.assign(this.listQuery, { priOutHosDeptName: this.$route.query.priOutHosDeptName })
      Object.assign(this.listQuery, { queryDrgsCode: this.$route.query.queryDrgsCode })
      this.listLoading = true
      queryMedicalDetailForBedDayCostList(this.listQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    exportExcel () {
      let tableId = 'medicalTableForBedDayCost'
      let fileName = '床日付费病案详细信息'
      elExportExcel(tableId, fileName)
    }
  }
}
</script>
<style scoped>
  .el-icon-caret-bottom{
    color:#FF0000;
  }
  .el-icon-caret-top{
    color:#00CC00;
  }
</style>
