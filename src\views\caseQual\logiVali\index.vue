<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-patient-num
             show-hos-dept
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="病例逻辑校对情况"
             :container="true"
             @query="handleSearchList">

      <template slot="extendFormItems">
        <el-form-item label="错误类型">
          <el-select v-model="listQuery.errType" clearable placeholder="请选择" class="som-form-item" @change="handleSearchList">
            <template v-for="(item,index)  in dictVoList.ERROR_TYPE">
              <el-option
                v-if="index>=4"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </template>
          </el-select>
        </el-form-item>
      </template>

      <template slot="buttons">
        <el-popconfirm
          confirm-button-text='确定'
          cancel-button-text='导出全部'
          icon="el-icon-info"
          icon-color="red"
          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>

      <template slot="containerContent">
        <div style="height:35%;">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="11" style="height: 100%">
              <div id="errorCount" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
            <el-col :span="13" style="height: 100%">
              <el-row :gutter="10" style="height: 48%">
                <el-col :span="8" style="height: 100%">
                  <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                    <div class="content-title" >编码信息错误病案</div>
                    <div style="height:70%;width:100%;padding: 10px;font-size: 15px;font-weight: bold;display: flex;">
                      <div style="position:relative;width: 45%;height:100%;">
                        <span v-if="codeError>=10000" style="position:absolute;left:0px;bottom:10%;font-size:30px;font-weight: bold;">
                          {{(codeError/10000).toFixed(1)}}万
                          <span style="font-size: 13px;font-weight: normal;">份</span>
                        </span>
                        <span v-else style="position:absolute;left:0px;bottom:10%;font-size:30px;font-weight: bold;">
                          {{codeError}}
                          <span style="font-size: 13px;font-weight: normal;">份</span>
                        </span>
                      </div>
                      <div style="display: flex;flex-direction: column">
                        <div style="margin:auto">
                          <div style="font-size: 13px;font-weight: normal;">
                            正确占比
                            <span style="font-weight: bold;color:#409EFF;margin-left:5px;">{{((totalNum-codeError)/totalNum*100).toFixed(1)}}%</span>
                          </div>
                          <div style="font-size: 13px;font-weight: normal;margin-top:5px;">
                            错误占比
                            <span style="font-weight: bold;color:red;margin-left:5px;">{{((codeError)/totalNum*100).toFixed(1)}}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8" style="height: 100%">
                  <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                    <div class="content-title">时间信息错误病案</div>
                    <div style="height:70%;width:100%;padding: 10px;font-size: 15px;font-weight: bold;display: flex;">
                      <div style="position:relative;width: 45%;height:100%;">
                        <span style="position:absolute;left:0px;bottom:10%;font-size:30px;font-weight: bold;">
                          {{timeError}}
                          <span style="font-size: 13px;font-weight: normal;">份</span>
                        </span>
                      </div>
                      <div style="display: flex;flex-direction: column">
                        <div style="margin:auto;">
                          <div style="font-size: 13px;font-weight: normal;">
                            正确占比
                            <span style="font-weight: bold;color:#409EFF;margin-left:5px;">{{((totalNum-timeError)/totalNum*100).toFixed(1)}}%</span>
                          </div>
                          <div style="font-size: 13px;font-weight: normal;margin-top:5px;">
                            错误占比
                            <span style="font-weight: bold;color:red;margin-left:5px;">{{((timeError)/totalNum*100).toFixed(1)}}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8" style="height: 100%">
                  <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                    <div class="content-title">费用信息错误病案</div>
                    <div style="height:70%;width:100%;padding: 10px;font-size: 15px;font-weight: bold;display: flex;">
                      <div style="position:relative;width: 45%;height:100%;">
                        <span style="position:absolute;left:0px;bottom:10%;font-size:30px;font-weight: bold;">
                          {{costError}}
                          <span style="font-size: 13px;font-weight: normal;">份</span>
                        </span>
                      </div>
                      <div style="display: flex;flex-direction: column">
                        <div style="margin:auto;">
                          <div style="font-size: 13px;font-weight: normal;">
                            正确占比
                            <span style="font-weight: bold;color:#409EFF;margin-left:5px;">{{((totalNum-costError)/totalNum*100).toFixed(1)}}%</span>
                          </div>
                          <div style="font-size: 13px;font-weight: normal;margin-top:5px;">
                            错误占比
                            <span style="font-weight: bold;color:red;margin-left:5px;">{{((costError)/totalNum*100).toFixed(1)}}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="5" style="height: 48%;margin-top: 10px">
                <el-col :span="6" style="height: 100%">
                  <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                    <div class="content-title">性别信息错误病案</div>
                    <div style="height:70%;width:100%;padding: 10px;font-size: 15px;font-weight: bold;display: flex;">
                      <div style="position:relative;width: 100%;height:100%;">
                        <span style="position:absolute;left:0px;bottom:10%;font-size:23px;font-weight: bold;">
                          {{sexError}}
                          <span style="font-size: 13px;font-weight: normal;">份</span>
                        </span>
                      </div>
                      <div style="display: flex;flex-direction: column">
                        <div style="margin:auto">
                          <div style="font-size: 13px;font-weight: normal;">
                            正确占比
                            <span style="font-weight: bold;color:#409EFF;">{{((totalNum-sexError)/totalNum*100).toFixed(2)}}%</span>
                          </div>
                          <div style="font-size: 13px;font-weight: normal;margin-top:5px;">
                            错误占比
                            <span style="font-weight: bold;color:red;">{{((sexError)/totalNum*100).toFixed(2)}}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="6" style="height: 100%">
                  <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                    <div class="content-title">年龄信息错误病案</div>
                    <div style="height:70%;width:100%;padding: 10px;font-size: 15px;font-weight: bold;display: flex;">
                      <div style="position:relative;width: 100%;height:100%;">
                        <span style="position:absolute;left:0px;bottom:10%;font-size:23px;font-weight: bold;">
                          {{ageError}}
                          <span style="font-size: 13px;font-weight: normal;">份</span>
                        </span>
                      </div>
                      <div style="display: flex;flex-direction: column">
                        <div style="margin:auto">
                          <div style="font-size: 13px;font-weight: normal;">
                            正确占比
                            <span style="font-weight: bold;color:#409EFF;margin-left:5px;">{{((totalNum-ageError)/totalNum*100).toFixed(2)}}%</span>
                          </div>
                          <div style="font-size: 13px;font-weight: normal;margin-top:5px;">
                            错误占比
                            <span style="font-weight: bold;color:red;margin-left:5px;">{{((ageError)/totalNum*100).toFixed(2)}}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="6" style="height: 100%">
                  <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                    <div class="content-title">新生儿信息错误病案</div>
                    <div style="height:70%;width:100%;padding: 10px;font-size: 15px;font-weight: bold;display: flex;">
                      <div style="position:relative;width: 100%;height:100%;">
                        <span style="position:absolute;left:0px;bottom:10%;font-size:23px;font-weight: bold;">
                          {{babyInfoError}}
                          <span style="font-size: 13px;font-weight: normal;">份</span>
                        </span>
                      </div>
                      <div style="display: flex;flex-direction: column">
                        <div style="margin:auto;">
                          <div style="font-size: 13px;font-weight: normal;">
                            正确占比
                            <span style="font-weight: bold;color:#409EFF;margin-left:5px;">{{((totalNum-babyInfoError)/totalNum*100).toFixed(2)}}%</span>
                          </div>
                          <div style="font-size: 13px;font-weight: normal;margin-top:5px;">
                            错误占比
                            <span style="font-weight: bold;color:red;margin-left:5px;">{{((babyInfoError)/totalNum*100).toFixed(2)}}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="6" style="height: 100%">
                  <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                    <div class="content-title">生存矛盾错误病案</div>
                    <div style="height:70%;width:100%;padding: 10px;font-size: 15px;font-weight: bold;display: flex;">
                      <div style="position:relative;width: 100%;height:100%;">
                        <span style="position:absolute;left:0px;bottom:10%;font-size:23px;font-weight: bold;">
                          {{existenceError}}
                          <span style="font-size: 13px;font-weight: normal;">份</span>
                        </span>
                      </div>
                      <div style="display: flex;flex-direction: column">
                        <div style="margin:auto;">
                          <div style="font-size: 13px;font-weight: normal;">
                            正确占比
                            <span style="font-weight: bold;color:#409EFF;margin-left:5px;">{{((totalNum-existenceError)/totalNum*100).toFixed(2)}}%</span>
                          </div>
                          <div style="font-size: 13px;font-weight: normal;margin-top:5px;">
                            错误占比
                            <span style="font-weight: bold;color:red;margin-left:5px;">{{((existenceError)/totalNum*100).toFixed(2)}}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="flex:1; overflow-y:auto;height: 63%;width: 100%">
          <el-table ref="settleListTable"
                    id="logicTable"
                    :header-cell-style="{'text-align':'center'}"
                    size="mini"
                    stripe
                    height="100%"
                    :data="list"
                    style="width: 100%;"
                    v-loading="listLoading"
                    border>
            <el-table-column
              label="序号"
              type="index"
              align="right"
              width="50">
            </el-table-column>
            <el-table-column label="查看详情" align="center" width="75">
              <template slot-scope="scope">
                <el-button  type="info" style="background-color: rgba(57,74,94,0.8)" size="mini" icon="el-icon-search"  @click="handleShowMedicalDetail(scope.$index, scope.row)" circle>
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="逻辑性校验结果"  prop="validateResult" align="center" :show-overflow-tooltip='true' sortable>
              <template slot-scope="scope">
                <span v-if="scope.row.validateResult==null" style="color:blue">{{scope.row.validateResult | formatValidateResult}}</span>
                <span v-else style="color: red">{{ scope.row.validateResult | formatValidateResult}}</span>
              </template>
            </el-table-column>
            <el-table-column label="病案号" prop="a48" align="right" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.a48 | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="姓名" prop="a11" align="left" width="70" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.a11 | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="性别"  align="left" width="60" prop="a12c"
                             :filters="[{ text: '男', value: '1' }, { text: '女', value: '2' }]"
                             :filter-method="filterSex">
              <template slot-scope="scope">{{scope.row.a12c | formatA12c}}</template>
            </el-table-column>
            <el-table-column label="年龄" prop="a14" align="right" width="90" :show-overflow-tooltip="true" sortable>
              <template slot-scope="scope">{{scope.row.a14 | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="入院科室" prop="b13n" align="left" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.b13n | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="出院科室" prop="b16n" align="left" width="90" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.b16n | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="疾病主诊段" prop="c04n" align="left" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.c04n | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="手术主诊段" prop="c15x01n" align="left" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.c15x01n | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="入院时间" prop="b12" align="right" width="100" :show-overflow-tooltip="true" sortable>
              <template slot-scope="scope">{{scope.row.b12 | formatTime }}</template>
            </el-table-column>
            <el-table-column label="出院时间" prop="b15" align="right" width="100" :show-overflow-tooltip="true" sortable>
              <template slot-scope="scope">{{scope.row.b15 | formatTime }}</template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, querySelectTreeAndSelectList } from '@/api/common/drgCommon'
import { fetchList as queryPageData, getCountInfo } from '@/api/medicalQuality/logicValidate'
import { formatDate } from '@/utils/date'
import echarts from 'echarts'
import { elExportExcel } from '@/utils/exportExcel'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  b16c: null,
  a48: null,
  errType: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date,
  dipCodg: '',
  resultType: '',
  doctorCodee: ''
}
export default {
  name: 'logiVali',
  components: { },
  inject: ['reload'],
  data () {
    return {
      depts: [],
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      dictVoList: {},
      listLoading: true,
      list: null,
      total: null,
      cy_start_date: null,
      cy_end_date: null,
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      cykb: null,
      totalNum: null,
      errorNum: null,
      codeError: null,
      timeError: null,
      costError: null,
      sexError: null,
      ageError: null,
      babyInfoError: null,
      existenceError: null,
      tableHeight: 0,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatValidateResult (value) {
      if (value) {
        return value
      } else {
        return '√'
      }
    },
    formatTime (time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },
    formatA12c (value) {
      if (value == '1') {
        return '男'
      } else if (value == '2') {
        return '女'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.settleListTable.$el.offsetTop：表格距离浏览器的高度
      // 50表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.settleListTable.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.settleListTable.$el.offsetTop - 35
      }
    })
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'ERROR_TYPE')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]

        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.cy_start_date = this.$route.query.begnDate
          this.cy_end_date = this.$route.query.expiDate
          this.listQuery.cysj = [this.$route.query.begnDate, this.$route.query.expiDate]
        }
        if (this.$route.query.dipCodg) {
          this.listQuery.dipCodg = this.$route.query.dipCodg
        }
        if (this.$route.query.deptCode) {
          this.listQuery.b16c = this.$route.query.deptCode
        }
        if (this.$route.query.drCodg) {
          this.listQuery.drCodg = this.$route.query.drCodg
        }
        if (this.$route.query.resultType) {
          this.listQuery.resultType = this.$route.query.resultType
        }

        // 查询数据
        this.getList()
        this.getCount()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.errType = this.listQuery.errType
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.a48 = this.listQuery.medcasCodg
      this.submitListQuery.dipCodg = this.listQuery.dipCodg
      this.submitListQuery.resultType = this.listQuery.resultType
      this.submitListQuery.drCodg = this.listQuery.drCodg
      this.submitListQuery.ry_start_date = this.listQuery.inStartTime
      this.submitListQuery.ry_end_date = this.listQuery.inEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.settle_start_date = this.listQuery.seStartTime
      this.submitListQuery.settle_end_date = this.listQuery.seEndTime
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.settleListTable.$children, document.getElementById('logicTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病案质控-逻辑性校对结果')
    },
    getCount () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.settle_start_date = this.listQuery.seStartTime
      this.submitListQuery.settle_end_date = this.listQuery.seEndTime
      this.submitListQuery.ry_start_date = this.listQuery.inStartTime
      this.submitListQuery.ry_end_date = this.listQuery.inEndTime
      getCountInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        this.totalNum = response.data.totalNum
        this.errorNum = response.data.errorNum
        this.codeError = response.data.codeError
        this.timeError = response.data.timeError
        this.costError = response.data.costError
        this.sexError = response.data.sexError
        this.ageError = response.data.ageError
        this.babyInfoError = response.data.babyInfoError
        this.existenceError = response.data.existenceError
        this.pieCount()
      })
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
      this.getCount()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.getList()
      this.getCount()
    },
    handleResetSearch () {
      this.getDataIsuue()
    },
    handleShowMedicalDetail (index, row) {
      this.$router.push({ path: '/setlListManage/setlListDetail', query: { id: row.id, k00: row.k00 } })
    },
    filterSex (value, row) {
      return row.a12c === value
    },
    pieCount () {
      let colors = ['#f56464', '#56d9bc']
      let option = {
        title: [
          { text: '逻辑性校验情况', left: 10, top: 10, textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          top: 'center',
          data: ['逻辑性错误病案', '正确病案']
        },
        series: [
          {
            type: 'pie',
            center: ['58%', '50%'],
            radius: ['40%', '65%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'outside',
                formatter: '{b}：{c}  ({d}%)',
                fontFamily: 'Microsoft YaHei',
                fontSize: 12,
                color: '#2b2b2b'
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % 2]
                }
              }
            },
            data: [
              { value: this.errorNum, name: '逻辑性错误病案' },
              { value: this.totalNum - this.errorNum, name: '正确病案' }
            ]
          }
        ]
      }
      let errorCount = echarts.getInstanceByDom(document.getElementById('errorCount'))
      if (errorCount) {
        errorCount.clear()
      } else {
        errorCount = echarts.init(document.getElementById('errorCount'))
      }
      errorCount.setOption(option)
      return errorCount
    },
    exportExcel () {
      let tableId = 'logicTable'
      let fileName = '病案质控-逻辑性校对结果'
      elExportExcel(tableId, fileName)
    },
    clearRouteQuery () {
      if (this.$route.query) {
        this.$router.push({ query: {} }).catch(() => {})
      }
    },
    refresh () {
      this.reload()
      this.clearRouteQuery()
    }
  }
}
</script>
<style>
.content-title{
  height:30%;width:100%;padding: 6px;font-size: 12px;font-weight: bold;background-color: rgba(57,74,94,0.8);color:white
}
</style>
