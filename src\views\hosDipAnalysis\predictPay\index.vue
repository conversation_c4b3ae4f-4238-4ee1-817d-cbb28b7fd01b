<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
              ref="somForm"
              show-date-range
              show-in-date-range
              show-se-date-range
              show-dip
              show-patient-num
              show-hos-dept
              showPagination
              :totalNum="total"
              :initTimeValueNotQuery="false"
              headerTitle="查询条件"
              :container="true"
              :showCoustemContentTitle="true"
              @query="fnQuery" @reset="refresh">

      <template slot="extendFormItems" prop="categories">
        <el-form-item label="参保类型">
          <drg-dict-select dicType="INSURANCE_TYPE" placeholder="请选择人群类别" v-model="queryForm.categories"
                           @change="getDataIsuue"/>
        </el-form-item>
        <el-form-item label="费用区间" prop="costSection">
          <drg-dict-select v-model="queryForm.costSection" placeholder="请选择费用区间" dicType="CASE_TYPE"
                           @change="getDataIsuue"/>
        </el-form-item>
        <el-form-item label="费用状态" prop="isLoss">
          <el-select v-model="queryForm.isLoss" clearable placeholder="请选择" class="som-form-extend-form-item"
                     @change="getDataIsuue">
            <el-option v-for="item in isBoolean"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="补偿比类型">-->
        <!--          <drg-dict-select dicType="FUNDRATIO_TYPE" placeholder="请选择人群类别" v-model="queryForm.fundRatioType"-->
        <!--                           @change="getDataIsuue"/>-->
        <!--        </el-form-item>-->
      </template>

      <template slot="buttons">
        <el-popconfirm
          confirm-button-text='确定'
          cancel-button-text='导出全部'
          icon="el-icon-info"
          icon-color="red"
          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>

      <template slot="contentTitle">
        <drg-title-line title="DIP支付预测">
          <template slot="rightSide">
            <div style="display: flex;flex-direction: row;align-items: center">
              <div class="title-side">当前月份：
                <span class="title-side-value">{{ calculateYM }}</span>
              </div>
              <div class="title-side">城乡测算单价：
                <span class="title-side-value">{{ calculatePriceCx }}元/点</span>
              </div>
              <div class="title-side">城职测算单价：
                <span class="title-side-value">{{ calculatePriceCz }}元/点</span>
              </div>
              <div class="title-side">测算单价均值：
                <span class="title-side-value">{{ calculatePrice }}元/点</span>
              </div>
              <el-button class="title-side-button" @click="fnModifyPrice">修改测算单价</el-button>
            </div>
          </template>
        </drg-title-line>
      </template>

      <template slot="containerContent">
        <div style="height: 40%;width: 100%;">
          <el-row class="som-wd-one-hundred">
            <el-col :span="6" class="som-h-one-hundred">
              <div class="som-h-fifty som-w-one-hundred">
                <!-- 总点数 -->
                <div class="som-w-one-hundred ptp-left-item">
                  <div class="ptp-left-item-title som-wd-one-hundred">
                    <div class="ptp-circle"></div>
                    总分值:
                  </div>
                  <div class="ptp-left-item-value" v-html="formatCostNum(totalPoint.total,true)">
                    {{ totalPoint.total | ifNullZero }}
                  </div>
                </div>
                <!-- 占比柱状图 -->
                <drg-echarts :options="totalPointOptions" class="ptp-left-item-draw"/>
                <!--                <div class="ptp-left-item-draw" id="totalPoint"></div>-->
              </div>
              <div class="som-h-fifty som-w-one-hundred">
                <!-- 结算预测金额 -->
                <div class="som-w-one-hundred ptp-left-item">
                  <div class="ptp-left-item-title som-wd-one-hundred">
                    <div class="ptp-circle"></div>
                    结算预测金额:
                  </div>
                  <div class="ptp-left-item-value" v-html="formatCost(preCost.total, true)">
                  </div>
                </div>
                <!-- 占比柱状图 -->
                <drg-echarts :options="totalCostOptions" class="ptp-left-item-draw"/>
                <!--                <div class="ptp-left-item-draw" id="sumfee"></div>-->
              </div>
            </el-col>
            <el-col :span="8" class="som-h-one-hundred som-el-form-item-margin-left">
              <div class="ptp-center-header">
                <ul class="ptp-center-header-ul">
                  <li class="ptp-center-header-li" :class="dip_pre_checked.dipPre.checked ? 'checked' : 'unchecked'">
                    <span class="ptp-center-header-li-content"
                          @click="fnChangeDipChecked(dip_pre_checked.dipPre.index)">所有病例</span>
                  </li>
                  <li class="ptp-center-header-li" :class="dip_pre_checked.sumfee.checked ? 'checked' : 'unchecked'">
                    <span class="ptp-center-header-li-content"
                          @click="fnChangeDipChecked(dip_pre_checked.sumfee.index)">正常</span>
                  </li>
                  <li class="ptp-center-header-li" :class="dip_pre_checked.payCount.checked ? 'checked' : 'unchecked'">
                    <span class="ptp-center-header-li-content"
                          @click="fnChangeDipChecked(dip_pre_checked.payCount.index)">超高</span>
                  </li>
                  <li class="ptp-center-header-li" :class="dip_pre_checked.mrType.checked ? 'checked' : 'unchecked'">
                    <span class="ptp-center-header-li-content"
                          @click="fnChangeDipChecked(dip_pre_checked.mrType.index)">超低</span>
                  </li>
                  <li class="ptp-center-header-li" :class="dip_pre_checked.EXHType.checked ? 'checked' : 'unchecked'">
                    <span class="ptp-center-header-li-content"
                          @click="fnChangeDipChecked(dip_pre_checked.EXHType.index)">极高（极长）</span>
                  </li>
                  <li class="ptp-center-header-li"
                      :class="dip_pre_checked.instabilityType.checked ? 'checked' : 'unchecked'">
                    <span class="ptp-center-header-li-content"
                          @click="fnChangeDipChecked(dip_pre_checked.instabilityType.index)">非稳定</span>
                  </li>
                  <li class="ptp-center-header-li" :class="dip_pre_checked.nobkType.checked ? 'checked' : 'unchecked'">
                    <span class="ptp-center-header-li-content"
                          @click="fnChangeDipChecked(dip_pre_checked.nobkType.index)">无标杆</span>
                  </li>
                  <li class="ptp-center-header-li"
                      :class="dip_pre_checked.nogroupType.checked ? 'checked' : 'unchecked'">
                    <span class="ptp-center-header-li-content"
                          @click="fnChangeDipChecked(dip_pre_checked.nogroupType.index)">未入组</span>
                  </li>
                </ul>
              </div>
              <div class="ptp-center-content">
                <div class="ptp-center-content-pre">
                  <!-- DIP预付 -->
                  <div class="som-w-fifty som-h-fifty ptp-center-content-item">
                    <div class="ptp-center-content-item-circle">
                      <div class="ptp-circle"></div>
                    </div>
                    <div>
                      <div class="ptp-center-content-title-value">
                        DIP预付：<span class="ptp-center-content-samll-title"
                                      v-html="formatCost(preCost.total, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城职：<span v-html="formatCost(preCost.cz, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城乡：<span v-html="formatCost(preCost.cx, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城职基金：<span v-html="formatCost(preCost.cz_fund, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城乡基金：<span v-html="formatCost(preCost.cx_fund, true)"></span>
                      </div>
                    </div>
                  </div>
                  <!-- 原项目 -->
                  <div class="som-w-fifty som-h-fifty ptp-center-content-item">
                    <div class="ptp-center-content-item-circle">
                      <div class="ptp-circle"></div>
                    </div>
                    <div>
                      <div class="ptp-center-content-title-value">
                        原项目：<span class="ptp-center-content-samll-title"
                                     v-html="formatCost(originalProj.total, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城职：<span v-html="formatCost(originalProj.cz, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城乡：<span v-html="formatCost(originalProj.cx, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城职基金：<span v-html="formatCost(originalProj.cz_fund, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城乡基金：<span v-html="formatCost(originalProj.cx_fund, true)"></span>
                      </div>
                    </div>
                  </div>
                  <!-- 预测结算差异 -->
                  <div class="som-w-fifty som-h-fifty ptp-center-content-item">
                    <div class="ptp-center-content-item-circle">
                      <div class="ptp-circle"></div>
                    </div>
                    <div>
                      <div class="ptp-center-content-title-value">
                        预测结算差异：<span class="ptp-center-content-samll-title"
                                           v-html="formatCost(preCostBalance.total, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城职：<span v-html="formatCost(preCostBalance.cz, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城乡：<span v-html="formatCost(preCostBalance.cx, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城职基金：<span v-html="formatCost(preCostBalance.cz_fund, true)"></span>
                      </div>
                      <div class="ptp-center-content-value">
                        城乡基金：<span v-html="formatCost(preCostBalance.cx_fund, true)"></span>
                      </div>
                    </div>
                  </div>
                  <!-- 基金补偿总比例 -->
                  <div class="som-w-fifty som-h-fifty ptp-center-content-item">
                    <div class="ptp-center-content-item-circle">
                      <div class="ptp-circle"></div>
                    </div>
                    <div>
                      <div class="ptp-center-content-title-value">
                        基金补偿总比例：<span class="ptp-center-content-samll-title">{{ balanceRate.total }}%</span>
                      </div>
                      <div class="ptp-center-content-value">
                        城职基金补偿比:：<span>{{ balanceRate.cz_fund }}%</span>
                      </div>
                      <div class="ptp-center-content-value">
                        城乡基金补偿比：<span>{{ balanceRate.cx_fund }}%</span>
                      </div>
                      <div class="ptp-center-content-value">
                        其他：<span>{{ balanceRate.qt_fund }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="9" class="som-h-one-hundred">
              <drg-echarts :options="tendencyAnalysisOption"/>
            </el-col>
          </el-row>
        </div>
        <div style="height: 58%;width: 100%;position: relative;">
          <div style="height: 100%;width: 100%">
            <drg-table :data="tableData"
                       border
                       tableId="dataTableId"
                       columnEmptyText="-"
                       highlight-current-row
                       :header-cell-style="{'text-align':'center'}"
                       size="mini"
                       @sort-change='sortChange'
                       height="100%"
                       :id="tableId"
                       ref="dataTable"
                       v-loading="tableLoading">
              <drg-table-column
                prop="index"
                type="index"
                align="center"
                label="序号">
              </drg-table-column>

              <drg-table-column
                prop="dept"
                label="科室"
                :show-overflow-tooltip="true">
              </drg-table-column>

              <drg-table-column
                prop="patientId"
                width="110"
                align="center"
                label="病案号">
              </drg-table-column>
              <drg-table-column
                prop="jzid"
                width="110"
                align="center"
                label="就诊id">
              </drg-table-column>
              <drg-table-column
                prop="jsid"
                width="110"
                align="center"
                label="结算id">
              </drg-table-column>
              <drg-table-column
                prop="beforeHospTime"
                width="110"
                align="center"
                label="入院时间">
              </drg-table-column>

              <drg-table-column
                prop="afterHospTime"
                width="110"
                align="center"
                label="出院时间">
              </drg-table-column>

              <el-table-column
                prop="name"
                width="90"
                label="姓名">
              </el-table-column>

              <drg-table-column
                prop="insuredType"
                width="90"
                dicType="INSURANCE_TYPE"
                label="参保类型">
              </drg-table-column>

              <drg-table-column
                prop="dipCodg"
                width="110"
                label="DIP编码"
                :show-overflow-tooltip="true">
              </drg-table-column>

              <drg-table-column
                prop="dipName"
                label="DIP名称"
                :show-overflow-tooltip="true">
              </drg-table-column>

              <drg-table-column
                prop="isUsedAsstList"
                label="辅助目录"
                dicType="AD"
                :show-overflow-tooltip="true">
              </drg-table-column>

              <drg-table-column
                prop="asstListAgeGrp"
                label="年龄段"
                :show-overflow-tooltip="true">
              </drg-table-column>

              <drg-table-column
                prop="asstListDiseSevDeg"
                width="110"
                label="疾病严重程度"
                :show-overflow-tooltip="true">
              </drg-table-column>

              <drg-table-column
                prop="asstListTmorSevDeg"
                width="110"
                label="肿瘤严重程度"
                :show-overflow-tooltip="true">
              </drg-table-column>
              <drg-table-column
                prop="auxiliaryBurn"
                width="110"
                label="烧伤严重程度"
                :show-overflow-tooltip="true">
              </drg-table-column>
              <el-table-column
                prop="stsbFee"
                width="120"
                align="right"
                label="费用区间"
                sortable='custom'>
                <template slot-scope="scope">
                  <span
                    :class="[['2', '1'].includes(scope.row.stsbFee) ? 'som-color-error' : scope.row.stsbFee == '3' ? 'som-color-success' : 'som-color-warning']">
                    {{ $somms.getDictValueByType(scope.row.stsbFee, 'CASE_TYPE') }}
                  </span>
                </template>
              </el-table-column>

              <drg-table-column
                prop="refer_sco"
                width="100"
                align="right"
                label="基准分值" sortable='custom'>
              </drg-table-column>

              <drg-table-column
                prop="adjm_cof"
                width="125"
                align="right"
                label="级别基本系数" sortable='custom'>
              </drg-table-column>

              <drg-table-column
                prop="calculateScore"
                width="100"
                align="right"
                :label="queryForm.feeStas == 1 ? '结算分值' : '预测分值'" sortable='custom'>
              </drg-table-column>

              <drg-table-column
                prop="dominantDiseaseRate"
                width="150"
                align="right"
                label="中医优势病例系数" sortable='custom'>
              </drg-table-column>

              <drg-table-column
                prop="baseDiseaseRate"
                width="125"
                align="right"
                label="基层病种系数" sortable='custom'>
              </drg-table-column>

              <drg-table-column
                prop="youngerDiseaseRate"
                width="125"
                align="right"
                label="年龄病种系数" sortable='custom'>
              </drg-table-column>

              <drg-table-column
                prop="professionalDiseaseRate"
                width="125"
                align="right"
                label="重点学科系数" sortable='custom'>
              </drg-table-column>

              <drg-table-column prop="hospCof" width="125" align="right" label="医院系数"
                                sortable="custom"></drg-table-column>

              <drg-table-column
                prop="addScore"
                width="100"
                align="right"
                label="加成分值" sortable='custom'>
              </drg-table-column>

              <drg-table-column
                prop="totlSco"
                width="120"
                align="right"
                :label="queryForm.feeStas == 1 ? '拨付分值' : '预测总分值'" sortable='custom'>
              </drg-table-column>

              <drg-table-column
                prop="preCost"
                width="100"
                align="right"
                :label="queryForm.feeStas == 1 ? '反馈金额' : '预付金额'" sortable='custom'>
              </drg-table-column>

              <drg-table-column
                prop="inHospCost"
                width="120"
                align="right"
                label="住院费用(元)" sortable='custom'>
              </drg-table-column>
              <drg-table-column
                prop="preHospExamfee"
                width="140"
                align="right"
                label="院前检查费(元)" sortable='custom'>
              </drg-table-column>
              <drg-table-column
                prop="checkTotalFee"
                width="140"
                align="right"
                label="医保总费用(元)" sortable='custom'>
              </drg-table-column>
              <drg-table-column
                prop="sumfee"
                width="140"
                align="right"
                label="结算总费用(元)" sortable='custom'>
              </drg-table-column>


              <drg-table-column
                prop="fbTotalCost"
                width="120"
                align="right"
                label="总费用(反馈)" sortable='custom' v-if="queryForm.feeStas == 1">
              </drg-table-column>

              <el-table-column
                prop="profitAndLoss"
                width="120"
                align="right"
                label="盈亏" sortable='custom'>
                <template slot-scope="scope">
                  <!--                  <span :class="[parseFloat(scope.row.preCost) - parseFloat(scope.row.sumfee) < 0 ? 'som-color-error' : 'som-color-success']">-->
                  <span :class="[scope.row.profitAndLoss < 0 ? 'som-color-error' : 'som-color-success']">
                    {{ scope.row.profitAndLoss }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="费用状态" width="100px" align="center">
                <template slot-scope="scope">
                  <i class="som-icon-error-waring som-icon-big"
                     v-if="queryForm.feeStas == 0 ? parseFloat(scope.row.preCost) - parseFloat(scope.row.sumfee) < 0 : parseFloat(scope.row.preCost) - parseFloat(scope.row.fbTotalCost) < 0"></i>
                  <i class="som-icon-success som-icon-big" v-else></i>
                </template>
              </el-table-column>
              <drg-table-column
                prop="standardFee"
                width="120"
                align="right"
                label="病组均费">
              </drg-table-column>
              <drg-table-column
                prop="standardDays"
                width="120"
                align="right"
                label="平均住院天数">
              </drg-table-column>
              <drg-table-column
                prop="inHosDays"
                width="120"
                align="right"
                label="实际住院天数">
              </drg-table-column>
              <drg-table-column
                prop="fundAmtSum"
                width="120"
                align="right"
                label="原项目基金费用">
              </drg-table-column>
              <drg-table-column
                prop="fundSourceType"
                width="140"
                align="right"
                label="基金费用来源">
              </drg-table-column>
              <drg-table-column
                prop="presonBearCost"
                width="140"
                align="right"
                label="个人负担金额">
              </drg-table-column>
              <drg-table-column
                prop="preFundFee"
                width="140"
                align="right"
                label="DIP预测基金费用">
              </drg-table-column>
              <drg-table-column
                prop="fundRatio"
                width="80"
                align="right"
                label="补偿比">

              </drg-table-column>
              <drg-table-column
                prop="fundRatioType"
                width="120"
                align="right"
                dicType="FUNDRATIO_TYPE"
                label="补偿比提醒">
              </drg-table-column>
            </drg-table>
          </div>
        </div>

        <el-dialog
          title="费用计算"
          ref="dipFeeForm"
          width="30%"
          :visible.sync="priceVisible">
          <el-form :model="dipFeeForm" size="mini" label-width="auto">
            <el-form-item label="期号" prop="ym">
              <el-date-picker
                style="width:70%"
                v-model="dipFeeForm.ym"
                type="month"
                value-format="yyyyMM"
                placeholder="选择期号"
                @change="queryPrice"
              >{{ this.dipFeeForm.ym }}
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="cxPrice" label="城乡测算单价" required style="width:70%">
              <el-input placeholder="城乡测算单价" v-model="dipFeeForm.cxPrice" :disabled="false">
                {{ this.dipFeeForm.cxPrice }}
              </el-input>
            </el-form-item>
            <el-form-item prop="cxPrice" label="城镇测算单价" required style="width:70%">
              <el-input placeholder="城镇测算单价" v-model="dipFeeForm.czPrice" :disabled="false">
                {{ this.dipFeeForm.czPrice }}
              </el-input>
            </el-form-item>
            <el-form-item prop="price" label="测算单价均值" required style="width:70%">
              <el-input placeholder="测算单价均值" v-model="dipFeeForm.price" :disabled="false">
                {{ this.dipFeeForm.price }}
              </el-input>
            </el-form-item>
          </el-form>
          <!--        <el-dialog-->
          <!--          title="修改测算单价"-->
          <!--          :visible.sync="priceVisible"-->
          <!--          width="20%">-->
          <!--          <div style="display: flex;flex-direction: column">-->
          <!--            <div style="margin-top: 1rem">-->
          <!--              城乡测算单价：-->
          <!--              <el-input-number v-model="calculatePriceCx"/>-->
          <!--            </div>-->
          <!--            <div style="margin-top: 1rem">-->
          <!--              城职测算单价：-->
          <!--              <el-input-number v-model="calculatePriceCz"/>-->
          <!--            </div>-->
          <!--            <div style="margin-top: 1rem">-->
          <!--              测算单价均值：-->
          <!--              <el-input-number v-model="calculatePrice"/>-->
          <!--            </div>-->
          <!--          </div>-->
          <template #footer>
          <span slot="footer" class="dialog-footer">
            <el-button @click="priceVisible = false">取 消</el-button>
            <el-button type="primary" @click="saveConfig" v-model="saveEnable">确 定</el-button>
          </span>
          </template>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>

<script>
import {queryMedicalDoctorSelectInput, queryLikeDipGroupByPram, queryDataIsuue} from '@/api/common/drgCommon'
import {queryData, updateDip, queryPrice, updateDipConfigBatch} from '@/api/dataConfig/dipConfig'
import {
  formatRate,
  formatCost,
  formatMoney,
  handleNumber,
  sortChange,
  formatCostNum,
  handleNumberCost
} from '@/utils/common'
import {getList as queryPageData, getListPoint, getMonthData} from '@/api/dipBusiness/dipPreToPredict'
import {exportAllExcel} from '@/api/common/sysCommon'
import echarts from 'echarts'
import {Message} from 'element-ui'
import moment from 'moment'
import {updatePriceAndResetFee} from '../../../api/dataConfig/dipConfig'

export default {
  name: 'predictPay',
  inject: ['reload'],
  data: () => ({

    totalPreHosCost: 0,
    countrysidePreHosCost: 0,
    cityPreHosCost: 0,
    otherPreHosCost: 0,
    queryForm: {
      ym: null,
      deptType: '',
      deptCode: '',
      dipGroup: '',
      doctorType: '',
      dockerCode: '',
      categories: '',
      fundRatioType: '',
      bah: '',
      recordType: '',
      pageNum: 1,
      pageSize: 200,
      cysj: null,
      cy_start_date: this.cy_start_date,
      cy_end_date: this.cy_end_date,
      costSection: '',
      isLoss: '',
      drCodg: '',
      feeStas: '0'
    },
    dipFeeForm: {
      ym: '',
      type: 1,
      cxPrice: '',
      czPrice: '',
      price: ''
    },
    isBoolean: [
      {value: 0, label: '盈利'},
      {value: 1, label: '亏损'}
    ],
    total: null,
    totalPoint: {
      total: 0,
      cx: 0,
      cx_rate: 0,
      cz: 0,
      cz_rate: 0,
      qt: 0,
      qt_rate: 0
    },
    preCost: {
      total: 0,
      cx: 0,
      cx_rate: 0,
      cz: 0,
      cz_rate: 0,
      qt: 0,
      qt_rate: 0,
      cx_fund: 0,
      cz_fund: 0,
      qt_fund: 0,
    },
    originalProj: {
      total: 0,
      cx: 0,
      cz: 0,
      qt: 0,
      preHospExamfee: 0,
      cx_fund: 0,
      cz_fund: 0,
      qt_fund: 0,
    },
    preCostBalance: {
      total: 0,
      cx: 0,
      cz: 0,
      qt: 0,
      cx_fund: 0,
      cz_fund: 0
    },
    fundRatio: {
      total: 0,
      cx: 0,
      cz: 0,
      qt: 0
    },
    balanceRate: {
      total: 0,
      cx: 0,
      cz: 0,
      qt: 0,
      cx_fund: 0,
      cz_fund: 0,
      qt_fund: 0
    },
    dockerNames: [],
    dip_pre_checked: {
      dipPre: {
        index: 1,
        checked: true
      },
      sumfee: {
        index: 2,
        checked: false
      },
      payCount: {
        index: 3,
        checked: false
      },
      mrType: {
        index: 4,
        checked: false
      },
      EXHType: {
        index: 5,
        checked: false
      },
      instabilityType: {
        index: 6,
        checked: false
      },
      nobkType: {
        index: 7,
        checked: false
      },
      nogroupType: {
        index: 8,
        checked: false
      }
    },
    tableData: [],
    tableLoading: false,
    calculateYM: '',
    calculatePriceTotal: 0,
    calculatePriceCz: 0,
    calculatePriceCx: 0,
    calculatePriceQT: 0,
    calculatePrice: 0,
    priceVisible: false,
    dataList: [],
    saveEnable: true,
    totalCostOptions: {},
    totalPointOptions: {},
    tendencyAnalysisOption: {},
    tableId: 'table',
    pickerOptions: {
      shortcuts: [
        {
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }
      ]
    },
    fontSize: getComputedStyle(document.documentElement).getPropertyValue('--chartSize')
  }),
  mounted() {
    this.queryForm.feeStas = String(this.$store.getters.feeStas)

    if (Object.keys(this.$route.query).length > 0) {
      if (this.$route.query.dipCodg) {
        this.queryForm.dipCodg = this.$route.query.dipCodg
      }
      if (this.$route.query.begnDate && this.$route.query.expiDate) {
        this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.queryForm)
      }
      if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
        this.$refs.somForm.jumpTimeChange('in', this.$route.query, this.queryForm)
      }

      if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
        this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.queryForm)
      }
    }
    this.$nextTick(() => {
      // 获取数据查询时间
      this.getDataIsuue()
    })
  },
  methods: {
    formatCostNum,
    handleNumberCost,
    formatMoney,
    formatCost,
    handleNumber,
    formatRate,
    sortChange,
    queryPageData,
    allExcel() {
      this.$somms.exportExcelAll(this.getParams(), this.total, this.$refs.dataTable.$children[0].$children, document.getElementById(this.tableId).children[0].children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DIP支付预测')
    },

    dateChangeCysj(val) {
      if (val) {
        this.queryForm.cy_start_date = val[0]
        this.queryForm.cy_end_date = val[1]
      } else {
        this.queryForm.cy_start_date = null
        this.queryForm.cy_end_date = null
      }
      this.queryListData()
    },
    initOperations() {
      this.fnQuery()
      this.fnQueryDockerNames()
    },
    getData() {
      let params = this.getParams()
      queryData(params).then(data => {
        this.dataList = data.data.list
        for (let item of this.dataList) {
          if (item.key === 'CX_PRICE') {
            this.calculatePriceCx = item.value
          }
          if (item.key === 'CZ_PRICE') {
            this.calculatePriceCz = item.value
          }
          if (item.key === 'PRICE') {
            this.calculatePrice = item.value
          }
        }
      })
    },
    getNewData(val) {
      let params = this.queryForm
      params.cy_start_date = this.queryForm.begnDate
      if (val == 1) {
        params.ym = this.queryForm.begnDate
      } else if (val == 2) {
        params.ym = this.queryForm.inStartTime
      } else if (val == 3) {
        params.ym = this.queryForm.seStartTime
      }
      if (params.ym) {
        params.ym = moment(params.ym).format('yyyyMM')
        this.calculateYM = params.ym
      }
      params.cy_end_date = this.queryForm.expiDate
      params.dept = this.queryForm.deptCode
      params.drgGroup = this.queryForm.drgCodg
      params.bah = this.queryForm.medcasCodg
      queryData(params).then(data => {
        this.dataList = data.data.list
        for (let item of this.dataList) {
          if (item.key === 'CX_PRICE') {
            this.calculatePriceCx = item.value
          }
          if (item.key === 'CZ_PRICE') {
            this.calculatePriceCz = item.value
          }
          if (item.key === 'PRICE') {
            this.calculatePrice = item.value
          }
        }
      })
    },
    getDataIsuue() {
      queryDataIsuue().then(response => {
        this.queryForm.cy_start_date = response.data.cy_start_date
        this.queryForm.cy_end_date = response.data.cy_end_date
        this.queryForm.cysj = [this.queryForm.cy_start_date, this.queryForm.cy_end_date]
        this.queryForm.ym = response.data.cy_start_date

        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.queryForm.cy_start_date = this.$route.query.begnDate
          this.queryForm.cy_end_date = this.$route.query.expiDate
          this.queryForm.cysj = [this.$route.query.begnDate, this.$route.query.expiDate]
          this.queryForm.ym = this.$route.query.begnDate
        }
        if (this.$route.query.dipCodg) {
          this.queryForm.dipGroup = this.$route.query.dipCodg
        }
        if (this.$route.query.deptCode) {
          this.queryForm.deptCode = this.$route.query.deptCode
        }
        if (this.$route.query.drCodg) {
          this.queryForm.drCodg = this.$route.query.drCodg
        }
        if (this.$route.query.isLoss) {
          this.queryForm.isLoss = Number(this.$route.query.isLoss)
        }
        if (this.$route.query.code) {
          this.queryForm.bah = this.$route.query.code
        }
        if (this.$route.query.costSection) {
          this.queryForm.costSection = this.$route.query.costSection
        }
        if (Object.keys(this.$route.query).length > 0) {
          if (this.$route.query.begnDate && this.$route.query.expiDate) {
            this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.queryForm)
          }

          if (this.$route.query.inStartTime && this.$route.query.inEndTime) {
            this.$refs.somForm.jumpTimeChange('in', this.$route.query, this.queryForm)
          }

          if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
            this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.queryForm)
          }
        }

        // 查询数据
        this.initOperations()
        this.getData()
        this.getMonth()
        this.queryPayForecast()
      })
    },
    saveConfig() {
      if (this.saveEnable != null) {
        this.$confirm('是否确认修改' + this.dipFeeForm.ym + '的各项单价，并重新计算患者费用？')
          .then(_ => {
            if (!this.validatePrices()) {
              this.$message({
                message: '请检查输入的参数不为空且可转为有效数字。',
                type: 'warning'
              })
              return // 终止请求
            }
            updatePriceAndResetFee(this.dipFeeForm).then((result) => {
              this.tableData = result.data
              this.priceVisible = false
              this.fnQuery()
              this.$message({
                message: '已完成单价修改与费用计算',
                type: 'success'
              })
              this.resetFeeForm()
            })
          })
          .catch(_ => {
            this.priceVisible = false
          })
      }
    },
    validatePrices() {
      if (!this.dipFeeForm || !this.dipFeeForm.price || !this.dipFeeForm.ym ||
        !this.dipFeeForm.czPrice || !this.dipFeeForm.cxPrice || !this.dipFeeForm.type
      ) {
        return false
      }
      const prices = [this.dipFeeForm.cxPrice, this.dipFeeForm.czPrice, this.dipFeeForm.price]
      const valid = prices.every(price => {
        const num = parseFloat(price)
        return !isNaN(num) && Number.isFinite(num) && !isNaN(num.toFixed(2)) // 先确保是有效数字，然后转为两位小数
      })
      if (valid) {
        this.dipFeeForm.cxPrice = parseFloat(this.dipFeeForm.cxPrice).toFixed(2)
        this.dipFeeForm.czPrice = parseFloat(this.dipFeeForm.czPrice).toFixed(2)
        this.dipFeeForm.price = parseFloat(this.dipFeeForm.price).toFixed(2)
      }
      return valid // 返回是否有效的结果
    },
    resetFeeForm() {
      this.dipFeeForm = {
        ym: '',
        type: 1,
        cxPrice: '',
        czPrice: '',
        price: ''
      }
    },
    fnQuery() {
      this.dip_pre_checked.dipPre.checked = true
      this.dip_pre_checked.sumfee.checked = false
      this.dip_pre_checked.payCount.checked = false
      this.dip_pre_checked.mrType.checked = false
      this.getMonth()
      this.queryListData()
      this.queryPayForecast(1)
    },
    queryListData() {
      this.tableLoading = true
      let params = this.getParams()
      this.getMonth()
      if (params.ym) {
        params.ym = moment(params.ym).format('yyyy-MM')
      }

      queryPageData(params).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.tableLoading = false
        this.getNewData(this.queryForm.inHosFlag)
      })
    },
    queryPayForecast(index) {
      let params = this.getParams()
      this.getMonth()
      if (params.ym) {
        params.ym = moment(params.ym).format('yyyy-MM')
      }
      getListPoint(params).then(res => {
        let data = res.data
        // 点数及占比
        // 所有病例
        if (index == 1) {
          this.totalPoint.cz = data.cityPoint.point.toFixed(2)
          this.totalPoint.cx = data.countrysidePoint.point.toFixed(2)
          this.totalPoint.qt = data.QTPointPoint.point.toFixed(2)
          this.totalPoint.total = data.balancePoint.point.toFixed(2)
          if (data.countrysidePoint.point == 0) {
            this.totalPoint.cx_rate = Number(0).toFixed(2)
          } else {
            this.totalPoint.cx_rate = ((this.totalPoint.cx / this.totalPoint.total) * 100).toFixed(2)
          }
          if (data.cityPoint.point == 0) {
            this.totalPoint.cz_rate = Number(0).toFixed(2)
          } else {
            this.totalPoint.cz_rate = ((this.totalPoint.cz / this.totalPoint.total) * 100).toFixed(2)
          }
          if (data.QTPointPoint.point == 0) {
            this.totalPoint.qt_rate = Number(0).toFixed(2)
          } else {
            this.totalPoint.qt_rate = ((this.totalPoint.qt / this.totalPoint.total) * 100).toFixed(2)
          }
        } else {
          // 正常病例
          if (index == 2) {
            if (data.cityPoint.ncpoint == 0) {
              this.totalPoint.cz = Number(0).toFixed(2)
            } else {
              this.totalPoint.cz = parseFloat(data.cityPoint.ncpoint).toFixed(2)
            }
            if (data.countrysidePoint.ncpoint == 0) {
              this.totalPoint.cx = Number(0).toFixed(2)
            } else {
              this.totalPoint.cx = parseFloat(data.countrysidePoint.ncpoint).toFixed(2)
            }
            if (data.QTPointPoint.ncpoint == 0) {
              this.totalPoint.qt = Number(0).toFixed(2)
            } else {
              this.totalPoint.qt = parseFloat(data.QTPointPoint.ncpoint).toFixed(2)
            }
          }
          // 超高病例
          if (index == 3) {
            if (data.cityPoint.upPoint == 0) {
              this.totalPoint.cz = Number(0).toFixed(2)
            } else {
              this.totalPoint.cz = parseFloat(data.cityPoint.upPoint).toFixed(2)
            }
            if (data.countrysidePoint.upPoint == 0) {
              this.totalPoint.cx = Number(0).toFixed(2)
            } else {
              this.totalPoint.cx = parseFloat(data.countrysidePoint.upPoint).toFixed(2)
            }
            if (data.QTPointPoint.upPoint == 0) {
              this.totalPoint.qt = Number(0).toFixed(2)
            } else {
              this.totalPoint.qt = parseFloat(data.QTPointPoint.upPoint).toFixed(2)
            }
          }
          // 超低病例
          if (index == 4) {
            if (data.cityPoint.lowPoint == 0) {
              this.totalPoint.cz = Number(0).toFixed(2)
            } else {
              this.totalPoint.cz = parseFloat(data.cityPoint.lowPoint).toFixed(2)
            }
            if (data.countrysidePoint.lowPoint == 0) {
              this.totalPoint.cx = Number(0).toFixed(2)
            } else {
              this.totalPoint.cx = parseFloat(data.countrysidePoint.lowPoint).toFixed(2)
            }
            if (data.QTPointPoint.lowPoint == 0) {
              this.totalPoint.qt = Number(0).toFixed(2)
            } else {
              this.totalPoint.qt = parseFloat(data.QTPointPoint.lowPoint).toFixed(2)
            }
          }
          // 极高极长
          if (index == 5) {
            if (data.cityPoint.exhPoint == 0) {
              this.totalPoint.cz = Number(0).toFixed(2)
            } else {
              this.totalPoint.cz = parseFloat(data.cityPoint.exhPoint).toFixed(2)
            }
            if (data.countrysidePoint.exhPoint == 0) {
              this.totalPoint.cx = Number(0).toFixed(2)
            } else {
              this.totalPoint.cx = parseFloat(data.countrysidePoint.exhPoint).toFixed(2)
            }
            if (data.QTPointPoint.exhPoint == 0) {
              this.totalPoint.qt = Number(0).toFixed(2)
            } else {
              this.totalPoint.qt = parseFloat(data.QTPointPoint.exhPoint).toFixed(2)
            }
          }
          // 非稳定
          if (index == 6) {
            if (data.cityPoint.instabilityPoint == 0) {
              this.totalPoint.cz = Number(0).toFixed(2)
            } else {
              this.totalPoint.cz = parseFloat(data.cityPoint.instabilityPoint).toFixed(2)
            }
            if (data.countrysidePoint.instabilityPoint == 0) {
              this.totalPoint.cx = Number(0).toFixed(2)
            } else {
              this.totalPoint.cx = parseFloat(data.countrysidePoint.instabilityPoint).toFixed(2)
            }
            if (data.QTPointPoint.instabilityPoint == 0) {
              this.totalPoint.qt = Number(0).toFixed(2)
            } else {
              this.totalPoint.qt = parseFloat(data.QTPointPoint.instabilityPoint).toFixed(2)
            }
          }
          // 无标杆
          if (index == 7) {
            if (data.cityPoint.nobkPoint == 0) {
              this.totalPoint.cz = Number(0).toFixed(2)
            } else {
              this.totalPoint.cz = parseFloat(data.cityPoint.nobkPoint).toFixed(2)
            }
            if (data.countrysidePoint.nobkPoint == 0) {
              this.totalPoint.cx = Number(0).toFixed(2)
            } else {
              this.totalPoint.cx = parseFloat(data.countrysidePoint.nobkPoint).toFixed(2)
            }
            if (data.QTPointPoint.nobkPoint == 0) {
              this.totalPoint.qt = Number(0).toFixed(2)
            } else {
              this.totalPoint.qt = parseFloat(data.QTPointPoint.nobkPoint).toFixed(2)
            }
          }
          // 未入组
          if (index == 8) {
            if (data.cityPoint.nogroupPoint == 0) {
              this.totalPoint.cz = Number(0).toFixed(2)
            } else {
              this.totalPoint.cz = parseFloat(data.cityPoint.nogroupPoint).toFixed(2)
            }
            if (data.countrysidePoint.nogroupPoint == 0) {
              this.totalPoint.cx = Number(0).toFixed(2)
            } else {
              this.totalPoint.cx = parseFloat(data.countrysidePoint.nogroupPoint).toFixed(2)
            }
            if (data.QTPointPoint.nogroupPoint == 0) {
              this.totalPoint.qt = Number(0).toFixed(2)
            } else {
              this.totalPoint.qt = parseFloat(data.QTPointPoint.nogroupPoint).toFixed(2)
            }
          }
          this.totalPoint.total = (parseFloat(this.totalPoint.cz) + parseFloat(this.totalPoint.cx) + parseFloat(this.totalPoint.qt)).toFixed(2)
          if (this.totalPoint.cx == 0) {
            this.totalPoint.cx_rate = Number(0).toFixed(2)
          } else {
            this.totalPoint.cx_rate = ((this.totalPoint.cx / this.totalPoint.total) * 100).toFixed(2)
          }
          if (this.totalPoint.cz == 0) {
            this.totalPoint.cz_rate = Number(0).toFixed(2)
          } else {
            this.totalPoint.cz_rate = ((this.totalPoint.cz / this.totalPoint.total) * 100).toFixed(2)
          }
          if (this.totalPoint.qt == 0) {
            this.totalPoint.qt_rate = Number(0).toFixed(2)
          } else {
            this.totalPoint.qt_rate = ((this.totalPoint.qt / this.totalPoint.total) * 100).toFixed(2)
          }
        }
        this.drawTotalPointBar()
        // 预测金额
        if (index == 1) {
          if (data.cityPoint.predictCost == 0) {
            this.preCost.cz = 0
          } else {
            this.preCost.cz = data.cityPoint.predictCost
          }
          if (data.countrysidePoint.predictCost == 0) {
            this.preCost.cx = 0
          } else {
            this.preCost.cx = data.countrysidePoint.predictCost
          }
          if (data.QTPointPoint.predictCost == 0) {
            this.preCost.qt = 0
          } else {
            this.preCost.qt = data.QTPointPoint.predictCost
          }
          this.preCost.total = (this.preCost.cz + this.preCost.cx + this.preCost.qt).toFixed(2)
          if (this.preCost.cz == 0) {
            this.preCost.cz_rate = 0
          } else {
            this.preCost.cz_rate = ((this.preCost.cz / this.preCost.total) * 100).toFixed(2)
          }
          if (this.preCost.cx == 0) {
            this.preCost.cx_rate = 0
          } else {
            this.preCost.cx_rate = ((this.preCost.cx / this.preCost.total) * 100).toFixed(2)
          }


          if (data.cityPoint.preFund == 0) {
            this.preCost.cz_fund = 0
          } else {
            this.preCost.cz_fund = data.cityPoint.preFund
          }
          if (data.countrysidePoint.preFund == 0) {
            this.preCost.cx_fund = 0
          } else {
            this.preCost.cx_fund = data.countrysidePoint.preFund
          }
          if (data.QTPointPoint.preFund == 0) {
            this.preCost.qt_fund = 0
          } else {
            this.preCost.qt_fund = data.QTPointPoint.preFund
          }

        } else {
          if (index == 2) {
            if (data.cityPoint.ncPreFund == 0) {
              this.preCost.cz_fund = 0
            } else {
              this.preCost.cz_fund = data.cityPoint.ncPreFund
            }
            if (data.countrysidePoint.ncPreFund == 0) {
              this.preCost.cx_fund = 0
            } else {
              this.preCost.cx_fund = data.countrysidePoint.ncPreFund
            }
            if (data.QTPointPoint.ncPreFund == 0) {
              this.preCost.qt_fund = 0
            } else {
              this.preCost.qt_fund = data.QTPointPoint.ncPreFund
            }
            if (data.cityPoint.ncpoint == 0) {
              this.preCost.cz = 0
            } else {
              this.preCost.cz = (data.cityPoint.ncpoint * data.cityPoint.czPrice)
            }
            if (data.countrysidePoint.ncpoint == 0) {
              this.preCost.cx = 0
            } else {
              this.preCost.cx = (data.countrysidePoint.ncpoint * data.countrysidePoint.cxPrice)
            }
            if (data.QTPointPoint.ncpoint == 0) {
              this.preCost.qt = 0
            } else {
              this.preCost.qt = (data.QTPointPoint.ncpoint * data.QTPointPoint.price)
            }
          }
          if (index == 3) {
            if (data.cityPoint.upPreFund == 0) {
              this.preCost.cz_fund = 0
            } else {
              this.preCost.cz_fund = data.cityPoint.upPreFund
            }
            if (data.countrysidePoint.upPreFund == 0) {
              this.preCost.cx_fund = 0
            } else {
              this.preCost.cx_fund = data.countrysidePoint.upPreFund
            }
            if (data.QTPointPoint.upPreFund == 0) {
              this.preCost.qt_fund = 0
            } else {
              this.preCost.qt_fund = data.QTPointPoint.upPreFund
            }
            if (data.cityPoint.upPoint == 0) {
              this.preCost.cz = 0
            } else {
              this.preCost.cz = (data.cityPoint.upPoint * data.cityPoint.czPrice)
            }
            if (data.countrysidePoint.upPoint == 0) {
              this.preCost.cx = 0
            } else {
              this.preCost.cx = (data.countrysidePoint.upPoint * data.countrysidePoint.cxPrice)
            }
            if (data.QTPointPoint.upPoint == 0) {
              this.preCost.qt = 0
            } else {
              this.preCost.qt = (data.QTPointPoint.upPoint * data.QTPointPoint.price)
            }
          }
          if (index == 4) {
            if (data.cityPoint.lowPreFund == 0) {
              this.preCost.cz_fund = 0
            } else {
              this.preCost.cz_fund = data.cityPoint.lowPreFund
            }
            if (data.countrysidePoint.lowPreFund == 0) {
              this.preCost.cx_fund = 0
            } else {
              this.preCost.cx_fund = data.countrysidePoint.lowPreFund
            }
            if (data.QTPointPoint.lowPreFund == 0) {
              this.preCost.qt_fund = 0
            } else {
              this.preCost.qt_fund = data.QTPointPoint.lowPreFund
            }
            if (data.cityPoint.lowPoint == 0) {
              this.preCost.cz = 0
            } else {
              this.preCost.cz = (data.cityPoint.lowPoint * data.cityPoint.czPrice)
            }
            if (data.countrysidePoint.lowPoint == 0) {
              this.preCost.cx = 0
            } else {
              this.preCost.cx = (data.countrysidePoint.lowPoint * data.countrysidePoint.cxPrice)
            }
            if (data.QTPointPoint.lowPoint == 0) {
              this.preCost.qt = 0
            } else {
              this.preCost.qt = (data.QTPointPoint.lowPoint * data.QTPointPoint.price)
            }
          }
          if (index == 5) {
            if (data.cityPoint.exhPreFund == 0) {
              this.preCost.cz_fund = 0
            } else {
              this.preCost.cz_fund = data.cityPoint.exhPreFund
            }
            if (data.countrysidePoint.exhPreFund == 0) {
              this.preCost.cx_fund = 0
            } else {
              this.preCost.cx_fund = data.countrysidePoint.exhPreFund
            }
            if (data.QTPointPoint.exhPreFund == 0) {
              this.preCost.qt_fund = 0
            } else {
              this.preCost.qt_fund = data.QTPointPoint.exhPreFund
            }
            if (data.cityPoint.exhPoint == 0) {
              this.preCost.cz = 0
            } else {
              this.preCost.cz = (data.cityPoint.exhPoint * data.cityPoint.czPrice)
            }
            if (data.countrysidePoint.exhPoint == 0) {
              this.preCost.cx = 0
            } else {
              this.preCost.cx = (data.countrysidePoint.exhPoint * data.countrysidePoint.cxPrice)
            }
            if (data.QTPointPoint.exhPoint == 0) {
              this.preCost.qt = 0
            } else {
              this.preCost.qt = (data.QTPointPoint.exhPoint * data.QTPointPoint.price)
            }
          }
          if (index == 6) {
            if (data.cityPoint.instabilityPreFund == 0) {
              this.preCost.cz_fund = 0
            } else {
              this.preCost.cz_fund = data.cityPoint.instabilityPreFund
            }
            if (data.countrysidePoint.instabilityPreFund == 0) {
              this.preCost.cx_fund = 0
            } else {
              this.preCost.cx_fund = data.countrysidePoint.instabilityPreFund
            }
            if (data.QTPointPoint.instabilityPreFund == 0) {
              this.preCost.qt_fund = 0
            } else {
              this.preCost.qt_fund = data.QTPointPoint.instabilityPreFund
            }
            if (data.cityPoint.instabilityPoint == 0) {
              this.preCost.cz = 0
            } else {
              this.preCost.cz = (data.cityPoint.instabilityPoint * data.cityPoint.czPrice)
            }
            if (data.countrysidePoint.instabilityPoint == 0) {
              this.preCost.cx = 0
            } else {
              this.preCost.cx = (data.countrysidePoint.instabilityPoint * data.countrysidePoint.cxPrice)
            }
            if (data.QTPointPoint.instabilityPoint == 0) {
              this.preCost.qt = 0
            } else {
              this.preCost.qt = (data.QTPointPoint.instabilityPoint * data.QTPointPoint.price)
            }
          }
          if (index == 7) {
            if (data.cityPoint.nobkPreFund == 0) {
              this.preCost.cz_fund = 0
            } else {
              this.preCost.cz_fund = data.cityPoint.nobkPreFund
            }
            if (data.countrysidePoint.nobkPreFund == 0) {
              this.preCost.cx_fund = 0
            } else {
              this.preCost.cx_fund = data.countrysidePoint.nobkPreFund
            }
            if (data.QTPointPoint.nobkPreFund == 0) {
              this.preCost.qt_fund = 0
            } else {
              this.preCost.qt_fund = data.QTPointPoint.nobkPreFund
            }
            if (data.cityPoint.nobkPoint == 0) {
              this.preCost.cz = 0
            } else {
              this.preCost.cz = (data.cityPoint.nobkPoint * data.cityPoint.czPrice)
            }
            if (data.countrysidePoint.nobkPoint == 0) {
              this.preCost.cx = 0
            } else {
              this.preCost.cx = (data.countrysidePoint.nobkPoint * data.countrysidePoint.cxPrice)
            }
            if (data.QTPointPoint.nobkPoint == 0) {
              this.preCost.qt = 0
            } else {
              this.preCost.qt = (data.QTPointPoint.nobkPoint * data.QTPointPoint.price)
            }
          }
          if (index == 8) {
            if (data.cityPoint.nogroupPreFund == 0) {
              this.preCost.cz_fund = 0
            } else {
              this.preCost.cz_fund = data.cityPoint.nogroupPreFund
            }
            if (data.countrysidePoint.nogroupPreFund == 0) {
              this.preCost.cx_fund = 0
            } else {
              this.preCost.cx_fund = data.countrysidePoint.nogroupPreFund
            }
            if (data.QTPointPoint.nogroupPreFund == 0) {
              this.preCost.qt_fund = 0
            } else {
              this.preCost.qt_fund = data.QTPointPoint.nogroupPreFund
            }
            if (data.cityPoint.nogroupPoint == 0) {
              this.preCost.cz = 0
            } else {
              this.preCost.cz = (data.cityPoint.nogroupPoint * data.cityPoint.czPrice)
            }
            if (data.countrysidePoint.nogroupPoint == 0) {
              this.preCost.cx = 0
            } else {
              this.preCost.cx = (data.countrysidePoint.nogroupPoint * data.countrysidePoint.cxPrice)
            }
            if (data.QTPointPoint.nogroupPoint == 0) {
              this.preCost.qt = 0
            } else {
              this.preCost.qt = (data.QTPointPoint.nogroupPoint * data.QTPointPoint.price)
            }
          }
          this.preCost.total = (this.preCost.cz + this.preCost.cx + this.preCost.qt).toFixed(2)
          if (this.preCost.cz == 0) {
            this.preCost.cz_rate = 0
          } else {
            this.preCost.cz_rate = ((this.preCost.cz / this.preCost.total) * 100).toFixed(2)
          }
          if (this.preCost.cx == 0) {
            this.preCost.cx_rate = 0
          } else {
            this.preCost.cx_rate = ((this.preCost.cx / this.preCost.total) * 100).toFixed(2)
          }
          if (this.preCost.qt == 0) {
            this.preCost.qt_rate = 0
          } else {
            this.preCost.qt_rate = ((this.preCost.qt / this.preCost.total) * 100).toFixed(2)
          }
        }
        this.drawTotalCostBar()
        // 原项目金额
        if (index == 1) {
          if (data.cityPoint.fundAmt == 0) {
            this.originalProj.cz_fund = 0
          } else {
            this.originalProj.cz_fund = data.cityPoint.fundAmt
          }
          if (data.countrysidePoint.fundAmt == 0) {
            this.originalProj.cx_fund = 0
          } else {
            this.originalProj.cx_fund = data.countrysidePoint.fundAmt
          }
          if (data.QTPointPoint.fundAmt == 0) {
            this.originalProj.qt_fund = 0
          } else {
            this.originalProj.qt_fund = data.QTPointPoint.fundAmt
          }
          this.originalProj.cz = Number(data.cityPoint.sumfee)
          this.originalProj.cx = Number(data.countrysidePoint.sumfee)
          this.originalProj.qt = Number(data.QTPointPoint.sumfee)
          this.originalProj.total = Number(data.balancePoint.sumfee)
        } else {
          if (index == 2) {
            if (data.cityPoint.ncFundAmt == 0) {
              this.originalProj.cz_fund = 0
            } else {
              this.originalProj.cz_fund = data.cityPoint.ncFundAmt
            }
            if (data.countrysidePoint.ncFundAmt == 0) {
              this.originalProj.cx_fund = 0
            } else {
              this.originalProj.cx_fund = data.countrysidePoint.ncFundAmt
            }
            if (data.QTPointPoint.ncFundAmt == 0) {
              this.originalProj.qt_fund = 0
            } else {
              this.originalProj.qt_fund = data.QTPointPoint.ncFundAmt
            }
            this.originalProj.cz = Number(data.cityPoint.nccost)
            this.originalProj.cx = Number(data.countrysidePoint.nccost)
            this.originalProj.qt = Number(data.QTPointPoint.nccost)
          }
          if (index == 3) {
            if (data.cityPoint.upFundAmt == 0) {
              this.originalProj.cz_fund = 0
            } else {
              this.originalProj.cz_fund = data.cityPoint.upFundAmt
            }
            if (data.countrysidePoint.upFundAmt == 0) {
              this.originalProj.cx_fund = 0
            } else {
              this.originalProj.cx_fund = data.countrysidePoint.upFundAmt
            }
            if (data.QTPointPoint.upFundAmt == 0) {
              this.originalProj.qt_fund = 0
            } else {
              this.originalProj.qt_fund = data.QTPointPoint.upFundAmt
            }
            this.originalProj.cz = Number(data.cityPoint.upCost)
            this.originalProj.cx = Number(data.countrysidePoint.upCost)
            this.originalProj.qt = Number(data.QTPointPoint.upCost)
          }
          if (index == 4) {
            if (data.cityPoint.lowFundAmt == 0) {
              this.originalProj.cz_fund = 0
            } else {
              this.originalProj.cz_fund = data.cityPoint.lowFundAmt
            }
            if (data.countrysidePoint.lowFundAmt == 0) {
              this.originalProj.cx_fund = 0
            } else {
              this.originalProj.cx_fund = data.countrysidePoint.lowFundAmt
            }
            if (data.QTPointPoint.lowFundAmt == 0) {
              this.originalProj.qt_fund = 0
            } else {
              this.originalProj.qt_fund = data.QTPointPoint.lowFundAmt
            }
            this.originalProj.cz = Number(data.cityPoint.lowCost)
            this.originalProj.cx = Number(data.countrysidePoint.lowCost)
            this.originalProj.qt = Number(data.QTPointPoint.lowCost)
          }
          if (index == 5) {
            if (data.cityPoint.exhFundAmt == 0) {
              this.originalProj.cz_fund = 0
            } else {
              this.originalProj.cz_fund = data.cityPoint.exhFundAmt
            }
            if (data.countrysidePoint.exhFundAmt == 0) {
              this.originalProj.cx_fund = 0
            } else {
              this.originalProj.cx_fund = data.countrysidePoint.exhFundAmt
            }
            if (data.QTPointPoint.exhFundAmt == 0) {
              this.originalProj.qt_fund = 0
            } else {
              this.originalProj.qt_fund = data.QTPointPoint.exhFundAmt
            }
            this.originalProj.cz = Number(data.cityPoint.exhCost)
            this.originalProj.cx = Number(data.countrysidePoint.exhCost)
            this.originalProj.qt = Number(data.QTPointPoint.exhCost)
          }
          if (index == 6) {
            if (data.cityPoint.instabilityFundAmt == 0) {
              this.originalProj.cz_fund = 0
            } else {
              this.originalProj.cz_fund = data.cityPoint.instabilityFundAmt
            }
            if (data.countrysidePoint.instabilityFundAmt == 0) {
              this.originalProj.cx_fund = 0
            } else {
              this.originalProj.cx_fund = data.countrysidePoint.instabilityFundAmt
            }
            if (data.QTPointPoint.instabilityFundAmt == 0) {
              this.originalProj.qt_fund = 0
            } else {
              this.originalProj.qt_fund = data.QTPointPoint.instabilityFundAmt
            }
            this.originalProj.cz = Number(data.cityPoint.instabilityCost)
            this.originalProj.cx = Number(data.countrysidePoint.instabilityCost)
            this.originalProj.qt = Number(data.QTPointPoint.instabilityCost)
          }
          if (index == 7) {
            if (data.cityPoint.nobkFundAmt == 0) {
              this.originalProj.cz_fund = 0
            } else {
              this.originalProj.cz_fund = data.cityPoint.nobkFundAmt
            }
            if (data.countrysidePoint.nobkFundAmt == 0) {
              this.originalProj.cx_fund = 0
            } else {
              this.originalProj.cx_fund = data.countrysidePoint.nobkFundAmt
            }
            if (data.QTPointPoint.nobkFundAmt == 0) {
              this.originalProj.qt_fund = 0
            } else {
              this.originalProj.qt_fund = data.QTPointPoint.nobkFundAmt
            }
            this.originalProj.cz = Number(data.cityPoint.nobkCost)
            this.originalProj.cx = Number(data.countrysidePoint.nobkCost)
            this.originalProj.qt = Number(data.QTPointPoint.nobkCost)
          }
          if (index == 8) {
            if (data.cityPoint.nogroupFundAmt == 0) {
              this.originalProj.cz_fund = 0
            } else {
              this.originalProj.cz_fund = data.cityPoint.nogroupFundAmt
            }
            if (data.countrysidePoint.nogroupFundAmt == 0) {
              this.originalProj.cx_fund = 0
            } else {
              this.originalProj.cx_fund = data.countrysidePoint.nogroupFundAmt
            }
            if (data.QTPointPoint.nogroupFundAmt == 0) {
              this.originalProj.qt_fund = 0
            } else {
              this.originalProj.qt_fund = data.QTPointPoint.nogroupFundAmt
            }
            this.originalProj.cz = Number(data.cityPoint.nogroupCost)
            this.originalProj.cx = Number(data.countrysidePoint.nogroupCost)
            this.originalProj.qt = Number(data.QTPointPoint.nogroupCost)
          }
          this.originalProj.total = Number(this.originalProj.cz) + Number(this.originalProj.cx) + Number(this.originalProj.qt)
        }
        // let czPre = data.cityPoint.preHospExamfee ? data.cityPoint.preHospExamfee : 0
        // let cxPre = data.countrysidePoint.preHospExamfee ? data.countrysidePoint.preHospExamfee : 0
        // let qtPre = data.QTPointPoint.preHospExamfee ? data.QTPointPoint.preHospExamfee : 0
        // 差值
        this.preCostBalance.cz = this.preCost.cz - this.originalProj.cz
        this.preCostBalance.cx = this.preCost.cx - this.originalProj.cx
        this.preCostBalance.qt = this.preCost.qt - this.originalProj.qt
        this.preCostBalance.total = this.preCost.total - this.originalProj.total
        this.preCostBalance.cx_fund = this.preCost.cx_fund - this.originalProj.cx_fund
        this.preCostBalance.cz_fund = this.preCost.cz_fund - this.originalProj.cz_fund

        // 收入差异比额
        this.obtainFund()
      })
    },
    // 收入差异比额
    obtainBalance() {
      if (this.preCost.cz && this.originalProj.cz) {
        if (this.preCost.cz == 0) {
          this.balanceRate.cz = -100
        } else {
          this.balanceRate.cz = (((this.preCost.cz - this.originalProj.cz) / this.originalProj.cz) * 100).toFixed(2)
        }
      } else {
        this.balanceRate.cz = 0
      }
      if (this.preCost.cx && this.originalProj.cx) {
        if (this.preCost.cx == 0) {
          this.balanceRate.cx = -100
        } else {
          this.balanceRate.cx = (((this.preCost.cx - this.originalProj.cx) / this.originalProj.cx) * 100).toFixed(2)
        }
      } else {
        this.balanceRate.cx = 0
      }
      if (this.preCost.qt && this.originalProj.qt) {
        if (this.preCost.qt == 0) {
          this.balanceRate.qt = -100
        } else {
          this.balanceRate.qt = (((this.preCost.qt - this.originalProj.qt) / this.originalProj.qt) * 100).toFixed(2)
        }
      } else {
        this.balanceRate.qt = 0
      }
      if (this.preCost.total && this.originalProj.total) {
        if (this.preCost.cz == 0) {
          this.balanceRate.total = -100
        } else {
          this.balanceRate.total = (((this.preCost.total - this.originalProj.total) / this.originalProj.total) * 100).toFixed(2)
        }
      } else {
        this.balanceRate.total = 0
      }
    },
    //获取基金比
    obtainFund() {
      if (this.preCost.cz_fund && this.originalProj.cz_fund) {
        if (this.preCost.cz_fund == 0 || this.originalProj.cz_fund == 0) {
          this.balanceRate.cz_fund = 0
        } else {
          this.balanceRate.cz_fund = ((this.originalProj.cz_fund / this.preCost.cz_fund) * 100).toFixed(2)
        }
      } else {
        this.balanceRate.cz_fund = 0
      }

      if (this.preCost.cx_fund && this.originalProj.cx_fund) {
        if (this.preCost.cx_fund == 0 || this.originalProj.cx_fund == 0) {
          this.balanceRate.cx_fund = 0
        } else {
          this.balanceRate.cx_fund = ((this.originalProj.cx_fund / this.preCost.cx_fund) * 100).toFixed(2)
        }
      } else {
        this.balanceRate.cx_fund = 0
      }

      if (this.preCost.qt_fund && this.originalProj.qt_fund) {
        if (this.preCost.qt_fund == 0 || this.originalProj.qt_fund == 0) {
          this.balanceRate.qt_fund = 0
        } else {
          this.balanceRate.qt_fund = ((this.originalProj.qt_fund / this.preCost.qt_fund) * 100).toFixed(2)
        }
      } else {
        this.balanceRate.qt_fund = 0
      }

      if (this.preCost.cz_fund == 0 && this.preCost.cz_fund == 0 && this.preCost.qt_fund == 0 &&
        this.originalProj.cz_fund == 0 && this.originalProj.cz_fund == 0 && this.originalProj.qt_fund == 0
      ) {
        this.balanceRate.total = 0
      } else {
        var preCostFund = this.preCost.cz_fund + this.preCost.cx_fund + this.preCost.qt_fund
        var originalProjFund = this.originalProj.cz_fund + this.originalProj.cx_fund + this.originalProj.qt_fund
        if (preCostFund == 0 || originalProjFund == 0) {
          this.balanceRate.total = 0
        } else {
          this.balanceRate.total = ((originalProjFund / preCostFund * 100).toFixed(2))
        }
      }
    },
    // DIP组选中
    fnDipGroupSelect(item) {
      this.queryForm.dipGroup = item.dipCodg
    },
    fnModifyPrice() {
      this.dipFeeForm.type = 1
      this.setPreviousMonth()
      queryPrice(this.dipFeeForm).then((result) => {
        this.dipFeeForm.cxPrice = result.data.cxPrice
        this.dipFeeForm.czPrice = result.data.czPrice
        this.dipFeeForm.price = result.data.price
      })
      this.priceVisible = true
      // TODO 修改单价
    },
    setPreviousMonth() {
      // 获取当前日期
      const currentDate = new Date()

      // 将月份减去1，获取上一个月
      currentDate.setMonth(currentDate.getMonth() - 1)

      // 格式化为 YYYYMM 的字符串
      const year = currentDate.getFullYear()
      const month = String(currentDate.getMonth() + 1).padStart(2, '0') // 月份从0开始，所以加1，并确保是两位数字
      this.dipFeeForm.ym = `${year}${month}`
    },
    // 月份改变 对应的数据也要发生改变
    queryPrice(value) {
      this.dipFeeForm.type = 1
      this.dipFeeForm.ym = value
      queryPrice(this.dipFeeForm).then((result) => {
        if (result.data != null) {
          this.dipFeeForm.cxPrice = result.data.cxPrice
          this.dipFeeForm.czPrice = result.data.czPrice
          this.dipFeeForm.price = result.data.price
        } else {
          this.dipFeeForm.cxPrice = ''
          this.dipFeeForm.czPrice = ''
          this.dipFeeForm.price = ''
        }
        this.tableLoading = false
      })
    },
    fnSure() {
      this.priceVisible = false
      Message({
        message: '修改成功',
        type: 'success'
      })
    },
    changeDept(deptType) {
      this.queryForm.deptType = deptType
      this.getDataIsuue()
    },
    fnChangeDipChecked(index) {
      Object.keys(this.dip_pre_checked).forEach(key => {
        if (this.dip_pre_checked[key].index == index) {
          this.dip_pre_checked[key].checked = true
        } else {
          this.dip_pre_checked[key].checked = false
        }
      })
      this.queryPayForecast(index)
    },
    drawTotalCostBar() {
      // 基于准备好的dom，初始化echarts实例
      // let sumfee = echarts.init(document.getElementById('totalCost'));
      let _this = this
      let option = {
        // color: ['#81C1DC', '#3488AD', '#00557C'],//设置颜色
        color: ['#83ced0', '#f2855c', '#67c23a'],
        // 顶部显示
        legend: [
          {
            left: '-1.2%',
            bottom: '0%',
            itemHeight: 11,
            itemWidth: 20,
            formatter: function (name) {
              return name + '：' + (_this.handleNumber(_this.preCost.cx) ? _this.handleNumber(_this.preCost.cx) : 0)
              // return name + "：" + _this.$somms.ifNullZero(_this.preCost.cx.toFixed(2))
            },
            textStyle: {
              fontSize: _this.fontSize
            },
            data: ['城乡']
          },
          {
            left: '35%',
            bottom: '0%',
            itemHeight: 11,
            itemWidth: 20,
            formatter: function (name) {
              return name + '：' + (_this.handleNumber(_this.preCost.qt) ? _this.handleNumber(_this.preCost.qt) : 0)
              // return name + "：" + _this.$somms.ifNullZero(_this.preCost.qt.toFixed(2))
            },
            textStyle: {
              fontSize: _this.fontSize
            },
            data: ['其他']
          },
          {
            right: '-5%',
            bottom: '0%',
            itemHeight: 11,
            itemWidth: 20,
            formatter: function (name) {
              return name + '：' + (_this.handleNumber(_this.preCost.cz) ? _this.handleNumber(_this.preCost.cz) : 0)
              // return name + "：" +  _this.$somms.ifNullZero(_this.preCost.cz.toFixed(2))
            },
            textStyle: {
              fontSize: _this.fontSize
            },
            data: ['城职']
          }
        ],
        // 设置下方图标的位置
        grid: {
          left: '0%',
          right: '0%',
          top: '2%',
          bottom: '30%',
          containLabel: true
        },
        // 设置X轴的参数
        xAxis: [
          {
            type: 'value',
            max: 100, // 设置最大值是多少
            splitNumber: 5, // 设置分几段显示
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            show: true
          }
        ],
        // 设置Y轴的参数
        yAxis: {
          splitLine: {
            show: false
          },
          axisLine: { // y轴
            show: false
          },
          axisTick: {
            show: false
          },
          type: 'category'
        },
        // 设置每个item的参数
        series: [{
          name: '城乡',
          type: 'bar',
          barWidth: 20,
          stack: '点数',
          label: {
            show: true,
            position: 'inside', // 在左边显示
            formatter: '{c}%'// 给计算后的数值添加%
          },
          data: this.preCost.cx_rate == 0 ? [] : [this.preCost.cx_rate]// 计算对应的百分比
        },
          {
            name: '其他',
            type: 'bar',
            stack: '点数',
            label: {
              show: true,
              position: 'inside', // 在右边显示
              formatter: '{c}%'
            },
            data: this.preCost.qt_rate == 0 ? [] : [this.preCost.qt_rate]
          },
          {
            name: '城职',
            type: 'bar',
            stack: '点数',
            label: {
              show: true,
              position: 'inside', // 在右边显示
              formatter: '{c}%'
            },
            data: this.preCost.cz_rate == 0 ? [] : [this.preCost.cz_rate]
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      // sumfee.setOption(option);
      this.totalCostOptions = option
    },
    drawTotalPointBar() {
      // 基于准备好的dom，初始化echarts实例
      // let totalPoint = echarts.init(document.getElementById('totalPoint'));
      let _this = this
      let option = {
        // color: ['#81C1DC', '#3488AD', '#00557C'],//设置颜色
        color: ['#83ced0', '#f2855c', '#67c23a'],
        // 顶部显示
        legend: [
          {
            left: '-1.2%',
            bottom: '0%',
            itemHeight: 11,
            itemWidth: 20,
            formatter: function (name) {
              // return name + "：" + _this.$somms.ifNullZero(_this.totalPoint.cx)
              return name + '：' + (_this.handleNumberCost(_this.totalPoint.cx) ? _this.handleNumberCost(_this.totalPoint.cx) : 0)
            },
            textStyle: {
              fontSize: _this.fontSize
            },
            data: ['城乡']
          },
          {
            left: '35%',
            itemHeight: 11,
            itemWidth: 20,
            bottom: '0%',
            formatter: function (name) {
              // return name + "："  + _this.$somms.ifNullZero(_this.totalPoint.qt)
              return name + '：' + (_this.handleNumberCost(_this.totalPoint.qt) ? _this.handleNumberCost(_this.totalPoint.qt) : 0)
            },
            textStyle: {
              fontSize: _this.fontSize
            },
            data: ['其他']
          },
          {
            right: '-5%',
            itemHeight: 11,
            itemWidth: 20,
            bottom: '0%',
            formatter: function (name) {
              // return name + "："  + _this.$somms.ifNullZero(_this.totalPoint.cz)
              return name + '：' + (_this.handleNumberCost(_this.totalPoint.cz) ? _this.handleNumberCost(_this.totalPoint.cz) : 0)
            },
            textStyle: {
              fontSize: _this.fontSize
            },
            data: ['城职']
          }
        ],
        // 设置下方图标的位置
        grid: {
          left: '0%',
          right: '0%',
          top: '2%',
          bottom: '30%',
          containLabel: true
        },
        // 设置X轴的参数
        xAxis: [
          {
            type: 'value',
            max: 100, // 设置最大值是多少
            splitNumber: 5, // 设置分几段显示
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            show: true
          }
        ],
        // 设置Y轴的参数
        yAxis: {
          splitLine: {
            show: false
          },
          axisLine: { // y轴
            show: false
          },
          axisTick: {
            show: false
          },
          type: 'category'
        },
        // 设置每个item的参数
        series: [{
          name: '城乡',
          type: 'bar',
          barWidth: 20,
          stack: '点数',
          label: {
            show: true,
            position: 'inside', // 在左边显示
            formatter: '{c}%'// 给计算后的数值添加%
          },
          data: this.totalPoint.cx_rate == 0 ? [] : [this.totalPoint.cx_rate] // 计算对应的百分比
        },
          {
            name: '其他',
            type: 'bar',
            stack: '点数',
            label: {
              show: true,
              position: 'inside',
              formatter: '{c}%'
            },
            data: this.totalPoint.qt_rate == 0 ? [] : [this.totalPoint.qt_rate]
          },
          {
            name: '城职',
            type: 'bar',
            stack: '点数',
            label: {
              show: true,
              position: 'inside', // 在右边显示
              formatter: '{c}%'
            },
            data: this.totalPoint.cz_rate == 0 ? [] : [this.totalPoint.cz_rate]
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      // totalPoint.setOption(option);
      this.totalPointOptions = option
    },
    drawTendencyAnalysis() {
      // 基于准备好的dom，初始化echarts实例
      // let tendencyAnalysis = echarts.init(document.getElementById('tendencyAnalysis'));
      let option = {
        color: ['#83ced0', '#f2855c'],
        title: {
          text: 'DIP结算预测趋势分析',
          left: '5%',
          textStyle: {
            fontSize: Number(getComputedStyle(document.documentElement).getPropertyValue('--biggerSize').replace('px', ' '))
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        grid: {
          bottom: '10%'
        },
        legend: {
          right: '1%',
          textStyle: {
            fontSize: Number(getComputedStyle(document.documentElement).getPropertyValue('--smallSize').replace('px', ' '))
          },
          data: ['原项目支付金额', 'DIP预测支付金额', '差异比例']
        },
        xAxis: [
          {
            type: 'category',
            splitLine: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            data: this.monthList,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位/万元',
            min: 0,
            axisTick: {
              show: false
            },
            axisLine: { // y轴
              show: false
            },
            splitLine: {
              show: false
            }
          },
          {
            type: 'value',
            // min: 0,
            // interval: 1,
            axisTick: {
              show: false
            },
            axisLine: { // y轴
              show: false
            },
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: '原项目支付金额',
            type: 'bar',
            barWidth: 20,
            data: this.totalList
          },
          {
            name: 'DIP预测支付金额',
            type: 'bar',
            barWidth: 20,
            data: this.dipList
          },
          {
            name: '差异比例',
            type: 'line',
            yAxisIndex: 1,
            data: this.cybList
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      // tendencyAnalysis.setOption(option);
      this.tendencyAnalysisOption = option
    },
    // 查询前几月数据
    getMonth() {
      let params = this.getParams()
      getMonthData(params).then(res => {
        let monthData = res.data
        this.monthList = [0]
        this.totalList = [0]
        this.dipList = [0]
        this.cybList = [0]
        if (monthData.controlMap.monthList != 0) {
          this.monthList = monthData.controlMap.monthList
          this.totalList = monthData.controlMap.totalList
          this.dipList = monthData.controlMap.dipList
          this.cybList = monthData.controlMap.cybList
        }
        this.drawTendencyAnalysis()
      })
    },
    queryDetails(row) {
      this.$router.push({
        path: '/auliManage/caseCompar',
        query: {
          id: row.id,
          cy_start_date: this.queryForm.cy_start_date,
          cy_end_date: this.queryForm.cy_end_date,
          dipGroup: row.dipCodg,
          type: '1'
        }
      })
    },
    fnQueryDockerNames() {
      const params = {
        b16c: this.queryForm.deptCode,
        doctorType: this.queryForm.doctorType
      }
      queryMedicalDoctorSelectInput(params).then((response) => {
        this.queryForm.dockerCode = ''
        this.dockerNames = response.data
      })
    },
    querySearchAsync(queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDipGroupByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSizeChange(val) {
      this.queryForm.pageNum = 1
      this.queryForm.pageSize = val
      this.fnQuery()
    },
    handleCurrentChange(val) {
      this.queryForm.pageNum = val
      this.fnQuery()
    },
    clearRouteQuery() {
      if (this.$route.query) {
        this.$router.push({query: {}}).catch(() => {
        })
      }
    },
    refresh() {
      this.reload()
      this.clearRouteQuery()
    },
    exportExcel() {
      let tableId = 'dataTableId'
      let fileName = '支付预测数据'
      this.somExportExcel(tableId, fileName)
    },
    getParams() {
      let params = {}
      Object.assign(params, this.queryForm)
      if (params.ym) {
        params.ym = moment(params.ym).format('yyyy-MM')
        this.calculateYM = params.ym
      }
      params.cy_start_date = this.queryForm.begnDate
      params.cy_end_date = this.queryForm.expiDate
      params.seStartTime = this.queryForm.seStartTime
      params.seEndTime = this.queryForm.seEndTime
      params.bah = this.queryForm.medcasCodg
      params.dipGroup = this.queryForm.dipCodg
      params.dataAuth = true
      return params
    },
    getNewParams() {
      let params = {}
      Object.assign(params, this.queryForm)
      if (params.ym) {
        params.ym = moment(params.ym).format('yyyy-MM')
        this.calculateYM = params.ym
      }
      params.cy_start_date = this.queryForm.begnDate
      params.cy_end_date = this.queryForm.expiDate
      params.seStartTime = this.queryForm.seStartTime
      params.seEndTime = this.queryForm.seEndTime
      params.bah = this.queryForm.medcasCodg
      params.dipGroup = this.queryForm.dipCodg
      params.dataAuth = true
      return params
    }
  },
  watch: {
    'queryForm.doctorType'() {
      this.fnQueryDockerNames()
    }
  }
}
</script>
<style scoped>
.code {
  font-size: 12px;
  color: #000000;
  text-overflow: ellipsis;
  overflow: hidden;
}

.name {
  font-size: 10px;
  color: #9b9b9b;
  text-overflow: ellipsis;
  overflow: hidden;
}

.ptp-left-item {
  height: 33%;
  display: flex;
  align-items: center;
}

.ptp-left-item-draw {
  height: 60%;
}

.ptp-left-item-title {
  display: inline-block;
  text-align: left;
  color: gray;
  font-size: var(--biggerSize);
  display: flex;
  align-items: center;
}

.ptp-left-item-value {
  width: 100%;
  display: inline-block;
  text-align: right;
  color: #469ca5;
  font-weight: 400;
  font-size: var(--smallTitleSize);
}

.ptp-circle {
  height: 2rem;
  width: 2rem;
  background-color: #bee3e8;
  border-radius: 50%;
  display: inline-block;
  margin-right: 0.5rem;
}

.ptp-center-header {
  height: 10%;
  width: 100%;
  margin-bottom: 1rem;
}

.ptp-center-content {
  /*height: 85%;*/
  height: 80%;
  width: 100%;
}

.ptp-center-header-ul {
  padding: 0;
  margin-bottom: 0;
  height: 100%;
  list-style-type: none;
  display: flex;
  text-align: center;
  align-content: space-between;
}

.ptp-center-header-li {
  text-align: center;
  height: 100%;
  width: 25%;
}

.ptp-center-header-li-content {
  cursor: pointer;
}

.ptp-center-content-title-value {
  color: gray;
  /*margin-top: 0.4rem;*/
  /*height: 1.7rem;*/
  height: 25%;
  font-size: var(--textSize);
  align-items: center;
}

.ptp-center-content-value {
  /*height: 1.5rem;*/
  height: 20%;
  color: gray;
  font-size: var(--smallSize);
}

.ptp-center-content-item {
  display: flex;
  flex-direction: row;
}

.ptp-center-content-item-circle {
  height: 100%;
  width: 2.5rem;
}

.ptp-center-content-samll-title {
  color: #469ca5;
  font-weight: 500;
  font-size: var(--biggerSize);
}

.ptp-center-content-pre {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
}

.title-side {
  color: gray;
  font-size: var(--smallSize);
  margin-right: 1rem;
}

.title-side-value {
  color: black;
  font-size: var(--textSize);
}

.title-side-button {
  background-color: #469ca5;
  color: white;
  border-radius: 5px
}

.checked {
  border-bottom: 2px solid #469ca5;
  color: #469ca5;
}

.unchecked {
  color: black;
  border-bottom: 2px solid whitesmoke;
}
</style>
