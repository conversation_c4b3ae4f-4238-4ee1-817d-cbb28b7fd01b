<template>
  <div class="app-container">
    <el-table :id="id"
              ref="elTable"
              :data="data"
              v-loading="loading"
              height="100%"
              :header-cell-style="{'text-align':'center'}"
              @selection-change="handleSelectionChange"
              stripe
              border>
      <el-table-column label="序号" type="index" align="center" width="40"/>
      <el-table-column label="病案号" prop="medcasCodg" align="right" width="60" v-if="condition3" :key="1"/>
      <el-table-column label="姓名" prop="name" width="80" v-if="condition3" :key="2"/>
      <el-table-column label="结算状态" width="60" align="center">
        <template slot-scope="scope">
          <i class="som-icon-error-waring som-icon-big" v-if="!scope.row.listSerialNumFlag"></i>
          <i class="som-icon-success som-icon-big" v-else></i>
        </template>
      </el-table-column>
<!--      <el-table-column label="标识状态" align="center"  width="40" show-overflow-tooltip v-if="condition1" :key="16" fixed="right">-->
<!--        <template slot-scope="scope">-->
<!--          <i class="som-icon-error-waring som-icon-big" v-if="scope.row.lookOver === '0'"></i>-->
<!--          <i class="som-icon-success som-icon-big" v-else></i>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="提交状态" align="center" width="80" show-overflow-tooltip v-if="condition5" :key="20"-->
<!--                       fixed="right">-->
<!--        <template slot-scope="scope">-->
<!--          <div class="som-flex-center" v-if="scope.row.stasType === '0'"><i-->
<!--              class="som-icon-waring2 mgr-l-02"></i>未提交-->
<!--          </div>-->
<!--          <div class="som-flex-center" v-else><i class="som-icon-success mgr-l-02"></i>已提交</div>-->
<!--        </template>-->
<!--      </el-table-column>-->

      <el-table-column label="校验状态" align="center" width="60" show-overflow-tooltip v-if="condition1" :key="17" fixed="right">
        <template slot-scope="scope">
          <i class="som-icon-error-waring som-icon-big" v-if="scope.row.chkStas === '0'"></i>
          <i class="som-icon-success som-icon-big" v-else></i>
        </template>
      </el-table-column>
      <!--      <el-table-column label="标识状态记录查看" align="center" width="160" :key="18" fixed="right">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button type="primary" size="mini" icon="el-icon-search" @click="showDialog(scope.row)" circle />-->
      <!--        </template>-->
      <!--      </el-table-column>-->
<!--      <el-table-column label="复制请求参数" align="center" width="80" :key="19" fixed="right"-->
<!--                       v-if="condition2 || condition4">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button type="primary" size="mini" icon="el-icon-document-copy" @click="seeRequestParams(scope.row)"-->
<!--                     circle/>-->
<!--        </template>-->
<!--      </el-table-column>-->

    <el-table-column label="编辑清单" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-search" @click="editDetails(scope.row)" circle/>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
        :title="profttl"
        :visible.sync="dialogVisible"
        width="50%">
      <el-table
          :data="form"
          style="width: 100%">
        <el-table-column
            prop="userName"
            label="用户名"
            width="180">
        </el-table-column>
        <el-table-column
            prop="nknm"
            label="昵称"
            width="180">
        </el-table-column>
        <el-table-column
            prop="crteTime"
            label="时间">
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <span>{{ scope.row.oprt == 0 ? '修改标识为未完成' : '修改标识为完成' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { querySettleListOpeLog } from '@/api/medicalQuality/settleListDetail'

export default {
  name: 'listTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    updateStatus: {
      type: String,
      default: '未上传'
    },
    tabName: {
      type: String,
      default: 'dataTable'
    },
    id: {
      type: String
    },
    // 是否通过校验才能上传
    passValidateUpload: {
      type: Boolean,
      default: true
    }
  },
  data: () => ({
    dialogVisible: false,
    profttl: '标识状态记录查看',
    form: {}
  }),
  updated () {
    this.updateTable()
  },
  computed: {
    // 未上传
    condition1 () {
      return this.updateStatus === '未上传'
    },
    // 上传失败
    condition2 () {
      return this.updateStatus === '上传失败'
    },
    // 上传成功
    condition4 () {
      return this.updateStatus === '上传成功'
    },
    // 状态修改
    condition5 () {
      return this.updateStatus === '状态修改'
    },
    // 所有
    condition3 () {
      return this.condition1 || this.condition2 || this.condition4 || this.condition5
    },
    // 病种分组类型名称
    disGpName () {
      return this.$somms.getGroupTypeName()
    },
    // 是否是DIP
    isDip () {
      return this.$somms.isDIP()
    },
    // 是否是DRG
    isDrg () {
      return this.$somms.isDRG()
    }
  },
  methods: {
    updateTable () {
      this.$nextTick(() => {
        if (this.$refs.elTable) {
          setTimeout(() => {
            this.$refs.elTable.doLayout()
          }, 400)
        }
      })
    },
    jumpDetails (row) {
      this.$router.push({
        path: '/setlListManage/setlListInfo2',
        query: { id: row.id, k00: row.k00, see: true }
      })
    },
    judgeSex (row) {
      switch (row.gend) {
        case '1':
          return '男'
        case '2':
          return '女'
      }
    },
    handleSelectionChange (val) {
      this.$emit('selectData', val)
    },
    seeRequestParams (row) {
      const textarea = document.createElement('textarea')
      textarea.setAttribute('readonly', 'readonly')
      textarea.value = row.reqtPara
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      this.$message.success('复制成功')
    },
    selectable (row) {
      if (this.condition5) {
        // if (row.stasType === '1') {
        //   return false
        // }
      } else {
        if ((row.chkStas === '0' || row.lookOver === '0') && this.passValidateUpload) {
          return false
        }
      }
      return true
    },
    showDialog (row) {
      let params = { k00: '' }
      params.k00 = row.k00
      querySettleListOpeLog(params).then(res => {
        this.form = res.data
        this.dialogVisible = true
      })
    },
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    editDetails (item) {
      // 更新 selectedItem，而不是跳转
      this.$emit('methodCalled', item)
    }
  }
}
</script>

<style scoped>
/deep/ .el-tooltip__popper, .el-tooltip__popper.is-dark {
  max-height: 90%;
}

.mgr-l-02{
  margin-right: 0.2rem;
}
</style>
