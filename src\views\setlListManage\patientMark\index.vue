<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             :show-date-range="{ show : true, clearable : true }"
             show-pagination
             :total-num="total"
             :show-hos-dept="{show : true }"
             :exportExcel="{ tableId: tableId, exportName: '状态管理'}"
             :exportExcelFun="queryPatientInfo"
             headerTitle="查询条件"
             contentTitle="患者列表"
             :extendFormIndex="[2]"
             @query="queryData" @reset="reset($event)">

      <template slot="buttonsSuffix">
        <el-checkbox-group v-model="errDscr" style="margin-left: 1rem" @change="queryData">
          <el-checkbox-button v-for="(errType, index) in errorList" :label="errType" :key="index">{{ errType }}</el-checkbox-button>
        </el-checkbox-group>
      </template>

      <!--  查询 -->
      <template slot="extendFormItems">
        <el-form-item label="结算时间" prop="settlementTime">
          <el-date-picker v-model="queryForm.settlementTime"
                          type="daterange"
                          class="item-width"
                          unlink-panels
                          range-separator="-"
                          start-placeholder="开始时间"
                          end-placeholder="结束时间"
                          value-format="yyyy-MM-dd"
                          @change="changeSettlementTime" />
        </el-form-item>

        <el-form-item label="标记清单完成状态" prop="finishSign">
          <el-select v-model="queryForm.finishSign" clearable placeholder="请选择" @change="queryData">
            <el-option
              v-for="item in finishSigns"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="清单修改状态" prop="updateSign">
          <el-select v-model="queryForm.updateSign" clearable placeholder="请选择" @change="queryData">
            <el-option
              v-for="item in updateSigns"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="病案号" prop="a48">
          <el-input v-model="queryForm.a48" placeholder="请输入病案号"  @input="queryData"/>
        </el-form-item>
        <el-form-item label="上传状态" prop="upldStas">
          <el-select v-model="queryForm.upldStas" clearable multiple placeholder="请选择" @change="queryData">
            <el-option v-for="(item,index) in uploadFlagList"
                       :key="index"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </el-form-item>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <el-table
          :data="tableData"
          v-loading="loading"
          height="100%"
          ref="dataTable"
          :id="tableId"
          style="width: 100%">
          <el-table-column type="index" label="序号"/>
          <el-table-column prop="a11" label="患者姓名"/>
          <el-table-column prop="a48" label="病案号"/>
          <el-table-column prop="b16n" label="科室"/>
          <el-table-column prop="b25n" label="住院医师"/>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="danger" @click="deleteMark(scope.row)" v-if="scope.row.mark === '1'">删除标记</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryPatientInfo, deleteSettlementMark } from '@/api/listManagement/patientInfo'

export default {
  name: 'patientMark',
  data: () => ({
    queryForm: {
      // finishSign: '',
      // updateSign: '',
      // cy_start_date: null,
      // cy_end_date: null,
      // deptCode: '',
    },
    tableData: [],
    loading: false,
    total: 0,
    tableId: 'dataTable',
    finishSigns: [
      {
        value: '1',
        label: '已完成'
      },
      {
        value: '0',
        label: '未完成'
      }
    ],
    updateSigns: [
      {
        value: '1',
        label: '已修改'
      },
      {
        value: '0',
        label: '未修改'
      }
    ],
    uploadFlagList: {},
    errDscr: [],
    errorList: ['票据/流水号/结算/人员编号信息为空', '住院时间与系统内住院日期不符', '自费且上传成功']
  }),
  mounted () {
    this.uploadFlagList = this.$somms.getDictValue('UPLOAD_FLAG', 2)
  },
  methods: {
    queryPatientInfo,
    queryData () {
      this.loading = true
      queryPatientInfo(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.loading = false
      })
    },
    deleteMark (row) {
      deleteSettlementMark(row).then(res => {
        this.$message.success('删除成功')
      })
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      if (params.upldStas) {
        params.uploadFlag = params.upldStas
      } else {
        params.uploadFlag = []
      }
      if (this.queryForm.dateRange != null && this.queryForm.dateRange.length > 0) {
        params.cy_start_date = this.queryForm.dateRange[0]
        params.cy_end_date = this.queryForm.dateRange[1]
      }

      if (this.queryForm.settlementTime && this.queryForm.settlementTime.length > 0) {
        params.settlementStartTime = this.queryForm.settlementTime[0]
        params.settlementEndTime = this.queryForm.settlementTime[1]
      }

      if (this.errDscr) {
        params.errorTypeDesc = this.errDscr
      } else {
        params.errorTypeDesc = []
      }
      return params
    },

    changeSettlementTime () {
      this.queryData()
    },
    reset (val) {
      this.queryForm = Object.assign({}, val)
      this.queryForm.dateRange[0] = this.queryForm.inDateRange[0]
      this.queryForm.dateRange[1] = this.queryForm.inDateRange[1]
      this.errDscr = []
    }
  }
}
</script>
