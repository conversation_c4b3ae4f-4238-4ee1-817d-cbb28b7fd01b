<template>
  <div class="app-container">
    <drg-container v-model="queryForm"
             ref="form"
             :container="true"
             contentTitle="数据列表"
             @query="queryData">

      <template slot="content">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  size="mini"
                  stripe
                  height="100%"
                  :data="dataList"
                  style="width: 100%;"
                  border>
          <el-table-column label="创建时间" prop="crteTime" align="left" >
          </el-table-column>
          <el-table-column label="交易流水号" prop="was002" align="center" >
          </el-table-column>
          <el-table-column label="日志信息" prop="logInfo" align="center" show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="复制日志信息" align="center" width="160" :key="19" fixed="right">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" icon="el-icon-document-copy" @click="seeRequestParams(scope.row)" circle />
            </template>
          </el-table-column>
        </el-table>
      </template>
    </drg-container>
  </div>
</template>
<script>
import { queryLog } from '@/api/interfaceTest/interfaceTestRule'
export default {
  name: 'interfaceTestLog',
  data: () => ({
    queryForm: {
    },
    dataList: []
  }),
  created () {
    // 页面创建时开始轮询
    this.startPolling()
  },
  destroyed () {
    // 页面销毁时停止轮询
    this.stopPolling()
  },
  mounted () {
    this.queryData() // 在页面加载时自动发起查询请求
  },

  methods: {
    // 开始轮询
    startPolling () {
      this.pollingTimer = setInterval(() => {
        this.queryData()
      }, 5000) // 每5秒查询一次数据库
    },
    // 停止轮询
    stopPolling () {
      clearInterval(this.pollingTimer)
    },
    queryData () {
      queryLog(this.getParams()).then(res => {
        this.dataList = res.data
      })
    },
    // 获取参数
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    },
    seeRequestParams (row) {
      const textarea = document.createElement('textarea')
      textarea.setAttribute('readonly', 'readonly')
      textarea.value = row.logInfo
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      this.$message.success('复制成功')
    }
  }
}
</script>
