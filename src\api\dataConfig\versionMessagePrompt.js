import request from '@/utils/request'
// 系统配置

export function queryData (params) {
  return request({
    url: '/versionMessagePromptController/queryData',
    method: 'post',
    data: params
  })
}
export function deleteData (params) {
  return request({
    url: '/versionMessagePromptController/deleteData',
    method: 'post',
    params: params
  })
}
export function addData (params) {
  return request({
    url: '/versionMessagePromptController/addData',
    method: 'post',
    params: params
  })
}
export function updateData (params) {
  return request({
    url: '/versionMessagePromptController/updateData',
    method: 'post',
    params: params
  })
}
export function updateSys (params) {
  return request({
    url: '/versionMessagePromptController/updateSys',
    method: 'post',
    params: params
  })
}
export function updateMessage (params) {
  return request({
    url: '/versionMessagePromptController/updateMessage',
    method: 'post',
    data: params
  })
}

export function querySystemTime (params) {
  return request({
    url: '/sysInitController/querySystemTime',
    method: 'post',
    data: params
  })
}
