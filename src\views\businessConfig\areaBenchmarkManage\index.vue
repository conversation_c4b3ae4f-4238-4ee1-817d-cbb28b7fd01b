<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
              :container="true"
              :show-dip="{ show: showDip}"
              :show-drg="{ show: showDrg }"
              :totalNum="total"
              show-pagination
              headerTitle="查询条件"
              contentTitle="区域标杆配置"
              @query="queryData">
      <!-- 内容 -->
      <template slot="extendFormItems">
        <el-form-item label="医院级别" prop="hospLv">
          <drg-dict-select dicType="YYJB" placeholder="请选择医院级别" v-model="queryForm.hospLv" @change="queryData"/>
        </el-form-item>
      </template>
      <template slot="extendFormItems">
        <el-form-item label="标杆年份">
          <el-input v-model="queryForm.year" placeholder="请输入标杆年份" @change="queryData"/>
        </el-form-item>
      </template>

      <template slot="buttons">
        <el-button @click="showDialog(null,2)" type="primary" class="som-button-margin-right">新增</el-button>
        <el-button type="primary" @click="dialogVisible = true" class="som-button-margin-right"><i
          class="el-icon-upload el-icon--left"></i>文件上传
        </el-button>
        <el-button type="primary" @click="downloadAreaBenchmarkTemplate" class="som-button-margin-right"><i
          class="el-icon-download el-icon--left"></i>模板下载
        </el-button>
      </template>
      <!--      <template slot="extendFormItems" >
                      <el-form-item label="组">
                        <el-select  v-model="queryForm.group"
                                    placeholder="请选择组"
                                    class="som-form-item"
                                    @change="typeConflict()">
                          <el-option
                            v-for="item in groupOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item>
            </template>-->
      <template slot="containerContent">
        <el-table
          :data="tableData"
          height="100%"
          style="width: 100%">
          <el-table-column :show="false"
                           prop="id"
                           label="ID">
          </el-table-column>
          <el-table-column
            prop="standardYear"
            label="标杆年份">
          </el-table-column>
          <el-table-column v-if="showDip"
                           prop="dipCodg"
                           label="DIP编码" show-overflow-tooltip>
          </el-table-column>
          <el-table-column v-if="showDrg"
                           prop="drgCodg"
                           label="DRG编码" show-overflow-tooltip>
          </el-table-column>
          <el-table-column v-if="showDip"
                           prop="dipName"
                           :show-overflow-tooltip="true"
                           label="DIP名称">
          </el-table-column>
          <el-table-column v-if="showDrg"
                           prop="drgName"
                           :show-overflow-tooltip="true"
                           label="DRG名称">
          </el-table-column>
          <el-table-column v-if="showDip"
                           prop="isUsedAsstList"
                           label="是否使用辅助目录">
          </el-table-column>
          <el-table-column v-if="showDip"
                           prop="asstListAgeGrp"
                           label="辅助目录-年龄段">
          </el-table-column>
          <el-table-column v-if="showDip"
                           prop="auxiliaryIlness"
                           label="辅助目录-疾病严重程度">
          </el-table-column>
          <el-table-column v-if="showDip"
                           prop="asstListTmorSevDeg"
                           label="辅助目录-肿瘤严重程度">
          </el-table-column>
          <el-table-column v-if="showDip"
                           prop="dipDiagCodg"
                           label="DIP诊断编码">
          </el-table-column>
          <el-table-column v-if="showDip"
                           prop="dipDiagName"
                           :show-overflow-tooltip="true"
                           label="DIP诊断名称">
          </el-table-column>
          <el-table-column v-if="showDip"
                           prop="dipOprtCodg"
                           label="DIP操作编码" show-overflow-tooltip>
          </el-table-column>
          <el-table-column v-if="showDip"
                           prop="dipOprtName"
                           :show-overflow-tooltip="true"
                           label="DIP操作名称">
          </el-table-column>
          <el-table-column v-if="showDip"
                           prop="dipWt"
                           label="DIP权重">
          </el-table-column>

          <el-table-column v-if="showDrg"
                           prop="mdcStandardAveHospDay"
                           label="MDC标杆平均住院日">
          </el-table-column>
          <el-table-column v-if="showDrg"
                           prop="mdcStandardAvgFee"
                           label="MDC标杆例均费用">
          </el-table-column>
          <el-table-column v-if="showDrg"
                           prop="adrgStandardAveHospDay"
                           label="ADRG标杆平均住院日">
          </el-table-column>
          <el-table-column v-if="showDrg"
                           prop="adrgStandardAvgFee"
                           label="ADRG标杆例均费用">
          </el-table-column>
          <el-table-column v-if="showDrg"
                           prop="drgWt"
                           label="DRG权重">
          </el-table-column>
          <el-table-column
            prop="standardAvgFee"
            label="标杆例均费用(区域)">
          </el-table-column>
          <el-table-column
            prop="dipStandardAvgFeeSameLv" v-if="showDip"
            label="DIP标杆例均费用(同级别)">
          </el-table-column>
          <el-table-column
            prop="drgStandardInpf" v-if="showDrg"
            label="DRG标杆住院费用(同级别)">
          </el-table-column>
          <el-table-column v-if="showDip"
                           prop="lastYearAvgFee"
                           label="近一年例均费用">
          </el-table-column>
          <el-table-column v-if="showDrg"
                           prop="avgAge"
                           label="平均年龄">
          </el-table-column>
          <el-table-column
            prop="refer_sco"
            label="基准分值">
          </el-table-column>
          <el-table-column
            prop="uplmtMag"
            label="上限倍率">
          </el-table-column>
          <el-table-column
            prop="lowlmtMag"
            label="下限倍率">
          </el-table-column>
          <el-table-column
            prop="adjm_cof"
            label="调节系数">
          </el-table-column>
          <drg-table-column
            prop="hospLv"
            dicType="YYJB"
            label="医院级别"/>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-edit"
                type="primary"
                circle
                @click="showDialog(scope.row,1)"></el-button>
            </template>
          </el-table-column>
          <el-table-column label="删除" align="center" fixed="right">
            <template slot-scope="scope">
              <el-popconfirm style="cursor: pointer;display: block" @confirm="deleteAreaBenchmarkInfo(scope.row)"
                             title="是否删除？">
                <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog :title="profttl" :visible.sync="dialogFormVisible">
          <el-form ref="form" :model="form" label-width="170px">
            <el-form-item label="标杆年份" v-if="addDrg">
              <el-input v-model="form.standardYear" placeholder="标杆年份"></el-input>
            </el-form-item>
            <el-form-item label="DRG编码" v-if="addDrg">
              <el-input v-model="form.drgCodg" placeholder="DRG编码"></el-input>
            </el-form-item>
            <el-form-item label="drg名称" v-if="addDrg">
              <el-input v-model="form.drgName" placeholder="drg名称"></el-input>
            </el-form-item>
            <el-form-item label="平均年龄" v-if="addDrg">
              <el-input v-model="form.avgAge" placeholder="平均年龄"></el-input>
            </el-form-item>
            <el-form-item label="标杆年份" v-if="addDip">
              <el-input v-model="form.standardYear" placeholder="标杆年份"></el-input>
            </el-form-item>
            <el-form-item label="DIP编码" v-if="addDip">
              <el-input v-model="form.dipCodg" placeholder="DIP编码"></el-input>
            </el-form-item>
            <el-form-item label="DIP名称" v-if="addDip">
              <el-input v-model="form.dipName" placeholder="DIP名称"></el-input>
            </el-form-item>
            <el-form-item label="是否使用辅助目录" v-if="addDip">
              <el-input v-model="form.isUsedAsstList" placeholder="是否使用辅助目录"></el-input>
            </el-form-item>
            <el-form-item label="辅助目录-年龄段" v-if="addDip">
              <el-input v-model="form.asstListAgeGrp" placeholder="辅助目录-年龄段"></el-input>
            </el-form-item>
            <el-form-item label="辅助目录-疾病严重程度" v-if="addDip">
              <el-input v-model="form.auxiliaryIlness" placeholder="辅助目录-疾病严重程度"></el-input>
            </el-form-item>
            <el-form-item label="辅助目录-肿瘤严重程度" v-if="addDip">
              <el-input v-model="form.asstListTmorSevDeg" placeholder="辅助目录-肿瘤严重程度"></el-input>
            </el-form-item>
            <el-form-item label="ADRG标杆平均住院日" v-if="showDrg">
              <el-input v-model="form.adrgStandardAveHospDay" placeholder="请输入ADRG标杆例均费用"></el-input>
            </el-form-item>
            <el-form-item label="ADRG标杆例均费用" v-if="showDrg">
              <el-input v-model="form.adrgStandardAvgFee" placeholder="请输入adrg标杆例均费用"></el-input>
            </el-form-item>
            <el-form-item label="MDC标杆例均费用" v-if="showDrg">
              <el-input v-model="form.mdcStandardAvgFee" placeholder="请输入MDC标杆例均费用"></el-input>
            </el-form-item>
            <el-form-item label="MDC标杆平均住院日" v-if="showDrg">
              <el-input v-model="form.mdcStandardAveHospDay" placeholder="请输入MDC标杆平均住院日"></el-input>
            </el-form-item>
            <el-form-item label="DRG权重" v-if="showDrg">
              <el-input v-model="form.drgWt" placeholder="请输入drg权重"></el-input>
            </el-form-item>
            <el-form-item label="DIP诊断编码" v-if="showDip">
              <el-input v-model="form.dipDiagCodg" placeholder="请输入DIP诊断编码"></el-input>
            </el-form-item>
            <el-form-item label="DIP诊断名称" v-if="showDip">
              <el-input v-model="form.dipDiagName" placeholder="请输入DIP诊断名称"></el-input>
            </el-form-item>
            <el-form-item label="DIP操作编码" v-if="showDip">
              <el-input v-model="form.dipOprtCodg" placeholder="请输入DIP诊断名称"></el-input>
            </el-form-item>
            <el-form-item label="DIP操作名称" v-if="showDip">
              <el-input v-model="form.dipOprtName" placeholder="请输入DIP诊断名称"></el-input>
            </el-form-item>
            <el-form-item label="DIP权重" v-if="showDip">
              <el-input v-model="form.dipWt" placeholder="请输入DIP权重"></el-input>
            </el-form-item>
            <el-form-item label="标杆例均费用(区域)">
              <el-input v-model="form.standardAvgFee" placeholder="请输入标杆例均费用(区域)"></el-input>
            </el-form-item>
            <el-form-item label="DRG标杆住院费用(同级别)" v-if="showDrg">
              <el-input v-model="form.drgStandardInpf" placeholder="请输入drg标杆住院费用(同级别)"></el-input>
            </el-form-item>
            <el-form-item label="DIP标杆例均费用(同级别)" v-if="showDip">
              <el-input v-model="form.dipStandardAvgFeeSameLv" placeholder="请输入dip标杆例均费用(同级别)"></el-input>
            </el-form-item>
            <el-form-item label="近一年例均费用" v-if="showDip">
              <el-input v-model="form.lastYearAvgFee" placeholder="请输入近一年例均费用"></el-input>
            </el-form-item>
            <el-form-item label="基准分值">
              <el-input v-model="form.refer_sco" placeholder="请输入基准分值"></el-input>
            </el-form-item>
            <el-form-item label="调节系数">
              <el-input v-model="form.adjm_cof" placeholder="请输入调节系数"></el-input>
            </el-form-item>
            <el-form-item label="医院等级">
              <el-input v-model="form.hospLv" placeholder="请输入医院等级"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取 消</el-button>
            <el-button type="primary" @click="commitData()">确 定</el-button>
          </div>
        </el-dialog>
        <el-dialog
          title="上传文件"
          :visible.sync="dialogVisible"
          width="50%">
          <el-upload
            style="text-align: center"
            drag
            ref="upload"
            :limit="1"
            action="customize"
            accept=".xlsx,.xls"
            :http-request="upload">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
          </el-upload>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import {
  deleteAreaBenchmarkInfo,
  insertAreaBenchmarkInfo,
  queryAreaBenchmarkInfo,
  updateAreaBenchmarkInfo,
  areaBenchmarkUpload,
  downloadAreaBenchmarkTemplate,
  downloadDeptTemplate
} from '@/api/dataConfig/areaBenchmarkConfig'
import {queryEnableGroup} from '@/api/common/sysCommon'
import Vue from 'vue'

export default {
  name: 'areaBenchmarkManage',
  data: () => ({
    queryForm: {
      group: '',
      queryType: '1',
      hospLv: '',
      year: ''
    },
    tableData: [],
    groupOptions: [1],
    showDip: true,
    showDrg: false,
    dialogFormVisible: false,
    dialogType: '1',
    isEdit: true,
    profttl: '',
    form: {},
    addDip: false,
    addDrg: false,
    dialogVisible: false,
    total: 0
  }),
  mounted() {
    this.typeConflict()
    this.$nextTick(() => {
      this.queryData()
      this.getEnabledGroup()
    })
  },
  methods: {
    queryData() {
      queryAreaBenchmarkInfo(this.getParams()).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
      })
    },
    deleteAreaBenchmarkInfo(row) {
      let params = {id: row.id, group: this.getParams().group}
      deleteAreaBenchmarkInfo(params).then(res => {
        this.queryData()
      })
    },
    getEnabledGroup() {
      queryEnableGroup().then(res => {
        if (res.code == 200) {
          this.groupOptions = res.data
        }
      })
    },
    getParams() {
      let params = {}
      Object.assign(params, this.queryForm)
      params.group = this.$somms.getGroupType()
      return params
    },
    typeConflict() {
      this.queryForm.group = this.$somms.getGroupType()
      if (this.queryForm.group == '1') {
        this.showDip = true
        this.showDrg = false
      } else {
        this.showDip = false
        this.showDrg = true
      }
    },
    showDialog(row, type) {
      this.dialogFormVisible = true
      if (type == 1) {
        this.profttl = '编辑'
        this.dialogType = type
        this.form = row
        this.addDip = false
        this.addDrg = false
      } else {
        this.profttl = '新增'
        this.dialogType = type
        this.form = {}
        if (this.queryForm.group == 1) {
          this.addDip = true
          this.addDrg = false
        } else {
          this.addDip = false
          this.addDrg = true
        }
      }
    },
    commitData() {
      Vue.set(this.form, 'group', this.queryForm.group)
      if (this.dialogType == 1) {
        updateAreaBenchmarkInfo(this.form)
        this.dialogFormVisible = false
      } else {
        insertAreaBenchmarkInfo(this.form).then(res => {
          this.queryData()
        })
        this.dialogFormVisible = false
      }
    },
    upload(data) {
      let params = new FormData()
      params.append('file', data.file)
      params.append('group', this.queryForm.group)
      areaBenchmarkUpload(params).then(res => {
        if (res.code == 200) {
          this.dialogVisible = false
          this.$refs.upload.clearFiles()
          this.$message.success('上传成功')
          this.queryData()
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
      })
    },
    downloadAreaBenchmarkTemplate() {
      let params = {group: this.queryForm.group}
      downloadAreaBenchmarkTemplate(params).then(res => {
        this.$somms.download(res, '区域标杆模板', 'application/vnd.ms-excel')
      })
    }
  }
}
</script>
<style scoped>
/deep/ .el-dialog {
  height: 60vh;
  overflow: auto;
}
</style>
<style>
.el-tooltip__popper {
  max-width: 20%;
}

.el-tooltip__popper, .el-tooltip__popper.is-dark {
  background: rgb(48, 65, 86) !important;
  color: #fff !important;
  line-height: 24px;
}
</style>
