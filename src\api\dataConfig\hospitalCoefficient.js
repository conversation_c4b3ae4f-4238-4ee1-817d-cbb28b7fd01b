import request from '@/utils/request'

export function queryHC (params) {
  return request({
    url: '/HospCof/queryHC',
    method: 'post',
    params: params
  })
}

export function insertHC (params) {
  return request({
    url: '/HospCof/insertHC',
    method: 'post',
    params: params
  })
}

export function updateHC (params) {
  return request({
    url: '/HospCof/updateHC',
    method: 'post',
    params: params
  })
}

export function deleteHC (params) {
  return request({
    url: '/HospCof/deleteHC',
    method: 'post',
    params: params
  })
}
