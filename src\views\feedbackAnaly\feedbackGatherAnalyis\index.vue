<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="form"
             :container="true"
             :show-se-date-range="{show:true,disabled:false}"
             headerTitle="查询条件"
             contentTitle="分组汇总详情"
             :totalNum="total"
             :showPagination="true"
             @query="queryData" @reset="reset">
      <template slot="extendFormItems">
        <el-form-item label="医生姓名" prop="drName" >
          <el-input v-model="queryForm.drName" placeholder="请输入医生姓名" class="som-form-item" @change="()=>{this.tabName
          ='doctor';this.index=0}"  clearable/>
        </el-form-item>
        <el-form-item label="病组名称" prop="disGpName" >
          <el-input v-model="queryForm.disGpName" placeholder="请输入病组名称" class="som-form-item"  @change="()=>{this.tabName
          ='disease';this.index=2}" clearable/>
        </el-form-item>
        <el-form-item label="科室名称" prop="deptName" >
          <el-input v-model="queryForm.deptName" placeholder="请输入科室名称" class="som-form-item"
                    @change="()=>{this.tabName='dept';  this.index=1}"
                    clearable/>
        </el-form-item>
      </template>
      <template slot="containerContent">

        <el-tabs class="som-table-height" v-model="tabName" @tab-click="tabClick">

          <el-tab-pane class="som-tab-pane"
                       :label="item.label" :name="item.type" v-for="(item,index) in tabs"
                       :key="index">
<!--            <cost-table :type="item.type"-->
<!--                        :data="tableData"-->
<!--                        :loading="item.loading"-->
<!--                        :tab-name="item.type"-->
<!--                        ref="dataTable"  />-->
            <el-table
              :data="tableData"
              ref="costTable"
              :id="index"
              v-loading="item.loading"
              height="100%"
              :header-cell-style="{'text-align':'center'}"
              stripe
              border
            >
              <el-table-column label="病组编码" prop="disGpCodg" align="center" width="210" :key="10" v-if="queryForm.type=='disease'"/>
              <el-table-column label="病组名称" prop="disGpName" align="left" width="310" :key="11" v-if="queryForm.type=='disease'"/>
              <el-table-column label="科室名称" prop="deptName" align="left" width="210" :key="20" v-if="queryForm.type=='dept'"/>
              <el-table-column label="医生姓名" prop="drName" align="left" width="110" :key="21" v-if="queryForm.type=='doctor'"/>
              <el-table-column label="总费用" prop="sumfee" align="center" width="310" sortable :key="1"/>
              <el-table-column label="统筹费用" prop="poolFee" align="center" width="310" sortable :key="2"/>
              <el-table-column label="病组总费用" prop="disGpSumfee" width="310" :key="3" sortable align="center"/>
              <el-table-column label="病组统筹费用" prop="disGpPoolFee"  width="310" sortable align="center" :key="4"/>
              <el-table-column label="费用差异" prop="diff" width="110" align="center" :key="5"  sortable/>
            </el-table>

          </el-tab-pane>
        </el-tabs>

      </template>

    </drg-form>
  </div>
</template>
<script>
import { queryFeedbackGatherData } from '@/api/newBusiness/feedbackGatherAnalyis'
export default {
  name:'feedbackGatherAnalyis',
  components: {
  },
  data: () => ({
    total: 0,
    tableData: [],
    tabName: 'doctor',
    index: 0,
    tabs: [
      { label: '医生', type: 'doctor', loading: true },
      { label: '科室', type: 'dept', loading: true },
      { label: '病组', type: 'disease', loading: true }
    ],
    queryForm: {
      type: 'doctor'
    }
  }),
  methods: {
    mounted () {
      this.$nextTick(() => {
        setTimeout(() => {
          this.queryData()
        })
      })
    },
    getParam () {
      let params = {}
      this.clearOtherFormItem()
      this.queryForm.type = this.tabs[this.index].type
      this.queryForm.seStartTime = this.queryForm.seDateRange[0]
      this.queryForm.seEndTime = this.queryForm.seDateRange[1]
      return this.queryForm
    },
    // 清除其他查询条件
    clearOtherFormItem () {
      let name = this.tabName
      if (name === 'dept') {
        this.queryForm.drName = ''
        this.queryForm.disGpName = ''
      } else if (name === 'doctor') {
        this.queryForm.deptName = ''
        this.queryForm.disGpName = ''
      } else if (name === 'disease') {
        this.queryForm.deptName = ''
        this.queryForm.drName = ''
      }
    },
    queryData () {
      queryFeedbackGatherData(this.getParam()).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.total
          this.tabs[this.index].loading = false
        }
      })
    },
    tabClick (val) {
      let index = val.index
      let queryType = this.tabs[index].type
      this.tabName = queryType
      this.index = index
      this.queryForm.type = queryType
      this.queryData()
    },
    reset () {
      this.queryForm.type = this.tabs[this.index].type
    }
  }
}
</script>
<style scoped>
  /deep/ .el-tabs__content {
    height: 93%;
  }
</style>
