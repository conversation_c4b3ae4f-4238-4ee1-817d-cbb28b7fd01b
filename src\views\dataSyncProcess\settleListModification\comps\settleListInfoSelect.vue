<template>
  <div>
      <el-select v-model="value"
                 filterable clearable remote
                 :style="{ width: width }"
                 @change="change">
      <el-option
        v-for="(item,index) in selectList"
        :key="item[keyProps.key] + '_' + index"
        :label="item[actualKeyProps.label]"
        :value="item[actualKeyProps.value]">
        <span class="code" v-if="showBadgeLabel">
          <el-badge :value="item.badgeLabel" class="item">
            {{ item[keyProps.value] }}
          </el-badge>
        </span>
        <span class="code" v-else> {{ item[keyProps.value] }} </span>
        <span class="name"> {{ item[keyProps.label] }}</span>
      </el-option>
    </el-select>
    <modify-icon :show="showIcon" :use-position="false"/>
  </div>
</template>
<script>
import ModifyIcon from './settleListInfoModifyIcon.vue'
export default {
  components: {
    'modify-icon': ModifyIcon
  },
  props: {
    // v-model 绑定值
    modelVal: [String, Number],
    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: false
    },
    // 搜索实际值还是显示值
    labelOrValue: {
      type: String
    },
    // 宽度
    width: {
      type: String,
      default: '80%'
    },
    // 数据
    data: [Array],
    // 下拉选使用的key
    keyProps: {
      type: Object,
      default: () => {
        return {
          key: 'key',
          label: 'label',
          value: 'value'
        }
      }
    },
    // 显示使用的key
    actualKeyProps: {
      type: Object,
      default: () => {
        return this.keyProps
      }
    },
    // 是否显示角标
    showBadgeLabel: {
      type: Boolean,
      default: false
    },
    // 是否替换括号
    replaceBracket: {
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: 'modelVal',
    event: 'selected'
  },
  data: () => ({
    selectList: [],
    value: ''
  }),
  mounted () {
    // this.getDisplayVal('', false)
  },
  methods: {
    getDisplayVal (str, max = true) {
      this.selectList = []
      let arr = this.data
      let count = 0
      let maxCount = 50
      for (let i = 0; i < arr.length; i++) {
        if (count === maxCount && max) {
          break
        }
        let item = arr[i]
        let inFlag = false
        if (this.labelOrValue === 'label' && item[this.keyProps.label].includes(str)) {
          this.selectList.push(item)
          inFlag = true
        } else if (this.labelOrValue === 'value' && item[this.keyProps.value].includes(str)) {
          this.selectList.push(item)
          inFlag = true
        }
        if (inFlag) {
          count++
        }
      }
    },
    change (val) {
      let data = {}
      let reg = /\([^\)]*\)/g
      if (this.labelOrValue === 'label') {
        data.label = this.replaceBracket ? val.replace(reg, '') : val
      } else {
        data.value = val
      }
      for (let item of this.selectList) {
        if (this.labelOrValue === 'label' && item[this.keyProps.label] === val) {
          data.value = item[this.keyProps.value]
        } else if (this.labelOrValue === 'value' && item[this.keyProps.value] === val) {
          data.label = this.replaceBracket ? item[this.keyProps.label].replace(reg, '') : item[this.keyProps.label]
        }
      }
      // 提交时也替换掉
      if (this.replaceBracket) {
        val = val.replace(reg, '')
      }
      this.$emit('selected', val)
      this.$emit('change', data)
    }
  },
  watch: {
    modelVal: {
      immediate: true,
      handler: function (val) {
        this.value = val
      }
    },
    data: {
      immediate: true,
      handler: function (val) {
        this.getDisplayVal('')
      }
    }
  }
}
</script>
<style scoped lang="scss">
.code {
  float: right;
  color: #8492a6;
  font-size: 13px;
}
.name {
  float: left;
}
/deep/.el-badge__content.is-fixed{
  top: 9px;
}
</style>
