<template>
  <section class="app-main">
    <transition name="fade" mode="out-in">
      <keep-alive :include="cacheNames">
        <router-view :key="$route.fullPath"></router-view>
      </keep-alive>
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  data: () => ({
    cacheNames: []
  }),
  watch: {
    $route: function () {
      this.cacheNames = this.$store.getters.cachedViews
    }
  }
}
</script>
