<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-date-range
             show-in-date-range
             show-se-date-range
             show-patient-num
             show-hos-dept
             showPagination
             :totalNum="total"
             headerTitle="查询条件"
             contentTitle="病例综合评分"
             :container="true"
             @query="handleSearchList">

      <template slot="extendFormItems">
        <el-form-item label="姓名">
          <el-input  v-model="listQuery.a11" placeholder="请输入患者姓名"></el-input>
        </el-form-item>
      </template>

      <template slot="buttons">
        <el-popconfirm
          confirm-button-text='确定'
          cancel-button-text='导出全部'
          icon="el-icon-info"
          icon-color="red"
          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>

      <template slot="containerContent">
        <div style="height:36%;width: 100%">
          <el-row style="height: 100%">
            <el-col :span="3" style="height: 100%">
              <div id="score1"  class="el-card is-always-shadow" style="height: 100%;width: 100%;">
                <div class="title">
                  <label>患者基本信息得分</label>
                </div>
                <div class="otherScoreImg">
                  <p style="color:#0099CC;font-size:30px;">{{baseInfoScore}}</p>
                  <p style="color:#0099CC;font-size:20px;">分</p>
                </div>
              </div>
            </el-col>
            <el-col :span="3" style="height: 100%">
              <div id="score2" class="el-card is-always-shadow" style="height: 100%;width: 100%">
                <div class="title">
                  <label>住院信息得分</label>
                </div>
                <div class="otherScoreImg">
                  <p style="color:#0099CC;font-size:30px;">{{inHosScore}}</p>
                  <p style="color:#0099CC;font-size:20px;">分</p>
                </div>
              </div>
            </el-col>

            <el-col :span="4" style="height: 100%">
              <div class="el-card is-always-shadow"  style="height: 100%;width: 100%">
                <div class="title">
                  <label>病案质量综合得分</label>
                </div>
                <div class="mainScoreImg">
                  <p style="color:#efb430;font-size:40px;">{{avgScore}}</p>
                </div>
              </div>
            </el-col>

            <el-col :span="3" style="height: 100%">
              <div id="score4" class="el-card is-always-shadow"  style="height: 100%;width: 100%">
                <div class="title">
                  <label>诊疗信息得分</label>
                </div>
                <div class="otherScoreImg">
                  <p style="color:#0099CC;font-size:30px;">{{diagnosisScore}}</p>
                  <p style="color:#0099CC;font-size:20px;">分</p>
                </div>
              </div>
            </el-col>
            <el-col :span="3" style="height: 100%">
              <div id="score5" class="el-card is-always-shadow" style="height: 100%;width: 100%">
                <div class="title">
                  <label>费用信息得分</label>
                </div>
                <div class="otherScoreImg">
                  <p style="color:#0099CC;font-size:30px;">{{costScore}}</p>
                  <p style="color:#0099CC;font-size:20px;">分</p>
                </div>
              </div>
            </el-col>

            <el-col :span="8" style="height: 100%">
              <div class="el-card is-always-shadow" style="height: 100%;width: 100%">
                <div class="title1">
                  <label>病案质量得分最差TOP10</label>
                </div>
                <div style="display: flex;height:80%;flex-direction: column;overflow:auto;">
                  <div v-for="(item, index) in lowTop10" style="display: flex;padding-left:10px;padding-right:10px;padding-top: 10px;" :key="index">
                    <div class='name'><span class="">{{(index+1)+"."}}</span><span>{{item.a11}}</span></div>
                    <div class='bar-back'>
                      <div class='bar-fore' :style='{width: (item.refer_sco / 100).toFixed(2) *100+ "%" }'></div>
                    </div>
                    <div class='score'>{{item.refer_sco}}分</div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="flex:1; overflow-y:auto;height: 62%;width: 100%">
          <el-table ref="settleListTable"
                    id="scoreTable"
                    size="mini"
                    :header-cell-style = "{'text-align' : 'center'}"
                    stripe
                    height="100%"
                    :data="list"
                    style="width: 100%;"
                    v-loading="listLoading"
                    border>
            <el-table-column
              label="序号"
              type="index"
              align="right"
              width="50">
            </el-table-column>
            <el-table-column label="查看扣分详情" align="left" width="100">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.refer_sco)<100">
                  <el-button type="info" style="background-color: rgba(57,74,94,0.8)" size="mini" icon="el-icon-search" @click="handleShowScoreDetail(scope.$index, scope.row)" circle>
                  </el-button>
                </div>
                <div v-if="Number(scope.row.refer_sco)==100">
                 <span style="font-size: 13px;">
                  无扣分
                </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="病案质量得分" prop="refer_sco" align="right" width="130" :show-overflow-tooltip='true' sortable>
              <template slot-scope="scope">{{ scope.row.refer_sco | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="扣分原因" prop="deduPointRea" align="left" :show-overflow-tooltip='true'>
              <template slot-scope="scope">
                <span v-if="scope.row.score=='0.00' && scope.row.loseScoreReason==null" style="color:red;">该病案未完成质检</span>
                <span v-else> {{ scope.row.deduPointRea | formatIsEmpty}}</span>
              </template>
            </el-table-column>
            <el-table-column label="病案号" prop="a48" align="right"  width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.a48 | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="姓名" prop="a11" align="left" width="70" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.a11 | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="性别" property="a12c" align="left" width="70" prop="a12c"
                             :filters="[{ text: '男', value: '1' }, { text: '女', value: '2' }]"
                             :filter-method="filterSex">
              <template slot-scope="scope">{{scope.row.a12c | formatA12c}}</template>
            </el-table-column>
            <el-table-column label="年龄" prop="a14" align="right" width="80" :show-overflow-tooltip="true" sortable>
              <template slot-scope="scope">{{scope.row.a14 | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="入院科室" prop="b13n" align="left" width="80" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.b13n | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="出院科室" prop="b16n" align="left" width="80" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.b16n | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="疾病主诊段" prop="c04n" align="left" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{ scope.row.c04n | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="手术主诊段" prop="c15x01n" align="left" width="100" :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.c15x01n | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="入院时间" prop="b12"  align="right" width="100" :show-overflow-tooltip="true" sortable>
              <template slot-scope="scope">{{scope.row.b12 | formatTime }}</template>
            </el-table-column>
            <el-table-column label="出院时间" prop="b15"  align="right" width="100" :show-overflow-tooltip="true" sortable>
              <template slot-scope="scope">{{scope.row.b15 | formatTime }}</template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, querySelectTreeAndSelectList } from '@/api/common/drgCommon'
import { fetchList as queryPageData, getScoreCountInfo, getScoreLowTopInfo } from '@/api/medicalQuality/score'
import { formatDate } from '@/utils/date'
import { elExportExcel } from '@/utils/exportExcel'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  cysj: null,
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date,
  a11: null,
  a48: null
}
export default {
  name: 'caseQualScore',
  components: { },
  inject: ['reload'],
  data () {
    return {
      depts: [],
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      list: null,
      total: null,
      cy_start_date: null,
      cy_end_date: null,
      a11: null,
      lowTop10: null,
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      cykb: null,
      baseInfoScore: null,
      inHosScore: null,
      avgScore: null,
      diagnosisScore: null,
      costScore: null,
      tableHeight: 0,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime (time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },
    formatA12c (value) {
      if (value == '1') {
        return '男'
      } else if (value == '2') {
        return '女'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.settleListTable.$el.offsetTop：表格距离浏览器的高度
      // 50表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.settleListTable.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.settleListTable.$el.offsetTop - 35
      }
    })
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
        this.getScoreCount()
        this.getScoreLowTop()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.a48 = this.listQuery.medcasCodg
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.a11 = this.listQuery.a11
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.ry_start_date = this.listQuery.inStartTime
      this.submitListQuery.ry_end_date = this.listQuery.inEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.settle_start_date = this.listQuery.seStartTime
      this.submitListQuery.settle_end_date = this.listQuery.seEndTime
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.settleListTable.$children, document.getElementById('scoreTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病案质控-病案得分')
    },
    getScoreCount () {
      this.listLoading = true
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.settle_start_date = this.listQuery.seStartTime
      this.submitListQuery.settle_end_date = this.listQuery.seEndTime
      this.submitListQuery.ry_start_date = this.listQuery.inStartTime
      this.submitListQuery.ry_end_date = this.listQuery.inEndTime
      getScoreCountInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        this.baseInfoScore = response.data.baseInfoScore.toFixed(0)
        this.inHosScore = response.data.inHosScore.toFixed(0)
        this.avgScore = response.data.avgScore.toFixed(0)
        this.diagnosisScore = response.data.diagnosisScore.toFixed(0)
        this.costScore = response.data.costScore.toFixed(0)
      })
    },
    getScoreLowTop () {
      this.listLoading = true
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.settle_start_date = this.listQuery.seStartTime
      this.submitListQuery.settle_end_date = this.listQuery.seEndTime
      this.submitListQuery.ry_start_date = this.listQuery.inStartTime
      this.submitListQuery.ry_end_date = this.listQuery.inEndTime
      getScoreLowTopInfo(this.submitListQuery).then(response => {
        this.listLoading = false
        this.lowTop10 = response.data
      })
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.handleSearchList()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.getList()
      this.getScoreCount()
      this.getScoreLowTop()
    },
    handleResetSearch () {
      this.getDataIsuue()
    },
    handleShowScoreDetail (index, row) {
      this.$router.push({ path: '/caseQual/caseQualScoreDet', query: { id: row.id, a11: row.a11, deduPointRea: row.deduPointRea } })
    },
    filterSex (value, row) {
      return row.a12c === value
    },
    exportExcel () {
      let tableId = 'scoreTable'
      let fileName = '病案质控-病案得分'
      elExportExcel(tableId, fileName)
    },
    refresh () {
      this.reload()
    }
  }
}
</script>
<style scoped>
  .title{
    height: 14%;
    display: flex;
    flex-direction: column;
    align-content: center;
    align-items: center;
    margin-bottom: 4px;
    background-color: rgba(57,74,94,0.8);
  }
  .title>label{
    margin-top: 5px;
    display: flex;
    text-align: center;
    overflow: hidden;
    white-space: pre-wrap;
    text-overflow: ellipsis;
    font-size: 14px;
    color:#FFFFFF;
    font-weight: bold;
  }
  .title1{
    height: 14%;
    display: flex;
    flex-direction: column;
    align-content: center;
    align-items: center;
    margin-bottom: 4px;
    background-color:rgba(57,74,94,0.8) ;
  }
  .title1>label{
    margin-top: 5px;
    display: flex;
    text-align: center;
    overflow: hidden;
    white-space: pre-wrap;
    text-overflow: ellipsis;
    font-size: 14px;
    color:#FFFFFF;
    font-weight: bold;
  }
  .mainScoreImg{
    height: 130px;
    width: 130px;
    position: relative;
    display: flex;
    margin: auto;
    margin-top:10px;
    align-items: center;
    align-content: center;
    justify-content: center;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background: url(../../../assets/images/medicalQuality/mainScore.png) center bottom / 100% no-repeat;
  }
  .otherScoreImg{
    height: 100px;
    width: 100px;
    position: relative;
    display: flex;
    margin: auto;
    margin-top:30px;
    align-items: center;
    align-content: center;
    justify-content: center;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background: url(../../../assets/images/medicalQuality/otherScore.png) center bottom / 100% no-repeat;
  }
  /*右侧得分top10*/
  .name{
    width:15%;
    font-size:10px;
    line-height: 15px;
    margin-left:10px;
  }
  .score{
    width:15%;
    font-size:13px;
    line-height: 10px;
    margin-left:20px;
  }
 .bar-back{
    width: 75%;
    height: 8px;
    background: #e0e5ef left center  no-repeat;
    border-radius: 6px;
  }
  .bar-fore{
    height: 100%;
    background-position:left center;
    background-repeat: no-repeat;
    background-color: #2687F2;
    border-radius: 6px;
  }

</style>
