<template>
  <div style="width: 100%; height: 85%;overflow: auto">
    <div class="som-wd-one-hundred" style="display: flex;position: relative">
      <!-- 科室 -->
      <div style="width: 30%;height: 100%">
        <drg-title-line title="科室信息" font-size="12px">
          <template slot="rightSide">
            <div class="title-line-title">
              <div>
                科室数量：{{ deptTableData.length }}
              </div>
              <el-button type="primary" circle icon="el-icon-upload" @click="deptUpload"></el-button>
            </div>
          </template>
        </drg-title-line>
        <el-table :data="deptTableData" height="90%">
          <el-table-column prop="code" label="科室编码" ></el-table-column>
          <el-table-column prop="name" label="科室名称" ></el-table-column>
        </el-table>
      </div>
      <!-- 医生 -->
      <div style="width: 30%;height: 100%;margin-left: 2%">
        <drg-title-line title="医生信息" font-size="12px">
          <template slot="rightSide">
            <div class="title-line-title">
              <div>
                医生数量：{{ doctorTableData.length }}
              </div>
              <el-button type="primary" circle icon="el-icon-upload" @click="doctorUpload"></el-button>
            </div>
          </template>
        </drg-title-line>
        <el-table :data="doctorTableData" height="90%">
          <el-table-column prop="workerCode" label="医生编码" ></el-table-column>
          <el-table-column prop="workerName" label="医生名称" ></el-table-column>
        </el-table>
      </div>

      <div style="width: 38%;height: 100%;margin-left: 2%">
        <el-alert :title="text"
                  :description="desc"
                  show-icon
                  center
                  :closable="false"
                  :type="type"></el-alert>

        <div class="switch">
          <!-- 开关 -->
          <el-switch
            v-model="switchExtract"
            active-text="抽取"
            inactive-text="不抽取">
          </el-switch>

          <!-- 选择医生抽取类型 -->
          <el-radio-group v-model="extractDoctorRadio" v-if="switchExtract">
            <el-radio :label="1">用户名生成医生账号</el-radio>
            <el-radio :label="2">员工编号生成医生账号</el-radio>
          </el-radio-group>
        </div>

      </div>

      <el-dialog
        title="上传文件"
        :z-index="1000"
        :visible.sync="uploadVisible"
        width="50%">
        <el-upload
          style="text-align: center"
          drag
          ref="upload"
          :limit="1"
          action="customize"
          accept=".xlsx,.xls"
          :http-request="upload">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件，
            <span style="color: #0a84ff;cursor: pointer" @click="templateDownload">点击下载模板</span>
          </div>
        </el-upload>
      </el-dialog>

      <!-- 加载 -->
      <div v-loading="loading" style="height: 100%;width: 100%;position: absolute;" v-show="loading"></div>
    </div>
    <!-- 按钮 -->
    <div style="position: absolute; bottom: 5%;right: 2%">
      <el-button type="primary" @click="prevStep" v-if="!this.$somms.hasHosRole()">上一步</el-button>
      <el-button type="primary" :disabled="nextDisabled" @click="next">{{ nextText }}</el-button>
    </div>
  </div>
</template>
<script>
import { queryDeptAndDoctorInfo } from '@/api/hospitalEnvGenerate'
import { extractDept } from '@/api/orgManamement'
import { uploadDeptFile, workersUpload } from '@/api/upload/upload'
import { downloadDeptTemplate } from '@/api/download/download'
import { downloadworkersTemplate } from '@/api/download/workersdownload'

export default {
  name: 'hospitalEnvGenerateStep2',
  props: {
    step: {
      type: Number
    },
    hospitalInfo: {
      type: Object,
      default: () => {}
    }
  },
  mounted () {

  },
  data: () => ({
    text: '提示',
    desc: '',
    type: 'success',
    nextText: '下一步',
    uploadType: '',
    nextDisabled: false,
    switchExtract: true,
    incomplete: false,
    loading: false,
    uploadVisible: false,
    executeExtract: false,
    deptTableData: [],
    doctorTableData: [],
    extractDoctorRadio: 1
  }),
  methods: {
    // 初始化
    init () {
      queryDeptAndDoctorInfo({ ...this.hospitalInfo }).then(res => {
        this.deptTableData = []
        this.doctorTableData = []
        if (res.data) {
          this.deptTableData = res.data.deptList
          this.doctorTableData = res.data.doctorList
        }
        this.initDesc()
      })
    },
    // 初始化描述
    initDesc () {
      this.incomplete = false
      let desc = this.hospitalInfo.medinsName
      let deptNum = this.deptTableData.length
      let doctorNum = this.doctorTableData.length
      let tempDesc = ''
      let type = 'success'
      let warningFlag = false
      // 科室
      if (deptNum == 0) {
        tempDesc = tempDesc + ',未查询到科室。'
        warningFlag = true
      } else {
        tempDesc = tempDesc + '科室数为：' + deptNum + '。'
      }

      // 医生
      if (doctorNum == 0) {
        tempDesc = tempDesc + ',未查询到医生。'
        warningFlag = true
      } else {
        tempDesc = tempDesc + '医生数为：' + doctorNum + '。'
      }

      if (warningFlag) {
        type = 'warning'
        desc = desc + '信息不完整。'
        this.incomplete = true
      } else {
        desc = desc + '信息完整，可以抽取。'
        this.incomplete = false
      }
      this.desc = desc + tempDesc
      this.type = type
    },
    // 上一步
    prevStep () {
      this.$emit('prevStep', null)
    },
    // 下一步
    next () {
      let message = ''
      if (this.incomplete && this.switchExtract) {
        message = '当前信息不完整，是否执行抽取？'
      }
      if (!this.incomplete && !this.switchExtract) {
        message = '当前信息完整，是否不执行抽取？'
      }
      if (message) {
        this.$confirm(message, '提示', {
          type: 'warning'
        }).then(() => {
          this.extract()
        })
      } else {
        this.extract()
      }
    },
    // 抽取
    extract () {
      if (this.switchExtract) {
        this.loading = true
        extractDept(
          {
            orgId: this.hospitalInfo.hospitalId,
            hospitalId: this.hospitalInfo.hospitalId,
            username: this.hospitalInfo.username,
            type: this.extractDoctorRadio
          }
        ).then(res => {
          this.$message({
            type: 'success',
            message: '抽取成功'
          })
          this.loading = false
          this.executeExtract = true
          this.success()
        }).catch(() => {
          this.loading = false
        })
      } else {
        this.executeExtract = false
        this.success()
      }
    },
    // 上传
    upload (data) {
      if (this.uploadType && this.uploadType == 'dept') {
        this.executeUpload(data, uploadDeptFile)
      } else if (this.uploadType && this.uploadType == 'doctor') {
        this.executeUpload(data, workersUpload)
      }
    },
    // 执行上传
    executeUpload (data, method) {
      let params = new FormData()
      params.append('file', data.file)
      params.append('hospitalId', this.hospitalInfo.hospitalId)
      method(params).then(res => {
        if (res.code == 200) {
          this.uploadVisible = false
          this.$refs.upload.clearFiles()
          this.$message.success('上传成功')
          this.init()
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
        this.init()
      })
    },
    // 科室上传
    deptUpload () {
      this.uploadVisible = true
      this.uploadType = 'dept'
    },
    // 医生上传
    doctorUpload () {
      this.uploadVisible = true
      this.uploadType = 'doctor'
    },
    // 模板下载
    templateDownload () {
      if (this.uploadType && this.uploadType == 'dept') {
        downloadDeptTemplate().then(res => {
          this.$somms.download(res, '科室模板', 'application/vnd.ms-excel')
        })
      } else if (this.uploadType && this.uploadType == 'doctor') {
        downloadworkersTemplate().then(res => {
          this.$somms.download(res, '医护人员信息模板', 'application/vnd.ms-excel')
        })
      }
    },
    // 完成
    success () {
      let params = {}
      let message = ''
      if (this.incomplete) {
        message = '信息不完整'
      } else {
        message = '信息完整'
      }
      if (this.executeExtract) {
        message = message + '，已执行抽取'
      } else {
        message = message + '，未执行抽取'
      }
      params.msg = message
      this.$emit('success', params)
    }
  },
  watch: {
    step: {
      immediate: true,
      handler: function (val) {
        if (val && val == 1) {
          this.init()
        }
      }
    },
    incomplete (val) {
      this.switchExtract = !val
    },
    switchExtract: {
      immediate: true,
      handler: function (val) {
        if (val) {
          this.nextText = '抽取'
        } else {
          this.nextText = '下一步'
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
.switch {
  width: 100%;
  height: 60%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  &> .el-switch{
    height: 20%;
  }
}
.title-line-title{
  display: flex;
  align-items: center;

  &>div {
    margin-right: 0.5rem;
  }
}
/deep/ .util{
  top: 0;
}
</style>
