<template>
  <div style="width: 100%; height: 80%;display: flex;">
    <div style="width: 20%;height: 100%;">
      <drg-title-line title="年份选择"/>
      <div style="width: 100%;height: 80%">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="选择年" @change="yearChange">
        </el-date-picker>
      </div>
    </div>

    <div class="item">
      <drg-title-line title="区域标杆">
        <template slot="rightSide">
          <el-button type="primary" circle icon="el-icon-upload" @click="areaUpload"></el-button>
        </template>
      </drg-title-line>
      <!-- 上传区域数据 -->
      <el-dialog
        :z-index="1000"
        title="上传文件"
        :visible.sync="areaVisible"
        width="50%">
        <el-upload
          style="text-align: center"
          drag
          ref="upload"
          :limit="1"
          action="customize"
          accept=".xlsx,.xls"
          :http-request="upload">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件
            <span style="color: #0a84ff;cursor: pointer" @click="areaTemplateDownload">点击下载模板</span>
          </div>
        </el-upload>
      </el-dialog>

      <el-alert :title="areaTitle"
                :description="areaDesc"
                show-icon
                center
                :closable="false"
                :type="areaType"></el-alert>
    </div>

    <div class="item">
      <drg-title-line title="医院标杆">
        <template slot="rightSide">
          <el-button type="primary"  @click="generateHosBenchmark" v-if="existsAreaData">生成</el-button>
        </template>
      </drg-title-line>
      <el-alert :title="curTitle"
                :description="curDesc"
                show-icon
                center
                :closable="false"
                :type="curType"></el-alert>
    </div>
    <!-- 按钮 -->
    <div style="position: absolute; bottom: 5%;right: 2%">
      <el-button type="primary" @click="prevStep">上一步</el-button>
      <el-button type="primary" :disabled="nextDisabled" @click="next">{{ nextText }}</el-button>
    </div>
  </div>
</template>
<script>
import {
  areaBenchmarkUpload,
  downloadAreaBenchmarkTemplate,
  queryAreaBenchmarkInfo
} from '@/api/dataConfig/areaBenchmarkConfig'
import { selectDip as queryHosBenchmark } from '@/api/dataConfig/benchmarkConfig'
import { generateHosBenchmark } from '@/api/hospitalEnvGenerate'

import moment from 'moment'

export default {
  name: 'hospitalEnvGenerateStep4',
  props: {
    step: {
      type: Number
    },
    hospitalInfo: {
      type: Object,
      default: () => {}
    }
  },
  mounted () {

  },
  data: () => ({
    nextDisabled: false,
    areaVisible: false,
    nextText: '下一步',
    year: new Date(),
    areaTitle: '',
    areaDesc: '',
    areaType: '',
    existsAreaData: false,
    existsHosData: false,
    curTitle: '',
    curDesc: '',
    curType: ''
  }),
  methods: {
    // 初始化
    init () {
      let params = this.getParams()
      queryAreaBenchmarkInfo(params).then(res => {
        if (res.data.list.length == 0) {
          this.alterTip('area', params.year, '无区域', 'warning')
          this.existsAreaData = false
        } else {
          this.alterTip('area', params.year, '存在区域', 'success')
          this.existsAreaData = true
        }
      })

      queryHosBenchmark(params).then(res => {
        if (res.data.list.length == 0) {
          this.alterTip('cur', params.year, '无医院', 'warning')
          this.existsHosData = false
        } else {
          this.alterTip('cur', params.year, '存在医院', 'success')
          this.existsHosData = true
        }
      })
    },
    // 提示
    alterTip (prefix, year, scope, type) {
      this[prefix + 'Title'] = '提示'
      this[prefix + 'Desc'] = '当前年份[' + year + ']' + scope + '标杆'
      this[prefix + 'Type'] = type
    },
    // 年份改变
    yearChange () {
      this.init()
    },
    // 生成医院数据
    generateHosBenchmark () {
      let prefix = this.existsHosData ? '当前标杆已存在，是否覆盖且' : ''
      this.$confirm(prefix + '是否以【' + this.getParams().year + '】区域数据生成【' + this.hospitalInfo.medinsName + '】标杆数据？',
        '提示', { type: 'warning' }).then(_ => {
        generateHosBenchmark(this.getParams()).then(res => {
          this.$message({
            message: '生成成功',
            type: 'success'
          })
          this.init()
        })
      })
    },
    // 区域数据上传
    areaUpload () {
      if (this.existsAreaData) {
        this.$confirm('【' + this.getParams().year + '】年已存在数据，是否覆盖上传？',
          '提示', { type: 'warning' }).then(_ => {
          this.areaVisible = true
        })
      } else {
        this.areaVisible = true
      }
    },
    upload (data) {
      let params = new FormData()
      params.append('file', data.file)
      params.append('group', this.getParams().group)
      areaBenchmarkUpload(params).then(res => {
        if (res.code == 200) {
          this.areaVisible = false
          this.$refs.upload.clearFiles()
          this.$message.success('上传成功')
          this.init()
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
      })
    },
    // 区域模板下载
    areaTemplateDownload () {
      let params = { group: this.getParams().group }
      downloadAreaBenchmarkTemplate(params).then(res => {
        this.$somms.download(res, '区域标杆模板' + this.$somms.getGroupType() == '1' ? '(DIP)' : '(DRG)', 'application/vnd.ms-excel')
      })
    },
    // 获取参数
    getParams () {
      let params = {
        year: moment(this.year).format('yyyy'),
        group: this.$somms.getGroupType(),
        pageSize: 50,
        pageNum: 1,
        hospitalId: this.hospitalInfo.hospitalId
      }
      params.standardYear = params.year
      params.queryType = params.group
      return params
    },
    // 上一步
    prevStep () {
      this.$emit('prevStep', this.$somms.hasHosRole() ? 2 : 1)
    },
    next () {
      this.success()
    },
    success () {
      this.$emit('success', { msg: '标杆配置完成' })
    }
  },
  watch: {
    step: {
      immediate: true,
      handler: function (val) {
        if (val && val == 3) {
          this.init()
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
.item{
  width: 30%;
  height: 100%;
  margin-left: 2%;
}
</style>
