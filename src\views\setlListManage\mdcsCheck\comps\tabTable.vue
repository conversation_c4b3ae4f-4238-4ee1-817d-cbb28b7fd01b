<template>
  <el-table :id="id"
            ref="elTable"
            :data="data"
            v-loading="loading"
            height="100%"
            :header-cell-style="{'text-align':'center'}"
            border>
    <el-table-column label="序号" type="index" align="center"></el-table-column>
    <el-table-column label="错误" prop="errDscr" :show-overflow-tooltip="true"></el-table-column>
    <el-table-column label="病案号" prop="bah"></el-table-column>
    <el-table-column label="姓名" prop="name"></el-table-column>
    <el-table-column label="出院科别" prop="deptName"></el-table-column>
    <el-table-column label="医生姓名" prop="drName"></el-table-column>
    <el-table-column label="出院时间" prop="outHosTime"></el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'tabTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    id: {
      type: String
    }
  },
  updated () {
    this.refreshTable()
  },
  methods: {
    refreshTable () {
      this.$nextTick(() => {
        this.$refs.elTable.doLayout()
      })
    },
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    }
  }
}
</script>

<style scoped>

</style>
