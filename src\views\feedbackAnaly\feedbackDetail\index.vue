<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             ref="form"
             :show-date-range="{ show: true, clearable: true }"
             :show-se-date-range="{ show: true, clearable: true }"
             show-patient-num
             :container="true"
              :showCoustemContentTitle="true"
             :showPagination="true"
             :totalNum="total"
             :exportExcel="{ 'tableId': tableId, exportName: '反馈数据详情'}"
             :exportExcelFun="queryFeedbackDetail2"
             headerTitle="查询条件"
             @query="queryData"
             @reset="reset">

      <template slot="extendFormItems">
        <el-form-item label="批次号" prop="batchNum">
          <el-input v-model="queryForm.batchNum" placeholder="请输入批次号" class="som-form-item"/>
        </el-form-item>
      </template>

      <!-- 按钮 -->
      <template #buttons>
        <el-button class="som-button-margin-right" type="primary" @click="feedbackCalculate">根据反馈病组测算</el-button>
      </template>

      <template slot="contentTitle">
        <drg-title-line title="数据列表">
          <template #rightSide>
            <span v-if="showContrast">
              城乡居民：<el-input-number v-model="calculatePriceCx" :precision="2" :step="0.1" :max="100"></el-input-number>
              城镇职工：<el-input-number v-model="calculatePriceCz" :precision="2" :step="0.1" :max="100"></el-input-number>
              其他：<el-input-number v-model="calculatePrice" :precision="2" :step="0.1" :max="100"></el-input-number>
            </span>
            <el-button @click="contrastClick">
              {{ showContrast ? '返回' : '分组结果对比' }}
            </el-button>
          </template>
        </drg-title-line>
      </template>

      <template slot="containerContent">
        <el-table :data="tableData"
                  ref="dataTable"
                  id="dataTable"
                  height="100%"
                  border
                  highlight-current-row
                  size="mini"
                  :header-cell-style="{'text-align':'center'}"
                  v-if="!showContrast"
                  v-loading="tableLoading">
          <el-table-column type="index" label="序号" align="center" />
          <el-table-column  prop="disGpCodg" width="140" label="病组编码" align="left" show-overflow-tooltip />
          <el-table-column  prop="disGpName" width="140" label="病组名称" align="left" show-overflow-tooltip />
          <el-table-column  prop="mdtrtId" width="230" label="就诊ID" align="left" />
          <el-table-column  prop="medcasNo" label="病案号" width="140" align="left" />
          <el-table-column  prop="psmName" label="姓名" width="140" align="left" />
<!--          <el-table-column  prop="asstListAgeGrp" label="年龄辅助目录" align="left" />-->
<!--          <el-table-column  prop="asstListDiseSevDeg" label="疾病辅助目录" align="left" />-->
<!--          <el-table-column  prop="isUsedAsstList" label="肿瘤辅助目录" align="left" />-->
          <el-table-column  prop="admTime" label="入院时间" width="100" align="left" />
          <el-table-column  prop="dscgTime" label="出院时间" width="100" align="left" />
          <el-table-column  prop="setlTime" label="结算时间" width="100" align="left" />
          <el-table-column  prop="deptName" label="出院科室" align="left"  />
          <el-table-column  prop="drName" label="医生名称" align="left"  />
          <el-table-column  prop="setlPtNum" label="结算点数" align="left" />
          <el-table-column  prop="ptNumFee" width="100" label="每点数费用" align="left" />
          <el-table-column  prop="sumfee" label="总费用" align="left" />
          <el-table-column  prop="poolFee" label="统筹费用" align="left" />
          <el-table-column  prop="disGpSumfee" width="100" label="病组总费用" align="left" />
          <el-table-column  prop="disGpPoolFee" width="100" label="病组统筹费用" align="left" />
          <el-table-column  prop="setlPatnType" width="100" label="结算患者类型" align="left" />
          <el-table-column  prop="insuredType" label="参保类型" align="left" />
          <drg-table-column  prop="isInGroup" label="是否入组" align="left" dicType="BOOLEAN"/>
          <el-table-column  prop="batchNum" width="200" label="批次号" align="left" show-overflow-tooltip />
        </el-table>
        <div style="width: 100%;height: 100%" v-if="showContrast">
          <el-card title="汇总信息" style="height: 10%">
            <el-alert
              :closable="false"
              type="success">
              <template slot="profttl">
                反馈总病例数：<el-tag>{{ summary.totalNum }}</el-tag>
                与系统匹配病例数：<el-tag>{{ summary.matchedNum }}</el-tag>
                不匹配病例数：<el-tag>{{ summary.nonMatchedNum }}</el-tag>
                组匹配病例数：<el-tag>{{ summary.groupMatchedNum }}</el-tag>
                病例数匹配率：<el-tag>{{ summary.matchedRate }}%</el-tag>
                组病例数匹配率：<el-tag>{{ summary.groupMatchedRate }}%</el-tag>
              </template>
            </el-alert>
          </el-card>
          <el-table :data="tableData" v-loading="tableLoading" ref="dataTable"
                    id="dataTable" height="90%">
            <el-table-column type="index" label="序号" align="center" />
            <el-table-column  prop="mdtrtId" label="就诊ID" align="left"  />
            <el-table-column  prop="a48" label="病案号" align="left"  />
            <el-table-column  prop="insuredType" label="参保类型" align="left"  />
            <el-table-column  prop="deptName" label="出院科室" align="left"  />
            <el-table-column  prop="drName" label="医生名称" align="left"  />
            <el-table-column  prop="sysGroupCode" label="系统入组编码" width="140" align="left" show-overflow-tooltip/>
            <el-table-column  prop="sysGroupName" label="系统入组名称" width="140" align="left" show-overflow-tooltip/>
            <el-table-column  prop="sysAuxiliaryAge" label="系统辅助目录-年龄" width="140" align="left" show-overflow-tooltip />
            <el-table-column  prop="sysAuxiliaryIllness" label="系统辅助目录-疾病" width="140" align="left" show-overflow-tooltip/>
            <el-table-column  prop="sysAuxiliaryTumour" label="系统辅助目录-肿瘤" width="140" align="left" show-overflow-tooltip/>
            <el-table-column  prop="disGpCodg" width="140" label="病组编码" align="left" show-overflow-tooltip />
            <el-table-column  prop="disGpName" width="140" label="病组名称" align="left" show-overflow-tooltip />
            <el-table-column  prop="asstListAgeGrp" width="140" label="辅助目录-年龄" align="left" show-overflow-tooltip />
            <el-table-column  prop="asstListDiseSevDeg" width="140" label="辅助目录-肿瘤" align="left" show-overflow-tooltip />
            <el-table-column  prop="asstListTmorSevDeg" width="140" label="辅助目录-肿瘤" align="left" show-overflow-tooltip />
            <el-table-column  prop="sysSelPoint" width="140" label="系统预测分值" align="left" show-overflow-tooltip />
            <el-table-column  prop="setlPtNum" width="140" label="反馈分值" align="left" show-overflow-tooltip />
            <el-table-column  prop="ptNumFee" width="140" label="每分值费用" align="left" show-overflow-tooltip />
            <el-table-column  prop="sysTotalCost" width="140" label="住院总费用" align="left" show-overflow-tooltip />
            <el-table-column  prop="disGpSumfee" width="140" label="病组总费用" align="left" show-overflow-tooltip />
            <el-table-column  prop="balance" width="140" label="费用差异" align="left" show-overflow-tooltip />
            <el-table-column  prop="matched" width="140" label="是否匹配" align="center">
              <template slot-scope="scope">
                <el-tag :type="scope.row.matched === '1' ? 'success' : 'danger'">{{ scope.row.matched === '1' ? '是' : '否' }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryFeedbackDetail2, queryFeedbackDetail2Summary, feedbackExtract } from '@/api/newBusiness/newBusinessUpload'
import { queryData as queryPoint } from '@/api/dataConfig/dipConfig'
export default {
  name: 'feedbackDetail',
  data: () => ({
    queryForm: {
      medcasCodg: '',
      batchNum: ''
    },
    summary: {
      totalNum: 0,
      matchedNum: 0,
      nonMatchedNum: 0,
      groupMatchedNum: 0,
      matchedRate: 0,
      groupMatchedRate: 0
    },
    total: 0,
    calculatePriceCz: 0,
    calculatePriceCx: 0,
    calculatePrice: 0,
    tableId: 'dataTable',
    tableData: [],
    tableLoading: false,
    showContrast: false
  }),
  mounted () {
    if (this.$route.query.batchNum) {
      this.queryForm.batchNum = this.$route.query.batchNum
      this.$nextTick(() => {
        this.$refs.form.clearTime()
      })
      this.queryData()
    }
    queryPoint({ dataAuth: true }).then(data => {
      for (let a in data.data) {
        if (data.data[a].key == 'CX_PRICE') {
          this.calculatePriceCx = data.data[a].value
        }
        if (data.data[a].key == 'CZ_PRICE') {
          this.calculatePriceCz = data.data[a].value
        }
        if (data.data[a].key == 'PRICE') {
          this.calculatePrice = data.data[a].value
        }
      }
    })
  },
  methods: {
    queryFeedbackDetail2,
    queryData () {
      this.tableLoading = true
      queryFeedbackDetail2(this.getParams()).then(res => {
        if (res.code == 200 && res.data) {
          this.tableData = res.data.list
          this.total = res.data.total
          this.tableLoading = false
        }
      })
      this.querySummary()
    },
    reset () {
      this.queryForm.batchNum = ''
    },
    // 获取参数
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.czPrice = this.calculatePriceCz
      params.cxPrice = this.calculatePriceCx
      params.price = this.calculatePrice
      return params
    },
    // 查询汇总
    querySummary () {
      queryFeedbackDetail2Summary(this.getParams()).then(res => {
        Object.assign(this.summary, res.data)
      })
    },
    contrastClick () {
      this.showContrast = !this.showContrast
      if (this.showContrast) {
        this.querySummary()
      }
    },
    feedbackCalculate () {
      this.tableLoading = true
      feedbackExtract(this.getParams()).then(res => {
        this.tableLoading = false
        this.queryData()
        this.$message.success('测算完成')
      })
    }
  }
}
</script>
