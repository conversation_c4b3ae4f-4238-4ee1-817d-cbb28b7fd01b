<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-date-range
             show-in-date-range
             show-se-date-range
             :show-hos-dept="{ show: this.$somms.hasHosRole()}"
             show-pagination
             :show-dip="{ show: showDip }"
             :show-drg="{ show: showDrg }"
             :show-cd="{ show: showCd }"
             :totalNum="total"
             :container="true"
              :showCoustemContentTitle="true"
             headerTitle="查询条件"
             @query="queryData"
             @reset="clearRouteQuery">
      <template slot="extendFormItems" >
<!--        <el-form-item label="组">-->
<!--          <el-select  v-model="queryForm.group"-->
<!--                      placeholder="请选择组"-->
<!--                      class="som-form-item"-->
<!--                      @change="typeConflict()">-->
<!--            <el-option-->
<!--              v-for="item in groupOptions"-->
<!--              :key="item.value"-->
<!--              :label="item.label"-->
<!--              :value="item.value">-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item label="费用区间" prop="costSection">
          <drg-dict-select v-model="queryForm.costSection" placeholder="请选择费用区间" @change="queryData" dicType="COSTRANGE"/>
        </el-form-item>
        <el-form-item label="最低倍率" >
          <el-input-number v-model="queryForm.num1" :precision="2" :step="0.1" :max="10" @change="changeNum"></el-input-number>
        </el-form-item>
        <el-form-item label="最高倍率" >
          <el-input-number v-model="queryForm.num2" :precision="2" :step="0.1" :max="10" @change="changeNum"></el-input-number>
        </el-form-item>
        <el-form-item label="自定义" >
          <el-input-number v-model="queryForm.num3" :precision="2" :step="0.1" :max="10" @change="changeNum"></el-input-number>
        </el-form-item>
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
<!--         散点图 -->
        <div id="splashes"  style="height: 100%;width: 100%">
          <drg-echarts :options="splashesOptions" @chartClick="splashesClick" ref="splashesChart"/>
        </div>
      </template>
      <!-- 内容 profttl -->
      <template slot="contentTitle">
        <drg-title-line title="病组倍率区间分析">
          <template slot="rightSide">
            <span style="font-size: 12px;">姓名：</span>
              <el-select v-model="nameTable" placeholder="请选择姓名"
                         collapse-tags
                         multiple
                         filterable
                         clearable
                         @change="changeSelectName"
                         style="width: 82%">
                <el-option
                  v-for="item in twoTableData"
                  :key="item.patientId"
                  :label="item.name"
                  :value="item.patientId">
                  <span style="position: absolute;right: 21%; color: #8492a6; font-size: 13px">{{ item.name }}</span>
                  <span style="float: left">{{ item.patientId }}</span>
                </el-option>
              </el-select>
          </template>
        </drg-title-line>
      </template>
    </drg-form>
  </div>
</template>
<script>

import { queryEnableGroup } from '@/api/common/sysCommon'
import { queryData } from '@/api/operationalDecision/analysis/operationalDecisionPplAnalysis'
import { cacheMixin } from '@/utils/mixin'
export default {
  name: 'diseExtrDetail',
  // mixins: [cacheMixin],
  data: () => ({
    queryForm: {
      group: '1',
      costSection: '',
      num1: '0.5',
      num2: '2.0',
      num3: '1.0'
    },
    tableData: [],
    tableLoading: false,
    total: 0,
    splashesOptions: {},
    curCheckedTag: 'dipData',
    showDept: true,
    showDip: true,
    showDrg: false,
    showCd: false,
    groupOptions: [1],
    nameTable: [],
    temporaryData: [],
    twoTableData: []
  }),
  created () {
    if (this.$route.query.group == '1') {
      this.queryForm.begnDate = this.$route.query.begnDate
      this.queryForm.expiDate = this.$route.query.expiDate
      this.queryForm.dateRange = this.$route.query.dateRange
      this.queryForm.group = this.$route.query.group
      this.queryForm.dipCodg = this.$route.query.dipCodg
      this.queryForm.deptCode = this.$route.query.deptCode
      this.showDip = true
      this.showDrg = false
      this.showCd = false
    } else if (this.$route.query.group == '3') {
      this.queryForm.begnDate = this.$route.query.begnDate
      this.queryForm.expiDate = this.$route.query.expiDate
      this.queryForm.dateRange = this.$route.query.dateRange
      this.queryForm.group = this.$route.query.group
      this.queryForm.drgCodg = this.$route.query.drgCodg
      this.queryForm.deptCode = this.$route.query.deptCode
      this.showDip = false
      this.showDrg = true
      this.showCd = false
    } else if (this.$route.query.group == '2') {
      this.queryForm.begnDate = this.$route.query.begnDate
      this.queryForm.expiDate = this.$route.query.expiDate
      this.queryForm.dateRange = this.$route.query.dateRange
      this.queryForm.group = this.$route.query.group
      this.queryForm.cdCodg = this.$route.query.cdCodg
      this.queryForm.deptCode = this.$route.query.deptCode
      this.queryForm.num1 = 0.7
      this.queryForm.num2 = 1.4
      this.showDip = false
      this.showDrg = false
      this.showCd = true
    }
  },
  mounted () {
    this.getEnabledGroup()
    if (!this.$somms.hasDeptRole()) {
      this.showDept = false
    }
    this.queryForm.group = this.$somms.getGroupType()
    this.typeConflict()
    this.$nextTick(() => {
      if ((this.queryForm.group == '1' && this.queryForm.dipCodg != '') ||
        (this.queryForm.group == '3' && this.queryForm.drgCodg != '') ||
        (this.queryForm.group == '2' && this.queryForm.cdCodg != '')) {
        this.queryData()
      }
    })
  },
  methods: {
    queryData () {
      let params = this.getParams()
      // if (!params.dipCodg && !params.drgCodg) {
      //   return
      // }
      const body = {
        ...this.getParams(),
        dipCode: this.getParams().dipCodg,
        drgCode: this.getParams().drgCodg
      }
      this.tableLoading = true
      queryData(body).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.total
          this.tableLoading = false
          this.temporaryData = res.data.list
          this.twoTableData = res.data.list
          this.createSplashes()
        }
      })
    },
    clearRouteQuery () {
      if (this.$route.query) {
        this.$router.push({ query: {} }).catch(() => {})
      }
    },
    changeNum () {
      this.createSplashes()
    },
    changeSection () {
      this.createSplashes()
    },
    changeSelectName () {
      if (this.nameTable.length == 0) {
        this.tableData = this.temporaryData
      } else {
        this.tableData = this.temporaryData.filter(item =>
          this.nameTable.includes(item.patientId)
        )
      }
      this.createSplashes()
    },
    // selectName(){
    //   if(this.nameTable.length==0){
    //     this.twoTableData=this.temporaryData
    //   }else {
    //     this.twoTableData =this.temporaryData.filter(item=>
    //       this.nameTable.includes(item.patientId))
    //   }
    createSplashes () {
      let queryData = []
      let deptNames = []
      let minFee = []
      let standardFee = []
      let highFee = []
      let pplCost = []
      let exceptCost = []
      if (this.tableData.length > 0) {
        this.tableData.map(data => {
          deptNames.push(data.deptName)
          minFee.push(data.min)
          standardFee.push(data.standardFee)
          highFee.push(data.max)
          pplCost.push(data.sumfee)
          exceptCost.push(data.exceptCost)
          if (this.queryForm.group == 1) {
            queryData.push([data.exceptCost, data.sumfee, data.dipCodg, data.dipName, data.name, data.sumfee, data.patientId, this.queryForm.group, this.queryForm.begnDate, this.queryForm.expiDate, this.queryForm.dateRange])
          } else if (this.queryForm.group == 3) {
            queryData.push([data.exceptCost, data.sumfee, data.drgCodg, data.drgName, data.name, data.sumfee, data.patientId, this.queryForm.group, this.queryForm.begnDate, this.queryForm.expiDate, this.queryForm.dateRange])
          } else if (this.queryForm.group == 2) {
            queryData.push([data.exceptCost, data.sumfee, data.cdCodg, data.cdName, data.name, data.sumfee, data.patientId, this.queryForm.group, this.queryForm.begnDate, this.queryForm.expiDate, this.queryForm.dateRange])
          }
        })
      }
      let MAX = ''
      let MIN = ''
      let XMIN = ''
      if (this.queryForm.costSection == 1) {
        MAX = Math.ceil(Math.max(...pplCost) / 1000) * 1000
        MIN = Math.floor(Math.max(...highFee) / 1000) * 1000
        XMIN = 1.5
      }
      if (this.queryForm.costSection == 2) {
        MAX = Math.ceil(Math.min(...minFee) / 1000) * 1000
        MIN = 0
        XMIN = 0
      }
      if (this.queryForm.costSection != 1 && this.queryForm.costSection != 2) {
        MAX = Math.ceil(Math.max(...pplCost) < Math.max(...highFee) ? Math.max(...highFee) / 1000 : Math.max(...pplCost) / 1000) * 1000
        MIN = Math.floor(Math.min(...pplCost) > Math.min(...minFee) ? Math.min(...minFee) / 1000 : Math.min(...pplCost) / 1000) * 1000
        XMIN = 0
      }
      this.splashesOptions = {
        title: {
          text: '病种超高超低分析',
          left: 'center',
          top: 0
        },
        tooltip: {
          // trigger: 'axis',
          showDelay: 0,
          formatter: function (params) {
            if (params.data.length > 1) {
              return deptNames[params.dataIndex] + '<br/>' +
                '病案号:' + params.data[6] + '<br/>' +
                '姓名:' + params.data[4] + '<br/>' +
                '总费用:' + params.data[5] + '<br/>' +
                '倍率:' + params.data[0] + '<br/>' +
                '编码:' + params.data[2] + '<br/>' +
                '名称' + params.data[3]
            } else {
              return params.data.name
            }
          },
          axisPointer: {
            show: true,
            type: 'cross',
            lineStyle: {
              type: 'dashed',
              width: 1
            }
          }
        },
        toolbox: {
          feature: {
            dataZoom: {},
            brush: {
              type: ['rect', 'polygon', 'clear']
            }
          }
        },
        xAxis: {
          name: '总费用/例均费用',
          splitLine: { show: false },
          axisLabel: {
            formatter: '{value}'
          },
          max:
          Math.ceil(Math.max(...exceptCost) < 2 ? Math.max(...exceptCost) + 1 : Math.max(...exceptCost)),
          min: XMIN
        },
        yAxis: {
          name: '总费用',
          scale: true,
          splitLine: { show: false },
          axisLabel: {
            formatter: '{value}'
          },
          max: MAX,
          min: MIN
        },
        series: [
          // 点
          {
            name: '人员',
            type: 'scatter',
            color: '#6699FF',
            symbolSize: 15,
            data: queryData,
            markLine: {
              lineStyle: {
                normal: {
                  type: 'solid',
                  color: '#91cc75'

                }
              },
              label: {
                show: true
              },
              data: [
                { yAxis: this.queryForm.costSection == 1 ? '-1' : Number(minFee[0]),
                  name: '最低费用',
                  lineStyle: {
                    color: '#f39494'
                  }
                },
                { yAxis: this.queryForm.costSection == 2 ? '-1' : Number(standardFee[0]), name: '例均费用' },
                { yAxis: this.queryForm.costSection == 2 ? '-1' : Number(highFee[0]),
                  name: '最高费用',
                  lineStyle: {
                    color: '#f39494'
                  }
                },
                // {xAxis:exceptCost[0] ? '0.5': '10' ,name:'最低倍率'},
                { xAxis: this.queryForm.num1 ? this.queryForm.num1 : '10', name: '最低倍率' },
                { xAxis: this.queryForm.num2, name: '最高倍率' },
                { xAxis: this.queryForm.num3 ? this.queryForm.num3 : '10', name: '自定义' }
              ]
            },
            markArea: {
              silent: true,
              itemStyle: {
                normal: {
                  color: 'transparent'
                }
              },
              label: {
                show: true,
                position: 'insideRight',
                fontStyle: 'italic',
                fontFamily: 'Microsoft YaHei',
                color: '#333',
                fontSize: 14
              },
              data: [
                [{
                  name: '超高',
                  coord: [Number.MIN_VALUE, Number(highFee[0] + 4000)]
                }, {
                  coord: [Number.MAX_VALUE, Number(highFee[0]) + 1000]
                }],
                [{
                  name: '正常区间',
                  coord: [Number.MIN_VALUE, Number(highFee[0])]
                }, {
                  coord: [Number.MAX_VALUE, Number(minFee[0])]
                }],
                [{
                  name: '超低',
                  coord: [Number.MIN_VALUE, Number(minFee[0])]
                }, {
                  coord: [Number.MAX_VALUE, -10]
                }]
              ]
            }
          }
        ]
      }
    },
    splashesClick (params) {
      this.$router.push({
        path: '/oprelDecimmak/pattExtrAnalysis',
        query: {
          a48: params.data[6],
          group: params.data[7],
          begnDate: params.data[8],
          expiDate: params.data[9],
          dateRange: params.data[10],
          code: params.data[2],
          deptCode: this.queryForm.deptCode
        }
      })
    },
    getEnabledGroup () {
      queryEnableGroup().then(res => {
        if (res.code == 200) {
          this.groupOptions = res.data
        }
      })
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.group = this.$somms.getGroupType()
      params.ym = params.ym.substring(0, 4) + '-' + params.ym.substring(4, 7)
      params.year = params.ym.substring(0, 4)
      params.dataAuth = true
      return params
    },
    typeConflict () {
      if (this.queryForm.group == 1) {
        this.showDip = true
        this.showDrg = false
        this.showCd = false
        this.queryForm.drgCodg = ''
        this.queryForm.cdCodg = ''
        this.queryForm.num1 = 0.5
        this.queryForm.num2 = 2.0
        this.queryData()
      } else if (this.queryForm.group == 3) {
        this.showDip = false
        this.showDrg = true
        this.showCd = false
        this.queryForm.dipCodg = ''
        this.queryForm.cdCodg = ''
        this.queryForm.num1 = 0.5
        this.queryForm.num2 = 2.0
        this.queryData()
      } else if (this.queryForm.group == 2) {
        this.showDip = false
        this.showDrg = false
        this.showCd = true
        this.queryForm.dipCodg = ''
        this.queryForm.drgCodg = ''
        this.queryForm.num1 = 0.7
        this.queryForm.num2 = 1.4
        this.queryData()
      }
    }
  }
}
</script>
