<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-date-range
             show-in-date-range
             show-se-date-range
             :show-hos-dept="{ show: showDept }"
             :container="true"
             headerTitle="查询条件"
             contentTitle="趋势分析"
             @query="queryData">
      <!-- 内容 -->
      <template slot="containerContent">
        <el-row :gutter="10" style="height: 50%">
          <el-col :span="8" style="height: 100%">
            <drg-echarts :options="patientMonthData" class="forecast-col-content" />
          </el-col>
          <el-col :span="16" style="height: 100%">
            <drg-echarts :options="profitAndLossData" class="forecast-col-content" />
          </el-col>
        </el-row>
        <el-row :gutter="10" style="height: 50%">
          <el-col :span="24" style="height: 100%">
            <drg-echarts :options="patientYearData" class="forecast-col-content"/>
          </el-col>
        </el-row>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryPatientNumYearData, queryPatientNumMonthData, queryProfitAndLossYearData } from '@/api/operationalDecision/analysis/operationalDecisionTrendAnalysis'

export default {
  name: 'extrTrndAnalysis',
  data: () => ({
    queryForm: {
    },
    patientMonthData: {},
    profitAndLossData: {},
    patientYearData: {}
  }),
  mounted () {
    this.$nextTick(() => {
      this.queryData()
    })
  },
  computed: {
    showDept () {
      return this.$somms.hasHosRole()
    }
  },
  methods: {
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.dataAuth = true
      params.groupType = this.$somms.getGroupType()
      return params
    },
    queryData () {
      this.queryPatientData()
    },
    queryPatientData () {
      queryPatientNumYearData(this.getParams()).then(res => {
        if (res.code == 200) {
          this.lineCount(res.data)
        }
      })
      queryProfitAndLossYearData(this.getParams()).then(res => {
        if (res.code == 200) {
          this.lineProfitAndLossCount(res.data)
        }
      })
      queryPatientNumMonthData(this.getParams()).then(res => {
        if (res.code == 200) {
          this.pieCount(res.data)
        }
      })
    },
    pieCount (data) {
      let patientData = []
      if (data.length > 0) {
        let currTotalNum = 1
        for (let i = 0; i < data.length; i++) {
          currTotalNum = data[i].totalNum > 0 ? data[i].totalNum : 1
          patientData.push({ value: data[i].ncnum, name: '正常病案数', rate: (data[i].ncnum / currTotalNum * 100).toFixed(2) })
          patientData.push({ value: data[i].upNum, name: '超高病案数', rate: (data[i].upNum / currTotalNum * 100).toFixed(2) })
          patientData.push({ value: data[i].lowNum, name: '超低病案数', rate: (data[i].lowNum / currTotalNum * 100).toFixed(2) })
          patientData.push({ value: data[i].noBenchmarkNum, name: '无标杆病案数', rate: (data[i].noBenchmarkNum / currTotalNum * 100).toFixed(2) })
          patientData.push({ value: data[i].unstableNum, name: '非稳定病种病案数', rate: (data[i].unstableNum / currTotalNum * 100).toFixed(2) })
          patientData.push({ value: data[i].longInHosNum, name: '超长住院病案数', rate: (data[i].longInHosNum / currTotalNum * 100).toFixed(2) })
        }
      } else {
        patientData.push(0)
      }

      let option = {
        color: this.$somms.generateColor(),
        title: {
          text: '各区间占比',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return params.data.name + '</br>' +
                   '病例数:' + params.data.value + '</br>' +
                   '占比:' + params.data.rate + '%'
          }
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 20,
          bottom: 20
        },
        series: [
          {
            type: 'pie',
            height: '90%',
            width: '90%',
            label: {
              show: true,
              formatter: '{b}'
            },
            data: patientData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.patientMonthData = option
    },
    lineProfitAndLossCount (data) {
      let monthData = []
      let costData = []
      let upCostData = []
      let NCCostData = []
      let lowCostData = []
      let unstableCostData = []
      let longInHosCostData = []
      for (let i = 0; i < data.length; i++) {
        monthData.push(data[i].ym)
        costData.push((data[i].profitAndLossCost / 10000).toFixed(2))
        upCostData.push((data[i].upProfitAndLossCost / 10000).toFixed(2))
        NCCostData.push((data[i].ncprofitAndLossCost / 10000).toFixed(2))
        lowCostData.push((data[i].lowProfitAndLossCost / 10000).toFixed(2))
        unstableCostData.push((data[i].unstableProfitAndLossCost / 10000).toFixed(2))
        longInHosCostData.push((data[i].longInHosProfitAndLossCost / 10000).toFixed(2))
      }
      let option = {
        color: this.$somms.generateColor(),
        title: {
          text: '盈亏',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: 20,
          right: 10,
          data: ['总病例', '正常病例', '超高病例', '超低病例', '非稳定病种病例', '超长住院病例']
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: monthData
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位/万'
          }
        ],
        series: [
          {
            name: '总病例',
            type: 'line',
            data: costData
          },
          {
            name: '正常病例',
            type: 'line',
            data: NCCostData
          },
          {
            name: '超高病例',
            type: 'line',
            data: upCostData
          },
          {
            name: '超低病例',
            type: 'line',
            data: lowCostData
          },
          {
            name: '非稳定病种病例',
            type: 'line',
            data: unstableCostData
          },
          {
            name: '超长住院病例',
            type: 'line',
            data: longInHosCostData
          }
        ]
      }
      this.profitAndLossData = option
    },
    lineCount (data) {
      let monthData = []
      let totalData = []
      let NCData = []
      let upData = []
      let lowData = []
      let noBenchmarkData = []
      let unstableData = []
      let longInHosData = []
      for (let i = 0; i < data.length; i++) {
        monthData.push(data[i].ym)
        totalData.push(data[i].totalNum)
        NCData.push(data[i].ncnum)
        upData.push(data[i].upNum)
        lowData.push(data[i].lowNum)
        noBenchmarkData.push(data[i].noBenchmarkNum)
        unstableData.push(data[i].unstableNum)
        longInHosData.push(data[i].longInHosNum)
      }
      let option = {
        color: this.$somms.generateColor(),
        title: {
          text: '各区间病案数',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: 20,
          right: 10,
          data: ['总病案数', '正常范围病案数', '超高病案数', '超低病案数', '无标杆病案数', '非稳定病种病案数', '超长住院病案数']
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: monthData
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: '总病案数',
            type: 'line',
            data: totalData
          },
          {
            name: '正常范围病案数',
            type: 'line',
            data: NCData
          },
          {
            name: '超高病案数',
            type: 'line',
            data: upData
          },
          {
            name: '超低病案数',
            type: 'line',
            data: lowData
          },
          {
            name: '无标杆病案数',
            type: 'line',
            data: noBenchmarkData
          },
          {
            name: '非稳定病种病案数',
            type: 'line',
            data: unstableData
          },
          {
            name: '超长住院病案数',
            type: 'line',
            data: longInHosData
          }
        ]
      }
      this.patientYearData = option
    }
  }
}
</script>
