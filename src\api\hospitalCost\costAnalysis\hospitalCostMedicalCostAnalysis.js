import request from '@/utils/request'

/**
 * 查询科室单元医疗成本数据
 * @param params
 * @returns {*}
 */
export function queryMedicalCostData (params) {
  return request({
    url: '/hospitalCostMedicalCostAnalysisController/queryMedicalCostData',
    method: 'post',
    params: params
  })
}

/**
 * 查询科室单元医疗成本明细
 * @param params
 * @returns {*}
 */
export function queryMedicalCostDetail (params) {
  return request({
    url: '/hospitalCostMedicalCostAnalysisController/queryMedicalCostDetail',
    method: 'post',
    params: params
  })
}

/**
 * 查询科室单元盈亏
 * @param params
 * @returns {*}
 */
export function queryProfitAndLossData (params) {
  return request({
    url: '/hospitalCostMedicalCostAnalysisController/queryProfitAndLossData',
    method: 'post',
    params: params
  })
}
