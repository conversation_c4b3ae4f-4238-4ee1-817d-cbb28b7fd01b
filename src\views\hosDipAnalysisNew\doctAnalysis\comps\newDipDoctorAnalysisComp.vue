<template>
  <div class="analysis-wrapper">

      <div class="analysis-head">
        <!-- 标题 -->
        <div class="analysis-head-title">
          <span>
            {{ checkTitle }}
          </span>

          <div class="analysis-head-title-dropdown">
            <el-select v-model="dropdownVal"
                       ref="select"
                       filterable
                       placeholder="请选择"
                       :filter-method="selectFilter"
                       @change="dropdownChange"
                       @focus="dropdownFocus"
                       @visible-change="visibleChange">
              <el-option
                v-for="item in dropdownValAnnex2"
                :key="item.value"
                :label="item.label"
                :value="item.value">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
              </el-option>
            </el-select>
          </div>
        </div>

        <div class="analysis-head-content">
          <div class="analysis-head-content-item"
               :style="{ backgroundColor: item.checked ? '#4e73df' : '#98c5bc' }"
               v-for="(item, index) in summaryData"
               :key="index"
               @click="itemClick(item.type)">
            <div class="title">
              {{ (item.usePrefix ? prefix : '') + item.profttl }}
            </div>
            <div class="value">
              {{ item.value }}
            </div>
          </div>
        </div>
      </div>
    <!-- 分析 -->
    <div class="analysis-wrapper-analysis" v-if="isTablePage">
      <div class="analysis-wrapper-left">
        <!-- 内容 -->
        <div class="analysis-content">
          <!-- 病种 -->
          <el-table ref="elTable"
                    :id="id"
                    height="100%"
                    stripe
                    :header-cell-style="{'text-align':'center'}"
                    :data="summaryData[0].data"
                    v-loading="loading"
                    v-if="summaryData[0].checked"
                    @row-click="tableRowClick"
                    border>
            <el-table-column label="序号" type="index" width="50" fixed="left" align="center" />
            <el-table-column label="DIP编码" prop="dipCodg" align="left"  show-overflow-tooltip/>
            <el-table-column label="DIP名称" prop="dipName"  align="left" show-overflow-tooltip/>
            <el-table-column label="辅助目录-年龄" prop="asstListAgeGrp" :show-overflow-tooltip="true" />
            <el-table-column label="辅助目录-疾病" prop="asstListDiseSevDeg" :show-overflow-tooltip="true" />
            <el-table-column label="辅助目录-肿瘤" prop="asstListTmorSevDeg" :show-overflow-tooltip="true" />
            <el-table-column label="例均费用" prop="avgCost"  align="right"/>
            <el-table-column label="例均费用(标杆)" prop="avgCostBenchmark"  align="right"/>
            <el-table-column label="总费用" prop="sumfee"  align="right"/>
            <el-table-column label="总费用(反馈)" prop="fbTotalCost"  align="right" v-if="queryForm.feeStas == 1"/>
            <el-table-column :label="this.$somms.getFCOrFBName(this.$store.getters.feeStas,0)" prop="preTotalCost"  align="right"/>
            <el-table-column :label="this.$somms.getFCOrFBName(this.$store.getters.feeStas,1)" prop="diff"  align="right"/>
          </el-table>

          <!-- 患者 -->
          <el-table ref="elTable"
                    :id="id"
                    height="100%"
                    stripe
                    :highlight-current-row="true"
                    :header-cell-style="{'text-align':'center'}"
                    :data="summaryData[1].data"
                    v-loading="loading"
                    v-if="summaryData[1].checked"
                    @row-click="tableRowClick"
                    border>
            <el-table-column label="序号" type="index" width="50" fixed="left" align="center" />
            <el-table-column label="病案号" prop="patientId"  align="right" show-overflow-tooltip/>
            <el-table-column label="患者姓名" prop="name"  align="left" show-overflow-tooltip/>
            <el-table-column label="DIP编码" prop="dipCodg" align="left"  show-overflow-tooltip/>
            <el-table-column label="DIP名称" prop="dipName"  align="left" show-overflow-tooltip/>
            <el-table-column label="总费用" prop="sumfee"  align="right"/>
            <el-table-column label="总费用(反馈)" prop="fbTotalCost"  align="right" v-if="queryForm.feeStas == 1" />
            <el-table-column :label="this.$somms.getFCOrFBName(this.$store.getters.feeStas,0)" prop="preTotalCost"  align="right"/>
            <el-table-column :label="this.$somms.getFCOrFBName(this.$store.getters.feeStas,1)" prop="diff"  align="right"/>
          </el-table>
        </div>
      </div>

      <!-- 右侧分析 -->
      <div class="analysis-wrapper-right">
        <el-empty description="暂未选择数据" v-if="Object.keys(this.rightAnalysisData).length == 0"></el-empty>

        <div class="analysis-wrapper-right-content" v-else>
          <!-- 标题 -->
          <div class="analysis-right-title text-ellip" >
            {{ rightAnalysisData.profttl }}
            <el-button type="info" v-if="type == analysisId" style="float: right" @click="changeFourteenCost" round>切换</el-button>
          </div>

          <!-- 副标 -->
          <div class="analysis-right-subhead text-ellip">
            {{ rightAnalysisData.subhead }}
          </div>

          <!-- 费用项 -->
          <div class="analysis-right-cost">
            <!-- 费用项明细对比 -->
            <cost-item :items="rightAnalysisData.items" v-if="checkedType == 'med'"/>

            <!-- 费用 -->
            <cost :queryForm="queryForm" :items="rightAnalysisData.items" v-if="['dis'].includes(checkedType)" />

          </div>

          <!-- 建议 -->
          <div class="analysis-right-suggest">
            <div class="analysis-right-suggest-title">
              建议
            </div>
            <div class="analysis-right-suggest-content">
              <div class="analysis-right-suggest-item" v-for="(item, index) in suggestData" :key="index">
                {{ (index+=1) + '、' + item }}
              </div>
            </div>

            <!-- 声明 -->
            <div class="analysis-right-suggest-declaration">
              <div style="width: 80%">当前费用仅供参考</div>
              <div style="float: right">
                <el-button size="mini" type="primary" @click="jumpDetails(rightAnalysisData)" >详情</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 错误统计 -->
    <div class="analysis-wrapper-no-table" v-if="!isTablePage">
      <med-error :options="medErrorOptions" :options1="comAndLogicErrorOptions"/>
    </div>
  </div>
</template>

<script>
import costItem from './newDipDoctorAnalysisCompCostItem'
import cost from './newDipDoctorAnalysisCompCost'
import medError from './newDipDoctorAnalysisCompMedError'

export default {
  components: {
    costItem,
    cost,
    medError
  },
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 表格id
    id: {
      type: String
    },
    // 医生下拉选
    dropdown: {
      type: Array,
      default: () => []
    },
    dropdownCheckVal: {
      type: String
    },
    // 亏损或盈利
    isLoss: {
      type: Boolean,
      default: true
    },
    queryForm: {
      type: Object
    }
  },
  data () {
    return {
      summaryData: [
        { profttl: '病组数', value: 0, type: 'dis', checked: true, data: [], pageNum: 1, usePrefix: true },
        { profttl: '病案数', value: 0, type: 'med', checked: false, data: [], pageNum: 1, usePrefix: true },
        { profttl: '错误病例', value: 0, type: 'errorMed', checked: false, usePrefix: false }
      ],
      checkedType: '',
      rightAnalysisData: {},
      dropdownVal: '',
      dropdownValAnnex: [],
      dropdownValAnnex2: [],
      checkTitle: '',
      suggestData: [],
      // 错误病例 options
      medErrorOptions: {},
      comAndLogicErrorOptions: {},
      // 是否显示亏损
      showLoss: true,
      prefix: '亏损',
      fourteenCost: true,
      rowData: [],
      analysisId: 'med',
      // 病组，医生，病案，错误 类型
      type: ''
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.itemClick(this.summaryData[0].type)
    })
  },
  updated () {
    if (this.isTablePage) {
      this.$nextTick(() => {
        this.$refs.elTable.doLayout()
      })
    }
  },
  computed: {
    // 是数据页面
    isTablePage () {
      let flag = false
      this.summaryData.forEach(item => {
        if (item.checked && item.type != 'errorMed') {
          flag = true
        }
      })
      return flag
    }
  },
  methods: {
    itemClick (type) {
      this.type = type
      this.summaryData.forEach(item => {
        if (item.type == type) {
          item.checked = true
          this.checkedType = type
          this.$emit('checkTypeChange', { type: type, data: this.summaryData, pageNum: item.pageNum })
        } else {
          item.checked = false
        }
      })
    },
    setData (dataArr) {
      for (let i = 0; i < dataArr.length; i++) {
        this.summaryData[i].data = dataArr[i].data
        this.summaryData[i].value = dataArr[i].total
        this.summaryData[i].pageNum = dataArr[i].pageNum
        // 错误病例
        if (i == 2 && dataArr[i].data && dataArr[i].data.length > 0) {
          this.generateMedErrorOptions(dataArr[i].data[0])
        }
      }
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    // 表格行选中
    tableRowClick (row) {
      this.rowData = row
      this.rightAnalysisData = {}
      if (this.checkedType == 'dis') {
        this.rightAnalysisData.profttl = row.dipCodg
        this.rightAnalysisData.subhead = row.dipName
        this.rightAnalysisData.type = 'dis'
        this.rightAnalysisData.items = this.getCostInfo(row)
      }
      if (this.checkedType == 'med') {
        this.rightAnalysisData.profttl = row.name
        this.rightAnalysisData.subhead = row.patientId
        this.rightAnalysisData.type = 'med'
        this.rightAnalysisData.items = this.getCostItemInfo(row)
      }
    },
    // 获取费用和标杆对比明细
    getCostItemInfo (row) {
      this.suggestData = []
      // 拼接结构
      let costItem = []
      if (this.fourteenCost) {
        costItem = [
          { name: '综合医疗服务费',
            value: row.com_med_servfee,
            bgValue: row.bgzhylfwf,
            children: [
              { name: '一般医疗服务费', value: row.ordn_med_servfee, bgValue: row.bgybylfwf },
              { name: '一般治疗操作费', value: row.ordn_trt_oprt_fee, bgValue: row.bgybzlczf },
              { name: '护理费', value: row.nursfee, bgValue: row.bghlf },
              { name: '其他费', value: row.zhylfwqtf, bgValue: row.bgzhylfwqtf }
            ]
          },
          { name: '诊断费',
            value: row.diag_fee,
            bgValue: row.bgzdf,
            children: [
              { name: '病理诊断费', value: row.cas_diag_fee, bgValue: row.bgblzdf },
              { name: '实验室诊断费', value: row.lab_diag_fee, bgValue: row.bgsyszdf },
              { name: '影像学诊断费', value: row.rdhy_diag_fee, bgValue: row.bgyxxzdf },
              { name: '临床诊断项目费', value: row.clnc_diag_item_fee, bgValue: row.bglczdxmf }
            ]
          },
          { name: '治疗费',
            value: row.treat_fee,
            bgValue: row.bgzlf,
            children: [
              { name: '非手术治疗项目费', value: row.nsrgtrt_item_fee, bgValue: row.bgfsszlxmf },
              { name: '手术治疗费', value: row.oprn_treat_fee, bgValue: row.bgsszlf }
            ]
          },
          { name: '康复费', value: row.rhab_fee, bgValue: row.bgkff, children: [] },
          { name: '中医费', value: row.tcmdrug_fee, bgValue: row.bgzyf, children: [] },
          { name: '西药费', value: row.west_fee, bgValue: row.bgxyf, children: [] },
          { name: '中药费',
            value: row.zyf1,
            bgValue: row.bgzyf1,
            children: [
              { name: '中成药费', value: row.tcmpat_fee, bgValue: row.bgzcyf },
              { name: '中草药费', value: row.tcmherb, bgValue: row.bgzcyf1 }
            ]
          },
          { name: '血液和血液制品费',
            value: row.zyf1,
            bgValue: row.bgzyf1,
            children: [
              { name: '血费', value: row.blo_fee, bgValue: row.bgxf },
              { name: '白蛋白类制品费', value: row.bdblzpf, bgValue: row.bgbdblzpf },
              { name: '球蛋白类制品费', value: row.qdblzpf, bgValue: row.bgqdblzpf },
              { name: '凝血因子类制品费', value: row.nxyzlzpf, bgValue: row.bgnxyzlzpf },
              { name: '细胞因子类制品费', value: row.xbyzlzpf, bgValue: row.bgxbyzlzpf }
            ]
          },
          { name: '耗材费',
            value: row.zyf1,
            bgValue: row.bgzyf1,
            children: [
              { name: '检查用一次性医用材料费', value: row.jcyycxyyclf, bgValue: row.bgjcyycxyyclf },
              { name: '治疗用一次性医用材料费', value: row.trt_use_dspo_med_matlfee, bgValue: row.bgzlyycxyyclf },
              { name: '手术用一次性医用材料费', value: row.oprn_use_dspo_med_matlfee, bgValue: row.bgssyycxyyclf }
            ]
          },
          { name: '其他费', value: row.oth_fee, bgValue: row.bgqtf, children: [] }
        ]
      } else {
        costItem = [
          { name: '床位费', value: row.fourteencwf, bgValue: row.bgfourteencwf, children: [] },
          { name: '诊察费', value: row.fourteenzcf, bgValue: row.bgfourteenzcf, children: [] },
          { name: '检查费', value: row.fourteenjcf, bgValue: row.bgfourteenjcf, children: [] },
          { name: '化验费', value: row.fourteenhyf, bgValue: row.bgfourteenhyf, children: [] },
          { name: '治疗费', value: row.fourteenzlf, bgValue: row.bgfourteenzlf, children: [] },
          { name: '手术费', value: row.fourteenssf, bgValue: row.bgfourteenssf, children: [] },
          { name: '护理费', value: row.fourteenhlf, bgValue: row.bgfourteenhlf, children: [] },
          { name: '卫生材料费', value: row.fourteenwsclf, bgValue: row.bgfourteenwsclf, children: [] },
          { name: '西药费', value: row.fourteenxyf, bgValue: row.bgfourteenxyf, children: [] },
          { name: '中药饮片费', value: row.fourteenzyypf, bgValue: row.bgfourteenzyypf, children: [] },
          { name: '中成药费', value: row.fourteenzcyf, bgValue: row.bgfourteenzcyf, children: [] },
          { name: '一般诊疗费', value: row.fourteenybzlf, bgValue: row.bgfourteenybzlf, children: [] },
          { name: '挂号费', value: row.fourteenghf, bgValue: row.bgfourteenghf, children: [] },
          { name: '其他费', value: row.fourteenqtf, bgValue: row.bgfourteenqtf, children: [] }
        ]
      }

      // 建议
      costItem.forEach(item => {
        if (item.bgValue != 0) {
          if (item.value / item.bgValue > 2) {
            this.suggestData.push('建议降低' + item.name)
          }

          if (item.value / item.bgValue < 0.5 && this.showLoss) {
            this.suggestData.push('建议提高' + item.name)
          }
        }
      })
      return costItem
    },
    // 获取费用解构
    getCostInfo (row) {
      this.suggestData = []
      let cost = [
        { name: '超高',
          data: [
            { name: '病案数', value: row.highNum, patientId: row.highPatientId, drCodg: this.dropdownVal },
            { name: '总费用', value: row.highTotalCost },
            { name: '预测费用', value: row.highPreTotalCost },
            { name: '预测差异', value: row.highDiff }
          ] },
        { name: '超低',
          data: [
            { name: '病案数', value: row.lowNum, patientId: row.lowPatientId, drCodg: this.dropdownVal },
            { name: '总费用', value: row.lowTotalCost },
            { name: '预测费用', value: row.lowPreTotalCost },
            { name: '预测差异', value: row.lowDiff }
          ] },
        { name: '正常',
          data: [
            { name: '病案数', value: row.normalNum, patientId: row.normalPatientId, name_c: '盈利病案数', value_c: row.normalEarnNum, patientId_c: row.normalEarnPatientId, drCodg: this.dropdownVal },
            { name: '总费用', value: row.normalTotalCost, name_c: '盈利总费用', value_c: row.normalEarnTotalCost },
            { name: '预测费用', value: row.normalPreTotalCost, name_c: '盈利预测费用', value_c: row.normalEarnPreTotalCost },
            { name: '预测差异', value: row.normalDiff, name_c: '盈利预测差异', value_c: row.normalEarnDiff }
          ],
          contrast: true
        }
      ]

      // 建议
      cost.forEach(item => {
        let errNum = item.data[0].value
        if (item.name == '正常') {
          if (errNum > 0) {
            this.suggestData.push('建议降低正常病例费用')
          }

          if (errNum > 0 && item.data[0].value_c > 0) {
            this.suggestData.push('建议参考盈利病例')
          }
        } else {
          if (errNum > 0) {
            this.suggestData.push('建议调整' + item.name)
          }
        }
      })
      return cost
    },
    // 选择框改变
    dropdownChange (val, emit = true) {
      this.dropdown.forEach(item => {
        if (item.value == val) {
          if (emit) {
            this.$emit('dropdownChange', item.value)
          }
          this.checkTitle = item.label
        }
      })
    },
    // 获取显示名称
    getErrorOptionsErrorName (index) {
      if (index == 1) {
        return '未找到手术级别'
      }
      if (index == 2) {
        return '未找到主要诊断节代码'
      }
      if (index == 3) {
        return '不在支付体系中'
      }
      if (index == 4) {
        return '诊断编码不是医保版编码'
      }
      if (index == 5) {
        return '手术及操作编码不是医保版编码'
      }
      if (index == 6) {
        return '未找到主要诊断编码'
      }
      if (index == 7) {
        return '主要诊断不参与分组'
      }
    },
    // 生成错误病例图信息
    generateMedErrorOptions (data) {
      let tempData = []
      for (let i = 0; i < 7; i++) {
        let errNum = data['error' + (i + 1)]
        let name = this.getErrorOptionsErrorName((i + 1))
        if (errNum && errNum > 0) {
          tempData.push({
            value: errNum,
            name: name,
            url: '/hosDipAnalysisNew/pattAnalysis',
            errorReason: 'error' + (i + 1),
            begnDate: this.queryForm.begnDate,
            expiDate: this.queryForm.expiDate,
            drCodg: this.dropdownVal,
            inStartTime: this.queryForm.inStartTime,
            inEndTime: this.queryForm.inEndTime,
            inHosFlag: this.queryForm.inHosFlag
          })
        }
      }

      let options = {
        color: this.$somms.generateColor(),
        title: {
          text: '入组失败情况',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '入组错误病例',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: tempData
          }
        ]
      }

      let options1 = JSON.parse(JSON.stringify(options))
      let tempData1 = []
      if (data.compeleteErrorNum && data.compeleteErrorNum > 0) {
        tempData1.push({
          value: data.compeleteErrorNum,
          name: '完整性错误',
          url: '/caseQual/compVali',
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: this.dropdownVal,
          deptCode: this.queryForm.deptCode,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag
        })
      }

      if (data.logicErrorNum && data.logicErrorNum > 0) {
        tempData1.push({
          value: data.logicErrorNum,
          name: '逻辑性错误',
          url: '/caseQual/logiVali',
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          deptCode: this.queryForm.deptCode,
          drCodg: this.dropdownVal,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag
        })
      }
      options1.series[0].name = '逻辑性完整性错误'
      options1.series[0].data = tempData1
      options1.profttl.text = '病案校验情况'
      this.medErrorOptions = options
      this.comAndLogicErrorOptions = options1
    },
    setPrefix () {
      if (this.showLoss) {
        this.prefix = '亏损'
      } else {
        this.prefix = '盈利'
      }
    },
    dropdownFocus () {
      this.dropdownValAnnex2 = []
      this.dropdownValAnnex2 = this.dropdown
    },
    visibleChange (flag) {
      if (flag) {
        this.$refs.select.focus()
      } else {
        this.$refs.select.blur()
      }
    },
    selectFilter (val) {
      // 判断是否为空
      if (val) {
        // 同时筛选Lable与value的值
        this.dropdownValAnnex2 = this.dropdownValAnnex.filter(item => item.label.match(val) || item.value.match(val))
      } else {
        // 赋值还原
        this.dropdownValAnnex2 = this.dropdownValAnnex
      }
    },
    // 右侧分析详情下转
    jumpDetails (data) {
      if (data && data.type == 'dis') {
        this.$router.push({
          path: '/hosDipAnalysisNew/pattAnalysis',
          query: {
            begnDate: this.queryForm.begnDate,
            expiDate: this.queryForm.expiDate,
            dipCodg: data.profttl,
            drCodg: this.dropdownVal,
            feeStas: this.queryForm.feeStas,
            inStartTime: this.queryForm.inStartTime,
            inEndTime: this.queryForm.inEndTime,
            seStartTime: this.queryForm.seStartTime,
            seEndTime: this.queryForm.seEndTime,
            inHosFlag: this.queryForm.inHosFlag
            // isLoss:this.prefix == '亏损' ? "1" : "0",
            // radioMode:'2'
          }
        })
      } else if (data && data.type == 'med') {
        this.$router.push({
          path: '/hosDipAnalysisNew/pattAnalysis',
          query: {
            begnDate: this.queryForm.begnDate,
            expiDate: this.queryForm.expiDate,
            bah: data.subhead,
            drCodg: this.dropdownVal,
            feeStas: this.queryForm.feeStas,
            inStartTime: this.queryForm.inStartTime,
            inEndTime: this.queryForm.inEndTime,
            seStartTime: this.queryForm.seStartTime,
            seEndTime: this.queryForm.seEndTime,
            inHosFlag: this.queryForm.inHosFlag
            // isLoss:this.prefix == '亏损' ? "1" : "0",
            // radioMode:'2'
          }
        })
      }
    },
    changeFourteenCost () {
      this.fourteenCost = !this.fourteenCost
      this.tableRowClick(this.rowData)
    }
  },
  watch: {
    data: {
      immediate: true,
      deep: true,
      handler: function (data) {
        if (data && data.length > 0) {
          this.setData(data)
        }
      }
    },
    dropdownCheckVal: {
      immediate: true,
      deep: true,
      handler: function (val) {
        if (val) {
          this.dropdownVal = val
          this.dropdownChange(val, false)
        }
      }
    },

    // 同步当前组件显示盈亏
    isLoss: {
      immediate: true,
      handler (val) {
        this.showLoss = val
        this.setPrefix()
      }
    },
    dropdown: {
      immediate: true,
      handler (val) {
        if (val) {
          this.dropdownValAnnex = val
          this.dropdownValAnnex2 = val
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.analysis-wrapper{
  height: 100%;
  width: 100%;
  position: relative;

  &-left{
    width: 80%;
    height: 98%;
    padding-right: 10px;
    box-sizing: border-box;
  }

  &-analysis{
    width: 100%;
    height: 89%;
    position: relative;
    display: flex;
  }

  &-right{
    width: 20%;
    height: 100%;
    background-color: rgba(131,175,155,.3);
    padding: 1% 1% 0 1%;
    border-radius: 1%;
  }

  &-no-table{
    width: 100%;
    height: 80%;
  }
}
.analysis-head{
  width: 80%;
  height: 11%;

  &-title{
    width: 100%;
    height: 20%;
    font-size: var(--biggerSize);
    font-weight: 600;
    position: relative;

    &-dropdown{
      position: absolute;
      right: 10px;
      top: 0;
    }
  }

  &-content{
    width: 100%;
    height: 54%;
    display: flex;

    &-item{
      width: 14%;
      //height: 100%;
      height: 60px;
      cursor: pointer;
      background-color: rgb(64,158,255);
      font-weight: 600;
      margin-right: 1%;
      padding: 1%;
      display: flex;
      align-items: center;
      justify-items: center;
      flex-direction: column;
      border-radius:  10px;

      .title{
        font-size: 14px;
        color: white;
      }

      .value{
        font-size: 24px;
        color: white;
      }
    }
  }
}

.analysis-content{
  width: 100%;
  height: 100%;
  position: relative;

  &-summary{
    width: 30%;
    height: 40%;
    border: 1px solid red;
  }
}

.analysis-wrapper-right{
  width: 20%;
  height: 118%;
  position: absolute;
  right: 0;
  top: -20%;
  display: flex;
  justify-content: center;

  &-content{
    width: 100%;
    padding-top: 15%;
    height: 100%;
  }
}

$titleGray: gray;
$titleSize: 13px;
.analysis-right{

  &-title{
    width: 100%;
    height: 4%;
    font-size: var(--biggerSize);
    font-weight: 600;
  }

  &-subhead{
    width: 100%;
    height: 4%;
    font-size: var(--biggerSmallSize);
    color: gray;
    border-bottom: 1px solid $titleGray;
  }

  &-cost{
    width: 100%;
    height: 62%;
    line-height: 20px;
    overflow-y: auto;
    border-bottom: 1px solid $titleGray;
  }

  &-suggest{
    padding-top: 4%;
    width: 100%;
    height: 30%;
    overflow-y: auto;

    &-title{
      width: 100%;
      font-weight: 600;
      height: 10%;
      font-size: $titleSize;
    }

    &-content{
      width: 100%;
      height: 70%;
      overflow-y: auto;
    }

    &-declaration{
      width: 100%;
      height: 15%;
      color: #fa5d5d;
      display: flex;
      align-items: center
    }

    &-item{
      width: 100%;
      height: 16%;
    }
  }
}
</style>
