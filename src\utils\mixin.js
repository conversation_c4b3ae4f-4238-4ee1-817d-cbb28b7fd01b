/**
 * 缓存局部混入
 * @type {{watch: {"$store.state.tagsView.tempView"(): void}, beforeRouteLeave(*=, *, *): void}}
 */
export const cacheMixin = {
  beforeRouteLeave (to, from, next) {
    if (Object.keys(from.query).length > 0) {
      this.$store.dispatch('tagsView/addTempView', to)
      if (this.$store.state.tagsView.tempCloseView.path != from.path) {
        this.$router.push({ query: {} })
      }
    }
    next()
  },
  watch: {
    '$store.state.tagsView.tempView' () {
      // 延迟一毫秒等待清除query数据完成
      setTimeout(() => {
        let path = this.$store.state.tagsView.tempView.path
        let query = this.$store.state.tagsView.tempView.query
        this.$router.push({ path, query })
      }, 100)
    }
  }
}
