import Vue from 'vue'
import Router from 'vue-router'
import store from '@/store'

/* Layout */
import Layout from '../views/indexView/IndexView'
import { getMenuTree, getToken, getUserName, setMenuTree } from '@/utils/auth' // 验权
import { getIFramePath, getIFrameUrl } from '@/utils/iframe'
import { findPermissions } from '@/api/user'
import { findNavTree, queryDoubleCacheData } from '@/api/menu'
import { verifyToken } from '@/api/login'

Vue.use(Router)
// import Generator from '@/views/Generator/Generator'

// 解决报错
const originalPush = Router.prototype.push
const originalReplace = Router.prototype.replace
// push
Router.prototype.push = function push (location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch(err => err)
}
// replace
Router.prototype.replace = function push (location, onResolve, onReject) {
  if (onResolve || onReject) return originalReplace.call(this, location, onResolve, onReject)
  return originalReplace.call(this, location).catch(err => err)
}

/**
 * is_hide: true                   if `hidden:true` will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu, whatever its child routes length
 *                                if not set alwaysShow, only more than one route under the children
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noredirect           if `redirect:noredirect` will no redirct in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
 profttl: 'title'               the name show in submenu and breadcrumb (recommend set)
 icon: 'svg-name'             the icon show in the sidebar,
 }
 **/
const staticRouter = [{
  path: '',
  component: Layout,
  redirect: '/home',
  children: [{
    path: 'home',
    name: 'home',
    component: () => import('@/views/workTable/index'),
    meta: { profttl: '工作台', icon: 'home', keepAlive: true, affix: true }
  }],
  is_hide: true
},
{ path: '/login', component: () => import('@/views/login/index'), is_hide: true },
// {path: '/view', component: () => import('@/views/screenView/drgDataView/index'), is_hide: true}, //大屏视图展示
{ path: '/404', component: () => import('@/views/404'), is_hide: true }
]

const drgDevAddRouter =
    {
      path: '/syssec',
      component: Layout,
      redirect: '/syssec/userConfig',
      name: 'oms',
      meta: { profttl: '系统安全', icon: 'el-icon-view' },
      children: [
        {
          path: 'userConfig',
          name: 'userConfig',
          component: () => import('@/views/syssec/userConfig/index'),
          meta: { profttl: '用户管理', icon: 'el-icon-s-dipDefined' }
        },
        {
          path: 'databaseConfig',
          name: 'databaseConfig',
          component: () => import('@/views/syssec/databaseConfig/index'),
          meta: { profttl: '数据库管理', icon: 'el-icon-coin' }
        }
      ]
    }

// 系统管理员特有附加路由
// if(getUserName()=='developer'){
//   staticRouter.push(drgDevAddRouter);
// }else{
//   staticRouter.pop(drgDevAddRouter);
// }
let router = new Router({ routes: staticRouter }) // 初始化静态路由
// 解决警告
router.$addRoutes = (params) => {
  router.matcher = new Router({ mode: 'history' }).matcher
  router.addRoutes(params)
}

let flag = 0
router.beforeEach((to, from, next) => {
  // 外部系统访问
  if (to.query.OUT_SYS && !getToken()) {
    verifyToken({ token: to.query.token }).then(res => {
      let data = res.data
      let username = data.username ? data.username : to.query.username ? to.query.username : ''
      let password = data.password ? data.password : to.query.password ? to.query.password : ''
      let token = data.token ? data.token : to.query.token ? to.query.token : ''
      store.dispatch('Login', { username, password, token }).then(() => {
        handlerBeforeEach(to, from, next)
      })
    }).catch(() => {
      handlerBeforeEach(to, from, next)
    })
  } else {
    handlerBeforeEach(to, from, next)
  }
})

function handlerBeforeEach (to, from, next) {
  // 登录界面登录成功之后，会把用户信息保存在会话
  // 存在时间为会话生命周期，页面关闭即失效。
  let token = getToken()
  let userName = getUserName()
  if (to.path === '/login') {
    // 如果是访问登录界面，如果用户会话信息存在，代表已登录过，跳转到主页
    if (token) {
      next({ path: '/' })
    } else {
      next()
    }
  } else {
    if (!token) {
      // 如果访问非登录界面，且户会话信息不存在，代表未登录，则跳转到登录界面
      next({ path: '/login' })
    } else {
      // 加载动态菜单和路由
      if (getMenuTree()) {
        if (flag === 0) {
          // 刷新需要重新刷新路由
          addDynamicMenuAndRoutes(userName, to, from).then(res => {
            flag++
            if (to.query.OUT_SYS) {
              store.commit('setOutSysTo', to)
            }
            next({ path: '/' })
          })
        } else {
          let outSysTo = store.state.app.outSysTo
          if (outSysTo) {
            store.commit('setOutSysTo', null)
            next(outSysTo)
          } else {
            if (store.getters.doubleCacheViews.length > 0) {
              nextRoute(to, from, next, store.getters.doubleCacheViews)
            } else {
              queryDoubleCacheData({}).then(res => {
                let cacheArray = []
                if (res.code == 200) {
                  for (let item of res.data) {
                    cacheArray.push(item.url)
                  }
                  store.commit('tagsView/SET_DOUBLE_CACHE_VIEWS', cacheArray)
                }
                nextRoute(to, from, next, cacheArray)
              }).catch(res => {
                nextRoute(to, from, next)
              })
            }
          }
        }
      } else {
        addDynamicMenuAndRoutes(userName, to, from).then(res => {
          if (to.query.OUT_SYS) {
            next(to)
          } else {
            next()
          }
        }).catch(() => {
        })
      }
    }
  }
}

function nextRoute (to, from, next, cacheArray = []) {
  cacheOperation(to, from, cacheArray, '1')
  cacheOperation(to, from, cacheArray, '2')
  next()
}

/**
 * 缓存操作
 * @param to 路由
 * @param from 路由
 * @param type 类型
 */
function cacheOperation (to, from, cacheArray, type) {
  let path = type == '1' ? to.path : from.path
  let length = type == '1' ? Object.keys(to.query).length : Object.keys(from.query).length
  if (cacheArray.includes(path) && length > 0) {
    if (type == '1') {
      // 删除缓存，添加临时缓存
      store.dispatch('tagsView/delCachedView', to)
      store.dispatch('tagsView/addTempCachedView', to)
    } else {
      // 新增缓存
      store.dispatch('tagsView/delTempCachedView', from)
      store.dispatch('tagsView/addCachedView', from)
    }
  }
}

function sleep (d) {
  for (let t = UpdtDate.now(); UpdtDate.now() - t <= d;);
}

/**
 * 加载动态菜单和路由
 */
function addDynamicMenuAndRoutes (userName, to, from) {
  return new Promise((resolve, reject) => {
    // 处理IFrame嵌套页面
    // handleIFrameUrl(to.path)
    findNavTree({ userName: userName })
      .then(res => {
        // 添加动态路由
        let dynamicRoutes = addDynamicRoutes(res.data)
        // 处理静态组件绑定路由
        handleStaticComponent(router, dynamicRoutes)
        router.$addRoutes(router.options.routes)
        // router.options.routes.forEach(route => router.addRoute(route))
        // console.log(router.options.routes)
        // 保存加载目录路由加载状态
        setMenuTree(true)
        // 保存菜单树所有加载路由
        store.commit('setNavTree', res.data)
        // 保存路由信息
        store.dispatch('setRouters', router.options.routes)
        resolve()
      }).then(res => {
        findPermissions({ username: userName }).then(res => {
          // 异步保存用户权限标识集合
          store.dispatch('setPerms', res.data)
        })
      }).catch(function (res) {
        reject(res)
      })
  })
}

/**
 * 处理路由到本地直接指定页面组件的情况
 * 比如'代码生成'是要求直接绑定到'Generator'页面组件
 */
function handleStaticComponent (router, dynamicRoutes) {
  let currRouter = []
  router.options.routes = currRouter.concat(staticRouter).concat(dynamicRoutes)
  // 最后注入拦截路由 404
  router.options.routes.push({ path: '*', redirect: '/404', is_hide: true })
}

/**
 * 处理IFrame嵌套页面
 */
function handleIFrameUrl (path) {
  // 嵌套页面，保存iframeUrl到store，供IFrame组件读取展示
  let url = path
  let length = store.state.iframe.iframeUrls.length
  for (let i = 0; i < length; i++) {
    let iframe = store.state.iframe.iframeUrls[i]
    if (path != null && path.endsWith(iframe.path)) {
      url = iframe.url
      store.commit('setIFrameUrl', url)
      break
    }
  }
}

/**
 * 添加动态(菜单)路由
 * @param {*} menuList 菜单列表
 * @param {*} routes 递归创建的动态(菜单)路由
 */
function addDynamicRoutes (menuList = [], routes = []) {
  let temp = []
  for (let i = 0; i < menuList.length; i++) {
    // 判断是否有子节点
    if (menuList[i].children && menuList[i].children.length >= 1) {
      temp = temp.concat(menuList[i].children)
    }
    let url = ''
    if (menuList[i].url) {
      url = menuList[i].url.substring(1)
    }
    let route = {
      path: url,
      component: null,
      name: menuList[i].name,
      meta: {
        icon: menuList[i].icon,
        index: menuList[i].id,
        profttl: menuList[i].name,
        keepAlive: menuList[i].is_cahe == '1'
      },
      is_hide: menuList[i].is_hide == '1',
      children: []
    }
    if (menuList[i].prntMenuIdLv1Menu == 0) {
      route.component = Layout
    }
    // 遍历
    if (menuList[i].url && /\S/.test(menuList[i].url)) {
      menuList[i].url = menuList[i].url.replace(/^\//, '')
      // 创建路由配置
      let path = getIFramePath(menuList[i].url)
      if (path) {
        // 如果是嵌套页面, 通过iframe展示
        route['path'] = path
        route['component'] = resolve => require([`@/utils/iframe`], resolve)
        // 存储嵌套页面路由路径和访问URL
        let url = getIFrameUrl(menuList[i].url)
        let iFrameUrl = { path: path, url: url }
        store.commit('addIFrameUrl', iFrameUrl)
      } else {
        try {
          // 根据菜单URL动态加载vue组件，这里要求vue组件须按照url路径存储
          // 如url="sys/user"，则组件路径应是"@/views/sys/user.vue",否则组件加载不到
          let urlPath = menuList[i].url
          route['component'] = () => import(`@/views/${urlPath}`)
        } catch (e) {}
      }
    }
    if (temp.length >= 1) {
      route.children = addDynamicRoutes(menuList[i].children)
    }
    routes.push(route)
  }
  return routes
}

export default router
