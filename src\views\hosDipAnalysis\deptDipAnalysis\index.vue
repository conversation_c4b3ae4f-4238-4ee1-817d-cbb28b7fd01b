<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-date-range
             :show-hos-dept="this.$somms.hasHosRole()"
             :container="true"
             headerTitle="查询条件"
             contentTitle="科室分析"
             @query="queryData">
      <!-- 内容 -->
      <template slot="containerContent">
        <div style="height: 100%">
          <el-table border
                    :header-cell-style="{ 'text-align' : 'center' }"
                    :data="tableData" v-loading="listLoading">
            <el-table-column label="序号" type="index" align="center"></el-table-column>
            <el-table-column label="科室编码" v-if="false"></el-table-column>
            <el-table-column label="科室名称" align="left">
              <template slot-scope="scope">
                {{ scope.row.deptName }}
              </template>
            </el-table-column>
            <el-table-column label="病案总数" align="right">
              <template slot-scope="scope">
                <div class='skip' v-if="scope.row.totalNum != 0" @click="queryPatientData(scope.row)">
                  {{ scope.row.totalNum }}
                </div>
                <div v-else>
                  {{ scope.row.totalNum }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="中医优势病种人数" align="right">
              <template slot-scope="scope">
                <div v-if="scope.row.dominantDiseaseNum != 0" class='skip' @click="queryDiseaseData(scope.row,1)">
                  {{ scope.row.dominantDiseaseNum }}
                </div>
                <div v-else>
                  {{ scope.row.dominantDiseaseNum }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="基层病种人数" align="right">
              <template slot-scope="scope">
                <div v-if="scope.row.baseDiseaseNum != 0" class='skip' @click="queryDiseaseData(scope.row,2)">
                  {{ scope.row.baseDiseaseNum }}
                </div>
                <div v-else>
                  {{ scope.row.baseDiseaseNum }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="低龄病种人数" align="right">
              <template slot-scope="scope">
                <div v-if="scope.row.youngerDiseaseNum != 0" class='skip' @click="queryDiseaseData(scope.row,3)">
                  {{ scope.row.youngerDiseaseNum }}
                </div>
                <div v-else>
                  {{ scope.row.youngerDiseaseNum }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="重点专科病种人数" align="right">
              <template slot-scope="scope">
                <div v-if="scope.row.professionalDiseaseNum != 0" class='skip' @click="queryDiseaseData(scope.row,4)">
                  {{ scope.row.professionalDiseaseNum }}
                </div>
                <div v-else>
                  {{ scope.row.professionalDiseaseNum }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { selectDeptData } from '@/api/dipBusiness/dipDeptAnalysis'

export default {
  name: 'deptDipAnalysis',
  data: () => ({
    queryForm: {

    },
    tableData: null,
    listLoading: false
  }),
  mounted () {
    this.$nextTick(() => {
      this.queryData()
    })
  },
  methods: {
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.dataAuth = true
      return params
    },
    queryData () {
      this.listLoading = true
      selectDeptData(this.getParams()).then(res => {
        if (res.code == 200) {
          this.tableData = res.data
          this.listLoading = false
        }
      })
    },
    queryPatientData (row) {
      this.goto('/hosDipAnalysis/patientAnalysis', {
        begnDate: this.queryForm.begnDate,
        expiDate: this.queryForm.expiDate,
        dateRange: this.queryForm.dateRange,
        deptCode: row.deptCode
      })
    },
    queryDiseaseData (row, type) {
      this.goto('/hosDipAnalysis/patientAnalysis', {
        begnDate: this.queryForm.begnDate,
        expiDate: this.queryForm.expiDate,
        dateRange: this.queryForm.dateRange,
        deptCode: row.deptCode,
        diseType: [type]
      })
    }
  }
}
</script>
