<template>
  <el-select v-model="value"
             filterable remote clearable
             :remote-method="(str) => search(str)"
             :style="{ width:'100%' }"
             :placeholder="'请输入' + (icdType === 'ICD-9' ? '手术' : '诊断') + '名称或编码'"
             :loading="loading"
             @change="change">
    <el-option
      v-for="item in selectList"
      :key="item.icdCodg"
      :label="item.icdName + '(' + item.icdCodg + ')'"
      :value="item.icdCodg">
      <span class="code">{{ item.icdCodg }}</span>
      <span class="name">{{ item.icdName }}</span>
    </el-option>
  </el-select>
</template>
<script>
import {
  queryICDCode
} from '@/api/medicalQuality/settleListDetail'
export default {
  props: {
    icdType: {
      type: String
    }
  },
  model: {
    prop: 'modelVal',
    event: 'selected'
  },
  data () {
    return {
      loading: false,
      value: '',
      selectList: [],
      icd9List: [],
      icd10List: []
    }
  },
  methods: {
    change (val) {
      this.$emit('selected', val)
    },
    async search (str) {
      this.loading = true
      if (this.icd9List.length === 0) {
        await queryICDCode({ ver: '10' }).then(res => {
          this.icd9List = res.data.icd9
          this.icd10List = res.data.icd10
        })
      }
      this.loading = false
      let list
      if (this.icdType === 'ICD-9') {
        list = this.icd9List
      } else {
        list = this.icd10List
      }
      if (str) {
        this.selectList = list.filter(l => l.icdCodg.includes(str.toUpperCase()) || l.icdName.includes(str)).slice(0, 20)
      }
    }
  },
  watch: {
    modelVal: {
      immediate: true,
      handler: function (val) {
        this.value = val
      }
    }
  }
}
</script>
