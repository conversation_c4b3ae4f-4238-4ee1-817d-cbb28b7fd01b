<template>
  <div class="app-container">
    <drg-form v-model="listQuery" show-date-range show-in-date-range show-se-date-range show-hos-dept showPagination
              :totalNum="total" headerTitle="查询条件" contentTitle="医生指标" :container="true" @query="handleSearchList"
              @reset="handleResetSearch">

      <template slot="extendFormItems">
        <el-form-item label="医生姓名" class="som-el-form-item-margin-left" v-if="!this.$somms.hasDoctorRole()">
          <el-select v-model="listQuery.drCodg" placeholder="请选择医生姓名" @change="changeSelectDoctor" clearable>
            <el-option v-for="(item, index) in doctorNameList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </template>

      <!--      <template slot="buttons">-->
      <!--        <el-popconfirm-->
      <!--          confirm-button-text='确定'-->
      <!--          cancel-button-text='导出全部'-->
      <!--          icon="el-icon-info"-->
      <!--          icon-color="red"-->
      <!--          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">-->
      <!--          <el-button slot="reference" type="success">导出Excel</el-button>-->
      <!--        </el-popconfirm>-->
      <!--      </template>-->

      <template slot="buttons">
        <el-button type="success" @click="exportExcel" style="margin-right: 15px">导出Excel</el-button>
      </template>

      <template slot="buttonsMiddle">
        <el-radio-group v-model="listQuery.queryType" size="mini" @change="changeSelectQueryType"
                        v-if="this.$somms.hasHosRole()">
          <el-radio-button :label="1">按科室查询</el-radio-button>
          <el-radio-button :label="2">按医生查询</el-radio-button>
        </el-radio-group>
      </template>

      <template slot="containerContent">
        <div style="height:36%">
          <el-row :gutter="10" style="height: 100%">
            <el-col :span="24" style="height: 100%">
              <div class="rankSelect">
                <span style="font-size: 10px;">排名指标：</span>
                <el-select v-model="rankSelect" placeholder="请选择排名指标" size="mini" @change="changeSelectDoctorRank">
                  <el-option v-for="item in rankList" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </div>
              <div id="doctorChart" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="height: 64.2%;">
          <el-table ref="doctorAnalysisTable" id="doctorTable" size="mini"
                    :header-cell-style="{'text-align' : 'center'}" height="98%" stripe :data="list"
                    :summary-method="getSummaries" show-summary @sort-change='sortChange' v-loading="listLoading" border>
            <el-table-column label="序号" fixed align="center" type="index" width="50">
            </el-table-column>
            <el-table-column label="出院科室编码" prop="priOutHosDeptCode" align="center" width="200" v-if="false">
              <template slot-scope="scope">{{ scope.row.priOutHosDeptCode | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="出院科室名称" prop="priOutHosDeptName" align="left" :show-overflow-tooltip="true"
                             width="150" v-if="showDept" fixed>
              <template slot-scope="scope">{{ scope.row.priOutHosDeptName | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="医生所属科室" prop="doctorDepts" align="left" width="150" :show-overflow-tooltip="true"
                             v-if="showDoctorDepts">
              <template slot-scope="scope">{{ scope.row.doctorDepts | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="医生编码" prop="drCodg" align="center" width="90" v-if="false">
              <template slot-scope="scope">{{ scope.row.drCodg | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="医生姓名" fixed prop="drName" align="center" :show-overflow-tooltip="true" width="90">
              <template slot-scope="scope">
                <div v-if="Number(scope.row.totalMedicalNum)>0" class='skip' @click="queryTotalMedicalNum(scope.row)">
                  {{ scope.row.drName | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.totalMedicalNum)==0" style="color:#000000">
                  {{ scope.row.drName | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="总病案数" prop="totalMedicalNum" align="center" width="110" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.totalMedicalNum)>0" class='skip' @click="queryTotalMedicalNum(scope.row)">
                  {{ scope.row.totalMedicalNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.totalMedicalNum)==0" style="color:#000000">
                  {{ scope.row.totalMedicalNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>


            <el-table-column label="正常倍率病案数" prop="normalMedicalNum" align="center" width="110" sortable='custom'>
              <!-- 自组件传过来 -->
              <template slot-scope="scope">
                <!-- 跳转 -->
                <div v-if="Number(scope.row.normalMedicalNum) > 0" class='skip' @click="queryPrePay(scope.row)">
                  {{ scope.row.normalMedicalNum | formatIsEmpty }}
                  <!-- 111 -->
                </div>
                <div v-if="Number(scope.row.normalMedicalNum) == 0" style="color:#000000">
                  {{ scope.row.normalMedicalNum | formatIsEmpty }}
                  <!-- 111 -->
                </div>
              </template>
            </el-table-column>


            <el-table-column label="入组病案数" align="center" width="110" prop="inGroupMedicalNum" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.inGroupMedicalNum)>0" class='skip'
                     @click="queryInGroupMedicalNum(scope.row)">
                  {{ scope.row.inGroupMedicalNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.inGroupMedicalNum)==0" style="color:#000000">
                  {{ scope.row.inGroupMedicalNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="未组病案数" align="center" width="110" prop="notGroupMedicalNum" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.notGroupMedicalNum)>0" class='skip'
                     @click="queryNotGroupMedicalNum(scope.row)">
                  {{ scope.row.notGroupMedicalNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.notGroupMedicalNum)==0" style="color:#000000">
                  {{ scope.row.notGroupMedicalNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="DRGs组数" align="center" width="110" prop="drgGroupNum" sortable='custom'>
              <template slot-scope="scope">
                <div v-if="Number(scope.row.drgGroupNum)>0" class='skip' @click="queryDrgGroupNum(scope.row)">
                  {{ scope.row.drgGroupNum | formatIsEmpty }}
                </div>
                <div v-if="Number(scope.row.drgGroupNum)==0" style="color:#000000">
                  {{ scope.row.drgGroupNum | formatIsEmpty }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="基准点数" align="right" width="110" prop="referSco" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.referSco | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="支付点数" align="right" width="110" prop="totlSco" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.totlSco | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="预测费用" align="right" width="110" prop="preCost" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.preCost | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="住院总费用" align="right" width="110" prop="iptSumfee" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.iptSumfee | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="盈亏额" align="right" width="110" prop="diff" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.diff | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="CMI指数" align="center" width="110" prop="cmi" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.cmi | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="总权重" align="center" width="110" prop="totalDrgWeight" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.totalDrgWeight | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="平均住院日" align="center" width="110" prop="avgDays" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.avgDays | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="入组平均住院日" align="center" width="140" prop="inGroupAvgDays" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.inGroupAvgDays | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="平均住院费用" align="right" width="140" prop="avgCost" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.avgCost | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="入组平均住院费用" align="right" width="150" prop="inGroupAvgCost" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.inGroupAvgCost | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="时间消耗指数" align="center" width="130" prop="timeIndex" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.timeIndex | formatIsEmpty }}</template>
              no
            </el-table-column>
            <el-table-column label="费用消耗指数" align="center" width="130" prop="costIndex" sortable='custom'>
              <template slot-scope="scope">{{ scope.row.costIndex | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="药品费" align="right" width="120" prop="medicalCost" :show-overflow-tooltip='true'>
              <template slot-scope="scope">{{ scope.row.medicalCost | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="药占比" align="center" width="120" prop="medicalCostRate"
                             :show-overflow-tooltip='true'>
              <template slot-scope="scope">{{ scope.row.medicalCostRate | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="耗材费" align="right" width="120" prop="materialCost" :show-overflow-tooltip='true'>
              <template slot-scope="scope">{{ scope.row.materialCost | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="耗占比" align="center" width="120" prop="materialCostRate"
                             :show-overflow-tooltip='true'>
              <template slot-scope="scope">{{ scope.row.materialCostRate | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="总费用" align="right" width="120" prop="iptSumfee" :show-overflow-tooltip='true'>
              <template slot-scope="scope">{{ scope.row.iptSumfee | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="预测费用" align="right" width="120" prop="preCost" :show-overflow-tooltip='true'>
              <template slot-scope="scope">{{ scope.row.preCost | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="盈亏金额" align="right" width="120" prop="diff" :show-overflow-tooltip='true'>
              <template slot-scope="scope">{{ scope.row.diff | formatIsEmpty }}</template>
            </el-table-column>
            <el-table-column label="单价" align="right" width="120" prop="price" :show-overflow-tooltip='true'>
              <template slot-scope="scope">{{ scope.row.price | formatIsEmpty }}</template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, queryMedicalDoctorSelectInput, querySelectTreeAndSelectList } from '@/api/common/drgCommon'
import { fetchList as queryPageData, getCountInfo } from '@/api/hospitalAnalysis/doctorAnalysis'
import { elExportExcel } from '@/utils/exportExcel'
import echarts from 'echarts'
import { sortChange } from '@/utils/common'

const defaultListQuery = {
  cysj: null,
  pageNum: 1,
  pageSize: 200,
  doctorType: null,
  drCodg: null,
  b16c: null,
  queryType: '1',
  cy_start_date: this.cy_start_date,
  cy_end_date: this.cy_end_date
}
export default {
  name: 'doctorTargets',
  inject: ['reload'],
  components: {},
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      doctorNameList: null, // 医生姓名下拉
      listLoading: true,
      list: [],
      total: null,
      cy_start_date: null,
      cy_end_date: null,
      listQuery: { ...Object.assign({}, defaultListQuery), queryType: '1' },
      submitListQuery: Object.assign({}, defaultListQuery),
      queryType: '1',
      showDoctorDepts: false,
      showDept: true,
      rankSelect: '4', // 默认权重排名
      deptName: null,
      tableHeight: 0,
      b16c: null,
      rankList: [
        { value: '0', label: '总病案数排名' },
        { value: '1', label: '入组病案数排名' },
        { value: '2', label: '未组病案数' },
        { value: '3', label: 'DRGs组数排名' },
        { value: '4', label: '总权重排名' },
        { value: '5', label: 'CMI指数排名' },
        { value: '6', label: '总平均住院日排名' },
        { value: '7', label: '入组平均住院日排名' },
        { value: '8', label: '总平均住院费用排名' },
        { value: '9', label: '入组平均住院费用排名' },
        { value: '10', label: '时间消耗指数排名' },
        { value: '11', label: '费用消耗指数排名' },
        { value: '12', label: '药品费排名' },
        { value: '13', label: '耗材费排名' }
      ],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  watch: {
    b16c: function () {
      if (this.listQuery.b16c) {
        this.deptName = document.getElementById('b13c').getElementsByTagName('input')[0].value
      } else {
        this.deptName = null
      }
      this.getList()
      this.getCount()
      this.getDoctor()
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    if (!this.$somms.hasHosRole()) {
      this.listQuery.queryType = '2'
      this.changeSelectQueryType(2)
    }
    // 获取数据查询时间
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatDocotrNameIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '未填写'
      }
    }
  },
  // 在vue生命周期updated中添加方法（使用该方法要给table里加ref="table"）
  updated: function () {
    this.$nextTick(() => {
      this.$refs['doctorAnalysisTable'].doLayout()
    })
  },
  methods: {
    sortChange,
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'DOCTORTYPE')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length,
        customAverage: (values) => {
          const sum = values.reduce((prev, curr) => prev + curr, 0)
          return sum / values.length
        }
      }
      let sum20 = 0, sum22 = 0, sum24 = 0
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计'
          return
        }
        const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value))
        if (index === 3 || index === 4 || index === 5) {
          sums[index] = calculations.sum(values)
        } else if (index === 7 || index === 8 || index === 9 || index === 10 || index === 11 || index === 12 || index === 13 || index === 20 || index === 22 || index === 24 || index === 25 || index === 26) {
          sums[index] = calculations.sum(values).toFixed(2)
          if (index === 20) sum20 = sums[index]
          else if (index === 24) sum24 = sums[index]
          else if (index === 22) sum22 = sums[index]
        } else if (index === 12 || index === 14 || index === 15 || index === 16 || index === 17 || index === 18 || index === 19|| index === 27) {
          sums[index] = calculations.average(values).toFixed(2)
        } else {
          sums[index] = ' '
        }
      })
      sums[21] = calculatePercentage(sum20, sum24)
      sums[23] = calculatePercentage(sum22, sum24)
      return sums

      function calculatePercentage (numerator, denominator) {
        if (denominator !== 0) {
          return ((numerator / denominator) * 100).toFixed(2) + '%'
        }
        return '0.00%'
      }
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        // 查询数据
        this.getList()
        this.getCount()
        this.getDoctor()
      })
    },
    getDoctor () {
      const params = {
        b16c: this.listQuery.deptCode,
        doctorType: this.listQuery.doctorType
      }
      queryMedicalDoctorSelectInput(params).then((response) => {
        this.doctorNameList = response.data
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.doctorType = this.listQuery.doctorType
      this.submitListQuery.drCodg = this.listQuery.drCodg
      this.submitListQuery.queryType = this.listQuery.queryType
      this.submitListQuery.pageNum = this.listQuery.pageNum
      this.submitListQuery.pageSize = this.listQuery.pageSize
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.doctorAnalysisTable.$children, document.getElementById('doctorTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DRG医生指标')
    },
    getCount () {
      this.submitListQuery.cy_start_date = this.listQuery.begnDate
      this.submitListQuery.cy_end_date = this.listQuery.expiDate
      this.submitListQuery.b16c = this.listQuery.deptCode
      this.submitListQuery.doctorType = this.listQuery.doctorType
      this.submitListQuery.drCodg = this.listQuery.drCodg
      this.submitListQuery.queryType = this.listQuery.queryType
      this.submitListQuery.inStartTime = this.listQuery.inStartTime
      this.submitListQuery.inEndTime = this.listQuery.inEndTime
      this.submitListQuery.inHosFlag = this.listQuery.inHosFlag
      this.submitListQuery.seStartTime = this.listQuery.seStartTime
      this.submitListQuery.seEndTime = this.listQuery.seEndTime
      getCountInfo(this.submitListQuery).then(response => {
        let result = response.data
        if (result.length > 0) {
          switch (this.rankSelect) {
            case '0':
              this.getMedicalNum(result)
              break
            case '1':
              this.getInGroupNum(result)
              break
            case '2':
              this.getNotGroupNum(result)
              break
            case '3':
              this.getDrgNum(result)
              break
            case '4':
              this.getDrgWeight(result)
              break
            case '5':
              this.getCmi(result)
              break
            case '6':
              this.getAvgDays(result)
              break
            case '7':
              this.getInGroupAvgDays(result)
              break
            case '8':
              this.getAvgCost(result)
              break
            case '9':
              this.getIngroupAvgCost(result)
              break
            case '10':
              this.getTimeIndex(result)
              break
            case '11':
              this.getCostIndex(result)
              break
            case '12':
              this.getMedicalCost(result)
              break
            case '13':
              this.getMaterialCost(result)
              break
            default:
              this.getDrgWeight(result)
          }
        }
      })
    },
    getMedicalNum (result) {
      result.sort(function (o1, o2) {
        return Number(o2.totalMedicalNum) - Number(o1.totalMedicalNum)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生姓名
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].totalMedicalNum)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].totalMedicalNumRate) // 占比或者标杆值
        }
      }
      let legendData = ['总病案数', '相对全院总病案数占比']
      let profttl = '医生总病案数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = '总病案数'
      let tool2 = '总病案数占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getInGroupNum (result) {
      result.sort(function (o1, o2) {
        return Number(o2.inGroupMedicalNum) - Number(o1.inGroupMedicalNum)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生姓名
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].inGroupMedicalNum)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].inGroupMedicalNumRate) // 占比或者标杆值
        }
      }
      let legendData = ['入组病案数', '相对全院入组病案数占比']
      let profttl = '医生入组病案数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = '入组病案数'
      let tool2 = '入组病案数占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getNotGroupNum (result) {
      result.sort(function (o1, o2) {
        return Number(o2.notGroupMedicalNum) - Number(o1.notGroupMedicalNum)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生姓名
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].notGroupMedicalNum)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].notGroupMedicalNumRate) // 占比或者标杆值
        }
      }
      let legendData = ['未组病案数', '相对全院未组病案数占比']
      let profttl = '医生未组病案数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = '未组病案数'
      let tool2 = '未组病案数占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getDrgNum (result) {
      result.sort(function (o1, o2) {
        return Number(o2.drgGroupNum) - Number(o1.drgGroupNum)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生姓名
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].drgGroupNum)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].drgGroupNumRate) // 占比或者标杆值
        }
      }
      let legendData = ['DRGs组数', '相对全院DRGs组数占比']
      let profttl = '医生DRGs组数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = 'DRGs组数'
      let tool2 = 'DRGs组数占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getDrgWeight (result) {
      result.sort(function (o1, o2) {
        return Number(o2.totalDrgWeight) - Number(o1.totalDrgWeight)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生姓名
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].totalDrgWeight)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].totalDrgWeightRate) // 占比或者标杆值
        }
      }
      let legendData = ['医生权重', '相对全院总权重占比']
      let profttl = '医生权重排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = '%'
      let tool1 = '权重'
      let tool2 = '权重占比'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 1)
    },
    getCmi (result) {
      result.sort(function (o1, o2) {
        return Number(o2.cmi) - Number(o1.cmi)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生姓名
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].cmi)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosCmi) // 占比或者标杆值
        }
      }
      let legendData = ['医生CMI指数', '全院CMI指数']
      let profttl = '医生CMI指数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = 'CMI指数'
      let tool2 = '全院CMI指数'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getAvgDays (result) {
      result.sort(function (o1, o2) {
        return Number(o2.avgDays) - Number(o1.avgDays)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生姓名
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].avgDays)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosAvgDays) // 占比或者标杆值
        }
      }
      let legendData = ['平均住院日', '全院平均住院日']
      let profttl = '医生接纳病人平均住院日排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '平均住院日'
      let tool2 = '全院平均住院日'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getInGroupAvgDays (result) {
      result.sort(function (o1, o2) {
        return Number(o2.inGroupAvgDays) - Number(o1.inGroupAvgDays)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生姓名
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].inGroupAvgDays)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosInGroupAvgDays) // 占比或者标杆值
        }
      }
      let legendData = ['入组平均住院日', '全院入组平均住院日']
      let profttl = '医生接纳病人入组平均住院日排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '入组平均住院日'
      let tool2 = '全院入组平均住院日'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getAvgCost (result) {
      result.sort(function (o1, o2) {
        return Number(o2.avgCost) - Number(o1.avgCost)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生姓名
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].avgCost)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosAvgCost) // 占比或者标杆值
        }
      }
      let legendData = ['平均住院费用', '全院平均住院费用']
      let profttl = '医生接纳病人平均住院费用排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '平均住院费用'
      let tool2 = '全院平均住院费用'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getIngroupAvgCost (result) {
      result.sort(function (o1, o2) {
        return Number(o2.inGroupAvgCost) - Number(o1.inGroupAvgCost)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生姓名
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].inGroupAvgCost)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosInGroupAvgCost) // 占比或者标杆值
        }
      }
      let legendData = ['入组平均住院费用', '全院入组平均住院费用']
      let profttl = '医生接纳病人入组平均住院费用排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '入组平均住院费用'
      let tool2 = '全院入组平均住院费用'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getTimeIndex (result) {
      result.sort(function (o1, o2) {
        return Number(o2.timeIndex) - Number(o1.timeIndex)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生姓名
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].timeIndex)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosTimeIndex) // 占比或者标杆值
        }
      }
      let legendData = ['时间消耗指数', '全院时间消耗指数']
      let profttl = '医生接纳病人时间消耗指数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '时间消耗指数'
      let tool2 = '全院时间消耗指数'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getCostIndex (result) {
      result.sort(function (o1, o2) {
        return Number(o2.costIndex) - Number(o1.costIndex)
      })
      let barData = [] // 指标数据
      let xAxisData = [] // 医生姓名
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].costIndex)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosCostIndex) // 占比或者标杆值
        }
      }
      let legendData = ['费用消耗指数', '全院费用消耗指数']
      let profttl = '医生接纳病人费用消耗指数排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '费用消耗指数'
      let tool2 = '全院费用消耗指数'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getMedicalCost (result) {
      result.sort(function (o1, o2) {
        return Number(o2.avgDrugFee) - Number(o1.avgDrugFee)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].avgDrugFee)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosAvgMedicalCost) // 占比或者标杆值
        }
      }
      let legendData = ['平均药品费', '全院平均药品费']
      let profttl = '医生接纳病人平均药品费用排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '平均药品费用'
      let tool2 = '全院平均药品费用'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    getMaterialCost (result) {
      result.sort(function (o1, o2) {
        return Number(o2.avgMcsFee) - Number(o1.avgMcsFee)
      })
      let barData = [] // 指标数据
      let xAxisData = []
      let lineData = [] // 占比或者标杆值
      let count = result.length > 20 ? 20 : result.length
      if (result) {
        for (let i = 0; i < count; i++) {
          barData.push(result[i].avgMcsFee)
          xAxisData.push(result[i].drName)
          lineData.push(result[i].hosAvgMaterialCost) // 占比或者标杆值
        }
      }
      let legendData = ['平均耗材费', '全院平均耗材费用']
      let profttl = '医生接纳病人平均耗材费用排名TOP' + count
      let yLeftUnit = ''
      let yRightUnit = ''
      let tool1 = '平均耗材费用'
      let tool2 = '全院平均耗材费用'
      this.getChart(barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, 0)
    },
    // 下转
    queryTotalMedicalNum (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({
          path: '/common/queryMedicalDetail',
          query: {
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            drCodg: row.drCodg,
            drName: row.drName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '2'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({
          path: '/common/queryMedicalDetail',
          query: {
            drCodg: row.drCodg,
            drName: row.drName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '2'
          }
        })
      }
    },
    //跳转支付预测
    queryPrePay(row){
      this.$router.push({
        path: '/hosDrgAnalysis/predictPay',
        query: {
          // priOutHosDeptCode: row.priOutHosDeptCode,
          // priOutHosDeptName: row.priOutHosDeptName,
          drCodg: row.drCodg,
          // drName: row.drName,
          // inHosFlag: this.listQuery.inHosFlag,
          cy_start_date: this.listQuery.begnDate,
          cy_end_date: this.listQuery.expiDate,
          inStartTime: this.listQuery.inStartTime,
          inEndTime: this.listQuery.inEndTime,
          seStartTime: this.listQuery.seStartTime,
          seEndTime: this.listQuery.seEndTime,
          diseType: "3"
        }
      })
    },
    queryInGroupMedicalNum (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({
          path: '/common/queryMedicalDetail',
          query: {
            queryType: 'groupNum',
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            drCodg: row.drCodg,
            drName: row.drName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '2'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({
          path: '/common/queryMedicalDetail',
          query: {
            queryType: 'groupNum',
            drCodg: row.drCodg,
            drName: row.drName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '2'
          }
        })
      }
    },
    queryNotGroupMedicalNum (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({
          path: '/common/queryMedicalDetail',
          query: {
            queryType: 'noGroupNum',
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            drCodg: row.drCodg,
            drName: row.drName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            type: '2'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({
          path: '/common/queryMedicalDetail',
          query: {
            queryType: 'noGroupNum',
            drCodg: row.drCodg,
            drName: row.drName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '2'
          }
        })
      }
    },
    queryDrgGroupNum (row) {
      let deptName = '全院'
      if (this.deptName != null) {
        deptName = this.deptName
      }
      if (this.submitListQuery.queryType == '1') {
        this.$router.push({
          path: '/common/queryDrgDetail',
          query: {
            priOutHosDeptCode: row.priOutHosDeptCode,
            priOutHosDeptName: row.priOutHosDeptName,
            drCodg: row.drCodg,
            drName: row.drName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inEndTime: this.listQuery.inEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '2'
          }
        })
      } else if (this.submitListQuery.queryType == '2') {
        this.$router.push({
          path: '/common/queryDrgDetail',
          query: {
            drCodg: row.drCodg,
            drName: row.drName,
            priOutHosDeptCode: this.submitListQuery.b16c,
            priOutHosDeptName: deptName,
            cy_start_date: this.listQuery.begnDate,
            cy_end_date: this.listQuery.expiDate,
            inStartTime: this.listQuery.inStartTime,
            inEndTime: this.listQuery.inEndTime,
            seStartTime: this.listQuery.seStartTime,
            seEndTime: this.listQuery.seEndTime,
            inHosFlag: this.listQuery.inHosFlag,
            type: '2'
          }
        })
      }
    },
    changeSelectDoctorRank (value) {
      this.rankSelect = value
      let doctorChart = echarts.getInstanceByDom(document.getElementById('doctorChart'))
      doctorChart.clear()
      this.getCount()
    },
    changeSelectQueryType (value) {
      if (value == 1) {
        this.showDoctorDepts = false
        this.showDept = true
      } else if (value == 2) {
        this.showDoctorDepts = true
        this.showDept = false
      }
      this.handleSearchList()
    },
    changeSelectDoctorType () {
      this.getDoctor()
      this.getList()
      this.getCount()
    },
    changeSelectDoctor () {
      this.getList()
      this.getCount()
    },
    dateChangeCysj (val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
      this.getCount()
    },
    handleSizeChange (val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList () {
      this.listQuery.pageNum = 1
      if (this.listQuery.b16c) {
        this.deptName = document.getElementById('b13c').getElementsByTagName('input')[0].value
      } else {
        this.deptName = null
      }
      this.getList()
      this.getCount()
    },
    handleResetSearch () {
      // this.listQuery = Object.assign({}, defaultListQuery);
      // this.showDept = true;
      // this.rankSelect='4';
      // this.getDataIsuue();
      this.reload()
    },
    getChart (barData, xAxisData, lineData, legendData, profttl, yLeftUnit, yRightUnit, tool1, tool2, flag) {
      let option = {
        title: [{ text: profttl, left: '20', top: '5', textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 16 } }],
        tooltip: {
          trigger: 'item',
          formatter: function (param) {
            let str = '医生姓名：' + xAxisData[param.dataIndex] + '</br>' +
              tool1 + '：' + barData[param.dataIndex] + yLeftUnit + '</br>'
            if (flag == 1) {
              str = str + tool2 + '：' + lineData[param.dataIndex] + '%'
            }
            if (flag == 0) {
              str = str + tool2 + '：' + lineData[param.dataIndex]
            }
            return str
          }
        },
        legend: [{
          data: legendData,
          top: '5',
          left: 'center',
          selectedMode: false
        }],
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            interval: 0,
            fontSize: 10,
            rotate: 40,
            formatter: function (value) {
              return (value.length > 4 ? (value.slice(0, 4) + '..') : value)
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            position: 'left',
            name: yLeftUnit,
            axisLabel: {
              formatter: function (value) {
                if (value > 10000) {
                  return (Number(value) / 10000).toFixed(1) + '万'
                } else {
                  return Number(value).toFixed(1)
                }
              }
            }
          },
          { type: 'value', position: 'right', name: '单位：' + yRightUnit, max: '100', splitLine: { show: false } }
        ],
        series: [{
          name: legendData[0],
          data: barData,
          type: 'bar',
          color: 'red',
          yAxisIndex: 0,
          label: {
            show: true,
            fontSize: 9,
            position: 'top',
            formatter: function (param) {
              if (param.value > 10000) {
                return (Number(param.value) / 10000).toFixed(1) + '万'
              } else {
                return Number(param.value).toFixed(2)
              }
            }
          },
          itemStyle: {
            color: function (param) {
              if (param.dataIndex < 10) {
                return 'rgba(36,185,179,0.7)'
              } else {
                return 'rgba(40,138,242,0.7)'
              }
            }
          }
        },
          {
            name: legendData[1],
            data: lineData,
            type: 'line',
            symbol: 'circle',
            yAxisIndex: flag,
            label: {
              show: true,
              position: 'top',
              fontSize: 10,
              formatter: function (param) {
                return Number(param.value).toFixed(2)
              }
            },
            itemStyle: {
              color: function (param) {
                if (param.value < 80) {
                  return '#FD5E51'
                } else {
                  return '#516FFF'
                }
              }

            }
          }],
        grid: {
          top: '55',
          bottom: '40',
          left: '60',
          right: '30'
        }
      }
      let doctorChart = echarts.getInstanceByDom(document.getElementById('doctorChart'))
      if (doctorChart) {
        doctorChart.clear()
      } else {
        doctorChart = echarts.init(document.getElementById('doctorChart'))
      }
      doctorChart.setOption(option)
      return doctorChart
    },
    exportExcel () {
      let tableId = 'doctorTable'
      let fileName = '医生DRGs指标' + '(' + this.listQuery.begnDate + '-' + this.listQuery.expiDate + ')'
      elExportExcel(tableId, fileName)
    }
  }
}
</script>
<style scoped>
.code {
  font-size: 12px;
  color: #000000;
  text-overflow: ellipsis;
  overflow: hidden;
}

.name {
  font-size: 10px;
  color: #9b9b9b;
  text-overflow: ellipsis;
  overflow: hidden;
}

.rankSelect {
  position: absolute;
  z-index: 1000;
  right: 5px;
}
</style>
