<template>
  <div class="som-comps-container">
    <!-- 基本信息 -->
    <el-descriptions title="基本信息" :column="4" :colon="false" border>
      <el-descriptions-item v-for="(item, index) in basicData"
                            :key="index"
                            :label="item.label">
        {{item.value}}
      </el-descriptions-item>
    </el-descriptions>

    <!-- 入组分析 -->
    <el-descriptions title="入组分析"
                     :column="4"
                     :colon="false" style="margin-top: 3%" border class="descriptions">
      <el-descriptions-item label="DIP"
                            :span="4"
                            label-class-name="medical-quality-analysis-des-label"
                            content-class-name="medical-quality-analysis-des-content"
                            v-if="inGroupData.length > 0">

      </el-descriptions-item>
      <!-- DIP -->
      <template v-for="(item, index) in inGroupData">
        <el-descriptions-item
          :content-style="{ borderRight: ((index == inGroupData.length - 1)
                              && (inGroupData.length % 4 != 0)) ? 'none': '' }"
          :key="index"
          :label="item.label" label-class-name="has-colon"
          v-if="inGroupData.length > 0">
          {{item.value}}
          <span v-if="item.type == 1">例</span>
          <span v-if="item.type == 2 && item.value != 0">%</span>
          <!--        <span v-if="item.type == 3">元</span>-->
          <span v-if="item.type == 4">人</span>
          <span v-if="item.type == 5">天</span>
          <span v-if="item.type == 6">组</span>
        </el-descriptions-item>
      </template>
      <el-descriptions-item :span="4"
                            label-class-name="medical-quality-analysis-des-separate"
                            :content-style="{ borderLeft: 'none' }"
                            :key="1"
                            v-if="inGroupData.length > 0"/>
      <el-descriptions-item label="DRG"
                            :span="4"
                            label-class-name="medical-quality-analysis-des-label"
                            content-class-name="medical-quality-analysis-des-content"
                            v-if="drgInGroupData.length > 0">
      </el-descriptions-item>
      <!-- DRG -->
      <el-descriptions-item v-for="(item, index) in drgInGroupData"
                            :key="index"
                            :label="item.label" label-class-name="has-colon">
        {{item.value}}
        <span v-if="item.type == 1">例</span>
        <span v-if="item.type == 2 && item.value != 0">%</span>
<!--        <span v-if="item.type == 3">元</span>-->
        <span v-if="item.type == 4">人</span>
        <span v-if="item.type == 5">天</span>
        <span v-if="item.type == 6">组</span>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 费用分析 -->
    <el-descriptions title="费用分析" :column="4" :colon="false" style="margin-top: 3%" border>
      <el-descriptions-item v-for="(item, index) in costData"
                            :key="index"
                            :label="item.label">
        {{item.value}}
        <span v-if="item.type == 1">例</span>
        <span v-if="item.type == 2 && item.value != 0">%</span>
<!--        <span v-if="item.type == 3">元</span>-->
        <span v-if="item.type == 4">人</span>
        <span v-if="item.type == 5">天</span>
        <span v-if="item.type == 6">组</span>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 费用分析 -->
    <el-descriptions title="支付预测" :column="4" :colon="false" style="margin-top: 3%" border>

      <el-descriptions-item label="DIP"
                            :span="4"
                            label-class-name="medical-quality-analysis-des-label"
                            content-class-name="medical-quality-analysis-des-content"
                            v-if="payCostData.length > 0">

      </el-descriptions-item>
      <!-- DIP -->
      <el-descriptions-item v-for="(item, index) in payCostData"
                            :content-style="{ borderRight: ((index == payCostData.length - 1) && (payCostData.length % 4 != 0)) ? 'none': '' }"
                            :key="index"
                            :contentStyle="{background: getCompareColor(item)}"
                            :label="item.label">
        {{item.value}}
        <span v-if="item.type == 1">例</span>
        <span v-if="item.type == 2 && item.value != 0">%</span>
<!--        <span v-if="item.type == 3">元</span>-->
        <span v-if="item.type == 4">人</span>
        <span v-if="item.type == 5">天</span>
        <span v-if="item.type == 6">组</span>
      </el-descriptions-item>

      <el-descriptions-item :span="4"
                            label-class-name="medical-quality-analysis-des-separate"
                            :content-style="{ borderLeft: 'none' }"
                            :key="1"
                            v-if="payCostData.length > 0"/>
      <el-descriptions-item label="DRG"
                            :span="4"
                            label-class-name="medical-quality-analysis-des-label"
                            content-class-name="medical-quality-analysis-des-content"
                            v-if="drgPayCostData.length > 0">
      </el-descriptions-item>

      <!-- DRG -->
      <el-descriptions-item v-for="(item, index) in drgPayCostData"
                            :key="index"
                            :contentStyle="{background: getCompareColor(item)}"
                            :label="item.label">
        {{item.value}}
        <span v-if="item.type == 1">例</span>
        <span v-if="item.type == 2 && item.value != 0">%</span>
<!--        <span v-if="item.type == 3">元</span>-->
        <span v-if="item.type == 4">人</span>
        <span v-if="item.type == 5">天</span>
        <span v-if="item.type == 6">组</span>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
import { handleNumber } from '@/utils/common'

export default {
  name: 'deptComparAnalysisDescription',
  props: {
    data: {},
    drgData: {},
    cost: {},
    payCost: {},
    otherPayCost: {},
    otherDrgPayCost: {},
    drgPayAmt: {}
  },
  data: () => ({
    basicData: [],
    inGroupData: [],
    drgInGroupData: [],
    costData: [],
    payCostData: [],
    drgPayCostData: []
  }),
  methods: {
    handleNumber,
    handlerData (data) {
      if (!data.typeData) {
        this.basicData = []
        this.basicData.push({ label: '科室编码', value: data.deptCode })
        this.basicData.push({ label: '科室名称', value: data.deptName })
      }
      if (data.typeData) {
        this.inGroupData = []
      } else {
        this.inGroupData = []
        this.inGroupData.push({ label: '病案总数', value: data.medcasVal, type: 1 })
        this.inGroupData.push({ label: 'DIP组数', value: data.dipNum, type: 6 })
        this.inGroupData.push({ label: '入组病案数', value: data.drgInGroupMedcasVal, type: 1 })
        this.inGroupData.push({ label: '未入组病案数', value: data.notInGroupNum, type: 1 })
        this.inGroupData.push({ label: '入组率', value: data.inGroupRate, type: 2 })
        this.inGroupData.push({ label: '权重', value: data.dipWt })
        this.inGroupData.push({ label: 'cmi', value: data.cmi })
        this.inGroupData.push({ label: '住院总费用', value: this.handleNumber(data.sumfee), type: 3 })
        this.inGroupData.push({ label: '平均住院费用', value: this.handleNumber(data.avgCost), type: 3 })
        this.inGroupData.push({ label: '平均住院天数', value: data.actIpt, type: 5 })
      }
    },
    handlerCost (cost) {
      this.costData = []
      this.costData.push({ label: '床位费', value: this.handleNumber(cost.cwf), type: 3 })
      this.costData.push({ label: '诊查费', value: this.handleNumber(cost.zcf), type: 3 })
      this.costData.push({ label: '检查费', value: this.handleNumber(cost.jcf), type: 3 })
      this.costData.push({ label: '化验费', value: this.handleNumber(cost.hyf), type: 3 })
      this.costData.push({ label: '治疗费', value: this.handleNumber(cost.treat_fee), type: 3 })
      this.costData.push({ label: '手术费', value: this.handleNumber(cost.ssf), type: 3 })
      this.costData.push({ label: '护理费', value: this.handleNumber(cost.nursfee), type: 3 })
      this.costData.push({ label: '卫生材料费', value: this.handleNumber(cost.wsclf), type: 3 })
      this.costData.push({ label: '西药费', value: this.handleNumber(cost.west_fee), type: 3 })
      this.costData.push({ label: '中药饮片费', value: this.handleNumber(cost.zyypf), type: 3 })
      this.costData.push({ label: '中成药', value: this.handleNumber(cost.zcy), type: 3 })
      this.costData.push({ label: '一般治疗费', value: this.handleNumber(cost.ybzlf), type: 3 })
      this.costData.push({ label: '挂号费', value: this.handleNumber(cost.ghf), type: 3 })
      this.costData.push({ label: '其他费', value: this.handleNumber(cost.qt), type: 3 })
    },
    handlerDrgData (drgData) {
      if (!drgData.typeData) {
        this.basicData = []
        this.drgInGroupData = []
        this.basicData.push({ label: '科室编码', value: drgData.deptCode })
        this.basicData.push({ label: '科室名称', value: drgData.deptName })
      }
      if (drgData.typeData) {
        this.drgInGroupData = []
      } else {
        this.drgInGroupData.push({ label: '病案总数', value: drgData.medcasVal, type: 1 })
        this.drgInGroupData.push({ label: 'DRGs组数', value: drgData.drgNum, type: 6 })
        this.drgInGroupData.push({ label: '入组病案数', value: drgData.drgInGroupMedcasVal, type: 1 })
        this.drgInGroupData.push({ label: '未入组病案数', value: drgData.notInGroupNum, type: 1 })
        this.drgInGroupData.push({ label: '入组率', value: drgData.inGroupRate, type: 2 })
        this.drgInGroupData.push({ label: '权重', value: drgData.dipWt })
        this.drgInGroupData.push({ label: 'cmi', value: drgData.cmi })
        this.drgInGroupData.push({ label: '住院总费用', value: this.handleNumber(drgData.sumfee), type: 3 })
        this.drgInGroupData.push({ label: '平均住院费用', value: this.handleNumber(drgData.avgCost), type: 3 })
        this.drgInGroupData.push({ label: '平均住院天数', value: drgData.actIpt, type: 5 })
      }
    },
    handlerPayCost (costData, otherData) {
      if (this.otherDipData) {
        otherData = this.otherDipData
      }
      this.payCostData = []
      this.payCostData.push({ label: '城乡预测费用', value: this.handleNumber(costData.cxpredictCost), type: 3 })
      this.payCostData.push({ label: '城乡支付总费用', value: this.handleNumber(costData.cxtotalCost), type: 3 })
      if (otherData) {
        let style = (Math.abs(costData.cxyccy) == Math.abs(otherData.cxyccy)) ? 0 : (Math.abs(costData.cxyccy) > Math.abs(otherData.cxyccy) ? 1 : 2)
        if (costData.cxyccy && otherData.cxyccy) {
          this.payCostData.push({ label: '城乡差异', value: this.handleNumber(costData.cxyccy), type: 3, style: style })
        } else {
          this.payCostData.push({ label: '城乡差异', value: this.handleNumber(costData.cxyccy), type: 3 })
        }
      } else {
        this.payCostData.push({ label: '城乡差异', value: this.handleNumber(costData.cxyccy), type: 3 })
      }
      this.payCostData.push({ label: '城乡差异比', value: costData.cxcyb, type: 2 })
      this.payCostData.push({ label: '城职预测费用', value: this.handleNumber(costData.czpredictCost), type: 3 })
      this.payCostData.push({ label: '城职支付总费用', value: this.handleNumber(costData.cztotalCost), type: 3 })
      if (otherData) {
        let style = (Math.abs(costData.czyccy) == Math.abs(otherData.czyccy)) ? 0 : (Math.abs(costData.czyccy) > Math.abs(otherData.czyccy) ? 1 : 2)
        if (costData.czyccy && otherData.czyccy) {
          this.payCostData.push({ label: '城职差异', value: this.handleNumber(costData.czyccy), type: 3, style: style })
        } else {
          this.payCostData.push({ label: '城职差异', value: this.handleNumber(costData.czyccy), type: 3 })
        }
      } else {
        this.payCostData.push({ label: '城职差异', value: this.handleNumber(costData.czyccy), type: 3 })
      }
      this.payCostData.push({ label: '城职差异比', value: costData.czcyb, type: 2 })
      this.payCostData.push({ label: '预测总费用', value: this.handleNumber(costData.predictCost), type: 3 })
      this.payCostData.push({ label: '支付总费用', value: this.handleNumber(costData.payCost), type: 3 })
      if (otherData) {
        let style = (Math.abs(costData.yccy) == Math.abs(otherData.yccy)) ? 0 : (Math.abs(costData.yccy) > Math.abs(otherData.yccy) ? 1 : 2)
        if (costData.yccy && otherData.yccy) {
          this.payCostData.push({ label: '差异', value: this.handleNumber(costData.yccy), type: 3, style: style })
        } else {
          this.payCostData.push({ label: '差异', value: this.handleNumber(costData.yccy), type: 3 })
        }
      } else {
        this.payCostData.push({ label: '差异', value: this.handleNumber(costData.yccy), type: 3 })
      }
      this.payCostData.push({ label: '差异比', value: costData.cyb, type: 2 })
      this.payCostData.push({ label: '城乡人数', value: costData.cxtotalCount, type: 4 })
      this.payCostData.push({ label: '城职人数', value: costData.cztotalCount, type: 4 })
      this.payCostData.push({ label: '总人数', value: costData.count, type: 4 })
      if (costData.typeData) {
        this.payCostData = []
      }
    },
    handlerDrgPayCost (drgCostData, otherData) {
      this.drgPayCostData = []
      this.drgPayCostData.push({ label: '城乡预测费用', value: this.handleNumber(drgCostData.cxpredictCost), type: 3 })
      this.drgPayCostData.push({ label: '城乡支付总费用', value: this.handleNumber(drgCostData.cxtotalCost), type: 3 })
      if (otherData) {
        let style = (Math.abs(drgCostData.cxyccy) == Math.abs(otherData.cxyccy)) ? 0 : (Math.abs(drgCostData.cxyccy) > Math.abs(otherData.cxyccy) ? 1 : 2)
        if (drgCostData.cxyccy && otherData.cxyccy) {
          this.drgPayCostData.push({ label: '城乡差异', value: this.handleNumber(drgCostData.cxyccy), type: 3, style: style })
        } else {
          this.drgPayCostData.push({ label: '城乡差异', value: this.handleNumber(drgCostData.cxyccy), type: 3 })
        }
      } else {
        this.drgPayCostData.push({ label: '城乡差异', value: this.handleNumber(drgCostData.cxyccy), type: 3 })
      }
      this.drgPayCostData.push({ label: '城乡差异比', value: drgCostData.cxcyb, type: 2 })
      this.drgPayCostData.push({ label: '城职预测费用', value: this.handleNumber(drgCostData.czpredictCost), type: 3 })
      this.drgPayCostData.push({ label: '城职支付总费用', value: this.handleNumber(drgCostData.cztotalCost), type: 3 })
      if (otherData) {
        let style = (Math.abs(drgCostData.czyccy) == Math.abs(otherData.czyccy)) ? 0 : (Math.abs(drgCostData.czyccy) > Math.abs(otherData.czyccy) ? 1 : 2)
        if (drgCostData.czyccy && otherData.czyccy) {
          this.drgPayCostData.push({ label: '城职差异', value: this.handleNumber(drgCostData.czyccy), type: 3, style: style })
        } else {
          this.drgPayCostData.push({ label: '城职差异', value: this.handleNumber(drgCostData.czyccy), type: 3 })
        }
      } else {
        this.drgPayCostData.push({ label: '城职差异', value: this.handleNumber(drgCostData.czyccy), type: 3 })
      }
      this.drgPayCostData.push({ label: '城职差异比', value: drgCostData.czcyb, type: 2 })
      this.drgPayCostData.push({ label: '预测总费用', value: this.handleNumber(drgCostData.predictCost), type: 3 })
      this.drgPayCostData.push({ label: '支付总费用', value: this.handleNumber(drgCostData.payCost), type: 3 })
      if (otherData) {
        let style = (Math.abs(drgCostData.yccy) == Math.abs(otherData.yccy)) ? 0 : (Math.abs(drgCostData.yccy) > Math.abs(otherData.yccy) ? 1 : 2)
        if (drgCostData.yccy && otherData.yccy) {
          this.drgPayCostData.push({ label: '差异', value: this.handleNumber(drgCostData.yccy), type: 3, style: style })
        } else {
          this.drgPayCostData.push({ label: '差异', value: this.handleNumber(drgCostData.yccy), type: 3 })
        }
      } else {
        this.drgPayCostData.push({ label: '差异', value: this.handleNumber(drgCostData.yccy), type: 3 })
      }
      this.drgPayCostData.push({ label: '差异比', value: drgCostData.cyb, type: 2 })
      this.drgPayCostData.push({ label: '城乡人数', value: drgCostData.cxtotalCount, type: 4 })
      this.drgPayCostData.push({ label: '城职人数', value: drgCostData.cztotalCount, type: 4 })
      this.drgPayCostData.push({ label: '总人数', value: drgCostData.count, type: 4 })
      if (drgCostData.typeData) {
        this.drgPayCostData = []
      }
    },
    getCompareColor (item) {
      if (item && item.type) {
        if (item.style == 1) {
          return '#FDE2E2'
        } else if (item.style == 2) {
          return '#E1F3D8'
        }
      }
      return ''
    }
  },
  watch: {
    data: {
      immediate: true,
      deep: true,
      handler: function (data) {
        this.handlerData(data)
      }
    },
    drgData: {
      immediate: true,
      deep: true,
      handler: function (drgData) {
        this.handlerDrgData(drgData)
      }

    },
    cost: function (cost) {
      this.handlerCost(cost)
    },
    payCost: {
      immediate: true,
      deep: true,
      handler: function (costData) {
        this.handlerPayCost(costData)
      }
    },
    drgPayAmt: {
      immediate: true,
      deep: true,
      handler: function (drgCostData) {
        this.handlerDrgPayCost(drgCostData)
      }
    },
    otherPayCost: {
      deep: true,
      handler: function (costData) {
        if (costData) {
          this.otherDipData = costData
        }
        if (this.payCost) {
          this.handlerPayCost(this.payCost, costData)
        }
      }
    },
    otherDrgPayCost: {
      deep: true,
      handler: function (drgCostData) {
        if (this.drgPayAmt) {
          this.handlerDrgPayCost(this.drgPayAmt, drgCostData)
        }
      }
    }
  }
}
</script>
<style scoped>
 /deep/ .el-descriptions__body{
   margin-bottom: 12px;
 }
</style>
