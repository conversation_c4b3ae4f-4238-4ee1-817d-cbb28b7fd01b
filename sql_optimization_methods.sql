-- ========================================
-- SQL查询优化方案集合
-- ========================================

-- 方法1: 使用EXISTS替代JOIN（推荐）
-- 优势：EXISTS通常比JOIN更高效，特别是在大数据量情况下
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_valid_result_inhosp a
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
WHERE
  a.rule_scen_type = '1'
  AND EXISTS (
    SELECT 1 FROM hcm_settle_zy_b e 
    WHERE e.hisid = a.unique_id 
    AND e.discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
  )
LIMIT 200;

-- 方法2: 分步查询（临时表方式）
-- 优势：将复杂查询分解，减少JOIN复杂度
-- 第一步：获取符合条件的患者ID
CREATE TEMPORARY TABLE temp_valid_patients AS
SELECT DISTINCT hisid 
FROM hcm_settle_zy_b 
WHERE discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59';

-- 第二步：基于患者ID查询详细信息
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_valid_result_inhosp a
  INNER JOIN temp_valid_patients t ON a.unique_id = t.hisid
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
WHERE
  a.rule_scen_type = '1'
LIMIT 200;

DROP TEMPORARY TABLE temp_valid_patients;

-- 方法3: 使用子查询优化JOIN顺序
-- 优势：先过滤小结果集，再进行JOIN
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  (SELECT * FROM hcm_valid_result_inhosp WHERE rule_scen_type = '1') a
  INNER JOIN (
    SELECT hisid FROM hcm_settle_zy_b 
    WHERE discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
  ) e ON a.unique_id = e.hisid
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
LIMIT 200;

-- 方法4: 避免使用LEFT函数，改用年份字段
-- 优势：如果有年份字段，避免函数计算
-- 假设表中有year字段，替换LEFT(a.oprn_date, 4)
SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_settle_zy_b e
  INNER JOIN hcm_valid_result_inhosp a ON a.unique_id = e.hisid
    AND a.rule_scen_type = '1'
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND YEAR(a.oprn_date) = b.rule_year  -- 使用YEAR函数替代LEFT
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = YEAR(a.oprn_date)  -- 使用YEAR函数替代LEFT
WHERE
  e.discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
LIMIT 200;

-- 方法5: 使用UNION ALL分批查询（适用于时间跨度大的情况）
-- 优势：将大时间范围分解为小范围，提高查询效率
(SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_settle_zy_b e
  INNER JOIN hcm_valid_result_inhosp a ON a.unique_id = e.hisid
    AND a.rule_scen_type = '1'
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
WHERE
  e.discharge_date BETWEEN '2024-07-01' AND '2024-12-31'
LIMIT 100)
UNION ALL
(SELECT
  a.rule_valid_result_id AS id,
  a.unique_id AS uniqueId,
  a.rule_scen_type AS ruleScenType,
  a.rule_detl_codg AS ruleDetlCodg,
  a.rule_data_meta AS ruleDataMeta,
  a.error_desc AS errorDesc,
  a.error_detail_codg AS errorDetailCodg,
  a.violation_amount AS violationAmount,
  a.med_list_codg AS medListCodg,
  b.data_name AS dataName,
  d.rule_grp_name AS ruleGrpName,
  a.cnt,
  a.pric,
  a.vola_deg AS volaDeg
FROM
  hcm_settle_zy_b e
  INNER JOIN hcm_valid_result_inhosp a ON a.unique_id = e.hisid
    AND a.rule_scen_type = '1'
  INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
    AND LEFT(a.oprn_date, 4) = b.rule_year
    AND b.data_grp_code = a.rule_data_meta
  INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
    AND d.rule_year = LEFT(a.oprn_date, 4)
WHERE
  e.discharge_date BETWEEN '2025-01-01' AND '2025-08-23'
LIMIT 100);

-- 方法6: 使用窗口函数进行排序优化（如果需要特定排序）
SELECT * FROM (
  SELECT
    a.rule_valid_result_id AS id,
    a.unique_id AS uniqueId,
    a.rule_scen_type AS ruleScenType,
    a.rule_detl_codg AS ruleDetlCodg,
    a.rule_data_meta AS ruleDataMeta,
    a.error_desc AS errorDesc,
    a.error_detail_codg AS errorDetailCodg,
    a.violation_amount AS violationAmount,
    a.med_list_codg AS medListCodg,
    b.data_name AS dataName,
    d.rule_grp_name AS ruleGrpName,
    a.cnt,
    a.pric,
    a.vola_deg AS volaDeg,
    ROW_NUMBER() OVER (ORDER BY e.discharge_date DESC, a.rule_valid_result_id) as rn
  FROM
    hcm_settle_zy_b e
    INNER JOIN hcm_valid_result_inhosp a ON a.unique_id = e.hisid
      AND a.rule_scen_type = '1'
    INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
      AND LEFT(a.oprn_date, 4) = b.rule_year
      AND b.data_grp_code = a.rule_data_meta
    INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
      AND d.rule_year = LEFT(a.oprn_date, 4)
  WHERE
    e.discharge_date BETWEEN '2024-07-01' AND '2025-08-23 23:59:59'
) ranked_results
WHERE rn <= 200;
