<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :show-dip="{ show: showDip}"
             :show-drg="{ show: showDrg }"
             :extendFormIndex="[1]"
             :container="true"
             headerTitle="查询条件"
             contentTitle="标杆例均费用"
             @query="queryData">
      <!-- 查询条件插槽 -->
      <template slot="extendFormItems" >
        <el-form-item label="年份" prop="standardYear">
          <el-date-picker v-model="queryForm.standardYear"
                          type="year"
                          :clearable="false"
                          placeholder="选择年"
                          value-format="yyyy">
          </el-date-picker>
        </el-form-item>
      </template>
      <!-- 按钮插槽 -->
      <template slot="buttons">
        <el-button type="primary" class="som-button-margin-right" @click="addStandardCost">新增标杆费用</el-button>
        <el-button type="success" class="som-button-margin-right" @click="anewScore">重新生成分值</el-button>
        <el-popconfirm confirm-button-text='确定'
                       cancel-button-text='导出全部'
                       icon="el-icon-info"
                       icon-color="red"
                       title="是否导出当前页面？"
                       @confirm="exportExcel"
                       @cancel="allExcel"
                       style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>
      <!-- 内容插槽 -->
      <template slot="containerContent">
<!--        <el-tabs v-model="checkTab" @tab-click="tabClick">-->
<!--          <el-tab-pane label="DIP组" name="1"></el-tab-pane>-->
<!--          <el-tab-pane label="DRG组" name="2"></el-tab-pane>-->
<!--        </el-tabs>-->
        <el-tabs class="som-table-height" v-model="checkTab" @tab-click="tabClick">
          <el-tab-pane label="DIP组" name="DIPData" style="height: 88%" v-if="enabledModules.includes('1')">
            <benchmark-data-result :table-data="tabData.dipData"
                                   :name-type="1"
                                   id="DIPId"
                                   name-ref="DIPRef"
                                   ref="DIPRefData"
                                   :table-loading="tabData.dipTableLoading"
                                   :query-type="queryForm.queryType"
                                   :standardYear="queryForm.standardYear"
                                   @selectData="queryData"/>
            <el-pagination
              background
              align="right"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              layout="total, sizes,prev, pager, next,jumper"
              :page-size="queryForm.pageSize"
              :page-sizes="[200,1000,5000,10000]"
              :current-page.sync="queryForm.pageNum"
              :total="total">
            </el-pagination>
          </el-tab-pane>
          <el-tab-pane label="DRG组" name="DRGsData"  style="height: 88%" v-if="enabledModules.includes('3')">
            <benchmark-data-result :table-data="tabData.drgData"
                                   :name-type="3"
                                   id="DRGsId"
                                   name-ref="DRGsRef"
                                   ref="DRGsRefData"
                                   :table-loading="tabData.drgTableLoading"
                                   :standard-year="queryForm.standardYear"
                                   :query-type="queryForm.queryType" />
            <el-pagination
              background
              align="right"
              @size-change="drgHandleSizeChange"
              @current-change="drgHandleCurrentChange"
              layout="total, sizes,prev, pager, next,jumper"
              :page-size="queryForm.drgPageSize"
              :page-sizes="[200,1000,5000,10000]"
              :current-page.sync="queryForm.drgPageNum"
              :total="drgTotal">
            </el-pagination>
          </el-tab-pane>
        </el-tabs>
      </template>
    </drg-form>
  </div>
</template>

<script>
import benchmarkDataResult from './comps/benchmarkDataResult'
import { insertData, selectDip, queryRatioRange, anewScore, generateScore } from '@/api/dataConfig/benchmarkConfig'
import { elExportExcel } from '@/utils/exportExcel'
import moment from 'moment'

export default {
  name: 'benchmarkConfig',
  inject: ['reload'],
  components: {
    'benchmark-data-result': benchmarkDataResult
  },
  data: () => ({
    queryForm: {
      queryType: 1,
      pageSize: 200,
      pageNum: 1,
      drgPageSize: 200,
      drgPageNum: 1,
      type: '',
      standardYear: moment(new Date()).format('yyyy')
    },
    editForm: {
      queryType: '',
      code: '',
      name: '',
      drgStandardCost: '',
      dipStandardCost: '',
      dipStandardCostLevel: ''
    },
    tabData: {
      dipData: [],
      dipTableLoading: false,
      drgData: [],
      drgTableLoading: false
    },
    addStandardCostRules: {
      code: [
        { required: true, message: '请输入编码', trigger: 'change' }
      ],
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' }
      ],
      drgStandardCost: [
        { required: true, message: '请输入DRG标杆费用', trigger: 'blur' }
      ],
      dipStandardCost: [
        { required: true, message: '请输入DIP标杆费用（同区域）', trigger: 'blur' }
      ],
      dipStandardCostLevel: [
        { required: true, message: '请输入DIP标杆费用（同级别）', trigger: 'blur' }
      ]
    },
    editVisible: false,
    checkTab: 'DIPData',
    total: 0,
    drgTotal: 0,
    showDip: true,
    showDrg: false,
    ratioRangeUp: '',
    ratioRangeDown: '',
    enabledModules: []
  }),
  created () {
    this.queryEnableModule()
  },
  methods: {
    selectDip,
    queryEnableModule () {
      this.enabledModules.push(this.$somms.getGroupType())
      switch (this.$somms.getGroupType()) {
        case '1':
          this.checkTab = 'DIPData'
          break
        case '3':
          this.checkTab = 'DRGsData'
          break
      }
      this.tabClick()
    },
    tabClick () {
      if (this.checkTab == 'DIPData') {
        this.queryForm.queryType = 1
        this.queryForm.drgCodg = ''
      } else {
        this.queryForm.queryType = 3
        this.queryForm.dipCodg = ''
      }
      this.$nextTick(() => {
        this.queryData()
      })
    },
    queryData () {
      let params = this.queryForm
      params.standardYear = moment(this.queryForm.standardYear).format('yyyy')
      if (this.checkTab == 'DIPData') {
        params.queryType = 1
        this.tabData.dipTableLoading = true
        selectDip(params).then(res => {
          this.tabData.dipData = res.data.list
          this.total = res.data.total
          this.tabData.dipTableLoading = false
          this.showDip = true
          this.showDrg = false
        })
      } else if (this.checkTab == 'DRGsData') {
        params.queryType = 3
        this.tabData.drgTableLoading = true
        selectDip(params).then(res => {
          this.tabData.drgData = res.data.list
          this.drgTotal = res.data.total
          this.tabData.drgTableLoading = false
          this.showDip = false
          this.showDrg = true
        })
      }

      this.queryRatioRange()
    },
    queryRatioRange () {
      queryRatioRange(this.queryForm).then(res => {
        if (res.code == 200) {
          this.ratioRangeUp = res.data.ratioRangeUp
          this.ratioRangeDown = res.data.ratioRangeDown
        }
      })
    },
    insertData (editForm) {
      let params = this.editForm
      if (this.queryForm.queryType == 1) {
        params.queryType = this.queryForm.queryType
        params.dipCodg = this.editForm.code
        params.dipName = this.editForm.name
        params.dipStandardCost = this.editForm.dipStandardCost
        params.dipStandardCostLevel = this.editForm.dipStandardCostLevel
        params.standardYear = moment(this.queryForm.standardYear).format('yyyy')
      } else if (this.queryForm.queryType == 3) {
        params.queryType = this.queryForm.queryType
        params.drgCodg = this.editForm.code
        params.drgName = this.editForm.name
        params.drgStandardCost = this.editForm.drgStandardCost
        params.standardYear = moment(this.queryForm.standardYear).format('yyyy')
      }

      this.$refs.editForm.validate((valid) => {
        if (valid) {
          insertData(params).then(result => {
            if (result.code == 200) {
              this.$message.success('新增成功')
            }
            this.editCancel()
          })
        } else {
          return false
        }
      })
    },
    editCancel () {
      this.editVisible = false
      this.editForm = {}
    },
    addStandardCost () {
      this.editVisible = true
    },
    getCurrentYear () {
      let updt_date = new Date()
      this.queryForm.standardYear = updt_date.getFullYear()
    },
    handleSizeChange (val) {
      this.queryForm.pageNum = 1
      this.queryForm.pageSize = val
      this.queryData()
    },
    handleCurrentChange (val) {
      this.queryForm.pageNum = val
      this.queryData()
    },
    anewScore () {
      let method = anewScore
      if (this.$somms.isDIP()) {
        method = generateScore
      }
      this.$confirm('请确认当前标杆是否发生变化，确认重新生成后数据将覆盖？', '提示', {
        type: 'warning'
      }).then(() => {
        method(this.queryForm).then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '生成成功！'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消！'
        })
      })
    },
    drgHandleSizeChange (val) {
      this.queryForm.pageNum = 1
      this.queryForm.pageSize = val
      this.queryData()
    },
    drgHandleCurrentChange (val) {
      this.queryForm.pageNum = val
      this.queryData()
    },
    getDiseaseShowName (type) {
      let suffix = ''
      if (type == 1) {
        suffix = '编码'
      } else {
        suffix = '名称'
      }
      if (this.queryForm.queryType == 1) {
        return 'DIP' + suffix
      }
      if (this.queryForm.queryType == 3) {
        return 'DRG' + suffix
      }
    },
    closedDialog () {
      this.$refs.editForm.clearValidate()
    },
    exportExcel () {
      let tableId = ''
      let fileName = ''
      if (this.checkTab == 'DIPData') {
        tableId = 'DIPId'
        fileName = 'DIP数据'
      }
      if (this.checkTab == 'DRGsData') {
        tableId = 'DRGsId'
        fileName = 'DRG数据'
      }
      elExportExcel(tableId, fileName)
    },
    allExcel () {
      let params = this.queryForm
      params.standardYear = moment(this.queryForm.standardYear).format('yyyy')
      if (this.checkTab == 'DIPData') {
        params.queryType = 1
        this.$somms.exportExcelAll(params, this.total, this.$refs.DIPRefData.$children[0].$children, document.getElementById('DIPId').children[1].children[0].children[1].children[0].childNodes, selectDip, 'DIP组')
      }
      if (this.checkTab == 'DRGsData') {
        params.queryType = 3
        this.$somms.exportExcelAll(params, this.drgTotal, this.$refs.DRGsRefData.$children[0].$children, document.getElementById('DRGsId').children[1].children[0].children[1].children[0].childNodes, selectDip, 'DRG组')
      }
    }
  }
}
</script>
<style scoped>
/deep/ .el-tabs__content{
  height: 100%;
}
</style>
