<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             showPagination
             :totalNum="tableTotalNum"
             headerTitle="查询条件"
             contentTitle="规则列表"
             @query="queryData">

      <template #extendFormItems>
        <el-form-item label="筛选条件" prop="condition">
          <el-input v-model="queryForm.condition" placeholder="请输入查询条件"/>
        </el-form-item>
      </template>

      <template #buttons>
        <el-button class="som-button-margin-right" type="primary" @click="addOrEdit(undefined)">新增</el-button>
        <el-button class="som-button-margin-right" type="primary" @click="refresh">刷新配置</el-button>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <el-table
          :data="ruleTableData"
          height="100%">
          <drg-table-column v-for="(item,idx) in ruleTableColumns" :key="idx" :prop="item.prop" :label="item.label" :width="item.width" :dicType="item.dicType"/>
          <el-table-column label="是否必填">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.required"
                         @change="updateActiveFlag(scope.row)"
                         active-color="#13ce66"
                         inactive-color="#ff4949"
                         active-value="1"
                         inactive-value="0">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="是否启用">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.activeFlag"
                         @change="updateActiveFlag(scope.row)"
                         active-color="#13ce66"
                         inactive-color="#ff4949"
                         active-value="1"
                         inactive-value="0">
              </el-switch>
            </template>
          </el-table-column>

          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button @click="addOrEdit(scope.row)" type="primary">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 新增/编辑 -->
        <el-dialog
          :title="isAdd ? '新增' : '修改'"
          :visible.sync="addOrEditVisible"
          width="30%">
          <el-form ref="addOrEditForm" :model="addOrEditForm" :rules="addOrEditFormRules">
            <el-form-item v-for="(item,idx) in ruleTableColumns" :key="idx" :label="item.label" :prop="item.prop">
              <el-input v-model="addOrEditForm[item.prop]" :placeholder="'请输入' + item.label" />
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="addOrEditVisible = false">取 消</el-button>
            <el-button type="primary" @click="addOrEditConfirm">{{ isAdd ? '新增' : '修改' }}</el-button>
          </span>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryData as queryRules, updateRule, addRule, refreshConfig } from '@/api/interfaceTest/interfaceTestRule'

export default {
  name: 'interfaceTestRules',
  data: () => ({
    queryForm: {
      condition: ''
    },
    addOrEditForm: {},
    tempAddOrEditForm: {},
    addOrEditFormRules: {},
    addOrEditVisible: false,
    isAdd: false,
    tableTotalNum: 0,
    ruleTableData: [],
    ruleTableColumns: [
      { label: '校验字段编码', prop: 'chkFldCodg' },
      { label: '校验字段名称', prop: 'chkFieldName' },
      { label: '校验字段类型', prop: 'chkFldType', dicType: 'FIELD_TYPE' },
      { label: '字典类型', prop: 'dicType' },
      { label: '正则校验', prop: 'reglChk' },
      { label: '所属接口', prop: 'blngIntf' }
    ]
  }),
  mounted () {
    const notValidFields = ['dictType', 'regEx']
    this.ruleTableColumns.forEach(column => {
      this.addOrEditForm[column.prop] = ''
      if (!notValidFields.includes(column.prop)) {
        this.addOrEditFormRules[column.prop] = [
          { required: true, message: '请输入' + column.label, trigger: 'blur' }
        ]
      }
    })
    this.tempAddOrEditForm = { ...this.addOrEditForm }
  },
  methods: {
    queryData () {
      queryRules(this.queryForm).then(res => {
        this.ruleTableData = res.data.list
        this.tableTotalNum = res.data.total
      })
    },
    // 更新启用标识
    updateActiveFlag (row) {
      this.update(row)
    },
    addOrEdit (row) {
      this.addOrEditVisible = true
      if (row !== undefined) {
        this.isAdd = false
        // 防止没有获取到属性值input无法输入
        this.addOrEditForm = { ...this.tempAddOrEditForm }
        Object.assign(this.addOrEditForm, row)
      } else {
        this.isAdd = true
        this.addOrEditForm = { ...this.tempAddOrEditForm }
        this.$refs.addOrEditForm.clearValidate()
      }
    },
    addOrEditConfirm () {
      this.$refs.addOrEditForm.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            addRule({ data: this.addOrEditForm }).then(res => {
              this.queryData()
              this.$message.success('新增成功')
              this.addOrEditVisible = false
            })
          } else {
            this.update(this.addOrEditForm)
            this.addOrEditVisible = false
          }
        }
      })
    },
    update (data) {
      updateRule({ data }).then(res => {
        this.queryData()
        this.$message.success('修改成功')
      })
    },
    refresh () {
      refreshConfig({}).then(res => {
        this.$message.success('刷新成功')
      })
    }
  }
}
</script>
