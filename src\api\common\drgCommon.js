import request from '@/utils/request'
// 查找科室下拉菜单树、以及所有码表值
export function querySelectTreeAndSelectList (params) {
  return request({
    url: '/drgCommon/querySelectTreeAndSelectList',
    method: 'post',
    params: params
  })
}

// 获取码表值
export function queryDictionary (params) {
  return request({
    url: '/drgCommon/queryDictionary',
    method: 'post',
    params: params
  })
}

// 获取清单码表值
export function querySettleListDictionary (params) {
  return request({
    url: '/drgCommon/querySettleListDictionary',
    method: 'post',
    params: params
  })
}

// 查询ICD模糊建议下拉
export function queryLikeIcdsByPram (data) {
  return request({
    url: '/drgCommon/queryLikeIcdsByPram',
    method: 'post',
    data: data
  })
}

// 查询DRGs组模糊建议下拉
export function queryLikeDrgsByPram (data) {
  return request({
    url: '/drgCommon/queryLikeDrgsByPram',
    method: 'post',
    data: data
  })
}

// 查询成都组模糊建议下拉
export function queryLikePpsGroupByPram (data) {
  return request({
    url: '/drgCommon/queryLikePpsGroupByPram',
    method: 'post',
    data: data
  })
}

// 查询DIP组模糊建议下拉
export function queryLikeDipGroupByPram (data) {
  return request({
    url: '/drgCommon/queryLikeDipGroupByPram',
    method: 'post',
    data: data
  })
}

// 查询各类型医生下拉数据（病案上填写医生）
export function queryMedicalDoctorSelectInput (data) {
  return request({
    url: '/drgCommon/queryMedicalDoctorSelectInput',
    method: 'post',
    data: data
  })
}

export function queryDrgDetailList (params) {
  return request({
    url: '/drgCommon/queryDrgDetailList',
    method: 'post',
    params: params
  })
}

export function queryMedicalDetailList (params) {
  return request({
    url: '/drgCommon/queryMedicalDetailList',
    method: 'post',
    params: params
  })
}

export function queryDeptDetailList (params) {
  return request({
    url: '/drgCommon/queryDeptDetailList',
    method: 'post',
    params: params
  })
}

export function queryPpsDeptDetailList (params) {
  return request({
    url: '/drgCommon/queryPpsDeptDetailList',
    method: 'post',
    params: params
  })
}

export function queryDipDeptDetailList (params) {
  return request({
    url: '/drgCommon/queryDipDeptDetailList',
    method: 'post',
    params: params
  })
}

export function queryMedicalDetailForBedDayCostList (params) {
  return request({
    url: '/drgCommon/queryMedicalDetailForBedDayCostList',
    method: 'post',
    params: params
  })
}

export function getCols (params) {
  return request({
    url: '/drgCommon/getCols',
    method: 'post',
    params: params
  })
}

export function queryDataIsuue (params) {
  return request({
    url: '/drgCommon/queryDataIsuue',
    method: 'post',
    params: params
  })
}

export function queryUserInfo (params) {
  return request({
    url: '/drgCommon/queryUserInfo',
    method: 'post',
    params: params
  })
}

export function queryDiagnosis (data) {
  return request({
    url: '/drgCommon/queryDiagnosis',
    method: 'post',
    data: data
  })
}

export function queryDIPGroup (params) {
  return request({
    url: '/drgCommon/queryDIPGroup',
    method: 'post',
    params: params
  })
}

export function queryHospitalId (params) {
  return request({
    url: '/drgCommon/queryHospitalId',
    method: 'post',
    params: params
  })
}

export function queryHospital (params) {
  return request({
    url: '/drgCommon/queryHospital',
    method: 'post',
    params: params
  })
}

/**
 * 获取系统配置
 * @param params
 * @returns {*}
 */
export function getSysConfigByKey (params) {
  return request({
    url: '/drgCommon/getSysConfigByKey',
    method: 'post',
    params: params
  })
}
