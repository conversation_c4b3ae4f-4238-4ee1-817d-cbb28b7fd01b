<template>
  <div style="height: 100%">
    <el-table
      :data="tableData"
      :summary-method="getSummaries"
      show-summary
      :header-cell-style="{'text-align':'center'}"
      height="100%"
      v-loading="tableLoading"
      :id="id"
      ref="elTable"
      border>
      <el-table-column label="序号" type="index" align="center" fixed="left"/>
      <el-table-column label="科室编码" prop="deptCode" width="100px" v-if="false" align="left"/>
      <el-table-column label="科室名称" prop="deptName" width="150px" :fixed="include('deptName')" v-if="queryType == 1" align="left"/>
      <el-table-column label="DIP编码" prop="dipCodg" width="100px" :fixed="include('dipCode')" v-if="grperType == 1" :key="1" show-overflow-tooltip align="center"/>
      <el-table-column label="DIP名称" prop="dipName" width="160px" :fixed="include('dipName')" v-if="grperType == 1" :key="2" show-overflow-tooltip align="left"/>
      <drg-table-column label="是否使用辅助目录" prop="isUsedAsstList" :fixed="include('auxiliaryCatalogue')" width="160px" dicType="AD" v-if="grperType == 1" :key="3" show-overflow-tooltip align="left"/>
      <el-table-column label="辅助目录-年龄段" prop="asstListAgeGrp" width="160px" :fixed="include('auxiliaryAge')" v-if="grperType == 1" :key="4" show-overflow-tooltip align="left"/>
      <el-table-column label="辅助目录-疾病严重程度" prop="asstListDiseSevDeg" :fixed="include('auxiliaryIllness')" width="160px" v-if="grperType == 1" :key="5" show-overflow-tooltip align="left"/>
      <el-table-column label="辅助目录-肿瘤严重程度" prop="asstListTmorSevDeg" :fixed="include('auxiliaryTumour')" width="160px" v-if="grperType == 1" :key="6" show-overflow-tooltip align="left"/>
      <el-table-column label="DRG编码" prop="drgCodg" width="100px" :fixed="include('drgCode')" v-if="grperType == 3" :key="7" show-overflow-tooltip align="center"/>
      <el-table-column label="DRG名称" prop="drgName" width="160px" :fixed="include('drgName')" v-if="grperType == 3" :key="8" show-overflow-tooltip align="left"/>
      <el-table-column label="成都编码" prop="cdCodg" width="100px" :fixed="include('cdCode')" v-if="grperType == 2" :key="9" show-overflow-tooltip align="left"/>
      <el-table-column label="成都名称" prop="cdName" width="160px" :fixed="include('cdName')" v-if="grperType == 2" :key="10" show-overflow-tooltip align="left"/>
      <el-table-column label="病案数" prop="medcasVal" width="60px" :fixed="include('medicalNum')" align="center">
        <template slot-scope="scope">
          <div v-if="Number(scope.row.medcasVal)>0" class='skip' @click="queryMedicalNum(scope.row)">
            {{scope.row.medcasVal | formatIsEmpty}}
          </div>
          <div v-if="Number(scope.row.medcasVal)==0" style="color:#000000">
            {{scope.row.medcasVal | formatIsEmpty}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="不存在标杆病案数" prop="nonBenchmarkNum" :fixed="include('nonBenchmarkNum')" width="125px" align="center"/>

      <el-table-column label="正常付费人数" prop="normalNum" width="100px"  :fixed="include('normalNum')" align="center">
        <template slot-scope="scope">
          <div v-if="Number(scope.row.normalNum)>0" class='skip' @click="queryNormalNum(scope.row)">
            {{scope.row.normalNum | formatIsEmpty}}
          </div>
          <div v-if="Number(scope.row.normalNum)==0" style="color:#000000">
            {{scope.row.normalNum | formatIsEmpty}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="超高病案数" prop="ultrahighNum"  :fixed="include('ultrahighNum')" width="130px" align="center">
        <template slot-scope="scope">
          <div v-if="Number(scope.row.ultrahighNum)>0" class='skip' @click="queryUltrahighNum(scope.row)">
            {{scope.row.ultrahighNum | formatIsEmpty}}
          </div>
          <div v-if="Number(scope.row.ultrahighNum)==0" style="color:#000000">
            {{scope.row.ultrahighNum | formatIsEmpty}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="超高率" prop="ultrahighRate" :fixed="include('ultrahighRate')" width="70px" align="center">
        <template slot-scope="scope">
          {{ formatRate(scope.row.ultrahighRate) }}
        </template>
      </el-table-column>
      <el-table-column label="超低病案数" prop="ultraLowNum" width="130px" :fixed="include('ultraLowNum')" align="center">
        <template slot-scope="scope">
          <div v-if="Number(scope.row.ultraLowNum)>0" class='skip' @click="queryUltraLowNum(scope.row)">
            {{scope.row.ultraLowNum | formatIsEmpty}}
          </div>
          <div v-if="Number(scope.row.ultraLowNum)==0" style="color:#000000">
            {{scope.row.ultraLowNum | formatIsEmpty}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="超低率" prop="ultraLowRate" :fixed="include('ultraLowRate')" width="70px" align="center">
        <template slot-scope="scope">
          {{ formatRate(scope.row.ultraLowRate) }}
        </template>
      </el-table-column>
      <el-table-column label="例均费用" prop="avgInHosTotalCost" :fixed="include('avgInHosTotalCost')" align="right"/>
      <el-table-column label="例均费用(标杆)" prop="standardFee" width="130" :fixed="include('standardCost')?'right':false" align="right" />
      <el-table-column label="例均住院天数" prop="avgInHosDays" width="130" :fixed="include('avgInHosDays')" align="center"/>
      <el-table-column label="例均住院天数(标杆)" prop="standardDays" :fixed="include('standardDays')" width="135" align="center"/>
      <el-table-column label="药品费" prop="drugfee" align="right" :fixed="include('medicalCost')"></el-table-column>
      <el-table-column label="药占比" prop="medicalCostRate" width="110px" :fixed="include('medicalCostRate')" align="center" />
      <el-table-column label="药占比(标杆)" prop="standardDrugRatio" width="70px" :fixed="include('standardDrugRatio')" align="center" v-if="grperType == 1">
        <template slot-scope="scope">
          <div>{{ $somms.addPercent(scope.row.standardDrugRatio) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="耗材费" prop="mcsFee" align="right" :fixed="include('materialCost')" ></el-table-column>
      <el-table-column label="耗占比" prop="materialCostRate" width="110px" :fixed="include('materialCostRate')" align="center" />
      <el-table-column label="耗占比(标杆)" prop="standardConsumeRatio" width="70px" :fixed="include('standardConsumeRatio')" align="center" v-if="grperType == 1">
        <template slot-scope="scope">
          <div>{{ $somms.addPercent(scope.row.standardConsumeRatio) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="总费用" prop="sumfee" align="right" :fixed="include('totalCost')"/>
      <el-table-column label="预测金额" prop="forecastAmount" align="right" :fixed="include('forecastAmount')"/>
      <el-table-column label="院前检查费" prop="preHosExamineCost" width="100px" align="right" :fixed="include('preHosExamineCost')" v-if="grperType == 1"/>
      <el-table-column label="预测金额差异" prop="forecastAmountDiff" width="100px" align="right" fixed="right">
        <template slot-scope="scope">
          <span :class="[parseFloat(scope.row.forecastAmountDiff) < 0 ? 'som-color-error' : 'som-color-success']">
            {{ scope.row.forecastAmountDiff }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="费用状态" width="100px" align="center" fixed="right">
        <template slot-scope="scope">
          <i class="som-icon-error-waring som-icon-big" v-if="parseFloat(scope.row.forecastAmountDiff) < 0"></i>
          <i class="som-icon-success som-icon-big" v-else></i>
        </template>
      </el-table-column>
      <el-table-column label="详情" align="center" fixed="right" width="60px">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-search" circle @click="queryDetails(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { formatRate } from '@/utils/common'
export default {
  name: 'queryDiseaseDataTable',
  props: {
    tableData: {
      type: Array
    },
    tableLoading: {
      type: Boolean,
      default: false
    },
    grperType: {
      grperType: Number
    },
    queryType: {
      queryType: Number
    },
    queryForm: {
      type: Object
    },
    id: {
      type: String
    },
    columnOptions: {
      type: Array,
      default: () => []
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    }
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '0'
      }
    }
  },
  updated () {
    this.refreshTable()
    this.$nextTick(() => {
      this.$refs['elTable'].doLayout()
    })
  },
  methods: {
    formatRate,
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      const calculations = {
        sum: (values) => values.reduce((prev, curr) => prev + curr, 0),
        average: (values) => values.reduce((prev, curr) => prev + curr, 0) / values.length,
        customAverage: (values) => {
          const sum = values.reduce((prev, curr) => prev + curr, 0)
          return sum / values.length
        }
      }
      let sum15 = 0, sum17 = 0, sum19 = 0
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计'
          return
        }
        const values = data.map(item => Number(item[column.property])).filter(value => !isNaN(value))
        if (index === 4 || index === 5 || index === 6 || index === 7 || index === 9) {
          sums[index] = calculations.sum(values)
        } else if (index === 15 || index === 17 || index === 19 || index === 20 || index === 21) {
          sums[index] = calculations.sum(values).toFixed(2)
          if (index === 15) sum15 = sums[index]
          else if (index === 17) sum17 = sums[index]
          else if (index === 19) sum19 = sums[index]
        } else if (index === 11 || index === 12 || index === 13 || index === 14) {
          sums[index] = calculations.average(values).toFixed(2)
        } else if (index === 8 || index === 10) {
          sums[index] = calculations.average(values).toFixed(2) + '%'
        } else {
          sums[index] = ' '
        }
      })
      sums[16] = calculatePercentage(sum15, sum19)
      sums[18] = calculatePercentage(sum17, sum19)
      return sums

      function calculatePercentage (numerator, denominator) {
        if (denominator !== 0) {
          return ((numerator / denominator) * 100).toFixed(2) + '%'
        }
        return '0.00%'
      }
    },
    refreshTable () {
      this.$nextTick(() => {
        this.$refs.elTable.doLayout()
      })
    },
    include (column) {
      return this.fixedColumns.includes(column)
    },
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    queryDetails (row) {
      this.goto('/oprelDecimmak/diseExtrDetail', {
        begnDate: this.queryForm.begnDate,
        expiDate: this.queryForm.expiDate,
        inStartTime: this.queryForm.inStartTime,
        inEndTime: this.queryForm.inEndTime,
        seStartTime: this.queryForm.seStartTime,
        seEndTime: this.queryForm.seEndTime,
        dateRange: this.queryForm.dateRange,
        dipCodg: row.dipCodg,
        drgCodg: row.drgCodg,
        cdCodg: row.cdCodg,
        group: this.grperType,
        deptCode: row.deptCode
      })
    },
    queryMedicalNum (row) {
      this.goto('/oprelDecimmak/pattExtrAnalysis', {
        begnDate: this.queryForm.begnDate,
        expiDate: this.queryForm.expiDate,
        inStartTime: this.queryForm.inStartTime,
        inEndTime: this.queryForm.inEndTime,
        seStartTime: this.queryForm.seStartTime,
        seEndTime: this.queryForm.seEndTime,
        dateRange: this.queryForm.dateRange,
        group: this.grperType,
        dipCodg: row.dipCodg,
        drgCodg: row.drgCodg,
        cdCodg: row.cdCodg,
        deptCode: this.queryForm.deptCode
      })
    },
    queryNormalNum (row) {
      this.goto('/oprelDecimmak/pattExtrAnalysis', {
        begnDate: this.queryForm.begnDate,
        expiDate: this.queryForm.expiDate,
        inStartTime: this.queryForm.inStartTime,
        inEndTime: this.queryForm.inEndTime,
        seStartTime: this.queryForm.seStartTime,
        seEndTime: this.queryForm.seEndTime,
        dateRange: this.queryForm.dateRange,
        group: this.grperType,
        dipCodg: row.dipCodg,
        drgCodg: row.drgCodg,
        cdCodg: row.cdCodg,
        deptCode: this.queryForm.deptCode,
        costSection: 3
      })
    },
    queryUltrahighNum (row) {
      this.goto('/oprelDecimmak/pattExtrAnalysis', {
        begnDate: this.queryForm.begnDate,
        expiDate: this.queryForm.expiDate,
        inStartTime: this.queryForm.inStartTime,
        inEndTime: this.queryForm.inEndTime,
        seStartTime: this.queryForm.seStartTime,
        seEndTime: this.queryForm.seEndTime,
        dateRange: this.queryForm.dateRange,
        group: this.grperType,
        dipCodg: row.dipCodg,
        drgCodg: row.drgCodg,
        cdCodg: row.cdCodg,
        deptCode: this.queryForm.deptCode,
        costSection: 1
      })
    },
    queryUltraLowNum (row) {
      this.goto('/oprelDecimmak/pattExtrAnalysis', {
        begnDate: this.queryForm.begnDate,
        expiDate: this.queryForm.expiDate,
        inStartTime: this.queryForm.inStartTime,
        inEndTime: this.queryForm.inEndTime,
        seStartTime: this.queryForm.seStartTime,
        seEndTime: this.queryForm.seEndTime,
        dateRange: this.queryForm.dateRange,
        group: this.grperType,
        dipCodg: row.dipCodg,
        drgCodg: row.drgCodg,
        cdCodg: row.cdCodg,
        deptCode: this.queryForm.deptCode,
        costSection: 2
      })
    }
  }
}
</script>
<style scoped>
.el-table .cell {
  line-height: unset;
}
</style>
