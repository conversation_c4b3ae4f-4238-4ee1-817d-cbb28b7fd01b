<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             headerTitle="查询条件"
             contentTitle="对比分析"
             show-pagination
             :totalNum="total"
             :extendFormIndex="[1]"
             ref="somForm"
             @query="queryData" @reset="handleResetSearch">
      <template slot="extendFormItems">
        <el-form-item label="年份">
          <el-date-picker
            v-model="queryForm.standardYear"
            align="left"
            style="width: 100%"
            type="year"
            value-format="yyyy"
            placeholder="选择年份">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="DIP组" class="som-el-form-item-margin-left">
          <el-input
            placeholder="请输入内容"
            v-model="queryForm.inputData"
            @input="queryData"
            clearable>
          </el-input>
<!--          <el-select v-model="queryForm.dipCodes"-->
<!--                     remote-->
<!--                     multiple-->
<!--                     filterable-->
<!--                     collapse-tags-->
<!--                     :clearable="true"-->
<!--                     :remote-method="getDIPGroupList"-->
<!--                     placeholder="请输入DIP编码或者名称">-->
<!--&lt;!&ndash;            <el-option v-for="item in itemList"&ndash;&gt;-->
<!--&lt;!&ndash;                       :key="item.value"&ndash;&gt;-->
<!--&lt;!&ndash;                       :label="item.label"&ndash;&gt;-->
<!--&lt;!&ndash;                       :value="item.value">&ndash;&gt;-->
<!--&lt;!&ndash;                <div style="font-size: 12px;color: #000000;text-overflow: ellipsis;overflow: hidden;width: 30%">{{ item.value }}</div>&ndash;&gt;-->
<!--&lt;!&ndash;                <span style="font-size: 10px;color: #9b9b9b;text-overflow: ellipsis;overflow: hidden;width: 70%">{{ item.label }}</span>&ndash;&gt;-->
<!--&lt;!&ndash;            </el-option>&ndash;&gt;-->
<!--          </el-select>-->
        </el-form-item>
      </template>
      <template slot="buttons">
        <el-popconfirm
          confirm-button-text='确定'
          cancel-button-text='导出全部'
          icon="el-icon-info"
          icon-color="red"
          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">
          <el-button slot="reference" type="success">导出Excel</el-button>
        </el-popconfirm>
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <div class="som-table-height">
          <el-row :gutter="12" class="som-h-one-hundred">
            <el-col :span="12" class="som-h-one-hundred">
              <el-card shadow="always" class="som-h-one-hundred">
                <custom-dip-contrast-table
                            :table-data="leftTableData"
                            :table-loading="leftTableLoading"
                            :id="leftTable"
                            :name="leftDataTable"
                            ref="leftRef"/>
              </el-card>
            </el-col>
            <el-col :span="12" class="som-h-one-hundred">
              <el-card shadow="always" class="som-h-one-hundred">
                <custom-dip-contrast-table
                            :table-data="rightTableData"
                            :table-loading="rightTableLoading"
                            :id="rightTable"
                            :name="rightDataTable"
                            ref="rightRef"/>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue, queryDIPGroup } from '@/api/common/drgCommon'
import customDipContrastTable from './comps/customDipContrastTable'
import { queryData, queryDatas as queryPageData } from '@/api/custom/custom'

export default {
  name: 'dipContrast',
  inject: ['reload'],
  components: {
    'custom-dip-contrast-table': customDipContrastTable
  },
  data: () => ({
    queryForm: {
      standardYear: '',
      inputData: ''
    },
    total: 0,
    leftTotal: 0,
    rightTotal: 0,
    leftTableData: [],
    leftTableLoading: false,
    rightTableData: [],
    rightTableLoading: false,
    leftDataTable: 'leftDataTable',
    rightDataTable: 'rightDataTable',
    leftTable: 'leftTable',
    rightTable: 'rightTable',
    itemList: []
  }),
  mounted () {
    this.initDate()
  },
  methods: {
    queryPageData,
    initDate () {
      queryDataIsuue().then(response => {
        if (response.data.cy_start_date) {
          this.queryForm.standardYear = response.data.cy_start_date.toString().substring(0, 4)
        } else {
          this.queryForm.standardYear = this.$somms.getDate('yyyy', 0, 0, 0)
        }
        this.queryData()
      })
    },
    queryData () {
      this.leftTableLoading = true
      this.rightTableLoading = true
      // queryData(this.getParams("1")).then(res => {
      //   if(res.code == 200){
      //     this.leftTableLoading = false
      //     this.leftTableData = res.data.list
      //   }
      // })
      //
      // queryData(this.getParams("2")).then(res => {
      //   if(res.code == 200){
      //     this.rightTableLoading = false
      //     this.rightTableData = res.data.list
      //   }
      // })
      setTimeout(() => {
        queryPageData(this.getParams('1', true)).then(result => {
          this.leftTableData = result.data.list
          this.total = result.data.total
          this.leftTotal = result.data.total
          this.leftTableLoading = false
        })
        queryPageData(this.getParams('2', true)).then(result => {
          this.rightTableData = result.data.list
          this.rightTotal = result.data.total
          this.rightTableLoading = false
        })
      }, 1000)
    },
    getParams (queryType, inputFlag = false) {
      let params = {}
      Object.assign(params, this.queryForm)
      params.queryType = queryType
      if (inputFlag) {
        params.pageNum = 1
      }
      return params
    },
    handleResetSearch () {
      this.reload()
    },
    // 导出全部
    allExcel () {
      // this.$refs.ref.allExcel()
      this.$somms.exportExcelAll(this.getParams('1'), this.leftTotal, this.$refs.leftRef.$children[0].$children, document.getElementById(this.leftTable).children[1].children[0].children[1].children[0].childNodes, queryPageData, '左列表')
      this.$somms.exportExcelAll(this.getParams('2'), this.rightTotal, this.$refs.rightRef.$children[0].$children, document.getElementById(this.rightTable).children[1].children[0].children[1].children[0].childNodes, queryPageData, '右列表')
    },
    exportExcel () {
      let leftFileName = '左数据'
      let rightFileName = '右数据'
      this.somExportExcel(this.leftTable, leftFileName, 0)
      this.somExportExcel(this.rightTable, rightFileName, 1)
    }
  }
}
</script>

<style scoped>
.el-card__body {
  height: 100% !important;
  padding: 10px 15px 0px 10px !important;
}
.el-select-dropdown__item {
  height: 100%;
}
/deep/.el-select-dropdown__item {
  width: 193px !important;
}
</style>
