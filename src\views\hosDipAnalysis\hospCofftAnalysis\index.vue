<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             headerTitle="查询条件"
             contentTitle="医院系数"
             @query="queryData">
      <template slot="extendFormItems">
        <el-form-item label="年份">
          <el-date-picker
            v-model="queryForm.year"
            align="left"
            style="width: 100%"
            type="year"
            :clearable="false"
            value-format="yyyy"
            placeholder="选择年份">
          </el-date-picker>
        </el-form-item>
      </template>
      <template slot="containerContent">
        <el-table border
                  :data="tableData"
                  v-loading="loading"
                  :header-cell-style="{'text-align':'center'}">
          <el-table-column label="CMI" align="right">
            <template slot-scope="scope">{{scope.row.cmi}}</template>
          </el-table-column>
          <el-table-column label="时间消耗指数" align="right">
            <template slot-scope="scope">{{scope.row.timeIndex}}</template>
          </el-table-column>
          <el-table-column label="费用消耗指数" align="right">
            <template slot-scope="scope">{{scope.row.costIndex}}</template>
          </el-table-column>
        </el-table>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { queryDataIsuue } from '@/api/common/drgCommon'
import { queryHospitalCoefficient } from '@/api/dipBusiness/dipHospitalCoefficient'

export default {
  name: 'hospCofftAnalysis',
  data: () => ({
    queryForm: {
      year: ''
    },
    tableData: null,
    loading: false
  }),
  mounted () {
    this.queryYear()
  },
  methods: {
    queryYear () {
      queryDataIsuue().then(response => {
        if (response.data.cy_start_date) {
          this.queryForm.year = response.data.cy_start_date.toString().substring(0, 4)
        } else {
          this.queryForm.year = this.$somms.getDate('yyyy', 0, 0, 0)
        }
        this.queryData()
      })
    },
    queryData () {
      this.loading = true
      queryHospitalCoefficient(this.getParams()).then(res => {
        if (res.code == 200) {
          this.tableData = res.data
          this.loading = false
        }
      })
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      return params
    }
  }
}
</script>
