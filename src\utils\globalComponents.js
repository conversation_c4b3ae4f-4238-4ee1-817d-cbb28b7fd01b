import Vue from 'vue'
import TitleLine from '@/components/drgComponents/TitleLine/index.vue'
import Tendency from '@/components/drgComponents/Tendency/index.vue'
import Container from '@/components/drgComponents/Container/index.vue'
import Department from '@/components/drgComponents/Department/index.vue'
import DictSelect from '@/components/drgComponents/DictSelect/index.vue'
import Table from '@/components/drgComponents/Table/index.vue'
import TableColumn from '@/components/drgComponents/TableColumn/index.vue'
import Echarts from '@/components/drgComponents/Echarts/index.vue'
import Loading from '@/components/drgComponents/Loading/index.vue'
import Group from '@/components/drgComponents/Group/index.vue'
import Form from '@/components/drgComponents/Form/index.vue'

/**
 * 全局组件
 */

/**
 * 每个小模块标题
 * @type {{install: globalComponents.install}}
 */
const globalComponents = {
  install: function (Vue) {
    Vue.component('drg-title-line', TitleLine) // 标题
    Vue.component('drg-tendency', Tendency) // 上升或下降
    Vue.component('drg-container', Container) // 容器
    Vue.component('drg-department', Department) // 科室
    Vue.component('drg-dict-select', DictSelect) // 码表下拉选
    Vue.component('drg-table', Table) // 表格
    Vue.component('drg-table-column', TableColumn) // 表格列
    Vue.component('drg-echarts', Echarts) // 表格列
    Vue.component('drg-loading', Loading) // 加载
    Vue.component('drg-group', Group) // 组
    Vue.component('drg-form', Form) // 表单
  }
}

// render 组件
Vue.component('form-extend', {
  render: function (createElement) {
    return createElement(
      'div', // tag name 标签名称
      this.dom // 子组件中的阵列
    )
  },
  props: {
    dom: Array
  }
})

export default globalComponents
