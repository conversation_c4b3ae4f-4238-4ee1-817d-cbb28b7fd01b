import request from '@/utils/request'
export function getDataList (params) {
  return request({
    url: '/dipInGroupAnalysis/mainInfoList',
    method: 'post',
    params: params
  })
}

export function getTopCountInfo (params) {
  return request({
    url: '/dipInGroupAnalysis/getTopCountInfo',
    method: 'post',
    params: params
  })
}

export function getNoGroupResonCountInfo (params) {
  return request({
    url: '/dipInGroupAnalysis/getNoGroupResonCountInfo',
    method: 'post',
    params: params
  })
}
