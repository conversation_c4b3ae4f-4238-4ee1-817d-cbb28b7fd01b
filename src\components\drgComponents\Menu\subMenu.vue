<template>
  <div class="sub-menu-item" @mouseleave="lessenMouseleave">
    <div v-show="data.active" style="background: rgba(34,74,190,1);height: 100%;width: calc(100% + 1.6rem);position: absolute;z-index: -1;left: -1.6rem"></div>
    <div v-if="lessen">
      <div :class="['sub-menu-item-title', data.active ? 'active' : '', borderBottom ? 'sub-menu-item-title-border' : '']"
           @mouseover="mouseover = true"
           @mouseleave="mouseover = false"
           @click="show = !show">
        <div :class="[mouseover ? 'sub-menu-item-title-content' : '']">
          <slot name="profttl"/>
        </div>
        <div class="sub-menu-item-arrow-right">
          <i :class="['el-icon-arrow-right', show ? 'sub-menu-item-arrow-right-active' : '']"></i>
        </div>
      </div>
      <collapse-transition>
        <div class="active" v-show="show">
          <div>
            <slot></slot>
          </div>
        </div>
      </collapse-transition>
    </div>

    <!-- 缩放 -->
    <div v-else>
      <div :class="['sub-menu-item-title', 'sub-menu-item-title-lessen' ,show ? 'active' : '']"
           @mouseover="lessenMouseover">
        <slot name="profttl"/>
      </div>
      <div class="child-menu" ref="rightSide" @mouseover="show = true" :style="{ top: lessenStyle.y, left: lessenStyle.x }">
          <div class="active" v-show="show">
            <slot></slot>
          </div>
      </div>
    </div>

<!--    <div v-show="data.active" style="height: 100%;width: 0.3rem;-->
<!--    border-radius: 0.2rem;-->
<!--    background: rgba(176, 215, 248,.2);position: absolute;top: 0;left: 0"></div>-->
  </div>
</template>
<script>
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'
export default {
  components: {
    CollapseTransition
  },
  props: {
    data: {
      type: Object
    },
    borderBottom: {
      type: Boolean,
      default: false
    }
  },
  name: 'jpSubMenu',
  data: () => ({
    show: false,
    mouseover: false,
    lessenStyle: {
      x: '',
      y: ''
    }
  }),
  computed: {
    lessen () {
      return this.$store.state.app.sidebar.opened
    }
  },
  methods: {
    lessenMouseover (e) {
      this.show = true
      this.$nextTick(() => {
        let rectTop = e.currentTarget.getBoundingClientRect().top
        let bottomDis = document.documentElement.clientHeight - rectTop
        let hideElHeight = this.$refs.rightSide.offsetHeight
        let sub = 0
        // 元素高度大于距离底部高度
        if (hideElHeight > bottomDis) {
          sub = hideElHeight - bottomDis
        }
        this.lessenStyle.x = '120px'
        this.lessenStyle.y = e.currentTarget.getBoundingClientRect().top - sub + 'px'
      })
    },
    lessenMouseleave () {
      if (!this.lessen) {
        this.show = false
        this.lessenStyle.x = 0
        this.lessenStyle.y = 0
      }
    }
  }
}
</script>
<style lang="scss" scoped>
$activeColor: white;
.sub-menu-item{
  width: 100%;
  font-size: 15px;
  padding: 0 .8rem;
  //margin: 0.5rem 0;
  position: relative;

  &-title:hover{
    color: $activeColor;
  }

  &-title{
    position: relative;
    border-top: 1px solid rgba(255,255,255,.15);
    display: flex;
    align-items: center;
    height: 50px;
    color: rgba(255,255,255,.7);
    cursor: pointer;

    &-border{
      border-bottom: 1px solid rgba(255,255,255,.15);
    }

    &-content{
      transform: scale(1.1);
      //transform: translateX(10%);
      //transition: all .3s linear;
      transition: all .2s linear;
    }

    &-lessen {
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
    }
  }

  &-arrow-right{
    position: absolute;
    right: 0;
    &-active {
      transform: rotate(90deg);
    }
  }
}
.active{
  color: $activeColor;
}
.child-menu{
  position: fixed;
  z-index: 9999999;
  background: #224abe;
  box-shadow: 0 .15rem 1.75rem #224abe;
  border-radius: 0 0.3rem 0.3rem 0;
  padding-left: .5rem;
  width: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none; /* firefox */
  -ms-overflow-style: none; /* IE 10+ */
  max-height: 600px;
}
.child-menu::-webkit-scrollbar{
  display: none;
}

</style>
