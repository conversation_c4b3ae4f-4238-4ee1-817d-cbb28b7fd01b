import request from '@/utils/request'

/**
 * 查询需要核对信息
 * @param params
 * @returns {*}
 */
export function queryVerifyInfo (params) {
  return request({
    url: '/settleListVerifyController/queryVerifyInfo',
    method: 'post',
    data: params
  })
}

/**
 * 修改标识状态
 * @param params
 * @returns {*}
 */
export function modifyMarkState (params) {
  return request({
    url: '/settleListVerifyController/modifyMarkState',
    method: 'post',
    data: params
  })
}

/**
 * 上传结算数据
 * @param params
 * @returns {*}
 */
export function uploadSettleData (params) {
  return request({
    url: '/settleListVerifyController/uploadSettleData',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}
