<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-issue
             :container="true"
             headerTitle="查询条件"
             contentTitle="测试"
             @query="queryData">
      <!-- 内容 -->
      <template slot="containerContent">
        <h1>测试</h1>
      </template>
    </drg-form>
  </div>
</template>
<script>
export default {
  name: 'testUpload',
  data: () => ({
    queryForm: {

    }
  }),
  methods: {
    queryData () {

    }
  }
}
</script>
