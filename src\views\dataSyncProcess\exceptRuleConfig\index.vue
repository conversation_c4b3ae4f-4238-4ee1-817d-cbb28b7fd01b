<template>
  <div class="app-container">
    <drg-container :headerPercent="10">
        <template slot="header">
       <!--     <el-card class="filter-container" style="overflow-y:auto;"> -->
            <!--   <i class="el-icon-search"></i> -->
              <drg-title-line title="查询条件" />
               <div>
                 <el-form :inline="true" :model="listQuery" size="mini">
                 <!--  <el-row :gutter="0">
                     <el-col :span="6"> -->
                       <el-form-item label="错误类型">
                         <el-select v-model="listQuery.errType" placeholder="请选择错误类型"  clearable>
                           <el-option
                             v-for="item in dictVoList.ERRORTYPE"
                             :key="item.value"
                             :label="item.label"
                             :value="item.value">
                           </el-option>
                         </el-select>
                       </el-form-item>
                  <!--   </el-col> -->
                     <el-form-item class="som-el-form-item-margin-left">
                       <div>
                         <el-button
                           @click="handleSearchList()"
                           type="primary"
                           size="mini">
                           查询结果
                         </el-button>
                           <el-button @click="refresh">重置</el-button>
                       </div>
                      </el-form-item>
<!--                   </el-row>-->
                 </el-form>
               </div>
        <!--     </el-card> -->
        </template>
    <template slot="content">
      <drg-title-line title="医保排除规则" />
      <div class="table-container" style="height: 90%;">
        <el-table ref="medicalIngroupConfigTable"
                :data="list" size="mini"
                :header-cell-style="{'text-align':'center'}"
                stripe
                style="width: 100%"
                v-loading="listLoading"
                border>
          <el-table-column fixed
                           label="序号"
                           type="index"
                           align="right"
                           width="50">
          </el-table-column>
          <el-table-column v-if="false">
            <template slot-scope="scope">{{scope.row.id}}</template>
          </el-table-column>
          <el-table-column label="启用状态"  align="left" width="100">
            <template slot-scope="scope">
              <div class='skip' @click="startOrStop(scope.row)">
              {{scope.row.enabFlag | formatStartFlag}}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="验证字段"  align="left" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.verfFld}}</template>
          </el-table-column>
          <el-table-column label="验证规则"  align="left" :show-overflow-tooltip="true">
            <template slot-scope="scope">{{scope.row.verfRule}}</template>
          </el-table-column>
          <el-table-column label="错误编码"  align="left" width="140">
            <template slot-scope="scope">{{scope.row.errorCode | formatIsEmpty}}</template>
          </el-table-column>
          <el-table-column label="错误类型"  align="left">
            <template slot-scope="scope">{{scope.row.errType | formatIsEmpty}}</template>
          </el-table-column>
        </el-table>
      </div>
    </template>
        </drg-container>
      </div>
    </template>
<script>
import { querySelectTreeAndSelectList } from '@/api/common/drgCommon'
import { fetchList, updateState } from '@/api/dataHandle/medicalIngroupConfig'

const defaultListQuery = {
  errType: null,
  tableHeight: 0
}
export default {
  name: 'exceptRuleConfig',
  inject: ['reload'],
  data () {
    return {
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      list: null,
      listQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      dataForm: {
        id: null,
        enabFlag: null
      }
    }
  },
  created () {
    this.findSelectTreeAndSelectList()
    this.getList()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatStartFlag (value) {
      if (value) {
        if (value == '1') {
          return '已启用'
        } else {
          return '停用'
        }
      } else {
        return '-'
      }
    }
  },
  refresh () {
    this.reload()
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.medicalIngroupConfigTable.$el.offsetTop：表格距离浏览器的高度
      // 50表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.medicalIngroupConfigTable.$el.offsetTop
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.medicalIngroupConfigTable.$el.offsetTop
      }
    })
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'ERRORTYPE')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getList () {
      this.listLoading = true
      let params = new URLSearchParams()
      if (this.listQuery.errType) {
        params.append('errorType', this.listQuery.errType)
      } else {
        params.append('errorType', '-1')
      }
      fetchList(params).then(response => {
        this.listLoading = false
        this.list = response.data
      })
    },
    startOrStop (row) {
      if (row.enabFlag == '0') {
        this.$confirm('确认启用当前规则吗？', '提示', {}).then(() => {
          this.listLoading = true
          this.dataForm = {
            id: row.id,
            enabFlag: '1'
          }
          let params = Object.assign({}, this.dataForm)
          updateState(params).then(res => {
            this.editLoading = false
            if (res.code == 200) {
              this.$message({ message: '操作成功', type: 'success' })
            } else {
              this.$message({
                message: '操作失败, ' + res.msg,
                type: 'error'
              })
            }
            this.getList()
          })
        })
      } else if (row.enabFlag == '1') {
        this.$confirm('确认停用当前规则吗？', '提示', {}).then(() => {
          this.listLoading = true
          this.dataForm = {
            id: row.id,
            enabFlag: '0'
          }
          let params = Object.assign({}, this.dataForm)
          updateState(params).then(res => {
            this.editLoading = false
            if (res.code == 200) {
              this.$message({ message: '操作成功', type: 'success' })
            } else {
              this.$message({
                message: '操作失败, ' + res.msg,
                type: 'error'
              })
            }
            this.getList()
          })
        })
      }
    },
    handleSearchList () {
      this.getList()
    },
    handleResetSearch () {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.getList()
    },
    refresh () {
      this.reload()
    }
  }
}
</script>
<style></style>
