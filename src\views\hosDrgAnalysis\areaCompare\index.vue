<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
             show-drg
             headerTitle="查询条件"
             contentTitle="DRGs库"
             :container="true"
             :extendFormIndex="[1]"
             @query="handleSearchList" @reset="handleResetSearch">

      <template slot="extendFormItems">
        <el-form-item label="年份">
          <el-date-picker
            v-model="listQuery.year"
            align="right"
            type="year"
            value-format="yyyy"
            placeholder="选择年份"
            @change="handleSearchList">
          </el-date-picker>
        </el-form-item>
      </template>

<!--      <template slot="buttons">-->
<!--        <el-popconfirm-->
<!--          confirm-button-text='确定'-->
<!--          cancel-button-text='导出全部'-->
<!--          icon="el-icon-info"-->
<!--          icon-color="red"-->
<!--          title="是否导出当前页面？" @confirm="exportExcel" @cancel="allExcel" style="margin-right: 15px">-->
<!--          <el-button slot="reference" type="success">导出Excel</el-button>-->
<!--        </el-popconfirm>-->
<!--        <el-button  @click="showTable()" size="mini">全部展开</el-button>-->
<!--        <el-button  @click="hiddenTable()" size="mini">全部折叠</el-button>-->
<!--      </template>-->

      <template slot="buttons">
        <el-button type="success" @click="exportExcel" style="margin-right: 15px">导出Excel</el-button>
      </template>

      <template slot="containerContent">
        <div style="height:25%;">
          <el-row :gutter="2" style="height: 100%">
            <el-col :span="6" style="height: 100%">
              <div id="mdcCoverRageChart" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
            <el-col :span="6" style="height: 100%">
              <div id="drgsCoverRageChart" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
            <el-col :span="6" style="height: 100%">
              <div id="medicalAndSurgeryCoverRageChart" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
            <el-col :span="6" style="height: 100%">
              <div id="complicationCoverRageChart" class="el-card is-always-shadow" style="height: 100%;width: 100%"></div>
            </el-col>
          </el-row>
        </div>
        <div class="table-container" style="flex:5px; height:80%;">
          <el-table ref="drgsKnowledgeBaseTable"
                    id="drgskbTable"

                    size="mini"
                    :header-cell-style = "{'text-align' : 'center'}"
                    height="100%"
                    :data="list"
                    style="width: 100%;"
                    v-loading="listLoading"
                    :row-style="hasGroupStyle"
                    :cell-style="drgsStyle"
                    row-key="id"
                    border
                    default-expand-all
                    :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
            <el-table-column label="编码" prop="drgsCode" align="left" width="220">
              <template slot-scope="scope">{{scope.row.drgsCode | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="名称" prop="drgsName" align="left"  :show-overflow-tooltip="true">
              <template slot-scope="scope">{{scope.row.drgsName | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="覆盖人次" prop="medcasVal" align="right" width="100">
              <template slot-scope="scope">{{scope.row.medcasVal | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="标杆住院费用" prop="avgCost" align="right" width="120">
              <template slot-scope="scope">{{scope.row.avgCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="标杆住院日" prop="avgDays" align="right" width="100">
              <template slot-scope="scope">{{scope.row.avgDays | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="标杆药品费用" prop="avgDrugFee" align="right" width="120">
              <template slot-scope="scope">{{scope.row.avgDrugFee | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="标杆耗材费用" prop="avgMaterailCost" align="right" width="120">
              <template slot-scope="scope">{{scope.row.avgMaterailCost | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="标杆抗生素费用" prop="avgAbtFee" align="right" width="120">
              <template slot-scope="scope">{{scope.row.avgAbtFee | formatIsEmpty}}</template>
            </el-table-column>
            <el-table-column label="标杆检验费" prop="inspectFeeStandardVal" align="right" width="110">
              <template slot-scope="scope">{{scope.row.inspectFeeStandardVal | formatIsEmpty}}</template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </drg-form>
  </div>
</template>
<script>
import { querySelectTreeAndSelectList, queryDataIsuue, queryLikeDrgsByPram } from '@/api/common/drgCommon'
import { fetchList as queryPageData, getCountByCoverRate } from '@/api/hospitalAnalysis/drgsKnowledgeBase'
import { elExportExcel } from '@/utils/exportExcel'
import { getDefultYear } from '@/utils/date'
import echarts from 'echarts'

const defaultListQuery = {
  year: null,
  queryDrg: null
}
export default {
  name: 'areaCompare',
  components: { },
  data () {
    return {
      depts: [], // 科室
      dictVoList: {}, // 码表
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      listLoading: true,
      list: [],
      year: null,
      queryDrg: null,
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0
    }
  },
  created () {
    // this.listQuery.year=getDefultYear();
    this.getDataIsuue()
  },
  filters: {
    formatIsEmpty (value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    }
  },
  // 动态调整表格高度
  mounted: function () {
    this.$nextTick(function () {
      // this.$refs.drgsKnowledgeBaseTable.$el.offsetTop：表格距离浏览器的高度
      // 35表示你想要调整的表格距离底部的高度（你可以自己随意调整），因为我们一般都有放分页组件的，所以需要给它留一个高度
      this.tableHeight = window.innerHeight - this.$refs.drgsKnowledgeBaseTable.$el.offsetTop - 35
      // 监听窗口大小变化
      let self = this
      window.onresize = function () {
        self.tableHeight = window.innerHeight - self.$refs.drgsKnowledgeBaseTable.$el.offsetTop - 35
      }
    })
  },
  methods: {
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', '')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue () {
      queryDataIsuue().then(response => {
        this.listLoading = false
        if (response.data.cy_start_date) {
          this.listQuery.year = response.data.cy_start_date.toString().substring(0, 4)
        } else {
          this.listQuery.year = getDefultYear()
        };
        // 查询数据
        this.findSelectTreeAndSelectList()
        this.getList()
        this.getCountByCover()
      })
    },
    getList () {
      this.listLoading = true
      this.submitListQuery.year = this.listQuery.year
      this.submitListQuery.queryDrg = this.listQuery.drgCodg
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data
      })
    },
    queryPageData,
    allExcel () {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.drgsKnowledgeBaseTable.$children, document.getElementById('drgskbTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, 'DRG知识库')
    },
    getCountByCover () {
      this.listLoading = true
      this.submitListQuery.year = this.listQuery.year
      this.submitListQuery.queryDrg = this.listQuery.drgCodg
      getCountByCoverRate(this.submitListQuery).then(response => {
        this.listLoading = false
        let result = response.data
        let title1 = 'MDC覆盖情况'
        let mdcCoverItem = ['MDC覆盖', 'MDC未覆盖']
        let mdcCoverData = [
          { value: result.mdcNotCoverNum, name: 'MDC未覆盖' },
          { value: result.mdcCoverNum, name: 'MDC覆盖' }
        ]
        let title2 = 'DRGs覆盖情况'
        let drgsCoverItem = ['DRGs覆盖', 'DRGs未覆盖']
        let drgsCoverData = [
          { value: result.drgsNotCoverNum, name: 'DRGs未覆盖' },
          { value: result.drgsCoverNum, name: 'DRGs覆盖' }
        ]
        let title3 = '内外科非手术室操作组分布情况'
        let medicalAndSurgeryItem = ['内科组', '外科组', '非手术室操作组']
        let medicalAndSurgeryData = [
          { value: result.medicalDeptNum, name: '内科组' },
          { value: result.surgeryDeptNum, name: '外科组' },
          { value: result.notOperationNum, name: '非手术室操作组' }
        ]
        let title4 = '伴随症不同严重程度组分布情况'
        let complicationItem = ['伴严重合并症及伴随病组', '伴一般合并症及伴随病组', '不伴合并症及伴随病组']
        let complicationData = [
          { value: result.seriousComplication, name: '伴严重合并症及伴随病组' },
          { value: result.normalComplication, name: '伴一般合并症及伴随病组' },
          { value: result.noComplication, name: '不伴合并症及伴随病组' }
        ]
        this.leftChart('mdcCoverRageChart', title1, mdcCoverItem, mdcCoverData)
        this.leftChart('drgsCoverRageChart', title2, drgsCoverItem, drgsCoverData)
        this.leftChart('medicalAndSurgeryCoverRageChart', title3, medicalAndSurgeryItem, medicalAndSurgeryData)
        this.leftChart1('complicationCoverRageChart', title4, complicationItem, complicationData)
      })
    },
    hasGroupStyle ({ row, rowIndex }) {
      if (row.medcasVal) {
        return {
          'background-color': '#336688',
          'font-size': '14px',
          'font-weight': 'bold',
          color: '#ffffff'
        }
      }
    },
    drgsStyle ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        return {
          'font-weight': 'bold'
        }
      }
    },
    querySearchAsync (queryString, cb) {
      const param = {
        likeQueryString: queryString
      }
      queryLikeDrgsByPram(param).then(response => {
        cb(response.data)
        this.$refs.elautocomplete.handleFocus()
      })
    },
    handleSelect (item) {
      this.listQuery.queryDrg = item.drgsCode
    },
    handleSearchList () {
      this.getList()
      this.getCountByCover()
    },
    handleResetSearch () {
      this.listQuery = Object.assign({}, defaultListQuery)
      this.year = null
      this.queryDrg = null
      this.getList()
      this.getCountByCover()
    },
    leftChart (id, profttl, coverItem, coverData) {
      let colors = ['rgba(38,185,181,0.8)', 'rgba(40,138,242,0.8)', 'rgba(11,162,179,0.8)']
      let option = {
        title: [
          { text: profttl, left: '5', top: 1, textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 13 } }
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b}:{c} ({d}%)'
        },
        legend: {
          left: 'center',
          bottom: '0',
          type: 'scroll',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: { color: '#666666', fontSize: 10 },
          data: coverItem
        },
        series: [
          {
            type: 'pie',
            radius: '55%', // 设置饼图大小
            center: ['50%', '54%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'outside',
                formatter: '{b}\n{c}({d}%)',
                fontFamily: 'Microsoft YaHei',
                fontSize: 11,
                color: '#000000'
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % coverData.length]
                }
              }
            },
            data: coverData
          }
        ]
      }

      let leftChart = echarts.getInstanceByDom(document.getElementById(id))
      if (leftChart) {
        leftChart.clear()
      } else {
        leftChart = echarts.init(document.getElementById(id))
      }
      leftChart.setOption(option)
      return leftChart
    },
    leftChart1 (id, profttl, coverItem, coverData) {
      let colors = ['rgba(38,185,181,0.8)', 'rgba(40,138,242,0.8)', 'rgba(11,162,179,0.8)']
      let total = 0
      for (let i = 0; i < coverData.length; i++) {
        total = total + Number(coverData[i].value)
      }
      let get = function (e) {
        let newStr = ''
        let name_len = e.name.length // 每个内容名称的长度
        let max_name = 5 // 每行最多显示的字数
        if (name_len > max_name) {
          newStr = e.name.slice(0, max_name) + '..' + '\n' // 拼接字符串,超出部分省略号
        } else {
          newStr = e.name
        }
        let rate = ((Number(e.value) / (Number(total) == 0 ? 1 : Number(total))) * 100).toFixed(2) + '%'
        let result = newStr + e.value + '(' + rate + ')'
        return result
      }
      let option = {
        title: [
          { text: profttl, left: '5', top: 1, textStyle: { fontFamily: 'Microsoft YaHei', fontSize: 13 } }
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b}:{c} ({d}%)'
        },
        legend: {
          left: 'center',
          bottom: '0',
          type: 'scroll',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: { color: '#666666', fontSize: 10 },
          data: coverItem
        },
        series: [
          {
            type: 'pie',
            radius: '55%', // 设置饼图大小
            center: ['50%', '54%'],
            labelLine: {
              show: true,
              lineStyle: {
                color: '#dddddd'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'outside',
                formatter: get,
                fontFamily: 'Microsoft YaHei',
                fontSize: 10,
                color: '#000000'
              }
            },
            itemStyle: {
              normal: {
                color: function (seriesData) {
                  return colors[seriesData.dataIndex % coverData.length]
                }
              }
            },
            data: coverData
          }
        ]
      }

      let leftChart = echarts.getInstanceByDom(document.getElementById(id))
      if (leftChart) {
        leftChart.clear()
      } else {
        leftChart = echarts.init(document.getElementById(id))
      }
      leftChart.setOption(option)
      return leftChart
    },
    showTable () {
      this.forArr(this.list, true)
    },
    hiddenTable () {
      this.forArr(this.list, false)
    },
    forArr (arr, isExpand) {
      arr.forEach(i => {
        this.$refs.drgsKnowledgeBaseTable.toggleRowExpansion(i, isExpand)
        if (i.children) {
          this.forArr(i.children, isExpand)
        }
      })
    },
    exportExcel () {
      let tableId = 'drgskbTable'
      let fileName = 'DRGs知识库'
      elExportExcel(tableId, fileName)
    }
  }
}
</script>
<style scoped>
  /deep/ .el-scrollbar__wrap {
    max-height: 450px;
  }
  /deep/ .el-autocomplete-suggestion li {
    line-height: 20px;
  }
  .code {
    font-size: 12px;
    color: #000000;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .name {
    font-size: 10px;
    color: #9b9b9b;
    text-overflow: ellipsis;
    overflow: hidden;
  }
</style>
