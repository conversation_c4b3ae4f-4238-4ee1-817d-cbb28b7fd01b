@echo off
if "%1"=="" (
    echo package filename is null, Exit in 5 seconds
    timeout /t 5
    exit
) else (
    if "%2"=="" (
        echo branch name is null, Exit in 5 seconds
        timeout /t 5
        exit
    ) else (
        if "%3"=="" (
            echo project name is null
            timeout /t 5
            exit
        )
    )
)


SET packageProjPath=%cd%\%1
cd ..
SET projPath=%cd%
SET bgCssPath=%projPath%\src\styles\loginStyles
SET passwdPath=%projPath%\src\store\modules
SET hostPath=%projPath%\config

call git checkout %2
if %errorlevel% == 0 (

  cd %packageProjPath%
  copy index.html %projPath%
  copy style.css %bgCssPath%
  copy user.js %passwdPath%
  copy host.js %hostPath%

  echo execute npm package...
  cd %projPath%
  npm run build

  ren dist %3

  pause

) else (

  echo git error, Exit in 5 seconds
  timeout /t 5

)

