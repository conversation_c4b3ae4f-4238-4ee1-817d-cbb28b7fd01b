// 引入导出Excel表格依赖
import FileSaver from 'file-saver'
// import XLSX from 'xlsx'
import XLSX from '../../static/xlsx-js-style/xlsx.bundle.js'

// 定义导出Excel表格事件
export function elExportExcel (tableId, fileName, index = -1) {
  let fix = getFix(index)
  let wb

  // 获取表格元素
  const tableElement = document.querySelector('#' + tableId)
  if (!tableElement) {
    console.error('Table element not found.')
    return
  }

  // 处理包含固定表格元素
  if (fix) {
    wb = XLSX.utils.table_to_book(tableElement.removeChild(fix))
    tableElement.appendChild(fix)
  } else {
    wb = XLSX.utils.table_to_book(tableElement)
  }

  var new_wb = XLSX.utils.book_new()

  // 获取工作表
  const ws = wb.Sheets[wb.SheetNames[0]]

  // 获取表头行
  const range = XLSX.utils.decode_range(ws['!ref'])
  const headerRow = range.s.r

  // 记录需要设置为字符串类型的列
  const stringCols = []

  // 查找包含“占比”字样的列
  for (let C = range.s.c; C <= range.e.c; ++C) {
    const headerCellRef = XLSX.utils.encode_cell({ r: headerRow, c: C })
    const headerCell = ws[headerCellRef]
    if (headerCell && headerCell.v && typeof headerCell.v === 'string' && headerCell.v.includes('占比')) {
      stringCols.push(C)
    }
  }

  // 设置特定列为字符串类型，并保留格式
  for (const col of stringCols) {
    for (let R = headerRow + 1; R <= range.e.r; ++R) {
      const cellRef = XLSX.utils.encode_cell({ r: R, c: col })
      const cell = ws[cellRef]
      if (cell && cell.v !== undefined) {
        cell.t = 's'
        cell.v = (parseFloat(cell.v) * 100).toFixed(2) + '%'
      }
    }
  }

  // 计算每列的最大宽度，并设置列宽
  const colWidths = []
  for (let C = range.s.c; C <= range.e.c; ++C) {
    let maxWidth = 10 // 默认宽度
    for (let R = range.s.r; R <= range.e.r; ++R) {
      const cellRef = XLSX.utils.encode_cell({ r: R, c: C })
      let cell = ws[cellRef]
      if (cell) {
        ws[cellRef].s = {
          alignment: {
            horizontal: 'center',
            vertical: 'center'
          }
        }
      }
      console.log(cell)
      if (cell && cell.v !== undefined) {
        const cellValue = cell.v.toString()
        const cellWidth = cellValue.length * 2 // 宽度计算
        if (cellWidth > maxWidth) {
          maxWidth = cellWidth
        }
      }
    }
    colWidths.push({ wch: maxWidth })
  }

  // 应用列宽
  ws['!cols'] = colWidths
  XLSX.utils.book_append_sheet(new_wb, ws, fileName)

  // XLSX.utils.sheets
  // XLSX.utils.sheet_set_range_style(ws, 'A1:C4', style); // 设置单元格样式
  // 导出 Excel 文件
  let wbout = XLSX.write(new_wb, { bookType: 'xlsx', bookSST: false, type: 'array' })
  // let wbout = XLSX.write(wb, {bookType: 'xlsx', bookSST: false, type: 'binary'});

  try {
    FileSaver.saveAs(new Blob([wbout], { type: 'application/octet-stream' }), fileName + '.xlsx')
  } catch (e) {
    if (typeof console !== 'undefined') console.log(e, wbout)
  }
  return wbout
}

function getFix (index) {
  let fix
  if (index == -1) {
    fix = document.querySelector('.el-table__fixed')
    if (!fix) {
      fix = document.querySelector('.el-table__fixed-right')
    }
    return fix
  } else {
    fix = document.querySelectorAll('.el-table__fixed')
    if (!fix) {
      fix = document.querySelectorAll('.el-table__fixed-right')
    }
    return fix[index]
  }
}
