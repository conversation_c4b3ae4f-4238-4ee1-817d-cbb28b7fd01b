<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :show-dip="{ show: showDip }"
             :show-hos-dept="{ show: showDept }"
             show-pagination
             :total-num="total"
             :container="true"
             headerTitle="查询条件"
             contentTitle="DIP病种类型配置列表"
             @query="queryData">

      <template slot="buttons">
        <el-button type="success" @click="addDipDiseaseType" style="margin-right: 16px">新增病种类型</el-button>
      </template>

      <template slot="extendFormItems">
        <!-- 中医编码 -->
        <el-form-item label="中医编码" prop="chineseCode" v-if="showCode">
          <el-input v-model="queryForm.chineseCode" placeholder="请输入中医编码"/>
        </el-form-item>

        <!-- 医保编码 -->
        <el-form-item label="医保编码" prop="medicalCode" v-if="showCode">
          <el-input v-model="queryForm.medicalCode" placeholder="请输入医保编码"/>
        </el-form-item>

        <!-- 病种类型 -->
        <el-form-item label="病种类型" prop="diseaseCFTType" v-if="showDiseaseCFT">
          <el-select v-model="queryForm.diseaseCFTType" placeholder="请选择病种类型">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </template>

      <!-- 内容 -->
      <template slot="containerContent">
        <el-tabs v-model="tabActiveName" @tab-click="tabClick" class="som-table-height">
          <el-tab-pane label="基层病种" name="2" class="som-tab-pane">
            <dip-disease-type-config-table :table-loading="baseTableLoading"
                                           :table-data="baseTableData"
                                           :type="2"
                                           @queryData="queryData"
                                           @editDipDiseaseType="editDipDiseaseType($event)" />
          </el-tab-pane>

          <el-tab-pane label="中医优势" name="1" class="som-tab-pane">
            <dip-disease-type-config-table :table-loading="chineseTableLoading"
                                           :table-data="chineseTableData"
                                           :type="1"
                                           @queryData="queryData"
                                           @editDipDiseaseType="editDipDiseaseType($event)" />
          </el-tab-pane>

          <el-tab-pane label="重点专科" name="4" class="som-tab-pane">
            <dip-disease-type-config-table :table-loading="professionalTableLoading"
                                           :table-data="professionalTableData"
                                           :type="4"
                                           @queryData="queryData"
                                           @editDipDiseaseType="editDipDiseaseType($event)" />
          </el-tab-pane>

          <el-tab-pane label="非稳定病种" name="6" class="som-tab-pane">
            <dip-disease-type-config-table :table-loading="unstableDiseaseLoading"
                                           :table-data="unstableTableData"
                                           :type="6"
                                           @queryData="queryData"
                                           @editDipDiseaseType="editDipDiseaseType($event)" />
          </el-tab-pane>

          <el-tab-pane label="病种类型系数" name="5" class="som-tab-pane">
            <dip-disease-type-config-table :type="5"
                                           :table-data="diseaseCFTTableData"
                                           :table-loading="diseaseCFTTableLoading"
                                           @queryData="queryData"
                                           @editDipDiseaseType="editDipDiseaseType($event)" />
          </el-tab-pane>

        </el-tabs>

        <el-dialog
          :title="profttl"
          v-som-dialog-drag
          @opened="dialogOpened"
          @closed="setActive"
          :visible.sync="showDipDiseaseType"
          width="25%">
          <el-form :inline="true" v-model="diaData" ref="somForm">

            <!-- 基层病种 -->
            <el-form-item label="DIP编码" prop="dipCodg" v-if="tabActiveName == 2" style="margin-left: 60.6px">
              <el-input class="som-form-item" v-model="diaData.dipCodg" :disabled="!addOrEdit" />
            </el-form-item>
            <el-form-item label="诊断编码" prop="dipDiagCodg" v-if="tabActiveName == 2" style="margin-left: 56px">
              <el-input class="som-form-item" v-model="diaData.dipDiagCodg" />
            </el-form-item>
            <el-form-item label="诊断名称" prop="dipDiagName" v-if="tabActiveName == 2" style="margin-left: 56px">
              <el-input class="som-form-item" v-model="diaData.dipDiagName" />
            </el-form-item>
            <el-form-item label="手术编码" prop="dipOprtCodg" v-if="tabActiveName == 2" style="margin-left: 56px">
              <el-input class="som-form-item" v-model="diaData.dipOprtCodg" />
            </el-form-item>
            <el-form-item label="手术名称" prop="dipOprtName" v-if="tabActiveName == 2" style="margin-left: 56px">
              <el-input class="som-form-item" v-model="diaData.dipOprtName" />
            </el-form-item>
            <el-form-item label="是否使用辅助目录" prop="usedAsstList" v-if="tabActiveName == 2">
              <el-input class="som-form-item" v-model="diaData.usedAsstList" />
            </el-form-item>
            <el-form-item label="是否稳定病种" prop="isStabilizeDisease" v-if="tabActiveName == 2" style="margin-left: 28px">
              <el-input class="som-form-item" v-model="diaData.isStabilizeDisease" />
            </el-form-item>
            <el-form-item label="病种类型" prop="diseType" v-if="tabActiveName == 2" style="margin-left: 56px">
              <drg-dict-select v-model="diaData.diseType" dicType="DISEASE_TYPE" placeholder="请选择类型" />
            </el-form-item>

            <!-- 中医优势 -->
            <el-form-item label="中医编码" prop="chineseDiseaseCode" v-if="tabActiveName == 1">
              <el-input class="som-form-item" v-model="diaData.chineseDiseaseCode" />
            </el-form-item>
            <el-form-item label="中医名称" prop="chineseDiseaseName" v-if="tabActiveName == 1">
              <el-input class="som-form-item" v-model="diaData.chineseDiseaseName" />
            </el-form-item>
            <el-form-item label="医保编码" prop="medicalCode" v-if="tabActiveName == 1">
              <el-input class="som-form-item" v-model="diaData.medicalCode" />
            </el-form-item>
            <el-form-item label="医保名称" prop="medicalName" v-if="tabActiveName == 1">
              <el-input class="som-form-item" v-model="diaData.medicalName" />
            </el-form-item>

            <!-- 重点专科 -->
            <el-form-item label="院内科室编码" prop="inhospDeptCodg" v-if="tabActiveName == 4">
              <el-input class="som-form-item" v-model="diaData.inhospDeptCodg" />
            </el-form-item>
            <el-form-item label="院内科室名称" prop="inhospDeptName" v-if="tabActiveName == 4">
              <el-input class="som-form-item" v-model="diaData.inhospDeptName" />
            </el-form-item>
            <el-form-item label="重点专科名称" prop="keySpcyName" v-if="tabActiveName == 4">
              <el-input class="som-form-item" v-model="diaData.keySpcyName" />
            </el-form-item>
            <el-form-item label="重点专科类型" prop="keySpcyType" v-if="tabActiveName == 4">
              <drg-dict-select v-model="diaData.keySpcyType" dicType="PROFESSIONAL_LEVEL" placeholder="重点专科类型" />
            </el-form-item>

            <!-- 病种类型系数 -->
            <el-form-item label="病种类型" prop="diseaseCFTType" v-if="tabActiveName == 5" style="margin-left: 23px">
              <drg-dict-select v-model="diaData.diseaseCFTType" dicType="DISEASE_CFT_TYPE" placeholder="请选择病种类型" />
            </el-form-item>
            <el-form-item label="医院等级" prop="hospLv" v-if="tabActiveName == 5" style="margin-left: 23px">
              <el-input class="som-form-item" v-model="diaData.hospLv" />
            </el-form-item>
            <el-form-item label="医院评级" prop="hospRatg" v-if="tabActiveName == 5" style="margin-left: 23px">
              <el-input class="som-form-item" v-model="diaData.hospRatg" />
            </el-form-item>
            <el-form-item label="重点学(专)科" prop="keyDisc" v-if="tabActiveName == 5">
              <el-input class="som-form-item" v-model="diaData.keyDisc" />
            </el-form-item>
            <el-form-item label="病种系数" prop="diseCof" v-if="tabActiveName == 5" style="margin-left: 23px">
              <el-input class="som-form-item" v-model="diaData.diseCof" />
            </el-form-item>

            <el-form-item label="dip编码" prop="dipCodg" v-if="tabActiveName == 6">
              <el-input class="som-form-item" v-model="diaData.dipCodg" />
            </el-form-item>
            <el-form-item label="dip名称" prop="dipName" v-if="tabActiveName == 6">
              <el-input class="som-form-item" v-model="diaData.dipName" />
            </el-form-item>

          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="showDipDiseaseType = false">取 消</el-button>
            <el-button type="primary" @click="clickMethods">
              {{ this.addOrEdit ? '新 增' : '更 新' }}
            </el-button>
          </div>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import dipDiseaseTypeConfigTable from './comps/dipDiseaseTypeConfigTable'
import { insertDipDiseaseType, updateDipDiseaseType, queryData } from '@/api/dataConfig/dipDiseaseTypeConfig'

export default {
  name: 'dipDiseaseTypeConfig',
  components: {
    'dip-disease-type-config-table': dipDiseaseTypeConfigTable
  },
  data: () => ({
    queryForm: {
      chineseCode: '',
      medicalCode: '',
      diseaseCFTType: ''
    },
    tabActiveName: '2',
    total: 0,
    baseTableData: [],
    baseTableLoading: false,
    professionalTableData: [],
    professionalTableLoading: false,
    chineseTableData: [],
    chineseTableLoading: false,
    diseaseCFTTableData: [],
    diseaseCFTTableLoading: false,
    unstableTableData: [],
    unstableDiseaseLoading: false,
    showDip: true,
    showDept: false,
    showCode: false,
    showDiseaseCFT: false,

    showDipDiseaseType: false,
    addOrEdit: false,
    profttl: '',
    diaData: {
      dipCodg: '',
      dipDiagCodg: '',
      dipDiagName: '',
      dipOprtCodg: '',
      dipOprtName: '',
      usedAsstList: '',
      isStabilizeDisease: '',
      diseType: '',
      chineseDiseaseCode: '',
      chineseDiseaseName: '',
      medicalCode: '',
      medicalName: '',
      inhospDeptCodg: '',
      inhospDeptName: '',
      keySpcyName: '',
      keySpcyType: '',
      diseaseCFTType: '',
      hospLv: '',
      hospRatg: '',
      keyDisc: '',
      diseCof: ''
    },
    options: [{
      value: '1',
      label: '中医优势病种'
    }, {
      value: '2',
      label: '基层病种'
    }, {
      value: '3',
      label: '低龄病种'
    }, {
      value: '4',
      label: '重点学(专)科病种'
    }]
  }),
  mounted () {
    this.queryData()
  },
  methods: {
    queryData () {
      this.startOrStopLoading(true)
      queryData(this.getParams()).then(res => {
        if (res.code == 200) {
          if (this.tabActiveName == '1') {
            this.chineseTableData = res.data.list
          }
          if (this.tabActiveName == '2') {
            this.baseTableData = res.data.list
          }
          if (this.tabActiveName == '4') {
            this.professionalTableData = res.data.list
          }
          if (this.tabActiveName == '5') {
            this.diseaseCFTTableData = res.data.list
          }
          if (this.tabActiveName == '6') {
            this.unstableTableData = res.data.list
          }
          this.total = res.data.total
          this.startOrStopLoading(false)
        }
      })
    },
    tabClick () {
      this.queryData()
      this.showCode = false
      this.showDept = false
      this.showDip = false
      this.showDiseaseCFT = false
      if (this.tabActiveName == '1') {
        this.showCode = true
      }
      if (this.tabActiveName == '2') {
        this.showDip = true
      }
      if (this.tabActiveName == '4') {
        this.showDept = true
      }
      if (this.tabActiveName == '5') {
        this.showDiseaseCFT = true
      }
      if (this.tabActiveName == '6') {
        this.showDip = true
      }
    },
    startOrStopLoading (isStart = false) {
      if (this.tabActiveName == '1') {
        this.chineseTableLoading = isStart
      }
      if (this.tabActiveName == '2') {
        this.baseTableLoading = isStart
      }
      if (this.tabActiveName == '4') {
        this.professionalTableLoading = isStart
      }
      if (this.tabActiveName == '5') {
        this.diseaseCFTTableLoading = isStart
      }
      if (this.tabActiveName == '6') {
        this.unstableDiseaseLoading = isStart
      }
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.type = this.tabActiveName
      return params
    },
    addDipDiseaseType () {
      this.showDipDiseaseType = true
      this.addOrEdit = true
      this.profttl = '新增病种类型'
    },
    editDipDiseaseType (row) {
      this.showDipDiseaseType = true
      this.addOrEdit = false
      this.profttl = '编辑病种类型'
      this.templateData = row
    },
    clickMethods () {
      if (this.addOrEdit == true) {
        this.insertDipDiseaseType()
      } else if (this.addOrEdit == false) {
        this.updateDipDiseaseType()
      }
    },
    insertDipDiseaseType () {
      let params = this.diaData
      if (this.tabActiveName == '1') {
        params.type = '1'
        params.chineseCode = this.diaData.chineseDiseaseCode
        params.chineseName = this.diaData.chineseDiseaseName
      }
      if (this.tabActiveName == '2') {
        params.type = '2'
      }
      if (this.tabActiveName == '4') {
        params.type = '4'
      }
      if (this.tabActiveName == '5') {
        params.type = '5'
      }
      if (this.tabActiveName == '6') {
        params.type = '6'
      }
      insertDipDiseaseType(params).then(res => {
        if (res.code == 200) {
          this.$message.success('新增成功')
          this.showDipDiseaseType = false
          this.queryData()
        }
      })
    },
    updateDipDiseaseType () {
      let params = this.diaData
      if (this.tabActiveName == '1') {
        params.type = '1'
        params.chineseCode = this.diaData.chineseDiseaseCode
        params.chineseName = this.diaData.chineseDiseaseName
      }
      if (this.tabActiveName == '2') {
        params.type = '2'
      }
      if (this.tabActiveName == '4') {
        params.type = '4'
      }
      if (this.tabActiveName == '5') {
        params.type = '5'
      }
      if (this.tabActiveName == '6') {
        params.type = '6'
      }
      updateDipDiseaseType(params).then(res => {
        if (res.code == 200) {
          this.$message.success('更新成功')
          this.showDipDiseaseType = false
          this.queryData()
        }
      })
    },
    dialogOpened () {
      if (!this.addOrEdit) {
        let row = this.templateData
        Object.assign(this.diaData, row)
      }
    },
    setActive () {
      this.diaData = {
        dipCodg: '',
        dipName: '',
        dipDiagCodg: '',
        dipDiagName: '',
        dipOprtCodg: '',
        dipOprtName: '',
        usedAsstList: '',
        isStabilizeDisease: '',
        diseType: '',
        chineseDiseaseCode: '',
        chineseDiseaseName: '',
        medicalCode: '',
        medicalName: '',
        inhospDeptCodg: '',
        inhospDeptName: '',
        keySpcyName: '',
        keySpcyType: '',
        diseaseCFTType: '',
        hospLv: '',
        hospRatg: '',
        keyDisc: '',
        diseCof: ''
      }
      this.showDipDiseaseType = false
    }
  }
}
</script>

<style scoped>
/deep/ .el-tabs__content{
  height: 96%;
}
</style>
