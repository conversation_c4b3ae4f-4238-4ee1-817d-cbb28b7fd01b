<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
              :show-date-range="{ show: true, label: timeLabel }"
              :show-in-date-range="{ show: false }"
              :showAccurate="isShow"
              :show-patient-num="{ show : isShow }"
              :show-hos-dept="{show : isShow }"
              :container="true"
              :showPagination="!showPie"
              :totalNum="total"
              :initTimeValueNotQuery="false"
              headerTitle="查询条件"
              :exportExcel="['未上传', '上传失败', '上传成功'].includes(tabName) ? { 'tableId': tableId, exportName: this.tabName} : undefined "
              :exportExcelFun="exportFun"
              :exportExcelHasChild="true"
              @query="queryData" ref="somForm">
      <!-- 查询条件 -->
      <template slot="extendFormItems">
        <el-form-item label="是否标识" prop="lookOver" v-if="isShow">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择" v-model="queryForm.lookOver" @change="queryData"/>
        </el-form-item>
        <el-form-item label="通过校验" prop="validateOver" v-if="isShow">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择" v-model="queryForm.validateOver"
                           @change="queryData"/>
        </el-form-item>
        <el-form-item label="是否修改" prop="isRevise">
          <drg-dict-select dict-type="BOOLEAN" placeholder="请选择" v-model="queryForm.isRevise" @change="queryData"/>
        </el-form-item>
        <el-form-item label="是否异地" prop="isRemote">
          <drg-dict-select dict-type="BOOLEAN" placeholder="请选择" v-model="queryForm.isRemote" @change="queryData"/>
        </el-form-item>
        <el-form-item label="是否自费" prop="isSpend">
          <drg-dict-select dict-type="BOOLEAN" placeholder="请选择" v-model="queryForm.isSpend" @change="queryData"/>
        </el-form-item>
        <el-form-item label="是否完成" prop="isSuccess">
          <drg-dict-select dict-type="BOOLEAN" placeholder="请选择" v-model="queryForm.isSuccess" @change="queryData"/>
        </el-form-item>
      </template>

      <!-- 按钮 -->
      <template slot="buttons">
        <el-button type="primary" @click="dialogVisible = true" class="som-button-margin-right"><i
            class="el-icon-upload el-icon--left"></i>文件上传
        </el-button>
        <el-button type="success" class="som-button-margin-right" size="mini"
                   v-if="['未上传', '上传失败', '上传成功'].includes(tabName)"
                   :disabled="this.uploadData.length > 0 ? false : true" @click="uploadPatient('1')">
          {{ getUploadName }}
        </el-button>
        <el-button type="success" class="som-button-margin-right" size="mini"
                   v-if="['未上传', '上传失败', '上传成功'].includes(tabName)" @click="uploadPatient('2')">
          {{ getAllUploadName }}
        </el-button>
        <el-button type="danger" class="som-button-margin-right" size="mini" v-if="tabName === '撤销'"
                   :disabled="this.revokeData.length > 0 ? false : true" @click="dialogShow">撤销
        </el-button>
        <el-button type="danger" class="som-button-margin-right" size="mini" v-if="tabName === '撤销'"
                   @click="dialogShow">全部撤销
        </el-button>
      </template>
      <!-- profttl -->
      <template slot="contentTitle" v-if="!uploadFalse">
        <drg-title-line :title="profttl">
          <template slot="rightSide">
            <div style="display: flex;position: absolute;top: 20px;right: 0px;" v-show="this.showTab">
              <div>
                <el-button type="primary" size="mini" @click="returnBack">返回</el-button>
              </div>
            </div>
          </template>
        </drg-title-line>
      </template>
      <!-- 内容 -->
      <template slot="containerContent" v-if="!showPie">
        <el-tabs class="som-table-height" v-model="tabName" @tab-click="tabClick">
          <el-tab-pane class="som-tab-pane" label="未上传" name="未上传">
            <list-table :id="tableId"
                        :data="tableData"
                        :loading="loading"
                        :update-status="tabName"
                        @selectData="selectData"
                        ref="dataTable" @setRefObj="(obj) => this.tableObj = obj"/>
          </el-tab-pane>
          <el-tab-pane class="som-tab-pane" label="上传失败" name="上传失败">
            <list-table :id="tableId" :data="tableData" :loading="loading"  :update-status="tabName" @selectData="selectData"
                        ref="dataTable" @setRefObj="(obj) => this.tableObj = obj"/>
          </el-tab-pane>
          <el-tab-pane class="som-tab-pane" label="上传成功" name="上传成功">
            <list-table :id="tableId" :data="tableData" :loading="loading"  :update-status="tabName" @selectData="selectData"
                        ref="dataTable" @setRefObj="(obj) => this.tableObj = obj"/>
          </el-tab-pane>
          <el-tab-pane class="som-tab-pane" label="操作记录" name="记录">
            <record-table ref="recordTable" :data="tableData" :loading="loading" @showBack="item => this.showTab = item"
                          @recordData="item => this.backData = item"/>
            <div style="height: 80%" v-if="this.showTab">
              <list-table :data="detailData" :loading="detailLoading"/>
            </div>
          </el-tab-pane>
        </el-tabs>
        <el-dialog title="请输入撤销原因" :visible.sync="showDialog" width="25%">
          <el-form :model="dialogForm" :rules="rules" ref="dialogForm">
            <el-form-item label="撤销原因" prop="desc" required>
              <el-input type="textarea" placeholder="请输入撤销原因" :rows="4" v-model="dialogForm.desc"/>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="resetForm('dialogForm')">取消</el-button>
            <el-button type="primary" @click="submitForm('dialogForm')">确定</el-button>
          </div>
        </el-dialog>
        <el-dialog
            title="上传文件"
            :visible.sync="dialogVisible"
            width="50%">
          <el-upload
              style="text-align: center"
              drag
              ref="upload"
              :limit="1"
              action="customize"
              accept=".xlsx,.xls"
              :http-request="upload">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
          </el-upload>
        </el-dialog>
      </template>

      <template slot="contentTitle" v-if="uploadFalse">
        <drg-title-line title="结算清单上传">
          <template slot="rightSide">
            <i class="som-icon-pie som-iconTool"
               title="饼图"
               v-if="!showPie"
               @click="changePieOrCard(1)"
               style="height: 1.2rem;width: 1.2rem"></i>
            <i class="som-icon-card som-iconTool"
               title="卡片"
               v-else
               @click="changePieOrCard(2)"
               style="height: 1.2rem;width: 1.2rem"></i>
          </template>
        </drg-title-line>
      </template>

      <template slot="containerContent" v-if="showPie">
        <drg-echarts :options="pieOptions1" ref="pieChart1" @chartClick="pieChartClick"/>
      </template>

    </drg-form>
  </div>
</template>
<script>
import listTable from './comps/listTable'
import recordTable from './comps/recordTable'
import {
  batchListUpload,
  queryDetailSybData,
  queryRecordSybData,
  querySybData as queryPageData,
  queryUpdateTypeData,
  queryUploadedSybData,
  queryUploadFalseData,
  updateSybData
} from '@/api/listManagement/listUpload'

export default {
  name: 'listSybUpload',
  components: {
    listTable,
    recordTable
  },
  data: () => ({
    queryForm: {},
    profttl: '结算清单上传',
    tabName: '未上传',
    tableData: [],
    loading: false,
    total: 0,
    uploadData: [],
    revokeData: [],
    ids: [],
    k00s: [],
    showDialog: false,
    dialogVisible: false,
    dialogForm: {
      desc: ''
    },
    rules: {
      desc: [{ required: true, message: '请输入撤销原因', trigger: 'blur' }]
    },
    isShow: true,
    backNum: 0,
    showTab: false,
    detailData: [],
    detailLoading: false,
    backData: {},
    timeLabel: '结算时间',
    showAccurate: false,
    showPie: false,
    uploadFalse: false,
    pieOptions1: {},
    uploadFalseData: [],
    pieData: [],

    tableId: 'tableId',
    tableObj: {},
    exportFun: queryPageData
  }),
  computed: {
    getUploadName () {
      if (this.tabName === '未上传') {
        return '上传'
      }
      if (['上传失败', '上传成功'].includes(this.tabName)) {
        return '重传'
      }
      return ''
      // if(this.tabName === '状态修改'){
      //   return "修改"
      // }
    },
    getAllUploadName () {
      if (this.tabName === '未上传') {
        return '全部上传'
      }
      if (['上传失败', '上传成功'].includes(this.tabName)) {
        return '全部重传'
      }
      return ''
      // if(this.tabName === '状态修改'){
      //   return "全部修改"
      // }
    }
  },
  mounted () {
    this.$nextTick(() => {
      if (Object.keys(this.$route.query.length > 0)) {
        if (this.$route.query.tabName1) {
          this.tabName = this.$route.query.tabName1
          this.selectData(this.tabName)
        }
        // 此处写结算时间无效
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.queryForm)
        }
      }
      this.queryData()
    })
  },
  methods: {
    queryPageData,
    queryUploadedSybData,
    queryRecordSybData,
    queryUpdateTypeData,
    setPieOptions1 () {
      this.pieOptions1 = {
        color: this.$somms.generateColor(),
        title: {
          text: '结算清单上传失败类型统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '失败类型',
            type: 'pie',
            radius: '50%',
            data: this.uploadFalseData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      // 生成图表
      if (this.showPie) {
        this.$nextTick(() => {
          this.$refs.pieChart1.initChart()
        })
      }
    },
    queryUploadFalse: function () {
      // 查询数据
      let params = this.getParams()
      params.upldStas = '0'
      queryUploadFalseData(params).then(res => {
        this.uploadFalseData = res.data
        this.setPieOptions1()
      })
    },
    pieChartClick: function (params) {
      let k00Str = params.data.k00
      let k00Arr = k00Str.split(',')
      let params1 = this.getParams()
      params1.upldStas = '0'
      params1.k00s = k00Arr
      this.showPie = false
      queryUploadedSybData(params1).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.list
          this.total = res.data.total
          this.loading = false
        }
      })
    },
    changePieOrCard (index) {
      if (index == 1) {
        this.showPie = true
        // 查询上传失败清单
        this.queryUploadFalse()
      } else {
        this.showPie = false
      }
    },
    queryData () {
      this.loading = true
      if (this.tabName === '记录') {
        queryRecordSybData(this.getParams()).then(res => {
          this.tableData = res.data
          this.loading = false
          this.exportFun = queryRecordSybData
        })
      } else if (this.tabName === '未上传') {
        queryPageData(this.getParams()).then(res => {
          if (res.code == 200) {
            this.tableData = res.data.list
            this.total = res.data.total
            this.loading = false
            this.exportFun = queryPageData
          }
        })
      } else if (['上传失败', '上传成功'].includes(this.tabName)) {
        let params = this.getParams()
        if (this.tabName === '上传失败') {
          params.upldStas = '0'
          this.queryForm.upldStas = '0'
        } else {
          params.upldStas = '1'
          this.queryForm.upldStas = '1'
        }
        queryUploadedSybData(params).then(res => {
          if (res.code === 200) {
            this.tableData = res.data.list
            this.total = res.data.total
            this.loading = false
            this.exportFun = queryUploadedSybData
          }
        })
      }
      // else if(this.tabName === "状态修改"){
      //   let params = this.getParams()
      //   params.upldStas = '1'
      //   queryUpdateTypeData(params).then(res => {
      //     if(res.code === 200){
      //       this.tableData = res.data.list
      //       this.total = res.data.total
      //       this.loading = false
      //       this.exportFun = queryUpdateTypeData
      //     }
      //   })
      // }
    },
    tabClick () {
      if (['未上传', '上传失败', '上传成功'].includes(this.tabName)) {
        this.profttl = '结算清单上传'
        this.tableData = []
        this.revokeData = []
        this.isShow = true
        this.timeLabel = '结算时间'
        this.uploadFalse = false
      } else if (this.tabName == '撤销') {
        this.profttl = '结算清单撤销'
        this.tableData = []
        this.uploadData = []
        this.isShow = true
        this.uploadFalse = false
      } else if (this.tabName == '记录') {
        this.profttl = '操作记录'
        this.tableData = []
        this.uploadData = []
        this.revokeData = []
        this.isShow = false
        this.timeLabel = '操作时间'
        this.uploadFalse = false
      }
      if (this.tabName == '上传失败') {
        this.uploadFalse = true
      }
      this.queryData()
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      if (['未上传', '上传失败', '上传成功'].includes(this.tabName)) {
        params.uploadFlag = '0'
      } else if (this.tabName === '撤销') {
        params.uploadFlag = '1'
      }
      this.ids = []
      this.k00s = []
      if (this.uploadData.length > 0 && this.uploadData instanceof Array) {
        this.uploadData.forEach(item => this.ids.push(item.id))
        this.uploadData.forEach(item => this.k00s.push(item.k00))
      }
      if (this.revokeData.length > 0 && this.uploadData instanceof Array) {
        this.revokeData.forEach(item => this.ids.push(item.id))
        this.revokeData.forEach(item => this.k00s.push(item.k00))
      }
      params.name = this.$store.getters.name
      params.nknm = this.$store.getters.nickname
      params.ids = this.ids
      params.k00s = this.k00s
      params.reason = this.dialogForm.desc
      return params
    },
    selectData (item) {
      if (['未上传', '上传失败', '上传成功'].includes(this.tabName)) {
        this.uploadData = item
      } else if (this.tabName == '撤销') {
        this.revokeData = item
      }
    },
    uploadPatient (type = '1') {
      let msg, profttl
      // if(this.tabName === "状态修改"){
      //   if(type === "1"){
      //     msg = "是否修改清单状态（stas_type），修改后清单将无法再重传？"
      //   } else {
      //     msg = "是否修改当前时间范围内所有清单状态（stas_type），修改后清单将无法再重传？"
      //   }
      //   profttl = '清单状态修改提示'
      // }
      // else
      // {
      if (type === '1') {
        msg = '是否确认上传清单，上传后不能再修改清单？'
      } else {
        msg = '是否全部上传清单且只能上传校验成功数据，上传后不能再修改清单？'
      }
      profttl = '清单上传提示'
      // }
      let params = this.getParams()
      if (this.tabName === '未上传') {
        params.uploadType = '1'
      } else if (this.tabName === '上传失败') {
        params.uploadType = '2'
        params.upldStas = '0'
      } else if (['上传成功'].includes(this.tabName)) {
        params.uploadType = '2'
        params.upldStas = '1'
      } else {
        params.uploadType = '0'
      }
      this.$confirm(msg, profttl, {
        type: 'warning'
      }).then(() => {
        if (this.tableData.length > 0) {
          this.loading = true
          updateSybData(params).then(res => {
            if (res.code === 200) {
              if (res.data.state === '1') {
                this.$message({ message: '上传成功', type: 'success' })
              } else {
                this.$message({ message: res.data.errMsg, type: 'warning' })
              }
              this.uploadData = []
              this.loading = false
              this.queryData()
            }
          }).catch(() => {
            this.loading = false
          })
        } else {
          this.$message({ message: '暂无上传数据', type: 'warning' })
        }
      })
    },
    dialogShow () {
      if (this.tableData.length > 0) {
        this.showDialog = true
      } else {
        this.$message({ message: '暂无撤销数据', type: 'warning' })
      }
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          updateSybData(this.getParams()).then(res => {
            if (res.code == 200) {
              this.showDialog = false
              this.$refs[formName].resetFields()
              this.$message({ message: '撤销成功', type: 'success' })
              this.revokeData = []
              this.queryData()
            }
          })
        } else {
          this.$message({ message: '请输入撤销原因', type: 'warning' })
        }
      })
    },
    resetForm (formName) {
      this.$refs[formName].resetFields()
      this.showDialog = false
    },
    returnBack () {
      this.showTab = false
      this.backData = {}
      this.$refs['recordTable'].returnRow()
      this.$refs['recordTable'].doLayout()
    },
    showDetail (row) {
      if (row) {
        this.detailLoading = true
        queryDetailSybData({ ...row, pageNum: 1, pageSize: 20 }).then(res => {
          this.detailData = res.data.list
          this.total = res.data.total
          this.detailLoading = false
        })
      }
    },
    upload (data) {
      let params = new FormData()
      params.append('file', data.file)
      params.append('group', this.queryForm.group)
      batchListUpload(params).then(res => {
        if (res.code == 200) {
          this.dialogVisible = false
          this.$refs.upload.clearFiles()
          if (res.data.state == '1') {
            this.$message({ message: '上传成功', type: 'success' })
          } else {
            this.$message({ message: res.data.errMsg, type: 'warning' })
          }
          this.queryData()
        }
      }).catch(() => {
        this.$refs.upload.clearFiles()
      })
    }
  },
  watch: {
    backData: {
      handler: function (row) {
        this.showDetail(row)
      }
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (vm.$refs.dataTable) {
        vm.$refs.dataTable.updateTable()
      }
    })
  }
}
</script>

<style scoped>
/deep/ .el-tabs__content {
  height: 97%;
}
</style>
