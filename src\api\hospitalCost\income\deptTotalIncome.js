import request from '@/utils/request'
/**
 * 查询科室收入最终数据
 * @param params
 * @returns {*}
 */
export function selectDeptTotalIncome (params) {
  return request({
    url: '/deptTotalIncomeController/selectDeptTotalIncome',
    method: 'post',
    params: params
  })
}
/**
 * 查询科室收入计算数据
 * @param params
 * @returns {*}
 */
export function insertDeptIncomeDetails (params) {
  return request({
    url: '/deptTotalIncomeController/insertDeptIncomeDetails',
    method: 'post',
    params: params
  })
}
/**
 * 初查询科室收入计算数据
 * @param params
 * @returns {*}
 */
export function firstSelectDeptTotalIncome (params) {
  return request({
    url: '/deptTotalIncomeController/firstSelectDeptTotalIncome',
    method: 'post',
    params: params
  })
}
/**
 * 查询科室收入share表
 * @param params
 * @returns {*}
 */
export function selectDeptIncomeShare (params) {
  return request({
    url: '/deptTotalIncomeController/selectDeptIncomeShare',
    method: 'post',
    params: params
  })
}
