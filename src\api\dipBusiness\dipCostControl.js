import request from '@/utils/request'
export function getDipGroupAndCostControl (params) {
  return request({
    url: '/dipCostControl/getDipGroupAndCostControl',
    method: 'post',
    params: params
  })
}

export function getDipGroupNumTop10 (params) {
  return request({
    url: '/dipCostControl/getDipGroupNumTop10',
    method: 'post',
    params: params
  })
}

export function getDipGroupCostTop10 (params) {
  return request({
    url: '/dipCostControl/getDipGroupCostTop10',
    method: 'post',
    params: params
  })
}

export function getCostCountInfo (params) {
  return request({
    url: '/dipCostControl/getCostCountInfo',
    method: 'post',
    params: params
  })
}
