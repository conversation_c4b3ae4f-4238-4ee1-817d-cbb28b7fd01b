<template>
    <el-form :model="queryForm" ref="queryForm" :rules="queryForm.rules" :inline="true">
      <el-form-item label="期号" prop="ym" required>
        <el-date-picker
          v-model="queryForm.ym"
          type="month"
          value-format="yyyy-MM"
          placeholder="选择期号">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="编制单位" required>
        <drg-dict-select dicType="ORGANIZATION_UNIT"
                        placeholder="请选择编制单位"
                        :clearable="false"
                        v-model="queryForm.organizationUnit" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="queryData">查询</el-button>
        <el-button @click="refresh">重置</el-button>
      </el-form-item>
    </el-form>
</template>
<script>
export default {
  name: 'hospitalCostReportSelect',
  inject: ['reload'],
  data: () => ({
    queryForm: {
      ym: '',
      organizationUnit: '',
      rules: {
        ym: [
          { required: true, message: '请选择期号', trigger: 'change' }
        ],
        organizationUnit: [
          { required: true, message: '请输入编制单位', trigger: 'blur' }
        ]
      }
    }
  }),
  mounted () {
    this.queryForm.organizationUnit = this.$somms.getDictValue('ORGANIZATION_UNIT')[0].value
    this.queryForm.ym = this.$somms.getDate('yyyy-MM', 0, -1)
    this.queryData()
  },
  methods: {
    queryData () {
      this.$refs['queryForm'].validate((valid) => {
        if (valid) {
          this.$emit('queryData', this.getParams())
        } else {
          return false
        }
      })
    },
    getParams () {
      let params = {}
      Object.assign(params, this.queryForm)
      params.ym = params.ym.replace('-', '')
      return params
    },
    refresh () {
      this.reload()
    }
  }
}
</script>
