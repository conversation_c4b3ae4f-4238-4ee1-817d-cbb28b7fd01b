<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             :container="true"
             show-in-date-range
             show-se-date-range
             showDateRange
             showPagination
             :totalNum="tableTotalNum"
             headerTitle="查询条件"
             contentTitle="列表"
             @query="query">
      <!-- 按钮 -->
      <template #buttons>
        <el-button class="som-button-margin-right" type="primary" @click="checkColumnClick(undefined)">指标/维度
        </el-button>
      </template>
      <!-- 内容 -->
      <template slot="containerContent">
        <el-table
          :data="tableData"
          ref="elTable"
          height="100%">
          <div v-if="columnData.length > 0">
            <el-table-column type="index" label="序号">
            </el-table-column>
          </div>
          <div v-for="(item,index) in columnData" :key="index">
            <div v-if="item.pageDispFld === 'inventory14Coststructure'">
              <el-table-column label="床位费" prop="fourteencwf"></el-table-column>
              <el-table-column label="诊查费" prop="fourteenzcf"></el-table-column>
              <el-table-column label="检查费" prop="fourteenjcf"></el-table-column>
              <el-table-column label="化验费" prop="fourteenhyf"></el-table-column>
              <el-table-column label="治疗费" prop="fourteenzlf"></el-table-column>
              <el-table-column label="手术费" prop="fourteenssf"></el-table-column>
              <el-table-column label="护理费" prop="fourteenhlf"></el-table-column>
              <el-table-column label="卫生材料费" prop="fourteenwsclf"></el-table-column>
              <el-table-column label="西药费" prop="fourteenxyf"></el-table-column>
              <el-table-column label="中药饮片费" prop="fourteenzyypf"></el-table-column>
              <el-table-column label="中成药费" prop="fourteenzcyf"></el-table-column>
              <el-table-column label="一般诊疗费" prop="fourteenybzlf"></el-table-column>
              <el-table-column label="挂号费" prop="fourteenghf"></el-table-column>
              <el-table-column label="其他费" prop="fourteenqtf"></el-table-column>
            </div>
            <div v-else-if="item.pageDispFld === 'medical10Coststructure'">
              <el-table-column label="一般医疗服务费" prop="D11"></el-table-column>
              <el-table-column label="一般治疗操作费" prop="D12"></el-table-column>
              <el-table-column label="护理费" prop="D13"></el-table-column>
              <el-table-column label="综合医疗服务类其他费用" prop="D14"></el-table-column>
              <el-table-column label="病理诊断费" prop="D15"></el-table-column>
              <el-table-column label="实验室诊断费" prop="D16"></el-table-column>
              <el-table-column label="影像学诊断费" prop="D17"></el-table-column>
              <el-table-column label="临床诊断项目费" prop="D18"></el-table-column>
              <el-table-column label="非手术治疗项目费" prop="D19"></el-table-column>
              <el-table-column label="手术治疗费" prop="D20"></el-table-column>
              <el-table-column label="康复费" prop="D21"></el-table-column>
              <el-table-column label="中医治疗费" prop="D22"></el-table-column>
              <el-table-column label="西药费" prop="D23"></el-table-column>
              <el-table-column label="中成药费" prop="D24"></el-table-column>
              <el-table-column label="中草药费" prop="D25"></el-table-column>
              <el-table-column label="血费" prop="D26"></el-table-column>
              <el-table-column label="白蛋白类制品费" prop="D27"></el-table-column>
              <el-table-column label="球蛋白类制品费" prop="D28"></el-table-column>
              <el-table-column label="凝血因子类制品费" prop="D29"></el-table-column>
              <el-table-column label="细胞因子类制品费" prop="D30"></el-table-column>
              <el-table-column label="检查用一次性医用材料费" prop="D31"></el-table-column>
              <el-table-column label="治疗用一次性医用材料费" prop="D32"></el-table-column>
              <el-table-column label="手术用一次性医用材料费" prop="D33"></el-table-column>
              <el-table-column label="其他费" prop="D34"></el-table-column>
            </div>
            <div v-else>
              <el-table-column :label="item.fieldName" :prop="item.pageDispFld">
              </el-table-column>
            </div>
          </div>

        </el-table>
        <check-column :checkColumnDrawer="checkColumnDrawer" @close="closedCheckColumnDrawer"
                      @generateColumn="generateColumn"></check-column>
      </template>
    </drg-form>
  </div>
</template>
<script>
import CheckColumn from '@/views/composite/comps/checkColumn'
import { queryData } from '@/api/integratedQuery/integratedQuery'

export default {
  name: 'composite',
  components: { CheckColumn },
  data: () => ({
    queryForm: {},
    tableTotalNum: 0,
    tableData: [],
    checkColumnDrawer: false,
    columnData: [],
    checkColumnparams: {}
  }),
  methods: {
    query () {
      if (this.columnData.length > 0) {
        let pa = this.getCheckColumnParams()
        queryData(pa).then(res => {
          if (res.code === 200) {
            this.tableData = res.data.list
            this.tableTotalNum = res.data.total
          }
        })
      } else {
        return null
      }
    },
    generateColumn (column, params) {
      this.checkColumnparams = Object.assign({}, params)
      this.columnData = column
      this.query()
    },
    getCheckColumnParams () {
      let params = Object.assign({}, this.queryForm)
      Object.entries(this.checkColumnparams).forEach(([key, value]) => {
        this.$set(params, key, value)
      })
      return params
    },
    closedCheckColumnDrawer (val) {
      this.checkColumnDrawer = val
    },
    checkColumnClick () {
      this.checkColumnDrawer = true
    }
  },
  updated () {
    this.$refs.elTable.doLayout()
  }
}
</script>
<style scoped>

</style>
