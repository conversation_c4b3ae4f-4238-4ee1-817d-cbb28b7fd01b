import request from '@/utils/request'
// 系统配置

export function queryData (params) {
  return request({
    url: '/sysCommonConfigController/queryData',
    method: 'post',
    params: params
  })
}

export function modifyConfig (params) {
  return request({
    url: '/sysCommonConfigController/modifyConfig',
    method: 'post',
    params: params
  })
}

export function querySettleListData (params) {
  return request({
    url: '/sysCommonConfigController/querySettleListData',
    method: 'post',
    params: params
  })
}

export function updateSysSettleListConfig (params) {
  return request({
    url: '/sysCommonConfigController/updateSysSettleListConfig',
    method: 'post',
    params: params
  })
}
