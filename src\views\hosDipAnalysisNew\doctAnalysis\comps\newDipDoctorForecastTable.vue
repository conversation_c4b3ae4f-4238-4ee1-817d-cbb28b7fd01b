<template>
  <el-table ref="elTable"
            :id="id"
            height="100%"
            stripe
            :header-cell-style="{'text-align':'center'}"
            :data="data"
            v-loading="loading"
            border>
    <el-table-column label="序号" type="index" width="50" fixed align="center"></el-table-column>
    <el-table-column label="科室名称" prop="deptName" width="100" fixed></el-table-column>
    <el-table-column label="住院医师姓名" prop="drName" width="150" fixed></el-table-column>
    <el-table-column label="病案数" prop="medicalTotalNum" width="110" :fixed="include('medicalTotalNum')" align="right" sortable>
      <template slot-scope="scope">
        <div :class="scope.row.medicalTotalNum == 0 ? '' : 'skip'" @click="scope.row.medicalTotalNum == 0 ? '' : queryPayToPredict(scope.row)">
          {{ scope.row.medicalTotalNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="正常付费数量" prop="normalNum" width="130" :fixed="include('normalNum')" align="right" sortable>
      <template slot-scope="scope">
        <div :class="scope.row.normalNum == 0 ? '' : 'skip'" @click="scope.row.normalNum == 0 ? '' : queryPayToPredictNormal(scope.row)">
          {{ scope.row.normalNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="超高病案数" prop="ultrahighNum" width="130" :fixed="include('ultrahighNum')" align="right" sortable>
      <template slot-scope="scope">
        <div :class="scope.row.ultrahighNum == 0 ? '' : 'skip'" @click="scope.row.ultrahighNum == 0 ? '' : queryPayToPredictUltraHigh(scope.row)">
          {{ scope.row.ultrahighNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="超高率" prop="ultrahighRate" width="130" :fixed="include('ultrahighRate')" align="right" sortable>
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.ultrahighRate) }}
      </template>
    </el-table-column>
    <el-table-column label="超低病案数" prop="ultraLowNum" width="130" :fixed="include('ultraLowNum')" align="right" sortable>
      <template slot-scope="scope">
        <div :class="scope.row.ultraLowNum == 0 ? '' : 'skip'" @click="scope.row.ultraLowNum == 0 ? '' : queryPayToPredictUltraLow(scope.row)">
          {{ scope.row.ultraLowNum }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="超低率" prop="ultraLowRate" width="130" :fixed="include('ultraLowRate')" align="right" sortable>
      <template slot-scope="scope">
        {{ $somms.addPercent(scope.row.ultraLowRate) }}
      </template>
    </el-table-column>
    <el-table-column label="总费用" prop="sumfee" width="130" :fixed="include('totalCost')" align="right" sortable></el-table-column>
    <el-table-column label="总费用(反馈)" prop="fbTotalCost" width="160" :fixed="include('fbTotalCost')" align="right" sortable v-if="queryForm.feeStas == 1"></el-table-column>
    <el-table-column :label="this.$somms.getFCOrFBName(this.$store.getters.feeStas,0)" prop="forecastAmount" width="130" :fixed="include('forecastAmount')" align="right" sortable></el-table-column>
    <el-table-column :label="this.$somms.getFCOrFBName(this.$store.getters.feeStas,1)" prop="forecastAmountDiff" width="130" :fixed="include('forecastAmountDiff')" align="right" sortable></el-table-column>
    <el-table-column label="O/E值" prop="oeVal" width="130" :fixed="include('oeVal')" align="right" sortable></el-table-column>
    <el-table-column label="悬浮"  align="center" >
      <template slot-scope="scope">
        <el-button size="mini" type="primary" @click="showSuspension(scope.row)">悬浮</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'newDipDoctorForecastTable',
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 固定列
    fixedColumns: {
      type: Array,
      default: () => []
    },
    // 表格id
    id: {
      type: String
    },
    queryForm: {
      type: Object
    },
    columnOptions: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    include (column) {
      return this.fixedColumns.includes(column)
    },
    // 导出
    setTableObj () {
      this.$emit('setRefObj', this.$refs.elTable)
    },
    // 显示悬浮
    showSuspension (scope) {
      this.scopeData = scope
      let scopeList = []
      for (let i in this.scopeData) {
        let o = {}
        o.key = i
        o.value = this.scopeData[i]
        scopeList.push(o)
      }
      let res = []
      if (scopeList.length) {
        for (let i = 0; i < scopeList.length; i++) {
          for (let j = 0; j < this.columnOptions.length; j++) {
            if (scopeList[i].key == this.columnOptions[j].value) {
              res.push({
                key: scopeList[i].key,
                label: this.columnOptions[j].label,
                value: scopeList[i].value,
                type: 2,
                show: true
              })
            }
          }
        }
      }
      if (scope) {
        this.$emit('showSuspension', res)
      }
    },
    queryPayToPredict (item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: item.drCodg,
          dipCodg: this.queryForm.dipCodg,
          deptCode: this.queryForm.deptCode,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryPayToPredictNormal (item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: item.drCodg,
          dipCodg: this.queryForm.dipCodg,
          deptCode: this.queryForm.deptCode,
          costSection: 3,
          radioMode: 2,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryPayToPredictUltraHigh (item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: item.drCodg,
          dipCodg: this.queryForm.dipCodg,
          deptCode: this.queryForm.deptCode,
          costSection: 1,
          radioMode: 2,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          seEndTime: this.queryForm.seEndTime
        }
      })
    },
    queryPayToPredictUltraLow (item) {
      this.$router.push({
        path: '/hosDipAnalysisNew/pattAnalysis',
        query: {
          begnDate: this.queryForm.begnDate,
          expiDate: this.queryForm.expiDate,
          drCodg: item.drCodg,
          deptCode: this.queryForm.deptCode,
          costSection: 2,
          radioMode: 2,
          categories: this.queryForm.categories,
          inStartTime: this.queryForm.inStartTime,
          inEndTime: this.queryForm.inEndTime,
          inHosFlag: this.queryForm.inHosFlag,
          seStartTime: this.queryForm.seStartTime,
          dipCodg: this.queryForm.dipCodg,
          seEndTime: this.queryForm.seEndTime
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
