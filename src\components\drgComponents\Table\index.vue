<template>
  <div class="som-table-container">
    <el-table :data="tableData"
              :id="tableId"
              :cell-style="cellStyle"
              :border="border"
              :highlight-current-row="highlightCurrentRow"
              :header-cell-style="headerCellStyle"
              :height="height"
              :default-expand-all="defaultExpandAll"
              :width="width"
              :size="size"
              :row-key="rowKey"
              :tree-props="treeProps"
              @sort-change="sortChange">
      <slot />
    </el-table>
  </div>
</template>
<script>
export default {
  name: 'jpTable',
  props: {
    data: {
      type: Array,
      default: function () {
        return []
      }
    },

    cellStyle: [Object, Function],

    border: Boolean,

    highlightCurrentRow: Boolean,

    size: String,

    width: [String, Number],

    height: [String, Number],

    columnEmptyText: {
      type: String,
      default: '-'
    },

    headerCellStyle: {},

    rowKey: String,

    defaultExpandAll: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },

    treeProps: Object,

    tableId: String
  },
  data: () => ({
    tableData: []
  }),
  methods: {
    setEmptyText () {
      if (this.data) {
        let data = []
        let slots = this.$slots.default.filter(item => item.componentOptions)
        for (const item of this.data) {
          for (const slot of slots) {
            let key = slot.componentOptions.propsData.prop
            if (!item[key]) {
              item[key] = this.columnEmptyText
            }
          }
          data.push(item)
        }
        this.tableData = data
      }
    },
    sortChange (column) {
      this.$emit('sort-change', column)
    }
  },
  watch: {
    data: function (data) {
      this.tableData = data
      if (this.columnEmptyText) {
        this.setEmptyText()
      }
    }
  }
}
</script>
<style scoped>
.som-table-container {
  width: 100%;
  height: 100%;
}
</style>
