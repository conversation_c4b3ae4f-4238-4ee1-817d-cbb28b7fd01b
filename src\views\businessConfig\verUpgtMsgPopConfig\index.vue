<template>
  <div class="app-container">
    <drg-form v-model="queryForm"
             show-issue
             :container="true"
             headerTitle="查询条件"
             contentTitle="版本更新日志"
             @query="getData">
      <template slot="extendFormItems">
        <el-form-item label="版本号">
          <el-input  v-model="queryForm.ver" placeholder="请输入版本号"></el-input>
        </el-form-item>
      </template>
      <template slot="buttons">
        <el-button @click="addMessagePrompt()" type="primary" size="mini">新增</el-button>
      </template>
<!--    <drg-container :headerPercent="11">-->
<!--      <template slot="header">-->
<!--        <drg-title-line title="查询条件" />-->
<!--          <el-form :inline="true"  size="mini" style="height: 70%">-->
<!--            <el-form-item label="更新时间" >-->
<!--              <el-date-picker class="som-form-item"-->
<!--                              :clearable="false"-->
<!--                              v-model="queryForm.ym"-->
<!--                              value-format="yyyyMM"-->
<!--                              type="month"-->
<!--                              placeholder="选择月">-->
<!--              </el-date-picker>-->
<!--            </el-form-item>-->
<!--&lt;!&ndash;            <el-form-item label="版本号">&ndash;&gt;-->
<!--&lt;!&ndash;              <el-input  v-model="queryForm.ver" placeholder="请输入版本号"></el-input>&ndash;&gt;-->
<!--&lt;!&ndash;            </el-form-item>&ndash;&gt;-->
<!--            <el-form-item>-->
<!--              <el-button type="primary" @click="getData">查 询</el-button>-->
<!--              <el-button-->
<!--                @click="addMessagePrompt()" type="primary" size="mini">新增</el-button>-->
<!--              <el-button @click="refresh">重 置</el-button>-->
<!--            </el-form-item>-->
<!--          </el-form>-->
<!--      </template>-->
      <template slot="containerContent">
        <div>
          <el-table :data="tableData"
                    border
                    highlight-current-row
                    size="mini"
                    v-loading="tableLoading"
                    style="width: 100%; height: 100%">
            <el-table-column
              type="index"
              label="序号">
            </el-table-column>

            <el-table-column
              prop="ver"
              label="版本号">
            </el-table-column>

            <el-table-column
              prop="content"
              label="描述">
            </el-table-column>

            <el-table-column
              prop="ym"
              label="更新时间">
            </el-table-column>

            <el-table-column label="编辑" width="100px" align="center">
              <template #default="scope">
                <el-button
                  size="mini"
                  icon="el-icon-edit"
                  type="primary"
                  circle
                  @click="handleEdit(scope.$index, scope.row)"></el-button>
              </template>
            </el-table-column>
            <el-table-column label="删除" width="100" align="center">
              <template slot-scope="scope">
                <el-popconfirm style="cursor: pointer;display: block" @confirm="deleteData(scope.$index, scope.row)"
                               title="是否删除？">
                  <el-button type="danger" icon="el-icon-delete" circle slot="reference"></el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
            <el-table-column label="推送" width="100" align="center">
              <template slot-scope="scope">
                <el-popconfirm style="cursor: pointer;display: block" @confirm="updateSys(scope.$index, scope.row)"
                               title="是否一键推送？">
                  <el-button type="primary" icon="el-icon-upload" circle slot="reference"></el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
          <el-dialog
            :title="profttl"
            ref="editForm"
            width="30%"
            :visible.sync="editVisible">
            <el-form :model="editForm" :rules="dataRule" ref="dataForm" size="mini" >
              <el-row>
                <el-col :span="24">
                  <el-form-item
                    prop="ver"
                    label="版本号">
                    <el-input :disabled="!this.addMessage" v-model="editForm.ver" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    prop="content"
                    label="描述">
                    <el-input type="textarea" placeholder="描述" v-model="editForm.content" ></el-input>
                    <!--   <i class="el-icon-question" style="position: absolute; right: -25px;bottom: 6px;cursor: pointer" @click="configQuestion"></i> -->
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <template #footer>
          <span class="dialog-footer">
            <el-button @click="editCancel" size="mini" >取 消</el-button>
            <el-button type="primary" size="mini" @click="holdData()" >保 存</el-button>
          </span>
            </template>
          </el-dialog>
        </div>
      </template>
    </drg-form>
<!--    </drg-container>-->
  </div>
</template>

<script>
import { queryData, deleteData, addData, updateData, updateSys } from '@/api/dataConfig/versionMessagePrompt'
export default {
  name: 'verUpgtMsgPopConfig',
  inject: ['reload'],
  data: () => ({
    tableData: [],
    tableLoading: false,
    editVisible: false,
    addMessage: false,
    resetFormFlag: false,
    profttl: '',
    params: {
      ver: '',
      content: ''
    },
    queryForm: {
      ym: '',
      ver: ''
    },
    editForm: {
      id: '',
      ver: '',
      content: ''
    },
    dataRule: {
      ver: [{ required: true, message: '版本号不能为空', trigger: 'blur' }],
      content: [{ required: true, message: '版本日志不能为空', trigger: 'blur' }]
    }
  }),
  mounted () {
    this.getData()
    this.getIssue()
  },
  methods: {
    getData () {
      this.tableLoading = true
      queryData(this.queryForm).then((result) => {
        this.tableData = result.data
        this.tableLoading = false
      })
    },
    handleEdit (index, row) {
      this.profttl = '编辑'
      this.configValue = row.content
      this.editForm.ver = row.ver
      this.editForm.content = row.content
      this.editVisible = true
      this.addMessage = false
    },
    addMessagePrompt () {
      this.editVisible = true
      this.addMessage = true
      this.profttl = '新增'
      this.editForm.ver = ''
      this.editForm.content = ''
      this.resetForm()
    },
    resetForm () {
      if (this.resetFormFlag) {
        this.$refs['editForm'].resetFields()
      }
      this.resetFormFlag = false
    },
    holdData () {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          if (this.addMessage) {
            addData(this.editForm).then(result => {
              if (result.code == 200) {
                this.$message({
                  message: '新增成功！',
                  type: 'success'
                })
                this.editVisible = false
                this.getData()
              } else {
                return false
              }
            })
          } else {
            // if(this.checkUpdate()){
            //   this.$message({
            //     message: "未作修改，无需提交！",
            //     type: "warning"
            //   })
            // } else
            // {
            updateData(this.editForm).then(result => {
              if (result.code == 200) {
                this.$message({
                  message: '修改成功！',
                  type: 'success'
                })
                this.editVisible = false
                this.getData()
              } else {
                return false
              }
            })
          }
        }
      })
    },
    // checkUpdate () {
    //   if (this.editForm.configKey == this.editForm.configKey &&
    //     this.editForm.configValue == this.editForm.configValue &&
    //     this.editForm.id == this.editForm.id) {
    //     return true
    //   }
    //   return false
    // },
    deleteData (index, row) {
      this.params.ver = row.ver
      this.params.content = row.content
      deleteData(this.params).then(result => {
        if (result.code == 200) {
          this.$message({
            message: '删除成功！',
            type: 'success'
          })
          this.getData()
        }
      })
    },
    updateSys (index, row) {
      this.params.ver = row.ver
      this.tableLoading = true
      updateSys(this.params).then(result => {
        if (result.code == 200) {
          this.tableLoading = false
          this.$message({
            message: '推送成功！',
            type: 'success'
          })
        }
      })
    },
    editCancel () {
      this.$refs['dataForm'].resetFields()
      this.$confirm('关闭后所做修改将不会保存,是否确认关闭？')
        .then(_ => {
          this.editVisible = false
        })
        .catch(_ => {
          this.editVisible = false
        })
    },
    getIssue () {
      let updt_date = new Date()
      updt_date.setDate(1)
      let month = parseInt(updt_date.getMonth() + 1)
      let day = updt_date.getDate()
      if (month < 10) month = '0' + month
      if (day < 10) day = '0' + day
      let Month = updt_date.getFullYear() + '-' + month
      this.queryForm.ym = Month
    },
    refresh () {
      this.reload()
    }
  }
}
</script>

<style scoped>

</style>
