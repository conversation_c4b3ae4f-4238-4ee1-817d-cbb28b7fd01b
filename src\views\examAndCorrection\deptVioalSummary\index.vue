<template>
  <div class="app-container">
    <drg-form v-model="listQuery"
              show-date-range
              :show-in-date-range="false"
              :show-se-date-range="false"
              :show-hos-dept="{ show: this.$somms.hasHosRole()}"
              showPagination
              :totalNum="total"
              headerTitle="查询条件"
              contentTitle="科室违规汇总"
              :container="true"
              :exportExcel="{ 'tableId': tableId, exportName: '科室违规汇总'}"
              :exportExcelFun="queryPageData"
              :exportExcelHasChild="false"
              :initTimeValueNotQuery="false"
              ref="somForm"
              @query="handleSearchList">

      <template slot="extendFormItems">

        <el-form-item label="场景类型" prop="ruleScenType">
          <drg-dict-select dicType="RULE_SCEN_TYPE" placeholder="请选择场景类型" v-model="listQuery.ruleScenType"
                           @change="getDataIsuue"/>
        </el-form-item>

        <el-form-item label="科室名称">
          <el-input v-model="listQuery.deptName" placeholder="请输入科室名称"/>
        </el-form-item>
        <el-form-item label="筛选违规" prop="showVioal">
          <drg-dict-select dicType="BOOLEAN" placeholder="请选择是否只展示违规数据" v-model="listQuery.showVioal"
                           @change="getDataIsuue"/>
        </el-form-item>

      </template>
      <!--      <template slot="buttons">
            <el-button type="primary" @click="extractHisViueData" class="som-button-margin-right"><i
              class="el-icon-upload el-icon&#45;&#45;left"></i>抽取
            </el-button>
            </template>-->

      <!-- 内容 -->
      <template slot="containerContent">
        <el-table ref="dataTable"
                  :header-cell-style="{'text-align':'center'}"
                  :id="tableId"
                  size="mini"
                  stripe
                  height="100%"
                  :data="list"
                  style="width: 100%;"
                  v-loading="listLoading"
                  border>
          <el-table-column label="序号" type="index" align="right">
          </el-table-column>
          <el-table-column label="月份" prop="mouth" align="center"/>
          <el-table-column label="科室名称" prop="deptName" align="center"/>
          <el-table-column label="医生数量" prop="doctorNum" align="center"/>
          <el-table-column label="违规规则条数" prop="violationRuleNum" align="center">
            <template slot-scope="scope">
              <span @click="showRuleDetail(scope.row)"
                    style="color: red; text-decoration: underline;  cursor: pointer;">
                {{ scope.row.violationRuleNum }}
                </span>
            </template>
          </el-table-column>
          <el-table-column label="违规明细总条数" prop="violationTupleNum" align="center">
            <template slot-scope="scope">
              <span @click="showTupleDetail(scope.row)"
                    style="color: red; text-decoration: underline;  cursor: pointer;">
                {{ scope.row.violationTupleNum }}
                </span>
            </template>
          </el-table-column>
          <el-table-column label="违规总金额" prop="totalAmount" align="center"/>
          <el-table-column label="违规病例数" prop="casesNum" align="center">
            <template slot-scope="scope">
              <div v-if="Number(scope.row.casesNum)>0" class='skip' @click="queryTotalMedicalNum(scope.row)">
                {{ scope.row.casesNum | formatIsEmpty }}
              </div>
              <div v-if="Number(scope.row.totalAmount)==0" style="color:#000000">
                {{ scope.row.casesNum | formatIsEmpty }}
              </div>
            </template>
          </el-table-column>
          <!--          <el-table-column label="参保地" prop="insuplcAdmdvs" align="center"  />-->
          <!--          <el-table-column label="医院id" prop="hospitalId" align="center"  />-->
        </el-table>
        <!-- 规则弹窗 -->
        <el-dialog
          :visible.sync="ruleDialogVisible"
          title="科室违规规则详细信息"
          width="70%"
        >
          <el-table :data="violationRuleList"
                    style="width: 100%"
                    height="400"
                    v-loading="ruleDialogLoading">
            <!-- 序号列 -->
            <el-table-column label="序号" width="60">
              <template slot-scope="scope">
                {{ (ruleDialogQuery.pageNum - 1) * ruleDialogQuery.pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="ruleType" label="违规描述" width="100"></el-table-column>
            <el-table-column prop="deptName" label="科室名称" width="100" :v-show="false"></el-table-column>
            <el-table-column prop="ruleDetlCodg" label="违规类型" width="80"></el-table-column>
            <el-table-column prop="ruleGrpName" label="规则分组名称"></el-table-column>

            <el-table-column prop="violationTupleNum" label="违规明细条数" width="80">
              <template slot-scope="scope">
              <span @click="showTupleDetail(scope.row)"
                    style="color: red; text-decoration: underline;cursor: pointer;">
                {{ scope.row.violationTupleNum }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="totalAmount" label="违规金额" width="80"></el-table-column>
          </el-table>

          <!-- 规则弹窗分页 -->
          <div class="dialog-pagination-right">
            <el-pagination
              background
              @size-change="handleRuleDialogSizeChange"
              @current-change="handleRuleDialogCurrentChange"
              layout="total, sizes, prev, pager, next, jumper"
              :page-size="ruleDialogQuery.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :current-page.sync="ruleDialogQuery.pageNum"
              :total="ruleDialogTotal">
            </el-pagination>
          </div>
        </el-dialog>
        <!-- 元素弹窗 -->
        <el-dialog
          :visible.sync="tupleDialogVisible"
          title="科室违规详细信息"
          width="70%"
        >
          <el-table :data="violationTupleList"
                    style="width: 100%"
                    height="400"
                    v-loading="tupleDialogLoading">
            <!-- 序号列 -->
            <el-table-column label="序号" width="60">
              <template slot-scope="scope">
                {{ (tupleDialogQuery.pageNum - 1) * tupleDialogQuery.pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="medcasno" label="病案号" width="80"></el-table-column>
            <el-table-column prop="ruleType" label="违规描述" width="100"></el-table-column>
            <el-table-column prop="ruleDetlCodg" label="违规编码" width="100"></el-table-column>
            <el-table-column prop="dataCode" label="违规明细编码"></el-table-column>
            <el-table-column prop="dataName" label="违规明细名称"></el-table-column>
            <el-table-column prop="totalAmount" label="违规金额" width="80"></el-table-column>
          </el-table>

          <!-- 元素弹窗分页 -->
          <div class="dialog-pagination-right">
            <el-pagination
              background
              @size-change="handleTupleDialogSizeChange"
              @current-change="handleTupleDialogCurrentChange"
              layout="total, sizes, prev, pager, next, jumper"
              :page-size="tupleDialogQuery.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :current-page.sync="tupleDialogQuery.pageNum"
              :total="tupleDialogTotal">
            </el-pagination>
          </div>
        </el-dialog>
      </template>
    </drg-form>
  </div>
</template>
<script>
import {
  querySelectTreeAndSelectList,
  queryLikeDipGroupByPram,
  queryLikeDrgsByPram,
  queryDataIsuue
} from '@/api/common/drgCommon'
import {getHisDate} from '@/api/medicalQuality/settleList'
import {
  getResultListByDept as queryPageData,
  getViolationsSummaryByMouth as fetchViolationRuleDetails,
  fetchViolationTupleDetails
} from '@/api/examCorrection/ruleAndTuples'
import {formatDate} from '@/utils/date'
import {elExportExcel} from '@/utils/exportExcel'
import {medicalDeleteDataUpload} from '@/api/medicalQuality/medicalDeleteDataUpload'

const defaultListQuery = {
  pageNum: 1,
  pageSize: 200,
  ruleScenType: '1',
  cysj: null,
  deptName: null,
  deptCode: null,
  showVioal: null,
  begnDate: '',
  expiDate: '',
  seStartTime: '',
  seEndTime: '',
  priOutHosDeptName:'',
  inHosFlag: ''
}
export default {
  name: 'deptVioalSummary',
  components: {},
  inject: ['reload'],
  data() {
    return {
      tupleDialogVisible: false,
      ruleDialogVisible: false,
      //点击违规规则存储当前行数据
      selectedRowData: {},
      //点击违规规则查询到的违规元素
      violationTupleList: [],
      //点击违规规则查询到的违规元素
      violationRuleList: [],
      //点击违规规则元素查的传参
      selectTupleParam: Object.assign({}, defaultListQuery),
      //点击违规规则元素查的传参
      selectRuleParam:{},
      // 规则弹窗分页参数
      ruleDialogQuery: {
        pageNum: 1,
        pageSize: 20
      },
      ruleDialogTotal: 0,
      ruleDialogLoading: false,
      // 元素弹窗分页参数
      tupleDialogQuery: {
        pageNum: 1,
        pageSize: 20
      },
      tupleDialogTotal: 0,
      tupleDialogLoading: false,
      showFlag: false,
      dialogVisible: false,
      tableId: 'dataTable',
      listLoading: true,
      list: null,
      total: null,
      dictVoList: {},
      listQuery: Object.assign({}, defaultListQuery),
      submitListQuery: Object.assign({}, defaultListQuery),
      tableHeight: 0,
      depts: [],
      defaultProps: {
        parent: 'parentCode', // 父级唯一标识
        value: 'code', // 唯一标识
        label: 'name', // 标签显示
        children: 'children' // 子级
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created() {

  },
  watch: {
    list: function () {
      this.$nextTick(() => {
        if (this.$refs.settleListTable) {
          this.$refs.settleListTable.doLayout()
        }
      })
    }
  },
  filters: {
    formatIsEmpty(value) {
      if (value) {
        return value
      } else {
        return '-'
      }
    },
    formatTime(time) {
      if (time) {
        let updt_date = new Date(time)
        return formatDate(updt_date, 'yyyy-MM-dd')
      } else {
        return '-'
      }
    },

  },
  mounted() {
    this.$nextTick(() => {
      this.findSelectTreeAndSelectList()
      if (Object.keys(this.$route.query).length > 0) {
        // 时间需要在最下面设置
        if (this.$route.query.begnDate && this.$route.query.expiDate) {
          this.$refs.somForm.jumpTimeChange('out', this.$route.query, this.listQuery)
        }
        if (this.$route.query.seStartTime && this.$route.query.seEndTime) {
          this.$refs.somForm.jumpTimeChange('se', this.$route.query, this.listQuery)
        }
      }
      this.getList()
    })
  },
  methods: {
    // 点击表格列时调用的事件
    showRuleDetail(rowData) {
      this.selectedRowData = rowData;
      this.ruleDialogVisible = true;
      // 重置分页参数
      this.ruleDialogQuery.pageNum = 1;
      this.ruleDialogQuery.pageSize = 20;
      this.fetchViolationRuleDetails(); // 打开弹窗
    },
    fetchViolationRuleDetails() {
      this.ruleDialogLoading = true;
      this.getParamByBaseQuery(this.selectRuleParam, this.listQuery)
      this.selectRuleParam.ruleMouth = this.selectedRowData.mouth
      this.selectRuleParam.deptCode = this.selectedRowData.deptCode
      this.selectRuleParam.priOutHosDeptName = this.selectedRowData.deptName
      this.selectRuleParam.ruleDetlCodg = this.selectedRowData.ruleDetlCodg
      // 添加分页参数
      this.selectRuleParam.pageNum = this.ruleDialogQuery.pageNum
      this.selectRuleParam.pageSize = this.ruleDialogQuery.pageSize
      fetchViolationRuleDetails(this.selectRuleParam).then(response => {
        this.ruleDialogLoading = false;
        // 请求成功后，更新违规详细信息
        if (response.data) {
          if (Array.isArray(response.data)) {
            this.violationRuleList = response.data;
            this.ruleDialogTotal = response.data.length; // 如果后端没有返回total，使用数组长度
          } else if (response.data.list) {
            this.violationRuleList = response.data.list;
            this.ruleDialogTotal = response.data.total || response.data.list.length;
          }
        }
      }).catch(() => {
        this.ruleDialogLoading = false;
      })
    },
    // 点击表格列时调用的事件
    showTupleDetail(rowData) {
      console.log(rowData);
      this.selectedRowData = rowData;
      this.tupleDialogVisible = true;
      // 重置分页参数
      this.tupleDialogQuery.pageNum = 1;
      this.tupleDialogQuery.pageSize = 20;
      this.fetchViolationTupleDetails(); // 打开弹窗
    },
    fetchViolationTupleDetails() {
      this.tupleDialogLoading = true;
      this.getParamByBaseQuery(this.selectTupleParam, this.listQuery)
      this.selectTupleParam.ruleMouth = this.selectedRowData.mouth
      this.selectTupleParam.errorType = this.selectedRowData.errorType
      this.selectTupleParam.deptCode = this.selectedRowData.deptCode
      this.selectTupleParam.priOutHosDeptName = this.selectedRowData.deptName
      this.selectTupleParam.ruleDetlCodg = this.selectedRowData.ruleDetlCodg
      // 添加分页参数
      this.selectTupleParam.pageNum = this.tupleDialogQuery.pageNum
      this.selectTupleParam.pageSize = this.tupleDialogQuery.pageSize
      fetchViolationTupleDetails(this.selectTupleParam).then(response => {
        this.tupleDialogLoading = false;
        // 请求成功后，更新违规详细信息
        if (response.data) {
          if (Array.isArray(response.data)) {
            this.violationTupleList = response.data;
            this.tupleDialogTotal = response.data.length; // 如果后端没有返回total，使用数组长度
          } else if (response.data.list) {
            this.violationTupleList = response.data.list;
            this.tupleDialogTotal = response.data.total || response.data.list.length;
          }
        }
      }).catch(() => {
        this.tupleDialogLoading = false;
      })
    },
    queryTotalMedicalNum(row) {
      let transformBaseParam = Object.assign({}, defaultListQuery)
      this.getParamByBaseQuery(transformBaseParam, this.listQuery)
      transformBaseParam.priOutHosDeptName = row.deptName
      transformBaseParam.priOutHosDeptCode = row.deptCode
      transformBaseParam.deptCode = row.deptCode
      transformBaseParam.deptName = row.deptName
      this.$router.push({
        path: '/examAndCorrection/docerVialDetial',
        query: transformBaseParam
      })

    },
    // 获取科室下拉树、以及所有码表值
    findSelectTreeAndSelectList: function () {
      let params = new URLSearchParams()
      // params.append('type', "2");
      params.append('codeKeys', 'B34C')
      querySelectTreeAndSelectList(params).then((response) => {
        if (response.data) {
          this.depts = response.data
        }
        if (response.dictVoList) {
          this.dictVoList = response.dictVoList
        }
      })
    },
    getDataIsuue() {
      queryDataIsuue().then(response => {
        this.listLoading = false
        this.cy_start_date = response.data.cy_start_date
        this.cy_end_date = response.data.cy_end_date
        this.listQuery.cysj = [this.cy_start_date, this.cy_end_date]
        this.getList()
      })
    },
    getList() {
      this.listLoading = true
      this.getParamByBaseQuery(this.submitListQuery, this.listQuery)
      queryPageData(this.submitListQuery).then(response => {
        this.listLoading = false
        this.list = response.data.list
        this.total = response.data.total
      })
    },
    queryPageData,
    allExcel() {
      this.$somms.exportExcelAll(this.submitListQuery, this.total, this.$refs.settleListTable.$children, document.getElementById('slTable').children[1].children[0].children[1].children[0].childNodes, queryPageData, '病案数据')
    },
    handleSelect(item) {
      this.listQuery.dipCodg = item.dipCodg
    },
    dateChangeCysj(val) {
      if (val) {
        this.cy_start_date = val[0]
        this.cy_end_date = val[1]
      } else {
        this.cy_start_date = null
        this.cy_end_date = null
      }
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1
      this.listQuery.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val
      this.getList()
    },
    handleSearchList() {
      this.getList()
    },
    handleResetSearch() {
      this.getDataIsuue()
    },
    // exportExcel() {
    //   let tableId = 'slTable'
    //   let fileName = '病案数据'
    //   elExportExcel(tableId, fileName)
    // },
    refresh() {
      this.reload()
    },
    getParamByBaseQuery(targetParam, sourceParam) {
      if (targetParam) {
        const keys = Object.keys(targetParam)
        // 更新参数值
        keys.forEach(key => {
          targetParam[key] = sourceParam[key]
        })
      }
    },
    getParams() {
      return this.submitListQuery
    },
    // 规则弹窗分页事件
    handleRuleDialogSizeChange(val) {
      this.ruleDialogQuery.pageNum = 1
      this.ruleDialogQuery.pageSize = val
      this.fetchViolationRuleDetails()
    },
    handleRuleDialogCurrentChange(val) {
      this.ruleDialogQuery.pageNum = val
      this.fetchViolationRuleDetails()
    },
    // 元素弹窗分页事件
    handleTupleDialogSizeChange(val) {
      this.tupleDialogQuery.pageNum = 1
      this.tupleDialogQuery.pageSize = val
      this.fetchViolationTupleDetails()
    },
    handleTupleDialogCurrentChange(val) {
      this.tupleDialogQuery.pageNum = val
      this.fetchViolationTupleDetails()
    }
  }
}
</script>
<style scoped>
/*时间样式设置*/
::v-deep .el-date-editor--daterange.el-input,
::v-deep .el-date-editor--daterange.el-input__inner,
::v-deep .el-date-editor--timerange.el-input,
::v-deep .el-date-editor--timerange.el-input__inner {
  width: 200px;
}

.autoSelectInputWidth {
  width: 178px;
}

/* 弹窗分页样式 */
.dialog-pagination-right {
  margin-top: 20px;
  text-align: right;
}

.dialog-pagination-right .el-pagination {
  display: inline-block;
}

</style>
