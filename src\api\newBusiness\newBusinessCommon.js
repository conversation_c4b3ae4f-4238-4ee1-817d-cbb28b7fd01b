import request from '@/utils/request'

/**
 * 查询病组亏损
 * @param params
 * @returns {*}
 */
export function queryDiseaseLoss (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDiseaseLoss',
    method: 'post',
    params: params
  })
}

/**
 * 查询医生亏损
 * @param params
 * @returns {*}
 */
export function queryDoctorLoss (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDoctorLoss',
    method: 'post',
    params: params
  })
}

/**
 * 查询患者亏损
 * @param params
 * @returns {*}
 */
export function queryPatientLoss (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryPatientLoss',
    method: 'post',
    params: params
  })
}

/**
 * 查询下拉列表
 * @param params
 * @returns {*}
 */
export function queryDropdown (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDropdown',
    method: 'post',
    params: params
  })
}

/**
 * 查询病案错误情况
 * @param params
 * @returns {*}
 */
export function queryMedError (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryMedError',
    method: 'post',
    params: params
  })
}

/**
 * 查询汇总页面表格数据
 * @param params
 * @returns {*}
 */
export function queryAnalysisSummary (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryAnalysisSummary',
    method: 'post',
    params: params
  })
}

/**
 * 查询医生下拉
 * @returns {*}
 */
export function queryDoctorDropDown () {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDoctorDropDown',
    method: 'post'
  })
}

export function updateSwitchState (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/updateSwitchState',
    method: 'post',
    params: params
  })
}

export function queryDrgDiseaseLoss (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDrgDiseaseLoss',
    method: 'post',
    params: params
  })
}

export function queryDrgDoctorLoss (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDrgDoctorLoss',
    method: 'post',
    params: params
  })
}

export function queryDrgPatientLoss (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDrgPatientLoss',
    method: 'post',
    params: params
  })
}

export function queryDrgAnalysisSummary (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDrgAnalysisSummary',
    method: 'post',
    params: params
  })
}

export function queryDrgMedError (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDrgMedError',
    method: 'post',
    params: params
  })
}

export function queryDrgDropdown (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDrgDropdown',
    method: 'post',
    params: params
  })
}

export function queryContrastData (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryContrastData',
    method: 'post',
    params: params
  })
}

export function queryDeptDropdown (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDeptDropdown',
    method: 'post',
    params: params
  })
}

export function queryDoctorDropdown (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDoctorDropdown',
    method: 'post',
    params: params
  })
}

export function queryDiseaseDropdown (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDiseaseDropdown',
    method: 'post',
    params: params
  })
}

export function queryPatientContrastData (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryPatientContrastData',
    method: 'post',
    params: params
  })
}

export function queryDoctorContrastData (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDoctorContrastData',
    method: 'post',
    params: params
  })
}

export function selectPatientContrastData (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/selectPatientContrastData',
    method: 'post',
    params: params
  })
}
/**
 * 查询患者亏损
 * @param params
 * @returns {*}
 */
export function queryPatientLoss2 (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryPatientLoss2',
    method: 'post',
    params: params
  })
}

export function queryDrgPatientLoss2 (params) {
  return request({
    url: '/newBusinessCommonAnalysisController/queryDrgPatientLoss2',
    method: 'post',
    params: params
  })
}
