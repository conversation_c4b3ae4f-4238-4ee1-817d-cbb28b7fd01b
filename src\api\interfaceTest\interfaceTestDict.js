import request from '@/utils/request'

/**
 * 查询数据
 * @param params
 * @returns {*}
 */
export function queryData (params) {
  return request({
    url: '/itftDictController/queryData',
    method: 'post',
    data: params
  })
}

/**
 * 删除数据
 * @param params
 * @returns {*}
 */
export function deleteDict (params) {
  return request({
    url: '/itftDictController/deleteDict',
    method: 'post',
    data: params
  })
}

/**
 * 保存数据
 * @param params
 * @returns {*}
 */
export function saveDict (params) {
  return request({
    url: '/itftDictController/saveDict',
    method: 'post',
    data: params
  })
}
